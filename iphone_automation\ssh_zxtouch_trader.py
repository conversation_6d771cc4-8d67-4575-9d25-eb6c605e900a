#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone7自动化交易脚本 - 修复版
通过SSH连接到iPhone7，使用ZXTouch执行币安期货自动化交易
"""

import paramiko
import time
import sys
from datetime import datetime

# iPhone配置
CONFIG = {
    # iPhone SSH连接配置
    "IPHONE_IP": "**************",
    "SSH_PORT": 22,
    "SSH_USERNAME": "mobile",
    "SSH_PASSWORD": "sdzddhy",
    "SSH_TIMEOUT": 15,
    
    # ZXTouch配置
    "ZXTOUCH_COMMAND": "/var/jb/usr/bin/zxtouchb",
    "SCRIPT_DIR": "/var/mobile/Documents/zxtouch_scripts",
    
    # 交易界面坐标 (最终校准版本)
    "COORDINATES": {
        "AMOUNT_INPUT": (330, 790),
        "UP_BUTTON": (193, 1057),
        "DOWN_BUTTON": (537, 1054),
        "CONFIRM_BUTTON": (360, 1260),
        "KEYBOARD_DONE": (675, 870),
        "KEYBOARD_DELETE": (610, 1270),
        "NUMBER_KEYS": {
            '1': (120, 952), '2': (375, 952), '3': (615, 952),
            '4': (120, 1050), '5': (375, 1050), '6': (615, 1050),
            '7': (120, 1170), '8': (375, 1170), '9': (615, 1170),
            '0': (375, 1272)
        }
    }
}

def execute_binance_trade(signal_type, amount):
    """执行币安自动化交易 - 统一入口函数"""

    print(f"📱 开始执行iPhone自动化交易...")
    print(f"   信号类型: {signal_type}")
    print(f"   交易金额: {amount} USDT")

    # 🔒 关键修复：iPhone自动化前检查交易状态管理器
    try:
        import sys
        import os

        # 添加项目根目录到路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        from src.core.trade_state_manager import enhanced_trade_state_manager as trade_state_manager

        if not trade_state_manager.can_start_new_trade():
            print(f"🔒 iPhone自动化: 拒绝执行 - 交易状态管理器阻止 (当前状态: {trade_state_manager._current_state.value})")
            return False

        print(f"✅ iPhone自动化: 交易状态检查通过，可以执行")

    except Exception as e:
        print(f"⚠️ iPhone自动化: 交易状态检查异常: {e}")
        # 为了安全起见，如果状态检查失败，拒绝执行
        print(f"🔒 iPhone自动化: 拒绝执行 - 交易状态检查异常")
        return False

    # 调用SSH远程执行函数
    return execute_trade_via_ssh(signal_type, amount)

def execute_trade_via_ssh(signal_type, amount):
    """通过SSH远程执行iPhone自动化交易"""
    
    # 信号类型映射
    signal_mapping = {
        "UP": "UP", "DOWN": "DOWN",
        "BUY": "UP", "SELL": "DOWN"
    }
    
    direction = signal_mapping.get(signal_type.upper(), "UP")
    
    # 选择按钮坐标和名称
    if direction == "UP":
        button_x, button_y = CONFIG["COORDINATES"]["UP_BUTTON"]
        button_name = "上涨"
    else:
        button_x, button_y = CONFIG["COORDINATES"]["DOWN_BUTTON"]
        button_name = "下跌"
    
    # 确保金额在合理范围内
    amount = max(5, min(250, int(amount)))
    
    print(f"🚀 准备执行{button_name}交易")
    print(f"💰 交易金额: {amount} USDT")
    print(f"🎯 按钮坐标: ({button_x}, {button_y})")
    
    try:
        # SSH连接到iPhone
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=CONFIG["IPHONE_IP"],
            port=CONFIG["SSH_PORT"],
            username=CONFIG["SSH_USERNAME"],
            password=CONFIG["SSH_PASSWORD"],
            timeout=CONFIG["SSH_TIMEOUT"]
        )
        print("✅ SSH连接成功")
        
        # 生成ZXTouch Python脚本
        script_content = f'''#!/usr/bin/env python3
import sys
import time

# 添加ZXTouch模块路径
sys.path.insert(0, '/var/jb/usr/lib/python3.9/site-packages')

try:
    from zxtouch.client import zxtouch
    from zxtouch.touchtypes import *
    print("✅ ZXTouch模块导入成功")
    
    # 创建连接
    device = zxtouch("127.0.0.1")
    print("✅ ZXTouch连接成功")
    
    print("🚀 开始执行币安{button_name}交易")
    print("💰 交易金额: {amount} USDT")
    
    # 步骤1: 点击数量输入框
    print("步骤1: 点击数量输入框")
    device.touch(TOUCH_DOWN, 1, {CONFIG["COORDINATES"]["AMOUNT_INPUT"][0]}, {CONFIG["COORDINATES"]["AMOUNT_INPUT"][1]})
    time.sleep(0.2)
    device.touch(TOUCH_UP, 1, {CONFIG["COORDINATES"]["AMOUNT_INPUT"][0]}, {CONFIG["COORDINATES"]["AMOUNT_INPUT"][1]})
    time.sleep(0.8)  # 键盘弹出等待
    
    # 步骤2: 清空现有数量
    print("步骤2: 清空现有数量")
    for i in range(3):
        device.touch(TOUCH_DOWN, 1, {CONFIG["COORDINATES"]["KEYBOARD_DELETE"][0]}, {CONFIG["COORDINATES"]["KEYBOARD_DELETE"][1]})
        time.sleep(0.2)
        device.touch(TOUCH_UP, 1, {CONFIG["COORDINATES"]["KEYBOARD_DELETE"][0]}, {CONFIG["COORDINATES"]["KEYBOARD_DELETE"][1]})
        time.sleep(0.2)  # 删除间隔
    
    # 步骤3: 输入金额 (使用正确的数字键盘坐标)
    print("步骤3: 输入金额 {amount}")
    amount_str = str({amount})
    
    # 正确的数字键盘坐标
    number_coords = {CONFIG["COORDINATES"]["NUMBER_KEYS"]}
    
    for digit in amount_str:
        if digit in number_coords:
            x, y = number_coords[digit]
            print(f"   点击数字: {{digit}} ({{x}}, {{y}})")
            device.touch(TOUCH_DOWN, 1, x, y)
            time.sleep(0.2)
            device.touch(TOUCH_UP, 1, x, y)
            time.sleep(0.3)  # 数字输入间隔
    
    print(f"   ✅ 金额输入完成: {{amount_str}}")
    
    # 步骤4: 点击键盘完成
    print("步骤4: 点击键盘完成")
    device.touch(TOUCH_DOWN, 1, {CONFIG["COORDINATES"]["KEYBOARD_DONE"][0]}, {CONFIG["COORDINATES"]["KEYBOARD_DONE"][1]})
    time.sleep(0.2)
    device.touch(TOUCH_UP, 1, {CONFIG["COORDINATES"]["KEYBOARD_DONE"][0]}, {CONFIG["COORDINATES"]["KEYBOARD_DONE"][1]})
    time.sleep(0.8)  # 键盘收起等待
    
    # 步骤5: 点击交易按钮
    print("步骤5: 点击{button_name}按钮")
    device.touch(TOUCH_DOWN, 1, {button_x}, {button_y})
    time.sleep(0.2)
    device.touch(TOUCH_UP, 1, {button_x}, {button_y})
    time.sleep(0.8)  # 确认界面等待
    
    # 步骤6: 点击确认按钮
    print("步骤6: 点击确认按钮")
    device.touch(TOUCH_DOWN, 1, {CONFIG["COORDINATES"]["CONFIRM_BUTTON"][0]}, {CONFIG["COORDINATES"]["CONFIRM_BUTTON"][1]})
    time.sleep(0.2)
    device.touch(TOUCH_UP, 1, {CONFIG["COORDINATES"]["CONFIRM_BUTTON"][0]}, {CONFIG["COORDINATES"]["CONFIRM_BUTTON"][1]})
    time.sleep(1.2)  # 交易完成等待
    
    print("✅ 交易执行完成!")
    device.disconnect()
    
except Exception as e:
    print(f"❌ 执行失败: {{e}}")
    import traceback
    traceback.print_exc()
'''
        
        # 创建脚本目录
        script_dir = CONFIG["SCRIPT_DIR"]
        mkdir_cmd = f"mkdir -p {script_dir}"
        ssh.exec_command(mkdir_cmd, timeout=10)
        
        # 写入脚本文件
        script_path = f"{script_dir}/trade_{direction.lower()}_{amount}.py"
        write_cmd = f"cat > {script_path} << 'EOF'\n{script_content}\nEOF"
        stdin, stdout, stderr = ssh.exec_command(write_cmd, timeout=30)
        stdout.channel.recv_exit_status()
        
        # 设置执行权限
        chmod_cmd = f"chmod +x {script_path}"
        ssh.exec_command(chmod_cmd, timeout=10)
        
        print("✅ 脚本上传成功")
        
        # 执行脚本
        exec_cmd = f"cd {script_dir} && python3.9 trade_{direction.lower()}_{amount}.py"
        stdin, stdout, stderr = ssh.exec_command(exec_cmd, timeout=60)
        
        # 实时读取输出
        while True:
            line = stdout.readline()
            if not line:
                break
            print(f"📱 输出: {line.strip()}")
        
        # 检查执行结果
        exit_status = stdout.channel.recv_exit_status()
        
        # 清理脚本文件
        cleanup_cmd = f"rm -f {script_path}"
        ssh.exec_command(cleanup_cmd, timeout=10)
        
        ssh.close()
        
        if exit_status == 0:
            print("✅ iPhone自动化交易执行成功")
            return True
        else:
            print(f"❌ iPhone自动化交易执行失败，退出码: {exit_status}")
            return False
            
    except Exception as e:
        print(f"❌ SSH执行失败: {e}")
        return False

def main():
    """主函数 - 命令行调用"""
    if len(sys.argv) != 3:
        print("用法: python ssh_zxtouch_trader.py <UP/DOWN> <金额>")
        print("示例: python ssh_zxtouch_trader.py UP 25")
        return
    
    signal_type = sys.argv[1].upper()
    try:
        amount = int(sys.argv[2])
    except ValueError:
        print("❌ 金额必须是数字")
        return
    
    if signal_type not in ["UP", "DOWN"]:
        print("❌ 信号类型必须是 UP 或 DOWN")
        return
    
    if not (5 <= amount <= 250):
        print("❌ 金额必须在 5-250 之间")
        return
    
    print("=" * 50)
    print("📱 iPhone7自动化交易")
    print("=" * 50)
    
    success = execute_trade_via_ssh(signal_type, amount)
    
    if success:
        print("🎉 交易执行成功!")
    else:
        print("❌ 交易执行失败!")

if __name__ == "__main__":
    main()
