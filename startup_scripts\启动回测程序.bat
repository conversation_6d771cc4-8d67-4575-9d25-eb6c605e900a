@echo off
title Backtest Program - Trading System

echo ========================================
echo    Starting Backtest Program
echo ========================================
echo.

echo [INFO] Starting backtest program...
echo [INFO] Current directory: %CD%
echo.

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not installed or not in PATH
    echo [ERROR] Please install Python and add to system PATH
    pause
    exit /b 1
)

REM Check if run_backtest.py exists
if not exist "run_backtest.py" (
    echo [ERROR] run_backtest.py file not found
    echo [ERROR] Please ensure running in correct project directory
    pause
    exit /b 1
)

echo [INFO] Python environment check passed
echo [INFO] Starting backtest program...
echo.

REM Start backtest program
python run_backtest.py

REM Check program exit status
if errorlevel 1 (
    echo.
    echo [ERROR] Backtest program exited abnormally
    echo [ERROR] Please check log files for detailed error information
) else (
    echo.
    echo [INFO] Backtest program exited normally
)

echo.
echo Press any key to close window...
pause >nul
