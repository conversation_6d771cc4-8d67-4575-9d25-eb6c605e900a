#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件系统 - 解耦GUI依赖

实现发布/订阅机制，使核心预测逻辑与GUI完全解耦。
核心模块通过事件系统发布状态更新，GUI适配器监听事件并更新界面。

主要功能：
1. 中央事件总线
2. 事件类型定义
3. 事件处理器接口
4. 线程安全的事件分发
5. 异步事件处理
"""

import threading
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union
from collections import defaultdict
import traceback

# 设置日志
logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    # 状态更新事件
    STATUS_UPDATE = "status_update"
    PREDICTION_RESULT = "prediction_result"
    TRAINING_PROGRESS = "training_progress"
    MODEL_LOADED = "model_loaded"
    
    # 系统事件
    SYSTEM_ERROR = "system_error"
    SYSTEM_WARNING = "system_warning"
    SYSTEM_INFO = "system_info"
    
    # 交易事件
    SIGNAL_SENT = "signal_sent"
    PRICE_UPDATE = "price_update"
    MARKET_DATA_UPDATE = "market_data_update"
    
    # GUI事件
    GUI_UPDATE_REQUEST = "gui_update_request"
    BUTTON_STATE_CHANGE = "button_state_change"
    PROGRESS_UPDATE = "progress_update"


@dataclass
class Event:
    """事件基类"""
    event_type: EventType
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = "unknown"
    data: Dict[str, Any] = field(default_factory=dict)
    priority: int = 0  # 0=normal, 1=high, 2=critical
    
    def __post_init__(self):
        """确保时间戳是datetime对象"""
        if not isinstance(self.timestamp, datetime):
            self.timestamp = datetime.now()


@dataclass
class StatusUpdateEvent(Event):
    """状态更新事件"""
    message: str = ""
    level: str = "neutral"  # neutral, success, warning, error

    def __init__(self, message: str = "", level: str = "neutral", **kwargs):
        super().__init__(event_type=EventType.STATUS_UPDATE, **kwargs)
        self.message = message
        self.level = level
        self.data.update({
            'message': self.message,
            'level': self.level
        })


@dataclass
class PredictionResultEvent(Event):
    """预测结果事件"""
    target_name: str = ""
    prediction_label: str = ""
    probabilities: List[float] = field(default_factory=list)
    confidence: float = 0.0
    signal_sent: Optional[str] = None
    current_price: Optional[float] = None

    def __init__(self, target_name: str = "", prediction_label: str = "",
                 probabilities: List[float] = None, confidence: float = 0.0,
                 signal_sent: Optional[str] = None, current_price: Optional[float] = None, **kwargs):
        super().__init__(event_type=EventType.PREDICTION_RESULT, **kwargs)
        self.target_name = target_name
        self.prediction_label = prediction_label
        self.probabilities = probabilities or []
        self.confidence = confidence
        self.signal_sent = signal_sent
        self.current_price = current_price
        self.data.update({
            'target_name': self.target_name,
            'prediction_label': self.prediction_label,
            'probabilities': self.probabilities,
            'confidence': self.confidence,
            'signal_sent': self.signal_sent
        })


@dataclass
class TrainingProgressEvent(Event):
    """训练进度事件"""
    progress: float = 0.0  # 0.0 to 1.0
    stage: str = ""
    details: str = ""

    def __init__(self, progress: float = 0.0, stage: str = "", details: str = "", **kwargs):
        super().__init__(event_type=EventType.TRAINING_PROGRESS, **kwargs)
        self.progress = progress
        self.stage = stage
        self.details = details
        self.data.update({
            'progress': self.progress,
            'stage': self.stage,
            'details': self.details
        })


@dataclass
class SystemErrorEvent(Event):
    """系统错误事件"""
    error_message: str = ""
    exception: Optional[Exception] = None
    traceback_str: str = ""
    
    def __post_init__(self):
        super().__post_init__()
        self.event_type = EventType.SYSTEM_ERROR
        self.priority = 2  # Critical
        if self.exception and not self.traceback_str:
            self.traceback_str = traceback.format_exc()
        self.data.update({
            'error_message': self.error_message,
            'exception_type': type(self.exception).__name__ if self.exception else None,
            'traceback': self.traceback_str
        })


@dataclass
class SignalSentEvent(Event):
    """信号发送事件"""
    target_name: str = ""
    signal_type: str = ""
    amount: float = 0.0
    success: bool = False
    response_message: str = ""
    
    def __post_init__(self):
        super().__post_init__()
        self.event_type = EventType.SIGNAL_SENT
        self.data.update({
            'target_name': self.target_name,
            'signal_type': self.signal_type,
            'amount': self.amount,
            'success': self.success,
            'response_message': self.response_message
        })


class EventHandler(ABC):
    """事件处理器抽象基类"""
    
    @abstractmethod
    def handle_event(self, event: Event) -> None:
        """处理事件"""
        pass
    
    @abstractmethod
    def get_supported_event_types(self) -> List[EventType]:
        """获取支持的事件类型"""
        pass


class EventBus:
    """
    中央事件总线
    
    实现线程安全的发布/订阅机制
    """
    
    def __init__(self):
        """初始化事件总线"""
        self._handlers: Dict[EventType, List[EventHandler]] = defaultdict(list)
        self._callback_handlers: Dict[EventType, List[Callable]] = defaultdict(list)
        self._lock = threading.RLock()
        self._event_queue = []
        self._queue_lock = threading.Lock()
        self._processing_thread = None
        self._stop_processing = False
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'handlers_registered': 0,
            'errors': 0
        }
        
        logger.info("事件总线初始化完成")
    
    def subscribe(self, event_type: EventType, handler: Union[EventHandler, Callable]) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理器或回调函数
        """
        with self._lock:
            if isinstance(handler, EventHandler):
                self._handlers[event_type].append(handler)
            elif callable(handler):
                self._callback_handlers[event_type].append(handler)
            else:
                raise ValueError(f"Handler must be EventHandler or callable, got {type(handler)}")
            
            self._stats['handlers_registered'] += 1
            logger.debug(f"订阅事件: {event_type.value}, 处理器: {type(handler).__name__}")
    
    def unsubscribe(self, event_type: EventType, handler: Union[EventHandler, Callable]) -> None:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理器或回调函数
        """
        with self._lock:
            if isinstance(handler, EventHandler):
                if handler in self._handlers[event_type]:
                    self._handlers[event_type].remove(handler)
            elif callable(handler):
                if handler in self._callback_handlers[event_type]:
                    self._callback_handlers[event_type].remove(handler)
            
            logger.debug(f"取消订阅事件: {event_type.value}, 处理器: {type(handler).__name__}")
    
    def publish(self, event: Event, async_processing: bool = True) -> None:
        """
        发布事件
        
        Args:
            event: 事件对象
            async_processing: 是否异步处理
        """
        self._stats['events_published'] += 1
        
        if async_processing:
            # 异步处理：添加到队列
            with self._queue_lock:
                self._event_queue.append(event)
            
            # 启动处理线程（如果尚未启动）
            self._ensure_processing_thread()
        else:
            # 同步处理：立即处理
            self._process_event(event)
    
    def _ensure_processing_thread(self) -> None:
        """确保处理线程正在运行"""
        if self._processing_thread is None or not self._processing_thread.is_alive():
            self._stop_processing = False
            self._processing_thread = threading.Thread(
                target=self._process_events_loop,
                name="EventBusProcessor",
                daemon=True
            )
            self._processing_thread.start()
            logger.debug("事件处理线程已启动")
    
    def _process_events_loop(self) -> None:
        """事件处理循环"""
        while not self._stop_processing:
            try:
                # 获取待处理事件
                events_to_process = []
                with self._queue_lock:
                    if self._event_queue:
                        events_to_process = self._event_queue.copy()
                        self._event_queue.clear()
                
                # 处理事件
                for event in events_to_process:
                    self._process_event(event)
                
                # 短暂休眠避免CPU占用过高
                time.sleep(0.01)
                
            except Exception as e:
                logger.error(f"事件处理循环出错: {e}")
                self._stats['errors'] += 1
                time.sleep(0.1)  # 出错时稍长休眠
    
    def _process_event(self, event: Event) -> None:
        """
        处理单个事件
        
        Args:
            event: 事件对象
        """
        try:
            self._stats['events_processed'] += 1
            
            # 处理EventHandler类型的处理器
            handlers = self._handlers.get(event.event_type, [])
            for handler in handlers:
                try:
                    handler.handle_event(event)
                except Exception as e:
                    logger.error(f"事件处理器 {type(handler).__name__} 处理事件失败: {e}")
                    self._stats['errors'] += 1
            
            # 处理回调函数类型的处理器
            callbacks = self._callback_handlers.get(event.event_type, [])
            for callback in callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"事件回调函数处理事件失败: {e}")
                    self._stats['errors'] += 1
            
            logger.debug(f"事件处理完成: {event.event_type.value}")
            
        except Exception as e:
            logger.error(f"处理事件时发生未知错误: {e}")
            self._stats['errors'] += 1
    
    def stop(self) -> None:
        """停止事件总线"""
        self._stop_processing = True
        if self._processing_thread and self._processing_thread.is_alive():
            self._processing_thread.join(timeout=1.0)
        logger.info("事件总线已停止")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                **self._stats,
                'active_handlers': sum(len(handlers) for handlers in self._handlers.values()),
                'active_callbacks': sum(len(callbacks) for callbacks in self._callback_handlers.values()),
                'queue_size': len(self._event_queue),
                'processing_thread_alive': self._processing_thread.is_alive() if self._processing_thread else False
            }


# 全局事件总线实例
_global_event_bus = None
_event_bus_lock = threading.Lock()


def get_event_bus() -> EventBus:
    """
    获取全局事件总线实例（单例模式）
    
    Returns:
        事件总线实例
    """
    global _global_event_bus
    
    if _global_event_bus is None:
        with _event_bus_lock:
            if _global_event_bus is None:
                _global_event_bus = EventBus()
    
    return _global_event_bus


# 便捷函数
def publish_event(event: Event, async_processing: bool = True) -> None:
    """便捷函数：发布事件"""
    get_event_bus().publish(event, async_processing)


def subscribe_event(event_type: EventType, handler: Union[EventHandler, Callable]) -> None:
    """便捷函数：订阅事件"""
    get_event_bus().subscribe(event_type, handler)


def unsubscribe_event(event_type: EventType, handler: Union[EventHandler, Callable]) -> None:
    """便捷函数：取消订阅事件"""
    get_event_bus().unsubscribe(event_type, handler)


# 便捷的事件发布函数
def publish_status_update(message: str, level: str = "neutral", source: str = "system") -> None:
    """发布状态更新事件"""
    event = StatusUpdateEvent(message=message, level=level, source=source)
    publish_event(event)


def publish_prediction_result(target_name: str, prediction_label: str,
                            probabilities: List[float], confidence: float = 0.0,
                            signal_sent: Optional[str] = None, current_price: Optional[float] = None,
                            source: str = "prediction") -> None:
    """发布预测结果事件"""
    event = PredictionResultEvent(
        target_name=target_name,
        prediction_label=prediction_label,
        probabilities=probabilities,
        confidence=confidence,
        signal_sent=signal_sent,
        current_price=current_price,
        source=source
    )
    publish_event(event)


def publish_training_progress(progress: float, stage: str = "", details: str = "", 
                            source: str = "training") -> None:
    """发布训练进度事件"""
    event = TrainingProgressEvent(
        progress=progress,
        stage=stage,
        details=details,
        source=source
    )
    publish_event(event)


def publish_system_error(error_message: str, exception: Optional[Exception] = None,
                        source: str = "system") -> None:
    """发布系统错误事件"""
    event = SystemErrorEvent(
        error_message=error_message,
        exception=exception,
        source=source
    )
    publish_event(event)


def publish_signal_sent(target_name: str, signal_type: str, amount: float = 0.0,
                       success: bool = False, response_message: str = "",
                       source: str = "signal") -> None:
    """发布信号发送事件"""
    event = SignalSentEvent(
        event_type=EventType.SIGNAL_SENT,
        target_name=target_name,
        signal_type=signal_type,
        amount=amount,
        success=success,
        response_message=response_message,
        source=source
    )
    publish_event(event)
