# SimGUI.py - 专业加密货币模拟交易平台GUI
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, font as tkFont
from datetime import datetime, timedelta
import time

class SimGUI:
    def __init__(self, root, main_app_callbacks, initial_states=None):
        """
        初始化专业加密货币模拟交易平台GUI。

        Args:
            root (tk.Tk): Tkinter 的主窗口。
            main_app_callbacks (dict): 一个包含回调函数的字典。
            initial_states (dict, optional): 包含初始开关状态的字典。
        """
        self.root = root
        self.callbacks = main_app_callbacks
        self.initial_states = initial_states if initial_states is not None else {}

        self.root.title("CryptoSim Pro - 专业加密货币模拟交易平台")
        self.root.geometry("1400x1100")
        self.root.minsize(1200, 800)
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # --- 现代暗黑主题配色方案 ---
        self.colors = {
            'bg_primary': '#0D1421',      # 主背景 - 深蓝黑
            'bg_secondary': '#1A2332',    # 次要背景 - 深蓝灰
            'bg_card': '#242B3D',         # 卡片背景 - 蓝灰
            'bg_input': '#2A3441',        # 输入框背景
            'border': '#3A4553',          # 边框颜色
            'text_primary': '#FFFFFF',    # 主要文字 - 白色
            'text_secondary': '#B8C5D1',  # 次要文字 - 浅蓝灰
            'text_muted': '#7A8B9A',      # 弱化文字
            'accent_blue': '#00D4FF',     # 科技蓝 - 主要强调色
            'accent_blue_hover': '#33DDFF', # 科技蓝悬停
            'success': '#00E676',         # 成功绿色
            'danger': '#FF5252',          # 危险红色
            'warning': '#FFB74D',         # 警告橙色
            'profit': '#4CAF50',          # 盈利绿
            'loss': '#F44336',            # 亏损红
            'neutral': '#9E9E9E'          # 中性灰
        }

        # --- 现代字体配置 ---
        self.fonts = {
            'title': tkFont.Font(family="Segoe UI", size=16, weight="bold"),
            'heading': tkFont.Font(family="Segoe UI", size=12, weight="bold"),
            'body': tkFont.Font(family="Segoe UI", size=10),
            'small': tkFont.Font(family="Segoe UI", size=9),
            'mono': tkFont.Font(family="Consolas", size=10),
            'price': tkFont.Font(family="Segoe UI", size=14, weight="bold"),
            'status': tkFont.Font(family="Segoe UI", size=9)
        }

        self.root.configure(bg=self.colors['bg_primary'])
        self._setup_styles()
        self._create_layout()

    def _setup_styles(self):
        """配置现代化样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 主框架样式
        self.style.configure("Main.TFrame", 
                           background=self.colors['bg_primary'],
                           borderwidth=0)

        # 卡片样式
        self.style.configure("Card.TFrame",
                           background=self.colors['bg_card'],
                           relief="flat",
                           borderwidth=1)

        # 标签框样式
        self.style.configure("Card.TLabelframe",
                           background=self.colors['bg_card'],
                           foreground=self.colors['text_primary'],
                           borderwidth=1,
                           relief="solid",
                           bordercolor=self.colors['border'],
                           lightcolor=self.colors['border'],
                           darkcolor=self.colors['border'])

        self.style.configure("Card.TLabelframe.Label",
                           background=self.colors['bg_card'],
                           foreground=self.colors['accent_blue'],
                           font=self.fonts['heading'])

        # 按钮样式
        self.style.configure("Primary.TButton",
                           background=self.colors['accent_blue'],
                           foreground=self.colors['text_primary'],
                           font=self.fonts['body'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))

        self.style.map("Primary.TButton",
                      background=[('active', self.colors['accent_blue_hover']),
                                ('pressed', '#0099CC')])

        self.style.configure("Success.TButton",
                           background=self.colors['success'],
                           foreground=self.colors['text_primary'],
                           font=self.fonts['body'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(12, 6))

        self.style.configure("Danger.TButton",
                           background=self.colors['danger'],
                           foreground=self.colors['text_primary'],
                           font=self.fonts['body'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(12, 6))

        # 输入框样式
        self.style.configure("Modern.TEntry",
                           fieldbackground=self.colors['bg_input'],
                           foreground=self.colors['text_primary'],
                           borderwidth=1,
                           relief="solid",
                           bordercolor=self.colors['border'],
                           insertcolor=self.colors['accent_blue'],
                           padding=8)

        # 下拉菜单样式
        self.style.configure("Modern.TCombobox",
                           fieldbackground=self.colors['bg_input'],
                           foreground=self.colors['text_primary'],
                           background=self.colors['bg_card'],
                           borderwidth=1,
                           relief="solid",
                           bordercolor=self.colors['border'],
                           arrowcolor=self.colors['accent_blue'],
                           padding=8)

        # 复选框样式
        self.style.configure("Modern.TCheckbutton",
                           background=self.colors['bg_card'],
                           foreground=self.colors['text_primary'],
                           font=self.fonts['body'],
                           focuscolor='none',
                           borderwidth=0,
                           padding=(5, 8))  # 增加内边距确保足够高度

        self.style.map("Modern.TCheckbutton",
                      background=[('active', self.colors['bg_card'])],
                      indicatorcolor=[('selected', self.colors['accent_blue']),
                                    ('!selected', self.colors['border'])])

        # 表格样式
        self.style.configure("Modern.Treeview",
                           background=self.colors['bg_card'],
                           foreground=self.colors['text_primary'],
                           fieldbackground=self.colors['bg_card'],
                           borderwidth=0,
                           rowheight=30,
                           font=self.fonts['body'])

        self.style.configure("Modern.Treeview.Heading",
                           background=self.colors['bg_secondary'],
                           foreground=self.colors['text_primary'],
                           font=self.fonts['heading'],
                           relief="flat",
                           borderwidth=1,
                           bordercolor=self.colors['border'])

        self.style.map("Modern.Treeview",
                      background=[('selected', self.colors['accent_blue'])],
                      foreground=[('selected', self.colors['text_primary'])])

        self.style.map("Modern.Treeview.Heading",
                      background=[('active', self.colors['bg_input'])])

        # 滚动条样式
        self.style.configure("Modern.Vertical.TScrollbar",
                           background=self.colors['bg_secondary'],
                           troughcolor=self.colors['bg_card'],
                           borderwidth=0,
                           arrowcolor=self.colors['text_secondary'],
                           darkcolor=self.colors['bg_secondary'],
                           lightcolor=self.colors['bg_secondary'])

    def _create_layout(self):
        """创建主布局"""
        # 创建菜单
        self._create_menu()

        # 主容器
        main_container = ttk.Frame(self.root, style="Main.TFrame")
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 顶部标题栏
        self._create_header(main_container)

        # 主内容区域
        content_frame = ttk.Frame(main_container, style="Main.TFrame")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # 左侧面板 (账户信息和统计)
        left_panel = ttk.Frame(content_frame, style="Main.TFrame")
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))
        left_panel.configure(width=350)
        left_panel.pack_propagate(False)

        # 右侧面板 (交易表格)
        right_panel = ttk.Frame(content_frame, style="Main.TFrame")
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 底部交易控制面板
        bottom_panel = ttk.Frame(main_container, style="Main.TFrame")
        bottom_panel.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))

        # 创建各个面板
        self._create_account_panel(left_panel)
        self._create_statistics_panel(left_panel)
        self._create_active_trades_panel(right_panel)
        self._create_trade_history_panel(right_panel)
        self._create_trading_controls(bottom_panel)

        # 状态栏
        self._create_status_bar()

    def _create_header(self, parent):
        """创建顶部标题栏"""
        header_frame = ttk.Frame(parent, style="Main.TFrame")
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # 平台标题
        title_label = tk.Label(header_frame,
                              text="CryptoSim Pro",
                              font=self.fonts['title'],
                              bg=self.colors['bg_primary'],
                              fg=self.colors['accent_blue'])
        title_label.pack(side=tk.LEFT)

        # 实时价格显示
        price_frame = ttk.Frame(header_frame, style="Main.TFrame")
        price_frame.pack(side=tk.RIGHT)

        self.price_label = tk.Label(price_frame,
                                   text="BTC/USDT",
                                   font=self.fonts['body'],
                                   bg=self.colors['bg_primary'],
                                   fg=self.colors['text_secondary'])
        self.price_label.pack(side=tk.TOP)

        self.price_value = tk.Label(price_frame,
                                   text="$----.--",
                                   font=self.fonts['price'],
                                   bg=self.colors['bg_primary'],
                                   fg=self.colors['text_primary'])
        self.price_value.pack(side=tk.TOP)

        # 在价格显示区域添加小的重连按钮
        reconnect_mini_button = ttk.Button(price_frame,
                                          text="🔄",
                                          width=3,
                                          style="Secondary.TButton",
                                          command=self._on_reconnect_price_click)
        reconnect_mini_button.pack(side=tk.TOP, pady=(5, 0))

        # 添加工具提示
        self._create_tooltip(reconnect_mini_button, "重连价格数据")

    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root,
                         bg=self.colors['bg_secondary'],
                         fg=self.colors['text_primary'],
                         activebackground=self.colors['accent_blue'],
                         activeforeground=self.colors['text_primary'],
                         borderwidth=0)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0,
                           bg=self.colors['bg_card'],
                           fg=self.colors['text_primary'],
                           activebackground=self.colors['accent_blue'],
                           activeforeground=self.colors['text_primary'])
        file_menu.add_command(label="设置账户状态", command=self._set_account_state_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        menubar.add_cascade(label="文件", menu=file_menu)

        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0,
                           bg=self.colors['bg_card'],
                           fg=self.colors['text_primary'],
                           activebackground=self.colors['accent_blue'],
                           activeforeground=self.colors['text_primary'])
        view_menu.add_command(label="刷新数据", command=self._refresh_data)
        menubar.add_cascade(label="视图", menu=view_menu)

        self.root.config(menu=menubar)

    def _create_account_panel(self, parent):
        """创建账户信息面板"""
        account_frame = ttk.LabelFrame(parent, text="账户信息", style="Card.TLabelframe")
        account_frame.pack(fill=tk.X, pady=(0, 15))

        # 账户信息变量
        self.account_info_vars = {
            "initial_balance": tk.StringVar(value="----.-- USDT"),
            "current_balance": tk.StringVar(value="----.-- USDT"),
            "daily_profit_loss": tk.StringVar(value="----.-- USDT"),
            "total_profit_loss": tk.StringVar(value="----.-- USDT"),
            "btc_price": tk.StringVar(value="$----.--")
        }

        # 余额信息
        balance_frame = ttk.Frame(account_frame, style="Card.TFrame")
        balance_frame.pack(fill=tk.X, padx=15, pady=10)

        # 初始余额
        self._create_info_row(balance_frame, "初始余额", self.account_info_vars["initial_balance"], 0)
        
        # 当前余额 (突出显示)
        current_balance_frame = ttk.Frame(balance_frame, style="Card.TFrame")
        current_balance_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=8)
        
        tk.Label(current_balance_frame,
                text="当前余额",
                font=self.fonts['body'],
                bg=self.colors['bg_card'],
                fg=self.colors['text_secondary']).pack(side=tk.LEFT)

        self.current_balance_label = tk.Label(current_balance_frame,
                                            textvariable=self.account_info_vars["current_balance"],
                                            font=self.fonts['price'],
                                            bg=self.colors['bg_card'],
                                            fg=self.colors['accent_blue'])
        self.current_balance_label.pack(side=tk.RIGHT)

        # 盈亏信息
        self.daily_profit_label = self._create_info_row(balance_frame, "今日盈亏", self.account_info_vars["daily_profit_loss"], 2)
        self.total_profit_label = self._create_info_row(balance_frame, "总盈亏", self.account_info_vars["total_profit_loss"], 3)


        balance_frame.columnconfigure(1, weight=1)

    def _create_statistics_panel(self, parent):
        """创建统计数据面板"""
        stats_frame = ttk.LabelFrame(parent, text="交易统计", style="Card.TLabelframe")
        stats_frame.pack(fill=tk.X, pady=(0, 15))

        # 统计变量
        self.stats_vars = {
            "daily_win_rate": tk.StringVar(value="--.-- %"),
            "daily_up_stats": tk.StringVar(value="做多: 0/0 (--.--%)"),
            "daily_down_stats": tk.StringVar(value="做空: 0/0 (--.--%)"),
            "total_win_rate": tk.StringVar(value="--.-- %"),
            "total_up_stats": tk.StringVar(value="做多: 0/0 (--.--%)"),
            "total_down_stats": tk.StringVar(value="做空: 0/0 (--.--%)"),
        }

        stats_content = ttk.Frame(stats_frame, style="Card.TFrame")
        stats_content.pack(fill=tk.X, padx=15, pady=10)

        # 今日统计
        daily_label = tk.Label(stats_content,
                              text="今日统计",
                              font=self.fonts['heading'],
                              bg=self.colors['bg_card'],
                              fg=self.colors['accent_blue'])
        daily_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 5))

        self._create_info_row(stats_content, "胜率", self.stats_vars["daily_win_rate"], 1)
        self._create_info_row(stats_content, "做多", self.stats_vars["daily_up_stats"], 2, small=True)
        self._create_info_row(stats_content, "做空", self.stats_vars["daily_down_stats"], 3, small=True)

        # 分隔线
        separator = tk.Frame(stats_content, height=1, bg=self.colors['border'])
        separator.grid(row=4, column=0, columnspan=2, sticky="ew", pady=10)

        # 总计统计
        total_label = tk.Label(stats_content,
                              text="总计统计",
                              font=self.fonts['heading'],
                              bg=self.colors['bg_card'],
                              fg=self.colors['accent_blue'])
        total_label.grid(row=5, column=0, columnspan=2, sticky="w", pady=(0, 5))

        self._create_info_row(stats_content, "胜率", self.stats_vars["total_win_rate"], 6)
        self._create_info_row(stats_content, "做多", self.stats_vars["total_up_stats"], 7, small=True)
        self._create_info_row(stats_content, "做空", self.stats_vars["total_down_stats"], 8, small=True)

        stats_content.columnconfigure(1, weight=1)

    def _create_info_row(self, parent, label_text, text_var, row, small=False):
        """创建信息行"""
        font_size = self.fonts['small'] if small else self.fonts['body']
        
        label = tk.Label(parent,
                        text=label_text,
                        font=font_size,
                        bg=self.colors['bg_card'],
                        fg=self.colors['text_secondary'])
        label.grid(row=row, column=0, sticky="w", pady=2)

        value = tk.Label(parent,
                        textvariable=text_var,
                        font=font_size,
                        bg=self.colors['bg_card'],
                        fg=self.colors['text_primary'])
        value.grid(row=row, column=1, sticky="e", pady=2)

        return value

    def _create_active_trades_panel(self, parent):
        """创建活跃交易面板"""
        active_frame = ttk.LabelFrame(parent, text="当前活跃交易", style="Card.TLabelframe")
        active_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建表格
        columns = ("direction", "entry_price", "amount_staked", "entry_time", "expiry_time", "remaining_sec")
        column_names = ("方向", "开仓价", "金额", "开仓时间", "到期时间", "剩余时间")
        column_widths = {"direction": 80, "entry_price": 100, "amount_staked": 80, 
                        "entry_time": 100, "expiry_time": 100, "remaining_sec": 80}

        tree_frame = ttk.Frame(active_frame, style="Card.TFrame")
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        self.active_trades_tree = ttk.Treeview(tree_frame, 
                                              columns=columns, 
                                              show="headings", 
                                              style="Modern.Treeview",
                                              height=8)

        # 配置列
        for i, col_id in enumerate(columns):
            self.active_trades_tree.heading(col_id, text=column_names[i])
            self.active_trades_tree.column(col_id, 
                                          width=column_widths[col_id], 
                                          anchor=tk.CENTER,
                                          stretch=tk.YES)

        # 滚动条
        scrollbar_y = ttk.Scrollbar(tree_frame, 
                                   orient="vertical", 
                                   command=self.active_trades_tree.yview,
                                   style="Modern.Vertical.TScrollbar")
        self.active_trades_tree.configure(yscrollcommand=scrollbar_y.set)

        self.active_trades_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_trade_history_panel(self, parent):
        """创建交易历史面板"""
        history_frame = ttk.LabelFrame(parent, text="最近交易历史", style="Card.TLabelframe")
        history_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("time", "direction", "entry_p", "exit_p", "amount", "status", "p_l")
        column_names = ("时间", "方向", "开仓价", "平仓价", "金额", "结果", "盈亏")
        column_widths = {"time": 140, "direction": 60, "entry_p": 80, 
                        "exit_p": 80, "amount": 70, "status": 60, "p_l": 80}

        tree_frame = ttk.Frame(history_frame, style="Card.TFrame")
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        self.trade_history_tree = ttk.Treeview(tree_frame,
                                              columns=columns,
                                              show="headings",
                                              style="Modern.Treeview",
                                              height=8)

        # 配置列
        for i, col_id in enumerate(columns):
            self.trade_history_tree.heading(col_id, text=column_names[i])
            self.trade_history_tree.column(col_id,
                                          width=column_widths[col_id],
                                          anchor=tk.CENTER,
                                          stretch=tk.YES)

        # 滚动条
        scrollbar_hist_y = ttk.Scrollbar(tree_frame,
                                        orient="vertical",
                                        command=self.trade_history_tree.yview,
                                        style="Modern.Vertical.TScrollbar")
        self.trade_history_tree.configure(yscrollcommand=scrollbar_hist_y.set)

        self.trade_history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_hist_y.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_trading_controls(self, parent):
        """创建交易控制面板"""
        control_frame = ttk.LabelFrame(parent, text="交易控制中心", style="Card.TLabelframe")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        control_content = ttk.Frame(control_frame, style="Card.TFrame")
        control_content.pack(fill=tk.X, padx=20, pady=15)

        # 手动交易区域
        manual_frame = ttk.Frame(control_content, style="Card.TFrame")
        manual_frame.pack(side=tk.LEFT, fill=tk.Y)

        manual_title = tk.Label(manual_frame,
                               text="手动交易",
                               font=self.fonts['heading'],
                               bg=self.colors['bg_card'],
                               fg=self.colors['accent_blue'])
        manual_title.pack(anchor="w", pady=(0, 10))

        # 交易方向和金额
        trade_inputs = ttk.Frame(manual_frame, style="Card.TFrame")
        trade_inputs.pack(fill=tk.X)

        # 方向选择
        direction_frame = ttk.Frame(trade_inputs, style="Card.TFrame")
        direction_frame.pack(side=tk.LEFT, padx=(0, 15))

        tk.Label(direction_frame,
                text="方向",
                font=self.fonts['body'],
                bg=self.colors['bg_card'],
                fg=self.colors['text_secondary']).pack(anchor="w")

        self.manual_direction_var = tk.StringVar(value="UP")
        direction_combo = ttk.Combobox(direction_frame,
                                      textvariable=self.manual_direction_var,
                                      values=["UP", "DOWN"],
                                      state="readonly",
                                      style="Modern.TCombobox",
                                      width=8)
        direction_combo.pack(pady=(5, 0))

        # 金额输入
        amount_frame = ttk.Frame(trade_inputs, style="Card.TFrame")
        amount_frame.pack(side=tk.LEFT, padx=(0, 15))

        tk.Label(amount_frame,
                text="金额 (USDT)",
                font=self.fonts['body'],
                bg=self.colors['bg_card'],
                fg=self.colors['text_secondary']).pack(anchor="w")

        self.manual_amount_var = tk.StringVar(value="5.0")
        amount_entry = ttk.Entry(amount_frame,
                                textvariable=self.manual_amount_var,
                                style="Modern.TEntry",
                                width=10)
        amount_entry.pack(pady=(5, 0))

        # 下单按钮
        button_frame = ttk.Frame(trade_inputs, style="Card.TFrame")
        button_frame.pack(side=tk.LEFT)

        tk.Label(button_frame,
                text=" ",
                font=self.fonts['body'],
                bg=self.colors['bg_card']).pack()  # 占位符

        trade_button = ttk.Button(button_frame,
                                 text="立即下单",
                                 style="Primary.TButton",
                                 command=self._on_manual_trade_click)
        trade_button.pack(pady=(5, 0))

        # 分隔线
        separator = tk.Frame(control_content, width=2, bg=self.colors['border'])
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=30)

        # 自动交易区域
        auto_frame = ttk.Frame(control_content, style="Card.TFrame")
        auto_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        auto_title = tk.Label(auto_frame,
                             text="自动交易设置",
                             font=self.fonts['heading'],
                             bg=self.colors['bg_card'],
                             fg=self.colors['accent_blue'])
        auto_title.pack(anchor="w", pady=(0, 10))

        # 自动交易开关
        initial_auto_trade_status = self.initial_states.get('auto_trade', False)
        initial_kelly_status = self.initial_states.get('kelly', False)

        self.auto_trade_var = tk.BooleanVar(value=initial_auto_trade_status)
        auto_trade_check = ttk.Checkbutton(auto_frame,
                                          text="启用自动交易",
                                          variable=self.auto_trade_var,
                                          style="Modern.TCheckbutton",
                                          command=self._on_toggle_auto_trade_click)
        auto_trade_check.pack(anchor="w", pady=2)

        # 凯利公式开关 - 紧跟在自动交易开关后面，确保可见
        self.kelly_risk_var = tk.BooleanVar(value=initial_kelly_status)

        # 创建一个简单的可点击标签作为开关
        self.kelly_status_text = tk.StringVar()
        self.kelly_status_text.set("✓ 启用自动凯利公式" if initial_kelly_status else "☐ 启用自动凯利公式")

        self.kelly_check = tk.Label(auto_frame,
                                   textvariable=self.kelly_status_text,
                                   bg=self.colors['bg_card'],
                                   fg=self.colors['accent_blue'] if initial_kelly_status else self.colors['text_primary'],
                                   font=self.fonts['body'],
                                   cursor="hand2",
                                   relief="flat",
                                   padx=5,
                                   pady=3)
        self.kelly_check.pack(anchor="w", pady=(8, 5))  # 适当的垂直间距
        self.kelly_check.bind("<Button-1>", self._on_kelly_label_click)

        # 自动交易金额设置
        auto_amount_frame = ttk.Frame(auto_frame, style="Card.TFrame")
        auto_amount_frame.pack(fill=tk.X, pady=(10, 0))

        self.auto_amount_label = tk.Label(auto_amount_frame,
                                         text="固定金额:",
                                         font=self.fonts['body'],
                                         bg=self.colors['bg_card'],
                                         fg=self.colors['text_secondary'])
        self.auto_amount_label.pack(side=tk.LEFT, padx=(20, 10))

        self.auto_trade_amount_var = tk.StringVar(value="10.0")
        self.auto_amount_entry = ttk.Entry(auto_amount_frame,
                                          textvariable=self.auto_trade_amount_var,
                                          style="Modern.TEntry",
                                          width=10)
        self.auto_amount_entry.pack(side=tk.LEFT)

        # 调试信息：确保按钮被创建
        print(f"SimGUI: 凯利公式按钮已创建，初始状态: {initial_kelly_status}")

        # 价格数据连接控制
        connection_frame = ttk.Frame(auto_frame, style="Card.TFrame")
        connection_frame.pack(fill=tk.X, pady=(15, 0))

        connection_title = tk.Label(connection_frame,
                                   text="连接控制",
                                   font=self.fonts['body'],
                                   bg=self.colors['bg_card'],
                                   fg=self.colors['text_secondary'])
        connection_title.pack(side=tk.LEFT, padx=(0, 10))

        reconnect_button = ttk.Button(connection_frame,
                                     text="重连价格数据",
                                     style="Secondary.TButton",
                                     command=self._on_reconnect_price_click)
        reconnect_button.pack(side=tk.LEFT)

        # 设置状态监听
        self.auto_trade_var.trace_add("write", self._toggle_auto_amount_entry_state)
        self.kelly_risk_var.trace_add("write", self._toggle_auto_amount_entry_state)
        self._toggle_auto_amount_entry_state()



    def _create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("状态：系统就绪")
        
        status_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=30)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(status_frame,
                                    textvariable=self.status_var,
                                    font=self.fonts['status'],
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['text_secondary'],
                                    anchor="w")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

        # 时间显示
        self.time_var = tk.StringVar()
        self.time_label = tk.Label(status_frame,
                                  textvariable=self.time_var,
                                  font=self.fonts['status'],
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_muted'])
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=5)
        self._update_time()

    def _update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self._update_time)

    # === 数据更新方法 ===

    def update_account_info(self, stats):
        """更新账户信息"""
        self.account_info_vars["initial_balance"].set(f"{stats.get('initial_balance_benchmark', 0):.2f} USDT")
        self.account_info_vars["current_balance"].set(f"{stats.get('current_balance', 0):.2f} USDT")
        
        # 今日盈亏
        daily_pl = stats.get('daily_profit_loss', 0)
        self.account_info_vars["daily_profit_loss"].set(f"{daily_pl:.2f} USDT")
        if hasattr(self, 'daily_profit_label'):
            color = self.colors['profit'] if daily_pl > 0 else (self.colors['loss'] if daily_pl < 0 else self.colors['text_primary'])
            self.daily_profit_label.configure(fg=color)

        # 总盈亏
        total_pl = stats.get('total_profit_loss_overall', 0)
        self.account_info_vars["total_profit_loss"].set(f"{total_pl:.2f} USDT")
        if hasattr(self, 'total_profit_label'):
            color = self.colors['profit'] if total_pl > 0 else (self.colors['loss'] if total_pl < 0 else self.colors['text_primary'])
            self.total_profit_label.configure(fg=color)

        # 更新统计数据
        self.stats_vars["daily_win_rate"].set(f"{stats.get('daily_win_rate', 0):.2f} %")
        self.stats_vars["daily_up_stats"].set(f"{stats.get('daily_up_wins_count',0)}/{stats.get('daily_up_trades_count',0)} ({stats.get('daily_up_win_rate',0):.2f}%)")
        self.stats_vars["daily_down_stats"].set(f"{stats.get('daily_down_wins_count',0)}/{stats.get('daily_down_trades_count',0)} ({stats.get('daily_down_win_rate',0):.2f}%)")

        self.stats_vars["total_win_rate"].set(f"{stats.get('total_win_rate_overall', 0):.2f} %")
        self.stats_vars["total_up_stats"].set(f"{stats.get('total_up_wins_count_overall',0)}/{stats.get('total_up_trades_count_overall',0)} ({stats.get('total_up_win_rate_overall',0):.2f}%)")
        self.stats_vars["total_down_stats"].set(f"{stats.get('total_down_wins_count_overall',0)}/{stats.get('total_down_trades_count_overall',0)} ({stats.get('total_down_win_rate_overall',0):.2f}%)")

    def update_btc_price(self, price_str, color=None):
        """更新BTC价格显示"""
        self.account_info_vars["btc_price"].set(price_str)
        self.price_value.configure(text=price_str)
        
        if color:
            self.price_value.configure(fg=color)
        else:
            self.price_value.configure(fg=self.colors['text_primary'])

    def update_active_trades(self, trades_data_list):
        """更新活跃交易表格"""
        # 清空现有数据
        for item in self.active_trades_tree.get_children():
            self.active_trades_tree.delete(item)
        
        # 添加新数据
        for trade_data in trades_data_list:
            direction = trade_data.get("direction", "N/A")
            values_tuple = (
                direction,
                f"{trade_data.get('entry_price', 0):.2f}",
                f"{trade_data.get('amount_staked', 0):.2f}",
                trade_data.get("entry_time", "--:--:--"),
                trade_data.get("expiry_time", "--:--:--"),
                f"{trade_data.get('remaining_seconds', 0)}s"
            )
            
            # 根据方向设置行颜色
            tag = 'up_trade' if direction == 'UP' else 'down_trade'
            item = self.active_trades_tree.insert("", tk.END, values=values_tuple, tags=(tag,))

        # 配置标签颜色
        self.active_trades_tree.tag_configure('up_trade', foreground=self.colors['success'])
        self.active_trades_tree.tag_configure('down_trade', foreground=self.colors['danger'])

    def update_trade_history(self, history_data_list):
        """更新交易历史表格"""
        # 清空现有数据
        for item in self.trade_history_tree.get_children():
            self.trade_history_tree.delete(item)
        
        # 添加新数据
        for hist_data in history_data_list:
            status = hist_data.get("status", "N/A")
            profit_loss = hist_data.get('profit_loss', 0)
            
            values_tuple = (
                hist_data.get("entry_time", "N/A"),
                hist_data.get("direction", "N/A"),
                f"{hist_data.get('entry_price', 0):.2f}",
                f"{hist_data.get('exit_price', 0):.2f}" if hist_data.get('exit_price') is not None else "N/A",
                f"{hist_data.get('amount_staked', 0):.2f}",
                status,
                f"{profit_loss:.2f}"
            )
            
            # 根据结果设置行颜色
            if status == "WON":
                tag = 'won_row'
            elif status == "LOST":
                tag = 'lost_row'
            else:
                tag = 'neutral_row'
                
            self.trade_history_tree.insert("", 0, values=values_tuple, tags=(tag,))

        # 配置标签颜色
        self.trade_history_tree.tag_configure('won_row', foreground=self.colors['success'])
        self.trade_history_tree.tag_configure('lost_row', foreground=self.colors['danger'])
        self.trade_history_tree.tag_configure('neutral_row', foreground=self.colors['text_primary'])

    def update_status(self, message, level="info"):
        """更新状态栏信息"""
        status_colors = {
            "info": self.colors['text_secondary'],
            "success": self.colors['success'],
            "warning": self.colors['warning'],
            "error": self.colors['danger']
        }
        
        self.status_var.set(f"状态：{message}")
        color = status_colors.get(level, self.colors['text_secondary'])
        self.status_label.configure(fg=color)

    # === 事件处理方法 ===

    def _toggle_auto_amount_entry_state(self, *args):
        """切换自动交易金额输入框状态"""
        if hasattr(self, 'auto_amount_entry') and hasattr(self, 'auto_amount_label'):
            is_fixed_amount_active = self.auto_trade_var.get() and not self.kelly_risk_var.get()
            state = tk.NORMAL if is_fixed_amount_active else tk.DISABLED
            
            # 更新输入框状态
            self.auto_amount_entry.configure(state=state)
            
            # 更新标签颜色
            label_color = self.colors['text_secondary'] if is_fixed_amount_active else self.colors['text_muted']
            self.auto_amount_label.configure(fg=label_color)

    def _on_manual_trade_click(self):
        """处理手动交易按钮点击"""
        if self.callbacks.get('on_manual_trade'):
            try:
                direction = self.manual_direction_var.get()
                amount_str = self.manual_amount_var.get()
                amount_float = float(amount_str)
                amount_int = int(round(amount_float))

                # 更新GUI输入框为四舍五入后的整数值
                self.manual_amount_var.set(str(amount_int))

                self.callbacks['on_manual_trade'](direction, amount_int)
                self.update_status(f"手动下单：{direction} {amount_int} USDT", "info")
            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的下注金额", parent=self.root)
                self.update_status("手动下单失败：金额无效", "error")
        else:
            self.update_status("手动下单功能未连接", "error")

    def _on_toggle_auto_trade_click(self):
        """处理自动交易开关"""
        if self.callbacks.get('on_toggle_auto_trade'):
            try:
                fixed_amount_float = float(self.auto_trade_amount_var.get())
                fixed_amount_int = int(round(fixed_amount_float))
                self.auto_trade_amount_var.set(str(fixed_amount_int))
            except ValueError:
                messagebox.showwarning("金额无效", "自动交易固定金额无效，将使用默认值", parent=self.root)
                fixed_amount_int = 5
                self.auto_trade_amount_var.set(str(fixed_amount_int))

            self.callbacks['on_toggle_auto_trade'](self.auto_trade_var.get(), fixed_amount_int)
            status_msg = "自动交易已启用" if self.auto_trade_var.get() else "自动交易已禁用"
            self.update_status(status_msg, "success" if self.auto_trade_var.get() else "info")
        else:
            self.update_status("自动交易开关未连接", "error")



    def _on_kelly_label_click(self, event):
        """处理凯利公式标签点击"""
        # 切换状态
        current_state = self.kelly_risk_var.get()
        new_state = not current_state
        self.kelly_risk_var.set(new_state)

        # 更新显示
        self.kelly_status_text.set("✓ 启用自动凯利公式" if new_state else "☐ 启用自动凯利公式")
        self.kelly_check.configure(fg=self.colors['accent_blue'] if new_state else self.colors['text_primary'])

        # 调用原有的处理逻辑
        self._on_toggle_kelly_click()

    def _on_toggle_kelly_click(self):
        """处理凯利公式开关"""
        if self.callbacks.get('on_toggle_kelly'):
            self.callbacks['on_toggle_kelly'](self.kelly_risk_var.get())
            status_msg = "凯利公式已启用" if self.kelly_risk_var.get() else "凯利公式已禁用"
            self.update_status(status_msg, "success" if self.kelly_risk_var.get() else "info")

            if self.kelly_risk_var.get() and self.auto_trade_var.get():
                self.update_status("凯利公式已启用，将动态计算下注额", "info")
        else:
            self.update_status("凯利公式开关未连接", "error")

    def _on_reconnect_price_click(self):
        """处理价格数据重连按钮点击（增强版，带详细反馈）"""
        if self.callbacks.get('on_reconnect_price'):
            try:
                self.update_status("正在重连价格数据...", "warning")

                # 执行重连回调
                result = self.callbacks['on_reconnect_price']()

                if result is not False:  # 如果回调没有明确返回False
                    self.update_status("重连请求已发送，正在检查连接状态...", "info")

                    # 延迟检查重连结果
                    def check_reconnect_status():
                        import time
                        import threading

                        def check_worker():
                            try:
                                # 等待3秒让重连完成
                                time.sleep(3)

                                # 检查价格更新情况
                                current_price_text = self.price_value.cget("text")

                                if "$" in current_price_text and "---" not in current_price_text:
                                    self.update_status("重连成功！价格数据已恢复", "success")
                                else:
                                    self.update_status("重连完成，请检查价格显示", "warning")

                            except Exception as e:
                                self.update_status(f"重连状态检查异常: {str(e)}", "error")

                        # 在后台线程中执行检查
                        check_thread = threading.Thread(target=check_worker, daemon=True)
                        check_thread.start()

                    check_reconnect_status()
                else:
                    self.update_status("重连请求被拒绝", "error")

            except Exception as e:
                self.update_status(f"重连失败: {str(e)}", "error")
        else:
            self.update_status("重连功能未连接", "error")

    def _create_tooltip(self, widget, text):
        """为控件创建工具提示"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip,
                           text=text,
                           background="#ffffe0",
                           foreground="#000000",
                           relief="solid",
                           borderwidth=1,
                           font=("Arial", 9))
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def _set_account_state_dialog(self):
        """设置账户状态对话框"""
        if self.callbacks.get('on_set_account_state'):
            try:
                current_benchmark_balance = float(self.account_info_vars["initial_balance"].get().replace(" USDT", ""))
            except:
                current_benchmark_balance = 10000.0
            try:
                current_total_pl = float(self.account_info_vars["total_profit_loss"].get().replace(" USDT", ""))
            except:
                current_total_pl = 0.0

            new_balance = simpledialog.askfloat("设置初始余额", "输入基准初始余额:", 
                                              parent=self.root, initialvalue=current_benchmark_balance, minvalue=0)
            if new_balance is None:
                return

            new_profit_loss = simpledialog.askfloat("设置历史盈亏", "输入历史总盈亏 (可为负):", 
                                                   parent=self.root, initialvalue=current_total_pl)
            if new_profit_loss is None:
                return

            new_total_trades = simpledialog.askinteger("设置交易次数", "输入历史总交易次数:", 
                                                      parent=self.root, initialvalue=0, minvalue=0)
            if new_total_trades is None:
                return

            new_total_wins = simpledialog.askinteger("设置胜利次数", "输入历史总胜利次数:", 
                                                    parent=self.root, initialvalue=0, minvalue=0)
            if new_total_wins is None:
                return

            if new_total_wins > new_total_trades:
                messagebox.showerror("输入错误", "历史胜利次数不能大于历史总交易次数", parent=self.root)
                return

            self.callbacks['on_set_account_state']({
                "balance": new_balance,
                "profit_loss": new_profit_loss,
                "total_trades": new_total_trades,
                "total_wins": new_total_wins
            })
            self.update_status("账户状态已更新", "success")
        else:
            messagebox.showwarning("未实现", "设置账户状态功能未连接", parent=self.root)

    def _refresh_data(self):
        """刷新数据"""
        self.update_status("正在刷新数据...", "info")
        # 这里可以添加刷新逻辑

    def _on_closing(self):
        """处理窗口关闭事件"""
        if self.callbacks.get('on_exit'):
            if messagebox.askokcancel("退出", "确定要关闭CryptoSim Pro吗？", parent=self.root):
                self.callbacks['on_exit']()
        else:
            if messagebox.askokcancel("退出", "确定要关闭CryptoSim Pro吗？", parent=self.root):
                self.root.destroy()


# === 测试代码 ===
if __name__ == '__main__':
    """独立测试GUI的简单主程序"""
    root = tk.Tk()
    
    class Trade:
        CONTRACT_DURATION_MINUTES = 0.2
    
    def mock_manual_trade(direction, amount):
        print(f"GUI请求手动下单: 方向={direction}, 金额={amount}")
        gui_app.update_status(f"手动下单: {direction} {amount} USDT (模拟)", "info")
        current_time_str = datetime.now().strftime("%H:%M:%S")
        expiry_time_str = (datetime.now() + timedelta(minutes=Trade.CONTRACT_DURATION_MINUTES)).strftime("%H:%M:%S")
        
        if not hasattr(mock_manual_trade, 'active_trades'):
            mock_manual_trade.active_trades = []
        if not hasattr(mock_manual_trade, 'history_trades'):
            mock_manual_trade.history_trades = []
        
        mock_manual_trade.active_trades.insert(0, {
            "id": int(time.time()),
            "direction": direction,
            "entry_price": 30000.00,
            "amount_staked": amount,
            "entry_time": current_time_str,
            "expiry_time": expiry_time_str,
            "remaining_seconds": int(Trade.CONTRACT_DURATION_MINUTES * 60),
            "status": "OPEN"
        })
        gui_app.update_active_trades(mock_manual_trade.active_trades)
        
        is_win_mock = True if direction == "UP" else False
        mock_manual_trade.history_trades.insert(0, {
            "entry_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "direction": direction,
            "entry_price": 30000.00,
            "exit_price": 30050.00 if is_win_mock else 29950.00,
            "amount_staked": amount,
            "status": "WON" if is_win_mock else "LOST",
            "profit_loss": amount * 0.8 if is_win_mock else -amount
        })
        gui_app.update_trade_history(mock_manual_trade.history_trades)

    def mock_toggle_auto(is_enabled, fixed_amount):
        print(f"GUI请求切换自动交易: 启用={is_enabled}, 固定金额={fixed_amount}")
        gui_app.update_status(f"自动交易: {'开' if is_enabled else '关'}, 固定额: {fixed_amount:.2f}")

    def mock_toggle_kelly(is_enabled):
        print(f"GUI请求切换凯利公式: 启用={is_enabled}")
        gui_app.update_status(f"凯利公式: {'开' if is_enabled else '关'}")

    def mock_set_account_state(params):
        print(f"GUI请求设置账户状态: {params}")
        mock_stats['initial_balance_benchmark'] = params['balance']
        mock_stats['current_balance'] = params['balance'] + params['profit_loss']
        mock_stats['total_profit_loss_overall'] = params['profit_loss']
        mock_stats['total_trades_count_overall'] = params['total_trades']
        mock_stats['total_wins_count_overall'] = params['total_wins']
        mock_stats['total_win_rate_overall'] = (params['total_wins'] / params['total_trades'] * 100) if params['total_trades'] > 0 else 0
        mock_stats['daily_profit_loss'] = 0
        mock_stats['daily_trades_count'] = 0
        mock_stats['daily_wins_count'] = 0
        mock_stats['daily_win_rate'] = 0
        gui_app.update_account_info(mock_stats)
        gui_app.update_status("账户状态已根据输入重置", "success")

    mock_callbacks = {
        'on_manual_trade': mock_manual_trade,
        'on_toggle_auto_trade': mock_toggle_auto,
        'on_toggle_kelly': mock_toggle_kelly,
        'on_set_account_state': mock_set_account_state,
        'on_exit': root.destroy
    }

    # 模拟初始状态
    sim_main_initial_auto = True
    sim_main_initial_kelly = True
    gui_app = SimGUI(root, mock_callbacks, initial_states={'auto_trade': sim_main_initial_auto, 'kelly': sim_main_initial_kelly})

    # 模拟数据
    mock_stats = {
        'initial_balance_benchmark': 10000.0,
        'current_balance': 9850.50,
        'daily_profit_loss': -149.50,
        'daily_win_rate': 45.80,
        'total_profit_loss_overall': 1203.20,
        'total_trades_count_overall': 100,
        'total_wins_count_overall': 55,
        'total_win_rate_overall': 55.23,
        'daily_up_wins_count': 5,
        'daily_up_trades_count': 12,
        'daily_up_win_rate': 41.67,
        'daily_down_wins_count': 3,
        'daily_down_trades_count': 8,
        'daily_down_win_rate': 37.50,
        'total_up_wins_count_overall': 28,
        'total_up_trades_count_overall': 52,
        'total_up_win_rate_overall': 53.85,
        'total_down_wins_count_overall': 27,
        'total_down_trades_count_overall': 48,
        'total_down_win_rate_overall': 56.25,
    }

    gui_app.update_account_info(mock_stats)
    gui_app.update_btc_price("$29,888.77", gui_app.colors['danger'])

    # 初始化模拟数据
    if not hasattr(mock_manual_trade, 'active_trades'):
        mock_manual_trade.active_trades = []
    if not hasattr(mock_manual_trade, 'history_trades'):
        mock_manual_trade.history_trades = []

    gui_app.update_active_trades(mock_manual_trade.active_trades)
    gui_app.update_trade_history(mock_manual_trade.history_trades)

    def simulate_updates():
        """模拟实时更新"""
        price = 29888.77 + (time.time() % 100 - 50)
        try:
            old_price_str = gui_app.account_info_vars["btc_price"].get().replace("$", "").replace(",", "")
        except:
            old_price_str = str(price)
        
        try:
            old_price = float(old_price_str) if old_price_str and old_price_str not in ["----.--", ""] else price
        except:
            old_price = price
            
        color = gui_app.colors['success'] if price > old_price else (gui_app.colors['danger'] if price < old_price else gui_app.colors['neutral'])
        gui_app.update_btc_price(f"${price:,.2f}", color)

        # 更新活跃交易倒计时
        trades_to_keep = []
        modified_active = False
        for trade in mock_manual_trade.active_trades:
            if trade["remaining_seconds"] > 0:
                trade["remaining_seconds"] -= 1
                trades_to_keep.append(trade)
                modified_active = True
            else:
                print(f"模拟交易 {trade['id']} 到期")

        if modified_active:
            mock_manual_trade.active_trades = trades_to_keep
            gui_app.update_active_trades(mock_manual_trade.active_trades)

        root.after(1000, simulate_updates)

    simulate_updates()
    root.mainloop()
