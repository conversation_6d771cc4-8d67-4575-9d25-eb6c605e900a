#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场状态分析器 - 识别危险的市场状态
专门用于识别"高波动盘整"等对交易不利的市场环境
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    """市场状态枚举"""
    TRENDING_UP = "trending_up"           # 上升趋势
    TRENDING_DOWN = "trending_down"       # 下降趋势
    STRONG_UPTREND = "strong_uptrend"     # 🚀 V12.0: 强劲上升趋势
    STRONG_DOWNTREND = "strong_downtrend" # 🚀 V12.0: 强劲下降趋势
    HIGH_VOLATILITY_SIDEWAYS = "high_vol_sideways"  # 高波动盘整 (危险)
    LOW_VOLATILITY_SIDEWAYS = "low_vol_sideways"    # 低波动盘整
    NORMAL_TRENDING = "normal_trending"   # 正常趋势
    UNKNOWN = "unknown"                   # 未知状态


class MarketStateAnalyzer:
    """市场状态分析器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化市场状态分析器
        
        Args:
            config: 分析器配置
        """
        self.config = config or self._get_default_config()
        
        # 状态分析历史
        self.analysis_history: List[Dict] = []
        
        logger.info("[MarketStateAnalyzer] 市场状态分析器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置，确保所有必需参数都有默认值"""
        return {
            # 波动率相关阈值
            'high_volatility_atr_threshold': 2.5,
            'low_volatility_atr_threshold': 0.8, # 低于此值为低波动

            # 趋势强度相关阈值
            'low_trend_strength_adx_threshold': 20,
            'strong_trend_adx_threshold': 30,

            # 价格行为相关阈值
            'sideways_price_range_threshold': 0.05,
            'price_whipsaw_threshold': 3, # 鞭打次数超过此值认为危险

            # 趋势方向判断阈值
            'trend_direction_ema_threshold': 0.02,

            # 时间窗口配置
            'price_range_lookback_periods': 20,
            'trend_consistency_periods': 10,

            # 危险评分权重
            'danger_score_weights': {
                'high_volatility': 0.4,
                'low_trend_strength': 0.3,
                'price_whipsaw': 0.2,
                'volume_inconsistency': 0.1 # 尽管当前未用，但保留以备扩展
            }
        }
    
    def analyze_market_state(self, global_market_data: Dict[str, float], 
                           recent_price_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        分析当前市场状态
        
        Args:
            global_market_data: 全局市场状态数据
            recent_price_data: 最近的价格数据（可选，用于更详细分析）
            
        Returns:
            市场状态分析结果
        """
        analysis_result = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'market_regime': MarketRegime.UNKNOWN,
            'danger_score': 0.0,
            'is_dangerous_for_trading': False,
            'analysis_details': {},
            'trading_recommendation': 'neutral',
            'filter_reasons': []
        }
        
        try:
            # 提取关键指标
            atr_percent = global_market_data.get('global_atr_percent', 0.0)
            adx_value = global_market_data.get('global_adx', 0.0)
            trend_strength = global_market_data.get('global_trend_strength', 0)
            ema_diff_pct = global_market_data.get('global_ema_diff_pct', 0.0)
            volatility_level = global_market_data.get('global_volatility_level', 0)
            
            # 1. 波动率分析
            volatility_analysis = self._analyze_volatility(atr_percent, volatility_level)
            analysis_result['analysis_details']['volatility'] = volatility_analysis
            
            # 2. 趋势强度分析
            trend_analysis = self._analyze_trend_strength(adx_value, trend_strength, ema_diff_pct)
            analysis_result['analysis_details']['trend'] = trend_analysis
            
            # 3. 价格行为分析（如果有价格数据）
            price_behavior_analysis = {}
            if recent_price_data is not None and not recent_price_data.empty:
                price_behavior_analysis = self._analyze_price_behavior(recent_price_data)
            analysis_result['analysis_details']['price_behavior'] = price_behavior_analysis
            
            # 4. 综合市场状态判断
            market_regime = self._determine_market_regime(
                volatility_analysis, trend_analysis, price_behavior_analysis
            )
            analysis_result['market_regime'] = market_regime
            
            # 5. 危险评分计算
            danger_score = self._calculate_danger_score(
                volatility_analysis, trend_analysis, price_behavior_analysis
            )
            analysis_result['danger_score'] = danger_score
            
            # 6. 交易建议和过滤决策
            trading_recommendation, filter_reasons = self._generate_trading_recommendation(
                market_regime, danger_score, volatility_analysis, trend_analysis
            )
            analysis_result['trading_recommendation'] = trading_recommendation
            analysis_result['filter_reasons'] = filter_reasons
            analysis_result['is_dangerous_for_trading'] = danger_score > 0.6
            
            # 记录分析历史
            self.analysis_history.append(analysis_result)
            
            # 输出分析结果
            logger.info(f"[MarketStateAnalyzer] 市场状态: {market_regime.value}, 危险评分: {danger_score:.2f}")
            if analysis_result['is_dangerous_for_trading']:
                logger.warning(f"[MarketStateAnalyzer] 检测到危险交易环境: {', '.join(filter_reasons)}")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"[MarketStateAnalyzer] 市场状态分析失败: {e}")
            analysis_result['error'] = str(e)
            return analysis_result
    
    def _analyze_volatility(self, atr_percent: float, volatility_level: int) -> Dict[str, Any]:
        """分析波动率状态"""
        config = self.config
        
        analysis = {
            'atr_percent': atr_percent,
            'volatility_level': volatility_level,
            'classification': 'normal',
            'is_high_volatility': False,
            'danger_contribution': 0.0
        }
        
        if atr_percent > config['high_volatility_atr_threshold']:
            analysis['classification'] = 'high'
            analysis['is_high_volatility'] = True
            analysis['danger_contribution'] = min(1.0, atr_percent / config['high_volatility_atr_threshold'] - 1.0)
        elif atr_percent < config['low_volatility_atr_threshold']:
            analysis['classification'] = 'low'
        
        return analysis
    
    def _analyze_trend_strength(self, adx_value: float, trend_strength: int, 
                              ema_diff_pct: float) -> Dict[str, Any]:
        """分析趋势强度"""
        config = self.config
        
        analysis = {
            'adx_value': adx_value,
            'trend_strength': trend_strength,
            'ema_diff_pct': ema_diff_pct,
            'has_clear_trend': False,
            'is_sideways': False,
            'danger_contribution': 0.0
        }
        
        # 判断是否有明确趋势
        if adx_value > config['strong_trend_adx_threshold']:
            analysis['has_clear_trend'] = True
            analysis['trend_classification'] = 'strong'
        elif adx_value > config['low_trend_strength_adx_threshold']:
            analysis['trend_classification'] = 'moderate'
        else:
            analysis['trend_classification'] = 'weak'
            analysis['is_sideways'] = True
            # 低趋势强度增加危险评分
            analysis['danger_contribution'] = (config['low_trend_strength_adx_threshold'] - adx_value) / config['low_trend_strength_adx_threshold']
            analysis['danger_contribution'] = max(0.0, min(1.0, analysis['danger_contribution']))
        
        return analysis
    
    def _analyze_price_behavior(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """分析价格行为模式，增强了对数据不足和异常情况的处理"""
        config = self.config
        lookback = config['price_range_lookback_periods']
        
        analysis = {
            'has_sufficient_data': False,
            'price_range_pct': 0.0,
            'is_range_bound': False,
            'whipsaw_count': 0,
            'danger_contribution': 0.0
        }

        # 严格检查输入数据
        if not isinstance(price_data, pd.DataFrame) or price_data.empty:
            logger.warning("价格行为分析失败: 输入数据为空或非DataFrame。")
            return analysis

        if len(price_data) < lookback:
            logger.warning(f"价格行为分析: 数据不足({len(price_data)}行)，至少需要{lookback}行。")
            return analysis
        
        analysis['has_sufficient_data'] = True
        
        try:
            recent_data = price_data.tail(lookback)
            
            # 计算价格区间
            high_price = recent_data['high'].max()
            low_price = recent_data['low'].min()
            current_price = recent_data['close'].iloc[-1]
            
            # 安全除法
            if current_price > 1e-9:
                price_range_pct = (high_price - low_price) / current_price
            else:
                price_range_pct = 0.0
            analysis['price_range_pct'] = price_range_pct
            
            # 判断是否区间震荡
            if price_range_pct < config['sideways_price_range_threshold']:
                analysis['is_range_bound'] = True
            
            # 计算价格鞭打次数（快速反转）
            close_prices = recent_data['close']
            price_changes = close_prices.diff() # 使用绝对差值而非百分比，更稳定
            
            # 标记价格变动方向 (+1, -1, 0)
            directions = np.sign(price_changes).fillna(0)
            # 计算方向改变的次数
            direction_changes = directions.diff().abs()
            whipsaw_count = int(direction_changes[direction_changes == 2].count()) # 从+1到-1或-1到+1

            analysis['whipsaw_count'] = whipsaw_count
            
            # 鞭打效应增加危险评分
            whipsaw_threshold = config.get('price_whipsaw_threshold', 3)
            if whipsaw_count > whipsaw_threshold:
                # 评分与超出阈值的程度成正比
                analysis['danger_contribution'] = min(1.0, (whipsaw_count - whipsaw_threshold) / 5.0)

        except Exception as e:
            logger.warning(f"价格行为分析失败: {e}")
            # 出错时重置为默认值
            analysis['has_sufficient_data'] = False
        
        return analysis
    
    def _determine_market_regime(self, volatility_analysis: Dict, trend_analysis: Dict, 
                               price_behavior_analysis: Dict) -> MarketRegime:
        """确定市场状态，采用更清晰的优先级判断逻辑"""
        
        is_high_vol = volatility_analysis['is_high_volatility']
        is_low_vol = volatility_analysis['classification'] == 'low'
        is_sideways = trend_analysis['is_sideways']
        has_strong_trend = trend_analysis['trend_classification'] == 'strong'
        
        # 优先级 1: 判断最危险的状态 - 高波动盘整
        if is_high_vol and is_sideways:
            return MarketRegime.HIGH_VOLATILITY_SIDEWAYS
        
        # 优先级 2: 判断强劲趋势状态 (🚀 V12.0 新增强劲趋势识别)
        if has_strong_trend:
            ema_diff_pct = trend_analysis['ema_diff_pct']
            adx_value = trend_analysis.get('adx_value', 0.0)

            # 🚀 V12.0: 强劲趋势判断标准 (ADX > 40 且 EMA差值显著)
            strong_trend_adx_threshold = self.config.get('strong_trend_adx_threshold', 40.0)
            strong_trend_ema_threshold = self.config.get('strong_trend_ema_threshold', 0.05)  # 5%

            if adx_value > strong_trend_adx_threshold and abs(ema_diff_pct) > strong_trend_ema_threshold:
                if ema_diff_pct > 0:
                    return MarketRegime.STRONG_UPTREND
                else:
                    return MarketRegime.STRONG_DOWNTREND

            # 普通趋势判断
            trend_direction_threshold = self.config.get('trend_direction_ema_threshold', 0.02)
            if ema_diff_pct > trend_direction_threshold:
                return MarketRegime.TRENDING_UP
            elif ema_diff_pct < -trend_direction_threshold:
                return MarketRegime.TRENDING_DOWN
        
        # 优先级 3: 判断低波动盘整
        if is_low_vol and is_sideways:
            return MarketRegime.LOW_VOLATILITY_SIDEWAYS
        
        # 优先级 4: 判断正常趋势
        if trend_analysis['trend_classification'] == 'moderate':
            return MarketRegime.NORMAL_TRENDING
        
        # 其他情况，标记为未知
        return MarketRegime.UNKNOWN
    
    def _calculate_danger_score(self, volatility_analysis: Dict, trend_analysis: Dict,
                              price_behavior_analysis: Dict) -> float:
        """计算危险评分"""
        # 🎯 修复：安全获取配置，提供默认值
        weights = self.config.get('danger_score_weights', {
            'high_volatility': 0.4,
            'low_trend_strength': 0.3,
            'price_whipsaw': 0.2,
            'volume_inconsistency': 0.1
        })
        
        danger_score = 0.0
        
        # 高波动率贡献
        danger_score += volatility_analysis.get('danger_contribution', 0.0) * weights['high_volatility']
        
        # 低趋势强度贡献
        danger_score += trend_analysis.get('danger_contribution', 0.0) * weights['low_trend_strength']
        
        # 价格鞭打贡献
        danger_score += price_behavior_analysis.get('danger_contribution', 0.0) * weights['price_whipsaw']
        
        return min(1.0, danger_score)
    
    def _generate_trading_recommendation(self, market_regime: MarketRegime, danger_score: float,
                                       volatility_analysis: Dict, trend_analysis: Dict) -> Tuple[str, List[str]]:
        """生成交易建议和过滤原因"""
        filter_reasons = []
        
        # 高波动盘整 - 最危险的状态
        if market_regime == MarketRegime.HIGH_VOLATILITY_SIDEWAYS:
            filter_reasons.append("高波动盘整期")
            filter_reasons.append(f"ATR={volatility_analysis['atr_percent']:.2f}%")
            filter_reasons.append(f"ADX={trend_analysis['adx_value']:.1f}")
            return "avoid", filter_reasons
        
        # 高危险评分
        if danger_score > 0.7:
            filter_reasons.append("高危险评分")
            filter_reasons.append(f"危险评分={danger_score:.2f}")
            return "avoid", filter_reasons
        
        # 中等危险评分
        if danger_score > 0.4:
            filter_reasons.append("中等风险")
            return "cautious", filter_reasons
        
        # 强趋势 - 有利环境
        if market_regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
            return "favorable", []
        
        # 正常情况
        return "neutral", []
    
    def get_current_market_summary(self) -> Dict[str, Any]:
        """获取当前市场状态摘要"""
        if not self.analysis_history:
            return {'status': 'no_data'}
        
        latest_analysis = self.analysis_history[-1]
        recent_analyses = self.analysis_history[-5:]  # 最近5次分析
        
        return {
            'current_regime': latest_analysis['market_regime'].value,
            'current_danger_score': latest_analysis['danger_score'],
            'is_currently_dangerous': latest_analysis['is_dangerous_for_trading'],
            'current_recommendation': latest_analysis['trading_recommendation'],
            'recent_danger_scores': [a['danger_score'] for a in recent_analyses],
            'danger_trend': 'increasing' if len(recent_analyses) >= 2 and recent_analyses[-1]['danger_score'] > recent_analyses[-2]['danger_score'] else 'stable',
            'total_analyses': len(self.analysis_history),
            'last_analysis_time': latest_analysis['timestamp']
        }
