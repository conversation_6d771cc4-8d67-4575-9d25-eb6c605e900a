# PriceFetcher.py

import time
import os # <--- 添加 os 用于获取环境变量 (如果需要)
import threading
import json
import logging
import sys
from binance import ThreadedWebsocketManager
import aiohttp # <--- 添加 aiohttp 用于 ClientTimeout

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from core.websocket_manager import WebSocketConnectionManager, ConnectionState

logger = logging.getLogger(__name__)

class PriceFetcher:
    DEFAULT_SYMBOL = 'ETHUSDT' # 你在 SimMain 中设置的是 ETHUSDT

    def __init__(self, symbol=None, client_for_ws=None): # client_for_ws 在这里可能不需要了
        """
        初始化价格获取器，使用 WebSocket。
        """
        self.symbol_to_fetch = (symbol if symbol is not None else self.DEFAULT_SYMBOL).upper()
        self.latest_price = None
        self.last_price_update_time = 0
        self.price_lock = threading.Lock()
        self.stream_name = None # 将在 start_stream 中被赋值
        self.is_running = False

        # --- 为 PriceFetcher 的 TWM 配置代理和超时 ---
        PROXY_URL_PF = 'http://127.0.0.1:7897' # 与你的代理配置一致

        session_timeout_config_pf = aiohttp.ClientTimeout(
            total=60,
            connect=25,
        )

        session_init_params_pf = {
            "timeout": session_timeout_config_pf,
            "trust_env": True, # 让 aiohttp 尝试从环境变量读取代理
                               # (虽然AsyncClient的_request也会传proxy=self.https_proxy)
            "connector": None, # 使用 aiohttp 默认的 TCPConnector
        }

        twm_init_kwargs_pf = {
            "api_key": None, # 公共行情流不需要
            "api_secret": None,
            "requests_params": { # 主要用于 AsyncClient 提取 timeout
                "timeout": session_timeout_config_pf
            },
            "https_proxy": PROXY_URL_PF, # 传递给 AsyncClient 的 https_proxy 参数
            "session_params": session_init_params_pf # 传递给 AsyncClient 的 session_params
        }
        print(f"[PriceFetcher DEBUG] Initializing TWM with kwargs: {twm_init_kwargs_pf}")
        self.twm = ThreadedWebsocketManager(**twm_init_kwargs_pf)
        # --- 结束 TWM 配置 ---

        print(f"PriceFetcher (WebSocket): 配置为获取 {self.symbol_to_fetch} 价格。")

    def _handle_socket_message(self, msg):
        """
        处理从WebSocket接收到的消息。
        """
        # print(f"DEBUG [PriceFetcher WS]: Raw msg: {msg}") # 用于调试
        if msg.get('e') == 'error':
            print(f"!!! PriceFetcher (WebSocket) Error: {msg.get('m')}")
            # 可以考虑如何处理流错误，例如记录或尝试重启
            return

        # PriceFetcher 通常订阅单个流，所以消息中可能没有 'stream' 字段
        # 我们依赖消息内容 ('e' 和 's') 来确认
        msg_event_type = msg.get('e')
        msg_symbol = msg.get('s')

        if msg_event_type == '24hrTicker' and msg_symbol == self.symbol_to_fetch:
            try:
                new_price = float(msg['c'])
                with self.price_lock:
                    self.latest_price = new_price
                    self.last_price_update_time = time.time()
                # print(f"DEBUG [PriceFetcher WS]: {self.symbol_to_fetch} updated to {self.latest_price}")
            except (KeyError, ValueError) as e:
                print(f"!!! PriceFetcher (WebSocket): Error parsing ticker message: {e} - Msg: {msg}")
        elif msg_event_type == 'aggTrade' and msg_symbol == self.symbol_to_fetch: # 如果用 aggTrade
            try:
                new_price = float(msg['p'])
                with self.price_lock:
                    self.latest_price = new_price
                    self.last_price_update_time = time.time()
            except (KeyError, ValueError) as e:
                print(f"!!! PriceFetcher (WebSocket): Error parsing aggTrade message: {e} - Msg: {msg}")
        # 可以根据需要添加对其他流类型（如kline）的处理，但PriceFetcher主要用于价格

    def start_stream(self):
        """
        启动WebSocket数据流。
        """
        if self.is_running:
            print(f"PriceFetcher (WebSocket): {self.symbol_to_fetch} 的数据流已在运行。")
            return

        if not self.twm.is_alive():
            print("INFO: [PriceFetcher] TWM not alive, starting TWM...")
            try:
                self.twm.start()
                print("INFO: [PriceFetcher] TWM started, sleeping for 2 seconds for AsyncClient init...")
                time.sleep(2) # 给AsyncClient初始化（包括可能的ping）留出时间
                print("INFO: [PriceFetcher] Sleep finished, proceeding to start socket.")
            except Exception as e_twm_start_pf:
                print(f"!!! ERROR: [PriceFetcher] Error starting TWM: {e_twm_start_pf}")
                return


        # 选择 @ticker 流获取价格快照
        stream_type_to_use = "ticker" # 或者 "aggTrade"

        print(f"INFO: [PriceFetcher] Starting {stream_type_to_use} stream for {self.symbol_to_fetch}...")
        try:
            if stream_type_to_use == "ticker":
                self.stream_name = self.twm.start_symbol_ticker_socket(
                    symbol=self.symbol_to_fetch,
                    callback=self._handle_socket_message
                )
            elif stream_type_to_use == "aggTrade":
                self.stream_name = self.twm.start_aggtrade_socket(
                    symbol=self.symbol_to_fetch,
                    callback=self._handle_socket_message
                )
            # 如果需要kline，可以取消注释并调整：
            # elif stream_type_to_use == "kline":
            #     self.stream_name = self.twm.start_kline_socket(
            #         symbol=self.symbol_to_fetch,
            #         callback=self._handle_socket_message,
            #         interval='1m' # 或其他所需间隔
            #     )

            if self.stream_name:
                self.is_running = True
                print(f"PriceFetcher (WebSocket): 已启动 {self.symbol_to_fetch} 的 {stream_type_to_use} 数据流 (Stream ID: {self.stream_name})。")
            else:
                print(f"!!! PriceFetcher (WebSocket): 启动 {self.symbol_to_fetch} {stream_type_to_use} 数据流失败 (TWM call returned None/False)。")
                # 如果启动流失败，可能不需要停止整个TWM，除非这是唯一用途
                # self.twm.stop()
        except Exception as e_start_socket_pf:
            print(f"!!! ERROR: [PriceFetcher] Exception while starting {stream_type_to_use} stream for {self.symbol_to_fetch}: {e_start_socket_pf}")
            self.is_running = False


    def stop_stream(self):
        """
        停止WebSocket数据流。
        """
        if not self.is_running or not self.stream_name:
            print(f"PriceFetcher (WebSocket): {self.symbol_to_fetch} 的数据流未运行或流名称未知。")
            return

        print(f"PriceFetcher (WebSocket): 正在停止 {self.symbol_to_fetch} 的数据流 (Stream ID: {self.stream_name})...")
        if self.twm.is_alive():
            self.twm.stop_socket(self.stream_name)
            # 通常不需要在停止单个流后立即 join TWM，除非这是TWM的唯一用途且要关闭TWM
            # self.twm.join(timeout=3)
            print(f"PriceFetcher (WebSocket): {self.symbol_to_fetch} 的数据流已请求停止。")
        else:
            print("WARNING: [PriceFetcher] TWM was not alive when trying to stop socket.")

        self.is_running = False
        self.stream_name = None # 清除流名称


    def get_current_price(self):
        with self.price_lock:
            price_to_return = self.latest_price
        return price_to_return

# --- 测试代码 (可选，如果直接运行此文件) ---
if __name__ == "__main__":
    from datetime import datetime # 确保导入
    print("--- 测试 PriceFetcher (WebSocket, ETHUSDT) ---")
    # 模拟环境变量 (如果直接运行此文件，os.getenv 可能获取不到IDE中设置的环境变量)
    # 如果你的系统环境变量已设置 HTTPS_PROXY，这里就不需要了
    # if 'HTTPS_PROXY' not in os.environ:
    #     os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7897'
    # print(f"[Test Main] HTTPS_PROXY: {os.getenv('HTTPS_PROXY')}")

    eth_fetcher = PriceFetcher(symbol='ETHUSDT')
    eth_fetcher.start_stream()

    try:
        for i in range(60): # 运行一段时间观察
            time.sleep(1)
            eth_price = eth_fetcher.get_current_price()
            current_time_str = datetime.now().strftime("%H:%M:%S")
            if eth_price is not None:
                print(f"[{current_time_str}] 最新 ETH 价格: {eth_price:.2f} USDT")
            else:
                print(f"[{current_time_str}] 等待 ETH 价格...")
            if not eth_fetcher.is_running and eth_fetcher.stream_name is None: # 如果流意外停止
                print("!!! [Test Main] PriceFetcher stream seems to have stopped. Attempting to restart...")
                # eth_fetcher.start_stream() # 可以尝试重启，但要注意循环重启
                break # 或者直接退出测试

    except KeyboardInterrupt:
        print("\n用户中断测试。")
    finally:
        print("\n--- 正在停止 PriceFetcher 的 WebSocket 数据流 ---")
        if eth_fetcher.is_running: # 确保先检查 is_running
            eth_fetcher.stop_stream()
        # 如果TWM只被这个fetcher使用，并且你想在测试结束时关闭TWM
        if hasattr(eth_fetcher, 'twm') and eth_fetcher.twm.is_alive():
             print("--- [Test Main] Stopping TWM for PriceFetcher ---")
             eth_fetcher.twm.stop() # 调用TWM的全局停止
             eth_fetcher.twm.join(timeout=5)
             if eth_fetcher.twm.is_alive():
                 print("!!! [Test Main] TWM did not stop after join().")
             else:
                 print("--- [Test Main] TWM stopped successfully. ---")

        print("测试完成。")
