#!/usr/bin/env python3
"""
综合数据泄露测试脚本

此脚本提供全面的数据泄露检测和验证功能，包括：
1. 特征工程过程的数据泄露检测
2. 训练/测试分割的时间序列完整性验证
3. 模型性能的合理性检查
4. 特征分布的时间稳定性分析
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def test_feature_engineering_leakage():
    """测试特征工程过程中的数据泄露"""
    print("🔍 测试特征工程数据泄露...")
    
    try:
        # 导入必要模块
        from src.core import data_utils
        from config import get_target_config_wrapper
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=1000, freq='15T')
        test_data = pd.DataFrame({
            'open': np.random.randn(1000).cumsum() + 100,
            'high': np.random.randn(1000).cumsum() + 102,
            'low': np.random.randn(1000).cumsum() + 98,
            'close': np.random.randn(1000).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 1000),
        }, index=dates)
        
        # 确保OHLC关系正确
        test_data['high'] = test_data[['open', 'close']].max(axis=1) + np.abs(np.random.randn(1000))
        test_data['low'] = test_data[['open', 'close']].min(axis=1) - np.abs(np.random.randn(1000))
        
        # 获取目标配置
        target_config = get_target_config_wrapper('BTC_15m_UP')
        
        # 生成特征
        features_df = data_utils.add_classification_features(test_data.copy(), target_config)
        
        if features_df is None:
            print("❌ 特征生成失败")
            return False
            
        # 检查1: 验证没有未来数据泄露
        print("  📊 检查特征中是否包含未来信息...")
        
        # 检查特征名称
        future_keywords = ['future', 'next', 'ahead', 'forward', 'target']
        leaked_features = []
        for col in features_df.columns:
            if any(keyword in col.lower() for keyword in future_keywords):
                leaked_features.append(col)
        
        if leaked_features:
            print(f"❌ 发现包含未来信息的特征: {leaked_features}")
            return False
        else:
            print("✅ 特征名称检查通过")
            
        # 检查2: 验证特征计算的时间依赖性
        print("  📊 检查特征的时间依赖性...")
        
        # 分割数据为两部分
        split_point = len(test_data) // 2
        data_part1 = test_data.iloc[:split_point]
        data_part2 = test_data.iloc[split_point:]
        
        # 分别计算特征
        features_part1 = data_utils.add_classification_features(data_part1.copy(), target_config)
        features_part2 = data_utils.add_classification_features(data_part2.copy(), target_config)
        
        if features_part1 is None or features_part2 is None:
            print("❌ 分段特征生成失败")
            return False
            
        # 检查重叠部分的特征是否一致（允许小的数值误差）
        overlap_start = data_part2.index[0]
        if overlap_start in features_part1.index:
            common_features = set(features_part1.columns) & set(features_part2.columns)
            
            for feature in list(common_features)[:5]:  # 检查前5个特征
                val1 = features_part1.loc[overlap_start, feature] if overlap_start in features_part1.index else np.nan
                val2 = features_part2.loc[overlap_start, feature] if overlap_start in features_part2.index else np.nan
                
                if not (pd.isna(val1) and pd.isna(val2)):
                    if not pd.isna(val1) and not pd.isna(val2):
                        if abs(val1 - val2) > 1e-6:
                            print(f"⚠️  特征 {feature} 在重叠点的值不一致: {val1} vs {val2}")
        
        print("✅ 特征工程数据泄露测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 特征工程测试失败: {e}")
        return False

def test_time_series_split_integrity():
    """测试时间序列分割的完整性"""
    print("🔍 测试时间序列分割完整性...")
    
    try:
        from sklearn.model_selection import TimeSeriesSplit
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=1000, freq='15T')
        test_data = pd.DataFrame({
            'feature1': np.random.randn(1000),
            'feature2': np.random.randn(1000),
            'target': np.random.randint(0, 2, 1000)
        }, index=dates)
        
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=3)
        
        for fold, (train_idx, test_idx) in enumerate(tscv.split(test_data)):
            train_data = test_data.iloc[train_idx]
            test_data_fold = test_data.iloc[test_idx]
            
            # 检查时间顺序
            if train_data.index.max() >= test_data_fold.index.min():
                print(f"❌ 第{fold+1}折: 训练集最大时间 >= 测试集最小时间")
                return False
                
            # 检查时间连续性
            time_gap = test_data_fold.index.min() - train_data.index.max()
            expected_gap = pd.Timedelta('15T')  # 15分钟间隔
            
            if time_gap < expected_gap:
                print(f"❌ 第{fold+1}折: 时间间隔过小 ({time_gap})")
                return False
        
        print("✅ 时间序列分割完整性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间序列分割测试失败: {e}")
        return False

def test_feature_stability():
    """测试特征的时间稳定性"""
    print("🔍 测试特征时间稳定性...")
    
    try:
        # 创建具有趋势的测试数据
        dates = pd.date_range('2024-01-01', periods=1000, freq='15T')
        trend = np.linspace(0, 10, 1000)  # 添加趋势
        
        test_data = pd.DataFrame({
            'open': trend + np.random.randn(1000) * 0.1 + 100,
            'high': trend + np.random.randn(1000) * 0.1 + 102,
            'low': trend + np.random.randn(1000) * 0.1 + 98,
            'close': trend + np.random.randn(1000) * 0.1 + 100,
            'volume': np.random.randint(1000, 10000, 1000),
        }, index=dates)
        
        # 确保OHLC关系正确
        test_data['high'] = test_data[['open', 'close']].max(axis=1) + np.abs(np.random.randn(1000) * 0.1)
        test_data['low'] = test_data[['open', 'close']].min(axis=1) - np.abs(np.random.randn(1000) * 0.1)
        
        # 计算简单的技术指标
        test_data['sma_20'] = test_data['close'].rolling(20).mean()
        test_data['rsi'] = calculate_simple_rsi(test_data['close'], 14)
        
        # 分析特征分布的时间稳定性
        window_size = 200
        num_windows = len(test_data) // window_size
        
        feature_stats = []
        for i in range(num_windows):
            start_idx = i * window_size
            end_idx = (i + 1) * window_size
            window_data = test_data.iloc[start_idx:end_idx]
            
            stats = {
                'window': i,
                'sma_mean': window_data['sma_20'].mean(),
                'sma_std': window_data['sma_20'].std(),
                'rsi_mean': window_data['rsi'].mean(),
                'rsi_std': window_data['rsi'].std(),
            }
            feature_stats.append(stats)
        
        stats_df = pd.DataFrame(feature_stats)
        
        # 检查特征分布的稳定性
        for feature in ['sma_mean', 'rsi_mean']:
            if len(stats_df) > 1:
                cv = stats_df[feature].std() / stats_df[feature].mean()  # 变异系数
                if cv > 0.5:  # 如果变异系数过大
                    print(f"⚠️  特征 {feature} 的时间稳定性较差 (CV={cv:.3f})")
        
        print("✅ 特征时间稳定性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 特征稳定性测试失败: {e}")
        return False

def calculate_simple_rsi(prices, period=14):
    """计算简单的RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def test_model_performance_reasonableness():
    """测试模型性能的合理性"""
    print("🔍 测试模型性能合理性...")
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import accuracy_score, precision_score, recall_score
        
        # 创建测试数据
        n_samples = 1000
        dates = pd.date_range('2024-01-01', periods=n_samples, freq='15T')
        
        # 创建有一定预测性的特征
        np.random.seed(42)
        feature1 = np.random.randn(n_samples)
        feature2 = np.random.randn(n_samples)
        noise = np.random.randn(n_samples) * 0.5
        
        # 目标变量与特征有一定相关性，但不是完美相关
        target_prob = 1 / (1 + np.exp(-(0.5 * feature1 + 0.3 * feature2 + noise)))
        target = (np.random.rand(n_samples) < target_prob).astype(int)
        
        features_df = pd.DataFrame({
            'feature1': feature1,
            'feature2': feature2,
            'feature3': np.random.randn(n_samples),  # 噪声特征
        }, index=dates)
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=3)
        scores = []
        
        for train_idx, test_idx in tscv.split(features_df):
            X_train, X_test = features_df.iloc[train_idx], features_df.iloc[test_idx]
            y_train, y_test = target[train_idx], target[test_idx]
            
            # 训练模型
            model = RandomForestClassifier(n_estimators=50, random_state=42)
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            
            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            
            scores.append({
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall
            })
        
        # 分析性能
        avg_accuracy = np.mean([s['accuracy'] for s in scores])
        avg_precision = np.mean([s['precision'] for s in scores])
        avg_recall = np.mean([s['recall'] for s in scores])
        
        print(f"  📊 平均准确率: {avg_accuracy:.3f}")
        print(f"  📊 平均精确率: {avg_precision:.3f}")
        print(f"  📊 平均召回率: {avg_recall:.3f}")
        
        # 合理性检查
        if avg_accuracy > 0.9:
            print("⚠️  准确率过高，可能存在数据泄露")
            return False
        elif avg_accuracy < 0.4:
            print("⚠️  准确率过低，模型可能无效")
            return False
        
        print("✅ 模型性能合理性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("🚀 综合数据泄露测试开始")
    print("=" * 80)
    
    tests = [
        ("特征工程数据泄露", test_feature_engineering_leakage),
        ("时间序列分割完整性", test_time_series_split_integrity),
        ("特征时间稳定性", test_feature_stability),
        ("模型性能合理性", test_model_performance_reasonableness),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*80}")
    print("📋 测试结果总结")
    print(f"{'='*80}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据泄露修复验证成功。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查数据泄露修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
