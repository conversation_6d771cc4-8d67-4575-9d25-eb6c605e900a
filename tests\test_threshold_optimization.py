#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值优化单元测试
测试阈值优化器和相关功能
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 忽略警告
warnings.filterwarnings('ignore')

class TestThresholdOptimizer(unittest.TestCase):
    """测试阈值优化器"""
    
    def setUp(self):
        try:
            from src.core.threshold_optimization import ThresholdOptimizer
            self.ThresholdOptimizer = ThresholdOptimizer
        except ImportError:
            self.skipTest("Threshold optimizer not available")
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 1000
        
        # 创建有区分度的数据
        self.y_true = np.random.choice([0, 1], size=n_samples, p=[0.7, 0.3])
        
        # 创建与真实标签相关的概率
        self.y_proba = np.random.random(n_samples)
        # 让正类的概率更高
        self.y_proba[self.y_true == 1] += 0.3
        self.y_proba = np.clip(self.y_proba, 0, 1)
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        self.assertEqual(optimizer.target_name, "test")
        self.assertIsInstance(optimizer.optimization_history, list)
    
    def test_grid_search_optimization(self):
        """测试网格搜索优化"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        result = optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="grid_search",
            metric="f1"
        )
        
        if result and result.get('success'):
            self.assertIn('optimal_threshold', result)
            self.assertIn('best_score', result)
            self.assertIn('method', result)
            self.assertEqual(result['method'], 'grid_search')
            
            # 检查阈值范围
            self.assertGreaterEqual(result['optimal_threshold'], 0)
            self.assertLessEqual(result['optimal_threshold'], 1)
            
            # 检查分数范围
            self.assertGreaterEqual(result['best_score'], 0)
            self.assertLessEqual(result['best_score'], 1)
    
    def test_bayesian_optimization(self):
        """测试贝叶斯优化"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        result = optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="bayesian",
            metric="f1",
            n_calls=10  # 减少调用次数以加快测试
        )
        
        if result and result.get('success'):
            self.assertIn('optimal_threshold', result)
            self.assertIn('best_score', result)
            self.assertIn('method', result)
            self.assertEqual(result['method'], 'bayesian')
            
            # 检查阈值范围
            self.assertGreaterEqual(result['optimal_threshold'], 0)
            self.assertLessEqual(result['optimal_threshold'], 1)
        else:
            # 如果贝叶斯优化不可用（缺少依赖），跳过
            self.skipTest("Bayesian optimization not available (missing dependencies)")
    
    def test_different_metrics(self):
        """测试不同的优化指标"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        metrics = ["f1", "precision", "recall", "accuracy"]
        
        for metric in metrics:
            with self.subTest(metric=metric):
                result = optimizer.optimize_threshold(
                    y_true=self.y_true,
                    y_proba=self.y_proba,
                    method="grid_search",
                    metric=metric
                )
                
                if result and result.get('success'):
                    self.assertIn('optimal_threshold', result)
                    self.assertIn('best_score', result)
                    self.assertIn('metric', result)
                    self.assertEqual(result['metric'], metric)
    
    def test_precision_constraint(self):
        """测试精确率约束"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        result = optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="grid_search",
            metric="recall",
            min_precision=0.6
        )
        
        if result and result.get('success'):
            self.assertIn('optimal_threshold', result)
            self.assertIn('precision_constraint', result)
            self.assertEqual(result['precision_constraint'], 0.6)
            
            # 验证精确率约束
            if 'evaluation_metrics' in result:
                precision = result['evaluation_metrics'].get('precision')
                if precision is not None:
                    self.assertGreaterEqual(precision, 0.6)
    
    def test_invalid_input(self):
        """测试无效输入"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        # 测试长度不匹配
        result = optimizer.optimize_threshold(
            y_true=self.y_true[:100],
            y_proba=self.y_proba,
            method="grid_search"
        )
        self.assertFalse(result.get('success', True))
        
        # 测试空输入
        result = optimizer.optimize_threshold(
            y_true=[],
            y_proba=[],
            method="grid_search"
        )
        self.assertFalse(result.get('success', True))
        
        # 测试无效方法
        result = optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="invalid_method"
        )
        self.assertFalse(result.get('success', True))
    
    def test_optimization_history(self):
        """测试优化历史记录"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        # 执行多次优化
        optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="grid_search",
            metric="f1"
        )
        
        optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="grid_search",
            metric="precision"
        )
        
        # 检查历史记录
        history = optimizer.get_optimization_history()
        self.assertIsInstance(history, list)
        
        if len(history) > 0:
            # 检查历史记录格式
            for record in history:
                self.assertIn('timestamp', record)
                self.assertIn('method', record)
                self.assertIn('metric', record)
                self.assertIn('result', record)
    
    def test_performance_comparison(self):
        """测试性能对比"""
        optimizer = self.ThresholdOptimizer(target_name="test")
        
        # 网格搜索
        import time
        start_time = time.time()
        grid_result = optimizer.optimize_threshold(
            y_true=self.y_true,
            y_proba=self.y_proba,
            method="grid_search",
            metric="f1"
        )
        grid_time = time.time() - start_time
        
        # 检查结果
        if grid_result and grid_result.get('success'):
            self.assertIn('computation_time', grid_result)
            self.assertGreater(grid_result['computation_time'], 0)
            
            # 验证计算时间的合理性
            self.assertLess(abs(grid_result['computation_time'] - grid_time), 1.0)


class TestThresholdOptimizationIntegration(unittest.TestCase):
    """测试阈值优化集成功能"""
    
    def setUp(self):
        # 创建测试数据
        np.random.seed(42)
        n_samples = 500
        
        self.y_true = np.random.choice([0, 1], size=n_samples, p=[0.6, 0.4])
        self.y_proba = np.random.random(n_samples)
        self.y_proba[self.y_true == 1] += 0.4
        self.y_proba = np.clip(self.y_proba, 0, 1)
    
    def test_find_optimal_threshold_integration(self):
        """测试find_optimal_threshold函数集成"""
        try:
            from src.core.data_utils import find_optimal_threshold
            
            # 测试支持的方法
            supported_methods = ["f1", "precision_recall", "youden", "balanced"]
            
            for method in supported_methods:
                with self.subTest(method=method):
                    result = find_optimal_threshold(
                        self.y_true, self.y_proba,
                        target_name="test", method=method
                    )
                    
                    if result is not None:
                        self.assertIn('optimal_threshold', result)
                        self.assertIn('method', result)
                        self.assertEqual(result['method'], method)
            
            # 测试不支持的方法
            result = find_optimal_threshold(
                self.y_true, self.y_proba,
                target_name="test", method="grid_search"
            )
            self.assertIsNone(result)
            
        except ImportError:
            self.skipTest("find_optimal_threshold function not available")
    
    def test_threshold_optimization_workflow(self):
        """测试完整的阈值优化工作流"""
        try:
            from src.core.threshold_optimization import ThresholdOptimizer
            
            # 创建优化器
            optimizer = ThresholdOptimizer(target_name="workflow_test")
            
            # 步骤1: 网格搜索找到初始阈值
            grid_result = optimizer.optimize_threshold(
                y_true=self.y_true,
                y_proba=self.y_proba,
                method="grid_search",
                metric="f1"
            )
            
            if grid_result and grid_result.get('success'):
                initial_threshold = grid_result['optimal_threshold']
                
                # 步骤2: 在初始阈值附近进行精细搜索
                fine_result = optimizer.optimize_threshold(
                    y_true=self.y_true,
                    y_proba=self.y_proba,
                    method="grid_search",
                    metric="f1",
                    threshold_range=(max(0, initial_threshold - 0.1), 
                                   min(1, initial_threshold + 0.1)),
                    n_thresholds=50
                )
                
                if fine_result and fine_result.get('success'):
                    fine_threshold = fine_result['optimal_threshold']
                    
                    # 验证精细搜索的结果
                    self.assertGreaterEqual(fine_threshold, max(0, initial_threshold - 0.1))
                    self.assertLessEqual(fine_threshold, min(1, initial_threshold + 0.1))
                    
                    # 检查优化历史
                    history = optimizer.get_optimization_history()
                    self.assertGreaterEqual(len(history), 2)
            
        except ImportError:
            self.skipTest("ThresholdOptimizer not available")


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
