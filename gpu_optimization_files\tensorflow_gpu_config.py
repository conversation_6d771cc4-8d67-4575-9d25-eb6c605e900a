"""
TensorFlow GPU优化配置
适用于RTX 3070 + AMD处理器
"""

import os
import tensorflow as tf

def setup_tensorflow_optimization():
    """设置TensorFlow优化配置"""
    print("🚀 初始化TensorFlow优化...")
    
    # 设置CUDA环境变量
    cuda_path = r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8"
    if os.path.exists(cuda_path):
        os.environ['CUDA_PATH'] = cuda_path
        os.environ['CUDA_HOME'] = cuda_path
    
    # TensorFlow优化环境变量
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'  # 减少日志
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'  # 启用oneDNN
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'  # 异步GPU内存分配
    
    try:
        # 检测GPU
        gpus = tf.config.list_physical_devices('GPU')
        
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备")
            
            try:
                # 配置GPU内存增长
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                # 设置线程配置
                tf.config.threading.set_inter_op_parallelism_threads(4)
                tf.config.threading.set_intra_op_parallelism_threads(0)  # 使用所有核心
                
                print("✅ GPU优化配置完成")
                return True
                
            except RuntimeError as e:
                print(f"⚠️  GPU配置警告: {e}")
                print("GPU可能已被其他进程使用，将使用CPU模式")
                return False
        else:
            print("⚠️  未检测到GPU，使用CPU优化模式")
            
            # CPU优化配置
            tf.config.threading.set_inter_op_parallelism_threads(4)
            tf.config.threading.set_intra_op_parallelism_threads(16)  # AMD 16核
            
            return False
            
    except Exception as e:
        print(f"❌ TensorFlow配置失败: {e}")
        return False

def create_lstm_model_with_gpu(input_shape, num_classes=2):
    """创建GPU优化的LSTM模型"""
    try:
        # 尝试使用GPU
        gpus = tf.config.list_physical_devices('GPU')
        device = '/GPU:0' if gpus else '/CPU:0'
        
        print(f"使用设备: {device}")
        
        with tf.device(device):
            model = tf.keras.Sequential([
                tf.keras.layers.LSTM(128, return_sequences=True, input_shape=input_shape),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.2),
                
                tf.keras.layers.LSTM(64, return_sequences=False),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.2),
                
                tf.keras.layers.Dense(32, activation='relu'),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(16, activation='relu'),
                
                tf.keras.layers.Dense(
                    num_classes,
                    activation='softmax' if num_classes > 2 else 'sigmoid'
                )
            ])
        
        # 编译模型
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy' if num_classes > 2 else 'binary_crossentropy',
            metrics=['accuracy']
        )
        
        return model
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return None

# 自动初始化
if __name__ != "__main__":
    setup_tensorflow_optimization()
