#!/usr/bin/env python3
"""
直接测试元模型训练
绕过复杂的导入问题，直接调用元模型训练
"""

import os
import sys
import pandas as pd
import numpy as np
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_meta_model_training_test():
    """直接测试元模型训练"""
    logger.info("🎯 直接测试元模型训练...")
    
    try:
        # 检查数据文件
        x_meta_file = "meta_model_data/X_meta_features_oof.csv"
        y_meta_file = "meta_model_data/y_meta_target.csv"
        
        if not os.path.exists(x_meta_file) or not os.path.exists(y_meta_file):
            logger.error("❌ 元模型数据文件不存在")
            return False
        
        # 加载数据
        logger.info("📊 加载数据...")
        X_meta_df = pd.read_csv(x_meta_file, index_col=0)
        y_meta_series = pd.read_csv(y_meta_file, index_col=0).iloc[:, 0]
        
        logger.info(f"特征数据: {X_meta_df.shape}")
        logger.info(f"目标数据: {y_meta_series.shape}")
        logger.info(f"目标分布: {y_meta_series.value_counts().to_dict()}")
        
        # 检查特征列表
        logger.info("📋 特征列表:")
        for i, col in enumerate(X_meta_df.columns):
            if i < 10:  # 显示前10个
                logger.info(f"  {i+1:2d}. {col}")
            elif i == 10:
                logger.info(f"  ... 还有 {len(X_meta_df.columns) - 10} 个特征")
                break
        
        # 检查是否有交互特征
        interaction_features = [col for col in X_meta_df.columns if '_IN_' in col]
        if interaction_features:
            logger.info(f"✅ 发现 {len(interaction_features)} 个交互特征")
        else:
            logger.warning("⚠️ 未发现交互特征，但可以继续训练")
        
        # 尝试简单的元模型训练（使用LightGBM）
        logger.info("🤖 尝试简单的LightGBM训练...")
        
        try:
            import lightgbm as lgb
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import accuracy_score, classification_report
            
            # 数据分割
            X_train, X_test, y_train, y_test = train_test_split(
                X_meta_df, y_meta_series, test_size=0.2, random_state=42, stratify=y_meta_series
            )
            
            logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
            
            # 创建LightGBM模型
            model = lgb.LGBMClassifier(
                objective='binary',
                metric='binary_logloss',
                class_weight={0: 1.0, 1: 6.0},  # 使用修复后的权重
                n_estimators=100,  # 快速测试
                learning_rate=0.1,
                num_leaves=31,
                max_depth=5,
                random_state=42,
                verbose=-1
            )
            
            # 训练模型
            logger.info("🚀 开始训练...")
            model.fit(X_train, y_train)
            
            # 预测和评估
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            
            accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"✅ 测试准确率: {accuracy:.4f}")
            
            # 分类报告
            report = classification_report(y_test, y_pred, output_dict=True)
            class_1_recall = report['1']['recall']
            class_0_recall = report['0']['recall']
            
            logger.info(f"Class_0 (下跌) recall: {class_0_recall:.3f}")
            logger.info(f"Class_1 (上涨) recall: {class_1_recall:.3f}")
            
            # 检查是否有改善
            if class_1_recall > 0.4:  # 目标是从23.1%提升至≥50%
                logger.info("🎉 Class_1 recall有显著改善！")
            else:
                logger.warning(f"⚠️ Class_1 recall仍需改善: {class_1_recall:.1%}")
            
            # 特征重要性
            feature_importance = model.feature_importances_
            top_features = sorted(zip(X_meta_df.columns, feature_importance), 
                                key=lambda x: x[1], reverse=True)[:10]
            
            logger.info("🔝 Top 10 重要特征:")
            for i, (feature, importance) in enumerate(top_features, 1):
                logger.info(f"  {i:2d}. {feature}: {importance:.4f}")
            
            return True
            
        except ImportError as e:
            logger.error(f"❌ LightGBM导入失败: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 直接训练测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_os_import_fix():
    """检查os导入修复"""
    logger.info("🔧 检查os导入修复...")
    
    try:
        # 检查prediction.py中的修复
        with open("src/core/prediction.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找train_meta_model函数中的import os
        if "def train_meta_model" in content and "import os  # 🔧 修复" in content:
            logger.info("✅ os导入修复已应用")
            return True
        else:
            logger.warning("⚠️ os导入修复未找到")
            return False
        
    except Exception as e:
        logger.error(f"❌ 检查修复失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始直接元模型训练测试...")
    
    try:
        # 1. 检查os导入修复
        os_fix_ok = check_os_import_fix()
        
        # 2. 直接测试元模型训练
        training_ok = direct_meta_model_training_test()
        
        if os_fix_ok and training_ok:
            logger.info("🎉 直接元模型训练测试成功！")
            logger.info("📋 测试结果总结:")
            logger.info("  ✅ os导入修复已应用")
            logger.info("  ✅ 元模型可以正常训练")
            logger.info("  ✅ 数据格式正确")
            logger.info("  ✅ 模型性能可以评估")
            logger.info("")
            logger.info("🚀 建议下一步:")
            logger.info("  1. 重新运行完整的元模型训练流程")
            logger.info("  2. 监控Class_1 recall是否有改善")
            logger.info("  3. 如果需要交互特征，重新训练基础模型")
            return True
        else:
            logger.warning("⚠️ 部分测试失败")
            logger.info("测试结果:")
            logger.info(f"  os修复: {'✅' if os_fix_ok else '❌'}")
            logger.info(f"  训练测试: {'✅' if training_ok else '❌'}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 直接元模型训练测试成功！")
        print("🚀 现在可以重新运行元模型训练")
    else:
        print("\n❌ 直接元模型训练测试失败！")
