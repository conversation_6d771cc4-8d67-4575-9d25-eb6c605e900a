#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元标签质量权重系统使用示例

演示如何使用新的质量权重系统来提升模型训练质量
"""

import sys
import os
import pandas as pd
import numpy as np
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.meta_label_quality_config import (
    get_meta_label_quality_config,
    apply_quality_config_to_target,
    get_quality_config_template,
    validate_meta_label_quality_config
)
from src.core.data_utils import _calculate_meta_label_weights
from src.core.training_utils import prepare_training_data_with_weights

def create_sample_data():
    """创建示例数据"""
    np.random.seed(42)
    
    # 创建模拟的K线数据
    n_samples = 1000
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='15T')
    
    # 生成价格数据
    base_price = 50000
    returns = np.random.normal(0, 0.002, n_samples)  # 0.2%的标准波动率
    
    # 添加一些趋势和波动
    trend = np.sin(np.arange(n_samples) * 0.01) * 0.001
    returns += trend
    
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建OHLC数据
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'close': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'volume': np.random.uniform(100, 1000, n_samples)
    })
    
    # 确保high >= close >= low
    df['high'] = np.maximum(df['high'], df['close'])
    df['low'] = np.minimum(df['low'], df['close'])
    
    return df

def create_sample_target_variable(df: pd.DataFrame, threshold: float = 0.01) -> pd.Series:
    """创建示例目标变量"""
    # 简单的未来收益率标签
    future_returns = df['close'].pct_change(periods=2).shift(-2)
    
    # 创建三分类标签
    labels = pd.Series(-1, index=df.index)  # -1表示无效
    labels[future_returns > threshold] = 1   # 上涨
    labels[future_returns < -threshold] = 0  # 下跌
    labels[(future_returns >= -threshold) & (future_returns <= threshold)] = 2  # 中性
    
    # 移除最后几个无法计算未来收益的样本
    labels.iloc[-2:] = -1
    
    return labels

def example_basic_quality_weighting():
    """基本质量权重使用示例"""
    print("🚀 基本质量权重使用示例")
    print("=" * 50)
    
    # 1. 创建示例数据
    df = create_sample_data()
    target_series = create_sample_target_variable(df)
    
    # 添加目标变量到DataFrame
    df['target_2p_example'] = target_series
    
    print(f"数据形状: {df.shape}")
    print(f"目标变量分布:")
    print(target_series.value_counts().sort_index())
    
    # 2. 配置质量权重
    target_config = {
        'name': 'example_target',
        'prediction_periods': [2],
        'target_threshold': 0.01,
        'enable_triple_barrier': False,
        'enable_meta_label_quality_weighting': True,  # 启用质量权重
        'meta_label_quality_config': {
            'efficiency_weight_config': {
                'max_efficiency_multiplier': 1.5,
                'direction_penalty': 0.5,
            },
            'logging_config': {
                'enable_detailed_logging': True,
                'log_sample_statistics': True,
                'log_weight_distribution': True,
            }
        }
    }
    
    # 3. 计算质量权重
    print("\n📊 计算质量权重...")
    quality_weights = _calculate_meta_label_weights(
        df_with_target=df,
        target_col='target_2p_example',
        config=target_config,
        target_name_for_log='example_target'
    )
    
    # 4. 分析权重分布
    print(f"\n📈 质量权重分析:")
    print(f"权重范围: [{quality_weights.min():.3f}, {quality_weights.max():.3f}]")
    print(f"权重均值: {quality_weights.mean():.3f}")
    print(f"权重标准差: {quality_weights.std():.3f}")
    
    # 高质量样本分析
    high_quality_mask = quality_weights > 2.0
    print(f"高质量样本数: {high_quality_mask.sum()} / {len(quality_weights)} ({high_quality_mask.mean()*100:.1f}%)")
    
    return df, quality_weights, target_config

def example_template_configurations():
    """配置模板使用示例"""
    print("\n🎯 配置模板使用示例")
    print("=" * 50)
    
    # 测试不同的配置模板
    templates = ['conservative', 'balanced', 'aggressive']
    
    for template_name in templates:
        print(f"\n📋 {template_name.upper()} 模板:")
        
        # 获取模板配置
        template_config = get_quality_config_template(template_name)
        
        # 验证配置
        is_valid = validate_meta_label_quality_config(template_config)
        print(f"配置有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
        
        # 显示关键参数
        eff_config = template_config['efficiency_weight_config']
        smooth_config = template_config['smoothness_weight_config']
        final_config = template_config['final_weight_limits']
        
        print(f"  效率权重倍数: {eff_config['max_efficiency_multiplier']}")
        print(f"  方向错误惩罚: {eff_config['direction_penalty']}")
        print(f"  回撤阈值: {smooth_config['drawdown_threshold']}")
        print(f"  最大最终权重: {final_config['max_final_weight']}")

def example_integration_with_training():
    """与训练流程集成示例"""
    print("\n🔧 与训练流程集成示例")
    print("=" * 50)
    
    # 1. 创建数据
    df = create_sample_data()
    target_series = create_sample_target_variable(df)
    df['target_2p_example'] = target_series
    
    # 2. 创建特征数据（简化示例）
    X_df = pd.DataFrame({
        'feature_1': df['close'].pct_change(),
        'feature_2': df['volume'],
        'feature_3': df['high'] - df['low'],
    }).fillna(0)
    
    # 移除无效标签的样本
    valid_mask = target_series != -1
    X_df = X_df[valid_mask]
    y_array = target_series[valid_mask].values
    df_filtered = df[valid_mask].copy()
    
    print(f"有效样本数: {len(X_df)}")
    print(f"特征数: {X_df.shape[1]}")
    
    # 3. 配置目标（使用aggressive模板）
    target_config = apply_quality_config_to_target(
        target_config={
            'name': 'integration_example',
            'prediction_periods': [2],
            'target_threshold': 0.01,
            'enable_triple_barrier': False,
            'enable_dynamic_sample_weighting': False,  # 只使用质量权重
        },
        enable_quality_weighting=True,
        custom_config=get_quality_config_template('aggressive')
    )
    
    # 4. 准备训练数据（包含质量权重）
    print("\n🏋️ 准备训练数据...")
    X_resampled, y_resampled, sample_weights = prepare_training_data_with_weights(
        X_df=X_df,
        y_array=y_array,
        target_config=target_config,
        target_name='integration_example',
        apply_smote=False,  # 简化示例，不使用SMOTE
        df_with_target=df_filtered,
        target_col='target_2p_example'
    )
    
    # 5. 分析结果
    print(f"\n📊 训练数据分析:")
    print(f"特征形状: {X_resampled.shape}")
    print(f"目标形状: {y_resampled.shape}")
    
    if sample_weights is not None:
        print(f"样本权重统计:")
        print(f"  范围: [{sample_weights.min():.3f}, {sample_weights.max():.3f}]")
        print(f"  均值: {sample_weights.mean():.3f}")
        print(f"  标准差: {sample_weights.std():.3f}")
        
        # 分析不同类别的权重分布
        for label in np.unique(y_resampled):
            label_mask = y_resampled == label
            label_weights = sample_weights[label_mask]
            print(f"  类别 {label} 权重均值: {label_weights.mean():.3f}")
    else:
        print("未计算样本权重")
    
    return X_resampled, y_resampled, sample_weights

def main():
    """主函数"""
    print("🎯 元标签质量权重系统示例")
    print("=" * 60)
    
    try:
        # 基本使用示例
        df, quality_weights, target_config = example_basic_quality_weighting()
        
        # 配置模板示例
        example_template_configurations()
        
        # 训练集成示例
        X_train, y_train, weights = example_integration_with_training()
        
        print("\n✅ 所有示例运行完成！")
        print("\n💡 使用建议:")
        print("1. 根据您的具体需求选择合适的配置模板")
        print("2. 在实际训练中启用质量权重: enable_meta_label_quality_weighting=True")
        print("3. 监控高质量样本的比例，调整配置参数")
        print("4. 结合现有的动态样本权重系统使用")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
