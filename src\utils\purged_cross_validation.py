"""
Purged K-Fold Cross Validation for Financial Machine Learning

This module implements Purged Time Series Split, a more robust cross-validation
method for financial time series data that prevents data leakage by introducing
a "purge" period between training and validation sets.

Based on concepts from "Advances in Financial Machine Learning" by <PERSON>.
"""

import numpy as np
import pandas as pd
from typing import Generator, Tuple, Optional, Union
from sklearn.model_selection._split import _BaseKFold
import warnings


class PurgedTimeSeriesSplit:
    """
    Purged Time Series Cross Validation
    
    This cross-validator provides train/test indices to split time series data samples
    that are observed at fixed time intervals, in train/test sets. In each split, test
    indices must be higher than before, and a purge period is enforced between training
    and validation sets to prevent data leakage.
    
    Parameters
    ----------
    n_splits : int, default=5
        Number of splits. Must be at least 2.
        
    purge_length : int, default=10
        Number of samples to purge between training and validation sets.
        This should be at least equal to the forward-looking period used
        in target variable creation.
        
    embargo_length : int, default=0
        Additional embargo period after validation set to further prevent
        data leakage. This removes samples that might be affected by
        validation set information.
        
    min_train_size : int, default=None
        Minimum number of samples in training set. If None, will be
        automatically calculated to ensure reasonable training set sizes.
        
    Examples
    --------
    >>> import numpy as np
    >>> from purged_cross_validation import PurgedTimeSeriesSplit
    >>> X = np.array([[1, 2], [3, 4], [1, 2], [3, 4], [1, 2], [3, 4]])
    >>> y = np.array([1, 2, 3, 4, 5, 6])
    >>> ptscv = PurgedTimeSeriesSplit(n_splits=2, purge_length=1)
    >>> for train_index, test_index in ptscv.split(X):
    ...     print("TRAIN:", train_index, "TEST:", test_index)
    TRAIN: [0 1] TEST: [4 5]
    TRAIN: [0 1 2] TEST: [5]
    """
    
    def __init__(
        self, 
        n_splits: int = 5,
        purge_length: int = 10,
        embargo_length: int = 0,
        min_train_size: Optional[int] = None
    ):
        if n_splits < 2:
            raise ValueError("n_splits must be at least 2")
        if purge_length < 0:
            raise ValueError("purge_length must be non-negative")
        if embargo_length < 0:
            raise ValueError("embargo_length must be non-negative")
            
        self.n_splits = n_splits
        self.purge_length = purge_length
        self.embargo_length = embargo_length
        self.min_train_size = min_train_size
        
    def get_n_splits(self, X=None, y=None, groups=None) -> int:
        """Returns the number of splitting iterations in the cross-validator."""
        return self.n_splits
    
    def split(
        self, 
        X: Union[np.ndarray, pd.DataFrame], 
        y: Optional[Union[np.ndarray, pd.Series]] = None, 
        groups: Optional[np.ndarray] = None
    ) -> Generator[Tuple[np.ndarray, np.ndarray], None, None]:
        """
        Generate indices to split data into training and test set.
        
        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Training data, where n_samples is the number of samples
            and n_features is the number of features.
            
        y : array-like of shape (n_samples,), default=None
            The target variable for supervised learning problems.
            
        groups : array-like of shape (n_samples,), default=None
            Group labels for the samples used while splitting the dataset.
            
        Yields
        ------
        train : ndarray
            The training set indices for that split.
            
        test : ndarray
            The testing set indices for that split.
        """
        n_samples = len(X)
        
        if n_samples < self.n_splits + self.purge_length + self.embargo_length:
            raise ValueError(
                f"Cannot have n_splits={self.n_splits} with "
                f"purge_length={self.purge_length} and embargo_length={self.embargo_length} "
                f"for n_samples={n_samples}. Reduce n_splits or purge/embargo lengths."
            )
        
        # Calculate minimum training size if not provided
        min_train_size = self.min_train_size
        if min_train_size is None:
            # Ensure at least 20% of data for training in the smallest split
            min_train_size = max(50, n_samples // (self.n_splits + 2))
        
        # Calculate test set size for each split
        # We need to reserve space for purge and embargo
        available_for_test = n_samples - min_train_size - self.purge_length - self.embargo_length
        test_size = max(1, available_for_test // self.n_splits)
        
        indices = np.arange(n_samples)
        
        for i in range(self.n_splits):
            # Calculate test set boundaries
            test_start = min_train_size + self.purge_length + i * test_size
            test_end = min(test_start + test_size, n_samples - self.embargo_length)
            
            # Skip if test set would be empty or too small
            if test_start >= test_end:
                warnings.warn(f"Skipping split {i+1} due to insufficient data")
                continue
                
            # Training set: all data before (test_start - purge_length)
            train_end = test_start - self.purge_length
            if train_end <= 0:
                warnings.warn(f"Skipping split {i+1} due to insufficient training data")
                continue
                
            train_indices = indices[:train_end]
            test_indices = indices[test_start:test_end]
            
            # Ensure minimum training size
            if len(train_indices) < min_train_size:
                warnings.warn(f"Skipping split {i+1} due to insufficient training samples")
                continue
                
            yield train_indices, test_indices
    
    def __repr__(self):
        return (
            f"{self.__class__.__name__}(n_splits={self.n_splits}, "
            f"purge_length={self.purge_length}, embargo_length={self.embargo_length})"
        )


def calculate_optimal_purge_length(
    target_forward_period: int,
    max_feature_window: int = 0,
    safety_factor: float = 1.5
) -> int:
    """
    Calculate optimal purge length based on target variable and feature characteristics.
    
    Parameters
    ----------
    target_forward_period : int
        Number of periods into the future that the target variable looks.
        
    max_feature_window : int, default=0
        Maximum window size used in feature engineering.
        
    safety_factor : float, default=1.5
        Safety multiplier to add extra buffer.
        
    Returns
    -------
    int
        Recommended purge length.
    """
    base_purge = max(target_forward_period, max_feature_window)
    return int(base_purge * safety_factor)


def validate_purged_split(
    train_indices: np.ndarray,
    test_indices: np.ndarray,
    purge_length: int,
    data_timestamps: Optional[pd.DatetimeIndex] = None
) -> bool:
    """
    Validate that a purged split maintains proper temporal separation.
    
    Parameters
    ----------
    train_indices : np.ndarray
        Training set indices.
        
    test_indices : np.ndarray
        Test set indices.
        
    purge_length : int
        Expected purge length.
        
    data_timestamps : pd.DatetimeIndex, optional
        Timestamps for the data points.
        
    Returns
    -------
    bool
        True if the split is valid, False otherwise.
    """
    if len(train_indices) == 0 or len(test_indices) == 0:
        return False
        
    max_train_idx = np.max(train_indices)
    min_test_idx = np.min(test_indices)
    
    # Check if purge gap is maintained
    actual_gap = min_test_idx - max_train_idx - 1
    if actual_gap < purge_length:
        return False
        
    # If timestamps provided, check temporal order
    if data_timestamps is not None:
        max_train_time = data_timestamps[max_train_idx]
        min_test_time = data_timestamps[min_test_idx]
        if max_train_time >= min_test_time:
            return False
            
    return True


# Example usage and testing
if __name__ == "__main__":
    # Simple test
    print("Testing PurgedTimeSeriesSplit...")
    
    # Create sample data
    n_samples = 100
    X = np.random.randn(n_samples, 5)
    y = np.random.randn(n_samples)
    
    # Test with different configurations
    configs = [
        {"n_splits": 3, "purge_length": 5, "embargo_length": 0},
        {"n_splits": 5, "purge_length": 10, "embargo_length": 2},
    ]
    
    for config in configs:
        print(f"\nTesting with config: {config}")
        ptscv = PurgedTimeSeriesSplit(**config)
        
        splits = list(ptscv.split(X, y))
        print(f"Generated {len(splits)} splits")
        
        for i, (train_idx, test_idx) in enumerate(splits):
            print(f"Split {i+1}: Train size={len(train_idx)}, Test size={len(test_idx)}")
            print(f"  Train range: [{train_idx[0]}, {train_idx[-1]}]")
            print(f"  Test range: [{test_idx[0]}, {test_idx[-1]}]")
            print(f"  Gap: {test_idx[0] - train_idx[-1] - 1}")
            
            # Validate split
            is_valid = validate_purged_split(train_idx, test_idx, config["purge_length"])
            print(f"  Valid: {is_valid}")
    
    print("\nPurgedTimeSeriesSplit testing completed!")
