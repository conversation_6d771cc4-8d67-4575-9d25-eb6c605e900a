# 一票否决权日志记录实现指南

## 概述

本文档介绍了如何在交易日志中优雅地记录一票否决权的触发情况，确保每次否决决策都能被完整追踪和分析。

## 功能特性

### ✅ 核心功能
- **完整记录**: 记录否决触发的完整上下文信息
- **特殊标识**: 使用`BLOCKED`类型标识被否决的交易
- **零成本记录**: 被否决的交易金额为0，不影响盈亏统计
- **异步写入**: 不阻塞主要交易流程
- **统一格式**: 与现有日志系统完全兼容

### ✅ 日志字段
- `target_name`: `MetaModel_BTC_15m_VETO` (特殊标识)
- `direction`: `BLOCKED` (表示被阻止的交易)
- `result`: `BLOCKED` (特殊结果类型)
- `amount`: `0.0` (零金额)
- `profit_loss`: `0.0` (无盈亏)
- `exit_reason`: `up_model_veto` (否决原因)

## 实现细节

### 1. 日志系统扩展

#### 支持BLOCKED类型
修改了以下文件以支持新的交易类型：

**src/core/unified_trade_logger.py**:
```python
# 支持BLOCKED作为交易方向
valid_directions = ['LONG', 'SHORT', 'UP', 'DOWN', 'BUY', 'SELL', 'BLOCKED']

# 支持BLOCKED作为交易结果
if result not in ['WIN', 'LOSS', 'BLOCKED']:
    raise ValueError(f"无效的交易结果: {result}")

# BLOCKED类型的盈亏计算
elif result == 'BLOCKED':
    profit_loss = 0.0  # 被阻止的交易没有盈亏
```

**src/core/unified_comprehensive_logger.py**:
```python
# 同样的修改以保持一致性
```

### 2. 否决记录逻辑

#### 触发条件检查
```python
if initial_signal == "UP_Meta" and up_model_down_prob > up_model_veto_threshold:
    # 触发一票否决权
    print(f"🚨 [一票否决权] UP模型强烈看跌(P_down={up_model_down_prob:.1%})，否决做多信号！")
    
    # 记录否决到日志
    # ... 日志记录逻辑
```

#### 上下文数据构建
```python
veto_context_data = {
    'signal_filtered_reason': f"UP模型一票否决权触发",
    'meta_decision_reason': f"UP模型看跌概率({up_model_down_prob:.1%})超过否决阈值({veto_threshold:.1%})",
    'threshold_check_result': f"否决阈值检查失败: {up_model_down_prob:.3f} > {veto_threshold:.3f}",
    'individual_model_probabilities': json.dumps({
        'UP_model_up_prob': float(up_model_up_prob),
        'UP_model_down_prob': float(up_model_down_prob),
        'Meta_model_up_prob': float(meta_model_probas[1]),
        'Meta_model_down_prob': float(meta_model_probas[0])
    }),
    'decision_context_summary': json.dumps({
        'veto_trigger': 'UP_model_veto',
        'original_signal': 'UP_Meta',
        'final_signal': 'Neutral_UP_Veto',
        'veto_threshold': veto_threshold,
        'up_model_down_prob': up_model_down_prob,
        'protection_level': 'high_risk_prevention'
    })
}
```

### 3. 日志记录流程

#### 开仓记录
```python
veto_trade_id = trade_logger.record_trade_entry(
    target_name="MetaModel_BTC_15m_VETO",  # 特殊标识
    symbol="BTCUSDT", 
    direction="BLOCKED",                   # 被阻止的方向
    entry_price=current_price,
    amount=0.0,                           # 零金额
    payout_ratio=0.0,
    context_data=veto_context_data
)
```

#### 平仓记录
```python
trade_logger.record_trade_exit(
    trade_id=veto_trade_id,
    exit_price=current_price,
    result="BLOCKED",                     # 特殊结果
    exit_reason="up_model_veto"          # 否决原因
)
```

## 日志分析

### CSV文件示例
```csv
trade_id,target_name,direction,result,amount,profit_loss,exit_reason,signal_filtered_reason,meta_decision_reason,threshold_check_result
MetaModel_BTC_15m_VETO_xxx,MetaModel_BTC_15m_VETO,BLOCKED,BLOCKED,0.0,0.0,up_model_veto,UP模型一票否决权触发,UP模型看跌概率(82.0%)超过否决阈值(75.0%),否决阈值检查失败: 0.820 > 0.750
```

### 查询方法

#### 查找所有否决记录
```sql
SELECT * FROM trades 
WHERE target_name LIKE '%VETO%' 
AND result = 'BLOCKED'
```

#### 统计否决频率
```sql
SELECT 
    DATE(entry_timestamp) as date,
    COUNT(*) as veto_count
FROM trades 
WHERE result = 'BLOCKED'
GROUP BY DATE(entry_timestamp)
```

#### 分析否决效果
```sql
-- 对比否决前后的市场表现
SELECT 
    entry_timestamp,
    entry_price,
    meta_decision_reason,
    threshold_check_result
FROM trades 
WHERE result = 'BLOCKED'
ORDER BY entry_timestamp DESC
```

## 监控和分析

### 关键指标
1. **否决频率**: 每日/每周否决次数
2. **阈值分布**: UP模型看跌概率分布
3. **保护效果**: 否决后市场实际走势
4. **误否决率**: 否决后价格上涨的情况

### 告警设置
- 否决频率异常高（可能阈值过低）
- 否决频率异常低（可能阈值过高）
- 连续多次否决（市场趋势变化）

## 配置参数

### 阈值设置
```python
# config.py
UP_MODEL_VETO_THRESHOLD = 0.75  # 一票否决阈值
```

### 日志配置
```python
# 日志目录
base_log_dir = "trading_logs_unified"

# 文件格式
# trading_logs_unified/YYYY/MM/trades_YYYY-MM-DD.csv
```

## 最佳实践

### 1. 阈值调优
- 监控否决频率和效果
- 根据历史数据调整阈值
- 考虑市场环境变化

### 2. 日志维护
- 定期清理过期日志
- 备份重要的否决记录
- 监控日志文件大小

### 3. 性能优化
- 异步写入避免阻塞
- 批量处理提高效率
- 内存使用监控

## 总结

一票否决权的日志记录功能提供了：
- **完整的决策追踪**: 每次否决都有详细记录
- **优雅的集成**: 与现有系统无缝结合
- **强大的分析能力**: 支持多维度数据分析
- **可靠的性能**: 异步处理不影响主流程

这个实现确保了风险控制决策的完全透明和可追溯性，为系统优化和风险管理提供了强有力的数据支持。
