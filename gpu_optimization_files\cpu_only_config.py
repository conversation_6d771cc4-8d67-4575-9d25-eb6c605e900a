"""
纯CPU训练配置
确保项目完全不使用GPU
"""

import os
import multiprocessing as mp

# 强制禁用GPU
def force_cpu_only():
    """强制使用CPU，禁用所有GPU"""
    print("🚫 强制CPU模式已启用")
    
    # 禁用CUDA
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'
    
    # TensorFlow CPU优化
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
    os.environ['TF_NUM_INTEROP_THREADS'] = '4'
    os.environ['TF_NUM_INTRAOP_THREADS'] = '16'  # AMD 16核
    
    # CPU线程优化
    cpu_count = mp.cpu_count()
    os.environ['OMP_NUM_THREADS'] = str(cpu_count)
    os.environ['MKL_NUM_THREADS'] = str(cpu_count)
    os.environ['NUMBA_NUM_THREADS'] = str(cpu_count)
    os.environ['OPENBLAS_NUM_THREADS'] = str(cpu_count)
    
    print(f"✅ CPU优化配置完成 - 使用 {cpu_count} 核心")

# LightGBM纯CPU参数
LIGHTGBM_CPU_ONLY_PARAMS = {
    'device_type': 'cpu',                    # 强制CPU
    'num_threads': mp.cpu_count(),           # 使用所有CPU核心
    'force_row_wise': True,                  # AMD处理器优化
    'histogram_pool_size': -1,               # 自动内存池
    'max_bin': 255,                          # 适合AMD缓存
    'boost_from_average': True,              # AMD友好初始化
    'tree_learner': 'serial',                # 串行学习器
    'verbose': -1,
    'objective': 'binary',
    'metric': 'binary_logloss',
    'boosting_type': 'gbdt',
    'random_state': 42,
    'learning_rate': 0.1,                    # CPU可以用更高学习率
    'feature_fraction': 0.9,                 # CPU模式可以用更高比例
    'bagging_fraction': 0.8,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'min_data_in_leaf': 20,
    'min_sum_hessian_in_leaf': 1e-3
}

def setup_tensorflow_cpu_only():
    """设置TensorFlow纯CPU模式"""
    try:
        import tensorflow as tf

        # 强制禁用GPU
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'

        # 禁用所有GPU设备
        try:
            tf.config.set_visible_devices([], 'GPU')
            print("✅ TensorFlow GPU设备已完全禁用")
        except Exception as e:
            print(f"⚠️  GPU设备禁用警告: {e}")

        # CPU线程配置
        tf.config.threading.set_inter_op_parallelism_threads(4)
        tf.config.threading.set_intra_op_parallelism_threads(16)  # AMD 16核

        print("✅ TensorFlow CPU模式配置完成")
        return True

    except ImportError:
        print("⚠️  TensorFlow未安装")
        return False
    except Exception as e:
        print(f"❌ TensorFlow配置失败: {e}")
        return False

def create_cpu_only_lightgbm_model(**kwargs):
    """创建纯CPU LightGBM模型"""
    try:
        import lightgbm as lgb
        
        # 合并参数，确保device_type为cpu
        params = LIGHTGBM_CPU_ONLY_PARAMS.copy()
        params.update(kwargs)
        params['device_type'] = 'cpu'  # 强制CPU
        
        # 移除任何GPU相关参数
        gpu_params = ['gpu_platform_id', 'gpu_device_id', 'gpu_use_dp']
        for param in gpu_params:
            params.pop(param, None)
        
        model = lgb.LGBMClassifier(**params)
        print("✅ 纯CPU LightGBM模型创建成功")
        return model
        
    except ImportError:
        print("❌ LightGBM未安装")
        return None
    except Exception as e:
        print(f"❌ LightGBM模型创建失败: {e}")
        return None

# 自动初始化
force_cpu_only()
