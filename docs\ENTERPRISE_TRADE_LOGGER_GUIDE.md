# 企业级 TradeLogger 使用指南

## 概述

企业级 TradeLogger 是一个高性能、线程安全的交易日志记录系统，专为生产环境设计。它提供了异步写入、智能日志轮转、分层目录结构和高效数据分析等企业级功能。

## 主要特性

### 🚀 异步高性能
- **异步队列写入**: 使用 `queue.Queue` 和后台线程，确保记录操作不阻塞主程序
- **批量处理**: 支持批量写入，提高I/O效率
- **高并发支持**: 完全线程安全，支持多线程并发记录

### 📁 智能日志管理
- **自动日志轮转**: 支持按日期（每日）和文件大小（默认10MB）轮转
- **分层目录结构**: `trading_logs/YYYY/MM/` 格式，便于管理和查找
- **文件命名规范**: `trades_2025-07-05.csv`, `trades_2025-07-05_part_2.csv`

### 📊 强大数据分析
- **智能数据加载**: 支持日期范围查询，自动合并多个CSV文件
- **数据类型处理**: 自动解析日期时间和JSON字段
- **完整上下文记录**: 21个字段记录完整的交易决策上下文

### 🛡️ 企业级可靠性
- **优雅关闭**: 确保所有队列数据在程序退出前完成写入
- **错误处理**: 完善的异常处理和日志记录
- **数据完整性**: 防止数据丢失和重复

## 快速开始

### 基础使用

```python
from src.core.enterprise_trade_logger import get_enterprise_trade_logger

# 获取企业级日志记录器（单例模式）
logger = get_enterprise_trade_logger(
    base_log_dir="trading_logs",
    max_file_size=10 * 1024 * 1024,  # 10MB
    batch_size=10,
    flush_interval=1.0
)

# 记录交易开仓
trade_id = logger.record_trade_entry(
    target_name="MyStrategy",
    symbol="BTCUSDT", 
    direction="LONG",
    entry_price=50000.0,
    amount=10.0,
    context_data={
        "signal_probability": 0.85,
        "market_regime": "uptrend",
        "atr_percent": 2.5,
        "meta_model_inputs": {"model_1": 0.8, "model_2": 0.9},
        "top_features": {"rsi": 0.3, "macd": 0.7}
    }
)

# 记录交易平仓
logger.record_trade_exit(trade_id, 51000.0, "WIN")

# 强制刷新（可选）
logger.flush()
```

### 数据分析

```python
# 加载所有交易数据
all_trades = logger.load_trade_logs()

# 加载指定日期范围的数据
recent_trades = logger.load_trade_logs(
    start_date="2025-07-01",
    end_date="2025-07-05"
)

# 数据分析示例
win_rate = (recent_trades['result'] == 'WIN').mean()
total_profit = recent_trades['profit_loss'].sum()

# 按策略分析
strategy_stats = recent_trades.groupby('target_name').agg({
    'trade_id': 'count',
    'result': lambda x: (x == 'WIN').mean(),
    'profit_loss': 'sum'
})
```

## 配置参数

### 初始化参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `base_log_dir` | str | "trading_logs" | 日志根目录 |
| `max_file_size` | int | 10MB | 单文件最大大小（字节） |
| `queue_maxsize` | int | 1000 | 队列最大容量 |
| `batch_size` | int | 10 | 批量写入大小 |
| `flush_interval` | float | 1.0 | 自动刷新间隔（秒） |

### 上下文数据字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `signal_probability` | float | 信号概率 |
| `neutral_probability` | float | 中性概率 |
| `opposite_probability` | float | 反向概率 |
| `meta_model_inputs` | dict | 元模型输入数据 |
| `market_regime` | str | 市场状态 |
| `atr_percent` | float | ATR百分比 |
| `adx_value` | float | ADX值 |
| `top_features` | dict | 核心特征快照 |

## 目录结构

```
trading_logs/
├── 2025/
│   ├── 07/
│   │   ├── trades_2025-07-05.csv
│   │   ├── trades_2025-07-05_part_2.csv
│   │   └── trades_2025-07-06.csv
│   └── 08/
│       └── trades_2025-08-01.csv
└── 2024/
    └── 12/
        └── trades_2024-12-31.csv
```

## CSV字段说明

### 基础交易信息
- `trade_id`: 唯一交易标识
- `entry_timestamp`: 开仓时间
- `exit_timestamp`: 平仓时间
- `target_name`: 策略名称
- `symbol`: 交易对
- `direction`: 交易方向 (LONG/SHORT)
- `entry_price`: 开仓价格
- `exit_price`: 平仓价格
- `amount`: 交易金额
- `payout_ratio`: 盈利比例
- `result`: 交易结果 (WIN/LOSS)
- `profit_loss`: 盈亏金额
- `exit_reason`: 平仓原因

### 模型预测信息
- `entry_signal_probability`: 信号概率
- `entry_neutral_probability`: 中性概率
- `entry_opposite_probability`: 反向概率
- `meta_model_inputs`: 元模型输入（JSON格式）

### 市场状态信息
- `entry_market_regime`: 市场状态
- `entry_atr_percent`: ATR百分比
- `entry_adx_value`: ADX值

### 特征信息
- `entry_top_features`: 核心特征快照（JSON格式）

## 性能特性

### 异步写入性能
- **记录速度**: >1000 TPS（交易/秒）
- **内存占用**: 低内存占用，队列大小可控
- **延迟**: 记录操作几乎无延迟（<1ms）

### 日志轮转
- **文件大小控制**: 防止单文件过大
- **自动分片**: 超过大小限制自动创建新文件
- **日期分组**: 按年/月自动组织目录结构

### 数据查询
- **智能文件发现**: 根据日期范围自动查找相关文件
- **高效合并**: 优化的多文件合并算法
- **类型转换**: 自动处理日期时间和JSON数据

## 最佳实践

### 1. 生产环境配置
```python
logger = get_enterprise_trade_logger(
    base_log_dir="/var/log/trading",
    max_file_size=50 * 1024 * 1024,  # 50MB
    batch_size=50,
    flush_interval=2.0
)
```

### 2. 高频交易配置
```python
logger = get_enterprise_trade_logger(
    base_log_dir="trading_logs",
    max_file_size=100 * 1024 * 1024,  # 100MB
    batch_size=100,
    flush_interval=5.0
)
```

### 3. 开发测试配置
```python
logger = get_enterprise_trade_logger(
    base_log_dir="test_logs",
    max_file_size=1 * 1024 * 1024,  # 1MB
    batch_size=5,
    flush_interval=0.5
)
```

### 4. 优雅关闭
```python
import atexit

def cleanup():
    logger.stop(timeout=10.0)

atexit.register(cleanup)
```

## 监控和维护

### 获取统计信息
```python
stats = logger.get_stats()
print(f"总开仓数: {stats['total_entries']}")
print(f"总平仓数: {stats['total_exits']}")
print(f"待平仓数: {stats['pending_trades']}")
print(f"写入统计: {stats['writer_stats']}")
```

### 检查待平仓交易
```python
pending_count = logger.get_pending_trades_count()
pending_ids = logger.get_pending_trade_ids()
```

### 当前日志文件
```python
current_file = logger.get_current_log_file()
print(f"当前日志文件: {current_file}")
```

## 故障排除

### 常见问题

1. **队列满错误**
   - 增加 `queue_maxsize` 参数
   - 减少 `flush_interval` 加快写入

2. **文件权限错误**
   - 确保日志目录有写入权限
   - 检查磁盘空间

3. **数据丢失**
   - 确保调用 `logger.stop()` 优雅关闭
   - 检查异常日志

4. **性能问题**
   - 增加 `batch_size` 提高写入效率
   - 调整 `flush_interval` 平衡实时性和性能

### 日志级别
```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 向后兼容性

企业级 TradeLogger 完全兼容现有的 TradeLogger API：

```python
# 旧版本代码无需修改
from src.core.enterprise_trade_logger import get_trade_logger

logger = get_trade_logger("trading_logs/trades.csv")
# 自动转换为企业级实现
```

## 总结

企业级 TradeLogger 提供了生产级别的交易日志记录能力，具有高性能、高可靠性和丰富的分析功能。通过异步处理、智能轮转和完整的上下文记录，它能够满足各种规模的交易系统需求。
