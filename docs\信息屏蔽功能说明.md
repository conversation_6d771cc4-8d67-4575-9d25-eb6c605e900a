# 🛡️ 信息屏蔽功能说明

## 概述

信息屏蔽功能是一个治本的解决方案，用于移除基础模型的反向信号，确保元模型接收到纯净的方向性信号。

## 核心原理

### 问题背景
在原有系统中，基础模型可能会产生反向信号：
- **UP模型**：专门训练用于识别上涨信号，但可能同时给出强烈的下跌概率
- **DOWN模型**：专门训练用于识别下跌信号，但可能同时给出强烈的上涨概率

这些反向信号会干扰元模型的决策，导致信号质量下降。

### 解决方案
信息屏蔽功能直接在元模型输入特征构建之前，修改基础模型的概率输出：

1. **DOWN模型屏蔽**：只保留下跌信号，将上涨概率设为下跌概率的补集
2. **UP模型屏蔽**：只保留上涨信号，将下跌概率设为上涨概率的补集
3. **LSTM模型**：保持原始概率不变（因为LSTM是综合性模型）

## 配置参数

在 `config.py` 的 `META_MODEL_FEATURE_ENGINEERING_CONFIG` 中添加了以下配置：

```python
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    # ... 其他配置 ...
    
    # 🛡️ 信息屏蔽配置
    "enable_signal_shielding": True,                     # [布尔值] 是否启用反向信号屏蔽功能
    "shield_up_model_down_signal": True,                 # [布尔值] 是否屏蔽UP模型的下跌信号
    "shield_down_model_up_signal": True,                 # [布尔值] 是否屏蔽DOWN模型的上涨信号
    "preserve_lstm_signals": True,                       # [布尔值] 是否保持LSTM模型的原始信号不变
}
```

### 配置说明

- **enable_signal_shielding**: 主开关，控制整个信息屏蔽功能的启用/禁用
- **shield_up_model_down_signal**: 控制是否屏蔽UP模型的下跌信号
- **shield_down_model_up_signal**: 控制是否屏蔽DOWN模型的上涨信号
- **preserve_lstm_signals**: 控制是否保持LSTM模型信号不变（当前版本中此参数暂未使用）

## 实现细节

### 屏蔽逻辑

#### DOWN模型屏蔽
```python
# 原始概率：p_up=0.55, p_down=0.85 (反向信号：中等看涨)
# 屏蔽后：p_up=0.15, p_down=0.85 (上涨概率被屏蔽为下跌概率的补集)
shielded_p_up = 1.0 - original_p_down
shielded_p_down = original_p_down
```

#### UP模型屏蔽
```python
# 原始概率：p_up=0.80, p_down=0.65 (反向信号：强烈看跌)
# 屏蔽后：p_up=0.80, p_down=0.20 (下跌概率被屏蔽为上涨概率的补集)
shielded_p_up = original_p_up
shielded_p_down = 1.0 - original_p_up
```

### 影响范围

信息屏蔽会影响以下组件：
1. **元模型特征工程**：使用屏蔽后的概率构建特征
2. **元模型预测**：基于纯净的方向性信号进行决策
3. **GUI显示**：显示屏蔽后的概率信息
4. **日志记录**：记录屏蔽后的概率数据

## 使用示例

### 完全启用（推荐）
```python
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    "enable_signal_shielding": True,
    "shield_up_model_down_signal": True,
    "shield_down_model_up_signal": True,
}
```

### 只屏蔽UP模型
```python
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    "enable_signal_shielding": True,
    "shield_up_model_down_signal": True,
    "shield_down_model_up_signal": False,
}
```

### 完全禁用
```python
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    "enable_signal_shielding": False,
    "shield_up_model_down_signal": True,  # 被忽略
    "shield_down_model_up_signal": True,  # 被忽略
}
```

## 预期效果

1. **信号纯度提升**：元模型接收到的信号更加纯净，减少噪音干扰
2. **决策一致性**：UP模型专注于上涨信号，DOWN模型专注于下跌信号
3. **性能改善**：减少反向信号的干扰，提高元模型的预测准确性

## 日志输出示例

启用信息屏蔽时的日志输出：
```
[🛡️ 信息屏蔽] 移除基础模型的反向信号...
  配置: UP模型下跌信号屏蔽=True, DOWN模型上涨信号屏蔽=True
  🛡️ UP模型 BTC_15m_UP: 屏蔽下跌信号
    原始概率: ↑0.800 ↓0.650
    屏蔽后概率: ↑0.800 ↓0.200
  🛡️ DOWN模型 BTC_15m_DOWN: 屏蔽上涨信号
    原始概率: ↑0.550 ↓0.850
    屏蔽后概率: ↑0.150 ↓0.850
  ✓ LSTM模型 BTC_15m_LSTM: 保持原始概率 ↑0.600 ↓0.400
[🛡️ 信息屏蔽完成] 反向信号已被屏蔽，元模型将使用纯净的方向性信号
```

## 测试验证

可以运行 `test_signal_shielding.py` 来验证功能的正确性：

```bash
python test_signal_shielding.py
```

测试会验证不同配置组合下的屏蔽效果，确保：
- 屏蔽后的概率和为1
- 专业信号得到保留
- 反向信号被正确屏蔽
- 配置参数正确生效

## 注意事项

1. **概率一致性**：屏蔽后的概率和始终为1，保持数学一致性
2. **配置灵活性**：可以根据实际需要选择性启用屏蔽功能
3. **向后兼容**：禁用功能时完全保持原有行为
4. **性能影响**：屏蔽操作在概率提取之前进行，对性能影响极小

## 技术实现位置

- **主要实现**：`src/core/prediction.py` 的 `run_meta_prediction_for_current_trigger` 函数
- **配置定义**：`config.py` 的 `META_MODEL_FEATURE_ENGINEERING_CONFIG`
- **测试文件**：`test_signal_shielding.py`
- **文档说明**：`docs/信息屏蔽功能说明.md`
