#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone越狱交易执行器兼容性模块
为了解决Pylance导入问题而创建的兼容性包装器
"""

import os
import sys

def execute_trade(signal_type, amount):
    """
    执行iPhone越狱自动化交易的兼容性函数
    
    Args:
        signal_type (str): 交易信号类型 ("UP" 或 "DOWN")
        amount (float): 交易金额
        
    Returns:
        bool: 交易是否成功执行
    """
    try:
        # 添加iphone_automation目录到Python路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        iphone_automation_path = os.path.join(project_root, 'iphone_automation')

        if iphone_automation_path not in sys.path:
            sys.path.append(iphone_automation_path)

        # 检查文件是否存在
        ssh_trader_file = os.path.join(iphone_automation_path, 'ssh_zxtouch_trader.py')
        if not os.path.exists(ssh_trader_file):
            print(f"❌ 文件不存在: {ssh_trader_file}")
            return False

        # 使用动态导入避免Pylance警告
        import importlib.util
        spec = importlib.util.spec_from_file_location("ssh_zxtouch_trader", ssh_trader_file)
        ssh_zxtouch_trader = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(ssh_zxtouch_trader)
        
        # 调用实际的交易执行函数
        return ssh_zxtouch_trader.execute_binance_trade(signal_type, amount)
        
    except ImportError as e:
        print(f"❌ 无法导入iPhone自动化交易模块: {e}")
        print("请确保iphone_automation/ssh_zxtouch_trader.py文件存在")
        return False
    except Exception as e:
        print(f"❌ iPhone自动化交易执行异常: {e}")
        return False

# 为了向后兼容，提供额外的函数别名
def execute_binance_trade(signal_type, amount):
    """向后兼容的函数别名"""
    return execute_trade(signal_type, amount)

if __name__ == "__main__":
    # 测试模块
    print("🎯 测试iPhone越狱交易执行器兼容性模块")
    print("="*50)
    
    # 测试导入
    try:
        # 测试兼容性函数
        print("测试execute_trade函数...")
        # 注意：这里不会实际执行交易，只是测试导入
        print("✅ 兼容性模块工作正常")

    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        print("请检查iphone_automation目录和ssh_zxtouch_trader.py文件")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
