# 一票否决权和加权投票制功能指南

## 概述

本文档介绍了为元模型决策系统新增的两个重要功能：
1. **UP模型一票否决权** - 保守风险控制机制
2. **加权投票制** - 智能概率加权系统

这两个功能旨在解决元模型对"DOWN模型反向信号"过度信任的问题，提高交易决策的质量和安全性。

## 功能详解

### 1. UP模型一票否决权

#### 设计理念
UP模型作为最保守、最追求精确率的"狙击手"，当它以极高的置信度认为市场即将下跌时，应该无条件地相信它，否决掉任何做多信号。

#### 工作机制
- **触发条件**: UP模型看跌概率 > 否决阈值 且 元模型信号为做多
- **默认阈值**: 85% (可通过配置调整)
- **执行位置**: 在元模型初步决策之后，统一过滤器之前
- **保护效果**: 避免在UP模型强烈看跌时的错误做多

#### 配置参数
```python
# config.py
UP_MODEL_VETO_THRESHOLD = 0.85  # 一票否决阈值
```

#### 代码实现位置
- 文件: `src/core/prediction.py`
- 函数: `run_meta_prediction_for_current_trigger`
- 位置: 步骤 4.5

### 2. 加权投票制

#### 设计理念
不同基础模型有各自的专长和可靠性，应该根据它们的特点给予不同的权重，而不是平等对待。

#### 权重策略
- **UP模型**: 看涨信号权重更高(1.2)，看跌信号权重降低(0.7)
- **DOWN模型**: 看跌信号权重更高(1.2)，看涨信号权重降低(0.7)
- **LSTM模型**: 保持中性权重(1.0)

#### 工作机制
1. 从基础模型中提取概率信息
2. 根据配置的权重对概率进行加权计算
3. 生成加权后的概率特征供元模型使用
4. 提供更准确的概率输入

#### 配置参数
```python
# config.py
# UP模型权重
UP_MODEL_UP_WEIGHT = 1.2      # 看涨信号权重
UP_MODEL_DOWN_WEIGHT = 0.7    # 看跌信号权重

# DOWN模型权重
DOWN_MODEL_UP_WEIGHT = 0.7    # 看涨信号权重
DOWN_MODEL_DOWN_WEIGHT = 1.2  # 看跌信号权重

# LSTM模型权重
LSTM_MODEL_UP_WEIGHT = 1.0    # 看涨信号权重
LSTM_MODEL_DOWN_WEIGHT = 1.0  # 看跌信号权重
```

#### 代码实现位置
- 文件: `src/core/prediction.py`
- 函数: `run_meta_prediction_for_current_trigger`
- 位置: 步骤 2a.5

## 使用示例

### 场景1：一票否决权生效
```
元模型决策: UP_Meta (概率: 75%)
UP模型概率: ↑20% ↓90%
DOWN模型概率: ↑80% ↓20%
LSTM模型概率: ↑70% ↓30%

结果: UP模型看跌概率(90%) > 否决阈值(85%)
      → 信号被否决，避免错误做多
```

### 场景2：加权投票制优化
```
原始平均概率: ↑63.3% ↓36.7%
加权后概率: ↑67.2% ↓40.5%

优化效果: UP模型强烈看涨信号得到更高权重
         DOWN模型反向看涨信号权重被降低
```

## 技术实现

### 概率提取函数
使用 `_extract_probabilities_from_core_info` 函数智能提取不同格式的概率数据：
- 传统格式: `p_up`, `p_down`
- 精英格式: `oof_{model}_elite_fold{n}`
- LSTM格式: `oof_{model}_lstm_prediction`

### 加权计算公式
```python
# 加权上涨概率
weighted_up_prob = (
    up_model_up_prob * UP_MODEL_UP_WEIGHT +
    down_model_up_prob * DOWN_MODEL_UP_WEIGHT +
    lstm_up_prob * LSTM_MODEL_UP_WEIGHT
) / (UP_MODEL_UP_WEIGHT + DOWN_MODEL_UP_WEIGHT + LSTM_MODEL_UP_WEIGHT)

# 加权下跌概率
weighted_down_prob = (
    up_model_down_prob * UP_MODEL_DOWN_WEIGHT +
    down_model_down_prob * DOWN_MODEL_DOWN_WEIGHT +
    lstm_down_prob * LSTM_MODEL_DOWN_WEIGHT
) / (UP_MODEL_DOWN_WEIGHT + DOWN_MODEL_DOWN_WEIGHT + LSTM_MODEL_DOWN_WEIGHT)
```

### 新增特征
加权投票制为元模型添加了以下新特征：
- `weighted_up_probability`: 加权上涨概率
- `weighted_down_probability`: 加权下跌概率
- `weighted_prob_difference`: 加权概率差异
- `weighted_prob_confidence`: 加权置信度

## 测试和验证

### 运行测试
```bash
python test_veto_and_weighted_voting.py
```

### 运行演示
```bash
python demo_veto_and_weighted_voting.py
```

## 配置建议

### 保守配置
```python
UP_MODEL_VETO_THRESHOLD = 0.80  # 更低的否决阈值，更严格的保护
UP_MODEL_UP_WEIGHT = 1.5        # 更高的UP模型看涨权重
DOWN_MODEL_UP_WEIGHT = 0.5      # 更低的DOWN模型看涨权重
```

### 激进配置
```python
UP_MODEL_VETO_THRESHOLD = 0.90  # 更高的否决阈值，较少干预
UP_MODEL_UP_WEIGHT = 1.1        # 适中的权重调整
DOWN_MODEL_UP_WEIGHT = 0.8      # 适中的权重调整
```

## 监控和日志

系统会在控制台输出详细的执行信息：
- 一票否决权检查结果
- 加权投票计算过程
- 概率变化对比
- 最终决策原因

## 注意事项

1. **元模型重训练**: 加权投票制引入了新特征，可能需要重新训练元模型
2. **阈值调优**: 根据实际交易表现调整否决阈值和权重参数
3. **监控效果**: 定期评估两个机制的保护效果和优化效果
4. **协同工作**: 两个机制会协同工作，共同提升决策质量

## 总结

一票否决权和加权投票制是对元模型决策系统的重要增强：
- **风险控制**: 一票否决权提供了强有力的风险保护
- **质量优化**: 加权投票制提高了概率输入的准确性
- **协同效应**: 两个机制互补，既保护风险又优化信号质量

这些改进直接针对"元模型对DOWN模型反向信号过度信任"的问题，提供了外科手术式的精准解决方案。
