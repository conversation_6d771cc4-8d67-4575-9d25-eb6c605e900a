# SignalReceiver.py
from flask import Flask, request, jsonify
import threading
import time
import requests # <--- 添加这一行


class SignalReceiver:
    def __init__(self, host='0.0.0.0', port=5005):
        self.app = Flask(__name__)
        self.host = host
        self.port = port
        self.latest_signal = None
        self.signal_received_time = None
        self.signal_lock = threading.Lock()

        @self.app.route('/signal', methods=['POST'])
        def handle_signal_route():
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"status": "error", "message": "无效的JSON数据"}), 400

                signal_type = data.get('signal_type')
                suggested_amount = data.get('amount')
                target_name_signal = data.get('target_name', 'UNKNOWN_TARGET')
                signal_symbol_received = data.get('symbol') # <--- 1. 获取 symbol 字段

                # 🚀 V12.0: 接收完整的上下文数据（信号快照）
                context_data = data.get('context_data', {})

                if signal_type not in ["UP", "DOWN"]:
                    return jsonify({"status": "error", "message": "无效的 signal_type"}), 400

                with self.signal_lock:
                    self.latest_signal = {
                        "type": signal_type,
                        "amount": float(suggested_amount) if suggested_amount is not None else None,
                        "target_name": target_name_signal,
                        "symbol": signal_symbol_received, # <--- 2. 将 symbol 存入 latest_signal
                        "context_data": context_data,  # 🚀 V12.0: 保存上下文数据
                        "timestamp": time.time()
                    }
                    self.signal_received_time = self.latest_signal["timestamp"]

                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.signal_received_time))}] "
                      f"SignalReceiver: 收到信号 - 类型: {signal_type}, "
                      f"币种: {signal_symbol_received if signal_symbol_received else '未提供'}, " # <--- 3. (可选) 打印 symbol
                      f"金额: {suggested_amount if suggested_amount is not None else '未指定'}, "
                      f"来源目标: {target_name_signal}")

                return jsonify({"status": "success", "message": "信号已接收"}), 200

            except Exception as e:
                print(f"SignalReceiver: 处理信号时出错: {e}")
                return jsonify({"status": "error", "message": str(e)}), 500



    def run_server(self, daemon=True):
        """在一个单独的线程中运行Flask服务器。"""
        if hasattr(self, 'server_thread') and self.server_thread.is_alive():
            print("SignalReceiver: 服务器已在运行。")
            return

        self.server_thread = threading.Thread(
            target=lambda: self.app.run(host=self.host, port=self.port, debug=False, use_reloader=False),
            daemon=daemon
        )
        self.server_thread.start()
        print(f"SignalReceiver: HTTP服务器已在 http://{self.host}:{self.port} 启动 (线程: {self.server_thread.name})")
        print(f"  - POST /signal (接收交易信号)")

    def get_latest_signal(self, clear_after_get=True):
        """
        获取最近一次收到的信号。

        Args:
            clear_after_get (bool): 获取后是否清除信号，以避免重复处理。

        Returns:
            dict or None: 如果有新信号则返回信号字典，否则返回None。
        """
        with self.signal_lock:
            signal_to_return = self.latest_signal
            if clear_after_get and signal_to_return is not None:
                self.latest_signal = None # 清除，表示已处理
        return signal_to_return

    def stop_server(self):
        # Flask 开发服务器没有内置的优雅停止方法从外部线程。
        # 对于生产环境，应使用 Gunicorn/uWSGI 等。
        # 对于此模拟盘，简单地退出主程序会终止daemon线程。
        # 如果需要更精细控制，可以研究werkzeug的关闭钩子，但会增加复杂性。
        print("SignalReceiver: 要停止服务器，请关闭主应用程序。")
        if hasattr(self, 'server_thread') and self.server_thread.is_alive():
             print(f"SignalReceiver: 服务器线程 ({self.server_thread.name}) 仍在运行。")


# --- 简单测试 (如何在预测系统中使用) ---
def send_mock_signal(signal_type, amount=None, target_name="TEST_10M"):
    """模拟预测系统发送信号。"""
    url = "http://127.0.0.1:5005/signal" # 与 SignalReceiver 中的 host/port 匹配
    payload = {
        "signal_type": signal_type,
        "target_name": target_name
    }
    if amount is not None:
        payload["amount"] = amount
    
    try:
        response = requests.post(url, json=payload, timeout=2)
        response.raise_for_status()
        print(f"  模拟信号发送: {signal_type}, 金额: {amount if amount else 'N/A'}, 目标: {target_name} -> 响应: {response.json()}")
    except requests.exceptions.ConnectionError:
        print(f"  模拟信号发送失败: 无法连接到 SignalReceiver 服务器 ({url})。请确保服务器已运行。")
    except requests.exceptions.RequestException as e:
        print(f"  模拟信号发送失败: {e}")

if __name__ == "__main__":
    print("--- 启动 SignalReceiver 服务器 ---")
    receiver = SignalReceiver()
    receiver.run_server() # 启动服务器在后台线程

    print("\n--- 模拟盘主应用轮询获取信号 (示例) ---")
    # 模拟主应用循环
    try:
        for i in range(10): # 模拟运行一段时间
            time.sleep(1)
            
            # 模拟预测系统在某个时刻发送信号
            if i == 2:
                print("\n[预测系统端] 准备发送做多信号...")
                send_mock_signal("UP", amount=50.0, target_name="BTC_10M_PRED")
            if i == 5:
                print("\n[预测系统端] 准备发送做空信号 (无金额)...")
                send_mock_signal("DOWN", target_name="ETH_5M_PRED")

            # 模拟盘应用获取信号
            signal_data = receiver.get_latest_signal(clear_after_get=True)
            if signal_data:
                print(f"[模拟盘端] 处理信号: 类型={signal_data['type']}, "
                      f"金额={signal_data.get('amount', '将由风控决定')}, "
                      f"来源={signal_data['target_name']}")
                # 在这里，模拟盘会调用 TradingEngine.open_trade() 等
            # else:
                # print("[模拟盘端] 未检测到新信号。")

    except KeyboardInterrupt:
        print("\n用户中断测试。")
    finally:
        print("\n--- 停止服务器 (演示) ---")
        receiver.stop_server() # 实际上对于开发服务器，主程序结束即可
        print("测试完成。 SignalReceiver 服务器线程如果是daemon，会随主程序退出。")
