@echo off
chcp 65001 >nul
title iPhone7自动交易系统

echo.
echo ========================================
echo 📱 iPhone7自动交易系统快速启动
echo ========================================
echo.
echo 🎉 系统状态: 完全成功 ✅
echo 📱 设备: iPhone7 (**************)
echo 🔧 技术: ZXTouch 0.0.8 + Python 3.9
echo.

:MENU
echo 请选择操作:
echo.
echo [1] 启动模拟盘 (集成iPhone自动化)
echo [2] 快速测试iPhone自动交易
echo [3] 交互式信号发送测试
echo [4] 查看使用说明
echo [5] 查看成功经验文档
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto START_SIM
if "%choice%"=="2" goto QUICK_TEST
if "%choice%"=="3" goto INTERACTIVE_TEST
if "%choice%"=="4" goto SHOW_README
if "%choice%"=="5" goto SHOW_DOC
if "%choice%"=="0" goto EXIT
goto MENU

:START_SIM
echo.
echo 🚀 启动模拟盘 (端口5008)...
echo ⚠️  请确保iPhone7在币安交易界面!
echo.
python SimMain.py --port 5008
pause
goto MENU

:QUICK_TEST
echo.
echo 🧪 快速测试iPhone自动交易...
echo 📱 将发送UP信号 10 USDT
echo.
python iphone_automation/quick_test.py
pause
goto MENU

:INTERACTIVE_TEST
echo.
echo 🎮 交互式信号发送测试...
echo.
python iphone_automation/test_signal_sender.py
pause
goto MENU

:SHOW_README
echo.
echo 📖 打开使用说明...
start iphone_automation/README_SIMPLE.md
goto MENU

:SHOW_DOC
echo.
echo 📚 打开成功经验文档...
start iPhone7自动交易成功经验文档.md
goto MENU

:EXIT
echo.
echo 👋 感谢使用iPhone7自动交易系统!
echo.
pause
exit
