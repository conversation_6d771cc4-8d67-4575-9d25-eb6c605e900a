#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MTFA列过滤器
完善的MTFA特征列过滤系统，维护显式排除列表和智能过滤规则
"""

import logging
import re
from typing import List, Set, Dict, Any, Optional
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class MTFAColumnFilter:
    """MTFA列过滤器 - 智能过滤MTFA特征列"""
    
    def __init__(self):
        """初始化过滤器"""
        self._initialize_exclusion_rules()
        self._initialize_inclusion_rules()
        self._initialize_data_type_rules()
    
    def _initialize_exclusion_rules(self):
        """初始化排除规则"""
        
        # 基础数据列（永远排除）
        self.base_columns = {
            'open', 'high', 'low', 'close', 'volume', 
            'qav', 'n', 'tbbav', 'tbqav'
        }
        
        # 目标变量列模式（永远排除）
        self.target_patterns = {
            'target_',           # 所有目标变量
            'future_close_',     # 未来收盘价
            'future_high_',      # 未来最高价
            'future_low_',       # 未来最低价
            'future_return_',    # 未来收益率
            'label_',            # 标签列
            'y_',                # 标签变量
        }
        
        # 字符串/分类列模式（永远排除）
        self.string_patterns = {
            '_name',             # 名称列
            '_pattern',          # 形态列
            '_signal',           # 信号列（如果是字符串）
            '_category',         # 分类列
            '_type',             # 类型列
            'candlestick_pattern_name',  # K线形态名称
            'pattern_name',      # 形态名称
        }
        
        # 临时/中间计算列模式（永远排除）
        self.temporary_patterns = {
            'temp_',             # 临时列
            'intermediate_',     # 中间计算列
            '_temp',             # 临时后缀
            '_intermediate',     # 中间后缀
            'calc_',             # 计算列
            '_calc',             # 计算后缀
            'debug_',            # 调试列
            '_debug',            # 调试后缀
        }
        
        # 元数据列模式（永远排除）
        self.metadata_patterns = {
            'timestamp',         # 时间戳
            'datetime',          # 日期时间
            'date',              # 日期
            'time',              # 时间
            'symbol',            # 交易对
            'interval',          # 时间间隔
            'source',            # 数据源
            'version',           # 版本
            '_id',               # ID列
            'id_',               # ID前缀
        }
        
        # 统计/诊断列模式（通常排除）
        self.diagnostic_patterns = {
            '_count',            # 计数列
            '_sum',              # 求和列
            '_mean',             # 均值列
            '_std',              # 标准差列
            '_var',              # 方差列
            '_min',              # 最小值列
            '_max',              # 最大值列
            'stat_',             # 统计前缀
            '_stat',             # 统计后缀
            'diag_',             # 诊断前缀
            '_diag',             # 诊断后缀
        }
        
        # 特定业务逻辑排除列（可配置）
        self.business_exclusions = {
            'commission',        # 手续费
            'slippage',          # 滑点
            'spread',            # 点差
            'funding_rate',      # 资金费率
            'liquidation_',      # 清算相关
            'margin_',           # 保证金相关
            'leverage_',         # 杠杆相关
        }
    
    def _initialize_inclusion_rules(self):
        """初始化包含规则 - 明确应该保留的特征模式"""
        
        # 技术指标特征（高优先级保留）
        self.technical_indicators = {
            'RSI_', 'rsi_',           # RSI
            'MACD', 'macd_',          # MACD
            'HMA_', 'hma_',           # Hull移动平均
            'EMA_', 'ema_',           # 指数移动平均
            'SMA_', 'sma_',           # 简单移动平均
            'ATR', 'atr_',            # 平均真实范围
            'WILLR_', 'willr_',       # Williams %R
            'CCI_', 'cci_',           # 商品通道指数
            'STOCH', 'stoch_',        # 随机指标
            'KC_', 'kc_',             # Keltner通道
            'BB_', 'bb_',             # 布林带
            'ADX', 'adx_',            # 平均方向指数
        }
        
        # 价格特征（高优先级保留）
        self.price_features = {
            'price_change_',      # 价格变化
            'price_pos_',         # 价格位置
            'price_vs_',          # 价格相对值
            'close_pos_',         # 收盘价位置
            'body_size',          # K线实体
            'candle_range',       # K线范围
            'upper_shadow',       # 上影线
            'lower_shadow',       # 下影线
        }
        
        # 成交量特征（高优先级保留）
        self.volume_features = {
            'volume_vs_avg',      # 成交量相对平均值
            'volume_change_',     # 成交量变化
            'volume_ratio_',      # 成交量比率
            'vol_',               # 成交量相关
            'taker_buy_ratio',    # 买方比率
        }
        
        # 波动率特征（高优先级保留）
        self.volatility_features = {
            'volatility_',        # 波动率
            'atr_',               # ATR相关
            'range_',             # 范围相关
            'stability_',         # 稳定性
        }
        
        # 趋势特征（高优先级保留）
        self.trend_features = {
            'trend_',             # 趋势相关
            'slope_',             # 斜率
            'direction_',         # 方向
            'momentum_',          # 动量
        }
    
    def _initialize_data_type_rules(self):
        """初始化数据类型规则"""
        
        # 允许的数值类型
        self.allowed_numeric_types = {
            'int8', 'int16', 'int32', 'int64',
            'uint8', 'uint16', 'uint32', 'uint64',
            'float16', 'float32', 'float64',
            'bool'
        }
        
        # 排除的数据类型
        self.excluded_data_types = {
            'object',             # 字符串/对象类型
            'string',             # 字符串类型
            'category',           # 分类类型
            'datetime64',         # 日期时间类型
            'timedelta64',        # 时间差类型
        }
    
    def filter_mtfa_columns(self, 
                           df: pd.DataFrame, 
                           timeframe: str,
                           config: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        过滤MTFA特征列，返回应该保留的列名列表
        
        Args:
            df: 包含特征的DataFrame
            timeframe: 时间框架字符串
            config: 可选的配置字典
            
        Returns:
            应该保留的列名列表
        """
        if df is None or df.empty:
            return []
        
        config = config or {}
        
        # 获取所有列名
        all_columns = df.columns.tolist()
        
        # 应用过滤规则
        filtered_columns = []
        exclusion_stats = {
            'base_columns': 0,
            'target_patterns': 0,
            'string_patterns': 0,
            'temporary_patterns': 0,
            'metadata_patterns': 0,
            'diagnostic_patterns': 0,
            'business_exclusions': 0,
            'data_type_exclusions': 0,
            'custom_exclusions': 0
        }
        
        for col in all_columns:
            # 检查是否应该排除
            exclusion_reason = self._should_exclude_column(col, df[col], config)
            
            if exclusion_reason:
                exclusion_stats[exclusion_reason] += 1
                logger.debug(f"MTFA列过滤 ({timeframe}): 排除 '{col}' - 原因: {exclusion_reason}")
                continue
            
            # 检查是否应该包含
            if self._should_include_column(col, df[col], config):
                filtered_columns.append(col)
            else:
                logger.debug(f"MTFA列过滤 ({timeframe}): 跳过 '{col}' - 不匹配包含规则")
        
        # 记录过滤统计
        total_excluded = sum(exclusion_stats.values())
        logger.info(f"MTFA列过滤完成 ({timeframe}): "
                   f"原始 {len(all_columns)} 列 -> 保留 {len(filtered_columns)} 列 "
                   f"(排除 {total_excluded} 列)")
        
        if logger.isEnabledFor(logging.DEBUG):
            for reason, count in exclusion_stats.items():
                if count > 0:
                    logger.debug(f"  {reason}: {count} 列")
        
        return filtered_columns
    
    def _should_exclude_column(self, 
                              col_name: str, 
                              col_data: pd.Series, 
                              config: Dict[str, Any]) -> Optional[str]:
        """
        检查列是否应该被排除
        
        Returns:
            排除原因字符串，如果不应排除则返回None
        """
        
        # 1. 基础数据列
        if col_name in self.base_columns:
            return 'base_columns'
        
        # 2. 目标变量列
        if any(pattern in col_name for pattern in self.target_patterns):
            return 'target_patterns'
        
        # 3. 字符串/分类列
        if any(pattern in col_name for pattern in self.string_patterns):
            return 'string_patterns'
        
        # 4. 临时/中间计算列
        if any(pattern in col_name for pattern in self.temporary_patterns):
            return 'temporary_patterns'
        
        # 5. 元数据列
        if any(pattern in col_name for pattern in self.metadata_patterns):
            return 'metadata_patterns'
        
        # 6. 统计/诊断列（可配置）
        if config.get('exclude_diagnostic_columns', True):
            if any(pattern in col_name for pattern in self.diagnostic_patterns):
                return 'diagnostic_patterns'
        
        # 7. 业务逻辑排除列（可配置）
        if config.get('exclude_business_columns', True):
            if any(pattern in col_name for pattern in self.business_exclusions):
                return 'business_exclusions'
        
        # 8. 数据类型排除
        if str(col_data.dtype) in self.excluded_data_types:
            return 'data_type_exclusions'
        
        # 9. 自定义排除列表
        custom_exclusions = config.get('custom_exclusion_patterns', [])
        if any(pattern in col_name for pattern in custom_exclusions):
            return 'custom_exclusions'
        
        # 10. 正则表达式排除
        regex_exclusions = config.get('regex_exclusion_patterns', [])
        for pattern in regex_exclusions:
            if re.search(pattern, col_name):
                return 'custom_exclusions'
        
        return None
    
    def _should_include_column(self, 
                              col_name: str, 
                              col_data: pd.Series, 
                              config: Dict[str, Any]) -> bool:
        """
        检查列是否应该被包含
        """
        
        # 1. 数据类型检查
        if str(col_data.dtype) not in self.allowed_numeric_types:
            return False
        
        # 2. 检查是否匹配包含模式
        all_inclusion_patterns = (
            self.technical_indicators | 
            self.price_features | 
            self.volume_features | 
            self.volatility_features | 
            self.trend_features
        )
        
        # 默认包含策略：匹配任何包含模式
        matches_inclusion = any(pattern in col_name for pattern in all_inclusion_patterns)
        
        # 3. 自定义包含列表
        custom_inclusions = config.get('custom_inclusion_patterns', [])
        matches_custom = any(pattern in col_name for pattern in custom_inclusions)
        
        # 4. 正则表达式包含
        regex_inclusions = config.get('regex_inclusion_patterns', [])
        matches_regex = any(re.search(pattern, col_name) for pattern in regex_inclusions)
        
        # 5. 宽松模式：如果启用，则包含所有数值列（除了排除的）
        loose_mode = config.get('loose_inclusion_mode', False)
        
        return matches_inclusion or matches_custom or matches_regex or loose_mode
    
    def get_filter_summary(self, 
                          original_columns: List[str], 
                          filtered_columns: List[str],
                          timeframe: str) -> Dict[str, Any]:
        """
        获取过滤摘要信息
        """
        excluded_columns = set(original_columns) - set(filtered_columns)
        
        return {
            'timeframe': timeframe,
            'original_count': len(original_columns),
            'filtered_count': len(filtered_columns),
            'excluded_count': len(excluded_columns),
            'retention_rate': len(filtered_columns) / len(original_columns) if original_columns else 0,
            'excluded_columns': list(excluded_columns)[:10],  # 只显示前10个
            'sample_included': filtered_columns[:10]  # 只显示前10个
        }

# 全局过滤器实例
mtfa_column_filter = MTFAColumnFilter()

def filter_mtfa_columns(df: pd.DataFrame, 
                       timeframe: str,
                       config: Optional[Dict[str, Any]] = None) -> List[str]:
    """
    便捷函数：过滤MTFA特征列
    
    Args:
        df: 包含特征的DataFrame
        timeframe: 时间框架字符串
        config: 可选的配置字典
        
    Returns:
        应该保留的列名列表
    """
    return mtfa_column_filter.filter_mtfa_columns(df, timeframe, config)

def get_default_mtfa_filter_config() -> Dict[str, Any]:
    """
    获取默认的MTFA过滤配置
    """
    return {
        'exclude_diagnostic_columns': True,      # 排除统计/诊断列
        'exclude_business_columns': True,        # 排除业务逻辑列
        'loose_inclusion_mode': False,           # 严格包含模式
        'custom_exclusion_patterns': [],        # 自定义排除模式
        'custom_inclusion_patterns': [],        # 自定义包含模式
        'regex_exclusion_patterns': [],         # 正则排除模式
        'regex_inclusion_patterns': []          # 正则包含模式
    }

def get_permissive_mtfa_filter_config() -> Dict[str, Any]:
    """
    获取宽松的MTFA过滤配置（包含更多列）
    """
    return {
        'exclude_diagnostic_columns': False,    # 不排除统计/诊断列
        'exclude_business_columns': False,      # 不排除业务逻辑列
        'loose_inclusion_mode': True,           # 宽松包含模式
        'custom_exclusion_patterns': [],
        'custom_inclusion_patterns': [],
        'regex_exclusion_patterns': [],
        'regex_inclusion_patterns': []
    }
