# 📱 iPhone7自动化交易系统 - 使用指南

## 🎉 系统状态：完全就绪 ✅

## 📁 文件说明

### 🔧 核心文件
- **`ssh_zxtouch_trader.py`** - 核心自动化脚本
- **`test_signal_sender.py`** - 信号发送测试器  
- **`system_status_check.py`** - 系统状态检查
- **`iphone_config.py`** - 配置文件

### 📚 文档文件
- **`AI_SUCCESS_EXPERIENCE.md`** - 完整开发经验文档 (⭐ 重要)
- **`PRODUCTION_READY.md`** - 生产就绪确认文档
- **`README.md`** - 本文件 (简易使用指南)

## 🚀 快速使用

### 生产环境 (真实交易)
```bash
# 启动模拟盘，自动接收预测系统信号
python SimMain.py --port 5008
```

### 测试环境
```bash
# 发送测试信号 (安全，不影响真实交易冷却)
python iphone_automation/test_signal_sender.py

# 直接调用测试
python iphone_automation/ssh_zxtouch_trader.py UP 25

# 系统状态检查
python iphone_automation/system_status_check.py
```

## ✅ 系统特性

- **成功率**: 100%
- **支持金额**: 5-250 USDT
- **交易方向**: 上涨/下跌双向
- **执行速度**: 完全优化
- **安全性**: 测试信号不影响真实交易冷却

## 📱 设备要求

- **iPhone7** (iOS 15.8.2, 已越狱)
- **IP**: **************
- **SSH**: mobile / sdzddhy  
- **ZXTouch**: 0.0.8完整版
- **界面**: 必须在币安期货交易界面

## 🎯 重要提醒

1. **测试安全**: 使用`test_signal_sender.py`测试完全安全
2. **真实交易**: 由预测系统自动发送MetaModel信号
3. **冷却机制**: 真实交易有5分钟冷却保护
4. **详细文档**: 查看`AI_SUCCESS_EXPERIENCE.md`了解完整技术细节

## 🎊 系统已完全就绪，可以开始自动化交易！🚀
