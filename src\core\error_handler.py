#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 完善的错误处理回退策略系统

提供分层错误处理、智能回退策略、错误分类和恢复机制，
确保系统在各种异常情况下都能优雅降级而不是崩溃。
"""

import logging
import traceback
import time
import functools
import inspect
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from enum import Enum
import pandas as pd
import numpy as np

# 导入特定异常类型
try:
    from binance.exceptions import BinanceAPIException, BinanceRequestException
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False

try:
    import requests.exceptions
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

# 导入配置管理器
try:
    from .config_manager import get_data_processing_param, get_constant
    CONFIG_MANAGER_AVAILABLE = True
except ImportError:
    CONFIG_MANAGER_AVAILABLE = False

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    CRITICAL = "critical"      # 关键错误，必须中断
    HIGH = "high"             # 高级错误，建议中断
    MEDIUM = "medium"         # 中等错误，可以回退
    LOW = "low"               # 低级错误，可以忽略
    WARNING = "warning"       # 警告，记录但继续


class ModuleType(Enum):
    """模块类型枚举"""
    MANDATORY = "mandatory"    # 强制模块，失败时中断
    IMPORTANT = "important"    # 重要模块，失败时回退
    OPTIONAL = "optional"      # 可选模块，失败时忽略
    ENHANCEMENT = "enhancement" # 增强模块，失败时使用默认值


class ErrorHandler:
    """
    🚀 错误处理器
    
    提供分层错误处理、智能回退策略和错误恢复机制。
    """
    
    def __init__(self, target_config: Optional[Dict[str, Any]] = None):
        """
        初始化错误处理器
        
        Args:
            target_config: 目标配置字典
        """
        self.target_config = target_config or {}
        
        # 🚀 从配置获取参数
        if CONFIG_MANAGER_AVAILABLE:
            self.enable_auto_fallback = get_data_processing_param('enable_auto_fallback', target_config, True, bool)
            self.max_retry_attempts = get_data_processing_param('max_retry_attempts', target_config, 3, int)
            self.retry_delay = get_data_processing_param('retry_delay_seconds', target_config, 1.0, float)
            self.enable_detailed_logging = get_data_processing_param('enable_detailed_error_logging', target_config, True, bool)
        else:
            self.enable_auto_fallback = True
            self.max_retry_attempts = 3
            self.retry_delay = 1.0
            self.enable_detailed_logging = True
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'critical_errors': 0,
            'high_errors': 0,
            'medium_errors': 0,
            'low_errors': 0,
            'warnings': 0,
            'successful_fallbacks': 0,
            'failed_fallbacks': 0,
            'retries_attempted': 0
        }
        
        # 模块配置
        self.module_configs = {
            # 基础数据处理 - 强制模块
            'data_validation': {
                'type': ModuleType.MANDATORY,
                'severity': ErrorSeverity.CRITICAL,
                'fallback_strategy': 'abort',
                'description': '数据验证'
            },
            'basic_columns': {
                'type': ModuleType.MANDATORY,
                'severity': ErrorSeverity.CRITICAL,
                'fallback_strategy': 'abort',
                'description': '基础列处理'
            },
            
            # 技术指标 - 重要模块
            'technical_indicators': {
                'type': ModuleType.IMPORTANT,
                'severity': ErrorSeverity.HIGH,
                'fallback_strategy': 'default_values',
                'description': '技术指标计算'
            },
            'price_features': {
                'type': ModuleType.IMPORTANT,
                'severity': ErrorSeverity.HIGH,
                'fallback_strategy': 'simplified_calculation',
                'description': '价格特征计算'
            },
            
            # 高级特征 - 可选模块
            'volume_features': {
                'type': ModuleType.OPTIONAL,
                'severity': ErrorSeverity.MEDIUM,
                'fallback_strategy': 'skip_module',
                'description': '成交量特征'
            },
            'candlestick_patterns': {
                'type': ModuleType.OPTIONAL,
                'severity': ErrorSeverity.MEDIUM,
                'fallback_strategy': 'skip_module',
                'description': '蜡烛图形态'
            },
            'fund_flow': {
                'type': ModuleType.OPTIONAL,
                'severity': ErrorSeverity.LOW,
                'fallback_strategy': 'skip_module',
                'description': '资金流向'
            },
            
            # 增强功能 - 增强模块
            'mtfa_features': {
                'type': ModuleType.ENHANCEMENT,
                'severity': ErrorSeverity.MEDIUM,
                'fallback_strategy': 'simplified_mtfa',
                'description': 'MTFA特征'
            },
            'market_state': {
                'type': ModuleType.ENHANCEMENT,
                'severity': ErrorSeverity.LOW,
                'fallback_strategy': 'default_state',
                'description': '市场状态分析'
            },
            'advanced_features': {
                'type': ModuleType.ENHANCEMENT,
                'severity': ErrorSeverity.LOW,
                'fallback_strategy': 'skip_module',
                'description': '高级特征'
            }
        }
    
    def handle_module_error(
        self,
        module_name: str,
        error: Exception,
        context: Dict[str, Any],
        fallback_func: Optional[Callable] = None,
        **kwargs
    ) -> Tuple[bool, Any]:
        """
        🚀 处理模块错误
        
        Args:
            module_name: 模块名称
            error: 异常对象
            context: 错误上下文信息
            fallback_func: 回退函数
            **kwargs: 传递给回退函数的参数
            
        Returns:
            tuple: (是否应该继续, 回退结果)
        """
        # 获取模块配置
        module_config = self.module_configs.get(module_name, {
            'type': ModuleType.OPTIONAL,
            'severity': ErrorSeverity.MEDIUM,
            'fallback_strategy': 'skip_module',
            'description': module_name
        })
        
        # 更新错误统计
        self._update_error_stats(module_config['severity'])
        
        # 记录错误
        self._log_error(module_name, error, context, module_config)
        
        # 根据模块类型决定处理策略
        if module_config['type'] == ModuleType.MANDATORY:
            # 强制模块失败，必须中断
            logger.critical(f"handle_module_error: 强制模块 '{module_name}' 失败，中断处理")
            return False, None
        
        elif module_config['type'] == ModuleType.IMPORTANT:
            # 重要模块失败，尝试回退
            if self.enable_auto_fallback and fallback_func:
                try:
                    logger.warning(f"handle_module_error: 重要模块 '{module_name}' 失败，尝试回退")
                    fallback_result = fallback_func(**kwargs)
                    self.error_stats['successful_fallbacks'] += 1
                    return True, fallback_result
                except Exception as fallback_error:
                    logger.error(f"handle_module_error: 模块 '{module_name}' 回退失败: {fallback_error}")
                    self.error_stats['failed_fallbacks'] += 1
                    return False, None
            else:
                logger.error(f"handle_module_error: 重要模块 '{module_name}' 失败且无回退策略")
                return False, None
        
        elif module_config['type'] == ModuleType.OPTIONAL:
            # 可选模块失败，跳过并继续
            logger.warning(f"handle_module_error: 可选模块 '{module_name}' 失败，跳过")
            return True, self._get_default_result(module_name, context)
        
        elif module_config['type'] == ModuleType.ENHANCEMENT:
            # 增强模块失败，使用默认值
            logger.info(f"handle_module_error: 增强模块 '{module_name}' 失败，使用默认值")
            return True, self._get_default_result(module_name, context)
        
        else:
            # 未知模块类型，保守处理
            logger.warning(f"handle_module_error: 未知模块类型 '{module_name}'，跳过")
            return True, None
    
    def with_error_handling(
        self,
        module_name: str,
        fallback_func: Optional[Callable] = None,
        retry_on_failure: bool = False
    ):
        """
        🚀 错误处理装饰器
        
        Args:
            module_name: 模块名称
            fallback_func: 回退函数
            retry_on_failure: 是否在失败时重试
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                last_error = None
                
                # 重试机制
                max_attempts = self.max_retry_attempts if retry_on_failure else 1
                
                for attempt in range(max_attempts):
                    try:
                        if attempt > 0:
                            logger.info(f"with_error_handling: 重试模块 '{module_name}' (第{attempt+1}次)")
                            time.sleep(self.retry_delay)
                            self.error_stats['retries_attempted'] += 1
                        
                        result = func(*args, **kwargs)
                        
                        # 成功执行
                        if attempt > 0:
                            logger.info(f"with_error_handling: 模块 '{module_name}' 重试成功")
                        
                        return result
                        
                    except Exception as e:
                        last_error = e
                        
                        if attempt < max_attempts - 1:
                            logger.warning(f"with_error_handling: 模块 '{module_name}' 第{attempt+1}次尝试失败: {e}")
                            continue
                        else:
                            # 所有重试都失败了
                            break
                
                # 处理最终失败
                context = {
                    'function_name': func.__name__,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys()),
                    'attempts_made': max_attempts
                }
                
                should_continue, fallback_result = self.handle_module_error(
                    module_name, last_error, context, fallback_func
                )
                
                if should_continue:
                    return fallback_result
                else:
                    # 重新抛出异常或返回None
                    if self._is_critical_module(module_name):
                        raise last_error
                    else:
                        return None
            
            return wrapper
        return decorator
    
    def validate_critical_data(
        self,
        df: pd.DataFrame,
        required_columns: List[str],
        min_rows: int = 1
    ) -> Tuple[bool, str]:
        """
        🚀 验证关键数据
        
        Args:
            df: 输入DataFrame
            required_columns: 必需的列
            min_rows: 最小行数
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            # 检查DataFrame是否为空
            if df is None or df.empty:
                return False, "DataFrame为空或None"
            
            # 检查行数
            if len(df) < min_rows:
                return False, f"数据行数不足: {len(df)} < {min_rows}"
            
            # 检查必需列
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"缺少必需的列: {missing_columns}"
            
            # 检查关键列的数据质量
            for col in required_columns:
                if col in df.columns:
                    # 检查是否全为NaN
                    if df[col].isna().all():
                        return False, f"列 '{col}' 全为NaN值"
                    
                    # 检查数据类型
                    if col in ['open', 'high', 'low', 'close', 'volume']:
                        if not pd.api.types.is_numeric_dtype(df[col]):
                            try:
                                pd.to_numeric(df[col], errors='raise')
                            except (ValueError, TypeError):
                                return False, f"列 '{col}' 无法转换为数值类型"
            
            return True, "数据验证通过"
            
        except Exception as e:
            return False, f"数据验证过程中出错: {e}"
    
    def create_safe_default_dataframe(
        self,
        original_df: Optional[pd.DataFrame],
        required_columns: List[str],
        default_values: Optional[Dict[str, Any]] = None
    ) -> pd.DataFrame:
        """
        🚀 创建安全的默认DataFrame
        
        Args:
            original_df: 原始DataFrame
            required_columns: 必需的列
            default_values: 默认值字典
            
        Returns:
            安全的默认DataFrame
        """
        try:
            if default_values is None:
                default_values = {
                    'open': 100.0, 'high': 101.0, 'low': 99.0, 'close': 100.0,
                    'volume': 1000.0, 'rsi': 50.0, 'macd': 0.0, 'atr': 1.0
                }
            
            # 如果原始DataFrame可用，使用其索引
            if original_df is not None and not original_df.empty:
                index = original_df.index
                rows = len(original_df)
            else:
                # 创建最小的时间索引
                index = pd.date_range('2023-01-01', periods=1, freq='1H')
                rows = 1
            
            # 创建默认数据
            data = {}
            for col in required_columns:
                if col in default_values:
                    data[col] = [default_values[col]] * rows
                else:
                    # 根据列名推断默认值
                    if 'price' in col.lower() or col in ['open', 'high', 'low', 'close']:
                        data[col] = [100.0] * rows
                    elif 'volume' in col.lower():
                        data[col] = [1000.0] * rows
                    elif 'rsi' in col.lower():
                        data[col] = [50.0] * rows
                    else:
                        data[col] = [0.0] * rows
            
            df_default = pd.DataFrame(data, index=index)
            
            logger.warning(f"create_safe_default_dataframe: 创建了包含 {len(required_columns)} 列的默认DataFrame")
            return df_default
            
        except Exception as e:
            logger.error(f"create_safe_default_dataframe: 创建默认DataFrame失败: {e}")
            # 最后的回退：创建最小DataFrame
            return pd.DataFrame({
                'close': [100.0],
                'volume': [1000.0]
            })
    
    def _update_error_stats(self, severity: ErrorSeverity):
        """更新错误统计"""
        self.error_stats['total_errors'] += 1
        
        if severity == ErrorSeverity.CRITICAL:
            self.error_stats['critical_errors'] += 1
        elif severity == ErrorSeverity.HIGH:
            self.error_stats['high_errors'] += 1
        elif severity == ErrorSeverity.MEDIUM:
            self.error_stats['medium_errors'] += 1
        elif severity == ErrorSeverity.LOW:
            self.error_stats['low_errors'] += 1
        elif severity == ErrorSeverity.WARNING:
            self.error_stats['warnings'] += 1
    
    def _log_error(
        self,
        module_name: str,
        error: Exception,
        context: Dict[str, Any],
        module_config: Dict[str, Any]
    ):
        """记录错误信息"""
        severity = module_config['severity']
        description = module_config['description']
        
        error_msg = f"模块错误 [{module_name}] ({description}): {error}"
        
        if self.enable_detailed_logging:
            error_msg += f"\n  上下文: {context}"
            error_msg += f"\n  模块类型: {module_config['type'].value}"
            error_msg += f"\n  回退策略: {module_config['fallback_strategy']}"
        
        if severity == ErrorSeverity.CRITICAL:
            logger.critical(error_msg)
            if self.enable_detailed_logging:
                logger.critical(f"堆栈跟踪:\n{traceback.format_exc()}")
        elif severity == ErrorSeverity.HIGH:
            logger.error(error_msg)
            if self.enable_detailed_logging:
                logger.debug(f"堆栈跟踪:\n{traceback.format_exc()}")
        elif severity == ErrorSeverity.MEDIUM:
            logger.warning(error_msg)
        elif severity == ErrorSeverity.LOW:
            logger.info(error_msg)
        else:
            logger.debug(error_msg)
    
    def _get_default_result(self, module_name: str, context: Dict[str, Any]) -> Any:
        """获取模块的默认结果"""
        # 根据模块名称返回合适的默认值
        if 'features' in module_name.lower():
            return {}  # 空特征字典
        elif 'dataframe' in str(context.get('expected_type', '')).lower():
            return pd.DataFrame()  # 空DataFrame
        elif 'indicators' in module_name.lower():
            return {'rsi': 50.0, 'macd': 0.0, 'atr': 1.0}  # 默认指标值
        else:
            return None
    
    def _is_critical_module(self, module_name: str) -> bool:
        """检查是否为关键模块"""
        module_config = self.module_configs.get(module_name, {})
        return module_config.get('type') == ModuleType.MANDATORY
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误统计摘要"""
        total = self.error_stats['total_errors']
        if total == 0:
            return {'status': 'no_errors', 'total_errors': 0}
        
        return {
            'status': 'has_errors',
            'total_errors': total,
            'error_breakdown': {
                'critical': self.error_stats['critical_errors'],
                'high': self.error_stats['high_errors'],
                'medium': self.error_stats['medium_errors'],
                'low': self.error_stats['low_errors'],
                'warnings': self.error_stats['warnings']
            },
            'fallback_stats': {
                'successful': self.error_stats['successful_fallbacks'],
                'failed': self.error_stats['failed_fallbacks']
            },
            'retry_stats': {
                'attempts': self.error_stats['retries_attempted']
            },
            'error_rate': {
                'critical_rate': self.error_stats['critical_errors'] / total,
                'high_rate': self.error_stats['high_errors'] / total,
                'fallback_success_rate': (
                    self.error_stats['successful_fallbacks'] / 
                    max(1, self.error_stats['successful_fallbacks'] + self.error_stats['failed_fallbacks'])
                )
            }
        }
    
    def reset_stats(self):
        """重置错误统计"""
        self.error_stats = {
            'total_errors': 0,
            'critical_errors': 0,
            'high_errors': 0,
            'medium_errors': 0,
            'low_errors': 0,
            'warnings': 0,
            'successful_fallbacks': 0,
            'failed_fallbacks': 0,
            'retries_attempted': 0
        }


# 全局错误处理器实例
_error_handler = None

def get_error_handler(target_config: Optional[Dict[str, Any]] = None) -> ErrorHandler:
    """获取全局错误处理器实例（单例模式）"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler(target_config)
    return _error_handler


# 便捷装饰器函数
def safe_execute(
    module_name: str,
    fallback_func: Optional[Callable] = None,
    retry_on_failure: bool = False
):
    """
    🚀 安全执行装饰器
    
    Args:
        module_name: 模块名称
        fallback_func: 回退函数
        retry_on_failure: 是否在失败时重试
    """
    error_handler = get_error_handler()
    return error_handler.with_error_handling(module_name, fallback_func, retry_on_failure)


def validate_data_safety(
    df: pd.DataFrame,
    required_columns: List[str],
    min_rows: int = 1
) -> Tuple[bool, str]:
    """
    🚀 便捷的数据安全验证函数
    
    Args:
        df: 输入DataFrame
        required_columns: 必需的列
        min_rows: 最小行数
        
    Returns:
        tuple: (是否有效, 错误信息)
    """
    error_handler = get_error_handler()
    return error_handler.validate_critical_data(df, required_columns, min_rows)


def get_error_summary() -> Dict[str, Any]:
    """获取全局错误统计摘要"""
    error_handler = get_error_handler()
    return error_handler.get_error_summary()


# ============================================================================
# 🚀 训练异常处理装饰器
# ============================================================================

class EnhancedLogger:
    """增强的日志记录器，支持上下文信息记录"""

    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)

    def log_with_context(self, level: str, message: str, **context):
        """记录带上下文信息的日志"""
        if context:
            context_str = ", ".join([f"ctx_{k}={v}" for k, v in context.items()])
            full_message = f"{message} | {context_str}"
        else:
            full_message = message

        getattr(self.logger, level.lower())(full_message)

    def info(self, message: str, **context):
        """记录信息级别日志"""
        self.log_with_context('INFO', message, **context)

    def warning(self, message: str, **context):
        """记录警告级别日志"""
        self.log_with_context('WARNING', message, **context)

    def error(self, message: str, **context):
        """记录错误级别日志"""
        self.log_with_context('ERROR', message, **context)

    def debug(self, message: str, **context):
        """记录调试级别日志"""
        self.log_with_context('DEBUG', message, **context)


def get_enhanced_logger(logger_name: str = __name__) -> EnhancedLogger:
    """获取增强日志记录器实例"""
    return EnhancedLogger(logger_name)


def handle_training_exception(
    function_name: str = None,
    target_name: str = None,
    fallback_result: Any = None,
    log_level: str = 'ERROR',
    include_traceback: bool = True,
    max_traceback_lines: int = 10
):
    """
    🚀 训练异常处理装饰器

    专门用于训练相关函数的异常处理，提供详细的上下文信息记录

    Args:
        function_name: 函数名称（可选，默认使用被装饰函数名）
        target_name: 目标名称（可选）
        fallback_result: 异常时返回的默认值
        log_level: 日志级别
        include_traceback: 是否包含堆栈跟踪
        max_traceback_lines: 最大堆栈跟踪行数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取增强日志记录器
            enhanced_logger = get_enhanced_logger(func.__module__)

            # 确定函数名称
            actual_function_name = function_name or func.__name__

            # 提取目标名称（如果在参数中）
            actual_target_name = target_name
            if not actual_target_name:
                # 尝试从参数中提取target_name
                if 'target_name' in kwargs:
                    actual_target_name = kwargs['target_name']
                elif len(args) > 0 and hasattr(args[0], '__class__'):
                    # 如果第一个参数是对象，尝试获取其属性
                    if hasattr(args[0], 'target_name'):
                        actual_target_name = args[0].target_name

            try:
                # 记录函数开始执行
                enhanced_logger.debug(
                    f"开始执行 {actual_function_name}",
                    target_name=actual_target_name,
                    args_count=len(args),
                    kwargs_keys=list(kwargs.keys())
                )

                # 执行原函数
                result = func(*args, **kwargs)

                # 记录成功执行
                enhanced_logger.debug(
                    f"成功完成 {actual_function_name}",
                    target_name=actual_target_name
                )

                return result

            except Exception as e:
                # 获取异常详细信息
                exc_type, exc_value, exc_traceback = sys.exc_info()

                # 获取调用栈信息
                frame = inspect.currentframe()
                caller_info = {}
                if frame and frame.f_back:
                    caller_frame = frame.f_back
                    caller_info = {
                        'line_number': caller_frame.f_lineno,
                        'filename': caller_frame.f_code.co_filename.split('/')[-1]
                    }

                # 构建错误上下文
                error_context = {
                    'function': actual_function_name,
                    'target_name': actual_target_name,
                    'error_type': type(e).__name__,
                    'timestamp': datetime.now().isoformat(),
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                }
                error_context.update(caller_info)

                # 分类处理不同类型的异常
                if BINANCE_AVAILABLE and isinstance(e, BinanceAPIException):
                    error_context.update({
                        'binance_error_code': e.code,
                        'binance_error_message': e.message
                    })
                    enhanced_logger.error(
                        f"Binance API异常在 {actual_function_name}",
                        **error_context
                    )
                elif BINANCE_AVAILABLE and isinstance(e, BinanceRequestException):
                    enhanced_logger.error(
                        f"Binance请求异常在 {actual_function_name}",
                        **error_context
                    )
                elif REQUESTS_AVAILABLE and isinstance(e, requests.exceptions.ConnectionError):
                    enhanced_logger.error(
                        f"网络连接异常在 {actual_function_name}",
                        **error_context
                    )
                elif REQUESTS_AVAILABLE and isinstance(e, requests.exceptions.Timeout):
                    enhanced_logger.error(
                        f"请求超时异常在 {actual_function_name}",
                        **error_context
                    )
                elif isinstance(e, (ValueError, TypeError)):
                    enhanced_logger.error(
                        f"数据类型异常在 {actual_function_name}",
                        **error_context
                    )
                elif isinstance(e, KeyError):
                    error_context['missing_key'] = str(e)
                    enhanced_logger.error(
                        f"键值缺失异常在 {actual_function_name}",
                        **error_context
                    )
                elif isinstance(e, FileNotFoundError):
                    enhanced_logger.error(
                        f"文件未找到异常在 {actual_function_name}",
                        **error_context
                    )
                elif isinstance(e, MemoryError):
                    enhanced_logger.error(
                        f"内存不足异常在 {actual_function_name}",
                        **error_context
                    )
                else:
                    # 通用异常处理
                    enhanced_logger.error(
                        f"未知异常在 {actual_function_name}: {str(e)}",
                        **error_context
                    )

                # 记录堆栈跟踪（如果需要）
                if include_traceback:
                    tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
                    if max_traceback_lines > 0:
                        tb_lines = tb_lines[-max_traceback_lines:]

                    enhanced_logger.debug(
                        f"堆栈跟踪 {actual_function_name}",
                        traceback_info=''.join(tb_lines),
                        **error_context
                    )

                # 返回回退结果或重新抛出异常
                if fallback_result is not None:
                    enhanced_logger.warning(
                        f"使用回退结果 {actual_function_name}",
                        fallback_result=str(fallback_result),
                        **error_context
                    )
                    return fallback_result
                else:
                    # 重新抛出异常
                    raise

        return wrapper
    return decorator
