#!/usr/bin/env python3
"""
增强样本权重配置示例
展示如何配置细化的动态样本权重策略
"""

# 增强样本权重配置
ENHANCED_SAMPLE_WEIGHTING_CONFIG = {
    # 启用增强权重功能
    'enable_enhanced_sample_weighting': True,
    
    # 增强权重配置
    'enhanced_weighting_config': {
        # 启用的权重策略
        'enable_time_decay': True,
        'enable_volatility_weighting': True,
        'enable_volume_weighting': True,
        'enable_market_state_weighting': True,
        
        # 时间衰减配置
        'time_decay': {
            'decay_rate': 0.95,           # 衰减率：越近的数据权重越高
            'min_weight': 0.1,            # 最小权重
            'max_weight': 2.0,            # 最大权重
        },
        
        # 波动率权重配置
        'volatility_weighting': {
            'lookback_period': 20,        # 波动率计算回望期
            'extreme_vol_threshold': 2.0, # 极端波动阈值（标准差倍数）
            'low_vol_multiplier': 1.5,    # 低波动权重倍数（趋势酝酿期）
            'high_vol_multiplier': 0.3,   # 高波动权重倍数（噪音过滤）
        },
        
        # 成交量权重配置
        'volume_weighting': {
            'lookback_period': 20,        # 成交量计算回望期
            'volume_threshold': 1.5,      # 放量阈值（平均成交量倍数）
            'high_volume_multiplier': 2.0, # 放量权重倍数（突破信号）
            'low_volume_multiplier': 0.8,  # 缩量权重倍数
        },
        
        # 权重组合策略
        'combination_method': 'weighted_average',  # 'weighted_average', 'multiply', 'max'
        'strategy_weights': {
            'time_decay': 0.3,      # 时间衰减权重占比
            'volatility': 0.3,      # 波动率权重占比
            'volume': 0.2,          # 成交量权重占比
            'market_state': 0.2     # 市场状态权重占比
        }
    },
    
    # 数据质量监控配置
    'data_quality_config': {
        'quality_thresholds': {
            'min_positive_ratio': 0.01,      # 最小正样本比例 1%
            'max_positive_ratio': 0.30,      # 最大正样本比例 30%
            'min_feature_variance': 1e-10,   # 最小特征方差
            'max_correlation': 0.98,         # 最大特征相关性
            'max_nan_ratio': 0.1,            # 最大NaN比例 10%
            'min_samples': 100,              # 最小样本数
        }
    }
}

# 不同市场条件下的权重策略配置
MARKET_CONDITION_CONFIGS = {
    # 牛市配置：更关注趋势延续
    'bull_market': {
        'enhanced_weighting_config': {
            'strategy_weights': {
                'time_decay': 0.4,      # 更重视近期数据
                'volatility': 0.2,      # 降低波动率权重
                'volume': 0.3,          # 重视成交量确认
                'market_state': 0.1     # 降低市场状态权重
            },
            'volatility_weighting': {
                'low_vol_multiplier': 2.0,    # 更重视低波动趋势
                'high_vol_multiplier': 0.2,   # 更严格过滤高波动
            }
        }
    },
    
    # 熊市配置：更关注反转信号
    'bear_market': {
        'enhanced_weighting_config': {
            'strategy_weights': {
                'time_decay': 0.2,      # 降低时间权重
                'volatility': 0.4,      # 重视波动率变化
                'volume': 0.2,          # 适中的成交量权重
                'market_state': 0.2     # 重视市场状态
            },
            'volatility_weighting': {
                'extreme_vol_threshold': 1.5,  # 降低极端波动阈值
                'low_vol_multiplier': 1.2,     # 适度重视低波动
                'high_vol_multiplier': 0.5,    # 适度过滤高波动
            }
        }
    },
    
    # 震荡市配置：平衡各种因素
    'sideways_market': {
        'enhanced_weighting_config': {
            'strategy_weights': {
                'time_decay': 0.25,     # 平衡时间权重
                'volatility': 0.25,     # 平衡波动率权重
                'volume': 0.25,         # 平衡成交量权重
                'market_state': 0.25    # 平衡市场状态权重
            },
            'combination_method': 'multiply',  # 使用乘积组合，更保守
        }
    }
}

# 不同时间框架的权重策略配置
TIMEFRAME_CONFIGS = {
    # 短期交易（5m, 15m）
    'short_term': {
        'enhanced_weighting_config': {
            'time_decay': {
                'decay_rate': 0.90,     # 更快的衰减
                'max_weight': 3.0,      # 更高的最大权重
            },
            'volatility_weighting': {
                'lookback_period': 10,  # 更短的回望期
                'extreme_vol_threshold': 1.5,
            },
            'volume_weighting': {
                'lookback_period': 10,  # 更短的回望期
                'volume_threshold': 1.2, # 更低的放量阈值
            }
        }
    },
    
    # 中期交易（1h, 4h）
    'medium_term': {
        'enhanced_weighting_config': {
            'time_decay': {
                'decay_rate': 0.95,     # 标准衰减
                'max_weight': 2.0,      # 标准最大权重
            },
            'volatility_weighting': {
                'lookback_period': 20,  # 标准回望期
                'extreme_vol_threshold': 2.0,
            },
            'volume_weighting': {
                'lookback_period': 20,  # 标准回望期
                'volume_threshold': 1.5, # 标准放量阈值
            }
        }
    },
    
    # 长期交易（1d）
    'long_term': {
        'enhanced_weighting_config': {
            'time_decay': {
                'decay_rate': 0.98,     # 更慢的衰减
                'max_weight': 1.5,      # 更低的最大权重
            },
            'volatility_weighting': {
                'lookback_period': 50,  # 更长的回望期
                'extreme_vol_threshold': 2.5,
            },
            'volume_weighting': {
                'lookback_period': 50,  # 更长的回望期
                'volume_threshold': 2.0, # 更高的放量阈值
            }
        }
    }
}

# 使用示例函数
def get_enhanced_weighting_config(market_condition='normal', timeframe='medium_term'):
    """
    根据市场条件和时间框架获取增强权重配置
    
    Args:
        market_condition: 市场条件 ('bull_market', 'bear_market', 'sideways_market', 'normal')
        timeframe: 时间框架 ('short_term', 'medium_term', 'long_term')
        
    Returns:
        配置字典
    """
    # 基础配置
    config = ENHANCED_SAMPLE_WEIGHTING_CONFIG.copy()
    
    # 应用市场条件配置
    if market_condition in MARKET_CONDITION_CONFIGS:
        market_config = MARKET_CONDITION_CONFIGS[market_condition]
        config = _merge_configs(config, market_config)
    
    # 应用时间框架配置
    if timeframe in TIMEFRAME_CONFIGS:
        timeframe_config = TIMEFRAME_CONFIGS[timeframe]
        config = _merge_configs(config, timeframe_config)
    
    return config

def _merge_configs(base_config, override_config):
    """递归合并配置字典"""
    result = base_config.copy()
    for key, value in override_config.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _merge_configs(result[key], value)
        else:
            result[key] = value
    return result

# 配置验证函数
def validate_enhanced_weighting_config(config):
    """
    验证增强权重配置的有效性
    
    Args:
        config: 配置字典
        
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    if not isinstance(config, dict):
        errors.append("配置必须是字典类型")
        return False, errors
    
    # 检查必要的配置项
    required_keys = ['enhanced_weighting_config']
    for key in required_keys:
        if key not in config:
            errors.append(f"缺少必要配置项: {key}")
    
    if 'enhanced_weighting_config' in config:
        weighting_config = config['enhanced_weighting_config']
        
        # 检查权重组合策略
        if 'strategy_weights' in weighting_config:
            weights = weighting_config['strategy_weights']
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.1:
                errors.append(f"策略权重总和应接近1.0，当前为: {total_weight}")
        
        # 检查时间衰减配置
        if 'time_decay' in weighting_config:
            time_config = weighting_config['time_decay']
            if 'decay_rate' in time_config:
                decay_rate = time_config['decay_rate']
                if not (0.5 <= decay_rate <= 1.0):
                    errors.append(f"衰减率应在0.5-1.0之间，当前为: {decay_rate}")
    
    return len(errors) == 0, errors
