#!/usr/bin/env python3
"""
彻底禁用项目中所有GPU使用
确保训练时只使用CPU
"""

import os
import sys

def disable_gpu_environment():
    """设置环境变量禁用GPU"""
    print("🚫 设置环境变量禁用GPU...")
    
    # 禁用CUDA
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'
    os.environ['TF_GPU_ALLOCATOR'] = 'cpu'
    
    # 移除GPU相关环境变量
    gpu_env_vars = [
        'CUDA_PATH', 'CUDA_HOME', 'CUDA_ROOT',
        'TF_GPU_ALLOCATOR', 'NVIDIA_VISIBLE_DEVICES'
    ]
    
    for var in gpu_env_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"  已移除环境变量: {var}")
    
    print("✅ GPU环境变量已禁用")

def create_cpu_only_config():
    """创建纯CPU配置文件"""
    print("\n📝 创建纯CPU配置文件...")
    
    config_content = '''"""
纯CPU训练配置
确保项目完全不使用GPU
"""

import os
import multiprocessing as mp

# 强制禁用GPU
def force_cpu_only():
    """强制使用CPU，禁用所有GPU"""
    print("🚫 强制CPU模式已启用")
    
    # 禁用CUDA
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'
    
    # TensorFlow CPU优化
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
    os.environ['TF_NUM_INTEROP_THREADS'] = '4'
    os.environ['TF_NUM_INTRAOP_THREADS'] = '16'  # AMD 16核
    
    # CPU线程优化
    cpu_count = mp.cpu_count()
    os.environ['OMP_NUM_THREADS'] = str(cpu_count)
    os.environ['MKL_NUM_THREADS'] = str(cpu_count)
    os.environ['NUMBA_NUM_THREADS'] = str(cpu_count)
    os.environ['OPENBLAS_NUM_THREADS'] = str(cpu_count)
    
    print(f"✅ CPU优化配置完成 - 使用 {cpu_count} 核心")

# LightGBM纯CPU参数
LIGHTGBM_CPU_ONLY_PARAMS = {
    'device_type': 'cpu',                    # 强制CPU
    'num_threads': mp.cpu_count(),           # 使用所有CPU核心
    'force_row_wise': True,                  # AMD处理器优化
    'histogram_pool_size': -1,               # 自动内存池
    'max_bin': 255,                          # 适合AMD缓存
    'boost_from_average': True,              # AMD友好初始化
    'tree_learner': 'serial',                # 串行学习器
    'verbose': -1,
    'objective': 'binary',
    'metric': 'binary_logloss',
    'boosting_type': 'gbdt',
    'random_state': 42,
    'learning_rate': 0.1,                    # CPU可以用更高学习率
    'feature_fraction': 0.9,                 # CPU模式可以用更高比例
    'bagging_fraction': 0.8,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'min_data_in_leaf': 20,
    'min_sum_hessian_in_leaf': 1e-3
}

def setup_tensorflow_cpu_only():
    """设置TensorFlow纯CPU模式"""
    try:
        import tensorflow as tf
        
        # 禁用GPU
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        
        # CPU线程配置
        tf.config.threading.set_inter_op_parallelism_threads(4)
        tf.config.threading.set_intra_op_parallelism_threads(16)  # AMD 16核
        
        print("✅ TensorFlow CPU模式配置完成")
        return True
        
    except ImportError:
        print("⚠️  TensorFlow未安装")
        return False
    except Exception as e:
        print(f"❌ TensorFlow配置失败: {e}")
        return False

def create_cpu_only_lightgbm_model(**kwargs):
    """创建纯CPU LightGBM模型"""
    try:
        import lightgbm as lgb
        
        # 合并参数，确保device_type为cpu
        params = LIGHTGBM_CPU_ONLY_PARAMS.copy()
        params.update(kwargs)
        params['device_type'] = 'cpu'  # 强制CPU
        
        # 移除任何GPU相关参数
        gpu_params = ['gpu_platform_id', 'gpu_device_id', 'gpu_use_dp']
        for param in gpu_params:
            params.pop(param, None)
        
        model = lgb.LGBMClassifier(**params)
        print("✅ 纯CPU LightGBM模型创建成功")
        return model
        
    except ImportError:
        print("❌ LightGBM未安装")
        return None
    except Exception as e:
        print(f"❌ LightGBM模型创建失败: {e}")
        return None

# 自动初始化
force_cpu_only()
'''
    
    with open('cpu_only_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 纯CPU配置文件已创建: cpu_only_config.py")

def update_main_config():
    """更新主配置文件，确保所有GPU设置都改为CPU"""
    print("\n🔧 检查主配置文件...")
    
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有GPU配置
        gpu_patterns = [
            "device_type': 'gpu'",
            'device_type": "gpu"',
            "device_type = 'gpu'",
            'device_type = "gpu"',
            'CUDA_VISIBLE_DEVICES', '0'
        ]
        
        gpu_found = any(pattern in content for pattern in gpu_patterns[:4])
        
        if gpu_found:
            print("⚠️  发现GPU配置，但已在之前的步骤中修改为CPU")
        else:
            print("✅ 主配置文件已确认为CPU模式")
            
    except FileNotFoundError:
        print("❌ 未找到config.py文件")
    except Exception as e:
        print(f"❌ 检查配置文件失败: {e}")

def create_startup_script():
    """创建CPU专用启动脚本"""
    print("\n📝 创建CPU专用启动脚本...")
    
    startup_content = '''@echo off
REM CPU专用启动脚本 - 禁用所有GPU使用

echo 🚫 启动CPU专用模式...

REM 禁用GPU
set CUDA_VISIBLE_DEVICES=-1
set TF_FORCE_GPU_ALLOW_GROWTH=false

REM CPU优化环境变量
set OMP_NUM_THREADS=16
set MKL_NUM_THREADS=16
set NUMBA_NUM_THREADS=16
set OPENBLAS_NUM_THREADS=16

REM TensorFlow CPU优化
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=1
set TF_NUM_INTEROP_THREADS=4
set TF_NUM_INTRAOP_THREADS=16

echo ✅ CPU专用环境设置完成
echo 🖥️  AMD 16核处理器优化已启用
echo 🚫 GPU使用已完全禁用

REM 启动Python程序
python %*
'''
    
    with open('cpu_only_start.bat', 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ CPU专用启动脚本已创建: cpu_only_start.bat")

def verify_no_gpu_usage():
    """验证GPU使用已被禁用"""
    print("\n🔍 验证GPU禁用状态...")
    
    # 检查环境变量
    cuda_disabled = os.environ.get('CUDA_VISIBLE_DEVICES') == '-1'
    print(f"CUDA禁用状态: {'✅ 已禁用' if cuda_disabled else '❌ 未禁用'}")
    
    # 测试TensorFlow
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        tf_gpu_disabled = len(gpus) == 0
        print(f"TensorFlow GPU: {'✅ 已禁用' if tf_gpu_disabled else '❌ 仍可用'}")
    except ImportError:
        print("TensorFlow: 未安装")
    except Exception as e:
        print(f"TensorFlow检查失败: {e}")
    
    # 测试LightGBM
    try:
        from cpu_only_config import create_cpu_only_lightgbm_model
        model = create_cpu_only_lightgbm_model()
        lgb_cpu_only = model is not None
        print(f"LightGBM CPU模式: {'✅ 已配置' if lgb_cpu_only else '❌ 配置失败'}")
    except ImportError:
        print("LightGBM: 配置文件未创建")
    except Exception as e:
        print(f"LightGBM检查失败: {e}")

def main():
    """主函数"""
    print("🚫 彻底禁用项目GPU使用")
    print("=" * 50)
    
    # 禁用GPU环境
    disable_gpu_environment()
    
    # 创建CPU专用配置
    create_cpu_only_config()
    
    # 检查主配置
    update_main_config()
    
    # 创建启动脚本
    create_startup_script()
    
    # 验证禁用状态
    verify_no_gpu_usage()
    
    print(f"\n🎉 GPU禁用配置完成！")
    print(f"\n📋 使用说明:")
    print("1. 使用CPU专用启动: cpu_only_start.bat main.py")
    print("2. 在代码中导入: from cpu_only_config import force_cpu_only")
    print("3. 创建模型: from cpu_only_config import create_cpu_only_lightgbm_model")
    print("4. 你的AMD 16核处理器将发挥最大性能！")
    
    print(f"\n💡 重要提醒:")
    print("- 所有GPU配置已被禁用")
    print("- TensorFlow将只使用CPU + oneDNN优化")
    print("- LightGBM将只使用CPU + 16核并行")
    print("- 对于你的数据规模，这是最优配置！")

if __name__ == "__main__":
    main()
