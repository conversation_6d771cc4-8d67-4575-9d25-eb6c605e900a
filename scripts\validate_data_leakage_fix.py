#!/usr/bin/env python3
"""
数据泄露修复验证脚本

此脚本用于验证我们修复的数据泄露问题是否完整，
检查代码库中是否还有其他潜在的数据泄露点。
"""

import os
import re
import sys
from pathlib import Path

def find_potential_leakage_patterns():
    """查找潜在的数据泄露模式"""
    
    # 定义潜在的数据泄露模式
    leakage_patterns = {
        'global_statistics': [
            r'\.mean\(\)',
            r'\.median\(\)',
            r'\.std\(\)',
            r'\.var\(\)',
            r'\.quantile\(',
            r'\.describe\(\)',
        ],
        'backward_fill': [
            r'\.bfill\(\)',
            r'\.fillna\(.*method=[\'"]bfill[\'"]',
            r'\.fillna\(.*method=[\'"]backfill[\'"]',
        ],
        'future_data_usage': [
            r'use_historical_only\s*=\s*False',
            r'historical_only\s*=\s*False',
        ],
        'suspicious_feature_names': [
            r'[\'"].*future.*[\'"]',
            r'[\'"].*next.*[\'"]',
            r'[\'"].*ahead.*[\'"]',
            r'[\'"].*target.*[\'"]',
        ]
    }
    
    # 要检查的文件扩展名
    file_extensions = ['.py']
    
    # 要排除的目录
    exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
    
    # 要排除的文件
    exclude_files = {'validate_data_leakage_fix.py', 'data_leakage_fix_summary.md'}
    
    results = {}
    
    # 遍历项目目录
    project_root = Path(__file__).parent.parent
    
    for root, dirs, files in os.walk(project_root):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            # 检查文件扩展名
            if not any(file.endswith(ext) for ext in file_extensions):
                continue
                
            # 排除特定文件
            if file in exclude_files:
                continue
                
            file_path = Path(root) / file
            relative_path = file_path.relative_to(project_root)
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                # 检查每种模式
                for category, patterns in leakage_patterns.items():
                    for pattern in patterns:
                        matches = list(re.finditer(pattern, content, re.IGNORECASE))
                        if matches:
                            if str(relative_path) not in results:
                                results[str(relative_path)] = {}
                            if category not in results[str(relative_path)]:
                                results[str(relative_path)][category] = []
                                
                            for match in matches:
                                # 获取匹配行号
                                line_num = content[:match.start()].count('\n') + 1
                                line_content = content.split('\n')[line_num - 1].strip()
                                
                                results[str(relative_path)][category].append({
                                    'line': line_num,
                                    'pattern': pattern,
                                    'content': line_content,
                                    'match': match.group()
                                })
                                
            except Exception as e:
                print(f"警告：无法读取文件 {relative_path}: {e}")
                
    return results

def analyze_results(results):
    """分析结果并生成报告"""
    
    print("=" * 80)
    print("数据泄露修复验证报告")
    print("=" * 80)
    
    if not results:
        print("✅ 未发现潜在的数据泄露模式！")
        return True
        
    total_issues = 0
    critical_issues = 0
    
    for file_path, categories in results.items():
        print(f"\n📁 文件: {file_path}")
        print("-" * 60)
        
        for category, issues in categories.items():
            print(f"\n🔍 类别: {category}")
            
            for issue in issues:
                total_issues += 1
                
                # 判断是否为关键问题
                is_critical = False
                if category in ['backward_fill', 'future_data_usage']:
                    is_critical = True
                    critical_issues += 1
                elif category == 'global_statistics':
                    # 检查是否在安全的上下文中（如统计报告）
                    safe_contexts = ['logger', 'print', 'log', 'report', 'stats', 'summary']
                    if not any(ctx in issue['content'].lower() for ctx in safe_contexts):
                        is_critical = True
                        critical_issues += 1
                
                status = "🚨 关键" if is_critical else "⚠️  警告"
                print(f"  {status} 第{issue['line']}行: {issue['match']}")
                print(f"    代码: {issue['content']}")
                
    print("\n" + "=" * 80)
    print("总结")
    print("=" * 80)
    print(f"总问题数: {total_issues}")
    print(f"关键问题数: {critical_issues}")
    print(f"警告问题数: {total_issues - critical_issues}")
    
    if critical_issues == 0:
        print("\n✅ 未发现关键的数据泄露问题！")
        return True
    else:
        print(f"\n❌ 发现 {critical_issues} 个关键数据泄露问题需要修复！")
        return False

def main():
    """主函数"""
    print("开始验证数据泄露修复...")
    
    # 查找潜在问题
    results = find_potential_leakage_patterns()
    
    # 分析结果
    is_clean = analyze_results(results)
    
    # 返回适当的退出码
    sys.exit(0 if is_clean else 1)

if __name__ == "__main__":
    main()
