#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一特征工程接口

为基础模型和元模型的特征工程定义标准接口，确保特征名称和顺序一致，
并增加数据验证步骤，处理NaN/Inf值。

主要功能：
1. 统一的特征工程接口
2. 数据验证和清理
3. 特征名称标准化
4. 特征顺序保证
5. 配置驱动的特征生成
"""

import pandas as pd
import numpy as np
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Tuple, Any
import warnings
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

class FeatureEngineeringException(Exception):
    """特征工程异常类"""
    def __init__(self, message: str, feature_type: str = None, context: Dict = None):
        self.message = message
        self.feature_type = feature_type
        self.context = context or {}
        super().__init__(self.message)

class DataValidationResult:
    """数据验证结果类"""
    def __init__(self):
        self.is_valid = True
        self.warnings = []
        self.errors = []
        self.nan_count = 0
        self.inf_count = 0
        self.feature_count = 0
        self.sample_count = 0
    
    def add_warning(self, message: str):
        self.warnings.append(message)
    
    def add_error(self, message: str):
        self.errors.append(message)
        self.is_valid = False
    
    def summary(self) -> str:
        return f"Valid: {self.is_valid}, Features: {self.feature_count}, Samples: {self.sample_count}, NaN: {self.nan_count}, Inf: {self.inf_count}"

class BaseFeatureEngineer(ABC):
    """
    统一特征工程基类
    
    定义标准的特征工程接口，所有具体的特征工程器都应该继承此类
    """
    
    def __init__(self, config: Any = None, feature_config: Dict = None):
        """
        初始化特征工程器
        
        Args:
            config: 全局配置对象
            feature_config: 特征工程专用配置
        """
        self.config = config
        self.feature_config = feature_config or {}
        self.feature_names = []
        self.feature_order = []
        self.validation_enabled = True
        
        # 数据清理配置
        self.nan_fill_strategy = self.feature_config.get('nan_fill_strategy', 'median')
        self.inf_fill_strategy = self.feature_config.get('inf_fill_strategy', 'clip')
        self.outlier_clip_quantile = self.feature_config.get('outlier_clip_quantile', 0.99)
        
        logger.info(f"初始化 {self.__class__.__name__}")
    
    @abstractmethod
    def generate_features(self, 
                         data: Union[pd.DataFrame, Dict], 
                         target_config: Dict = None,
                         **kwargs) -> pd.DataFrame:
        """
        生成特征的抽象方法
        
        Args:
            data: 输入数据（DataFrame或字典）
            target_config: 目标配置
            **kwargs: 其他参数
            
        Returns:
            包含所有特征的DataFrame
        """
        pass
    
    def validate_and_clean_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, DataValidationResult]:
        """
        验证和清理数据
        
        Args:
            df: 输入DataFrame
            
        Returns:
            清理后的DataFrame和验证结果
        """
        if not self.validation_enabled:
            result = DataValidationResult()
            result.feature_count = len(df.columns)
            result.sample_count = len(df)
            return df, result
        
        logger.debug(f"开始数据验证和清理，输入形状: {df.shape}")
        
        result = DataValidationResult()
        result.feature_count = len(df.columns)
        result.sample_count = len(df)
        
        # 创建副本避免修改原数据
        df_clean = df.copy()
        
        # 1. 检查和处理NaN值
        nan_mask = df_clean.isnull()
        result.nan_count = nan_mask.sum().sum()
        
        if result.nan_count > 0:
            result.add_warning(f"发现 {result.nan_count} 个NaN值")
            df_clean = self._handle_nan_values(df_clean, nan_mask)
        
        # 2. 检查和处理Inf值
        inf_mask = np.isinf(df_clean.select_dtypes(include=[np.number]))
        result.inf_count = inf_mask.sum().sum()
        
        if result.inf_count > 0:
            result.add_warning(f"发现 {result.inf_count} 个Inf值")
            df_clean = self._handle_inf_values(df_clean, inf_mask)
        
        # 3. 数据类型验证
        numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) == 0:
            result.add_error("没有找到数值型特征")
        
        # 4. 特征名称标准化
        df_clean = self._standardize_feature_names(df_clean)
        
        # 5. 异常值处理
        if self.feature_config.get('enable_outlier_clipping', True):
            df_clean = self._clip_outliers(df_clean)
        
        logger.debug(f"数据验证完成: {result.summary()}")
        
        return df_clean, result
    
    def _handle_nan_values(self, df: pd.DataFrame, nan_mask: pd.DataFrame) -> pd.DataFrame:
        """处理NaN值 - 🚀 修复数据泄露：使用历史数据填充"""
        if self.nan_fill_strategy == 'median':
            # 🚀 修复数据泄露：使用ffill而不是全局中位数
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if nan_mask[col].any():
                    # 使用向前填充（历史数据）
                    df[col] = df[col].ffill()
                    # 对于开头的NaN，使用第一个有效值或0
                    first_valid = df[col].dropna()
                    default_val = first_valid.iloc[0] if len(first_valid) > 0 else 0.0
                    df[col].fillna(default_val, inplace=True)

        elif self.nan_fill_strategy == 'mean':
            # 🚀 修复数据泄露：使用ffill而不是全局均值
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if nan_mask[col].any():
                    # 使用向前填充（历史数据）
                    df[col] = df[col].ffill()
                    # 对于开头的NaN，使用第一个有效值或0
                    first_valid = df[col].dropna()
                    default_val = first_valid.iloc[0] if len(first_valid) > 0 else 0.0
                    df[col].fillna(default_val, inplace=True)

        elif self.nan_fill_strategy == 'zero':
            # 使用0填充（安全）
            df.fillna(0.0, inplace=True)
        
        elif self.nan_fill_strategy == 'forward':
            # 前向填充
            df.fillna(method='ffill', inplace=True)
            df.fillna(0.0, inplace=True)  # 处理开头的NaN
        
        return df
    
    def _handle_inf_values(self, df: pd.DataFrame, inf_mask: pd.DataFrame) -> pd.DataFrame:
        """处理Inf值"""
        if self.inf_fill_strategy == 'clip':
            # 将Inf值裁剪到合理范围
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col in inf_mask.columns and inf_mask[col].any():
                    # 计算非Inf值的分位数
                    finite_values = df[col][np.isfinite(df[col])]
                    if len(finite_values) > 0:
                        # 🚀 修复数据泄露：使用固定的异常值阈值而不是分位数
                        # 使用3倍标准差作为异常值检测阈值
                        mean_val = finite_values.iloc[0] if len(finite_values) > 0 else 0
                        std_val = 1.0  # 使用固定标准差
                        lower_bound = mean_val - 3 * std_val
                        upper_bound = mean_val + 3 * std_val
                        df[col] = np.clip(df[col], lower_bound, upper_bound)
                    else:
                        df[col] = 0.0
        
        elif self.inf_fill_strategy == 'zero':
            # 将Inf值替换为0
            df.replace([np.inf, -np.inf], 0.0, inplace=True)
        
        return df
    
    def _standardize_feature_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化特征名称"""
        # 移除特殊字符，统一命名格式
        new_columns = []
        for col in df.columns:
            # 移除空格和特殊字符，转换为小写
            new_col = str(col).strip().replace(' ', '_').replace('-', '_')
            new_col = ''.join(c for c in new_col if c.isalnum() or c == '_')
            new_columns.append(new_col)
        
        df.columns = new_columns
        return df
    
    def _clip_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """裁剪异常值"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            # 🚀 修复数据泄露：使用固定的异常值阈值而不是分位数
            # 使用3倍标准差作为异常值检测阈值
            first_valid = df[col].dropna()
            if len(first_valid) > 0:
                mean_val = first_valid.iloc[0]  # 使用第一个有效值作为基准
                std_val = 1.0  # 使用固定标准差
                lower_bound = mean_val - 3 * std_val
                upper_bound = mean_val + 3 * std_val
                df[col] = np.clip(df[col], lower_bound, upper_bound)
        
        return df
    
    def ensure_feature_consistency(self, 
                                 df: pd.DataFrame, 
                                 expected_features: List[str] = None) -> pd.DataFrame:
        """
        确保特征一致性
        
        Args:
            df: 输入DataFrame
            expected_features: 期望的特征列表
            
        Returns:
            特征一致的DataFrame
        """
        if expected_features is None:
            expected_features = self.feature_names
        
        if not expected_features:
            logger.warning("没有指定期望的特征列表")
            return df
        
        # 确保所有期望的特征都存在
        missing_features = set(expected_features) - set(df.columns)
        if missing_features:
            logger.warning(f"缺失特征: {missing_features}")
            for feature in missing_features:
                df[feature] = 0.0  # 用默认值填充缺失特征
        
        # 移除多余的特征
        extra_features = set(df.columns) - set(expected_features)
        if extra_features:
            logger.warning(f"移除多余特征: {extra_features}")
            df = df.drop(columns=list(extra_features))
        
        # 确保特征顺序一致
        df = df[expected_features]
        
        return df
    
    def get_feature_info(self) -> Dict:
        """获取特征信息"""
        return {
            'feature_count': len(self.feature_names),
            'feature_names': self.feature_names.copy(),
            'feature_order': self.feature_order.copy(),
            'config': self.feature_config.copy()
        }
    
    def set_feature_names(self, feature_names: List[str]):
        """设置特征名称列表"""
        self.feature_names = feature_names.copy()
        self.feature_order = feature_names.copy()
        logger.info(f"设置特征名称列表，共 {len(feature_names)} 个特征")


class BasicModelFeatureEngineer(BaseFeatureEngineer):
    """
    基础模型特征工程器

    整合现有的基础模型特征工程逻辑，提供统一的接口
    """

    def __init__(self, config: Any = None, feature_config: Dict = None):
        super().__init__(config, feature_config)

        # 基础模型特征配置
        self.enable_price_features = self.feature_config.get('enable_price_features', True)
        self.enable_volume_features = self.feature_config.get('enable_volume_features', True)
        self.enable_candle_features = self.feature_config.get('enable_candle_features', True)
        self.enable_technical_indicators = self.feature_config.get('enable_technical_indicators', True)
        self.enable_time_features = self.feature_config.get('enable_time_features', True)
        self.enable_fund_flow_features = self.feature_config.get('enable_fund_flow_features', True)
        self.enable_trend_features = self.feature_config.get('enable_trend_features', True)
        self.enable_interaction_features = self.feature_config.get('enable_interaction_features', True)
        self.enable_higher_order_features = self.feature_config.get('enable_higher_order_features', True)
        self.enable_market_state_features = self.feature_config.get('enable_market_state_features', True)

        logger.info("初始化基础模型特征工程器")

    def generate_features(self,
                         data: pd.DataFrame,
                         target_config: Dict = None,
                         **kwargs) -> pd.DataFrame:
        """
        生成基础模型特征

        Args:
            data: K线数据DataFrame
            target_config: 目标配置
            **kwargs: 其他参数

        Returns:
            包含所有特征的DataFrame
        """
        logger.info(f"开始生成基础模型特征，输入数据形状: {data.shape}")

        try:
            # 使用现有的特征生成函数
            from src.core import data_utils

            # 调用现有的特征生成函数
            features_df = data_utils.add_classification_features(data.copy(), target_config)

            if features_df is None or features_df.empty:
                raise FeatureEngineeringException(
                    "基础特征生成失败，返回为空",
                    "basic_features",
                    {"input_shape": data.shape, "target_config": target_config}
                )

            # 数据验证和清理
            features_df, validation_result = self.validate_and_clean_data(features_df)

            if not validation_result.is_valid:
                logger.error(f"特征验证失败: {validation_result.errors}")
                raise FeatureEngineeringException(
                    f"特征验证失败: {validation_result.errors}",
                    "validation",
                    {"validation_result": validation_result.summary()}
                )

            # 更新特征名称列表
            self.set_feature_names(list(features_df.columns))

            logger.info(f"基础模型特征生成完成，输出形状: {features_df.shape}")
            logger.info(f"验证结果: {validation_result.summary()}")

            return features_df

        except Exception as e:
            logger.error(f"基础模型特征生成失败: {str(e)}")
            raise FeatureEngineeringException(
                f"基础模型特征生成失败: {str(e)}",
                "generation",
                {"input_shape": data.shape, "error": str(e)}
            )

    def generate_features_vectorized(self,
                                   data: pd.DataFrame,
                                   model_types: List[str] = ['up', 'down'],
                                   **kwargs) -> Dict[str, pd.DataFrame]:
        """
        向量化生成多个模型的特征

        Args:
            data: K线数据DataFrame
            model_types: 模型类型列表
            **kwargs: 其他参数

        Returns:
            包含各模型特征的字典
        """
        logger.info(f"开始向量化生成特征，模型类型: {model_types}")

        try:
            # 使用现有的向量化特征引擎
            from src.core.vectorized_feature_engine import VectorizedFeatureEngine

            engine = VectorizedFeatureEngine(self.config)
            features_dict = engine.generate_features_vectorized(data, model_types)

            # 对每个模型的特征进行验证和清理
            cleaned_features_dict = {}
            for model_type, features_df in features_dict.items():
                cleaned_df, validation_result = self.validate_and_clean_data(features_df)

                if not validation_result.is_valid:
                    logger.warning(f"模型 {model_type} 特征验证有问题: {validation_result.warnings}")

                cleaned_features_dict[model_type] = cleaned_df
                logger.info(f"模型 {model_type} 特征: {cleaned_df.shape}, 验证: {validation_result.summary()}")

            return cleaned_features_dict

        except Exception as e:
            logger.error(f"向量化特征生成失败: {str(e)}")
            raise FeatureEngineeringException(
                f"向量化特征生成失败: {str(e)}",
                "vectorized_generation",
                {"model_types": model_types, "error": str(e)}
            )


class MetaModelFeatureEngineer(BaseFeatureEngineer):
    """
    元模型特征工程器

    整合现有的元模型特征工程逻辑，提供统一的接口
    """

    def __init__(self, config: Any = None, feature_config: Dict = None):
        super().__init__(config, feature_config)

        # 元模型特征配置
        self.enable_prob_diff = self.feature_config.get('enable_prob_diff', True)
        self.enable_prob_sum = self.feature_config.get('enable_prob_sum', True)
        self.enable_lag_features = self.feature_config.get('enable_lag_features', True)
        self.enable_change_features = self.feature_config.get('enable_change_features', True)
        self.enable_global_features = self.feature_config.get('enable_global_features', True)

        # 滞后和变化特征的期数
        self.lag_periods = self.feature_config.get('lag_periods', [1])
        self.change_periods = self.feature_config.get('change_periods', [1])

        logger.info("初始化元模型特征工程器")

    def generate_features(self,
                         data: Union[pd.DataFrame, Dict],
                         target_config: Dict = None,
                         base_models_for_meta: List[str] = None,
                         precomputed_global_features: pd.DataFrame = None,
                         **kwargs) -> pd.DataFrame:
        """
        生成元模型特征

        Args:
            data: 基础模型OOF预测概率DataFrame或字典
            target_config: 目标配置
            base_models_for_meta: 基础模型名称列表
            precomputed_global_features: 预计算的全局特征
            **kwargs: 其他参数

        Returns:
            包含所有特征的DataFrame
        """
        logger.info(f"开始生成元模型特征")

        try:
            # 如果输入是字典，转换为DataFrame
            if isinstance(data, dict):
                X_meta_df = pd.DataFrame(data)
            else:
                X_meta_df = data.copy()

            logger.info(f"输入数据形状: {X_meta_df.shape}")

            # 使用现有的元模型特征工程函数
            if hasattr(self, '_use_existing_implementation'):
                # 调用现有的实现
                enhanced_df = self._call_existing_meta_feature_engineering(
                    X_meta_df, base_models_for_meta, precomputed_global_features
                )
            else:
                # 使用新的实现
                enhanced_df = self._generate_meta_features_new(
                    X_meta_df, base_models_for_meta, precomputed_global_features
                )

            # 数据验证和清理
            enhanced_df, validation_result = self.validate_and_clean_data(enhanced_df)

            if not validation_result.is_valid:
                logger.error(f"元模型特征验证失败: {validation_result.errors}")
                raise FeatureEngineeringException(
                    f"元模型特征验证失败: {validation_result.errors}",
                    "meta_validation",
                    {"validation_result": validation_result.summary()}
                )

            # 更新特征名称列表
            self.set_feature_names(list(enhanced_df.columns))

            logger.info(f"元模型特征生成完成，输出形状: {enhanced_df.shape}")
            logger.info(f"验证结果: {validation_result.summary()}")

            return enhanced_df

        except Exception as e:
            logger.error(f"元模型特征生成失败: {str(e)}")
            raise FeatureEngineeringException(
                f"元模型特征生成失败: {str(e)}",
                "meta_generation",
                {"input_type": type(data).__name__, "error": str(e)}
            )

    def _call_existing_meta_feature_engineering(self,
                                              X_meta_df: pd.DataFrame,
                                              base_models_for_meta: List[str],
                                              precomputed_global_features: pd.DataFrame) -> pd.DataFrame:
        """调用现有的元模型特征工程实现"""
        try:
            # 导入现有的函数
            import main

            enhanced_df = main.add_meta_feature_engineering(
                X_meta_df,
                base_models_for_meta,
                precomputed_global_features
            )

            return enhanced_df

        except Exception as e:
            logger.error(f"调用现有元模型特征工程失败: {str(e)}")
            raise

    def generate_realtime_features(self,
                                 meta_input_data_dict: Dict,
                                 trained_feature_names: List[str],
                                 **kwargs) -> Dict:
        """
        生成实时元模型特征

        Args:
            meta_input_data_dict: 包含基础模型概率的字典
            trained_feature_names: 训练时使用的特征名列表
            **kwargs: 其他参数

        Returns:
            添加了特征工程的字典
        """
        logger.info("开始生成实时元模型特征")

        try:
            # 使用现有的实时特征工程函数
            from src.core.prediction import apply_realtime_meta_feature_engineering

            enhanced_data = apply_realtime_meta_feature_engineering(
                meta_input_data_dict,
                trained_feature_names
            )

            logger.info(f"实时元模型特征生成完成，特征数: {len(enhanced_data)}")

            return enhanced_data

        except Exception as e:
            logger.error(f"实时元模型特征生成失败: {str(e)}")
            raise FeatureEngineeringException(
                f"实时元模型特征生成失败: {str(e)}",
                "realtime_meta_generation",
                {"input_features": list(meta_input_data_dict.keys()), "error": str(e)}
            )


class FeatureEngineeringFactory:
    """
    特征工程工厂类

    提供统一的特征工程器创建接口
    """

    @staticmethod
    def create_basic_feature_engineer(config: Any = None,
                                    feature_config: Dict = None) -> BasicModelFeatureEngineer:
        """创建基础模型特征工程器"""
        return BasicModelFeatureEngineer(config, feature_config)

    @staticmethod
    def create_meta_feature_engineer(config: Any = None,
                                   feature_config: Dict = None) -> MetaModelFeatureEngineer:
        """创建元模型特征工程器"""
        return MetaModelFeatureEngineer(config, feature_config)

    @staticmethod
    def get_default_feature_config(model_type: str = 'basic') -> Dict:
        """获取默认特征配置"""
        if model_type == 'basic':
            return {
                'enable_price_features': True,
                'enable_volume_features': True,
                'enable_candle_features': True,
                'enable_technical_indicators': True,
                'enable_time_features': True,
                'enable_fund_flow_features': True,
                'enable_trend_features': True,
                'enable_interaction_features': True,
                'enable_higher_order_features': True,
                'enable_market_state_features': True,
                'nan_fill_strategy': 'median',
                'inf_fill_strategy': 'clip',
                'enable_outlier_clipping': True,
                'outlier_clip_quantile': 0.99
            }
        elif model_type == 'meta':
            return {
                'enable_prob_diff': True,
                'enable_prob_sum': True,
                'enable_lag_features': True,
                'enable_change_features': True,
                'enable_global_features': True,
                'lag_periods': [1],
                'change_periods': [1],
                'nan_fill_strategy': 'median',
                'inf_fill_strategy': 'clip',
                'enable_outlier_clipping': True,
                'outlier_clip_quantile': 0.99
            }
        else:
            return {}


def validate_feature_consistency(df1: pd.DataFrame,
                               df2: pd.DataFrame,
                               tolerance: float = 1e-6) -> Tuple[bool, List[str]]:
    """
    验证两个DataFrame的特征一致性

    Args:
        df1: 第一个DataFrame
        df2: 第二个DataFrame
        tolerance: 数值比较容差

    Returns:
        (是否一致, 差异列表)
    """
    differences = []

    # 检查列名
    if list(df1.columns) != list(df2.columns):
        differences.append(f"列名不一致: {set(df1.columns) ^ set(df2.columns)}")

    # 检查形状
    if df1.shape != df2.shape:
        differences.append(f"形状不一致: {df1.shape} vs {df2.shape}")

    # 检查数值差异（如果形状一致）
    if df1.shape == df2.shape and list(df1.columns) == list(df2.columns):
        common_cols = df1.select_dtypes(include=[np.number]).columns
        for col in common_cols:
            if not np.allclose(df1[col], df2[col], rtol=tolerance, atol=tolerance, equal_nan=True):
                differences.append(f"列 {col} 数值不一致")

    return len(differences) == 0, differences


def create_feature_engineering_pipeline(config: Any = None) -> Dict[str, BaseFeatureEngineer]:
    """
    创建完整的特征工程管道

    Args:
        config: 全局配置对象

    Returns:
        包含所有特征工程器的字典
    """
    factory = FeatureEngineeringFactory()

    # 获取配置
    basic_config = factory.get_default_feature_config('basic')
    meta_config = factory.get_default_feature_config('meta')

    # 从全局配置中更新特征配置
    if config and hasattr(config, 'FEATURE_ENGINEERING_CONFIG'):
        basic_config.update(getattr(config, 'BASIC_MODEL_FEATURE_CONFIG', {}))
        meta_config.update(getattr(config, 'META_MODEL_FEATURE_ENGINEERING_CONFIG', {}))

    pipeline = {
        'basic': factory.create_basic_feature_engineer(config, basic_config),
        'meta': factory.create_meta_feature_engineer(config, meta_config)
    }

    logger.info("创建特征工程管道完成")
    return pipeline


# 便捷函数
def generate_basic_features(data: pd.DataFrame,
                          config: Any = None,
                          target_config: Dict = None,
                          feature_config: Dict = None) -> pd.DataFrame:
    """
    便捷函数：生成基础模型特征

    Args:
        data: K线数据
        config: 全局配置
        target_config: 目标配置
        feature_config: 特征配置

    Returns:
        特征DataFrame
    """
    engineer = FeatureEngineeringFactory.create_basic_feature_engineer(config, feature_config)
    return engineer.generate_features(data, target_config)


def generate_meta_features(data: Union[pd.DataFrame, Dict],
                         config: Any = None,
                         base_models_for_meta: List[str] = None,
                         precomputed_global_features: pd.DataFrame = None,
                         feature_config: Dict = None) -> pd.DataFrame:
    """
    便捷函数：生成元模型特征

    Args:
        data: 基础模型预测数据
        config: 全局配置
        base_models_for_meta: 基础模型列表
        precomputed_global_features: 预计算的全局特征
        feature_config: 特征配置

    Returns:
        特征DataFrame
    """
    engineer = FeatureEngineeringFactory.create_meta_feature_engineer(config, feature_config)
    return engineer.generate_features(data, None, base_models_for_meta, precomputed_global_features)
