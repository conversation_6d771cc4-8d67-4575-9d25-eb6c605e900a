#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征注册表和工厂模式实现
集中管理所有特征名、默认值、依赖项和元数据
"""

import logging
from typing import Dict, List, Any, Optional, Union, Callable, NamedTuple
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class FeatureType(Enum):
    """特征类型枚举"""
    PRICE = "price"
    VOLUME = "volume"
    TECHNICAL = "technical"
    CANDLE = "candle"
    TIME = "time"
    FUND_FLOW = "fund_flow"
    TREND = "trend"
    MTFA = "mtfa"
    DERIVED = "derived"
    TARGET = "target"

class FeatureDataType(Enum):
    """特征数据类型枚举"""
    FLOAT = "float64"
    INT = "int64"
    BOOL = "bool"
    STRING = "object"

@dataclass
class FeatureMetadata:
    """特征元数据"""
    name: str
    feature_type: FeatureType
    data_type: FeatureDataType
    default_value: Any
    description: str = ""
    dependencies: List[str] = field(default_factory=list)
    config_params: List[str] = field(default_factory=list)
    is_dynamic: bool = False  # 是否依赖配置参数动态生成
    validation_func: Optional[Callable] = None
    
    def __post_init__(self):
        """后处理验证"""
        if self.is_dynamic and not self.config_params:
            logger.warning(f"动态特征 {self.name} 没有指定配置参数")

class FeatureNameBuilder:
    """特征名构建器 - 用于动态生成特征名"""
    
    @staticmethod
    def build_technical_indicator_name(indicator: str, period: int, **kwargs) -> str:
        """构建技术指标特征名"""
        if indicator.upper() == 'RSI':
            return f"RSI_{period}"
        elif indicator.upper() == 'HMA':
            return f"HMA_{period}"
        elif indicator.upper() == 'ATR':
            return f"ATRr_{period}"
        elif indicator.upper() == 'WILLR':
            return f"WILLR_{period}"
        elif indicator.upper() == 'CCI':
            constant = kwargs.get('constant', 0.015)
            return f"CCI_{period}_{constant}"
        else:
            return f"{indicator.upper()}_{period}"
    
    @staticmethod
    def build_price_change_name(period: int) -> str:
        """构建价格变化特征名"""
        return f"price_change_{period}p"
    
    @staticmethod
    def build_volume_change_name(period: int) -> str:
        """构建成交量变化特征名"""
        return f"volume_change_{period}p"
    
    @staticmethod
    def build_smooth_feature_name(base_name: str, period: int) -> str:
        """构建平滑特征名"""
        return f"{base_name}_smooth{period}p"
    
    @staticmethod
    def build_mtfa_feature_name(base_name: str, timeframe: str) -> str:
        """构建MTFA特征名"""
        return f"{base_name}_{timeframe}"

class FeatureRegistry:
    """特征注册表 - 集中管理所有特征的元数据"""

    def __init__(self):
        self._features: Dict[str, FeatureMetadata] = {}
        self._feature_groups: Dict[FeatureType, List[str]] = {}
        self._dynamic_builders: Dict[str, Callable] = {}
        self._config_cache: Dict[str, Dict[str, Any]] = {}  # 配置缓存
        self._initialize_base_features()
        self._initialize_dynamic_builders()
    
    def register_feature(self, metadata: FeatureMetadata) -> None:
        """注册特征"""
        self._features[metadata.name] = metadata
        
        # 按类型分组
        if metadata.feature_type not in self._feature_groups:
            self._feature_groups[metadata.feature_type] = []
        self._feature_groups[metadata.feature_type].append(metadata.name)
        
        logger.debug(f"注册特征: {metadata.name} ({metadata.feature_type.value})")
    
    def get_feature(self, name: str) -> Optional[FeatureMetadata]:
        """获取特征元数据"""
        return self._features.get(name)
    
    def get_features_by_type(self, feature_type: FeatureType) -> List[FeatureMetadata]:
        """按类型获取特征"""
        feature_names = self._feature_groups.get(feature_type, [])
        return [self._features[name] for name in feature_names if name in self._features]
    
    def get_all_features(self) -> Dict[str, FeatureMetadata]:
        """获取所有特征"""
        return self._features.copy()
    
    def generate_dynamic_features(self, config: Dict[str, Any]) -> Dict[str, FeatureMetadata]:
        """根据配置动态生成特征"""
        dynamic_features = {}
        
        # 技术指标特征
        dynamic_features.update(self._generate_technical_features(config))
        
        # 价格变化特征
        dynamic_features.update(self._generate_price_change_features(config))
        
        # 成交量特征
        dynamic_features.update(self._generate_volume_features(config))
        
        # 平滑特征
        dynamic_features.update(self._generate_smooth_features(config))
        
        # MTFA特征
        dynamic_features.update(self._generate_mtfa_features(config))
        
        return dynamic_features
    
    def get_feature_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取所有特征的默认值"""
        defaults = {}
        
        # 静态特征默认值
        for feature in self._features.values():
            if not feature.is_dynamic:
                defaults[feature.name] = feature.default_value
        
        # 动态特征默认值
        dynamic_features = self.generate_dynamic_features(config)
        for feature in dynamic_features.values():
            defaults[feature.name] = feature.default_value
        
        return defaults
    
    def get_feature_config_params(self, feature_group: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """🚀 获取特征组的配置参数"""
        config_params = {}

        if feature_group == 'price_change':
            config_params = {
                'enable_price_change': config.get('enable_price_change', False),
                'price_change_periods': config.get('price_change_periods', [1, 3, 5, 10])
            }
        elif feature_group == 'volume':
            config_params = {
                'enable_volume': config.get('enable_volume', False),
                'volume_avg_period': config.get('volume_avg_period', 20),
                'volume_change_periods': config.get('volume_change_periods', [1])
            }
        elif feature_group == 'candle':
            config_params = {
                'enable_candle': config.get('enable_candle', False),
                'enable_pattern_recognition': config.get('enable_pattern_recognition', True),
                'candle_smoothing_periods': config.get('candle_smoothing_periods', [3])
            }
        elif feature_group == 'technical':
            config_params = {
                'enable_ta': config.get('enable_ta', False),
                'rsi_period': config.get('rsi_period', 14),
                'hma_period': config.get('hma_period', 14),
                'atr_period': config.get('atr_period', 14),
                'willr_period': config.get('willr_period', 14),
                'cci_period': config.get('cci_period', 14),
                'cci_constant': config.get('cci_constant', 0.015)
            }
        elif feature_group == 'fund_flow':
            config_params = {
                'enable_fund_flow': config.get('enable_fund_flow', False),
                'fund_flow_ratio_smoothing_period': config.get('fund_flow_ratio_smoothing_period', 5)
            }
        elif feature_group == 'mtfa':
            config_params = {
                'enable_mtfa': config.get('enable_mtfa', False),
                'mtfa_timeframes': config.get('mtfa_timeframes', [])
            }

        return config_params

    def register_feature_names_for_group(self, feature_group: str, feature_names: List[str], config: Dict[str, Any]) -> None:
        """🚀 为特征组注册实际生成的特征名"""
        cache_key = f"{feature_group}_{hash(str(sorted(config.items())))}"
        self._config_cache[cache_key] = {
            'feature_names': feature_names,
            'config': config.copy()
        }
        logger.debug(f"注册特征组 {feature_group} 的 {len(feature_names)} 个特征名")

    def get_registered_feature_names_for_group(self, feature_group: str, config: Dict[str, Any]) -> List[str]:
        """🚀 获取特征组已注册的特征名"""
        cache_key = f"{feature_group}_{hash(str(sorted(config.items())))}"
        cached = self._config_cache.get(cache_key)
        if cached:
            return cached['feature_names']
        return []

    def validate_feature_names(self, df_columns: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
        """验证DataFrame中的特征名是否与注册表一致"""
        validation_result = {
            'missing_features': [],
            'unexpected_features': [],
            'validation_passed': True,
            'total_registered': 0,
            'total_found': 0
        }

        # 获取预期的特征名
        expected_features = set(self._features.keys())
        dynamic_features = self.generate_dynamic_features(config)
        expected_features.update(dynamic_features.keys())

        # 过滤掉基础列和目标列
        base_cols = {'open', 'high', 'low', 'close', 'volume', 'qav', 'n', 'tbbav', 'tbqav'}
        feature_columns = [col for col in df_columns
                          if col not in base_cols
                          and not col.startswith('target_')
                          and not col.endswith('_name')]

        validation_result['total_registered'] = len(expected_features)
        validation_result['total_found'] = len(feature_columns)

        # 检查缺失的特征
        missing = expected_features - set(feature_columns)
        validation_result['missing_features'] = list(missing)

        # 检查意外的特征
        unexpected = set(feature_columns) - expected_features
        validation_result['unexpected_features'] = list(unexpected)

        # 判断验证是否通过
        if missing or unexpected:
            validation_result['validation_passed'] = False

        return validation_result
    
    def _initialize_base_features(self):
        """初始化基础特征"""
        
        # 价格相关特征
        price_features = [
            FeatureMetadata("body_size", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "K线实体大小"),
            FeatureMetadata("candle_range", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "K线范围"),
            FeatureMetadata("upper_shadow", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "上影线长度"),
            FeatureMetadata("lower_shadow", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "下影线长度"),
            FeatureMetadata("close_pos_in_candle", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.5, "收盘价在K线中的位置"),
            FeatureMetadata("is_green_candle", FeatureType.CANDLE, FeatureDataType.BOOL, False, "是否为阳线"),
            FeatureMetadata("is_doji", FeatureType.CANDLE, FeatureDataType.BOOL, False, "是否为十字星"),
        ]
        
        # 标准化特征
        normalized_features = [
            FeatureMetadata("body_size_norm", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "标准化实体大小"),
            FeatureMetadata("upper_shadow_norm", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "标准化上影线"),
            FeatureMetadata("lower_shadow_norm", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "标准化下影线"),
            FeatureMetadata("candle_range_norm", FeatureType.CANDLE, FeatureDataType.FLOAT, 0.0, "标准化K线范围"),
        ]
        
        # 成交量特征
        volume_features = [
            FeatureMetadata("volume_vs_avg", FeatureType.VOLUME, FeatureDataType.FLOAT, 1.0, "成交量与平均值比率"),
        ]
        
        # 技术指标特征（固定参数）
        technical_features = [
            FeatureMetadata("MACD", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 0.0, "MACD值"),
            FeatureMetadata("MACD_histogram", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 0.0, "MACD柱状图"),
            FeatureMetadata("MACD_signal", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 0.0, "MACD信号线"),
            FeatureMetadata("STOCH_k", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 50.0, "随机指标K值"),
            FeatureMetadata("STOCH_d", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 50.0, "随机指标D值"),
            FeatureMetadata("KC_lower", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 0.0, "Keltner通道下轨"),
            FeatureMetadata("KC_middle", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 0.0, "Keltner通道中轨"),
            FeatureMetadata("KC_upper", FeatureType.TECHNICAL, FeatureDataType.FLOAT, 0.0, "Keltner通道上轨"),
        ]
        
        # 衍生特征
        derived_features = [
            FeatureMetadata("ema_distance_abs", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "EMA绝对距离"),
            FeatureMetadata("ema_distance_pct", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "EMA百分比距离"),
            FeatureMetadata("ema_bullish_strength", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "EMA多头强度"),
            FeatureMetadata("ema_bearish_strength", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "EMA空头强度"),
            FeatureMetadata("bb_upper_breakout_strength", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "布林带上轨突破强度"),
            FeatureMetadata("price_vs_bb_upper_pct", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "价格相对布林带上轨百分比"),
            FeatureMetadata("bb_lower_breakout_strength", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "布林带下轨突破强度"),
            FeatureMetadata("price_vs_bb_lower_pct", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "价格相对布林带下轨百分比"),
        ]
        
        # 时间框架敏感度特征
        timeframe_features = [
            FeatureMetadata("rsi_15m_vs_4h_diff", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "RSI 15m vs 4h差异"),
            FeatureMetadata("rsi_15m_vs_4h_ratio", FeatureType.DERIVED, FeatureDataType.FLOAT, 1.0, "RSI 15m vs 4h比率"),
            FeatureMetadata("close_pos_15m_vs_4h_diff", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "收盘位置15m vs 4h差异"),
            FeatureMetadata("close_pos_15m_vs_4h_deviation", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "收盘位置15m vs 4h偏差"),
            FeatureMetadata("macd_15m_vs_4h_diff", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "MACD 15m vs 4h差异"),
            FeatureMetadata("macd_15m_vs_4h_signal_agreement", FeatureType.DERIVED, FeatureDataType.INT, 1, "MACD 15m vs 4h信号一致性"),
            FeatureMetadata("volume_ratio_15m_vs_4h_diff", FeatureType.DERIVED, FeatureDataType.FLOAT, 0.0, "成交量比率15m vs 4h差异"),
            FeatureMetadata("volume_activity_15m_vs_4h_ratio", FeatureType.DERIVED, FeatureDataType.FLOAT, 1.0, "成交量活跃度15m vs 4h比率"),
        ]
        
        # 时间特征
        time_features = [
            FeatureMetadata("hour", FeatureType.TIME, FeatureDataType.INT, 0, "小时"),
            FeatureMetadata("day_of_week", FeatureType.TIME, FeatureDataType.INT, 0, "星期几"),
            FeatureMetadata("is_weekend", FeatureType.TIME, FeatureDataType.BOOL, False, "是否周末"),
            FeatureMetadata("hour_sin", FeatureType.TIME, FeatureDataType.FLOAT, 0.0, "小时正弦值"),
            FeatureMetadata("hour_cos", FeatureType.TIME, FeatureDataType.FLOAT, 1.0, "小时余弦值"),
            FeatureMetadata("day_sin", FeatureType.TIME, FeatureDataType.FLOAT, 0.0, "日期正弦值"),
            FeatureMetadata("day_cos", FeatureType.TIME, FeatureDataType.FLOAT, 1.0, "日期余弦值"),
        ]

        # 资金流特征
        fund_flow_features = [
            FeatureMetadata("fund_flow_indicator", FeatureType.FUND_FLOW, FeatureDataType.FLOAT, 0.0, "资金流指标"),
            FeatureMetadata("taker_buy_ratio", FeatureType.FUND_FLOW, FeatureDataType.FLOAT, 0.5, "买方比率"),
        ]

        # 趋势特征
        trend_features = [
            FeatureMetadata("trend_slope_period_1", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "趋势斜率1"),
            FeatureMetadata("trend_slope_period_2", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "趋势斜率2"),
            FeatureMetadata("trend_adx_signal", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "ADX趋势信号"),
            FeatureMetadata("trend_ema_signal", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "EMA趋势信号"),
            FeatureMetadata("adx_value", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "ADX值"),
            FeatureMetadata("adx_pdi", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "ADX正向指标"),
            FeatureMetadata("adx_mdi", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "ADX负向指标"),
            FeatureMetadata("ema_short", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "短期EMA"),
            FeatureMetadata("ema_long", FeatureType.TREND, FeatureDataType.FLOAT, 0.0, "长期EMA"),
        ]

        # 字符串特征
        string_features = [
            FeatureMetadata("candlestick_pattern_name", FeatureType.CANDLE, FeatureDataType.STRING, "Unknown", "K线形态名称"),
        ]

        # 注册所有基础特征
        all_base_features = (price_features + normalized_features + volume_features +
                           technical_features + derived_features + timeframe_features +
                           time_features + fund_flow_features + trend_features + string_features)

        for feature in all_base_features:
            self.register_feature(feature)
    
    def _initialize_dynamic_builders(self):
        """初始化动态特征构建器"""
        self._dynamic_builders = {
            'technical_indicators': self._generate_technical_features,
            'price_changes': self._generate_price_change_features,
            'volume_features': self._generate_volume_features,
            'smooth_features': self._generate_smooth_features,
            'mtfa_features': self._generate_mtfa_features,
        }
    
    def _generate_technical_features(self, config: Dict[str, Any]) -> Dict[str, FeatureMetadata]:
        """生成技术指标特征"""
        features = {}
        
        # RSI特征
        rsi_period = config.get('rsi_period', 14)
        if isinstance(rsi_period, int) and rsi_period > 0:
            name = FeatureNameBuilder.build_technical_indicator_name('RSI', rsi_period)
            features[name] = FeatureMetadata(
                name=name,
                feature_type=FeatureType.TECHNICAL,
                data_type=FeatureDataType.FLOAT,
                default_value=50.0,
                description=f"RSI指标 (周期: {rsi_period})",
                config_params=['rsi_period'],
                is_dynamic=True
            )
        
        # HMA特征
        hma_period = config.get('hma_period', 14)
        if isinstance(hma_period, int) and hma_period > 0:
            name = FeatureNameBuilder.build_technical_indicator_name('HMA', hma_period)
            features[name] = FeatureMetadata(
                name=name,
                feature_type=FeatureType.TECHNICAL,
                data_type=FeatureDataType.FLOAT,
                default_value=0.0,
                description=f"Hull移动平均 (周期: {hma_period})",
                config_params=['hma_period'],
                is_dynamic=True
            )
        
        # ATR特征
        atr_period = config.get('atr_period', 14)
        if isinstance(atr_period, int) and atr_period > 0:
            name = FeatureNameBuilder.build_technical_indicator_name('ATR', atr_period)
            features[name] = FeatureMetadata(
                name=name,
                feature_type=FeatureType.TECHNICAL,
                data_type=FeatureDataType.FLOAT,
                default_value=0.0,
                description=f"平均真实范围 (周期: {atr_period})",
                config_params=['atr_period'],
                is_dynamic=True
            )
        
        # Williams %R特征
        willr_period = config.get('willr_period', 14)
        if isinstance(willr_period, int) and willr_period > 0:
            name = FeatureNameBuilder.build_technical_indicator_name('WILLR', willr_period)
            features[name] = FeatureMetadata(
                name=name,
                feature_type=FeatureType.TECHNICAL,
                data_type=FeatureDataType.FLOAT,
                default_value=-50.0,
                description=f"Williams %R (周期: {willr_period})",
                config_params=['willr_period'],
                is_dynamic=True
            )
        
        # CCI特征
        cci_period = config.get('cci_period', 14)
        cci_constant = config.get('cci_constant', 0.015)
        if isinstance(cci_period, int) and cci_period > 0:
            name = FeatureNameBuilder.build_technical_indicator_name('CCI', cci_period, constant=cci_constant)
            features[name] = FeatureMetadata(
                name=name,
                feature_type=FeatureType.TECHNICAL,
                data_type=FeatureDataType.FLOAT,
                default_value=0.0,
                description=f"商品通道指数 (周期: {cci_period}, 常数: {cci_constant})",
                config_params=['cci_period', 'cci_constant'],
                is_dynamic=True
            )
        
        return features
    
    def _generate_price_change_features(self, config: Dict[str, Any]) -> Dict[str, FeatureMetadata]:
        """生成价格变化特征"""
        features = {}
        
        price_change_periods = config.get('price_change_periods', [1, 3, 5, 10])
        for period in price_change_periods:
            if isinstance(period, int) and period > 0:
                name = FeatureNameBuilder.build_price_change_name(period)
                features[name] = FeatureMetadata(
                    name=name,
                    feature_type=FeatureType.PRICE,
                    data_type=FeatureDataType.FLOAT,
                    default_value=0.0,
                    description=f"价格变化 (周期: {period})",
                    config_params=['price_change_periods'],
                    is_dynamic=True
                )
        
        return features
    
    def _generate_volume_features(self, config: Dict[str, Any]) -> Dict[str, FeatureMetadata]:
        """生成成交量特征"""
        features = {}
        
        # 成交量变化特征
        volume_change_periods = config.get('volume_change_periods', [1])
        for period in volume_change_periods:
            if isinstance(period, int) and period > 0:
                name = FeatureNameBuilder.build_volume_change_name(period)
                features[name] = FeatureMetadata(
                    name=name,
                    feature_type=FeatureType.VOLUME,
                    data_type=FeatureDataType.FLOAT,
                    default_value=0.0,
                    description=f"成交量变化 (周期: {period})",
                    config_params=['volume_change_periods'],
                    is_dynamic=True
                )
        
        # 资金流平滑特征
        fund_flow_smoothing_period = config.get('fund_flow_ratio_smoothing_period', 5)
        if isinstance(fund_flow_smoothing_period, int) and fund_flow_smoothing_period > 0:
            name = f"taker_buy_ratio_smooth{fund_flow_smoothing_period}p"
            features[name] = FeatureMetadata(
                name=name,
                feature_type=FeatureType.FUND_FLOW,
                data_type=FeatureDataType.FLOAT,
                default_value=0.5,
                description=f"平滑买方比率 (周期: {fund_flow_smoothing_period})",
                config_params=['fund_flow_ratio_smoothing_period'],
                is_dynamic=True
            )
        
        return features
    
    def _generate_smooth_features(self, config: Dict[str, Any]) -> Dict[str, FeatureMetadata]:
        """生成平滑特征"""
        features = {}
        
        # K线平滑特征
        smoothing_periods = config.get('candle_smoothing_periods', [3])
        base_features = ['upper_shadow', 'close_pos_in_candle', 'body_size']
        
        for period in smoothing_periods:
            if isinstance(period, int) and period > 0:
                for base_feat in base_features:
                    name = FeatureNameBuilder.build_smooth_feature_name(base_feat, period)
                    default_val = 0.5 if 'pos_in_candle' in base_feat else 0.0
                    features[name] = FeatureMetadata(
                        name=name,
                        feature_type=FeatureType.CANDLE,
                        data_type=FeatureDataType.FLOAT,
                        default_value=default_val,
                        description=f"平滑{base_feat} (周期: {period})",
                        config_params=['candle_smoothing_periods'],
                        is_dynamic=True
                    )
        
        return features
    
    def _generate_mtfa_features(self, config: Dict[str, Any]) -> Dict[str, FeatureMetadata]:
        """生成MTFA特征"""
        features = {}
        
        if not config.get('enable_mtfa', False):
            return features
        
        mtfa_timeframes = config.get('mtfa_timeframes', [])
        base_features = [
            'price_change_1p', 'price_change_2p', 'price_change_3p', 'price_change_5p', 'price_change_10p',
            'body_size', 'candle_range', 'upper_shadow', 'lower_shadow', 'close_pos_in_candle',
            'body_size_norm', 'upper_shadow_norm', 'lower_shadow_norm', 'candle_range_norm',
            'volume_vs_avg', 'volume_change_1p'
        ]
        
        for timeframe in mtfa_timeframes:
            if isinstance(timeframe, str):
                for base_feat in base_features:
                    name = FeatureNameBuilder.build_mtfa_feature_name(base_feat, timeframe)
                    default_val = 0.5 if 'pos_in_candle' in base_feat or 'vs_avg' in base_feat else 0.0
                    features[name] = FeatureMetadata(
                        name=name,
                        feature_type=FeatureType.MTFA,
                        data_type=FeatureDataType.FLOAT,
                        default_value=default_val,
                        description=f"MTFA {base_feat} (时间框架: {timeframe})",
                        config_params=['mtfa_timeframes'],
                        is_dynamic=True
                    )
        
        return features

# 全局特征注册表实例
feature_registry = FeatureRegistry()

def get_feature_registry() -> FeatureRegistry:
    """获取全局特征注册表实例"""
    return feature_registry
