#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 综合失败案例分析器 V1.0
专为统一综合日志系统设计的失败案例分析工具

核心功能：
- 基于新的多层存储结构进行分析
- 整合交易数据和预测上下文数据
- 提供深度失败模式识别
- 生成可操作的改进建议
- 支持实时和批量分析
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from collections import Counter, defaultdict
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import logging


class ComprehensiveFailureAnalyzer:
    """综合失败案例分析器 - 适配统一日志系统"""
    
    def __init__(self, comprehensive_logs_dir: str = "comprehensive_logs"):
        self.logs_dir = Path(comprehensive_logs_dir)
        self.logger = logging.getLogger(f"{__name__}.ComprehensiveFailureAnalyzer")
        
        # 各层数据目录
        self.trades_dir = self.logs_dir / "trades"
        self.contexts_dir = self.logs_dir / "contexts"
        self.analysis_dir = self.logs_dir / "analysis"
        
        # 确保分析目录存在
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"综合失败案例分析器初始化完成: {comprehensive_logs_dir}")
    
    def analyze_failure_patterns(self, days_back: int = 7) -> Dict[str, Any]:
        """
        分析失败模式
        
        Args:
            days_back: 回溯天数
        
        Returns:
            失败模式分析结果
        """
        try:
            # 加载数据
            trades_df = self._load_trades_data(days_back)
            contexts_df = self._load_contexts_data(days_back)
            
            if trades_df is None or trades_df.empty:
                self.logger.warning("没有找到交易数据")
                return {}
            
            # 筛选失败交易
            failed_trades = trades_df[trades_df['result'] == 'LOSS'].copy()
            
            if failed_trades.empty:
                self.logger.info("在指定期间内没有失败交易")
                return {'message': '没有失败交易数据'}
            
            # 执行各项分析
            analysis_results = {
                'analysis_period': {
                    'start_date': (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d'),
                    'end_date': datetime.now().strftime('%Y-%m-%d'),
                    'days_analyzed': days_back
                },
                'basic_statistics': self._analyze_basic_statistics(failed_trades, trades_df),
                'failure_patterns': self._analyze_failure_patterns_detailed(failed_trades),
                'market_conditions': self._analyze_market_conditions(failed_trades),
                'model_performance': self._analyze_model_performance(failed_trades),
                'temporal_patterns': self._analyze_temporal_patterns(failed_trades),
                'recommendations': []
            }
            
            # 如果有上下文数据，进行更深入的分析
            if contexts_df is not None and not contexts_df.empty:
                context_analysis = self._analyze_prediction_context(failed_trades, contexts_df)
                analysis_results['context_analysis'] = context_analysis
            
            # 生成改进建议
            analysis_results['recommendations'] = self._generate_recommendations(analysis_results)
            
            # 保存分析结果
            self._save_analysis_results(analysis_results)
            
            # 打印分析报告
            self._print_analysis_report(analysis_results)
            
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"失败模式分析出错: {e}")
            return {'error': str(e)}
    
    def _load_trades_data(self, days_back: int) -> Optional[pd.DataFrame]:
        """加载交易数据"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            all_trades = []
            
            # 遍历所有交易文件
            for year_dir in self.trades_dir.glob("*"):
                if not year_dir.is_dir():
                    continue
                
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue
                    
                    for csv_file in month_dir.glob("trades_*.csv"):
                        try:
                            df = pd.read_csv(csv_file)
                            if not df.empty:
                                # 转换时间戳
                                df['entry_timestamp'] = pd.to_datetime(df['entry_timestamp'])
                                # 筛选时间范围
                                df = df[df['entry_timestamp'] >= start_date]
                                if not df.empty:
                                    all_trades.append(df)
                        except Exception as e:
                            self.logger.error(f"读取交易文件失败 {csv_file}: {e}")
            
            if not all_trades:
                return None
            
            combined_df = pd.concat(all_trades, ignore_index=True)
            self.logger.info(f"加载了 {len(combined_df)} 条交易记录")
            return combined_df
            
        except Exception as e:
            self.logger.error(f"加载交易数据失败: {e}")
            return None
    
    def _load_contexts_data(self, days_back: int) -> Optional[pd.DataFrame]:
        """加载预测上下文数据"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            all_contexts = []
            
            # 遍历所有上下文文件
            for year_dir in self.contexts_dir.glob("*"):
                if not year_dir.is_dir():
                    continue
                
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue
                    
                    for csv_file in month_dir.glob("contexts_*.csv"):
                        try:
                            df = pd.read_csv(csv_file)
                            if not df.empty:
                                # 转换时间戳
                                df['timestamp'] = pd.to_datetime(df['timestamp'])
                                # 筛选时间范围
                                df = df[df['timestamp'] >= start_date]
                                if not df.empty:
                                    all_contexts.append(df)
                        except Exception as e:
                            self.logger.error(f"读取上下文文件失败 {csv_file}: {e}")
            
            if not all_contexts:
                return None
            
            combined_df = pd.concat(all_contexts, ignore_index=True)
            self.logger.info(f"加载了 {len(combined_df)} 条上下文记录")
            return combined_df
            
        except Exception as e:
            self.logger.error(f"加载上下文数据失败: {e}")
            return None
    
    def _analyze_basic_statistics(self, failed_trades: pd.DataFrame, all_trades: pd.DataFrame) -> Dict[str, Any]:
        """分析基础统计信息"""
        total_trades = len(all_trades)
        failed_count = len(failed_trades)
        
        return {
            'total_trades': total_trades,
            'failed_trades': failed_count,
            'success_trades': total_trades - failed_count,
            'failure_rate': round(failed_count / total_trades * 100, 2) if total_trades > 0 else 0,
            'total_loss_amount': round(failed_trades['profit_loss'].sum(), 2),
            'avg_loss_per_trade': round(failed_trades['profit_loss'].mean(), 2),
            'max_single_loss': round(failed_trades['profit_loss'].min(), 2),
            'loss_std_dev': round(failed_trades['profit_loss'].std(), 2)
        }
    
    def _analyze_failure_patterns_detailed(self, failed_trades: pd.DataFrame) -> Dict[str, Any]:
        """详细分析失败模式"""
        patterns = {}
        
        # 按目标策略分析
        if 'target_name' in failed_trades.columns:
            target_failures = failed_trades['target_name'].value_counts()
            patterns['by_target'] = target_failures.to_dict()
        
        # 按交易方向分析
        if 'direction' in failed_trades.columns:
            direction_failures = failed_trades['direction'].value_counts()
            patterns['by_direction'] = direction_failures.to_dict()
        
        # 按交易对分析
        if 'symbol' in failed_trades.columns:
            symbol_failures = failed_trades['symbol'].value_counts()
            patterns['by_symbol'] = symbol_failures.to_dict()
        
        # 按平仓原因分析
        if 'exit_reason' in failed_trades.columns:
            exit_reason_failures = failed_trades['exit_reason'].value_counts()
            patterns['by_exit_reason'] = exit_reason_failures.to_dict()
        
        return patterns
    
    def _analyze_market_conditions(self, failed_trades: pd.DataFrame) -> Dict[str, Any]:
        """分析失败交易的市场条件"""
        market_analysis = {}
        
        # ATR波动率分析
        if 'entry_atr_percent' in failed_trades.columns:
            atr_data = failed_trades['entry_atr_percent'].dropna()
            if not atr_data.empty:
                market_analysis['atr_analysis'] = {
                    'avg_atr': round(atr_data.mean(), 3),
                    'median_atr': round(atr_data.median(), 3),
                    'high_volatility_failures': len(atr_data[atr_data > 3.0]),
                    'low_volatility_failures': len(atr_data[atr_data < 1.0])
                }
        
        # ADX趋势强度分析
        if 'entry_adx_value' in failed_trades.columns:
            adx_data = failed_trades['entry_adx_value'].dropna()
            if not adx_data.empty:
                market_analysis['adx_analysis'] = {
                    'avg_adx': round(adx_data.mean(), 2),
                    'strong_trend_failures': len(adx_data[adx_data > 25]),
                    'weak_trend_failures': len(adx_data[adx_data < 20])
                }
        
        # 市场状态分析
        if 'entry_market_regime' in failed_trades.columns:
            regime_failures = failed_trades['entry_market_regime'].value_counts()
            market_analysis['regime_analysis'] = regime_failures.to_dict()
        
        return market_analysis
    
    def _analyze_model_performance(self, failed_trades: pd.DataFrame) -> Dict[str, Any]:
        """分析模型性能"""
        model_analysis = {}
        
        # 信号概率分析
        prob_columns = ['entry_signal_probability', 'entry_neutral_probability', 'entry_opposite_probability']
        for col in prob_columns:
            if col in failed_trades.columns:
                prob_data = failed_trades[col].dropna()
                if not prob_data.empty:
                    model_analysis[col] = {
                        'avg': round(prob_data.mean(), 3),
                        'median': round(prob_data.median(), 3),
                        'std': round(prob_data.std(), 3)
                    }
        
        # 决策优势分析
        if 'direction_advantage' in failed_trades.columns:
            advantage_data = failed_trades['direction_advantage'].dropna()
            if not advantage_data.empty:
                model_analysis['direction_advantage'] = {
                    'avg': round(advantage_data.mean(), 3),
                    'weak_advantage_failures': len(advantage_data[advantage_data < 0.1])
                }
        
        return model_analysis
    
    def _analyze_temporal_patterns(self, failed_trades: pd.DataFrame) -> Dict[str, Any]:
        """分析时间模式"""
        temporal_analysis = {}
        
        if 'entry_timestamp' in failed_trades.columns:
            failed_trades['hour'] = pd.to_datetime(failed_trades['entry_timestamp']).dt.hour
            failed_trades['day_of_week'] = pd.to_datetime(failed_trades['entry_timestamp']).dt.dayofweek
            
            # 按小时分析
            hourly_failures = failed_trades['hour'].value_counts().sort_index()
            temporal_analysis['hourly_pattern'] = hourly_failures.to_dict()
            
            # 按星期分析
            daily_failures = failed_trades['day_of_week'].value_counts().sort_index()
            day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            temporal_analysis['daily_pattern'] = {
                day_names[day]: count for day, count in daily_failures.items()
            }
        
        return temporal_analysis

    def _analyze_prediction_context(self, failed_trades: pd.DataFrame, contexts_df: pd.DataFrame) -> Dict[str, Any]:
        """分析预测上下文与失败交易的关联"""
        context_analysis = {}

        try:
            # 尝试通过时间窗口匹配交易和上下文
            failed_trades['entry_timestamp'] = pd.to_datetime(failed_trades['entry_timestamp'])
            contexts_df['timestamp'] = pd.to_datetime(contexts_df['timestamp'])

            # 为每个失败交易找到最近的预测上下文
            matched_contexts = []
            for _, trade in failed_trades.iterrows():
                trade_time = trade['entry_timestamp']
                target_name = trade.get('target_name', '')

                # 在交易前5分钟内查找匹配的上下文
                time_window = contexts_df[
                    (contexts_df['timestamp'] >= trade_time - pd.Timedelta(minutes=5)) &
                    (contexts_df['timestamp'] <= trade_time) &
                    (contexts_df['target_name'] == target_name)
                ]

                if not time_window.empty:
                    # 选择时间最接近的上下文
                    closest_context = time_window.loc[time_window['timestamp'].idxmax()]
                    matched_contexts.append(closest_context)

            if matched_contexts:
                matched_df = pd.DataFrame(matched_contexts)

                # 分析特征重要性
                if 'feature_names_json' in matched_df.columns and 'feature_values_json' in matched_df.columns:
                    context_analysis['feature_analysis'] = self._analyze_feature_patterns(matched_df)

                # 分析信号强度
                if 'signal_strength' in matched_df.columns:
                    signal_strength = matched_df['signal_strength'].dropna()
                    if not signal_strength.empty:
                        context_analysis['signal_strength_analysis'] = {
                            'avg_signal_strength': round(signal_strength.mean(), 3),
                            'weak_signal_failures': len(signal_strength[signal_strength < 0.6])
                        }

                # 分析过滤原因
                if 'filter_reasons' in matched_df.columns:
                    filter_reasons = matched_df['filter_reasons'].dropna()
                    if not filter_reasons.empty:
                        filter_counter = Counter()
                        for reasons in filter_reasons:
                            if reasons and reasons != '':
                                filter_counter.update([reasons])
                        context_analysis['filter_reasons_analysis'] = dict(filter_counter.most_common(10))

        except Exception as e:
            self.logger.error(f"预测上下文分析失败: {e}")
            context_analysis['error'] = str(e)

        return context_analysis

    def _analyze_feature_patterns(self, contexts_df: pd.DataFrame) -> Dict[str, Any]:
        """分析特征模式"""
        feature_analysis = {}

        try:
            all_features = defaultdict(list)

            for _, row in contexts_df.iterrows():
                try:
                    feature_names = json.loads(row['feature_names_json'])
                    feature_values = json.loads(row['feature_values_json'])

                    if len(feature_names) == len(feature_values):
                        for name, value in zip(feature_names, feature_values):
                            if isinstance(value, (int, float)) and not np.isnan(value):
                                all_features[name].append(value)
                except (json.JSONDecodeError, TypeError):
                    continue

            # 计算每个特征的统计信息
            feature_stats = {}
            for feature_name, values in all_features.items():
                if len(values) > 0:
                    feature_stats[feature_name] = {
                        'count': len(values),
                        'mean': round(np.mean(values), 4),
                        'std': round(np.std(values), 4),
                        'min': round(np.min(values), 4),
                        'max': round(np.max(values), 4)
                    }

            # 找出最重要的特征（基于标准差）
            if feature_stats:
                sorted_features = sorted(
                    feature_stats.items(),
                    key=lambda x: x[1]['std'],
                    reverse=True
                )
                feature_analysis['top_variable_features'] = dict(sorted_features[:10])
                feature_analysis['total_features_analyzed'] = len(feature_stats)

        except Exception as e:
            self.logger.error(f"特征模式分析失败: {e}")
            feature_analysis['error'] = str(e)

        return feature_analysis

    def _generate_recommendations(self, analysis_results: Dict[str, Any]) -> List[str]:
        """基于分析结果生成改进建议"""
        recommendations = []

        # 基于失败率的建议
        basic_stats = analysis_results.get('basic_statistics', {})
        failure_rate = basic_stats.get('failure_rate', 0)

        if failure_rate > 60:
            recommendations.append("失败率过高(>60%)，建议重新评估模型参数和信号阈值")
        elif failure_rate > 40:
            recommendations.append("失败率偏高(>40%)，建议优化过滤条件和风险管理")

        # 基于失败模式的建议
        failure_patterns = analysis_results.get('failure_patterns', {})

        # 目标策略建议
        target_failures = failure_patterns.get('by_target', {})
        if target_failures:
            worst_target = max(target_failures, key=target_failures.get)
            recommendations.append(f"策略 '{worst_target}' 失败次数最多，建议重点优化")

        # 方向偏向建议
        direction_failures = failure_patterns.get('by_direction', {})
        if len(direction_failures) >= 2:
            directions = list(direction_failures.keys())
            if abs(direction_failures.get(directions[0], 0) - direction_failures.get(directions[1], 0)) > 5:
                recommendations.append("交易方向失败分布不均，建议检查方向预测逻辑")

        # 市场条件建议
        market_conditions = analysis_results.get('market_conditions', {})
        atr_analysis = market_conditions.get('atr_analysis', {})

        if atr_analysis.get('high_volatility_failures', 0) > atr_analysis.get('low_volatility_failures', 0):
            recommendations.append("高波动率环境下失败较多，建议加强波动率过滤")

        # 模型性能建议
        model_performance = analysis_results.get('model_performance', {})
        direction_advantage = model_performance.get('direction_advantage', {})

        if direction_advantage.get('weak_advantage_failures', 0) > 0:
            recommendations.append("弱优势信号失败较多，建议提高方向优势阈值")

        # 时间模式建议
        temporal_patterns = analysis_results.get('temporal_patterns', {})
        hourly_pattern = temporal_patterns.get('hourly_pattern', {})

        if hourly_pattern:
            worst_hours = sorted(hourly_pattern.items(), key=lambda x: x[1], reverse=True)[:3]
            worst_hour_list = [str(hour) for hour, _ in worst_hours]
            recommendations.append(f"时段 {', '.join(worst_hour_list)} 失败较多，建议避开这些时段或加强过滤")

        # 上下文分析建议
        context_analysis = analysis_results.get('context_analysis', {})
        signal_strength_analysis = context_analysis.get('signal_strength_analysis', {})

        if signal_strength_analysis.get('weak_signal_failures', 0) > 0:
            recommendations.append("弱信号强度导致较多失败，建议提高信号强度阈值")

        # 如果没有生成任何建议，提供通用建议
        if not recommendations:
            recommendations.extend([
                "建议增加更多过滤条件以提高信号质量",
                "考虑调整仓位管理策略以降低单笔损失",
                "定期回测和优化模型参数"
            ])

        return recommendations

    def _save_analysis_results(self, analysis_results: Dict[str, Any]):
        """保存分析结果到文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"failure_analysis_{timestamp}.json"
            filepath = self.analysis_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"分析结果已保存: {filepath}")

        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")

    def _print_analysis_report(self, analysis_results: Dict[str, Any]):
        """打印分析报告"""
        print(f"\n{'='*80}")
        print(f"📊 综合失败案例分析报告")
        print(f"{'='*80}")

        # 基础统计
        basic_stats = analysis_results.get('basic_statistics', {})
        print(f"\n📈 基础统计:")
        print(f"  总交易数: {basic_stats.get('total_trades', 0)}")
        print(f"  失败交易数: {basic_stats.get('failed_trades', 0)}")
        print(f"  失败率: {basic_stats.get('failure_rate', 0):.2f}%")
        print(f"  总损失金额: {basic_stats.get('total_loss_amount', 0):.2f}")
        print(f"  平均单笔损失: {basic_stats.get('avg_loss_per_trade', 0):.2f}")

        # 失败模式
        failure_patterns = analysis_results.get('failure_patterns', {})
        if failure_patterns:
            print(f"\n🔍 失败模式:")

            by_target = failure_patterns.get('by_target', {})
            if by_target:
                print(f"  按策略分布: {dict(list(by_target.items())[:5])}")

            by_direction = failure_patterns.get('by_direction', {})
            if by_direction:
                print(f"  按方向分布: {by_direction}")

        # 改进建议
        recommendations = analysis_results.get('recommendations', [])
        if recommendations:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(recommendations[:5], 1):
                print(f"  {i}. {rec}")

        print(f"\n{'='*80}")

    def generate_comprehensive_report(self, days_back: int = 7) -> Dict[str, Any]:
        """生成综合分析报告"""
        self.logger.info(f"开始生成 {days_back} 天的综合失败案例分析报告")

        analysis_results = self.analyze_failure_patterns(days_back)

        if analysis_results and 'error' not in analysis_results:
            self.logger.info("综合分析报告生成完成")
        else:
            self.logger.error("综合分析报告生成失败")

        return analysis_results
