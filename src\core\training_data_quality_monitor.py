#!/usr/bin/env python3
"""
训练数据质量监控模块
对训练数据进行全面的"体检"，确保模型训练的数据质量
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, Any, Tuple, List, Optional
from scipy.stats import pearsonr
import warnings

logger = logging.getLogger(__name__)

class TrainingDataQualityMonitor:
    """训练数据质量监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化质量监控器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.quality_report = {}
        
        # 默认阈值配置
        self.default_thresholds = {
            'min_positive_ratio': 0.01,      # 最小正样本比例 1%
            'max_positive_ratio': 0.30,      # 最大正样本比例 30%
            'min_feature_variance': 1e-10,   # 最小特征方差
            'max_correlation': 0.98,         # 最大特征相关性
            'max_nan_ratio': 0.1,            # 最大NaN比例 10%
            'min_samples': 100,              # 最小样本数
        }
        
        # 合并用户配置
        self.thresholds = {**self.default_thresholds, **self.config.get('quality_thresholds', {})}
    
    def comprehensive_quality_check(self, 
                                  X_train: pd.DataFrame, 
                                  y_train: pd.Series, 
                                  target_name: str) -> Dict[str, Any]:
        """
        对训练数据进行全面质量检查
        
        Args:
            X_train: 训练特征数据
            y_train: 训练目标数据
            target_name: 目标名称
            
        Returns:
            质量报告字典
        """
        logger.info(f"开始对 {target_name} 进行训练数据质量检查...")
        
        self.quality_report = {
            'target_name': target_name,
            'data_shape': X_train.shape,
            'timestamp': pd.Timestamp.now(),
            'checks': {},
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        # 1. 样本数量检查
        self._check_sample_size(X_train, y_train)
        
        # 2. 正负样本比例检查
        self._check_label_distribution(y_train)
        
        # 3. 特征稳定性检查
        self._check_feature_stability(X_train)
        
        # 4. 特征相关性检查
        self._check_feature_correlation(X_train)
        
        # 5. 缺失值检查
        self._check_missing_values(X_train, y_train)
        
        # 6. 数据类型检查
        self._check_data_types(X_train, y_train)
        
        # 7. 异常值检查
        self._check_outliers(X_train)
        
        # 生成总体评估
        self._generate_overall_assessment()
        
        # 打印质量报告
        self._print_quality_report()
        
        return self.quality_report
    
    def _check_sample_size(self, X_train: pd.DataFrame, y_train: pd.Series):
        """检查样本数量"""
        n_samples = len(X_train)
        min_samples = self.thresholds['min_samples']
        
        check_result = {
            'n_samples': n_samples,
            'min_required': min_samples,
            'status': 'pass' if n_samples >= min_samples else 'fail'
        }
        
        if n_samples < min_samples:
            error_msg = f"样本数量不足: {n_samples} < {min_samples}"
            self.quality_report['errors'].append(error_msg)
            logger.error(f"[{self.quality_report['target_name']}] {error_msg}")
        
        self.quality_report['checks']['sample_size'] = check_result
    
    def _check_label_distribution(self, y_train: pd.Series):
        """检查正负样本比例"""
        value_counts = y_train.value_counts()
        total_samples = len(y_train)
        
        if len(value_counts) < 2:
            error_msg = "标签只有一个类别，无法进行二分类训练"
            self.quality_report['errors'].append(error_msg)
            logger.error(f"[{self.quality_report['target_name']}] {error_msg}")
            return
        
        # 假设正样本是1，负样本是0
        positive_count = value_counts.get(1, 0)
        negative_count = value_counts.get(0, 0)
        positive_ratio = positive_count / total_samples
        
        check_result = {
            'positive_count': positive_count,
            'negative_count': negative_count,
            'positive_ratio': positive_ratio,
            'negative_ratio': 1 - positive_ratio,
            'status': 'pass'
        }
        
        # 检查正样本比例是否在合理范围内
        if positive_ratio < self.thresholds['min_positive_ratio']:
            warning_msg = f"正样本比例过低: {positive_ratio:.3f} < {self.thresholds['min_positive_ratio']}"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("考虑调整target_threshold或使用SMOTE等过采样技术")
            check_result['status'] = 'warning'
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")
        
        elif positive_ratio > self.thresholds['max_positive_ratio']:
            warning_msg = f"正样本比例过高: {positive_ratio:.3f} > {self.thresholds['max_positive_ratio']}"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("考虑调整target_threshold以降低正样本比例")
            check_result['status'] = 'warning'
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")
        
        self.quality_report['checks']['label_distribution'] = check_result
    
    def _check_feature_stability(self, X_train: pd.DataFrame):
        """检查特征稳定性（方差）"""
        feature_variances = X_train.var()
        min_variance = self.thresholds['min_feature_variance']
        
        constant_features = feature_variances[feature_variances < min_variance].index.tolist()
        
        check_result = {
            'total_features': len(X_train.columns),
            'constant_features': constant_features,
            'constant_count': len(constant_features),
            'min_variance_threshold': min_variance,
            'status': 'pass' if len(constant_features) == 0 else 'warning'
        }
        
        if constant_features:
            warning_msg = f"发现 {len(constant_features)} 个近似常数特征: {constant_features[:5]}{'...' if len(constant_features) > 5 else ''}"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("建议移除这些常数特征以提高训练效率")
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")
        
        self.quality_report['checks']['feature_stability'] = check_result
    
    def _check_feature_correlation(self, X_train: pd.DataFrame):
        """检查特征相关性"""
        if len(X_train.columns) < 2:
            return
        
        # 计算相关性矩阵
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            corr_matrix = X_train.corr()
        
        # 找出高度相关的特征对
        high_corr_pairs = []
        max_corr_threshold = self.thresholds['max_correlation']
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_value = abs(corr_matrix.iloc[i, j])
                if not np.isnan(corr_value) and corr_value > max_corr_threshold:
                    high_corr_pairs.append({
                        'feature1': corr_matrix.columns[i],
                        'feature2': corr_matrix.columns[j],
                        'correlation': corr_value
                    })
        
        check_result = {
            'total_feature_pairs': len(X_train.columns) * (len(X_train.columns) - 1) // 2,
            'high_correlation_pairs': high_corr_pairs,
            'high_correlation_count': len(high_corr_pairs),
            'max_correlation_threshold': max_corr_threshold,
            'status': 'pass' if len(high_corr_pairs) == 0 else 'warning'
        }
        
        if high_corr_pairs:
            warning_msg = f"发现 {len(high_corr_pairs)} 对高度相关的特征 (相关性 > {max_corr_threshold})"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("考虑移除其中一个特征以减少多重共线性")
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")
        
        self.quality_report['checks']['feature_correlation'] = check_result

    def _check_missing_values(self, X_train: pd.DataFrame, y_train: pd.Series):
        """检查缺失值"""
        # 检查特征缺失值
        feature_nan_ratios = X_train.isnull().sum() / len(X_train)
        high_nan_features = feature_nan_ratios[feature_nan_ratios > self.thresholds['max_nan_ratio']].index.tolist()

        # 检查目标变量缺失值
        target_nan_count = y_train.isnull().sum()
        target_nan_ratio = target_nan_count / len(y_train)

        check_result = {
            'feature_nan_ratios': feature_nan_ratios.to_dict(),
            'high_nan_features': high_nan_features,
            'high_nan_count': len(high_nan_features),
            'target_nan_count': target_nan_count,
            'target_nan_ratio': target_nan_ratio,
            'max_nan_threshold': self.thresholds['max_nan_ratio'],
            'status': 'pass'
        }

        if high_nan_features:
            warning_msg = f"发现 {len(high_nan_features)} 个高缺失率特征: {high_nan_features[:3]}{'...' if len(high_nan_features) > 3 else ''}"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("考虑移除高缺失率特征或使用插值方法")
            check_result['status'] = 'warning'
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")

        if target_nan_count > 0:
            error_msg = f"目标变量存在 {target_nan_count} 个缺失值"
            self.quality_report['errors'].append(error_msg)
            check_result['status'] = 'fail'
            logger.error(f"[{self.quality_report['target_name']}] {error_msg}")

        self.quality_report['checks']['missing_values'] = check_result

    def _check_data_types(self, X_train: pd.DataFrame, y_train: pd.Series):
        """检查数据类型"""
        # 检查特征数据类型
        numeric_features = X_train.select_dtypes(include=[np.number]).columns.tolist()
        non_numeric_features = X_train.select_dtypes(exclude=[np.number]).columns.tolist()

        # 检查目标变量类型
        target_is_numeric = pd.api.types.is_numeric_dtype(y_train)

        check_result = {
            'total_features': len(X_train.columns),
            'numeric_features': len(numeric_features),
            'non_numeric_features': len(non_numeric_features),
            'non_numeric_feature_names': non_numeric_features,
            'target_is_numeric': target_is_numeric,
            'target_dtype': str(y_train.dtype),
            'status': 'pass'
        }

        if non_numeric_features:
            warning_msg = f"发现 {len(non_numeric_features)} 个非数值特征: {non_numeric_features[:3]}{'...' if len(non_numeric_features) > 3 else ''}"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("确保所有特征都已正确编码为数值类型")
            check_result['status'] = 'warning'
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")

        if not target_is_numeric:
            error_msg = f"目标变量不是数值类型: {y_train.dtype}"
            self.quality_report['errors'].append(error_msg)
            check_result['status'] = 'fail'
            logger.error(f"[{self.quality_report['target_name']}] {error_msg}")

        self.quality_report['checks']['data_types'] = check_result

    def _check_outliers(self, X_train: pd.DataFrame):
        """检查异常值"""
        outlier_features = {}

        for column in X_train.select_dtypes(include=[np.number]).columns:
            Q1 = X_train[column].quantile(0.25)
            Q3 = X_train[column].quantile(0.75)
            IQR = Q3 - Q1

            if IQR > 0:  # 避免除零错误
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = X_train[(X_train[column] < lower_bound) | (X_train[column] > upper_bound)][column]
                outlier_ratio = len(outliers) / len(X_train)

                if outlier_ratio > 0.05:  # 超过5%的异常值
                    outlier_features[column] = {
                        'outlier_count': len(outliers),
                        'outlier_ratio': outlier_ratio,
                        'lower_bound': lower_bound,
                        'upper_bound': upper_bound
                    }

        check_result = {
            'outlier_features': outlier_features,
            'outlier_feature_count': len(outlier_features),
            'status': 'pass' if len(outlier_features) == 0 else 'warning'
        }

        if outlier_features:
            warning_msg = f"发现 {len(outlier_features)} 个特征存在较多异常值"
            self.quality_report['warnings'].append(warning_msg)
            self.quality_report['recommendations'].append("考虑对异常值进行处理（截断、变换等）")
            logger.warning(f"[{self.quality_report['target_name']}] {warning_msg}")

        self.quality_report['checks']['outliers'] = check_result

    def _generate_overall_assessment(self):
        """生成总体评估"""
        total_checks = len(self.quality_report['checks'])
        passed_checks = sum(1 for check in self.quality_report['checks'].values() if check['status'] == 'pass')
        warning_checks = sum(1 for check in self.quality_report['checks'].values() if check['status'] == 'warning')
        failed_checks = sum(1 for check in self.quality_report['checks'].values() if check['status'] == 'fail')

        if failed_checks > 0:
            overall_status = 'fail'
            overall_message = "数据质量检查失败，存在严重问题"
        elif warning_checks > total_checks // 2:
            overall_status = 'warning'
            overall_message = "数据质量一般，建议优化"
        else:
            overall_status = 'pass'
            overall_message = "数据质量良好"

        self.quality_report['overall_assessment'] = {
            'status': overall_status,
            'message': overall_message,
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'warning_checks': warning_checks,
            'failed_checks': failed_checks,
            'pass_rate': passed_checks / total_checks if total_checks > 0 else 0
        }

    def _print_quality_report(self):
        """打印质量报告"""
        target_name = self.quality_report['target_name']
        overall = self.quality_report['overall_assessment']

        print(f"\n{'='*60}")
        print(f"🔍 训练数据质量报告 - {target_name}")
        print(f"{'='*60}")
        print(f"📊 数据形状: {self.quality_report['data_shape']}")
        print(f"⏰ 检查时间: {self.quality_report['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📈 总体状态: {overall['status'].upper()} - {overall['message']}")
        print(f"✅ 通过检查: {overall['passed_checks']}/{overall['total_checks']} ({overall['pass_rate']:.1%})")

        if self.quality_report['warnings']:
            print(f"\n⚠️  警告 ({len(self.quality_report['warnings'])}):")
            for i, warning in enumerate(self.quality_report['warnings'][:5], 1):
                print(f"  {i}. {warning}")
            if len(self.quality_report['warnings']) > 5:
                print(f"  ... 还有 {len(self.quality_report['warnings']) - 5} 个警告")

        if self.quality_report['errors']:
            print(f"\n❌ 错误 ({len(self.quality_report['errors'])}):")
            for i, error in enumerate(self.quality_report['errors'], 1):
                print(f"  {i}. {error}")

        if self.quality_report['recommendations']:
            print(f"\n💡 建议 ({len(self.quality_report['recommendations'])}):")
            for i, rec in enumerate(self.quality_report['recommendations'][:3], 1):
                print(f"  {i}. {rec}")

        print(f"{'='*60}\n")


def perform_training_data_quality_check(X_train: pd.DataFrame,
                                       y_train: pd.Series,
                                       target_name: str,
                                       config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    便捷函数：对训练数据进行质量检查

    Args:
        X_train: 训练特征数据
        y_train: 训练目标数据
        target_name: 目标名称
        config: 配置字典

    Returns:
        质量报告字典
    """
    monitor = TrainingDataQualityMonitor(config)
    return monitor.comprehensive_quality_check(X_train, y_train, target_name)
