# 纯方向标签策略使用指南

## 概述

纯方向标签策略是一种新的标签生成方法，专门为二元期权交易目标设计。与传统的基于阈值的方法不同，纯方向标签法直接比较未来价格与当前价格，不设置中性区间，确保每个样本都被明确分类为上涨或下跌。

## 核心特点

### 🎯 与二元期权目标直接对齐
- **无中性样本**：每个时刻都必须做出明确的方向判断
- **简化决策**：直接比较 `future_price > current_price` 或 `future_price < current_price`
- **减少噪音**：避免了阈值设置带来的主观性

### 📊 对比传统阈值方法

| 特性 | 传统阈值方法 | 纯方向标签法 |
|------|-------------|-------------|
| 中性样本 | 有（在阈值范围内） | 无 |
| 阈值依赖 | 需要设置target_threshold | 不依赖阈值 |
| 样本利用率 | 部分样本被标记为中性 | 100%样本都有明确标签 |
| 二元期权适配 | 需要处理中性情况 | 完全对齐 |

## 配置方法

### 1. 在config.py中启用纯方向标签法

```python
# 在PREDICTION_TARGETS中的任何目标配置中添加：
{
    "name": "BTC_15m_UP_Pure",
    "target_variable_type": "UP_ONLY",  # 或 "DOWN_ONLY", "BOTH"
    "labeling_method": "pure_direction",  # 🎯 关键配置
    "target_threshold": 0.002,  # 在纯方向模式下会被忽略
    # ... 其他配置保持不变
}
```

### 2. 配置选项说明

- **labeling_method**: 
  - `"threshold"` (默认): 传统基于阈值的方法
  - `"pure_direction"`: 纯方向标签法

- **target_variable_type**: 支持所有现有类型
  - `"UP_ONLY"`: 1=明确上涨, 0=非明确上涨
  - `"DOWN_ONLY"`: 1=明确下跌, 0=非明确下跌  
  - `"BOTH"`: 1=上涨, 0=下跌（无中性）

## 实际应用示例

### 示例1：UP_ONLY模型使用纯方向标签

```python
BTC_UP_PURE_CONFIG = {
    "name": "BTC_15m_UP_Pure",
    "interval": "15m",
    "symbol": "BTCUSDT",
    "prediction_periods": [1],
    "target_variable_type": "UP_ONLY",
    "labeling_method": "pure_direction",  # 启用纯方向标签
    "enable_dynamic_thresholds": False,   # 与纯方向标签法不兼容
    "enable_triple_barrier": False,       # 与纯方向标签法不兼容
    # ... 其他特征工程配置
}
```

### 示例2：DOWN_ONLY模型使用纯方向标签

```python
BTC_DOWN_PURE_CONFIG = {
    "name": "BTC_15m_DOWN_Pure", 
    "interval": "15m",
    "symbol": "BTCUSDT",
    "prediction_periods": [1],
    "target_variable_type": "DOWN_ONLY",
    "labeling_method": "pure_direction",  # 启用纯方向标签
    # ... 其他配置
}
```

## 兼容性说明

### ✅ 兼容的配置
- 所有特征工程开关
- 所有模型类型 (LightGBM, LSTM等)
- 所有target_variable_type
- 多周期融合增强

### ❌ 不兼容的配置
- `enable_dynamic_thresholds`: 纯方向标签法不使用阈值
- `enable_triple_barrier`: 三道屏障方法有自己的标签逻辑
- `target_threshold`: 在纯方向模式下被忽略

## 性能对比测试

### 测试结果示例
```
传统阈值方法标签分布: {0: 419, 1: 424, 2: 156}
  下跌: 419 样本 (41.94%)
  上涨: 424 样本 (42.44%) 
  中性: 156 样本 (15.62%)

纯方向标签法分布: {0: 490, 1: 509}
  下跌: 490 样本 (49.05%)
  上涨: 509 样本 (50.95%)
  中性: 0 样本 (0.00%)  ✅
```

### 关键观察
1. **样本利用率提升**：纯方向标签法使用了100%的样本
2. **标签分布更均衡**：接近50-50分布，符合随机游走假设
3. **无中性样本**：完全符合二元期权交易需求

## 使用建议

### 🎯 推荐使用场景
1. **二元期权交易**：需要明确的方向判断
2. **短期预测**：减少阈值设置的主观性
3. **样本稀缺情况**：最大化样本利用率
4. **快速原型开发**：简化配置，专注模型优化

### ⚠️ 注意事项
1. **噪音增加**：可能包含更多微小价格变动的噪音
2. **模型复杂度**：需要模型学会区分有意义的方向变化
3. **特征工程重要性**：更依赖高质量的特征来过滤噪音

## 迁移指南

### 从传统阈值方法迁移到纯方向标签法

1. **备份现有配置**
2. **修改labeling_method**：
   ```python
   # 原配置
   "labeling_method": "threshold"
   
   # 新配置  
   "labeling_method": "pure_direction"
   ```
3. **禁用不兼容选项**：
   ```python
   "enable_dynamic_thresholds": False,
   "enable_triple_barrier": False,
   ```
4. **重新训练模型**
5. **对比性能指标**

## 总结

纯方向标签策略为二元期权交易提供了一种更直接、更简洁的标签生成方法。通过消除中性样本和阈值依赖，它能够：

- ✅ 提高样本利用率
- ✅ 简化配置复杂度  
- ✅ 与二元期权目标完美对齐
- ✅ 减少主观参数设置

建议在新项目中优先考虑使用纯方向标签法，特别是针对二元期权交易场景。
