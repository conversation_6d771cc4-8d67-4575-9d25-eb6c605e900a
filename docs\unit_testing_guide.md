# 单元测试系统使用指南

## 概述

本项目实现了全面的单元测试系统，覆盖核心功能的各个方面，确保代码质量和功能正确性。测试系统采用Python的unittest框架，并提供了便捷的运行工具。

## 测试覆盖范围

### 🔧 核心数据工具测试 (`tests/test_data_utils.py`)

#### 1. **时间间隔转换** (`TestIntervalToTimedelta`)
- **测试函数**: `interval_to_timedelta`
- **测试用例**:
  - 分钟间隔: `1m`, `5m`, `15m`, `30m`
  - 小时间隔: `1h`, `4h`, `12h`
  - 天间隔: `1d`, `3d`, `7d`
  - 周间隔: `1w`, `2w`
  - 无效输入处理
  - 边界情况测试

#### 2. **安全NaN填充** (`TestSafeFillNans`)
- **测试函数**: `safe_fill_nans`
- **测试用例**:
  - 无NaN的序列
  - 开头有NaN的序列
  - 中间有NaN的序列
  - 结尾有NaN的序列
  - 全部为NaN的序列
  - 历史模式vs传统模式对比
  - 空序列和None输入处理

#### 3. **目标变量创建** (`TestCreateTargetVariable`)
- **测试函数**: `create_target_variable`
- **测试用例**:
  - 二元上涨目标创建
  - 二元下跌目标创建
  - 多分类目标创建
  - 删除中性目标功能
  - 无效配置处理

#### 4. **价格变化特征** (`TestPriceChangeFeatures`)
- **测试函数**: `_add_price_change_features`
- **测试用例**:
  - 价格变化计算正确性
  - 禁用价格变化特征
  - 空周期列表处理

#### 5. **技术指标计算** (`TestTechnicalIndicators`)
- **测试函数**: `_add_technical_indicators`
- **测试用例**:
  - RSI计算和值范围验证
  - MACD计算和组件验证
  - ATR计算和正值验证
  - 禁用技术指标功能

#### 6. **最优阈值查找** (`TestFindOptimalThreshold`)
- **测试函数**: `find_optimal_threshold`
- **测试用例**:
  - F1分数方法
  - 精确率-召回率方法
  - Youden指数方法
  - 精确率约束方法
  - 不支持方法处理
  - 无效输入处理

#### 7. **预测特征准备** (`TestPrepareFeaturesForPrediction`)
- **测试函数**: `prepare_features_for_prediction`
- **测试用例**:
  - 缺失特征对齐
  - 特征顺序保持

### 🏗️ 特征管理系统测试 (`tests/test_feature_registry.py`)

#### 1. **特征注册表** (`TestFeatureRegistry`)
- **测试组件**: `FeatureRegistry`, `FeatureMetadata`
- **测试用例**:
  - 获取所有特征
  - 按类型获取特征
  - 动态特征生成
  - 特征默认值生成
  - 特征验证
  - 注册新特征

#### 2. **特征常量** (`TestFeatureConstants`)
- **测试组件**: 特征常量管理函数
- **测试用例**:
  - 获取所有特征名
  - 获取特征分组
  - 获取特征默认值
  - 特征名验证
  - 动态特征识别
  - 基础特征默认值

#### 3. **特征名构建器** (`TestFeatureNameBuilder`)
- **测试组件**: `FeatureNameBuilder`
- **测试用例**:
  - 技术指标名称构建
  - 价格变化名称构建
  - 成交量变化名称构建
  - 平滑特征名称构建
  - MTFA特征名称构建

### 📊 MTFA优化测试 (`tests/test_mtfa_optimization.py`)

#### 1. **MTFA列过滤器** (`TestMTFAColumnFilter`)
- **测试组件**: `MTFAColumnFilter`
- **测试用例**:
  - 基础过滤功能
  - 可配置过滤
  - 自定义排除模式
  - 自定义包含模式
  - 过滤摘要生成
  - 边界情况处理

#### 2. **MTFA性能优化器** (`TestMTFAPerformanceOptimizer`)
- **测试组件**: `MTFAPerformanceOptimizer`
- **测试用例**:
  - MTFA优化配置
  - 优化器初始化
  - 性能统计
  - 单个时间框架处理

### 🎯 阈值优化测试 (`tests/test_threshold_optimization.py`)

#### 1. **阈值优化器** (`TestThresholdOptimizer`)
- **测试组件**: `ThresholdOptimizer`
- **测试用例**:
  - 优化器初始化
  - 网格搜索优化
  - 贝叶斯优化
  - 不同优化指标
  - 精确率约束
  - 无效输入处理
  - 优化历史记录
  - 性能对比

#### 2. **阈值优化集成** (`TestThresholdOptimizationIntegration`)
- **测试用例**:
  - `find_optimal_threshold`函数集成
  - 完整阈值优化工作流

## 运行测试

### 1. 使用简化运行器

```bash
# 运行快速测试（推荐）
python run_unit_tests.py --quick

# 运行完整测试
python run_unit_tests.py --full
```

### 2. 使用标准unittest

```bash
# 运行所有测试
python -m unittest discover tests -v

# 运行特定测试文件
python -m unittest tests.test_data_utils -v

# 运行特定测试类
python -m unittest tests.test_data_utils.TestIntervalToTimedelta -v

# 运行特定测试方法
python -m unittest tests.test_data_utils.TestIntervalToTimedelta.test_minute_intervals -v
```

### 3. 使用高级测试运行器

```bash
# 列出所有测试文件
python tests/run_tests.py --list

# 运行特定文件
python tests/run_tests.py --file test_data_utils.py

# 运行所有测试（带颜色输出）
python tests/run_tests.py
```

## 测试结果示例

### 成功的测试输出
```
🧪 核心功能测试验证
==================================================
🔧 测试 interval_to_timedelta...
  ✅ 1m -> 0 days 00:01:00
  ✅ 5m -> 0 days 00:05:00
  ✅ 15m -> 0 days 00:15:00
  📊 结果: 6/6 通过

🔧 测试 safe_fill_nans...
  ✅ 无NaN序列: 填充成功
  ✅ 开头有NaN: 填充成功
  📊 结果: 5/5 通过

==================================================
🎯 测试总结
==================================================
总测试模块: 5
✅ 通过: 5
❌ 失败: 0
📊 成功率: 100.0%

🎉 所有核心功能测试通过！
```

## 测试配置

### 测试配置文件 (`tests/test_config.py`)

提供了统一的测试配置和工具函数：

```python
# 测试数据配置
TEST_DATA_CONFIG = {
    'n_samples': 100,
    'random_seed': 42,
    'price_range': (29000, 31000),
    'volume_range': (100, 1000),
    'date_start': '2023-01-01',
    'freq': '5T'
}

# 创建测试数据
def create_test_price_data(n_samples=None, random_seed=None, with_trend=False):
    """创建测试用的价格数据"""
    
def create_test_binary_classification_data(n_samples=None, random_seed=None, class_balance=0.3):
    """创建测试用的二分类数据"""
```

### 测试工具函数

```python
# 验证DataFrame结构
def assert_dataframe_structure(df, expected_columns=None, min_rows=1):
    """验证DataFrame结构"""

# 验证特征值
def assert_feature_values(df, feature_name, value_range=None, allow_nan=True):
    """验证特征值"""

# 跳过缺少依赖的测试
@skip_if_missing_dependency('optional_package')
def test_optional_feature(self):
    """测试可选功能"""
```

## 最佳实践

### 1. 编写新测试

```python
class TestNewFeature(unittest.TestCase):
    """测试新功能"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = create_test_price_data()
        self.config = get_test_config()
    
    def test_basic_functionality(self):
        """测试基础功能"""
        result = new_function(self.test_data, self.config)
        self.assertIsNotNone(result)
        self.assertGreater(len(result), 0)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空输入
        result = new_function(pd.DataFrame(), self.config)
        self.assertEqual(len(result), 0)
        
        # 测试无效配置
        with self.assertRaises(ValueError):
            new_function(self.test_data, {})
    
    def tearDown(self):
        """测试后清理"""
        pass
```

### 2. 测试数据管理

```python
# 使用固定随机种子确保可重现性
np.random.seed(42)

# 使用配置文件统一测试参数
config = get_test_config({
    'enable_feature_validation': False,  # 测试时禁用复杂验证
    'rsi_period': 14
})

# 创建有意义的测试数据
test_df = create_test_price_data(n_samples=100, with_trend=True)
```

### 3. 异常处理测试

```python
def test_error_handling(self):
    """测试错误处理"""
    # 测试预期异常
    with self.assertRaises(ValueError):
        function_with_validation(invalid_input)
    
    # 测试异常消息
    with self.assertRaisesRegex(ValueError, "期望的错误消息"):
        function_with_specific_error(bad_input)
```

### 4. 性能测试

```python
def test_performance(self):
    """测试性能"""
    import time
    
    large_data = create_test_price_data(n_samples=10000)
    
    start_time = time.time()
    result = expensive_function(large_data)
    end_time = time.time()
    
    # 验证性能要求
    self.assertLess(end_time - start_time, 5.0)  # 应在5秒内完成
    self.assertIsNotNone(result)
```

## 持续集成

### 测试自动化

```bash
# 在CI/CD管道中运行
python run_unit_tests.py --quick
if [ $? -eq 0 ]; then
    echo "✅ 单元测试通过"
else
    echo "❌ 单元测试失败"
    exit 1
fi
```

### 测试覆盖率

```bash
# 安装coverage工具
pip install coverage

# 运行带覆盖率的测试
coverage run -m unittest discover tests
coverage report
coverage html  # 生成HTML报告
```

## 故障排除

### 1. 导入错误
**问题**: `ImportError: cannot import name 'function_name'`
**解决**: 
- 检查函数是否存在于指定模块
- 确认模块路径正确
- 使用`@skip_if_missing_dependency`装饰器处理可选依赖

### 2. 测试数据问题
**问题**: 测试结果不一致
**解决**:
- 使用固定的随机种子
- 检查测试数据生成逻辑
- 确保测试环境一致

### 3. 性能问题
**问题**: 测试运行缓慢
**解决**:
- 减少测试数据量
- 使用mock对象替代复杂依赖
- 并行运行独立测试

## 总结

单元测试系统提供了：

1. **全面覆盖** - 测试核心功能的各个方面
2. **易于使用** - 简化的运行器和清晰的输出
3. **可扩展性** - 易于添加新的测试用例
4. **可靠性** - 固定种子和标准化测试数据
5. **实用性** - 实际验证功能正确性

通过这个测试系统，可以确保代码修改不会破坏现有功能，提高代码质量和开发效率。
