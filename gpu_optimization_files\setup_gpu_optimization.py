#!/usr/bin/env python3
"""
RTX 3070 + AMD处理器优化设置脚本
为LightGBM和TensorFlow配置GPU加速
"""

import os
import sys
import subprocess
import importlib.util
import platform

def check_gpu_support():
    """检查GPU支持情况"""
    print("🔍 检查GPU支持情况...")
    
    # 检查NVIDIA GPU
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA GPU检测成功")
            print(result.stdout.split('\n')[8])  # GPU信息行
        else:
            print("❌ NVIDIA GPU检测失败")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ nvidia-smi命令不可用")
        return False
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，检测到 {torch.cuda.device_count()} 个GPU设备")
            print(f"   CUDA版本: {torch.version.cuda}")
            return True
        else:
            print("❌ CUDA不可用")
            return False
    except ImportError:
        print("⚠️  PyTorch未安装，无法检查CUDA")
        # 尝试其他方式检查CUDA
        try:
            result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ NVCC可用，CUDA已安装")
                return True
        except FileNotFoundError:
            pass
        return False

def install_lightgbm_gpu():
    """安装LightGBM GPU版本"""
    print("\n🚀 安装LightGBM GPU版本...")
    
    try:
        # 卸载现有版本
        print("卸载现有LightGBM...")
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'lightgbm', '-y'], 
                      check=True)
        
        # 安装GPU版本
        print("安装LightGBM GPU版本...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'lightgbm', '--install-option=--gpu'], 
                      check=True)
        
        print("✅ LightGBM GPU版本安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ LightGBM GPU安装失败: {e}")
        print("尝试使用conda安装...")
        
        try:
            subprocess.run(['conda', 'install', '-c', 'conda-forge', 'lightgbm-gpu', '-y'], 
                          check=True)
            print("✅ 通过conda安装LightGBM GPU成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ conda安装也失败，将使用CPU版本")
            return False

def test_lightgbm_gpu():
    """测试LightGBM GPU功能"""
    print("\n🧪 测试LightGBM GPU功能...")
    
    try:
        import lightgbm as lgb
        import numpy as np
        
        # 创建测试数据
        X = np.random.random((1000, 10))
        y = np.random.randint(0, 2, 1000)
        
        # 测试GPU训练
        model = lgb.LGBMClassifier(
            device_type='gpu',
            gpu_platform_id=0,
            gpu_device_id=0,
            verbose=-1
        )
        
        model.fit(X, y)
        print("✅ LightGBM GPU训练测试成功")
        return True
        
    except Exception as e:
        print(f"❌ LightGBM GPU测试失败: {e}")
        return False

def setup_tensorflow_gpu():
    """设置TensorFlow GPU优化"""
    print("\n🔧 设置TensorFlow GPU优化...")
    
    try:
        import tensorflow as tf
        
        # 检查GPU可用性
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                # 设置内存增长
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                print(f"✅ TensorFlow检测到 {len(gpus)} 个GPU设备")
                for i, gpu in enumerate(gpus):
                    print(f"   GPU {i}: {gpu}")
                
                return True
            except RuntimeError as e:
                print(f"❌ TensorFlow GPU配置失败: {e}")
                return False
        else:
            print("❌ TensorFlow未检测到GPU设备")
            return False
            
    except ImportError:
        print("⚠️  TensorFlow未安装")
        return False

def create_optimization_config():
    """创建优化配置文件"""
    print("\n📝 创建优化配置文件...")
    
    config_content = '''"""
RTX 3070 + AMD处理器优化配置
自动生成的优化参数
"""

import os
import multiprocessing as mp

# 系统信息
CPU_COUNT = mp.cpu_count()
GPU_AVAILABLE = True  # RTX 3070

# 环境变量设置
def setup_environment():
    """设置优化环境变量"""
    # CPU优化
    os.environ['OMP_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['MKL_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['NUMBA_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['OPENBLAS_NUM_THREADS'] = str(CPU_COUNT)
    
    # TensorFlow优化
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
    os.environ['TF_NUM_INTEROP_THREADS'] = '4'
    os.environ['TF_NUM_INTRAOP_THREADS'] = str(CPU_COUNT)
    
    # CUDA优化
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第一个GPU
    
    print(f"✅ 环境优化设置完成 - CPU核心: {CPU_COUNT}, GPU: RTX 3070")

# LightGBM GPU优化参数
LIGHTGBM_GPU_PARAMS = {
    'device_type': 'gpu',
    'gpu_platform_id': 0,
    'gpu_device_id': 0,
    'num_threads': CPU_COUNT,
    'force_row_wise': False,  # GPU模式下使用列优先
    'histogram_pool_size': -1,
    'max_bin': 255,
    'boost_from_average': True,
    'tree_learner': 'serial',
    'verbose': -1
}

# LightGBM CPU备用参数（如果GPU不可用）
LIGHTGBM_CPU_PARAMS = {
    'device_type': 'cpu',
    'num_threads': CPU_COUNT,
    'force_row_wise': True,  # AMD处理器推荐
    'histogram_pool_size': -1,
    'max_bin': 255,
    'boost_from_average': True,
    'tree_learner': 'serial',
    'verbose': -1
}

# TensorFlow GPU优化配置
def setup_tensorflow_gpu():
    """配置TensorFlow GPU优化"""
    try:
        import tensorflow as tf
        
        # GPU内存配置
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                # 启用内存增长
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                # 线程配置
                tf.config.threading.set_inter_op_parallelism_threads(4)
                tf.config.threading.set_intra_op_parallelism_threads(0)
                
                print("✅ TensorFlow GPU优化配置完成")
                return True
            except RuntimeError as e:
                print(f"❌ TensorFlow GPU配置失败: {e}")
                return False
        else:
            print("⚠️  未检测到GPU，使用CPU模式")
            return False
    except ImportError:
        print("⚠️  TensorFlow未安装")
        return False

# 内存优化配置
MEMORY_OPTIMIZATION = {
    'max_depth': -1,
    'min_data_in_leaf': 20,
    'min_sum_hessian_in_leaf': 1e-3,
    'bagging_fraction': 0.8,
    'feature_fraction': 0.8,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1
}

# 性能监控配置
PERFORMANCE_CONFIG = {
    'enable_gpu_monitoring': True,
    'enable_memory_monitoring': True,
    'log_performance_metrics': True,
    'benchmark_mode': False
}
'''
    
    with open('gpu_optimization_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 优化配置文件已创建: gpu_optimization_config.py")

def main():
    """主函数"""
    print("🚀 RTX 3070 + AMD处理器优化设置")
    print("=" * 50)
    
    # 检查GPU支持
    gpu_available = check_gpu_support()
    
    if gpu_available:
        # 安装LightGBM GPU版本
        lgb_gpu_success = install_lightgbm_gpu()
        
        if lgb_gpu_success:
            # 测试LightGBM GPU
            test_lightgbm_gpu()
        
        # 设置TensorFlow GPU
        setup_tensorflow_gpu()
    else:
        print("⚠️  GPU不可用，将使用CPU优化配置")
    
    # 创建优化配置
    create_optimization_config()
    
    print("\n🎉 优化设置完成！")
    print("请运行以下命令应用优化：")
    print("python -c \"from gpu_optimization_config import setup_environment; setup_environment()\"")

if __name__ == "__main__":
    main()
