#!/usr/bin/env python3
# enhanced_trade_logger_usage.py
"""
升级版 TradeLogger 使用示例
展示如何记录完整的模型决策上下文信息
"""

import sys
import os
import tempfile
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.trade_logger import get_trade_logger


def example_prediction_context():
    """
    模拟 prediction.py 中 run_prediction_cycle_for_target 函数的上下文数据
    """
    # 模拟模型预测结果
    prediction_probs = np.array([0.15, 0.25, 0.60])  # [DOWN, NEUTRAL, UP]
    signal_direction = "UP"  # 最终信号方向
    
    # 模拟元模型输入特征
    meta_model_inputs = {
        "base_model_1_prob": 0.72,
        "base_model_2_prob": 0.68,
        "base_model_3_prob": 0.55,
        "ensemble_confidence": 0.85,
        "volatility_regime": "medium",
        "trend_strength": 0.42,
        "volume_profile": "increasing",
        "market_hours": "active"
    }
    
    # 模拟市场状态信息
    market_state = {
        "regime": "strong_uptrend",
        "atr_percent": 2.34,
        "adx_value": 45.6,
        "rsi": 68.2,
        "bb_position": 0.78
    }
    
    # 模拟 SHAP 分析的 Top 10 重要特征
    top_features = {
        "ma_cross_signal": 0.0234,
        "rsi_divergence": -0.0156,
        "volume_surge": 0.0189,
        "support_resistance": 0.0098,
        "momentum_indicator": 0.0145,
        "volatility_breakout": 0.0067,
        "trend_confirmation": 0.0234,
        "market_sentiment": 0.0089,
        "correlation_btc": 0.0123,
        "funding_rate": -0.0045
    }
    
    # 构建完整的上下文数据
    context_data = {
        # 模型预测信息
        "signal_probability": prediction_probs[2],      # UP 概率
        "neutral_probability": prediction_probs[1],     # NEUTRAL 概率
        "opposite_probability": prediction_probs[0],    # DOWN 概率
        "meta_model_inputs": meta_model_inputs,
        
        # 市场状态信息
        "market_regime": market_state["regime"],
        "atr_percent": market_state["atr_percent"],
        "adx_value": market_state["adx_value"],
        
        # 核心特征快照
        "top_features": top_features
    }
    
    return context_data


def example_basic_usage():
    """基础使用示例（不包含上下文数据）"""
    print("=== 基础使用示例 ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        test_file = f.name
    
    try:
        # 获取日志记录器
        trade_logger = get_trade_logger(test_file)
        
        # 记录基础交易（无上下文数据）
        trade_id = trade_logger.record_trade_entry(
            target_name="BTC_15m_Basic",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=10.0,
            payout_ratio=0.85
        )
        
        print(f"开仓记录: {trade_id}")
        
        # 记录平仓
        success = trade_logger.record_trade_exit(
            trade_id=trade_id,
            exit_price=51000.0,
            result="WIN"
        )
        
        print(f"平仓记录成功: {success}")
        
        # 显示CSV内容
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print("\nCSV内容:")
            print(content)
            
    finally:
        if os.path.exists(test_file):
            os.unlink(test_file)


def example_enhanced_usage():
    """增强使用示例（包含完整上下文数据）"""
    print("\n=== 增强使用示例（完整上下文） ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        test_file = f.name
    
    try:
        # 获取日志记录器
        trade_logger = get_trade_logger(test_file)
        
        # 获取模拟的上下文数据
        context_data = example_prediction_context()
        
        # 记录增强交易（包含完整上下文数据）
        trade_id = trade_logger.record_trade_entry(
            target_name="BTC_15m_Enhanced",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=15.0,
            payout_ratio=0.85,
            context_data=context_data  # 传入完整上下文
        )
        
        print(f"开仓记录: {trade_id}")
        print(f"信号概率: {context_data['signal_probability']:.3f}")
        print(f"市场状态: {context_data['market_regime']}")
        print(f"ATR波动率: {context_data['atr_percent']:.2f}%")
        
        # 记录平仓
        success = trade_logger.record_trade_exit(
            trade_id=trade_id,
            exit_price=51500.0,
            result="WIN"
        )
        
        print(f"平仓记录成功: {success}")
        
        # 显示CSV内容
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print("\nCSV内容:")
            print(content)
            
        # 分析CSV字段
        import pandas as pd
        df = pd.read_csv(test_file)
        print(f"\nCSV字段数: {len(df.columns)}")
        print("所有字段名:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
            
    finally:
        if os.path.exists(test_file):
            os.unlink(test_file)


def example_multiple_trades():
    """多笔交易示例"""
    print("\n=== 多笔交易示例 ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        test_file = f.name
    
    try:
        trade_logger = get_trade_logger(test_file)
        
        # 交易1: 有完整上下文
        context1 = example_prediction_context()
        context1["market_regime"] = "strong_uptrend"
        context1["signal_probability"] = 0.75
        
        trade_id1 = trade_logger.record_trade_entry(
            target_name="MetaModel_V2",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=20.0,
            context_data=context1
        )
        
        # 交易2: 部分上下文
        context2 = {
            "signal_probability": 0.65,
            "market_regime": "sideways_volatile",
            "atr_percent": 3.45
        }
        
        trade_id2 = trade_logger.record_trade_entry(
            target_name="ETH_5m_Strategy",
            symbol="ETHUSDT",
            direction="SHORT",
            entry_price=3000.0,
            amount=25.0,
            context_data=context2
        )
        
        # 交易3: 无上下文
        trade_id3 = trade_logger.record_trade_entry(
            target_name="Simple_Strategy",
            symbol="ADAUSDT",
            direction="LONG",
            entry_price=0.5,
            amount=30.0
        )
        
        print(f"记录了3笔交易: {trade_id1}, {trade_id2}, {trade_id3}")
        
        # 平仓所有交易
        trade_logger.record_trade_exit(trade_id1, 51000.0, "WIN")
        trade_logger.record_trade_exit(trade_id2, 2950.0, "WIN")
        trade_logger.record_trade_exit(trade_id3, 0.48, "LOSS")
        
        # 分析结果
        import pandas as pd
        df = pd.read_csv(test_file)
        
        print(f"\n总交易数: {len(df)}")
        print(f"盈利交易: {len(df[df['result'] == 'WIN'])}")
        print(f"亏损交易: {len(df[df['result'] == 'LOSS'])}")
        print(f"总盈亏: {df['profit_loss'].sum():.2f}")
        
        # 显示有上下文信息的交易
        context_trades = df[df['entry_signal_probability'].notna()]
        print(f"\n有上下文信息的交易: {len(context_trades)}")
        
        if len(context_trades) > 0:
            print("上下文信息摘要:")
            for _, row in context_trades.iterrows():
                print(f"  {row['target_name']}: 信号概率={row['entry_signal_probability']:.3f}, "
                      f"市场状态={row['entry_market_regime']}")
        
    finally:
        if os.path.exists(test_file):
            os.unlink(test_file)


def example_integration_with_prediction_system():
    """与预测系统集成的示例"""
    print("\n=== 预测系统集成示例 ===")
    
    def mock_run_prediction_cycle_for_target(target_name: str):
        """
        模拟 prediction.py 中的 run_prediction_cycle_for_target 函数
        """
        print(f"运行预测周期: {target_name}")
        
        # 模拟预测过程...
        prediction_result = {
            "signal": "UP",
            "confidence": 0.78,
            "entry_price": 50000.0,
            "amount": 15.0
        }
        
        # 收集上下文数据
        context_data = example_prediction_context()
        context_data["signal_probability"] = prediction_result["confidence"]
        
        # 如果信号确认，记录交易
        if prediction_result["confidence"] > 0.7:
            trade_logger = get_trade_logger("results/logs/prediction_trades.csv")
            
            trade_id = trade_logger.record_trade_entry(
                target_name=target_name,
                symbol="BTCUSDT",
                direction=prediction_result["signal"],
                entry_price=prediction_result["entry_price"],
                amount=prediction_result["amount"],
                context_data=context_data
            )
            
            print(f"交易信号确认并记录: {trade_id}")
            return trade_id
        else:
            print("信号置信度不足，未开仓")
            return None
    
    # 模拟运行
    os.makedirs("results/logs", exist_ok=True)
    trade_id = mock_run_prediction_cycle_for_target("BTC_15m_MetaModel")
    
    if trade_id:
        print(f"模拟交易已开仓: {trade_id}")
        # 在实际系统中，平仓会在交易到期时自动处理


if __name__ == "__main__":
    print("升级版 TradeLogger 使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_basic_usage()
    example_enhanced_usage()
    example_multiple_trades()
    example_integration_with_prediction_system()
    
    print("\n所有示例完成！")
