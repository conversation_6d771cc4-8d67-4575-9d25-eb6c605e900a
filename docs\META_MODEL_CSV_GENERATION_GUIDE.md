# 元模型CSV文件生成功能使用指南

## 概述

为了恢复之前版本中的 `X_meta_features_oof.csv` 和 `y_meta_target.csv` 文件生成功能，我们对元模型训练代码进行了增强，现在可以在训练过程中自动保存这些CSV文件。

## 功能特性

### 1. 自动CSV文件生成
- **X_meta_features_oof.csv**: 包含所有元模型特征的OOF预测数据
- **y_meta_target.csv**: 包含对应的目标变量数据
- **meta_data_summary.json**: 包含数据摘要和元信息

### 2. 数据完整性保证
- 严格的索引对齐确保特征和目标变量完全匹配
- 自动处理缺失值和数据质量问题
- 包含详细的数据验证和日志记录

### 3. 灵活的配置选项
- 可选择是否启用CSV文件保存
- 可自定义输出目录
- 支持传统元模型和精英元模型两种训练方式

## 使用方法

### 1. 基本使用（优化的元模型数据准备）

```python
from src.training.optimized_meta_data_preparation import prepare_meta_training_data_optimized

# 准备元模型训练数据并保存CSV文件
X_meta, y_meta = prepare_meta_training_data_optimized(
    df_raw_data=df_raw_data,           # 原始K线数据
    target_data=target_data,           # 目标变量
    trained_models_info=trained_models_info,  # 已训练模型信息
    base_models_config=base_models_config,    # 基础模型配置
    save_csv_files=True,               # 启用CSV文件保存
    output_dir="models/meta_model_data"  # 输出目录
)
```

### 2. 精英元模型训练

```python
from src.training.elite_meta_model import EliteMetaModelTrainer

# 创建精英元模型训练器
elite_trainer = EliteMetaModelTrainer(base_models_config)

# 启用优化的数据准备（会自动保存CSV文件）
elite_trainer.enable_optimized_data_preparation(True)

# 训练超级元模型
result = elite_trainer.train_super_meta_model(
    oof_predictions=oof_predictions,
    target_data=target_data,
    output_dir="models/elite_meta_model",  # CSV文件会保存到这个目录
    df_full_hist_data_for_oof_input=df_raw_data
)
```

### 3. 测试功能

运行测试脚本验证CSV生成功能：

```bash
python test_meta_csv_generation.py
```

## 生成的文件说明

### X_meta_features_oof.csv
- **内容**: 元模型的所有特征数据
- **格式**: CSV格式，包含时间索引
- **列**: 各基础模型的OOF预测概率和上下文特征
- **示例列名**: 
  - `oof_BTC_15m_UP_elite_fold0`
  - `oof_BTC_15m_DOWN_elite_fold1`
  - `feat_market_volatility_btc_15m_up_model`

### y_meta_target.csv
- **内容**: 元模型的目标变量
- **格式**: CSV格式，包含时间索引
- **数据**: 二分类或多分类标签
- **对齐**: 与X_meta_features_oof.csv严格索引对齐

### meta_data_summary.json
- **内容**: 数据摘要和元信息
- **包含信息**:
  - 时间戳
  - 样本数量
  - 特征数量
  - 特征名称列表
  - 目标变量分布
  - 数据时间范围

## 文件位置

### 默认保存位置
- **优化元模型**: `models/meta_model_data/`
- **精英元模型**: `models/elite_meta_model/`

### 文件结构示例
```
models/
├── meta_model_data/
│   ├── X_meta_features_oof.csv
│   ├── y_meta_target.csv
│   └── meta_data_summary.json
└── elite_meta_model/
    ├── X_meta_features_oof.csv
    ├── y_meta_target.csv
    ├── elite_training_data_summary.json
    ├── elite_super_meta_model.joblib
    └── elite_meta_features.json
```

## 配置选项

### save_csv_files 参数
- **类型**: boolean
- **默认值**: True
- **作用**: 控制是否生成CSV文件

### output_dir 参数
- **类型**: string
- **默认值**: "models/meta_model_data"
- **作用**: 指定CSV文件保存目录

## 注意事项

1. **数据一致性**: CSV文件中的特征数据和目标变量保证严格的索引对齐
2. **文件覆盖**: 重新训练时会覆盖现有的CSV文件
3. **磁盘空间**: 大数据集可能生成较大的CSV文件，请确保有足够的磁盘空间
4. **编码格式**: CSV文件使用UTF-8编码保存

## 故障排除

### 常见问题

1. **CSV文件未生成**
   - 检查 `save_csv_files=True` 是否设置
   - 确认输出目录有写入权限
   - 查看日志中的错误信息

2. **数据为空**
   - 检查输入数据是否有效
   - 确认已训练模型信息是否正确
   - 验证基础模型配置

3. **索引不对齐**
   - 确保原始数据和目标变量有相同的时间索引
   - 检查数据预处理步骤

### 调试建议

1. 启用详细日志记录
2. 使用测试脚本验证功能
3. 检查生成的摘要文件了解数据统计信息

## 更新历史

- **2024-12-XX**: 添加CSV文件生成功能
- **2024-12-XX**: 支持精英元模型CSV保存
- **2024-12-XX**: 添加数据摘要和验证功能
