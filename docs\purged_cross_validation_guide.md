# Purged K-Fold 交叉验证使用指南

## 📖 概述

Purged K-Fold交叉验证是一种专为金融时间序列设计的高级交叉验证方法，通过在训练集和验证集之间引入"清洗期"（Purge Period）来防止数据泄露，特别适用于目标变量基于未来价格变化定义的场景。

### 🎯 核心优势

1. **防止数据泄露**：通过purge期间确保训练数据严格早于验证数据
2. **适应金融特性**：考虑了金融时间序列的特殊性质
3. **提高模型可靠性**：更真实地模拟实际交易环境
4. **专业标准**：基于《Advances in Financial Machine Learning》的理论基础

## 🔧 配置方法

### 1. 基本配置

在目标配置中添加以下参数：

```python
target_config = {
    'name': 'BTC_15m_UP',
    'interval': '15m',
    'prediction_periods': [2],  # 预测未来2个周期
    
    # 启用Purged K-Fold交叉验证
    'purged_cv_enable': True,
    
    # 基本参数
    'cv_folds': 5,                          # 交叉验证折数
    'purged_cv_purge_length': 10,           # 清洗期长度（样本数量）
    'purged_cv_embargo_length': 2,          # 禁运期长度（可选）
    'purged_cv_min_train_size': None,       # 最小训练集大小（None为自动）
    
    # 自动计算purge长度
    'purged_cv_auto_purge_calculation': True,  # 推荐设为True
    
    # 其他配置...
}
```

### 2. 自动Purge长度计算

当`purged_cv_auto_purge_calculation=True`时，系统会根据以下因素自动计算最优purge长度：

- **目标变量前瞻期**：`prediction_periods`参数
- **最大特征窗口**：特征工程中使用的最大回看期
- **安全系数**：默认1.5倍的安全边际

```python
# 自动计算示例
prediction_periods = [2]        # 预测未来2个周期
max_feature_window = 20         # 最大特征窗口为20
safety_factor = 1.5             # 安全系数

# 推荐purge长度 = max(2, 20) * 1.5 = 30
```

### 3. 手动Purge长度设置

如果需要手动控制purge长度：

```python
target_config = {
    'purged_cv_enable': True,
    'purged_cv_auto_purge_calculation': False,  # 禁用自动计算
    'purged_cv_purge_length': 15,               # 手动设置purge长度
    'purged_cv_embargo_length': 3,              # 额外的禁运期
}
```

## 📊 参数详解

### 核心参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `purged_cv_enable` | bool | False | 是否启用Purged K-Fold |
| `purged_cv_purge_length` | int | 10 | 清洗期长度（样本数量） |
| `purged_cv_embargo_length` | int | 0 | 禁运期长度（可选） |
| `purged_cv_min_train_size` | int/None | None | 最小训练集大小 |
| `purged_cv_auto_purge_calculation` | bool | True | 是否自动计算purge长度 |

### Purge长度建议

| 预测周期 | 特征窗口 | 推荐Purge长度 | 说明 |
|----------|----------|---------------|------|
| 1期 | 10-20 | 15-30 | 短期预测 |
| 2期 | 20-30 | 30-45 | 中期预测 |
| 5期+ | 30+ | 50+ | 长期预测 |

## 🚀 使用示例

### 示例1：15分钟BTC上涨预测

```python
BTC_15M_UP_CONFIG = {
    'name': 'BTC_15m_UP',
    'interval': '15m',
    'symbol': 'BTCUSDT',
    'prediction_periods': [2],  # 预测30分钟后
    'target_variable_type': 'UP_ONLY',
    'target_threshold': 0.002,
    
    # Purged K-Fold配置
    'purged_cv_enable': True,
    'cv_folds': 5,
    'purged_cv_auto_purge_calculation': True,
    'purged_cv_embargo_length': 2,
    
    # 特征工程配置
    'enable_ta': True,
    'enable_mtfa': True,
    'enable_volume': True,
    
    # 其他配置...
}
```

### 示例2：5分钟高频交易配置

```python
BTC_5M_CONFIG = {
    'name': 'BTC_5m_SCALP',
    'interval': '5m',
    'prediction_periods': [1],  # 预测5分钟后
    
    # 高频交易需要更严格的purge
    'purged_cv_enable': True,
    'purged_cv_auto_purge_calculation': False,
    'purged_cv_purge_length': 20,  # 更长的purge期
    'purged_cv_embargo_length': 5,
    'cv_folds': 3,  # 减少折数以保证足够的训练数据
    
    # 其他配置...
}
```

## 📈 性能对比

### 传统TimeSeriesSplit vs Purged K-Fold

| 方面 | TimeSeriesSplit | Purged K-Fold |
|------|-----------------|---------------|
| 数据泄露风险 | 中等 | 极低 |
| 验证真实性 | 一般 | 高 |
| 训练数据利用率 | 高 | 中等 |
| 计算复杂度 | 低 | 低 |
| 适用场景 | 通用时间序列 | 金融时间序列 |

### 预期效果

使用Purged K-Fold后，您可能会观察到：

1. **验证分数下降**：这是正常的，因为消除了数据泄露
2. **模型更稳定**：实际交易表现与验证结果更一致
3. **过拟合减少**：更真实的验证环境

## ⚠️ 注意事项

### 1. 数据量要求

Purged K-Fold需要更多的数据：

```python
# 最小数据量估算
min_samples = n_splits * (min_train_size + purge_length + test_size + embargo_length)

# 示例：5折，每折最少100个训练样本，purge=20，test=50，embargo=5
min_samples = 5 * (100 + 20 + 50 + 5) = 875
```

### 2. 配置建议

- **开始时使用自动计算**：`purged_cv_auto_purge_calculation=True`
- **逐步调优**：根据实际效果调整purge长度
- **监控训练数据量**：确保每折有足够的训练样本

### 3. 故障排除

常见问题及解决方案：

```python
# 问题1：数据不足警告
# 解决：减少折数或purge长度
'cv_folds': 3,  # 从5减少到3
'purged_cv_purge_length': 10,  # 从20减少到10

# 问题2：训练时间过长
# 解决：优化数据量或使用并行训练
'data_fetch_limit': 10000,  # 限制数据量

# 问题3：验证分数过低
# 解决：这可能是正常的，检查是否消除了数据泄露
```

## 🔄 迁移指南

### 从TimeSeriesSplit迁移

1. **备份现有配置**
2. **添加Purged CV配置**
3. **对比结果**
4. **逐步调优**

```python
# 迁移前
old_config = {
    'cv_folds': 5,
    # 其他配置...
}

# 迁移后
new_config = {
    'cv_folds': 5,
    'purged_cv_enable': True,
    'purged_cv_auto_purge_calculation': True,
    # 其他配置...
}
```

## 📚 参考资料

1. **《Advances in Financial Machine Learning》** - Marcos Lopez de Prado
2. **Purged Cross-Validation论文**
3. **金融机器学习最佳实践**

## 🎯 总结

Purged K-Fold交叉验证是提升金融机器学习模型可靠性的重要工具。通过正确配置和使用，可以显著减少数据泄露风险，提高模型在实际交易中的表现。

建议从自动配置开始，然后根据具体需求进行调优。记住，验证分数的下降通常意味着模型更加真实和可靠。
