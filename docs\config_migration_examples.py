#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 配置管理迁移示例

本文件展示如何从直接字典访问迁移到类型安全的配置包装器。
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from config import get_target_config_wrapper, safe_get_target_config, migrate_config_access_patterns
from src.core.config_validator import ConfigurationError


def example_old_pattern():
    """❌ 旧的配置访问模式（不推荐）"""
    print("=== 旧的配置访问模式（不推荐）===")
    
    # 旧方式：直接字典访问
    from config import get_target_config
    
    try:
        config = get_target_config("BTC_15m_UP")
        
        # 直接字典访问，容易出错
        name = config['name']  # 可能抛出 KeyError
        threshold = config.get('target_threshold', 0.001)  # 无类型安全
        periods = config.get('prediction_periods', [1])  # 无类型验证
        
        print(f"目标名称: {name}")
        print(f"阈值: {threshold}")
        print(f"预测周期: {periods}")
        
    except KeyError as e:
        print(f"配置键缺失: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


def example_new_pattern():
    """✅ 新的配置访问模式（推荐）"""
    print("\n=== 新的配置访问模式（推荐）===")
    
    try:
        # 新方式：类型安全的配置包装器
        wrapper = get_target_config_wrapper("BTC_15m_UP")
        
        # 类型安全的配置访问
        name = wrapper.get_str('name', required=True)
        threshold = wrapper.get_float('target_threshold', default=0.001)
        periods = wrapper.get_list('prediction_periods', default=[1])
        enable_ta = wrapper.get_bool('enable_ta', default=True)
        
        print(f"目标名称: {name}")
        print(f"阈值: {threshold}")
        print(f"预测周期: {periods}")
        print(f"启用技术指标: {enable_ta}")
        
        # 处理可能不存在的配置项
        optional_param = wrapper.get('optional_param', default='default_value')
        print(f"可选参数: {optional_param}")
        
    except ConfigurationError as e:
        print(f"配置错误: {e}")
        if hasattr(e, 'suggestions') and e.suggestions:
            print(f"建议: {', '.join(e.suggestions)}")
    except ValueError as e:
        print(f"目标未找到: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


def example_safe_access():
    """🛡️ 安全的配置访问模式"""
    print("\n=== 安全的配置访问模式 ===")
    
    # 使用安全获取函数，支持回退
    config_or_wrapper = safe_get_target_config("BTC_15m_UP", fallback_to_dict=True)
    
    if hasattr(config_or_wrapper, 'get_str'):
        # 是配置包装器
        print("使用配置包装器模式")
        name = config_or_wrapper.get_str('name', required=True)
        threshold = config_or_wrapper.get_float('target_threshold', default=0.001)
    else:
        # 是字典，回退模式
        print("回退到字典模式")
        name = config_or_wrapper.get('name', 'Unknown')
        threshold = config_or_wrapper.get('target_threshold', 0.001)
    
    print(f"目标名称: {name}")
    print(f"阈值: {threshold}")


def example_batch_validation():
    """📋 批量配置验证示例"""
    print("\n=== 批量配置验证 ===")
    
    from config import validate_all_target_configs
    
    validation_results = validate_all_target_configs()
    
    for target_name, errors in validation_results.items():
        if errors:
            print(f"❌ 目标 '{target_name}' 有配置问题:")
            for error in errors:
                print(f"   - {error}")
        else:
            print(f"✅ 目标 '{target_name}' 配置正常")


def example_migration_patterns():
    """🔄 具体的迁移模式示例"""
    print("\n=== 具体的迁移模式示例 ===")
    
    # 示例1: 基本配置访问
    print("1. 基本配置访问迁移:")
    print("   旧: config = get_target_config(target_name)")
    print("       value = config['key']")
    print("   新: wrapper = get_target_config_wrapper(target_name)")
    print("       value = wrapper.get_str('key', required=True)")
    
    # 示例2: 带默认值的访问
    print("\n2. 带默认值的访问迁移:")
    print("   旧: value = config.get('key', default_value)")
    print("   新: value = wrapper.get('key', default=default_value)")
    
    # 示例3: 类型特定的访问
    print("\n3. 类型特定的访问:")
    print("   字符串: wrapper.get_str('name', default='')")
    print("   数值:   wrapper.get_float('threshold', default=0.001)")
    print("   整数:   wrapper.get_int('periods', default=1)")
    print("   布尔:   wrapper.get_bool('enable_feature', default=True)")
    print("   列表:   wrapper.get_list('items', default=[])")


def example_error_handling():
    """🚨 错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    try:
        # 尝试访问不存在的目标
        wrapper = get_target_config_wrapper("NonExistent_Target")
    except ValueError as e:
        print(f"目标未找到错误: {e}")
    
    try:
        # 尝试访问存在的目标但要求不存在的必需字段
        wrapper = get_target_config_wrapper("BTC_15m_UP")
        value = wrapper.get_str('non_existent_required_field', required=True)
    except ConfigurationError as e:
        print(f"配置错误: {e}")
        print(f"字段路径: {e.field_path}")


def main():
    """主函数：运行所有示例"""
    print("🚀 配置管理迁移示例")
    print("=" * 50)
    
    # 显示迁移指南
    migrate_config_access_patterns()
    
    # 运行示例
    example_old_pattern()
    example_new_pattern()
    example_safe_access()
    example_batch_validation()
    example_migration_patterns()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("✅ 所有示例运行完成")


if __name__ == "__main__":
    main()
