# config.py
import os
import sys
import multiprocessing as mp
from enum import Enum
from typing import Dict, Any, List, Optional, Union, Literal, TypedDict, overload, TYPE_CHECKING
from dotenv import load_dotenv # 用于从 .env 文件加载环境变量 (如API密钥)
import pandas as pd           # 用于时间处理，例如 pd.Timedelta

# --- 性能优化配置 (AMD处理器 + RTX 3070) ---
def setup_performance_optimization():
    """设置性能优化环境变量"""
    cpu_count = mp.cpu_count()

    # CPU线程优化
    os.environ.setdefault('OMP_NUM_THREADS', str(cpu_count))
    os.environ.setdefault('MKL_NUM_THREADS', str(cpu_count))
    os.environ.setdefault('NUMBA_NUM_THREADS', str(cpu_count))
    os.environ.setdefault('OPENBLAS_NUM_THREADS', str(cpu_count))

    # TensorFlow优化
    os.environ.setdefault('TF_CPP_MIN_LOG_LEVEL', '1')
    os.environ.setdefault('TF_ENABLE_ONEDNN_OPTS', '1')
    os.environ.setdefault('TF_NUM_INTEROP_THREADS', '4')
    os.environ.setdefault('TF_NUM_INTRAOP_THREADS', str(cpu_count))

    # 内存优化
    os.environ.setdefault('MALLOC_TRIM_THRESHOLD_', '100000')
    os.environ.setdefault('PYTHONHASHSEED', '0')

    # 禁用GPU使用，确保只使用CPU
    os.environ.setdefault('CUDA_VISIBLE_DEVICES', '-1')  # -1表示禁用所有GPU

# 自动应用性能优化
setup_performance_optimization()

# 性能优化常量
CPU_COUNT = mp.cpu_count()
MEMORY_OPTIMIZATION_ENABLED = True
GPU_AVAILABLE = False  # 强制禁用GPU，确保只使用CPU训练

# 类型检查时导入，避免循环导入
if TYPE_CHECKING:
    from src.core.config_validator import TargetConfigWrapper

# --- 枚举和常量定义 ---
class TargetType(Enum):
    """目标变量类型枚举"""
    UP_ONLY = "UP_ONLY"
    DOWN_ONLY = "DOWN_ONLY"
    BOTH = "BOTH"
    META_MODEL_DISPLAY = "META_MODEL_DISPLAY"

class DeviceType(Enum):
    """设备类型枚举"""
    CPU = "cpu"
    GPU = "gpu"

class TriggerType(Enum):
    """预测触发类型枚举"""
    KLINE_CLOSE = "kline_close"
    APSCHEDULER_DRIVEN = "apscheduler_driven"

class TrendIndicatorType(Enum):
    """趋势指标类型枚举"""
    ADX = "adx"
    EMA_CROSS = "ema_cross"

class OptimizationMethod(Enum):
    """阈值优化方法枚举"""
    F1 = "f1"
    PRECISION_RECALL = "precision_recall"
    YOUDEN = "youden"
    BALANCED = "balanced"
    PRECISION_CONSTRAINED_RECALL = "precision_constrained_recall"

# 基础模型名称常量
class BaseModelNames:
    """基础模型名称常量"""
    BTC_15M_UP = "BTC_15m_UP"
    BTC_15M_DOWN = "BTC_15m_DOWN"
    BTC_15M_LSTM = "BTC_15m_LSTM"  # 新增LSTM模型名称
    META_SIGNAL_BTC = "MetaSignal_BTC"

# 概率类型常量
class ProbabilityTypes:
    """概率类型常量"""
    P_FAVORABLE = "p_favorable"
    P_UP = "p_up"
    P_DOWN = "p_down"

# --- 类型定义 ---
# 设备类型字面量
DeviceTypeLiteral = Literal["cpu", "gpu"]

# 目标变量类型字面量
TargetVariableTypeLiteral = Literal["UP_ONLY", "DOWN_ONLY", "BOTH", "META_MODEL_DISPLAY"]

# 触发类型字面量
TriggerTypeLiteral = Literal["kline_close", "apscheduler_driven"]

# 趋势指标类型字面量
TrendIndicatorTypeLiteral = Literal["adx", "ema_cross"]

# 优化方法字面量
OptimizationMethodLiteral = Literal["f1", "precision_recall", "youden", "balanced", "precision_constrained_recall"]

# 缩放器类型字面量
ScalerTypeLiteral = Literal["minmax", "standard"]

# 概率类型字面量
ProbabilityTypeLiteral = Literal["p_favorable", "p_up", "p_down"]

# LightGBM参数类型定义
class LightGBMParams(TypedDict, total=False):
    """LightGBM参数类型定义"""
    objective: str
    metric: str
    class_weight: Optional[Dict[int, float]]
    boosting_type: str
    random_state: int
    n_estimators: int
    early_stopping_rounds: int
    verbose: int
    verbosity: int
    learning_rate: float
    num_leaves: int
    max_depth: int
    reg_alpha: float
    reg_lambda: float
    colsample_bytree: float
    subsample: float
    min_child_samples: int
    subsample_freq: int

# 特征工程参数类型定义
class FeatureEngineeringParams(TypedDict, total=False):
    """特征工程参数类型定义"""
    enable_price_change: bool
    enable_volume: bool
    enable_candle: bool
    enable_ta: bool
    enable_time: bool
    enable_fund_flow: bool
    enable_mtfa: bool
    enable_pattern_recognition: bool
    enable_trend_slope: bool
    price_change_periods: List[int]
    volume_avg_period: int
    atr_period: int
    rsi_period: int
    macd_fast: int
    macd_slow: int
    macd_sign: int
    stoch_k: int
    stoch_d: int
    stoch_smooth_k: int

# SMOTE参数类型定义
class SMOTEParams(TypedDict, total=False):
    """SMOTE参数类型定义"""
    smote_enable: bool
    smote_k_neighbors: int
    smote_min_samples_threshold: int
    smote_random_state: int

# 阈值优化参数类型定义
class ThresholdOptimizationParams(TypedDict, total=False):
    """阈值优化参数类型定义"""
    threshold_optimization_enable: bool
    threshold_optimization_method: OptimizationMethodLiteral
    threshold_save_to_metadata: bool
    threshold_default_value: float
    threshold_use_independent_validation: bool
    threshold_independent_val_ratio: float
    threshold_min_precision: float
    threshold_precision_constraint_fallback: bool

# APScheduler配置类型定义
class APSchedulerConfig(TypedDict, total=False):
    """APScheduler配置类型定义"""
    apscheduler_job_enabled: bool
    apscheduler_trigger_type: Literal["cron", "interval", "date"]
    apscheduler_cron_config: Dict[str, Union[str, int]]

# 完整的目标配置类型定义
class TargetConfig(TypedDict, total=False):
    """完整的目标配置类型定义"""
    # 基本信息
    name: str
    symbol: str
    scaler_type: ScalerTypeLiteral
    device_type: DeviceTypeLiteral
    interval: str
    interval_timedelta: Optional[pd.Timedelta]
    prediction_periods: List[int]
    prediction_minutes_display: Union[int, str]
    model_save_dir: str
    target_variable_type: TargetVariableTypeLiteral
    drop_neutral_targets: bool
    prediction_trigger_type: TriggerTypeLiteral
    target_threshold: float
    signal_threshold: float

    # 数据处理参数
    data_fetch_limit: int
    train_ratio: float
    validation_ratio: float

    # 特征工程参数（继承自FeatureEngineeringParams）
    enable_price_change: bool
    enable_volume: bool
    enable_candle: bool
    enable_ta: bool
    enable_time: bool
    enable_fund_flow: bool
    enable_mtfa: bool
    enable_pattern_recognition: bool
    enable_trend_slope: bool

    # LightGBM参数（继承自LightGBMParams）
    objective: str
    metric: str
    class_weight: Optional[Dict[int, float]]
    boosting_type: str
    random_state: int
    n_estimators: int
    early_stopping_rounds: int
    learning_rate: float
    num_leaves: int
    max_depth: int
    reg_alpha: float
    reg_lambda: float
    colsample_bytree: float
    subsample: float
    min_child_samples: int

    # SMOTE参数（继承自SMOTEParams）
    smote_enable: bool
    smote_k_neighbors: int
    smote_min_samples_threshold: int
    smote_random_state: int

    # 阈值优化参数（继承自ThresholdOptimizationParams）
    threshold_optimization_enable: bool
    threshold_optimization_method: OptimizationMethodLiteral
    threshold_save_to_metadata: bool
    threshold_default_value: float
    threshold_use_independent_validation: bool
    threshold_independent_val_ratio: float

    # APScheduler参数（继承自APSchedulerConfig）
    apscheduler_job_enabled: bool
    apscheduler_trigger_type: Literal["cron", "interval", "date"]
    apscheduler_cron_config: Dict[str, Union[str, int]]

# 类型别名
ConfigDict = Dict[str, Any]  # 通用配置字典类型
TargetConfigDict = TargetConfig  # 目标配置字典类型

# 加载 .env 文件中的环境变量 (如果存在的话)
# 这允许你将敏感信息（如API密钥）存储在 .env 文件中，而不是直接写入代码
load_dotenv()

# --- Binance API 密钥 ---
# 用于与Binance API进行认证的通信，例如获取历史数据、账户信息等。
# 建议将这些密钥存储在 .env 文件中，以增强安全性。
API_KEY = os.getenv("BINANCE_API_KEY", None)        # Binance API Key
API_SECRET = os.getenv("BINANCE_API_SECRET", None)  # Binance API Secret

# --- APScheduler 时区配置 ---
# APScheduler (用于定时任务，如按计划触发预测) 使用的时区。
# 推荐与应用程序的主要时区 (APP_TIMEZONE 在 main.py 中定义) 保持一致，以避免混淆。
APScheduler_TIMEZONE = "Asia/Shanghai" # 例如 "UTC", "America/New_York"

# --- 🎯 自动预测任务调度配置 ---
# 控制自动预测任务的执行频率和行为
AUTO_PREDICTION_ENABLED = True         # [布尔值] ✅ V11.0: 启用自动预测，允许元模型决策
                                       # True: 启用自动预测，按照下面的时间间隔执行
                                       # False: 禁用自动预测，只能手动触发

AUTO_PREDICTION_INTERVAL_MINUTES = 15  # [整数] ✅ V11.0: 设置为15分钟执行自动预测
                                       # 常用值：1, 5, 15, 30, 60
                                       # 设置为0表示禁用自动预测
                                       # 注意：过小的间隔可能导致系统负载过高

AUTO_PREDICTION_TRIGGER_TYPE = "cron"  # [字符串] 触发类型
                                          # "interval": 按固定间隔执行（推荐）
                                          # "cron": 按cron表达式执行（高级用户）

AUTO_PREDICTION_CRON_EXPRESSION = "*/15 * * * *"  # [字符串] 当trigger_type为"cron"时使用
                                                  # 格式：分钟 小时 日 月 星期
                                                  # 例如："*/15 * * * *" = 每15分钟
                                                  # 例如："0 * * * *" = 每小时整点
                                                  # 例如："0 9,21 * * *" = 每天9点和21点

AUTO_PREDICTION_OFFSET_SECONDS = 0     # [整数] 执行时间偏移（秒）
                                       # 用于避免多个任务同时执行
                                       # 例如：设置为30表示在每个间隔的第30秒执行

# --- 全局通用设置 ---
SYMBOL = 'BTCUSDT'              # [字符串] 默认交易对，当单个预测目标配置中未指定交易对时使用。
SCALER_TYPE = 'minmax'          # [字符串] 特征缩放器类型:
                                # 'minmax': MinMaxScaler，将特征缩放至0-1范围。
                                # 'standard': StandardScaler，将特征标准化 (均值为0，标准差为1)。
# === 分层数据策略配置 ===
ENABLE_LAYERED_DATA_STRATEGY = True  # [布尔] 是否启用分层数据策略（True=启用分层策略, False=使用传统单一数据获取）
DATA_FETCH_LIMIT = 7777        # [整数] 模型核心训练使用的短期数据条数（保证效率和近期市场敏感度）
LONG_TERM_DATA_LIMIT = 22222   # [整数] 长期上下文分析数据条数（用于样本权重、市场状态分析等）
MTFA_DATA_LIMIT = 7777         # [整数] 🎯 优化：与核心数据保持一致，提升MTFA特征质量

# 🎯 多地形作战配置 - 市场状态分层采样
ENABLE_REGIME_STRATIFIED_SAMPLING = True  # [布尔] 启用市场状态分层采样，强迫模型学习多种市场环境
REGIME_STRATIFIED_TRAIN_RATIO = 0.7       # [浮点数] 分层采样训练集比例
REGIME_STRATIFIED_VAL_RATIO = 0.15        # [浮点数] 分层采样验证集比例
REGIME_STRATIFIED_RANDOM_STATE = 42       # [整数] 分层采样随机种子

# === 全局数据管理器配置 ===
TRAINING_DATA_DAYS = 7         # [整数] 🎯 优化：减少全局数据获取天数，避免API限制（从30天减少到7天）
GLOBAL_DATA_LIMIT = 7777       # [整数] 🎯 优化：与短期数据保持一致，使用7777条数据获得更好的全局特征

# === K线数据缓存系统配置 ===
ENABLE_KLINE_CACHE = True      # [布尔] 是否启用K线数据缓存系统
KLINE_CACHE_DIR = "cache/kline_data"  # [字符串] 缓存目录路径
KLINE_CACHE_EXPIRE_HOURS = 2   # [整数] 缓存过期时间（小时）
KLINE_CACHE_MAX_DAYS = 3       # [整数] 缓存文件最大保留天数
TRAIN_RATIO = 0.7               # [浮点数, 0-1] 训练集在总获取数据中的占比。
VALIDATION_RATIO = 0.15         # [浮点数, 0-1] 验证集在总获取数据中的占比。
                                # 测试集占比将自动计算为: 1 - TRAIN_RATIO - VALIDATION_RATIO。



# --- SMOTE过采样全局配置 ---
SMOTE_GLOBAL_ENABLE = True      # [布尔值] 全局SMOTE开关，如果为False，所有目标都不会使用SMOTE
SMOTE_MIN_SAMPLES_THRESHOLD = 5 # [整数] 少数类样本的最小数量阈值，低于此值时跳过SMOTE
SMOTE_DEFAULT_K_NEIGHBORS = 4   # [整数] SMOTE默认的k_neighbors参数
SMOTE_RANDOM_STATE = 42         # [整数] SMOTE的随机种子，确保可重现性

# --- 完整阈值优化系统配置 ---
THRESHOLD_OPTIMIZATION_ENABLE = False          # [布尔值] 🎯 关闭基础模型阈值优化：只使用元模型预测
THRESHOLD_OPTIMIZATION_METHOD = "precision_constrained_recall"           # [字符串] 默认阈值优化方法:
                                               # "f1": 最大化F1分数
                                               # "precision_recall": 基于精确率-召回率曲线的最优点
                                               # "youden": Youden指数 (敏感性 + 特异性 - 1)
                                               # "balanced": 平衡精确率和召回率
                                               # "precision_constrained_recall": 在精确率约束下最大化召回率
                                               # "grid_search": 网格搜索优化
                                               # "bayesian": 贝叶斯优化

THRESHOLD_SAVE_TO_METADATA = True              # [布尔值] 是否将最优阈值保存到模型元数据
THRESHOLD_DEFAULT_VALUE = 0.5                  # [浮点数] 默认决策阈值
THRESHOLD_USE_INDEPENDENT_VALIDATION = True    # [布尔值] 是否使用独立验证集进行阈值优化（推荐）
THRESHOLD_INDEPENDENT_VAL_RATIO = 0.15         # [浮点数] 独立验证集比例（从训练集中分离）

# --- 阈值优化算法参数 ---
THRESHOLD_GRID_SEARCH_RANGE = (0.1, 0.9)      # [元组] 网格搜索的阈值范围
THRESHOLD_GRID_SEARCH_STEP = 0.05              # [浮点数] 网格搜索的步长
THRESHOLD_BAYESIAN_N_TRIALS = 100              # [整数] 贝叶斯优化的试验次数
THRESHOLD_BAYESIAN_TIMEOUT = 300               # [整数] 贝叶斯优化超时时间（秒）

# --- 阈值约束参数 ---
THRESHOLD_MIN_PRECISION = 0.65                  # [浮点数] 最小精确率约束
THRESHOLD_MIN_RECALL = 0.2                     # [浮点数] 最小召回率约束
THRESHOLD_PRECISION_CONSTRAINT_FALLBACK = True # [布尔值] 约束不满足时是否使用最接近的阈值

# --- 阈值加载和应用配置 ---
THRESHOLD_LOAD_RETRY_COUNT = 3                 # [整数] 阈值加载失败重试次数
THRESHOLD_FALLBACK_TO_DEFAULT = True           # [布尔值] 加载失败时是否回退到默认阈值
THRESHOLD_VALIDATION_ENABLE = True             # [布尔值] 是否验证加载的阈值有效性

# --- 独立阈值投票机制配置 ---
USE_INDIVIDUAL_FOLD_THRESHOLDS = True         # [布尔值] 是否启用独立阈值投票机制
                                               # True: 每个fold使用自己的最优阈值进行决策，然后投票
                                               # False: 使用传统的平均概率+统一阈值机制
INDIVIDUAL_THRESHOLD_VOTE_RATIO = 0.5          # [浮点数] 投票通过所需的比例（0.5表示超过一半）
INDIVIDUAL_THRESHOLD_MIN_VOTES = 3             # [整数] 投票通过所需的最小票数

# --- 交互特征和高阶特征配置 ---
ENABLE_INTERACTION_FEATURES = True             # [布尔值] 🎯 V5.0优化：重新启用交互特征，寻找普适规律
                                               # 包括：量价结合、波动率与趋势结合、K线实体与波动率结合等
ENABLE_HIGHER_ORDER_FEATURES = True            # [布尔值] 🎯 V5.0优化：重新启用高阶特征，强化模型认知
                                               # 包括：RSI变化率、MACD柱状图加速度、价格变化加速度等

# 🎯 新增：市场状态自适应特征工程配置 - 核心优化建议2.1
MARKET_STATE_ADAPTIVE_FEATURES_CONFIG = {
    "enable": True,                              # [布尔值] 是否启用市场状态自适应特征

    # 市场状态识别参数
    "market_state_atr_period": 14,               # [整数] ATR计算周期
    "market_state_adx_period": 14,               # [整数] ADX计算周期
    "market_state_ema_fast": 12,                 # [整数] 快速EMA周期
    "market_state_ema_slow": 26,                 # [整数] 慢速EMA周期

    # 市场状态阈值
    "market_state_high_vol_threshold": 2.5,      # [浮点数] 高波动ATR阈值
    "market_state_extreme_vol_threshold": 4.0,   # [浮点数] 极端波动ATR阈值
    "market_state_low_trend_threshold": 20,      # [浮点数] 低趋势强度ADX阈值
    "market_state_strong_trend_threshold": 30,   # [浮点数] 强趋势ADX阈值
    "market_state_extreme_trend_threshold": 50,  # [浮点数] 极端趋势ADX阈值

    # 🎯 新增：动量和反转识别阈值
    "momentum_strength_threshold": 0.7,          # [浮点数] 动量强度阈值
    "reversal_signal_threshold": 0.8,            # [浮点数] 反转信号阈值
    "exhaustion_rsi_upper": 80,                  # [浮点数] 趋势衰竭RSI上限
    "exhaustion_rsi_lower": 20,                  # [浮点数] 趋势衰竭RSI下限
    "breakout_volume_multiplier": 1.5,           # [浮点数] 突破成交量倍数

    # 交互特征配置
    "market_state_interaction_indicators": [     # [列表] 要进行交互的技术指标
        # 基础技术指标
        'rsi', 'macd', 'macd_histogram', 'bb_position',
        'williams_r', 'volume_ratio', 'close_pos_in_candle',

        # 🎯 新增：SHAP证明有效的核心特征（来自Top 10重要特征）
        'meta_prob_diff_up_vs_down',              # SHAP #1: 最重要的概率差异特征
        'meta_prob_sum_up_down',                  # SHAP #2: 概率总和特征
        'global_ema_short',                       # SHAP #3: 全局短期EMA
        'global_mdi',                             # SHAP #4: 全局负向指标
        'global_ema_long',                        # SHAP #5: 全局长期EMA
        'global_adx',                             # SHAP #6: 全局ADX趋势强度
        'global_pdi',                             # SHAP #7: 全局正向指标
        'global_ema_diff_pct',                    # SHAP #8: 全局EMA差异百分比
        'global_price_ema_distance_pct',          # SHAP #9: 价格与EMA距离百分比
        'global_ema_slope_short',                 # SHAP #10: 全局短期EMA斜率

        # 🎯 新增：高阶特征（速度和加速度）
        'rsi_velocity', 'rsi_acceleration', 'macd_hist_acceleration',
        'volume_change_accel', 'atr_velocity', 'atr_acceleration',
        'adx_velocity', 'adx_acceleration', 'price_change_accel',

        # 🎯 新增：多时间框架高阶动态特征
        'rsi_velocity_4h',                        # 4小时RSI速度
        'macd_hist_acceleration_1h',              # 1小时MACD柱状图加速度
        'volume_momentum_30m',                    # 30分钟成交量动量
        'price_momentum_2h'                       # 2小时价格动量
    ],
    "market_state_interaction_states": [         # [列表] 要进行交互的市场状态
        # 🎯 基础趋势状态
        'strong_uptrend', 'strong_downtrend', 'weak_uptrend', 'weak_downtrend',

        # 🎯 波动率状态
        'high_vol_sideways', 'low_vol_sideways', 'extreme_volatility',

        # 🎯 确定性状态
        'high_certainty', 'low_certainty', 'extreme_uncertainty',

        # 🎯 新增：复合市场制度（更精细的状态识别）
        'bull_momentum',                          # 牛市动量状态
        'bear_momentum',                          # 熊市动量状态
        'consolidation_breakout',                 # 整理突破状态
        'trend_exhaustion',                       # 趋势衰竭状态
        'reversal_signal',                        # 反转信号状态
        'accumulation_phase',                     # 吸筹阶段
        'distribution_phase'                      # 派发阶段
    ],

    # 市场确定性特征参数
    "market_certainty_window": 10,               # [整数] 确定性计算窗口
}

# 🎯 新增：基于SHAP的智能特征选择配置 - 挖掘更深的"阿尔法"
SHAP_FEATURE_SELECTION_CONFIG = {
    "enable": True,                              # [布尔值] 是否启用SHAP特征选择
    "min_importance_threshold": 0.001,           # [浮点数] 最小重要性阈值，低于此值的特征将被过滤
    "top_k_features": None,                      # [整数或None] 保留前K个最重要的特征，None表示不限制
    "importance_percentile": 0.1,                # [浮点数] 重要性百分位数阈值（0.1表示过滤掉最低10%的特征）
    "save_selection_results": True,              # [布尔值] 是否保存特征选择结果
    "selection_results_file": "shap_feature_selection.json",  # [字符串] 选择结果保存文件名
    "use_cached_selection": True,                # [布尔值] 是否使用缓存的选择结果（如果存在）
    "fallback_to_rfe": True,                     # [布尔值] 当SHAP选择失败时是否回退到RFE
    "apply_to_base_models": True,                # [布尔值] 是否对基础模型应用SHAP特征选择
    "apply_to_meta_model": True,                 # [布尔值] 是否对元模型应用SHAP特征选择
}

# 🌍 元模型"极简路由器"配置 - 激进简化：纯信号融合
META_MODEL_FORCE_INCLUDE_FEATURES = [
    # --- 🎯 核心基础模型信号 (82-84%准确率) ---
    'oof_proba_BTC_15m_UP_p_up',               # 核心：UP模型预测概率
    'oof_proba_BTC_15m_DOWN_p_down',           # 核心：DOWN模型预测概率
    'meta_prob_diff_up_vs_down',               # 核心：概率差异 (决策关键)
    'meta_prob_sum_up_down',                   # 核心：概率总和 (置信度指标)

    # --- 📈 基础模型共识特征 ---
    'models_consensus_score',                   # 模型共识度
    'models_direction_consensus',               # 方向共识

    # --- ⏰ 时序稳定性特征 ---
    'meta_lag1_oof_proba_BTC_15m_UP_p_up',     # 滞后UP概率 (趋势确认)
    'meta_lag1_oof_proba_BTC_15m_DOWN_p_down', # 滞后DOWN概率 (趋势确认)

    # --- 🌍 最小环境过滤器 (仅保留最关键的波动率指标) ---
    'global_atr_percent',                       # 波动率百分比 (交易适宜性判断)
]

# 🧠 元模型特征选择策略配置 - 极简路由器：专注信号融合
META_MODEL_FEATURE_STRATEGY = {
    "enable_force_include": True,                # [布尔值] 是否启用强制保留特征策略
    "force_include_features": META_MODEL_FORCE_INCLUDE_FEATURES,  # [列表] 强制保留的特征列表
    "max_features_after_selection": 10,         # [整数] 🎯 二分类优化：严格限制最终特征数量在10个以内
    "prioritize_global_features": False,        # [布尔值] 🎯 关键：不优先保留全局特征
    "global_feature_min_ratio": 0.1,           # [浮点数] 🎯 二分类优化：保留一个核心的宏观波动率指标
}

# 🎯 V11.0 弃用：动态交易过滤器配置 - 已被统一的 PredictionFilter 替代
# 注意：此配置已不再使用，所有过滤逻辑现在统一由 PredictionFilter 处理
# 保留此配置仅为向后兼容，建议在各目标配置中使用 PredictionFilter 的配置参数
DYNAMIC_TRADING_FILTER_CONFIG = {
    "enable": True,                              # [布尔值] 是否启用动态交易过滤器
    "enable_market_state_filter": True,         # [布尔值] 是否启用市场状态过滤
    "enable_volatility_filter": True,           # [布尔值] 是否启用波动率过滤
    "enable_trend_consistency_filter": True,    # [布尔值] 是否启用趋势一致性过滤
    "enable_confidence_adjustment": True,       # [布尔值] 是否启用置信度调整

    # 阻止信号的条件
    "block_on_high_vol_sideways": True,         # [布尔值] 高波动盘整时阻止信号
    "block_danger_score_threshold": 0.8,        # [浮点数] 危险评分阈值，超过则阻止
    "block_on_extreme_volatility": True,        # [布尔值] 极端波动时阻止
    "extreme_volatility_atr_threshold": 4.0,    # [浮点数] 极端波动ATR阈值

    # 降低置信度的条件
    "reduce_confidence_danger_threshold": 0.5,  # [浮点数] 危险评分阈值，超过则降低置信度
    "confidence_reduction_factor": 0.7,         # [浮点数] 置信度降低因子
    "reduce_on_moderate_volatility": True,      # [布尔值] 中等波动时降低置信度

    # 趋势一致性检查
    "require_trend_signal_alignment": True,     # [布尔值] 要求信号与趋势方向一致
    "trend_alignment_adx_threshold": 25,        # [浮点数] 趋势一致性检查的ADX阈值

    # 市场状态分析器配置
    "market_analyzer_config": {
        "high_volatility_atr_threshold": 2.5,
        "low_volatility_atr_threshold": 0.5,  # ✅ 正确！必须放在这个花括号里面
        "low_trend_strength_adx_threshold": 20,
        "strong_trend_adx_threshold": 30,
        "sideways_price_range_threshold": 0.05,
        "price_range_lookback_periods": 20,
    }
}

# --- 模拟盘与指令服务器集成 ---
SIMULATOR_INTEGRATION_ENABLED = True # [布尔值] V11.0: 保持启用，但基础模型已通过return_core_prediction_only静默
SIMULATOR_API_URL = "http://127.0.0.1:5008/signal" # [字符串] 模拟盘接收预测信号的API端点URL。请确保端口与SimMain实例匹配。
COMMAND_SERVER_URL = "http://127.0.0.1:8080/internal_signal" # [字符串] 指令服务器 (CommandServer.py) 接收预测信号的API端点URL，用于对接Hamibot等外部执行系统。
# SIGNAL_SEND_COOLDOWN_SECONDS = 120 # [整数, 可选] 全局信号发送冷却时间（秒）。如果启用，预测系统在发送一个信号后，会等待这段时间才能发送下一个。prediction.py 中有更细致的per-target冷却。

# --- iPhone SSH交易执行配置 ---
IPHONE_SSH_ENABLED = True # [布尔值] 是否启用iPhone SSH交易执行
IPHONE_TRADING_SIGNAL_ENABLED = True # [布尔值] 是否启用预测程序向iPhone发送交易信号
                                      # True: 当接收到元模型信号或测试信号时，自动执行iPhone自动化交易
                                      # False: 禁用iPhone交易信号发送，只在模拟盘内部处理信号
IPHONE_SSH_CONFIG = {
    "host": "*************",  # [字符串] iPhone的IP地址，请替换为您的iPhone实际IP
    "port": 22,               # [整数] SSH端口，通常为22
    "username": "root",       # [字符串] SSH用户名，越狱iPhone通常为root
    "password": "your_ssh_password_here",  # [字符串] SSH密码，请替换为您的实际密码
    "timeout": 10,            # [整数] SSH连接超时时间（秒）
    "script_path": "/var/mobile/trade_executor.py"  # [字符串] iPhone上交易脚本的路径
}

# --- GUI 与声音相关 ---
ALERT_SOUND_ENABLED = True      # [布尔值] 是否启用交易信号的声音提示。
CUSTOM_UP_SIGNAL_SOUND_PATH = "sounds/signal_up.mp3"      # [字符串] 做多信号提示音文件的路径。
CUSTOM_DOWN_SIGNAL_SOUND_PATH = "sounds/signal_down.mp3"  # [字符串] 做空信号提示音文件的路径。
CUSTOM_SIGNAL_SOUND_DURATION_MS = 20000 # [整数] 交易信号提示音的播放时长 (毫秒)。pygame会在这个时长后尝试淡出音乐。

# --- GUI 颜色主题 ---
# 定义了预测程序GUI界面的各种颜色，用于美化和信息区分。
BG_COLOR = "#2E2E2E"        # 主要背景色
FG_COLOR = "#E0E0E0"        # 主要前景色 (文本颜色)
LABEL_BG_COLOR = "#3C3C3C"  # 标签控件的背景色
BUTTON_COLOR = "#4A4A4A"    # 按钮背景色
BUTTON_FG_COLOR = "#FFFFFF" # 按钮文字颜色
UP_COLOR = "#26A69A"        # 上涨趋势/信号的颜色 (通常为绿色系)
DOWN_COLOR = "#EF5350"      # 下跌趋势/信号的颜色 (通常为红色系)
NEUTRAL_COLOR = "#FFCA28"   # 中性状态/信号的颜色 (通常为黄色系)
TEXT_AREA_BG = "#1E1E1E"    # 滚动文本区域的背景色
TEXT_AREA_FG = "#D0D0D0"    # 滚动文本区域的文字颜色
ERROR_COLOR = "#FF5252"     # 错误信息的颜色
BLUE_COLOR_BUTTON = "#007BFF" # 一个标准的蓝色，用于某些特殊按钮

# --- 元模型 Stacking 配置 ---
ENABLE_META_MODEL_TRAINING = True     # [布尔值] 是否启用元模型的OOF (Out-of-Fold) 特征生成和后续的元模型训练流程。
                                      # ✅ 已修复模型文件名匹配问题，重新启用元模型训练
META_MODEL_SAVE_DIR = "meta_model_data" # [字符串] 元模型本身、其使用的特征列表以及基础模型OOF预测数据保存的根目录。
ENABLE_META_MODEL_PREDICTION = True   # [布尔值] 是否在实时预测时使用元模型进行最终决策。
                                      # 如果为False，系统将按原方式独立预测和处理每个基础模型的信号。

# 定义用于元模型训练的基础模型列表
BASE_MODELS_FOR_META = [
    BaseModelNames.BTC_15M_UP,
    BaseModelNames.BTC_15M_DOWN,
    # BaseModelNames.BTC_15M_LSTM  # ❌ LSTM模型已关闭，不参与元模型训练
]  # [列表] 用于元模型训练的基础模型名称列表，必须与PREDICTION_TARGETS中的name字段匹配

# OOF (Out-of-Fold) 预测生成参数 (用于基础模型，为元模型准备特征)
META_MODEL_OOF_CV_FOLDS = 5                # [整数] 生成OOF预测时，对每个基础模型数据进行交叉验证的折数。
META_MODEL_OOF_LGBM_N_ESTIMATORS = 3000    # [整数] 在OOF生成过程中，每折训练基础模型时LightGBM的基础树数量 (通常配合早停使用)。
META_MODEL_OOF_LGBM_EARLY_STOPPING_ROUNDS = 75 # [整数] OOF生成过程中，每折LightGBM训练的早停轮数。
META_MODEL_OOF_VALID_FOR_EARLY_STOP_RATIO = 0.15 # [浮点数, 0-1] 在OOF的每一折训练中，从该折的训练数据中再划分出的、用于早停的内部验证集比例。
META_MODEL_OOF_MIN_SAMPLES_FOR_EARLY_STOP_VALID = 50 # [整数] OOF早停内部验证集所需的最少样本数。

# 元模型 (LightGBM 分类器) 自身训练参数
META_MODEL_LGBM_OBJECTIVE = 'binary'                 # [字符串] 🎯 二元期权：使用二分类目标函数
# META_MODEL_LGBM_NUM_CLASS 已移除 - 二分类不需要此参数
META_MODEL_LGBM_METRIC = 'binary_logloss'           # [字符串] 🎯 二元期权：使用二分类评估指标
META_MODEL_LGBM_CLASS_WEIGHT = 'balanced'     # 🚨 修复极端权重问题：使用balanced自动计算合理权重，避免6:1极端偏向
# 🎯 平衡策略优化: 引入"共识度"特征后回归平衡权重
# - 轻微鼓励涨跌信号关注(1.2)，允许模型在"看不清"时安全选择中性(1.0)
# - 配合新的共识度特征，让模型学会基于信号质量做决策，而非依赖极端权重
#
# 📊 优化历程:
# 1. ✅ 已完成LGBM本身优化 (类别权重 + Optuna指标优化)
# 2. ✅ 已完成概率质量提升 (custom_f1_class01_avg指标)
# 3. ✅ 引入共识度特征 + 平衡权重策略 (当前版本)
# 3. ✅ 已完成EMA特征增强 (新增7个高信息量EMA衍生特征)
# 4. ✅ 已完成阈值优化重新启用 (composite_score策略，500次试验)
# 🎯 V17.0 二分类模型：类别权重策略已简化
# 注意：旧的三分类权重策略（阶段1-5）已移除，现在使用二分类的 'balanced' 策略
# - 最终目标: 提升交易信号的全面性和盈利能力
META_MODEL_LGBM_N_ESTIMATORS = 3000                  # [整数] 🎯 最新优化：保持3000棵树，提供充分学习能力
META_MODEL_LGBM_LEARNING_RATE = 0.021601817057284978 # [浮点数] 🎯 最新优化：精确学习率，平衡学习速度和精度
META_MODEL_LGBM_NUM_LEAVES = 12                     # [整数] 🎯 最新优化：减少叶子数，防止过拟合
META_MODEL_LGBM_MAX_DEPTH = 3                       # [整数] 🎯 最新优化：降低深度，提高泛化能力
META_MODEL_LGBM_REG_ALPHA = 5.444071690783628       # [浮点数] 🎯 最新优化：L1正则化系数
META_MODEL_LGBM_REG_LAMBDA = 9.155900815022385      # [浮点数] 🎯 最新优化：L2正则化系数
META_MODEL_LGBM_COLSAMPLE_BYTREE = 0.8913677394340551 # [浮点数, 0-1] 🎯 最新优化：特征采样比例
META_MODEL_LGBM_SUBSAMPLE = 0.8365825309508274      # [浮点数, 0-1] 🎯 最新优化：样本采样比例
META_MODEL_LGBM_MIN_CHILD_SAMPLES = 59              # [整数] 🎯 最新优化：增加最小叶子样本数，防止过拟合
META_MODEL_LGBM_RANDOM_STATE = 2024                 # [整数] 元模型训练的随机种子。
META_MODEL_LGBM_VERBOSE = 1                         # [整数] LightGBM训练日志详细程度: -1静默, 0警告, 1信息。
META_MODEL_TRAIN_TEST_SPLIT_FOR_EVAL_RATIO = 0.2    # [浮点数, 0-1, 可选] 从OOF数据中划分出一部分作为元模型最终评估的测试集比例。
META_MODEL_LGBM_EARLY_STOPPING_ROUNDS_FINAL = 20    # [整数] 元模型最终训练时（如果使用了上述划分的验证集）的早停轮数。
META_MODEL_MAX_FEATURES = 50                         # [整数] 🎯 特征选择：元模型最大特征数量，超过此数量将进行特征选择

# --- 元模型 SMOTE 配置 ---
META_MODEL_SMOTE_ENABLE = True                       # [布尔值] 是否为元模型启用SMOTE过采样
META_MODEL_SMOTE_K_NEIGHBORS = 4                     # [整数] 元模型SMOTE的k_neighbors参数
META_MODEL_SMOTE_MIN_SAMPLES_THRESHOLD = 5           # [整数] 元模型SMOTE的最小样本阈值
META_MODEL_SMOTE_RANDOM_STATE = 42                   # [整数] 元模型SMOTE的随机种子

# --- 元模型 Optuna 配置 ---
META_MODEL_OPTUNA_ENABLE = True               # [布尔值] 是否为元模型启用Optuna超参数优化
# 🎯 重新启用Optuna，专注于Class 1性能优化:
# - 使用增强的custom_f1_class01_avg指标
# - 结合类别权重调整，寻找最佳超参数组合
# - 确保指标计算稳定可靠
META_MODEL_OPTUNA_N_TRIALS = 200             # [整数] 🎯 增加试验次数以充分探索统一决策逻辑下的参数空间
META_MODEL_OPTUNA_TIMEOUT = None              # [整数 或 None] Optuna运行的最大秒数。None表示不限制时间。
META_MODEL_OPTUNA_METRIC = 'custom_f1_class01_avg'  # [字符串] ✅ 修复：使用Class 0和1的F1平均值，解决多分类ROC AUC失效问题
# 🎯 核心优化建议2.2：全流程盈利导向优化目标
#
# 💰 盈利能力导向指标 (推荐，已统一):
# 1. 'simulated_profit_expected' (direction='maximize') - 直接优化期望收益，最直接的盈利目标
# 2. 'simulated_profit_risk_adjusted' (direction='maximize') - 风险调整收益，考虑交易频率
# 3. 'simulated_profit_composite' (direction='maximize') - 复合盈利指标，综合收益、胜率、频率 (🎯 推荐使用)
# 4. 'simulated_profit_sharpe' (direction='maximize') - 基于模拟交易的夏普比率
#
# 📊 技术指标 (向后兼容，不推荐):
# 5. 'custom_f1_class01_avg' (direction='maximize') - Class 0和1的F1分数平均值
# 6. 'multi_logloss' (direction='minimize') - 基础LogLoss优化，提升整体概率质量
# 7. 'custom_precision_class01_avg' (direction='maximize') - Class 0和1的精确率平均值
# 8. 'custom_precision_class01_weighted' (direction='maximize') - 加权精确率，更侧重Class 1 (做多)
# 9. 'custom_f1_class01_weighted' (direction='maximize') - 加权F1分数，更侧重Class 1 (做多)
# 10. 'custom_recall_class01_avg' (direction='maximize') - Class 0和1的召回率平均值
#
# 🔧 传统指标:
# - 'macro_f1_score', 'weighted_f1_score', 'val_accuracy'
META_MODEL_OPTUNA_DIRECTION = "maximize"      # [字符串] Optuna优化目标指标的方向: "minimize" 用于multi_logloss优化。
META_MODEL_OPTUNA_CV_FOLDS = 3                # [整数] Optuna在评估每个试验参数集时，内部进行交叉验证的折数 (使用TimeSeriesSplit)。
META_MODEL_OPTUNA_LGBM_N_ESTIMATORS_MAX = 300 # [整数] Optuna单次试验中，元模型LGBM允许训练的最大树数量 (通常配合早停)。
META_MODEL_OPTUNA_LGBM_EARLY_STOPPING_ROUNDS = 30 # [整数] Optuna单次试验中，元模型LGBM训练的早停轮数。

# --- 元模型特征工程配置 (高优先级优化) - 已移至下方统一配置 ---
META_MODEL_OPTUNA_LGBM_EVAL_METRIC = 'multi_logloss' # [字符串] Optuna单次试验中，元模型LGBM早停所依据的评估指标。

# --- 元模型决策阈值优化配置 ---
META_MODEL_THRESHOLD_OPTIMIZATION_ENABLE = True          # [布尔值] 🎯 新增：启用元模型阈值优化
META_MODEL_THRESHOLD_OPTIMIZATION_METHOD = 'optuna_kiss'      # [字符串] 优化方法: 'optuna_kiss'(V29.0推荐，纯概率), 'optuna'(复杂过滤), 'youden'
META_MODEL_THRESHOLD_OPTIMIZATION_N_TRIALS = 1000       # [整数] 🎯 二分类修复：增加到1000以充分探索新的参数空间
META_MODEL_THRESHOLD_OPTIMIZATION_TIMEOUT = 2400         # [整数] 🎯 二分类修复：增加到40分钟，给予更多优化时间
META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY = 'composite_score'  # [字符串] 优化策略:

# --- 元模型概率校准配置 ---
META_MODEL_PROBABILITY_CALIBRATION_ENABLE = True         # [布尔值] 🎯 新增：启用元模型概率校准
META_MODEL_CALIBRATION_METHOD = 'sigmoid'                # [字符串] 校准方法: 'sigmoid' (Platt scaling) 或 'isotonic'
META_MODEL_CALIBRATION_CV = 3                           # [整数] 校准时的交叉验证折数
META_MODEL_CALIBRATION_IMPROVEMENT_THRESHOLD = 0.001     # [浮点数] Brier分数改善阈值
# 🎯 核心优化策略 (已优化，推荐使用):
# - 'profit_first': 优先最大化总盈利 (隐式鼓励更多正收益交易)
# - 'balanced_profit_frequency': 风险调整收益 (收益 × √交易次数)
# - 'frequency_under_winrate_constraint': 胜率约束下最大化交易频率 (胜率>55%时优先频率)
# - 'composite_score': 复杂效用函数，正收益时鼓励频率，负收益时重惩罚 (最推荐)
# - 'risk_adjusted': 风险调整收益 (考虑夏普比率)
# - 'total_expected_profit': 综合指标 (总收益+风险调整+胜率×频率)
#
# 🔧 传统策略 (向后兼容):
# - 'expected_profit': 纯期望收益最大化
# - 'risk_adjusted_return': 风险调整收益
# - 'win_rate_weighted': 胜率加权的期望收益
# - 'f1_weighted': F1分数加权的期望收益
# - 'sharpe_like': 类似夏普比率

# --- 🎯 V17.0 二分类元模型：简化的阈值配置 ---
# 注意：旧的三分类相关参数（CONFIDENCE_GAP等）已移除，因为二分类模型不需要这些参数

# --- 优化约束配置（基于0交易的紧急调整）---
META_MODEL_MIN_TRADES_CONSTRAINT = 10                     # 🎯 优化：大幅降低最小交易次数，从30降到10
META_MODEL_MIN_WIN_RATE_CONSTRAINT = 0.45                 # 🎯 优化：降低胜率要求，从0.55降到0.45
META_MODEL_MAX_CONSECUTIVE_LOSSES = 12                     # 🎯 优化：放宽连续亏损限制，从8提高到12
META_MODEL_ENABLE_EARLY_STOPPING = True                  # [布尔值] 是否启用早停机制

# --- Optuna优化增强配置 ---
# META_MODEL_OPTUNA_DIRECTION 已在上面定义，这里不重复
META_MODEL_OPTUNA_SAMPLER_N_STARTUP_TRIALS = 30          # [整数] TPE采样器启动试验数 (前10%用于随机探索)
META_MODEL_OPTUNA_EARLY_STOP_PATIENCE = 50               # [整数] 早停耐心值 (连续N次无改进则停止)

# --- Optuna约束惩罚配置 ---
OPTUNA_CONSTRAINT_PENALTY_BASE = -100.0                  # [浮点数] 约束违反基础惩罚值 (减小惩罚，允许更多探索)
OPTUNA_CONSTRAINT_PENALTY_MULTIPLIER = 1.0               # [浮点数] 约束违反惩罚倍数 (减小倍数)

# 🎯 V17.1 元模型Optuna搜索空间 - 基于优化结果调整范围
META_MODEL_OPTUNA_PARAM_GRID = {
    'learning_rate': ('float', 0.02, 0.035, True),  # 🎯 V17.1：围绕0.027优化值调整范围
    'num_leaves': ('int', 10, 20),                  # 🎯 V17.1：围绕15优化值调整范围
    'max_depth': ('int', 3, 5),                     # 🎯 V17.1：围绕3优化值调整范围
    'reg_alpha': ('float', 3.0, 8.0, True),        # 🎯 V17.1：围绕5.22优化值调整范围
    'reg_lambda': ('float', 4.0, 10.0, True),      # 🎯 V17.1：围绕6.60优化值调整范围
    'colsample_bytree': ('float', 0.7, 0.9),       # 🎯 V17.1：围绕0.795优化值调整范围
    'subsample': ('float', 0.8, 0.9),              # 🎯 V17.1：围绕0.853优化值调整范围
    'min_child_samples': ('int', 35, 60)           # 🎯 V17.1：围绕47优化值调整范围
    # 'class_weight': ('categorical', [None, 'balanced']) # 固定为'balanced'以保持二分类平衡
}

# --- 统一特征工程配置 ---
# 🎯 新增：统一特征工程接口配置

# 基础模型特征工程配置
BASIC_MODEL_FEATURE_CONFIG = {
    'enable_price_features': True,           # [布尔值] 是否启用价格变化特征
    'enable_volume_features': True,          # [布尔值] 是否启用成交量特征
    'enable_candle_features': True,          # [布尔值] 是否启用K线形态特征
    'enable_technical_indicators': True,     # [布尔值] 是否启用技术指标特征
    'enable_time_features': True,            # [布尔值] 是否启用时间特征
    'enable_fund_flow_features': True,       # [布尔值] 是否启用资金流向特征
    'enable_trend_features': True,           # [布尔值] 是否启用趋势特征
    'enable_interaction_features': True,     # [布尔值] 🎯 V5.0优化：重新启用交互特征，寻找普适规律
    'enable_higher_order_features': True,    # [布尔值] 🎯 V5.0优化：重新启用高阶特征，强化模型认知
    'enable_market_state_features': True,    # [布尔值] 是否启用市场状态特征

    # 数据验证和清理配置
    'nan_fill_strategy': 'median',           # [字符串] NaN值填充策略: 'median', 'mean', 'zero', 'forward'
    'inf_fill_strategy': 'clip',             # [字符串] Inf值处理策略: 'clip', 'zero'
    'enable_outlier_clipping': True,         # [布尔值] 是否启用异常值裁剪
    'outlier_clip_quantile': 0.99,           # [浮点数] 异常值裁剪分位数
    'enable_feature_validation': True,       # [布尔值] 是否启用特征验证
}

# 元模型特征工程配置（与现有META_MODEL_FEATURE_ENGINEERING_CONFIG整合）
UNIFIED_META_MODEL_FEATURE_CONFIG = {
    'enable_prob_diff': True,                # [布尔值] 是否启用概率差异特征
    'enable_prob_sum': True,                 # [布尔值] 是否启用概率总和特征
    'enable_lag_features': True,             # [布尔值] 是否启用滞后特征
    'enable_change_features': True,          # [布尔值] 是否启用变化特征
    'enable_global_features': True,          # [布尔值] 是否启用全局市场特征
    'enable_context_features': True,         # [布尔值] 是否启用基础模型上下文特征
    'enable_model_divergence': True,         # [布尔值] 是否启用基础模型间分歧度特征
    'enable_confidence_features': True,      # [布尔值] 是否启用置信度相关特征
    'enable_market_regime_features': True,   # [布尔值] 是否启用市场状态特征

    # 滞后和变化特征配置
    'lag_periods': [1],                      # [列表] 滞后期数列表
    'change_periods': [1],                   # [列表] 变化期数列表

    # 数据验证和清理配置
    'nan_fill_strategy': 'median',           # [字符串] NaN值填充策略
    'inf_fill_strategy': 'clip',             # [字符串] Inf值处理策略
    'enable_outlier_clipping': True,         # [布尔值] 是否启用异常值裁剪
    'outlier_clip_quantile': 0.99,           # [浮点数] 异常值裁剪分位数
    'enable_feature_validation': True,       # [布尔值] 是否启用特征验证
}

# 特征工程全局配置
FEATURE_ENGINEERING_GLOBAL_CONFIG = {
    'use_unified_interface': True,           # [布尔值] 是否使用统一特征工程接口
    'enable_feature_caching': True,          # [布尔值] 是否启用特征缓存
    'enable_parallel_processing': False,     # [布尔值] 是否启用并行处理（实验性）
    'feature_consistency_check': True,       # [布尔值] 是否启用特征一致性检查
    'log_feature_statistics': True,          # [布尔值] 是否记录特征统计信息
}



# --- 元模型输入特征与预过滤配置 ---
# 🎯 丰富化元模型特征输入：不仅包含基础模型概率，还包含决策上下文特征
# 🔧 修复OOF特征语义：明确区分UP模型的"上涨概率"和DOWN模型的"下跌概率"
META_MODEL_INPUT_FEATURES_CONFIG = [
    # --- UP模型特征 ---
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": ProbabilityTypes.P_FAVORABLE,
        "meta_feature_name": "oof_proba_BTC_15m_UP_p_up"  # 明确表示这是上涨概率
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.atr_percent",
        "meta_feature_name": "feat_atr_pct_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.adx_value",
        "meta_feature_name": "feat_adx_val_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.prediction_confidence",
        "meta_feature_name": "feat_confidence_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.rsi_value",
        "meta_feature_name": "feat_rsi_val_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.volume_ratio",
        "meta_feature_name": "feat_volume_ratio_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.price_change_1p",
        "meta_feature_name": "feat_price_change_1p_up_model"
    },

    # --- DOWN模型特征 ---
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": ProbabilityTypes.P_FAVORABLE,
        "meta_feature_name": "oof_proba_BTC_15m_DOWN_p_down"  # 明确表示这是下跌概率
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.atr_percent",
        "meta_feature_name": "feat_atr_pct_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.adx_value",
        "meta_feature_name": "feat_adx_val_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.prediction_confidence",
        "meta_feature_name": "feat_confidence_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.rsi_value",
        "meta_feature_name": "feat_rsi_val_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.volume_ratio",
        "meta_feature_name": "feat_volume_ratio_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.price_change_1p",
        "meta_feature_name": "feat_price_change_1p_down_model"
    },

    # --- LSTM模型特征（已关闭） ---
    # {
    #     "base_model_name": BaseModelNames.BTC_15M_LSTM,
    #     "prob_type": ProbabilityTypes.P_FAVORABLE,
    #     "meta_feature_name": "oof_proba_BTC_15m_LSTM_pfavorable"
    # },
    # {
    #     "base_model_name": BaseModelNames.BTC_15M_LSTM,
    #     "prob_type": "context_features.prediction_confidence",
    #     "meta_feature_name": "feat_confidence_lstm_model"
    # },
    # {
    #     "base_model_name": BaseModelNames.BTC_15M_LSTM,
    #     "prob_type": "context_features.atr_percent",
    #     "meta_feature_name": "feat_atr_pct_lstm_model"
    # },
    # {
    #     "base_model_name": BaseModelNames.BTC_15M_LSTM,
    #     "prob_type": "context_features.rsi_value",
    #     "meta_feature_name": "feat_rsi_val_lstm_model"
    # },
    # {
    #     "base_model_name": BaseModelNames.BTC_15M_LSTM,
    #     "prob_type": "context_features.volume_ratio",
    #     "meta_feature_name": "feat_volume_ratio_lstm_model"
    # }
]

# 🎯 核心优化建议2.4：元模型特征工程配置
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    "enable_model_divergence": True,                     # [布尔值] 是否启用基础模型间分歧度特征
    "enable_confidence_features": True,                  # [布尔值] 是否启用综合置信度特征
    "enable_market_state_features": True,                # [布尔值] 是否启用市场状态特征
    "enable_interaction_features": True,                 # [布尔值] 是否启用交互特征

    # 🛡️ 信息屏蔽配置
    "enable_signal_shielding": True,                     # [布尔值] 是否启用反向信号屏蔽功能
    "shield_up_model_down_signal": True,                 # [布尔值] 是否屏蔽UP模型的下跌信号
    "shield_down_model_up_signal": True,                 # [布尔值] 是否屏蔽DOWN模型的上涨信号
    "preserve_lstm_signals": False,                      # [布尔值] LSTM模型已关闭，此选项无效

    # 分歧度特征配置
    "divergence_features": [
        "model_divergence_up",                           # 上涨概率标准差
        "model_divergence_down",                         # 下跌概率标准差
        "model_divergence_favorable",                    # 有利概率标准差
        "model_max_diff_up",                            # 上涨概率最大差异
        "model_max_diff_down"                           # 下跌概率最大差异
    ],

    # 置信度特征配置
    "confidence_features": [
        "avg_model_confidence",                          # 平均置信度
        "max_model_confidence",                          # 最大置信度
        "min_model_confidence",                          # 最小置信度
        "confidence_spread"                              # 置信度差异
    ],

    # 交互特征配置
    "interaction_features": [
        "prob_confidence_product",                       # 概率×置信度乘积
        "prob_volatility_ratio",                        # 概率/波动率比值
        "confidence_divergence_ratio"                    # 置信度/分歧度比值
    ],

    # 🎯 紧急修复：增强上涨信号捕获特征
    'enable_bullish_bias_features': True,               # [布尔值] 启用上涨偏向特征
    'bullish_momentum_threshold': 0.6,                  # [浮点数] 上涨动量阈值
    'enable_volatility_adjusted_probs': True,           # [布尔值] 启用波动率调整概率
    'volatility_boost_factor': 1.5                      # [浮点数] 高波动时上涨信号增强因子
}

# 元模型实时预测的预过滤配置
META_MODEL_PRE_FILTER_UP_CONFIG = {
    "base_model_name": BaseModelNames.BTC_15M_UP,    # 必须是 META_MODEL_INPUT_FEATURES_CONFIG 中某一项的 base_model_name
    "prob_type": ProbabilityTypes.P_FAVORABLE,  # 用于预过滤判断的概率类型
    "threshold": 0.05
}

META_MODEL_PRE_FILTER_DOWN_CONFIG = {
    "base_model_name": BaseModelNames.BTC_15M_DOWN,  # 必须是 META_MODEL_INPUT_FEATURES_CONFIG 中某一项的 base_model_name
    "prob_type": ProbabilityTypes.P_FAVORABLE,
    "threshold": 0.05
}
# 预过滤逻辑: 如果 META_MODEL_PRE_FILTER_UP_CONFIG 指定的概率 < threshold AND META_MODEL_PRE_FILTER_DOWN_CONFIG 指定的概率 < threshold，则预判为中性。

# 定义在创建元模型的目标变量时，使用哪个基础模型的配置参数
# (如 prediction_periods, target_threshold) 来确定“明确上涨/下跌/中性”的定义。
BASE_CONFIG_FOR_META_TARGET_DEFINITION = BaseModelNames.BTC_15M_UP # 或者 BaseModelNames.BTC_15M_DOWN，取决于你认为哪个更能代表元目标周期。

# 元模型相关的特殊名称定义
META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS = "MetaModel_BTC_15m" # [字符串] 用于在 dynamic_params.json 中查找元模型相关动态参数的键名。
META_MODEL_GUI_DISPLAY_NAME = BaseModelNames.META_SIGNAL_BTC # [字符串] 在GUI中，代表元模型综合预测结果的那个显示条目的名称。
META_MODEL_TARGET_NAME = BaseModelNames.META_SIGNAL_BTC # [字符串] 元模型的目标名称，用于训练和预测

# --- 🎯 用户建议1：专门的质量权重配置管理 ---
# 集中管理质量权重参数，方便调优
META_LABEL_QUALITY_CONFIG = {
    "efficiency_weight_config": {
        "max_efficiency_multiplier": 3.0,      # [浮点数] 🎯 用户建议1：从2.0提升到3.0，更重地奖励快速达标
        "direction_penalty": 0.2,              # [浮点数] 严格惩罚反向案例
        "enable_efficiency_weighting": True,   # [布尔值] 启用效率权重计算
    },
    "smoothness_weight_config": {
        "max_smoothness_multiplier": 2.0,      # [浮点数] 平滑度权重倍数
        "drawdown_threshold": 0.15,            # [浮点数] 🎯 用户建议1：从0.25降到0.15，更严厉惩罚回撤
        "enable_smoothness_weighting": True,   # [布尔值] 启用平滑度权重计算
    },
    "final_weight_limits": {
        "min_final_weight": 0.1,               # [浮点数] 最小权重
        "max_final_weight": 5.0,               # [浮点数] 最大权重
        "high_quality_threshold": 1.2,         # [浮点数] 🎯 用户建议1：精英训练集阈值
    },
    "logging_config": {
        "enable_detailed_logging": True,       # [布尔值] 启用详细日志
        "log_sample_statistics": True,         # [布尔值] 记录样本统计
        "log_weight_distribution": True,       # [布尔值] 记录权重分布
    }
}

# --- 🎯 V17.0 二分类元模型决策配置 ---
# 注意：旧的多阶段决策逻辑（V15.1）已移除，现在使用简化的二分类决策体系
# 旧的参数如 META_FILTER_DIRECTION_ADVANTAGE 等已不再需要，因为二分类模型使用更简单的阈值判断

# --- 🎯 V30.1 超宽松模式：统一过滤器概率阈值（降低以增加信号频率）---
# 注意：这些参数仍被prediction_filter.py等模块使用，影响最终信号通过率
META_SIGNAL_UP_THRESHOLD = 0.45         # [浮点数] 🎯 超宽松：从0.52降到0.45，大幅增加UP信号通过率
META_SIGNAL_DOWN_THRESHOLD = 0.48       # [浮点数] 🎯 超宽松：从0.57降到0.48，大幅增加DOWN信号通过率

# --- ⚠️ V27.0 已废弃：动态阈值调整参数（仍保留用于兼容性）---
META_ENABLE_DYNAMIC_THRESHOLD = True    # [布尔值] ⚠️ V27.0已废弃：融合决策体系不使用动态阈值
META_DYNAMIC_ADJUSTMENT_RANGE = 0.03    # [浮点数] ⚠️ V27.0已废弃：融合决策体系不使用动态阈值

# --- 🎯 V30.0 反转大师决策体系配置 (Mean-Reversion Master) ---
# 🌟 V30.0 革命性进化：拥抱均值回归，动态情景阈值
# 彻底放弃"趋势思维"的固定阈值，基于均值回归机会分数动态调整决策阈值
# 核心理念：机会分数越高，越愿意在更低概率下入场

# === V30.0 反转大师 动态阈值决策 ===
ENABLE_MEAN_REVERSION_DECISION = True   # [布尔] 启用反转大师决策模式，False时回退到V29.0固定阈值

# --- 🎯 V30.1 合理宽松反转大师动态阈值核心参数 ---
MEAN_REVERSION_BASE_UP_THRESHOLD = 0.52     # [浮点数] 🎯 合理宽松：保持适度的质量要求
MEAN_REVERSION_BASE_DOWN_THRESHOLD = 0.54   # [浮点数] 🎯 合理宽松：DOWN信号稍微谨慎一些
MEAN_REVERSION_ADJUSTMENT_FACTOR = 0.15     # [浮点数] 🎯 保持动态调整灵活性

# --- 阈值安全边界 ---
MEAN_REVERSION_MIN_THRESHOLD = 0.51         # [浮点数] 最小阈值 - 防止过度激进
MEAN_REVERSION_MAX_THRESHOLD = 0.75         # [浮点数] 最大阈值 - 防止过度保守

# --- 均值回归机会分数权重配置 ---
MEAN_REVERSION_MARKET_CONDITION_WEIGHT = 0.5    # [浮点数] 市场条件权重（超跌/超涨+EMA回归倾向）
MEAN_REVERSION_MODEL_SIGNAL_WEIGHT = 0.5        # [浮点数] 模型信号权重（反转信号+共识度）

# --- 市场条件子权重 ---
MEAN_REVERSION_OVERSOLD_WEIGHT = 0.4            # [浮点数] 超跌/超涨程度权重
MEAN_REVERSION_EMA_REVERSION_WEIGHT = 0.3       # [浮点数] EMA回归倾向权重

# --- 模型信号子权重 ---
MEAN_REVERSION_REVERSAL_SIGNAL_WEIGHT = 0.6     # [浮点数] 基础模型反转信号权重
MEAN_REVERSION_CONSENSUS_WEIGHT = 0.4           # [浮点数] 模型共识度权重

# --- 市场状态阈值配置 ---
MEAN_REVERSION_EXTREME_DISTANCE_PCT = 5.0       # [浮点数] 极度超跌/超涨的价格EMA距离百分比阈值
MEAN_REVERSION_MIN_CERTAINTY_WEIGHT = 0.3       # [浮点数] 最小市场确定性权重

# --- 反转大师Optuna优化配置 ---
MEAN_REVERSION_BASE_THRESHOLD_MIN = 0.50        # [浮点数] 基础阈值搜索下限
MEAN_REVERSION_BASE_THRESHOLD_MAX = 0.75        # [浮点数] 基础阈值搜索上限
MEAN_REVERSION_ADJUSTMENT_FACTOR_MIN = 0.05     # [浮点数] 调整因子搜索下限
MEAN_REVERSION_ADJUSTMENT_FACTOR_MAX = 0.25     # [浮点数] 调整因子搜索上限

# --- 反转大师约束配置 ---
MEAN_REVERSION_MAX_CONSECUTIVE_LOSSES = 15      # [整数] 反转策略最大连续亏损次数
MEAN_REVERSION_MIN_SHARPE_RATIO = -0.5          # [浮点数] 反转策略最小夏普比率

# --- 反转大师优化策略配置 ---
MEAN_REVERSION_OPTIMIZATION_STRATEGY = 'mean_reversion_composite'  # [字符串] 反转大师优化策略

# --- 🎯 V30.1 合理宽松狙击手模式配置 ---
SNIPER_MODE_ENABLE = True                       # [布尔值] 重新启用，但使用合理的宽松阈值
SNIPER_MODE_MIN_CONSENSUS_SCORE = -0.5         # [浮点数] 临时修复：允许-0.09通过检查
SNIPER_MODE_STRONG_TREND_THRESHOLD = 7         # [整数] 强宏观趋势阈值（只阻止极强趋势冲突）
SNIPER_MODE_BLOCK_COUNTER_TREND = True         # [布尔值] 是否阻止与强宏观趋势相反的信号

# === V29.0 KISS 纯概率阈值决策（作为回退方案）===
META_FINAL_UP_THRESHOLD = 0.7          # [浮点数] 上涨信号概率阈值 - 基于验证集65.7%精准度设定稳健初始值
META_FINAL_DOWN_THRESHOLD = 0.74        # [浮点数] 下跌信号概率阈值 - 基于验证集62.5%精准度设定稳健初始值

# --- ⚠️ V29.0 已废弃：V28.0 非对称融合决策体系配置（仍保留用于兼容性）---
# 🚨 注意：以下参数在V29.0 KISS决策体系中已不再使用，仅为兼容性保留
# 🌟 V28.0 进化：为上涨和下跌信号量身定制的非对称评分体系
# 上涨信号更信赖"将军"（元模型）的判断力，下跌信号更信赖"专家组"（基础模型）的共识

# === 上涨信号评分体系 (基于元模型训练结果优化 - 精确率导向) ===
UP_WEIGHT_PROB_ADVANTAGE = 0.52        # [浮点数] ⚠️ V29.0已废弃：上涨信号：概率优势权重52%
UP_WEIGHT_CONSENSUS = 0.18              # [浮点数] ⚠️ V29.0已废弃：上涨信号：基础共识权重18%
UP_WEIGHT_MACRO = 0.30                  # [浮点数] ⚠️ V29.0已废弃：上涨信号：宏观顺势权重30%
UP_FINAL_DECISION_THRESHOLD = 42.0      # [浮点数] ⚠️ V29.0已废弃：上涨信号：决策门槛42分

# === 下跌信号评分体系 (基于元模型训练结果优化 - 召回率导向) ===
DOWN_WEIGHT_PROB_ADVANTAGE = 0.45       # [浮点数] ⚠️ V29.0已废弃：下跌信号：概率优势权重45%
DOWN_WEIGHT_CONSENSUS = 0.40             # [浮点数] ⚠️ V29.0已废弃：下跌信号：基础共识权重40%
DOWN_WEIGHT_MACRO = 0.15                 # [浮点数] ⚠️ V29.0已废弃：下跌信号：宏观顺势权重15%
DOWN_FINAL_DECISION_THRESHOLD = 57.0     # [浮点数] ⚠️ V29.0已废弃：下跌信号：决策门槛57分

# === 通用参数 ===
# 宏观顺势分计算参数
META_MACRO_ADX_MULTIPLIER = 2.0         # [浮点数] ADX转换为宏观顺势分的倍数因子

# --- ⚠️ V28.0 已废弃：V27.0统一权重配置（仍保留用于兼容性）---
# 🚨 注意：以下参数在V28.0非对称融合决策体系中已不再使用，仅为兼容性保留
META_WEIGHT_PROB_ADVANTAGE = 0.50      # [浮点数] ⚠️ V28.0已废弃：非对称体系使用UP_/DOWN_前缀的专用权重
META_WEIGHT_CONSENSUS = 0.35            # [浮点数] ⚠️ V28.0已废弃：非对称体系使用UP_/DOWN_前缀的专用权重
META_WEIGHT_MACRO = 0.15                # [浮点数] ⚠️ V28.0已废弃：非对称体系使用UP_/DOWN_前缀的专用权重
META_FINAL_DECISION_THRESHOLD = 37.0    # [浮点数] ⚠️ V28.0已废弃：非对称体系使用UP_/DOWN_前缀的专用阈值

# --- 🎯 V30.1 GUI显示兼容：旧版共识阈值（与狙击手模式保持一致）---
# 注意：这些参数被GUI显示使用，需要与狙击手模式的实际阈值保持一致
META_UP_CONSENSUS_THRESHOLD = 0.10      # [浮点数] 🎯 最小分歧阈值：要求模型有基本的方向倾向
META_DOWN_CONSENSUS_THRESHOLD = 0.10    # [浮点数] 🎯 最小分歧阈值：要求模型有基本的方向倾向
META_STRONG_SIGNAL_THRESHOLD = 0.12     # [浮点数] 🎯 更宽松：从0.18降到0.12，更容易识别强信号
META_STRONG_CONSENSUS_THRESHOLD = 0.12  # [浮点数] ⚠️ V27.0已废弃：融合决策体系不使用强共识机制

# --- 🎯 V30.1 超宽松模式：统一概率阈值（降低以增加信号频率）---
META_SIGNAL_MIN_PROBABILITY = 0.45      # [浮点数] 🎯 超宽松：从0.52降到0.45，大幅增加信号通过率

# --- 🚀 反向验证机制配置 (Reverse Validation Mechanism) ---
UP_MODEL_VETO_THRESHOLD = 0.92    # [浮点数, 0-1] UP模型一票否决阈值，当UP模型看跌概率超过此值时否决做多信号
DOWN_MODEL_VETO_THRESHOLD = 0.92  # [浮点数, 0-1] DOWN模型一票否决阈值，当DOWN模型看涨概率超过此值时否决做空信号

# --- 🎯 加权投票制配置 ---
# UP模型权重：作为最保守的狙击手，其看涨信号权重更高，看跌信号权重降低
UP_MODEL_UP_WEIGHT = 1.2    # [浮点数] UP模型看涨信号权重
UP_MODEL_DOWN_WEIGHT = 0.7  # [浮点数] UP模型看跌信号权重

# DOWN模型权重：作为看跌专家，其看跌信号权重更高，看涨信号权重降低
DOWN_MODEL_UP_WEIGHT = 0.7    # [浮点数] DOWN模型看涨信号权重
DOWN_MODEL_DOWN_WEIGHT = 1.2  # [浮点数] DOWN模型看跌信号权重

# LSTM模型权重：已关闭，不再使用
# LSTM_MODEL_UP_WEIGHT = 1.0    # [浮点数] LSTM模型看涨信号权重（已关闭）
# LSTM_MODEL_DOWN_WEIGHT = 1.0  # [浮点数] LSTM模型看跌信号权重（已关闭）

# --- 单笔交易限制机制配置 ---
# 🔒 严格的交易状态管理，确保同时只有一笔交易
ENABLE_SINGLE_TRADE_LIMIT = True                    # [布尔值] 是否启用单笔交易限制机制
SINGLE_TRADE_TIMEOUT_MINUTES = 22                   # [整数] 交易超时时间（分钟），超过此时间自动重置状态
                                                    # 🔧 修复：设置为22分钟，适合15分钟交易+7分钟缓冲
SINGLE_TRADE_FORCE_RESET_ENABLED = True             # [布尔值] 是否允许手动强制重置交易状态
SINGLE_TRADE_LOG_FILTERED_SIGNALS = True            # [布尔值] 是否记录被过滤的交易信号
SINGLE_TRADE_STATE_FILE = "trade_state.json"        # [字符串] 交易状态持久化文件名
SINGLE_TRADE_BACKUP_INTERVAL_MINUTES = 5            # [整数] 交易状态备份间隔（分钟）
SINGLE_TRADE_FORCE_RESET_ON_STARTUP = True          # [布尔值] 程序启动时是否强制重置交易状态

# 交易状态定义
class TradeState:
    """交易状态枚举"""
    IDLE = "idle"                    # 空闲状态，可以开启新交易
    OPENING = "opening"              # 开仓中
    ACTIVE = "active"                # 持仓中
    CLOSING = "closing"              # 平仓中
    ERROR = "error"                  # 异常状态

# 交易状态管理配置
TRADE_STATE_CONFIG = {
    'default_state': 'idle',
    'allowed_transitions': {
        'idle': ['opening'],
        'opening': ['active', 'error', 'idle'],
        'active': ['closing', 'error'],
        'closing': ['idle', 'error'],
        'error': ['idle']  # 错误状态可以重置为空闲
    },
    'blocking_states': ['opening', 'active', 'closing'],  # 阻止新交易的状态
    'timeout_reset_states': ['opening', 'active', 'closing'],  # 可以超时重置的状态
}

# --- 全局市场状态配置 (用于元模型特征工程) ---
# 这些参数用于计算全局的趋势和波动率指标，作为元模型的额外特征
GLOBAL_MARKET_STATE_CONFIG = {
    "symbol": "BTCUSDT",                             # [字符串] 全局市场状态分析的交易对
    "timeframe": "15m",                              # [字符串] 全局市场状态分析的时间框架 (修改为15m以确保动态变化)
    "trend_indicator_type": "both",                  # [字符串] 全局趋势指标类型: 'adx', 'ema_cross', 或 'both' (修复：启用ADX计算同时保持EMA)
    "trend_adx_period": 14,                          # [整数] 全局ADX计算周期
    "trend_adx_strength_threshold": 20,              # [整数] 全局ADX强趋势阈值 (修复：从25降到20)
    "trend_adx_threshold": 15,                       # [整数] 全局ADX趋势存在阈值 (修复：从20降到15)
    "trend_ema_short_period": 21,                    # [整数] 全局短期EMA周期
    "trend_ema_long_period": 50,                     # [整数] 全局长期EMA周期
    # 🎯 新增：EMA衍生特征配置
    "ema_slope_period": 5,                           # [整数] EMA斜率计算周期 (用于计算EMA变化率)
    "volatility_atr_period": 14,                     # [整数] 全局ATR计算周期
    "volatility_min_atr_percent": 0.08,              # [浮点数] 全局最小ATR百分比阈值
    "volatility_max_atr_percent": 1.5,               # [浮点数] 全局最大ATR百分比阈值
    "data_fetch_limit": 500,                         # [整数] 获取全局市场状态数据的K线数量 (增加以确保EMA预热)
}

# 🚨 ADX调试开关 (修复全局ADX问题时启用)
GLOBAL_ADX_DEBUG_VERBOSE = False  # [布尔值] 启用全局ADX计算的详细调试输出

# --- EMA调试配置 ---
EMA_DEBUG_VERBOSE = False                            # [布尔值] 是否启用详细的EMA调试日志输出
                                                     # True: 每次EMA计算都输出详细信息 (仅用于调试)
                                                     # False: 只在必要时输出日志 (生产环境推荐)

# 为元模型定义静态的凯利公式参数默认值。
# 这些值可以被 `dynamic_params.json` 文件中同名目标下的参数覆盖。
# 其结构模仿 `PREDICTION_TARGETS` 中单个目标的 "kelly_config_params" 部分。
META_MODEL_STATIC_KELLY_CONFIG = {
    "name": META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS, # [字符串] 必须与上面的虚拟目标名一致
    "trade_amount_strategy": "kelly_config", # [字符串] 明确指定元模型信号金额计算策略为凯利
    "kelly_config_params": {
        "payout_ratio_b": 0.85,                         # [浮点数] 盈亏比 (赔率)，通常与基础模型一致或接近。
        "win_rate_p_estimate": 0.3,                    # [浮点数] 🎯 初始预估胜率 (将被动态胜率替代)
        "virtual_total_capital_for_kelly": 50.0,        # [浮点数] 用于元模型凯利计算的虚拟总资本。
        "max_kelly_fraction_f": 0.1,                   # [浮点数, 0-1] 🎯 保守的分数凯利：最大25%凯利比例 (原0.07太保守，0.25更平衡)
        "min_bet_kelly": 5.0,                           # [浮点数] 凯利公式计算金额的下限。
        "max_bet_kelly": 250.0,                         # [浮点数] 凯利公式计算金额的上限。
        "enable_initial_conservative_betting": True,    # [布尔值] 是否启用初始保守下注期。
        "initial_conservative_trades_count": 25,        # [整数] 保守下注期的交易次数。
        "initial_conservative_bet_amount": 5.0,         # [浮点数] 保守期内的固定下注金额。
        "min_bet_if_kelly_negative": 5.0,               # [浮点数] 当凯利分数计算为负或零时使用的最小下注额。
        "enabled": True                                 # [布尔值] 此凯利配置是否启用。元模型信号是否实际发送还受全局和元模型自身动态参数的 `master_signal_sending_enabled` 和 `enabled` 控制。
    }
}

# --- 全局硬性开关 (这些开关会覆盖目标配置中的对应设置) ---
GLOBAL_HARD_SWITCHES = {
    'smote_global_enable': SMOTE_GLOBAL_ENABLE,      # 全局SMOTE开关，如果为False，所有目标都不会使用SMOTE
    'threshold_optimization_global_enable': THRESHOLD_OPTIMIZATION_ENABLE,  # 全局阈值优化开关
    'meta_model_training_enable': ENABLE_META_MODEL_TRAINING,  # 全局元模型训练开关
    'meta_model_prediction_enable': ENABLE_META_MODEL_PREDICTION,  # 全局元模型预测开关
    'simulator_integration_enabled': SIMULATOR_INTEGRATION_ENABLED,  # 全局模拟盘集成开关
    'alert_sound_enabled': ALERT_SOUND_ENABLED,  # 全局声音提示开关
}

# --- 全局默认值 (目标配置可以覆盖这些值) ---
GLOBAL_DEFAULTS = {
    'symbol': SYMBOL,
    'scaler_type': SCALER_TYPE,
    'data_fetch_limit': DATA_FETCH_LIMIT,
    'train_ratio': TRAIN_RATIO,
    'validation_ratio': VALIDATION_RATIO,
    'smote_min_samples_threshold': SMOTE_MIN_SAMPLES_THRESHOLD,
    'smote_default_k_neighbors': SMOTE_DEFAULT_K_NEIGHBORS,
    'smote_random_state': SMOTE_RANDOM_STATE,
    'threshold_default_value': THRESHOLD_DEFAULT_VALUE,
    'threshold_independent_val_ratio': THRESHOLD_INDEPENDENT_VAL_RATIO,
}

# --- 配置模板定义 ---
# 基础模型配置模板，减少冗余配置
BASE_MODEL_TEMPLATE = {
    # 基本信息模板
    'interval': '15m',
    'symbol': SYMBOL,
    'prediction_periods': [2],  # 🎯 优化：预测未来2个周期(30分钟)，减少噪音
    'prediction_minutes_display': 30,  # 🎯 更新显示时间为30分钟
    'drop_neutral_targets': False,
    'device_type': DeviceType.CPU.value,  # 🚀 AMD处理器优化
    'prediction_trigger_type': TriggerType.KLINE_CLOSE.value,

    # 特征工程开关模板
    'enable_price_change': True,
    'enable_volume': True,
    'enable_candle': True,
    'enable_ta': True,
    'enable_time': True,
    'enable_fund_flow': True,
    'enable_mtfa': True,
    'enable_pattern_recognition': False,
    'enable_trend_slope': True,
    'enable_market_state_adaptive': True,        # [布尔值] 🎯 新增：是否启用市场状态自适应特征

    # 通用参数模板
    'price_change_periods': [1, 2, 3, 5, 10],
    'enable_time_trigonometric': True,
    'enable_adx_trend_features': True,
    'enable_ema_trend_features': True,
    'enable_ta_derived_features': True,
    'mtfa_timeframes': ['30m', '1h', '4h'],
    'mtfa_feature_lookback_periods': 200,
    'mtfa_specific_lookbacks': {},
    'mtfa_min_bars_to_fetch': 50,
    'mtfa_min_bars_for_calc': 50,
    'mtfa_fetch_buffer': 10,

    # 高级数据处理配置模板
    'enable_intelligent_nan_processing': True,
    'enable_safe_fill_nans': True,
    'min_historical_bars_for_prediction': 100,
    'enable_advanced_feature_validation': True,

    # RFE配置模板
    'rfe_enable': False,
    'rfe_cv_folds': 3,
    'rfe_step': 1,
    'rfe_estimator_n_estimators': 100,
    'rfe_estimator_learning_rate': 0.1,
    'rfe_estimator_num_leaves': 31,
    'rfe_min_features_to_select': 10,

    # 特征选择配置模板
    'importance_thresholding_enable': False,
    'importance_threshold_value': 22,
    'importance_top_n_features': 52,
    'importance_model_n_estimators': 200,
    'learning_rate_initial_imp': 0.05,
    'num_leaves_initial_imp': 15,
    'max_depth_initial_imp': 5,
    'reg_alpha_initial_imp': 5.0,
    'reg_lambda_initial_imp': 5.0,
    'colsample_bytree_initial_imp': 0.7,
    'subsample_initial_imp': 0.7,
    'min_child_samples_initial_imp': 30,
    'metric_initial_imp': 'binary_logloss',

    # 🎯 V8.0专家委员会：重新启用自动化工具！
    # 现在每个专家模型面对的数据模式更单一、更纯粹，自动化工具将发挥最大威力
    'optuna_enable': True,  # ✅ 重新启用Optuna超参数优化
    'optuna_n_trials': 100,
    'optuna_timeout': None,
    'optuna_direction': 'maximize',
    'optuna_cv_folds': 3,
    'optuna_trial_n_estimators_max': 1500,
    'optuna_trial_early_stopping_rounds': 50,
    'optuna_trial_eval_metric': 'binary_logloss',

    # Purged K-Fold交叉验证配置模板
    'purged_cv_enable': True,                      # [布尔值] 是否启用Purged K-Fold交叉验证
    'purged_cv_purge_length': 10,                   # [整数] 清洗期长度（样本数量）
    'purged_cv_embargo_length': 0,                  # [整数] 禁运期长度（可选，进一步防止泄露）
    'purged_cv_min_train_size': None,               # [整数或None] 最小训练集大小，None为自动计算
    'purged_cv_auto_purge_calculation': True,       # [布尔值] 是否自动计算purge长度

    # LightGBM基础配置模板 (AMD处理器优化)
    'objective': 'binary',
    'metric': 'binary_logloss',
    'class_weight': {0: 1.0, 1: 4.0},   # 🎯 策略二优化：降低权重，依靠高质量数据学习（从15.0降至4.0）
                                     # 注意：此参数在Optuna超参数优化网格中未包含，因此是固定参数。如需优化，请在Optuna配置中添加相应的搜索空间。
    'boosting_type': 'gbdt',
    'random_state': 42,
    'n_estimators': 3000,                    # 🎯 优化：减少最大树数量以防止过拟合
    'early_stopping_rounds': 100,           # 🎯 优化：更早停止以防止过拟合
    'verbose': -1,
    'verbosity': -1,

    # 🚀 智能自适应配置 (AMD 16核 + RTX 3070)
    # 注意：实际参数将由 adaptive_lightgbm_config.py 根据数据规模自动选择
    'device_type': 'cpu',                    # 默认CPU（小数据集更快）
    'num_threads': CPU_COUNT,                # 使用所有CPU核心
    'force_row_wise': True,                  # AMD处理器推荐行优先
    'histogram_pool_size': -1,               # 自动内存池大小
    'max_bin': 255,                          # 适合AMD缓存结构
    'boost_from_average': True,              # AMD处理器友好的初始化
    'tree_learner': 'serial',                # 串行学习器在AMD上更稳定

    # 🚀 性能优化参数
    'bagging_fraction': 0.8,                 # 减少内存使用
    'feature_fraction': 0.9,                 # CPU模式可以用更高比例
    'lambda_l1': 0.1,                        # L1正则化
    'lambda_l2': 0.1,                        # L2正则化
    'min_data_in_leaf': 20,                  # 适合AMD L3缓存
    'min_sum_hessian_in_leaf': 1e-3,         # 叶子节点最小Hessian和

    # 💡 GPU配置说明：
    # - GPU在小数据集(< 50K样本)上比AMD 16核CPU慢
    # - 大数据集(>= 50K样本)时可考虑GPU，但需要实际测试
    # - 建议使用 adaptive_lightgbm_config.py 自动选择最优配置
    'ensemble_runs': 1,  # 已弃用参数：原用于控制集成运行次数，现已被 ensemble_cv_folds 替代。保留用于向后兼容，建议使用 ensemble_cv_folds 控制集成策略
    'ensemble_cv_folds': 5,
    'subsample_freq': 1,

    # SMOTE配置模板
    'smote_enable': True,
    'smote_k_neighbors': 3,
    'smote_min_samples_threshold': 5,
    'smote_random_state': 42,

    # 阈值优化配置模板
    'threshold_optimization_enable': False,
    'threshold_save_to_metadata': True,
    'threshold_default_value': 0.5,
    'threshold_use_independent_validation': True,
    'threshold_independent_val_ratio': 0.20,
    'threshold_precision_constraint_fallback': True,

    # 概率校准配置模板
    'enable_probability_calibration': True,
    'calibration_brier_improvement_threshold': 0.0001,

    # SHAP分析配置模板 - 🏆 只保留精英模型深度分析
    'enable_shap_analysis_per_fold': False,         # 🚫 已禁用：不再需要单折SHAP分析
    'enable_shap_analysis_ensemble': False,         # 🚫 已禁用：不再需要集成模型SHAP分析
    'enable_shap_analysis_elite': True,             # 🏆 精英模型分析：唯一保留的SHAP分析方式
    'elite_selection_metric': 'test_f1_score',      # 精英模型选择标准
    'elite_model_count': 3,                         # 精英模型数量
    'enable_shap_for_live_prediction': False,

    # 信号过滤配置模板 (关闭基础模型过滤，让元模型处理)
    'enable_trend_detection': False,
    'trend_detection_timeframe': '30m',
    'trend_indicator_type': TrendIndicatorType.ADX.value,
    'trend_filter_strategy': 'filter_only',
    'trend_chase_confidence_boost': 0.05,
    'enable_volatility_filter': False,
    'volatility_filter_timeframe': '30m',
    'volatility_atr_period': 14,
    'volatility_min_atr_percent': 0.1,
    'volatility_max_atr_percent': 1.2,
    'enable_dynamic_threshold': True,

    # OOF参数模板
    'meta_model_oof_cv_folds': 5,
    'meta_model_oof_n_estimators_large': 3000,
    'meta_model_oof_early_stopping_rounds': 100,
    'meta_model_oof_early_stop_eval_ratio': 0.15,
    'meta_model_oof_min_samples_for_eval': 50,
}

# --- 预测目标配置列表 (PREDICTION_TARGETS) ---
# 这是一个列表，其中每个元素都是一个字典，定义了一个独立的预测目标（通常是一个基础模型）。
PREDICTION_TARGETS = [
    {
        # --- 目标基本信息 ---
        "name": "BTC_15m_UP",                           # [字符串] 目标名称，必须唯一。用于GUI识别、日志记录、模型文件命名等。
        "interval": "15m",                              # [字符串] 此目标模型分析和预测所基于的主要K线时间周期 (例如 '1m', '5m', '15m', '1h')。
        "symbol": "BTCUSDT",                            # [字符串] 此目标针对的交易对。
        "prediction_periods": [2],                      # [列表, 包含整数] 🎯 优化：预测未来2个周期(30分钟)后的价格方向，减少噪音提升信噪比
        "prediction_minutes_display": 30,               # [整数] 🎯 更新：在GUI中显示此目标的预测时效 (30分钟预测)。
        "model_save_dir": "trained_models_btc_15m_up",  # [字符串] 存储此目标训练产物（模型、scaler、特征列表、元数据等）的目录名。
        "target_variable_type": "UP_ONLY",              # [字符串] 目标变量的类型:
                                                        #   "UP_ONLY": 模型预测是否“明确上涨”。目标变量为1代表明确上涨，0代表非明确上涨（即横盘或下跌）。
                                                        #   "DOWN_ONLY": 模型预测是否“明确下跌”。目标变量为1代表明确下跌，0代表非明确下跌（即横盘或上涨）。
                                                        #   "BOTH": （如果仍在使用此逻辑）通常指模型预测上涨（1）或下跌（0），可能需要处理中性情况。
        "drop_neutral_targets": False,                  # [布尔值] 在创建目标变量时，是否丢弃“中性”样本。对于 "UP_ONLY" 或 "DOWN_ONLY"，这通常设为False，因为中性情况已被归为0类。
        "device_type": 'cpu',                           # [字符串] 🚀 强制CPU训练：AMD 16核处理器最优配置
        "prediction_trigger_type": "kline_close",       # [字符串] 此目标预测的主要触发方式。 "kline_close" 表示当对应symbol和interval的K线关闭时触发。 "apscheduler_driven" 表示主要由APScheduler任务驱动。
        # "apscheduler_job_enabled": True,                # [布尔值, 可选] 是否为这个目标启用APScheduler定时作业。
        # "apscheduler_trigger_type": "cron",             # [字符串, 可选] APScheduler触发类型: 'cron', 'interval', 'date'。
        # "apscheduler_cron_config": {"minute": "*/15"},  # [字典或字符串, 可选] 如果是cron类型，具体的cron配置。例如 {"minute": "5"} 表示每小时的第5分钟，{"minute": "*/15"} 表示每15分钟。

        # 🎯 V10.0集大成者：统一框架下的宏观调控配置
        "enable_expert_committee": False,               # [布尔值] ❌ V10.0：废除物理分割，使用统一模型
        "enable_market_state_adaptive": True,           # [布尔值] ✅ V10.0：启用市场状态自适应特征
        "enable_dynamic_sample_weighting": True,        # [布尔值] ✅ V10.0：启用动态样本权重宏观调控

        # --- 特征工程参数 ---
        # 🔧 Optimized Switches (优化开关):
        "enable_price_change": True,                    # [布尔值] 是否启用价格变动百分比特征。
        "enable_volume": True,                          # [布尔值] 是否启用成交量相关特征。
        "enable_candle": True,                          # [布尔值] 是否启用基于K线形状的特征。
        "enable_ta": True,                              # [布尔值] 是否启用基于pandas-ta库计算的技术分析指标。
        "enable_time": True,                            # [布尔值] 是否启用基于时间戳的特征。
        "enable_fund_flow": True,                       # [布尔值] 是否启用资金流相关特征。
        "enable_mtfa": True,                            # [布尔值] 是否启用多时间框架分析 (MTFA) 特征。
        "mtfa_timeframes": ['30m', '1h', '4h'],         # [列表] MTFA时间框架配置
        "enable_pattern_recognition": True,             # [布尔值] 是否启用高级K线形态识别。
        "enable_trend_slope": True,                     # [布尔值] 是否启用趋势斜率分析特征。
        "enable_derivatives_features": True,            # [布尔值] 是否启用衍生品市场数据特征（资金费率、持仓量、多空比等）。

        # 🎯 V10.0集大成者：高级特征工程配置
        "enable_higher_order_features": True,            # [布尔值] 🎯 恢复高阶特征：在优化标签和目标下利用高级特征
        "enable_interaction_features": True,             # [布尔值] 🎯 恢复交互特征：状态×指标组合提升预测能力

        # 🚀 MTFA特征过滤器配置
        "force_new_mtfa_filter": True,                  # [布尔值] ✅ 强制使用新的MTFA列过滤器。True: 导入失败时报错; False: 回退到传统方法
        "mtfa_filter_config": {                         # [字典] MTFA过滤器的详细配置
            "exclude_diagnostic_columns": True,         # [布尔值] 排除统计/诊断列
            "exclude_business_columns": True,            # [布尔值] 排除业务逻辑列
            "loose_inclusion_mode": False,               # [布尔值] 严格包含模式
            "custom_exclusion_patterns": [],            # [列表] 自定义排除模式
            "custom_inclusion_patterns": [],            # [列表] 自定义包含模式
            "regex_exclusion_patterns": [],             # [列表] 正则排除模式
            "regex_inclusion_patterns": []              # [列表] 正则包含模式
        },

        # ⚙️ Optimized Parameters (优化参数):
        "volume_avg_period": 33,                         # [整数] 成交量移动平均周期。
        "atr_period": 12,                               # [整数] Average True Range (ATR) 的周期。
        "rsi_period": 22,                               # [整数] Relative Strength Index (RSI) 的周期。
        "willr_period": 22,                             # [整数] Williams %R 周期参数 (匹配训练模型)
        "cci_period": 22,                               # [整数] CCI 周期参数 (匹配训练模型)
        "macd_fast": 9,                                # [整数] MACD快线EMA周期。
        "macd_slow": 32,                                # [整数] MACD慢线EMA周期。
        "macd_sign": 11,                                 # [整数] MACD信号线EMA周期。
        "stoch_k": 23,                                  # [整数] Stochastic %K周期。
        "stoch_d": 2,                                  # [整数] Stochastic %D周期。
        "stoch_smooth_k": 4.9,                            # [整数] Stochastic %K平滑周期。
        "fund_flow_ratio_smoothing_period": 8,         # [整数] 资金流指标平滑处理周期 (匹配训练模型)。
        "target_threshold": 0.002,                   # [浮点数] 目标变量阈值。
        "hma_period": 15,                               # [整数] Hull Moving Average (HMA) 周期。
        "kc_period": 13,                                # [整数] Keltner Channel EMA中线周期。
        "kc_atr_period": 13,                            # [整数] Keltner Channel ATR计算周期。
        "kc_multiplier": 2.115598740558161,             # [浮点数] Keltner Channel ATR倍数。
        "signal_threshold": 0.6,                   # [浮点数] 模型预测信号阈值。
        "trend_adx_period": 17,                         # [整数] ADX趋势指标计算周期。
        "trend_adx_threshold": 34,                      # [整数] ADX趋势强度阈值。
        "trend_ema_short_period": 19,                   # [整数] 趋势分析短期EMA周期。
        "trend_ema_long_period": 56,                    # [整数] 趋势分析长期EMA周期。
        "trend_slope_period_1": 3,                      # [整数] 第一个趋势斜率计算周期。
        "trend_slope_period_2": 12,                     # [整数] 第二个趋势斜率计算周期。
        # 🎯 新增：UP模型专用EMA距离特征参数
        "ema_short_period_up": 10,                      # [整数] UP模型短期EMA周期，用于计算均线距离特征。
        "ema_long_period_up": 30,                       # [整数] UP模型长期EMA周期，用于计算均线距离特征。
        # 🎯 新增：UP模型专用布林带突破强度特征参数
        "bb_period": 20,                                # [整数] 布林带计算周期。
        "bb_std": 2.0,                                  # [浮点数] 布林带标准差倍数。
        # 🎯 新增：时间框架敏感度特征参数
        "enable_timeframe_sensitivity": True,           # [布尔值] 是否启用时间框架敏感度特征。
        "tf_sensitivity_reference_timeframe": "4h",     # [字符串] 参考时间框架（用于对比）。
        "tf_sensitivity_features": ["rsi", "close_pos_in_candle", "macd", "volume_ratio"],  # [列表] 要计算对比的特征类型。
        "doji_threshold": 0.12785813365104795,                     # [浮点数] 十字星形态阈值。
        "hammer_body_ratio": 0.3940528078916167,                  # [浮点数] 锤子线实体比例阈值。
        "hammer_shadow_ratio": 2.431616506840809,                # [浮点数] 锤子线影线比例阈值。
        "marubozu_threshold": 0.904916951086358,                 # [浮点数] 光头光脚线阈值。
        "spinning_top_threshold": 0.4785481038750671,             # [浮点数] 陀螺线阈值。
        "engulfing_body_multiplier": 1.450922007228928,               # [浮点数] 吞没形态倍数阈值。
        "morning_evening_star_body_ratio": 0.20214578907876324,         # [浮点数] 启明星/黄昏星比例阈值。

        # --- 🚀 K线形态优先级配置 ---
        "enable_pattern_priority_system": True,                    # [布尔值] 是否启用形态优先级/互斥处理系统
        "pattern_priority_levels": {                               # [字典] 形态优先级配置
            "high_priority": ["Doji", "Bullish_Engulfing", "Bearish_Engulfing"],  # 高优先级形态
            "medium_priority": ["Morning_Star", "Evening_Star", "Hammer", "Shooting_Star"],  # 中优先级形态
            "low_priority": ["Spinning_Top", "Green_Long_Body", "Red_Long_Body"]  # 低优先级形态
        },
        "allow_pattern_override": False,                           # [布尔值] 是否允许低优先级形态覆盖高优先级形态
        "doji_threshold_batac": 0.08397705355404736,                # [浮点数] 多K线十字星阈值。

        # 📊 Additional Configuration (附加配置):
        "price_change_periods": [1, 2, 3, 5, 10],       # [列表] 价格变动计算周期。
        "cci_constant": 0.0025,                         # [浮点数] CCI计算常数 (匹配UP模型)。
        "enable_time_trigonometric": True,              # [布尔值] 是否启用时间三角函数编码。
        "enable_adx_trend_features": True,              # [布尔值] 是否启用ADX趋势特征。
        "enable_ema_trend_features": True,              # [布尔值] 是否启用EMA交叉趋势特征。
        "enable_ta_derived_features": True,             # [布尔值] 是否启用技术指标衍生特征。
        "mtfa_timeframes": ['30m','1h', '4h'],          # [列表] MTFA时间框架。
        "mtfa_feature_lookback_periods": 200,           # [整数] MTFA特征回看周期。
        "mtfa_specific_lookbacks": {},                  # [字典] MTFA特定回看周期。
        "mtfa_min_bars_to_fetch": 50,                   # [整数] MTFA最小K线数量。
        "mtfa_min_bars_for_calc": 50,                   # [整数] MTFA计算最小K线数量。
        "mtfa_fetch_buffer": 10,                        # [整数] MTFA数据获取缓冲区。

        # --- 衍生品市场特征配置 (动态自适应版) ---
        # 1. 定义需要哪些衍生品数据源
        "derivatives_data_sources": ["funding_rate", "open_interest", "long_short_ratio", "top_trader_ratio"],

        # 2. 定义特征计算参数 (告诉程序要算什么)
        "derivatives_feature_params": {
            "funding_rate": {
                "moving_averages": [8, 24, 96],         # 计算8周期、24周期、96周期的移动平均 (对应2h, 6h, 24h @ 15m)
                "pct_changes": [4, 12, 48],             # 计算1h, 3h, 12h的资金费率变化率
                "rolling_std": [8, 24]                  # 计算8周期、24周期的滚动标准差
            },
            "open_interest": {
                "moving_averages": [12, 48, 192],       # 计算3h, 12h, 48h的持仓量移动平均
                "pct_changes": [4, 12, 48],             # 计算1h, 3h, 12h的持仓量变化率
                "rolling_std": [12, 48]                 # 计算持仓量波动性指标
            },
            "long_short_ratio": {
                "moving_averages": [16, 64, 256],       # 计算4h, 16h, 64h的多空比移动平均
                "pct_changes": [8, 24],                 # 计算2h, 6h的多空比变化率
                "sentiment_indicators": True            # 计算情绪偏向等衍生指标
            },
            "top_trader_ratio": {
                "moving_averages": [16, 64],            # 计算4h, 16h的大户多空比移动平均
                "divergence_analysis": True             # 计算与散户情绪的分歧指标
            }
        },

        # 3. 数据获取的缓冲设置 (告诉程序要多拿多少数据)
        "derivatives_fetch_buffer_multiplier": 1.5,     # [浮点数] 在计算出的所需数据量基础上，再多拿50%作为缓冲
        "derivatives_min_fetch_limit": 200,             # [整数] 无论如何，最少也要拿200条数据
        "derivatives_max_fetch_limit": 1000,            # [整数] 最多获取1000条数据（API限制）
        "derivatives_period_alignment": True,           # [布尔值] 是否将衍生品数据周期与主K线周期对齐

        # --- 高级数据处理配置 ---
        "enable_intelligent_nan_processing": True,      # [布尔值] 是否启用智能NaN/Inf处理，根据特征类型选择合适的默认值。
        "enable_safe_fill_nans": True,                  # [布尔值] 是否启用安全的NaN填充方法，仅使用历史数据进行填充。
        "min_historical_bars_for_prediction": 100,      # [整数] 实时预测时所需的最小历史K线数量。
        "enable_advanced_feature_validation": True,     # [布尔值] 是否启用高级特征验证和错误处理。

        # --- 目标变量定义 和 预测信号阈值 ---
        "target_threshold": 0.001,                      # [浮点数] 定义“明确方向变动”的价格百分比阈值。例如0.002代表0.2%。用于创建目标变量 (上涨/下跌)。
        "signal_threshold": 0.6,                        # [浮点数, 0-1] 模型输出的预测概率需要超过此阈值，才初步认为是一个有效的交易信号 (此阈值可被后续的动态阈值逻辑调整)。
        # "labeling_method": "pure_direction",             # [字符串] ❌ 已禁用：纯方向标签法产生大量噪音
        "labeling_method": "threshold",                   # [字符串] ✅ 启用传统阈值法以配合三道屏障

        # --- 🎯 动态波动率阈值配置 (革新目标变量定义) - 策略一优化 ---
        "enable_dynamic_thresholds": False,             # [布尔值] 🎯 **关键**：禁用动态阈值，完全依赖三道屏障法定义标签
        "dynamic_threshold_base": "ATRr_14",            # [字符串] 用作动态阈值基准的波动率指标列名 (ATR百分比)
        "dynamic_threshold_atr_period": 14,             # [整数] ATR周期
        "dynamic_threshold_atr_multiplier": 0.8,        # [浮点数] 🎯 策略一：降低至0.6，目标5%-15%正类样本
        "dynamic_threshold_multipliers": [0.6],         # [列表] 🎯 策略一：降低ATR倍数，增加学习样本
        "dynamic_threshold_min": 0.0005,                # [浮点数] 🎯 策略一：提高最小阈值，减少噪音
        "dynamic_threshold_max": 0.008,                 # [浮点数] 🎯 策略一：适度提高最大阈值，平衡质量与数量
        "dynamic_threshold_smoothing": 3,               # [整数] 动态阈值的平滑周期，0表示不平滑

        # --- 🚀 多周期融合增强配置 (Multi-Timeframe Target Enhancement) - 策略一优化 ---
        "enable_multi_timeframe_target": True,          # [布尔值] 🎯 策略一：与三道屏障协同，增强信号确认度
        "multi_timeframe_secondary_interval": "5m",     # [字符串] 辅助时间周期，用于增强目标变量定义
        "multi_timeframe_min_bullish_ratio": 0.67,      # [浮点数] 🎯 策略一：UP模型要求67%小周期K线同向上涨
        "multi_timeframe_min_bearish_ratio": 0.67,      # [浮点数] 辅助周期内最小下跌K线比例，0.67表示3根中至少2根下跌

        # --- 🚀 三道屏障标签法配置 (Triple-Barrier Method) - 对称化优化 ---
        "enable_triple_barrier": True,                 # [布尔值] ✅ 启用三道屏障法：提升标签质量，减少噪音
        "triple_barrier_profit_multiplier": 1.2,        # [浮点数] 🎯 元模型优化：降低到1.2倍ATR，增加上涨样本
        "triple_barrier_loss_multiplier": 1.0,          # [浮点数] 🎯 微调标签定义：1.0倍ATR止损，实现1.5:1.0不对称比例
        "triple_barrier_use_fixed": False,             # [布尔值] 🎯 策略一：使用动态ATR而非固定百分比，适应波动性
        "triple_barrier_fixed_profit": 0.015,           # [浮点数] 固定止盈阈值 (1.5%)，降低以增加正类样本
        "triple_barrier_fixed_loss": 0.012,             # [浮点数] 固定止损阈值 (1.2%)，实现不对称盈亏比
        "triple_barrier_min_profit": 0.005,             # [浮点数] 🎯 元模型优化：降低最小止盈到0.5%，增加上涨样本
        "triple_barrier_max_profit": 0.05,              # [浮点数] 最大止盈阈值 (5%)，防止屏障过大
        "triple_barrier_min_loss": 0.003,               # [浮点数] 最小止损阈值 (0.3%)，防止屏障过小
        "triple_barrier_max_loss": 0.03,                # [浮点数] 最大止损阈值 (3%)，防止屏障过大

        # --- 🚀 质量权重配置 (Meta Label Quality Weighting) - 教科书级别筛选 ---
        "enable_meta_label_quality_weighting": True,   # [布尔值] 🎯 启用质量权重，筛选教科书级别的交易案例
        "meta_label_quality_config": {
            "efficiency_weight_config": {
                "max_efficiency_multiplier": 3.0,      # [浮点数] 🎯 用户建议1：从2.0提升到3.0，更重地奖励快速达标的案例
                "direction_penalty": 0.2,              # [浮点数] 🎯 严格惩罚反向案例，降低其权重
                "enable_efficiency_weighting": True,   # [布尔值] 启用效率权重计算
            },
            "smoothness_weight_config": {
                "max_smoothness_multiplier": 2.0,      # [浮点数] 🎯 提高平滑度权重倍数，奖励低回撤路径
                "drawdown_threshold": 0.15,            # [浮点数] 🎯 用户建议1：从0.25降低到0.15，更严厉地惩罚回撤
                "enable_smoothness_weighting": True,   # [布尔值] 启用平滑度权重计算
            },
            "logging_config": {
                "enable_detailed_logging": True,       # [布尔值] 启用详细日志记录
                "log_sample_statistics": True,         # [布尔值] 记录样本统计信息
                "log_weight_distribution": True,       # [布尔值] 记录权重分布信息
            }
        },

        # --- 🚀 分层数据策略特征工程配置 (Layered Data Strategy Features) ---
        "enable_macro_trend_features": True,           # [布尔值] 🎯 启用宏观市场状态特征
        "macro_ema_period": 200,                       # [整数] 🎯 宏观EMA周期，用于长期趋势过滤
        "long_term_normalization_methods": ["zscore", "percentile"],  # [列表] 🎯 长期归一化方法
        "enable_long_term_feature_stats": True,       # [布尔值] 🎯 启用长期特征统计量计算

        # --- 🚀 反向验证机制特征配置 (Reverse Validation Features) ---
        "enable_reverse_validation": True,            # [布尔值] 🎯 启用反向验证特征
        "dual_kill_threshold": 0.6,                   # [浮点数] 🎯 多空双杀风险阈值

        # --- 🎯 V10.0集大成者：关闭特征选择，使用固定特征策略 ---
        "two_stage_feature_selection_enable": True,    # [布尔值] ❌ V10.0：关闭两阶段特征选择，使用固定特征策略
        "importance_prescreening_ratio": 0.5,           # [浮点数] 第一阶段重要性初筛保留的特征比例（0.6表示保留60%特征）

        # --- RFE (Recursive Feature Elimination) 配置 ---
        "rfe_enable": False,                          # [布尔值] 是否启用RFE递归特征消除来进行特征选择。
         "rfe_cv_folds": 3,                              # [整数, 可选] 如果启用RFE，RFE内部交叉验证的折数。
         "rfe_step": 1,                                  # [整数或浮点数, 可选] RFE每次迭代移除特征的数量或百分比。
         "rfe_scoring": "binary_profit_precision_composite", # [字符串, 可选] 🎯 根本性优化：RFE使用盈利导向复合指标，与Optuna保持一致。
         "rfe_estimator_n_estimators": 100,              # [整数, 可选] RFE内部使用的LightGBM评估器的树数量。
         "rfe_estimator_learning_rate": 0.1,             # [浮点数, 可选] RFE内部LightGBM评估器的学习率。
         "rfe_estimator_num_leaves": 31,                 # [整数, 可选] RFE内部LightGBM评估器的叶子数。
         "rfe_min_features_to_select": 30,               # [整数, 可选] RFE最终选择的最小特征数量。

         # --- 🚀 RFE阈值优化配置 ---
         "rfe_threshold_strategy": "baseline_optimal",   # [字符串] RFE阈值策略: 'fixed', 'baseline_optimal', 'cv_optimal'
         "rfe_baseline_validation_ratio": 0.2,           # [浮点数] 基准验证集比例 (用于寻找最优阈值)
         "rfe_threshold_search_range": [0.1, 0.9],       # [列表] 阈值搜索范围
         "rfe_threshold_search_step": 0.05,              # [浮点数] 阈值搜索步长
         "rfe_min_validation_size": 50,                  # [整数] 最小验证集大小
         "rfe_fixed_threshold": 0.5,                     # [浮点数] 固定阈值 (当strategy='fixed'时使用)

         # --- 🚀 RFE性能优化配置 ---
         "rfe_data_subsampling_enable": True,            # [布尔值] 是否启用数据子采样来加速RFE计算
         "rfe_data_subsampling_ratio": 0.4,              # [浮点数] 数据子采样比例，0.3表示使用30%的数据进行特征选择

        # --- 特征选择：基于初始模型重要性进行筛选 ---
        "importance_thresholding_enable": True,         # [布尔值] 是否在RFE之后（或直接在全部特征上）再进行一次基于LightGBM初始重要性的特征筛选。
        "importance_threshold_value": None,               # [整数] 如果 `importance_top_n_features` 未设置或为None，则移除重要性绝对值低于此阈值的特征。
        "importance_top_n_features": 40,                # [整数 或 None] 如果设置了此值，则优先选择重要性最高的Top N个特征。如果为None，则使用 `importance_threshold_value`。
        "importance_model_n_estimators": 500,           # [整数] 用于获取初始特征重要性的临时LightGBM模型的树数量。
        # 以下为用于获取初始重要性的LGBM模型的超参数，可以根据DOWN模型的特性进行微调。
        "learning_rate_initial_imp": 0.0163,              # [浮点数] 初始重要性模型的学习率。
        "num_leaves_initial_imp": 40,                   # [整数] 初始重要性模型的每棵树最大叶子数。
        "max_depth_initial_imp": 7,                     # [整数] 初始重要性模型的每棵树最大深度。
        "reg_alpha_initial_imp": 5.2398,                   # [浮点数] 初始重要性模型的L1正则化系数。
        "reg_lambda_initial_imp": 18.2754,                  # [浮点数] 初始重要性模型的L2正则化系数。
        "colsample_bytree_initial_imp": 0.7091, # [浮点数] 初始重要性模型的列采样率。
        "subsample_initial_imp": 0.9236,    # [浮点数] 初始重要性模型的行采样率。
        "min_child_samples_initial_imp": 131,            # [整数] 初始重要性模型一个叶子节点上所需的最小数据量。
        "metric_initial_imp": 'binary_logloss',         # [字符串] 初始重要性模型训练时使用的评估指标。

        # --- 🎯 UP模型超参数优化已完成，关闭Optuna ---
        "optuna_enable": False,                        # [布尔值] ❌ 关闭Optuna，使用已优化的最佳参数
        "optuna_n_trials": 200,                         # [整数] 🎯 精度优化：进一步增加试验次数寻找最佳精度参数组合
        "optuna_timeout": None,                         # [整数 或 None] Optuna运行的最大秒数。如果为None，则不限制时间，直到达到 `optuna_n_trials`。
        "optuna_metric": "binary_profit_precision_composite", # [字符串] 🎯 升级优化目标：盈利导向复合指标 (70%盈利+30%精确率)
        # 🎯 核心优化建议2.2：基础模型盈利导向指标选项 (已统一):
        # 1. 'binary_profit_precision_composite' - 复合指标：70%盈利+30%精确率 (🎯 推荐，平衡收益质量)
        # 2. 'binary_simulated_profit' - 直接优化期望收益/交易 (纯盈利导向)
        # 3. 'binary_risk_adjusted_return' - 风险调整收益，考虑交易频率
        # 4. 'binary_win_rate_weighted_profit' - 胜率加权的期望收益
        # 5. 传统指标: 'average_precision', 'f1', 'accuracy' (不推荐，非盈利导向)
        "optuna_direction": "maximize",                 # [字符串] Optuna优化目标指标的方向: "maximize" (最大化) 或 "minimize" (最小化)。
        "optuna_cv_folds": 3,                           # [整数] Optuna在评估每个试验参数集时，内部进行交叉验证的折数。
        "optuna_trial_n_estimators_max": 2000,          # [整数] Optuna单次试验中，LightGBM模型允许训练的最大树数量 (通常配合早停)。
        "optuna_trial_early_stopping_rounds": 50,       # [整数] Optuna单次试验中，LightGBM训练的早停轮数。
        "optuna_trial_eval_metric": "binary_logloss",   # [字符串] Optuna单次试验中，LightGBM早停所依据的评估指标。

        # --- LightGBM 模型固定/回退参数 ---
        # 这些参数在 Optuna 未启用或优化失败时，作为模型的默认参数。
        # 如果Optuna成功，它找到的最佳参数会覆盖这里的对应项。
        "objective": "binary",                          # [字符串] LightGBM学习任务目标：'binary' 表示二分类。
        "metric": "binary_logloss",                     # [字符串 或 列表] 模型训练过程中的评估指标，也用于早停。例如 'binary_logloss', 'auc', 'average_precision'。
        "class_weight": {0: 1.0, 1: 4.0},                  # [字典] 🎯 恢复激进类别权重：4.0倍权重强化上涨信号学习

        # --- ⚖️ 动态样本权重配置 (让模型"学得更聪明") ---
        "enable_dynamic_sample_weighting": True,        # [布尔值] 是否启用动态样本权重计算
        "time_decay_factor": 0.2,                       # [浮点数] 🎯 精度优化：增强时间衰减，更重视近期高质量数据

        # 🎯 增强的动态样本权重配置
        "dynamic_sample_weighting": {
            "enable_dynamic_weighting": True,
            "weighting_strategies": ["time_decay", "volatility", "market_state"],  # 启用多种权重策略
            "strategy_weights": {"time_decay": 0.3, "volatility": 0.4, "market_state": 0.3},  # 策略权重分配
            "combination_method": "weighted_average",    # 权重组合方法

            # 时间衰减配置
            "time_decay_rate": 0.1,                     # 衰减率
            "time_decay_unit": "samples",               # 衰减单位
            "time_decay_power": 1.0,                    # 衰减幂次

            # 波动率加权配置
            "volatility_column": "ATRr_14",             # 波动率指标列
            "volatility_power": 2.0,                    # 🎯 精度优化：增强波动率权重，重视高波动期的信号质量
            "volatility_cap": 2.5,                      # 🎯 精度优化：降低权重上限，避免极端权重

            # 🎯 强化市场状态加权配置 - 多地形作战核心
            "enable_market_state_weighting": True,
            "market_state_weights": {
                # 🎯 极度稀有但极其重要的市场状态 - 超高权重
                "strong_uptrend": 5.0,                  # 强上升趋势：价值连城，拼尽全力学习
                "strong_downtrend": 5.0,                # 强下降趋势：价值连城，拼尽全力学习
                "panic_selling": 6.0,                   # 恐慌性抛售：极度稀有，超高价值
                "bubble_state": 6.0,                    # 泡沫状态：极度稀有，超高价值

                # 🎯 重要但相对常见的状态 - 高权重
                "high_certainty": 3.0,                  # 高确定性：重要学习目标
                "low_vol_sideways": 2.5,                # 低波动盘整：趋势酝酿期，重要
                "normal_trend": 1.0,                    # 正常趋势：基准权重

                # 🎯 噪音和干扰状态 - 极低权重或忽略
                "high_vol_sideways": 0.1,               # 高波动盘整：噪音过滤，极低权重
                "extreme_volatility": 0.0,              # 极端波动：直接忽略，不学习
                "liquidity_drought": 0.0,               # 流动性枯竭：异常状态，忽略
                "low_certainty": 0.3                    # 低确定性：降低权重，减少干扰
            },

            # 成交量事件权重配置
            "enable_volume_event_weighting": True,
            "volume_breakthrough_threshold": 3.0,       # 放量突破阈值
            "volume_breakthrough_multiplier": 1.5,      # 放量突破权重倍数
            "volume_anomaly_threshold": 2.0,            # 异常成交量阈值
            "volume_anomaly_multiplier": 1.3,           # 异常成交量权重倍数
            "low_volume_threshold": 0.3,                # 低成交量阈值
            "low_volume_penalty": 0.7,                  # 低成交量惩罚倍数

            # 技术指标确认权重配置
            "enable_technical_confirmation_weighting": True,
            "rsi_extreme_multiplier": 1.2,              # RSI极值权重倍数
            "macd_cross_multiplier": 1.3,               # MACD交叉权重倍数
            "bb_extreme_multiplier": 1.2,               # 布林带极值权重倍数
            "multi_indicator_consensus_multiplier": 1.15, # 多指标一致性权重倍数

            # 权重标准化配置
            "normalize_weights": True,
            "weight_clip_min": 0.05,                    # 最小权重（更严格）
            "weight_clip_max": 5.0,                     # 最大权重
            "target_weight_sum": None                    # None表示使用样本数
        },

        "market_analyzer_config": {                     # [字典] 市场状态分析器配置
            "volatility_threshold": 0.02,               # [浮点数] 波动率阈值
            "trend_strength_threshold": 0.5,            # [浮点数] 趋势强度阈值
            "volume_spike_threshold": 2.0,               # [浮点数] 成交量异常阈值

            # 🔧 修复：波动率相关阈值
            "high_volatility_atr_threshold": 2.5,       # [浮点数] 高波动率ATR阈值
            "low_volatility_atr_threshold": 0.8,        # [浮点数] 低波动率ATR阈值

            # 🔧 修复：趋势强度相关阈值
            "low_trend_strength_adx_threshold": 20.0,   # [浮点数] 低趋势强度ADX阈值
            "strong_trend_adx_threshold": 30.0,         # [浮点数] 强趋势ADX阈值

            # 🔧 修复：价格行为相关阈值
            "sideways_price_range_threshold": 0.05,     # [浮点数] 横盘价格区间阈值
            "price_whipsaw_threshold": 3,               # [整数] 价格鞭打次数阈值

            # 🔧 修复：趋势方向判断阈值
            "trend_direction_ema_threshold": 0.02,      # [浮点数] 趋势方向EMA阈值

            # 🔧 修复：时间窗口配置
            "price_range_lookback_periods": 20,         # [整数] 价格区间回看周期
            "trend_consistency_periods": 10,            # [整数] 趋势一致性周期

            # 🔧 修复：危险评分权重
            "danger_score_weights": {
                "high_volatility": 0.4,
                "low_trend_strength": 0.3,
                "price_whipsaw": 0.2,
                "volume_inconsistency": 0.1
            }
        },

        "boosting_type": "gbdt",                        # [字符串] LightGBM的提升类型，常用 'gbdt', 'dart', 'goss'。
        "random_state": 42,                             # [整数] 随机种子，用于确保模型训练的可复现性。
        "n_estimators": 8000,                           # [整数] 最终模型训练时允许的最大树数量 (通常配合早停)。
        "early_stopping_rounds": 250,                   # [整数] 最终模型训练时的早停轮数。如果在这么多轮内验证集指标没有改善，则停止训练。
        "verbose": 1,                                   # [整数] LightGBM训练日志的详细程度: -1 (静默), 0 (仅警告), 1 (信息), >1 (每N轮打印)。
        "verbosity": 1,                                 # [整数] 与 verbose 类似，用于控制LightGBM输出信息的级别 (通常设置一个即可，LGBMClassifier优先用verbosity)。
        "ensemble_runs": 1,                             # [整数] （已弃用或用途变更）原意可能是多次独立运行取平均，现在主要通过 ensemble_cv_folds 实现集成。
        "ensemble_cv_folds": 10,                        # [整数] 🎯 V5.0进阶：增加到10折，提升集成稳定性和泛化能力

        # --- Purged K-Fold交叉验证配置 (🎯 新增：防止数据泄露的高级交叉验证) ---
        "purged_cv_enable": True,                       # [布尔值] 是否启用Purged K-Fold交叉验证（推荐用于金融时间序列）
        "purged_cv_auto_purge_calculation": True,        # [布尔值] 是否自动计算purge长度（基于prediction_periods和特征窗口）
        "purged_cv_purge_length": 10,                    # [整数] 手动设置的清洗期长度（当auto_purge_calculation=False时使用）
        "purged_cv_embargo_length": 2,                   # [整数] 禁运期长度，在验证集后额外移除的样本数（进一步防止泄露）
        "purged_cv_min_train_size": None,                # [整数或None] 最小训练集大小，None为自动计算

        # 🎯 V11.0最新优化：Optuna 200次试验最佳参数组合
        "learning_rate": 0.0171,                        # [浮点数] 🏆 最新冠军参数：学习率
        "num_leaves": 12,                               # [整数] 🏆 最新冠军参数：叶子节点数 (降低复杂度)
        "max_depth": 8,                                 # [整数] 🏆 最新冠军参数：最大深度
        "reg_alpha": 13.1938,                          # [浮点数] 🏆 最新冠军参数：L1正则化系数 (增强)
        "reg_lambda": 27.3476,                         # [浮点数] 🏆 最新冠军参数：L2正则化系数 (增强)
        "colsample_bytree": 0.6490,                    # [浮点数] 🏆 最新冠军参数：特征采样比例
        "subsample": 0.9069,                           # [浮点数] 🏆 最新冠军参数：数据采样比例
        "min_child_samples": 107,                      # [整数] 🏆 最新冠军参数：最小子样本数
        "subsample_freq": 1,                            # [整数] 行采样的频率。0表示不进行行采样。
        "bagging_fraction": 0.8,            # 新增: 行采样比例 (等同于subsample)，明确写出
        "bagging_freq": 1,                  # 新增: 每迭代1次进行1次行采样
        "feature_fraction": 0.7,            # 新增: 列采样比例 (等同于colsample_bytree)，明确写出



        # --- SMOTE过采样配置 ---
        "smote_enable": True,                            # [布尔值] 是否为此目标启用SMOTE过采样
        "smote_k_neighbors": 3,                          # [整数] SMOTE的k_neighbors参数，会根据少数类样本数量自动调整
        "smote_min_samples_threshold": 5,                # [整数] 少数类样本的最小数量阈值，低于此值时跳过SMOTE
        "smote_random_state": 42,                        # [整数] SMOTE的随机种子

        # --- 最优阈值寻优配置 ---
        "threshold_optimization_enable": False,         # [布尔值] 🎯 用户要求：关闭UP模型阈值优化
        "threshold_optimization_method": "precision_constrained_recall", # [字符串] 🎯 用户建议3：精确率约束下最大化召回率
                                                        # "f1": 最大化F1分数
                                                        # "precision_recall": 基于精确率-召回率曲线的最优点
                                                        # "youden": Youden指数 (敏感性 + 特异性 - 1)
                                                        # "balanced": 平衡精确率和召回率
                                                        # "precision_constrained_recall": 在精确率约束下最大化召回率
        "threshold_min_precision": 0.62,                # [浮点数] 🎯 用户建议3：设定精准度约束为62%，略高于目标60%
        "threshold_save_to_metadata": True,             # [布尔值] 是否将最优阈值保存到模型元数据
        "threshold_default_value": 0.5,                 # [浮点数] 默认决策阈值
        "threshold_use_independent_validation": True,   # [布尔值] 是否使用独立验证集进行阈值优化
        "threshold_independent_val_ratio": 0.20,        # [浮点数] 独立验证集比例

        # --- 精确率约束配置 ---
        "threshold_min_precision": 0.62,               # [浮点数] 🎯 用户建议3：要求阈值优化找到的精确率至少为62%
                                                        # 当使用精确率约束方法时，只考虑精确率 >= 此值的阈值
                                                        # 在满足约束的阈值中选择召回率最高的
        "threshold_precision_constraint_fallback": True, # [布尔值] 当没有阈值满足精确率约束时，是否使用最接近约束的阈值

        # --- 集成模型预测阈值配置 ---
        "ensemble_prediction_threshold": 0.6,        # [浮点数] 集成模型预测阈值，用于将概率预测转换为二分类决策
                                                        # 当集成模型输出的"明确上涨"概率超过此阈值时，产生UP信号
                                                        # 此阈值独立于训练时的最优阈值，专门用于实时预测决策
                                                        # 影响范围：实时预测、回测、信号生成
        "ensemble_prediction_threshold_override": True, # [布尔值] 是否在实时预测时使用ensemble_prediction_threshold覆盖训练时的最优阈值
                                                        # True: 使用ensemble_prediction_threshold作为预测阈值
                                                        # False: 使用训练时优化得到的最优阈值

        # --- 概率校准 ---
        "enable_probability_calibration": True,         # [布尔值] 🎯 V4.0优化：重新启用概率校准，提升预测质量
        "calibration_brier_improvement_threshold": 0.0005, # [浮点数] 🎯 V4.0优化：提高校准阈值，只在显著改善时采用

        # --- SHAP 分析 ---
        "enable_shap_analysis_per_fold": False,         # [布尔值] 🚫 已禁用：不再需要单折SHAP分析
        "enable_shap_analysis_ensemble": False,         # [布尔值] 🚫 已禁用：只保留精英模型SHAP分析
        "enable_shap_analysis_elite": True,             # [布尔值] 🏆 精英模型分析：对表现最佳的子模型进行深度SHAP分析
        "elite_selection_metric": "test_f1_score",      # [字符串] 精英模型选择标准：test_f1_score, test_precision, test_recall, test_accuracy
        "elite_model_count": 3,                         # [整数] 精英模型数量：选择前N个表现最佳的fold进行分析
        "enable_shap_for_live_prediction": False,       # [布尔值] 是否在实时预测时计算每个预测的SHAP值。这通常性能开销较大，主要用于调试或深入分析。

        # --- 信号过滤逻辑 (方案一：关闭基础模型过滤，将信息作为特征喂给元模型) ---
        "enable_trend_detection": False,                # [布尔值] 关闭基础模型的趋势检测过滤，让元模型处理趋势信息。
            "trend_detection_timeframe": "30m",         # [字符串] 用于判断主要趋势的K线时间框架 (应等于或高于模型本身的 'interval')。
            "trend_indicator_type": 'adx',              # [字符串] 趋势判断使用的指标类型: 'adx' (Average Directional Index) 或 'ema_cross' (EMA均线交叉)。
            "trend_adx_period": 14,                     # [整数] 如果使用ADX，ADX的计算周期。
            "trend_adx_strength_threshold": 30,         # [整数] ADX值高于此阈值，认为趋势强劲。
            "trend_adx_threshold": 20,                  # [整数] ADX值高于此阈值，认为存在趋势（无论强弱）。
            "trend_ema_short_period": 20, "trend_ema_long_period": 50, # [整数] 如果使用EMA交叉，短期和长期EMA的周期。
            "trend_filter_strategy": 'filter_only',     # [字符串] 趋势过滤策略:
                                                        #   'filter_only': 如果信号与强趋势相反，则过滤掉信号。
                                                        #   'chase_trend': 如果当前无信号但存在强趋势，且模型对顺趋势方向有一定信心，则产生一个“追逐趋势”的信号。
                                                        #   'none': 不使用趋势过滤。
            "trend_chase_confidence_boost": 0.05,       # [浮点数, 0-1] 对于 'chase_trend' 策略，当追逐趋势时，对顺趋势方向概率的额外置信度提升要求。

        "enable_volatility_filter": False,              # [布尔值] 关闭基础模型的波动率过滤，让元模型处理波动率信息。
            "volatility_filter_timeframe": "30m",       # [字符串] 用于判断波动率的K线时间框架。
            "volatility_atr_period": 14,                # [整数] 计算波动率（基于ATR）的周期。
            "volatility_min_atr_percent": 0.1,          # [浮点数] ATR占收盘价的最小百分比。低于此值认为市场波动过低，可能过滤信号。
            "volatility_max_atr_percent": 1.2,          # [浮点数] ATR占收盘价的最大百分比。高于此值认为市场波动过高，可能过滤信号。

        "enable_dynamic_threshold": True,               # [布尔值] 是否根据当前趋势和波动率动态调整发出信号所需的概率阈值 (`signal_threshold`)。
            "signal_dynamic_threshold_base": 0.4,       # [浮点数, 0-1] 🚀 修复：重命名避免与目标变量创建的dynamic_threshold_base冲突
            "dynamic_threshold_trend_adjust": 0.05,     # [浮点数] 当信号与强趋势一致时，阈值降低的幅度；当信号与强趋势相反时，阈值增加的幅度。
            "dynamic_threshold_volatility_adjust": 0.03,# [浮点数] 当市场波动率过高或过低时，阈值增加的幅度 (使得信号更难触发)。

        # --- OOF参数 (如果计划让每个基础模型可以有不同的OOF生成策略，否则这些应为全局) ---
        # 注意：这些参数与顶层的 META_MODEL_OOF_... 参数用途相同，但在这里是针对单个基础模型。
        # 如果所有基础模型的OOF生成策略一致，则这些是冗余的，应使用全局参数。
        # 如果希望每个基础模型在OOF生成时有特定配置，则这些参数有效。
        "meta_model_oof_cv_folds": 5,                   # [整数] (同全局) 生成OOF预测时交叉验证的折数。
        "meta_model_oof_n_estimators_large": 3000,      # [整数] (同全局) OOF每折LGBM树数量。
        "meta_model_oof_early_stopping_rounds": 100,    # [整数] (同全局) OOF每折LGBM早停轮数。
        "meta_model_oof_early_stop_eval_ratio": 0.15,   # [浮点数] (同全局) OOF早停验证集比例。
        "meta_model_oof_min_samples_for_eval": 50,      # [整数] (同全局) OOF早停验证集最少样本。
    },

    # ==============================================================================
    # === 配置 BTC_15m_DOWN 模型 (专注于预测下跌信号) ===
    # ==============================================================================
    {
        # --- 目标基本信息 ---
        "name": "BTC_15m_DOWN",                         # [字符串] 目标唯一名称。
        "interval": "15m",                              # [字符串] K线周期。
        "symbol": "BTCUSDT",                            # [字符串] 交易对。
        "prediction_periods": [2],                      # [列表, 整数] 🎯 优化：改为预测30分钟，减少标签噪音，提升信号质量
        "prediction_minutes_display": 30,               # [整数] 🎯 更新：GUI显示时效为30分钟。
        "model_save_dir": "trained_models_btc_15m_down",# [字符串] 此DOWN模型的独立保存目录。
        "target_variable_type": "DOWN_ONLY",            # [字符串] **关键配置**: 此模型专门用于预测“明确下跌”。目标变量1为跌，0为非明确下跌。
        "drop_neutral_targets": False,                  # [布尔值] 通常为False，因为中性已归为0类。
        "device_type": 'cpu',                           # [字符串] 🚀 AMD处理器优化：使用CPU
        "prediction_trigger_type": "kline_close",       # [字符串] 主要由K线关闭触发。
        # "apscheduler_job_enabled": True,              # [布尔值, 可选] 是否启用APScheduler。
        # "apscheduler_trigger_type": "cron",           # [字符串, 可选] APScheduler触发类型。
        # "apscheduler_cron_config": {"minute": "*/15"},# [字典, 可选] Cron配置，例如每15分钟。

        # 🎯 V10.0集大成者：统一框架下的宏观调控配置
        "enable_expert_committee": False,               # [布尔值] ❌ V10.0：废除物理分割，使用统一模型
        "enable_market_state_adaptive": True,           # [布尔值] ✅ V10.0：启用市场状态自适应特征
        "enable_dynamic_sample_weighting": True,        # [布尔值] ✅ V10.0：启用动态样本权重宏观调控

        # --- 特征工程参数 ---
        # 🔧 Optimized Switches (优化开关):
        "enable_price_change": True,                    # [布尔值] 是否启用价格变动百分比特征。
        "enable_volume": True,                          # [布尔值] 是否启用成交量相关特征。
        "enable_candle": True,                          # [布尔值] 是否启用基于K线形状的特征。
        "enable_ta": True,                              # [布尔值] 是否启用基于pandas-ta库计算的技术分析指标。
        "enable_time": False,                           # [布尔值] 🎯 DOWN模型优化：禁用时间特征，减少噪音
        "enable_fund_flow": False,                      # [布尔值] 🎯 DOWN模型优化：禁用资金流特征，专注价格动量
        "enable_mtfa": True,                            # [布尔值] 是否启用多时间框架分析 (MTFA) 特征。
        "mtfa_timeframes": ['30m', '1h', '4h'],         # [列表] MTFA时间框架配置
        "enable_pattern_recognition": True,             # [布尔值] 是否启用高级K线形态识别。
        "enable_trend_slope": True,                     # [布尔值] 是否启用趋势斜率分析特征。
        "enable_market_state_adaptive": True,           # [布尔值] 🎯 新增：是否启用市场状态自适应特征。
        "enable_derivatives_features": True,            # [布尔值] 是否启用衍生品市场数据特征（资金费率、持仓量、多空比等）。

        # 🎯 V10.0集大成者：高级特征工程配置
        "enable_higher_order_features": True,            # [布尔值] 🎯 恢复高阶特征：在优化标签和目标下利用高级特征
        "enable_interaction_features": True,             # [布尔值] 🎯 恢复交互特征：状态×指标组合提升预测能力

        # 🚀 MTFA特征过滤器配置
        "force_new_mtfa_filter": True,                  # [布尔值] ✅ 强制使用新的MTFA列过滤器。True: 导入失败时报错; False: 回退到传统方法
        "mtfa_filter_config": {                         # [字典] MTFA过滤器的详细配置
            "exclude_diagnostic_columns": True,         # [布尔值] 排除统计/诊断列
            "exclude_business_columns": True,            # [布尔值] 排除业务逻辑列
            "loose_inclusion_mode": False,               # [布尔值] 严格包含模式
            "custom_exclusion_patterns": [],            # [列表] 自定义排除模式
            "custom_inclusion_patterns": [],            # [列表] 自定义包含模式
            "regex_exclusion_patterns": [],             # [列表] 正则排除模式
            "regex_inclusion_patterns": []              # [列表] 正则包含模式
        },

        # ⚙️ Optimized Parameters (优化参数):
        "volume_avg_period": 21,                         # [整数] 成交量移动平均周期。
        "atr_period": 7,                               # [整数] Average True Range (ATR) 的周期。
        "rsi_period": 22,                               # [整数] Relative Strength Index (RSI) 的周期。
        "willr_period": 22,                             # [整数] Williams %R 周期参数 (匹配训练模型)
        "cci_period": 22,                               # [整数] CCI 周期参数 (匹配训练模型)
        "macd_fast": 15,                                # [整数] MACD快线EMA周期。
        "macd_slow": 27,                                # [整数] MACD慢线EMA周期。
        "macd_sign": 15,                                # [整数] MACD信号线EMA周期。
        "stoch_k": 8,                                  # [整数] Stochastic %K周期。
        "stoch_d": 5,                                   # [整数] Stochastic %D周期。
        "stoch_smooth_k": 5,                            # [整数] Stochastic %K平滑周期。
        "fund_flow_ratio_smoothing_period": 8,         # [整数] 资金流指标平滑处理周期 (匹配训练模型)。
        "target_threshold": 0.0025,                     # [浮点数] 🎯 精度优化：提高目标阈值到0.25%，筛选更明确的上涨信号
        "hma_period": 21,                               # [整数] Hull Moving Average (HMA) 周期。
        "kc_period": 24,                                # [整数] Keltner Channel EMA中线周期。
        "kc_atr_period": 17,                            # [整数] Keltner Channel ATR计算周期。
        "kc_multiplier": 2.431932682355995,                      # [浮点数] Keltner Channel ATR倍数。
        "signal_threshold": 0.6309671569917381,                        # [浮点数] 模型预测信号阈值。
        "trend_adx_period": 11,                         # [整数] ADX趋势指标计算周期。
        "trend_adx_threshold": 32,                      # [整数] ADX趋势强度阈值。
        "trend_ema_short_period": 19,                   # [整数] 趋势分析短期EMA周期。
        "trend_ema_long_period": 35,                    # [整数] 趋势分析长期EMA周期。
        "trend_slope_period_1": 3,                      # [整数] 第一个趋势斜率计算周期。
        "trend_slope_period_2": 22,                     # [整数] 第二个趋势斜率计算周期。
        # 🎯 新增：DOWN模型专用EMA距离特征参数
        "ema_short_period_down": 10,                    # [整数] DOWN模型短期EMA周期，用于计算均线距离特征。
        "ema_long_period_down": 30,                     # [整数] DOWN模型长期EMA周期，用于计算均线距离特征。
        # 🎯 新增：DOWN模型专用布林带突破强度特征参数
        "bb_period": 20,                                # [整数] 布林带计算周期。
        "bb_std": 2.0,                                  # [浮点数] 布林带标准差倍数。
        # 🎯 新增：时间框架敏感度特征参数
        "enable_timeframe_sensitivity": True,           # [布尔值] 是否启用时间框架敏感度特征。
        "tf_sensitivity_reference_timeframe": "4h",     # [字符串] 参考时间框架（用于对比）。
        "tf_sensitivity_features": ["rsi", "close_pos_in_candle", "macd", "volume_ratio"],  # [列表] 要计算对比的特征类型。
        "doji_threshold": 0.1453139927466848,                     # [浮点数] 十字星形态阈值。
        "hammer_body_ratio": 0.38937696296971414,                  # [浮点数] 锤子线实体比例阈值。
        "hammer_shadow_ratio": 2.2601513266757376,                # [浮点数] 锤子线影线比例阈值。
        "marubozu_threshold": 0.8741915362541589,                     # [浮点数] 光头光脚线阈值。
        "spinning_top_threshold": 0.6376683909810659,                  # [浮点数] 陀螺线阈值。
        "engulfing_body_multiplier": 1.05,          # [浮点数] 吞没形态倍数阈值。

        # --- 🚀 K线形态优先级配置 ---
        "enable_pattern_priority_system": True,                    # [布尔值] 是否启用形态优先级/互斥处理系统
        "pattern_priority_levels": {                               # [字典] 形态优先级配置
            "high_priority": ["Doji", "Bullish_Engulfing", "Bearish_Engulfing"],  # 高优先级形态
            "medium_priority": ["Morning_Star", "Evening_Star", "Hammer", "Shooting_Star"],  # 中优先级形态
            "low_priority": ["Spinning_Top", "Green_Long_Body", "Red_Long_Body"]  # 低优先级形态
        },
        "allow_pattern_override": False,                           # [布尔值] 是否允许低优先级形态覆盖高优先级形态
        "morning_evening_star_body_ratio": 0.09062130825046674,    # [浮点数] 启明星/黄昏星比例阈值。
        "doji_threshold_batac": 0.11355368203773268,               # [浮点数] 多K线十字星阈值。

        # 📊 Additional Configuration (附加配置):
        "price_change_periods": [1, 2, 3, 5, 10],       # [列表] 价格变动计算周期。
        "cci_constant": 0.002404,                       # [浮点数] CCI计算常数 (匹配DOWN模型)。
        "enable_time_trigonometric": True,              # [布尔值] 是否启用时间三角函数编码。
        "enable_adx_trend_features": True,              # [布尔值] 是否启用ADX趋势特征。
        "enable_ema_trend_features": True,              # [布尔值] 是否启用EMA交叉趋势特征。
        "enable_ta_derived_features": True,             # [布尔值] 是否启用技术指标衍生特征。
        "mtfa_timeframes": ['30m','1h', '4h'],          # [列表] MTFA时间框架。
        "mtfa_feature_lookback_periods": 200,           # [整数] MTFA特征回看周期。
        "mtfa_specific_lookbacks": {},                  # [字典] MTFA特定回看周期。
        "mtfa_min_bars_to_fetch": 50,                   # [整数] MTFA最小K线数量。
        "mtfa_min_bars_for_calc": 50,                   # [整数] MTFA计算最小K线数量。
        "mtfa_fetch_buffer": 10,                        # [整数] MTFA数据获取缓冲区。

        # --- 衍生品市场特征配置 (动态自适应版) ---
        # 1. 定义需要哪些衍生品数据源
        "derivatives_data_sources": ["funding_rate", "open_interest", "long_short_ratio", "top_trader_ratio"],

        # 2. 定义特征计算参数 (告诉程序要算什么)
        "derivatives_feature_params": {
            "funding_rate": {
                "moving_averages": [8, 24, 96],         # 计算8周期、24周期、96周期的移动平均 (对应2h, 6h, 24h @ 15m)
                "pct_changes": [4, 12, 48],             # 计算1h, 3h, 12h的资金费率变化率
                "rolling_std": [8, 24]                  # 计算8周期、24周期的滚动标准差
            },
            "open_interest": {
                "moving_averages": [12, 48, 192],       # 计算3h, 12h, 48h的持仓量移动平均
                "pct_changes": [4, 12, 48],             # 计算1h, 3h, 12h的持仓量变化率
                "rolling_std": [12, 48]                 # 计算持仓量波动性指标
            },
            "long_short_ratio": {
                "moving_averages": [16, 64, 256],       # 计算4h, 16h, 64h的多空比移动平均
                "pct_changes": [8, 24],                 # 计算2h, 6h的多空比变化率
                "sentiment_indicators": True            # 计算情绪偏向等衍生指标
            },
            "top_trader_ratio": {
                "moving_averages": [16, 64],            # 计算4h, 16h的大户多空比移动平均
                "divergence_analysis": True             # 计算与散户情绪的分歧指标
            }
        },

        # 3. 数据获取的缓冲设置 (告诉程序要多拿多少数据)
        "derivatives_fetch_buffer_multiplier": 1.5,     # [浮点数] 在计算出的所需数据量基础上，再多拿50%作为缓冲
        "derivatives_min_fetch_limit": 200,             # [整数] 无论如何，最少也要拿200条数据
        "derivatives_max_fetch_limit": 1000,            # [整数] 最多获取1000条数据（API限制）
        "derivatives_period_alignment": True,           # [布尔值] 是否将衍生品数据周期与主K线周期对齐

        # --- 高级数据处理配置 ---
        "enable_intelligent_nan_processing": True,      # [布尔值] 是否启用智能NaN/Inf处理，根据特征类型选择合适的默认值。
        "enable_safe_fill_nans": True,                  # [布尔值] 是否启用安全的NaN填充方法，仅使用历史数据进行填充。
        "min_historical_bars_for_prediction": 100,      # [整数] 实时预测时所需的最小历史K线数量。
        "enable_advanced_feature_validation": True,     # [布尔值] 是否启用高级特征验证和错误处理。

        # --- 目标变量定义 和 预测信号阈值 (可与UP模型不同，针对DOWN预测调整) ---
        "target_threshold": 0.002,      # [浮点数] 定义“明确下跌”所需的最小价格变动百分比 (例如，价格下跌超过0.2%)。
        "signal_threshold": 0.4,        # [浮点数, 0-1] 模型输出的“明确下跌”概率需要高于此阈值，才初步认为是一个有效的看空信号。
        # "labeling_method": "pure_direction",  # [字符串] ❌ 已禁用：纯方向标签法产生大量噪音
        "labeling_method": "threshold",        # [字符串] ✅ 启用传统阈值法以配合三道屏障

        # --- 🎯 动态波动率阈值配置 (革新目标变量定义) - 策略一优化 ---
        "enable_dynamic_thresholds": False,             # [布尔值] 🎯 **关键**：禁用动态阈值，完全依赖三道屏障法定义标签
        "dynamic_threshold_base": "ATRr_14",            # [字符串] 用作动态阈值基准的波动率指标列名 (ATR百分比)
        "dynamic_threshold_multipliers": [0.5],         # [列表] 🎯 策略一：适度提高至0.5，平衡信号质量与数量
        "dynamic_threshold_min": 0.001,                # [浮点数] 🎯 策略一：提高最小阈值，减少噪音信号
        "dynamic_threshold_max": 0.025,                 # [浮点数] 🎯 策略一：降低最大阈值，增加正类样本
        "dynamic_threshold_smoothing": 3,               # [整数] 动态阈值的平滑周期，0表示不平滑

        # --- 🚀 多周期融合增强配置 (Multi-Timeframe Target Enhancement) - 策略一优化 ---
        "enable_multi_timeframe_target": True,          # [布尔值] 🎯 策略一：与三道屏障协同，增强信号确认度
        "multi_timeframe_secondary_interval": "5m",     # [字符串] 辅助时间周期，用于增强目标变量定义
        "multi_timeframe_min_bearish_ratio": 0.67,      # [浮点数] 🎯 策略一：DOWN模型要求67%小周期K线同向下跌

        # --- 🚀 三道屏障标签法配置 (Triple-Barrier Method) - 对称化优化 ---
        "enable_triple_barrier": True,                 # [布尔值] ✅ 启用三道屏障法：提升标签质量，减少噪音
        "triple_barrier_profit_multiplier": 1.8,        # [浮点数] 🎯 优化标签定义：1.8倍ATR止盈，提升信号质量
        "triple_barrier_loss_multiplier": 1.0,          # [浮点数] 🎯 微调标签定义：1.0倍ATR止损，实现1.5:1.0不对称比例
        "triple_barrier_use_fixed": False,             # [布尔值] 🎯 策略一：使用动态ATR而非固定百分比
        "triple_barrier_fixed_profit": 0.015,           # [浮点数] 固定止盈阈值 (1.5%)，降低以增加正类样本
        "triple_barrier_fixed_loss": 0.012,             # [浮点数] 固定止损阈值 (1.2%)，实现不对称盈亏比
        "triple_barrier_min_profit": 0.012,             # [浮点数] 🎯 优化：提高最小止盈阈值到1.2%，过滤微小波动
        "triple_barrier_max_profit": 0.05,              # [浮点数] 最大止盈阈值 (5%)，防止屏障过大
        "triple_barrier_min_loss": 0.004,               # [浮点数] 最小止损阈值 (0.3%)，防止屏障过小
        "triple_barrier_max_loss": 0.035,                # [浮点数] 最大止损阈值 (3%)，防止屏障过大

        # --- 🚀 质量权重配置 (Meta Label Quality Weighting) - 教科书级别筛选 ---
        "enable_meta_label_quality_weighting": True,   # [布尔值] 🎯 启用质量权重，筛选教科书级别的交易案例
        "meta_label_quality_config": {
            "efficiency_weight_config": {
                "max_efficiency_multiplier": 3.0,      # [浮点数] 🎯 用户建议1：从2.0提升到3.0，更重地奖励快速达标的案例
                "direction_penalty": 0.2,              # [浮点数] 🎯 严格惩罚反向案例，降低其权重
                "enable_efficiency_weighting": True,   # [布尔值] 启用效率权重计算
            },
            "smoothness_weight_config": {
                "max_smoothness_multiplier": 2.0,      # [浮点数] 🎯 提高平滑度权重倍数，奖励低回撤路径
                "drawdown_threshold": 0.15,            # [浮点数] 🎯 用户建议1：从0.25降低到0.15，更严厉地惩罚回撤
                "enable_smoothness_weighting": True,   # [布尔值] 启用平滑度权重计算
            },
            "logging_config": {
                "enable_detailed_logging": True,       # [布尔值] 启用详细日志记录
                "log_sample_statistics": True,         # [布尔值] 记录样本统计信息
                "log_weight_distribution": True,       # [布尔值] 记录权重分布信息
            }
        },

        # --- 🚀 分层数据策略特征工程配置 (Layered Data Strategy Features) ---
        "enable_macro_trend_features": True,           # [布尔值] 🎯 启用宏观市场状态特征
        "macro_ema_period": 200,                       # [整数] 🎯 宏观EMA周期，用于长期趋势过滤
        "long_term_normalization_methods": ["zscore", "percentile"],  # [列表] 🎯 长期归一化方法
        "enable_long_term_feature_stats": True,       # [布尔值] 🎯 启用长期特征统计量计算

        # --- 🚀 反向验证机制特征配置 (Reverse Validation Features) ---
        "enable_reverse_validation": True,            # [布尔值] 🎯 启用反向验证特征
        "dual_kill_threshold": 0.6,                   # [浮点数] 🎯 多空双杀风险阈值

        # --- 🎯 V10.1：为DOWN模型重新启用特征选择，优化稀有信号识别 ---
        "two_stage_feature_selection_enable": True,     # [布尔值] ✅ V10.1：重新启用两阶段特征选择，寻找最佳特征组合
        "importance_prescreening_ratio": 0.5,           # [浮点数] 第一阶段重要性初筛保留的特征比例（0.6表示保留60%特征）

        # --- RFE (Recursive Feature Elimination) 配置 ---
        "rfe_enable": False,                          # [布尔值] 是否启用RFE递归特征消除来进行特征选择。
         "rfe_cv_folds": 3,                              # [整数, 可选] 如果启用RFE，RFE内部交叉验证的折数。
         "rfe_step": 1,                                  # [整数或浮点数, 可选] RFE每次迭代移除特征的数量或百分比。
         "rfe_scoring": "binary_profit_precision_composite", # [字符串, 可选] 🎯 根本性优化：RFE使用盈利导向复合指标，与Optuna保持一致。
         "rfe_estimator_n_estimators": 100,              # [整数, 可选] RFE内部使用的LightGBM评估器的树数量。
         "rfe_estimator_learning_rate": 0.1,             # [浮点数, 可选] RFE内部LightGBM评估器的学习率。
         "rfe_estimator_num_leaves": 31,                 # [整数, 可选] RFE内部LightGBM评估器的叶子数。
         "rfe_min_features_to_select": 30,               # [整数, 可选] RFE最终选择的最小特征数量。

         # --- 🚀 RFE阈值优化配置 ---
         "rfe_threshold_strategy": "baseline_optimal",   # [字符串] RFE阈值策略: 'fixed', 'baseline_optimal', 'cv_optimal'
         "rfe_baseline_validation_ratio": 0.2,           # [浮点数] 基准验证集比例 (用于寻找最优阈值)
         "rfe_threshold_search_range": [0.1, 0.9],       # [列表] 阈值搜索范围
         "rfe_threshold_search_step": 0.05,              # [浮点数] 阈值搜索步长
         "rfe_min_validation_size": 50,                  # [整数] 最小验证集大小
         "rfe_fixed_threshold": 0.5,                     # [浮点数] 固定阈值 (当strategy='fixed'时使用)

         # --- 🚀 RFE性能优化配置 ---
         "rfe_data_subsampling_enable": True,            # [布尔值] 是否启用数据子采样来加速RFE计算
         "rfe_data_subsampling_ratio": 0.4,              # [浮点数] 数据子采样比例，0.3表示使用30%的数据进行特征选择

        # --- 特征选择：基于初始模型重要性进行筛选 ---
        "importance_thresholding_enable": True,         # [布尔值] 是否在RFE之后（或直接在全部特征上）再进行一次基于LightGBM初始重要性的特征筛选。
        "importance_threshold_value": None,               # [整数] 如果 `importance_top_n_features` 未设置或为None，则移除重要性绝对值低于此阈值的特征。
        "importance_top_n_features": 40,                # [整数 或 None] 如果设置了此值，则优先选择重要性最高的Top N个特征。如果为None，则使用 `importance_threshold_value`。
        "importance_model_n_estimators": 500,           # [整数] 用于获取初始特征重要性的临时LightGBM模型的树数量。
        # 以下为用于获取初始重要性的LGBM模型的超参数，可以根据DOWN模型的特性进行微调。
        "learning_rate_initial_imp": 0.0064, # [浮点数] 初始重要性模型的学习率。
        "num_leaves_initial_imp": 19,                   # [整数] 初始重要性模型的每棵树最大叶子数。
        "max_depth_initial_imp": 8,                     # [整数] 初始重要性模型的每棵树最大深度。
        "reg_alpha_initial_imp": 12.4750,                   # [浮点数] 初始重要性模型的L1正则化系数。
        "reg_lambda_initial_imp": 12.2479,                  # [浮点数] 初始重要性模型的L2正则化系数。
        "colsample_bytree_initial_imp": 0.5929, # [浮点数] 初始重要性模型的列采样率。
        "subsample_initial_imp": 0.5518,    # [浮点数] 初始重要性模型的行采样率。
        "min_child_samples_initial_imp": 84,            # [整数] 初始重要性模型一个叶子节点上所需的最小数据量。
        "metric_initial_imp": 'binary_logloss',         # [字符串] 初始重要性模型训练时使用的评估指标。

        # --- 🎯 DOWN模型超参数优化已完成，关闭Optuna ---
        "optuna_enable": False,                        # [布尔值] ❌ 关闭Optuna，使用已优化的最佳参数
        "optuna_n_trials": 150,                         # [整数] 🎯 DOWN模型优化：增加试验次数，寻找更好的正则化参数
        "optuna_timeout": None,                         # [整数 或 None] Optuna运行的最大秒数。如果为None，则不限制时间，直到达到 `optuna_n_trials`。
        "optuna_metric": "binary_profit_precision_composite", # [字符串] 🎯 升级优化目标：盈利导向复合指标 (70%盈利+30%精确率)
        # 🎯 DOWN模型也使用复合指标，确保信号质量和盈利能力的平衡
        "optuna_direction": "maximize",                 # [字符串] Optuna优化目标指标的方向: "maximize" (最大化) 或 "minimize" (最小化)。
        "optuna_cv_folds": 5,                           # [整数] 🎯 DOWN模型优化：增加CV折数，提升验证稳定性
        "optuna_trial_n_estimators_max": 1000,          # [整数] 🎯 DOWN模型优化：降低最大估计器数量，防止过拟合
        "optuna_trial_early_stopping_rounds": 30,       # [整数] 🎯 DOWN模型优化：更早停止，防止过拟合
        "optuna_trial_eval_metric": "binary_logloss",   # [字符串] Optuna单次试验中，LightGBM早停所依据的评估指标。

        # --- LightGBM 模型固定/回退参数 ---
        # 这些参数在 Optuna 未启用或优化失败时，作为模型的默认参数。
        # 如果Optuna成功，它找到的最佳参数会覆盖这里的对应项。
        "objective": "binary",                          # [字符串] LightGBM学习任务目标：'binary' 表示二分类。
        "metric": "binary_logloss",                     # [字符串 或 列表] 模型训练过程中的评估指标，也用于早停。例如 'binary_logloss', 'auc', 'average_precision'。
        "class_weight": {0: 1.0, 1: 4.0},                  # [字典] 🎯 恢复激进类别权重：4.0倍权重强化下跌信号学习

        # --- ⚖️ 动态样本权重配置 (让模型"学得更聪明") ---
        "enable_dynamic_sample_weighting": True,        # [布尔值] 是否启用动态样本权重计算
        "time_decay_factor": 0.1,                       # [浮点数] 时间衰减因子，越新的数据权重越高

        # 🎯 增强的动态样本权重配置（DOWN模型专用）
        "dynamic_sample_weighting": {
            "enable_dynamic_weighting": True,
            "weighting_strategies": ["time_decay", "volatility", "market_state"],
            "strategy_weights": {"time_decay": 0.3, "volatility": 0.4, "market_state": 0.3},
            "combination_method": "weighted_average",

            # 时间衰减配置
            "time_decay_rate": 0.1,
            "time_decay_unit": "samples",
            "time_decay_power": 1.0,

            # 波动率加权配置
            "volatility_column": "ATRr_14",
            "volatility_power": 1.5,
            "volatility_cap": 3.0,

            # 🎯 强化市场状态加权配置（DOWN模型专用）- 多地形作战
            "enable_market_state_weighting": True,
            "market_state_weights": {
                # 🎯 DOWN模型核心关注状态 - 超高权重
                "strong_downtrend": 6.0,                # DOWN模型最重要：强下跌趋势
                "panic_selling": 8.0,                   # DOWN模型极度重视：恐慌性抛售
                "liquidity_drought": 5.0,               # 流动性枯竭：DOWN模型重要信号

                # 🎯 反向信号状态 - 高权重学习
                "strong_uptrend": 4.0,                  # 强上涨：重要的反向信号
                "bubble_state": 5.0,                    # 泡沫状态：DOWN模型的机会

                # 🎯 中等重要状态
                "high_certainty": 3.0,                  # 高确定性：重要参考
                "low_vol_sideways": 2.0,                # 低波动盘整：潜在下跌酝酿
                "normal_trend": 1.0,                    # 正常趋势：基准权重

                # 🎯 噪音状态 - 极低权重
                "high_vol_sideways": 0.1,               # 高波动盘整：噪音，极低权重
                "extreme_volatility": 0.0,              # 极端波动：忽略
                "low_certainty": 0.2                    # 低确定性：大幅降低权重
            },

            # 成交量事件权重配置
            "enable_volume_event_weighting": True,
            "volume_breakthrough_threshold": 3.0,
            "volume_breakthrough_multiplier": 1.5,
            "volume_anomaly_threshold": 2.0,
            "volume_anomaly_multiplier": 1.3,
            "low_volume_threshold": 0.3,
            "low_volume_penalty": 0.7,

            # 技术指标确认权重配置
            "enable_technical_confirmation_weighting": True,
            "rsi_extreme_multiplier": 1.2,
            "macd_cross_multiplier": 1.3,
            "bb_extreme_multiplier": 1.2,
            "multi_indicator_consensus_multiplier": 1.15,

            # 权重标准化配置
            "normalize_weights": True,
            "weight_clip_min": 0.05,
            "weight_clip_max": 5.0,
            "target_weight_sum": None
        },

        "market_analyzer_config": {                     # [字典] 市场状态分析器配置
            "volatility_threshold": 0.02,               # [浮点数] 波动率阈值
            "trend_strength_threshold": 0.5,            # [浮点数] 趋势强度阈值
            "volume_spike_threshold": 2.0,               # [浮点数] 成交量异常阈值

            # 🔧 修复：波动率相关阈值
            "high_volatility_atr_threshold": 2.5,       # [浮点数] 高波动率ATR阈值
            "low_volatility_atr_threshold": 0.8,        # [浮点数] 低波动率ATR阈值

            # 🔧 修复：趋势强度相关阈值
            "low_trend_strength_adx_threshold": 20.0,   # [浮点数] 低趋势强度ADX阈值
            "strong_trend_adx_threshold": 30.0,         # [浮点数] 强趋势ADX阈值

            # 🔧 修复：价格行为相关阈值
            "sideways_price_range_threshold": 0.05,     # [浮点数] 横盘价格区间阈值
            "price_whipsaw_threshold": 3,               # [整数] 价格鞭打次数阈值

            # 🔧 修复：趋势方向判断阈值
            "trend_direction_ema_threshold": 0.02,      # [浮点数] 趋势方向EMA阈值

            # 🔧 修复：时间窗口配置
            "price_range_lookback_periods": 20,         # [整数] 价格区间回看周期
            "trend_consistency_periods": 10,            # [整数] 趋势一致性周期

            # 🔧 修复：危险评分权重
            "danger_score_weights": {
                "high_volatility": 0.4,
                "low_trend_strength": 0.3,
                "price_whipsaw": 0.2,
                "volume_inconsistency": 0.1
            }
        },

        "boosting_type": "gbdt",                        # [字符串] LightGBM的提升类型，常用 'gbdt', 'dart', 'goss'。
        "random_state": 42,                             # [整数] 随机种子，用于确保模型训练的可复现性。
        "n_estimators": 8000,                           # [整数] 最终模型训练时允许的最大树数量 (通常配合早停)。
        "early_stopping_rounds": 300,                   # [整数] 最终模型训练时的早停轮数。如果在这么多轮内验证集指标没有改善，则停止训练。
        "verbose": 1,                                   # [整数] LightGBM训练日志的详细程度: -1 (静默), 0 (仅警告), 1 (信息), >1 (每N轮打印)。
        "verbosity": 1,                                 # [整数] 与 verbose 类似，用于控制LightGBM输出信息的级别 (通常设置一个即可，LGBMClassifier优先用verbosity)。
        "ensemble_runs": 1,                             # [整数] （已弃用或用途变更）原意可能是多次独立运行取平均，现在主要通过 ensemble_cv_folds 实现集成。
        "ensemble_cv_folds": 10,                         # [整数] 在最终模型训练时，使用多少折 TimeSeriesSplit 进行交叉验证式的集成训练。如果为1，则不进行CV集成，只训练一个单模型。

        # --- Purged K-Fold交叉验证配置 (🎯 新增：防止数据泄露的高级交叉验证) ---
        "purged_cv_enable": True,                       # [布尔值] 是否启用Purged K-Fold交叉验证（推荐用于金融时间序列）
        "purged_cv_auto_purge_calculation": True,        # [布尔值] 是否自动计算purge长度（基于prediction_periods和特征窗口）
        "purged_cv_purge_length": 10,                    # [整数] 手动设置的清洗期长度（当auto_purge_calculation=False时使用）
        "purged_cv_embargo_length": 2,                   # [整数] 禁运期长度，在验证集后额外移除的样本数（进一步防止泄露）
        "purged_cv_min_train_size": None,                # [整数或None] 最小训练集大小，None为自动计算

        # 🎯 优化后的DOWN模型最佳超参数（来自Optuna优化结果）
        "learning_rate": 0.0140,                        # [浮点数] 🎯 优化后参数：学习率
        "num_leaves": 20,                               # [整数] 🎯 优化后参数：每棵树的最大叶子数
        "max_depth": 8,                                 # [整数] 🎯 优化后参数：树的最大深度
        "reg_alpha": 11.0119,                           # [浮点数] 🎯 优化后参数：L1正则化系数
        "reg_lambda": 24.8725,                          # [浮点数] 🎯 优化后参数：L2正则化系数
        "colsample_bytree": 0.5915,                     # [浮点数] 🎯 优化后参数：每棵树训练时使用的特征比例
        "subsample": 0.8789,                            # [浮点数] 🎯 优化后参数：每棵树训练时使用的样本比例
        "min_child_samples": 113,                       # [整数] 🎯 优化后参数：一个叶子节点上所需的最小数据量
        "subsample_freq": 1,                            # [整数] 行采样的频率。0表示不进行行采样。
        "bagging_fraction": 0.8,            # 新增: 行采样比例 (等同于subsample)，明确写出
        "bagging_freq": 1,                  # 新增: 每迭代1次进行1次行采样
        "feature_fraction": 0.7,            # 新增: 列采样比例 (等同于colsample_bytree)，明确写出




        # --- SMOTE过采样配置 ---
        "smote_enable": True,                            # [布尔值] 🎯 V10.1：重新启用SMOTE，配合宽松阈值增强稀有样本
        "smote_k_neighbors": 3,                          # [整数] SMOTE的k_neighbors参数，会根据少数类样本数量自动调整
        "smote_min_samples_threshold": 5,                # [整数] 少数类样本的最小数量阈值，低于此值时跳过SMOTE
        "smote_random_state": 42,                        # [整数] SMOTE的随机种子

        # --- 最优阈值寻优配置 ---
        "threshold_optimization_enable": False,         # [布尔值] 🎯 用户要求：关闭DOWN模型阈值优化
        "threshold_optimization_method": "precision_constrained_recall", # [字符串] 🎯 用户建议3：精确率约束下最大化召回率
                                                        # "f1": 最大化F1分数
                                                        # "precision_recall": 基于精确率-召回率曲线的最优点
                                                        # "youden": Youden指数 (敏感性 + 特异性 - 1)
                                                        # "balanced": 平衡精确率和召回率
                                                        # "precision_constrained_recall": 在精确率约束下最大化召回率
        "threshold_save_to_metadata": True,             # [布尔值] 是否将最优阈值保存到模型元数据
        "threshold_default_value": 0.5,                 # [浮点数] 默认决策阈值
        "threshold_use_independent_validation": True,   # [布尔值] 是否使用独立验证集进行阈值优化
        "threshold_independent_val_ratio": 0.20,        # [浮点数] 独立验证集比例

        # --- 精确率约束配置 ---
        "threshold_min_precision": 0.62,               # [浮点数] 🎯 用户建议3：要求阈值优化找到的精确率至少为62%
                                                        # 当使用精确率约束方法时，只考虑精确率 >= 此值的阈值
                                                        # 在满足约束的阈值中选择召回率最高的
        "threshold_precision_constraint_fallback": True, # [布尔值] 当没有阈值满足精确率约束时，是否使用最接近约束的阈值

        # --- 集成模型预测阈值配置 ---
        "ensemble_prediction_threshold": 0.6,        # [浮点数] 集成模型预测阈值，用于将概率预测转换为二分类决策
                                                        # 当集成模型输出的"明确上涨"概率超过此阈值时，产生UP信号
                                                        # 此阈值独立于训练时的最优阈值，专门用于实时预测决策
                                                        # 影响范围：实时预测、回测、信号生成
        "ensemble_prediction_threshold_override": True, # [布尔值] 是否在实时预测时使用ensemble_prediction_threshold覆盖训练时的最优阈值
                                                        # True: 使用ensemble_prediction_threshold作为预测阈值
                                                        # False: 使用训练时优化得到的最优阈值

        # --- 概率校准 (可独立配置是否对DOWN模型进行校准) ---
        "enable_probability_calibration": True,       # [布尔值] 是否对DOWN模型输出的概率进行校准。
        "calibration_brier_improvement_threshold": 0.0001, # [浮点数] 校准后Brier Score改善阈值。

        # --- SHAP 分析 (可独立配置是否为DOWN模型进行SHAP分析) ---
        "enable_shap_analysis_per_fold": False,       # [布尔值] 🚫 已禁用：不再需要单折SHAP分析
        "enable_shap_analysis_ensemble": False,       # [布尔值] 🚫 已禁用：只保留精英模型SHAP分析
        "enable_shap_analysis_elite": True,           # [布尔值] 🏆 DOWN模型精英分析：对表现最佳的子模型进行深度SHAP分析
        "elite_selection_metric": "test_f1_score",    # [字符串] DOWN模型精英选择标准
        "elite_model_count": 3,                       # [整数] DOWN模型精英数量
        "enable_shap_for_live_prediction": False,     # [布尔值] 实时预测DOWN信号时是否计算SHAP值 (通常保持False以优化性能)。

        # --- 信号过滤逻辑 (方案一：关闭基础模型过滤，将信息作为特征喂给元模型) ---
        "enable_trend_detection": False,              # [布尔值] 关闭DOWN基础模型的趋势检测过滤，让元模型处理趋势信息。
            "trend_detection_timeframe": "30m",       # [字符串] 判断趋势的K线周期。
            "trend_indicator_type": 'adx',            # [字符串] 趋势指标类型 ('adx' 或 'ema_cross')。
            "trend_adx_period": 14,                   # [整数] ADX周期。
            "trend_adx_strength_threshold": 30,       # [整数] ADX强趋势阈值。
            "trend_adx_threshold": 20,                # [整数] ADX趋势存在阈值。
            "trend_filter_strategy": 'filter_only',   # [字符串] 趋势过滤策略 ('filter_only', 'chase_trend', 'none')。
            "trend_chase_confidence_boost": 0.05,     # [浮点数] 'chase_trend'策略下的概率提升。
        "enable_volatility_filter": False,            # [布尔值] 关闭DOWN基础模型的波动率过滤，让元模型处理波动率信息。
            "volatility_filter_timeframe": "30m",     # [字符串] 判断波动率的K线周期。
            "volatility_atr_period": 14,              # [整数] ATR周期。
            "volatility_min_atr_percent": 0.1,        # [浮点数] 最小ATR百分比 (波动过低)。
            "volatility_max_atr_percent": 1.2,        # [浮点数] 最大ATR百分比 (波动过高)。
        "enable_dynamic_threshold": True,             # [布尔值] 是否启用动态信号阈值调整。
            "signal_dynamic_threshold_base": 0.7,    # [浮点数, 0-1] 🚀 修复：重命名避免与目标变量创建的dynamic_threshold_base冲突
            "dynamic_threshold_trend_adjust": 0.07,   # [浮点数] 根据趋势调整阈值的幅度。
            "dynamic_threshold_volatility_adjust": 0.03,# [浮点数] 根据波动率调整阈值的幅度。

        # --- OOF参数 (如果需要为DOWN模型独立配置OOF生成策略，否则这些应为全局参数) ---
        # 这些参数与全局的 META_MODEL_OOF_... 参数用途相同。
        # 如果所有基础模型的OOF生成策略一致，则这些是冗余的，应使用全局参数。
        # 如果希望DOWN模型在OOF生成时有特定配置，则这些参数有效。
        "meta_model_oof_cv_folds": 5,                   # [整数] 生成OOF预测时交叉验证的折数。
        "meta_model_oof_n_estimators_large": 3000,      # [整数] OOF每折LGBM树数量。
        "meta_model_oof_early_stopping_rounds": 100,    # [整数] OOF每折LGBM早停轮数。
        "meta_model_oof_early_stop_eval_ratio": 0.15,   # [浮点数] OOF早停验证集比例。
        "meta_model_oof_min_samples_for_eval": 50,      # [整数] OOF早停验证集最少样本。
    },



    # ❌ LSTM模型配置已关闭 - 不参与训练和预测
    # {
    #     # --- LSTM模型配置 ---
    #     "name": "BTC_15m_LSTM",                             # [字符串] LSTM模型的唯一名称
    #     "model_type": "LSTM",                               # [字符串] 模型类型标识，用于区分LGBM和LSTM
    #     "interval": "15m",                                  # [字符串] 时间周期
    #     "symbol": "BTCUSDT",                                # [字符串] 交易对
    #     "prediction_periods": [4],                          # [列表] 🎯 优化：预测未来4个周期，减少噪音
    #     "prediction_minutes_display": 60,                   # [整数] 🎯 更新：GUI显示时效为60分钟
    #     "model_save_dir": "trained_models_btc_15m_lstm",    # [字符串] LSTM模型保存目录
    #     "target_variable_type": "BOTH",                     # [字符串] 🎯 V17.0注意：三分类已废弃，如重新启用LSTM请使用二分类
    #     "drop_neutral_targets": False,                      # [布尔值] 保留中性样本用于三分类
    #     "device_type": 'cpu',                               # [字符串] 🚀 强制CPU训练：TensorFlow CPU优化（oneDNN + 16核并行）
    #     "prediction_trigger_type": "kline_close",           # [字符串] K线关闭时触发预测
    #
    #     # --- LSTM特有参数 ---
    #     "lstm_sequence_length": 60,                         # [整数] LSTM输入序列长度（时间步数）
    #     "lstm_hidden_units": 50,                            # [整数] LSTM隐藏层单元数
    #     "lstm_dropout_rate": 0.2,                           # [浮点数] Dropout比率防止过拟合
    #     "lstm_learning_rate": 0.0005,                        # [浮点数] 学习率
    #     "lstm_batch_size": 32,                              # [整数] 批次大小
    #     "lstm_epochs": 100,                                 # [整数] 训练轮数
    #     "lstm_early_stopping_patience": 10,                 # [整数] 早停耐心值
    #     "lstm_validation_split": 0.2,                       # [浮点数] 验证集比例
    #
    #     # --- 特征工程参数（继承基础配置但可能有所调整） ---
    #     "enable_price_change": True,
    #     "enable_volume": True,
    #     "enable_candle": True,
    #     "enable_ta": True,
    #     "enable_time": True,
    #     "enable_fund_flow": True,
    #     "enable_mtfa": True,                                # [布尔值] 🎯 启用MTFA特征，提供多时间框架信息
    #     "mtfa_timeframes": ['30m', '1h', '4h'],             # [列表] MTFA时间框架配置
    #     "enable_pattern_recognition": False,                # [布尔值] 简化特征集
    #     "enable_trend_slope": True,
    #     "enable_derivatives_features": True,
    #
    #     # --- 🎯 两阶段特征选择配置 ---
    #     "two_stage_feature_selection_enable": True,     # [布尔值] 是否启用两阶段特征选择（LightGBM重要性初筛 + RFE精选）
    #     "importance_prescreening_ratio": 0.5,           # [浮点数] 第一阶段重要性初筛保留的特征比例
    #
    #     # --- RFE (Recursive Feature Elimination) 配置 ---
    #     "rfe_enable": False,                            # [布尔值] 是否启用RFE递归特征消除
    #     "rfe_cv_folds": 3,                              # [整数] RFE内部交叉验证的折数
    #     "rfe_step": 1,                                  # [整数] RFE每次迭代移除特征的数量
    #     "rfe_scoring": "binary_profit_precision_composite",               # [字符串] RFE特征选择评分指标
    #     "rfe_min_features_to_select": 30,               # [整数] RFE最终选择的最小特征数量
    #
    #     # --- 🚀 RFE性能优化配置 ---
    #     "rfe_data_subsampling_enable": True,            # [布尔值] 是否启用数据子采样来加速RFE计算
    #     "rfe_data_subsampling_ratio": 0.4,              # [浮点数] 数据子采样比例，0.3表示使用30%的数据进行特征选择
    #
    #     # --- Optuna 超参数优化配置 ---
    #     "optuna_enable": True,                              # [布尔值] 是否启用Optuna进行超参数优化
    #     "optuna_n_trials": 100,                             # [整数] Optuna运行时尝试的试验次数
    #     "optuna_timeout": None,                             # [整数 或 None] Optuna运行的最大秒数。如果为None，则不限制时间
    #     "optuna_metric": "binary_profit_precision_composite", # [字符串] 🎯 统一盈利导向：复合指标平衡收益与质量
    #     "optuna_direction": "maximize",                     # [字符串] Optuna优化目标指标的方向: "maximize" (最大化) 或 "minimize" (最小化)
    #     "optuna_cv_folds": 3,                               # [整数] Optuna在评估每个试验参数集时，内部进行交叉验证的折数
    #
    #     # --- 其他参数 ---
    #     "scaler_type": "minmax",                            # [字符串] LSTM通常使用MinMax缩放
    #     "target_threshold": 0.002,                          # [浮点数] 目标阈值
    #     "signal_threshold": 0.6,                            # [浮点数] 信号阈值
    #     "labeling_method": "threshold",                      # [字符串] 🎯 新增：标签生成方法。"threshold"=基于阈值的传统方法，"pure_direction"=纯方向标签法
    #
    #     # --- 🎯 动态波动率阈值配置 (革新目标变量定义) ---
    #     "enable_dynamic_thresholds": True,              # [布尔值] 是否启用基于ATR的动态波动率阈值，替代固定的target_threshold
    #     "dynamic_threshold_base": "ATRr_14",            # [字符串] 用作动态阈值基准的波动率指标列名 (ATR百分比)
    #     "dynamic_threshold_multipliers": [1.5],         # [列表] ATR倍数，用于计算动态阈值 (1.5倍ATR作为阈值)
    #     "dynamic_threshold_min": 0.001,                 # [浮点数] 动态阈值的最小值 (0.1%)，防止在极低波动时阈值过小
    #     "dynamic_threshold_max": 0.02,                  # [浮点数] 动态阈值的最大值 (2.0%)，防止在极高波动时阈值过大
    #     "dynamic_threshold_smoothing": 3,               # [整数] 动态阈值的平滑周期，0表示不平滑
    #
    #     # --- 🚀 三道屏障标签法配置 (Triple-Barrier Method) ---
    #     "enable_triple_barrier": True,                 # [布尔值] 是否启用三道屏障标签法，考虑交易路径信息
    #     "triple_barrier_profit_multiplier": 3.0,        # [浮点数] 🎯 优化：止盈屏障的ATR倍数，用于计算上屏障 (盈亏比 3.0:1.0)
    #     "triple_barrier_loss_multiplier": 1.0,          # [浮点数] 止损屏障的ATR倍数，用于计算下屏障
    #     "triple_barrier_use_fixed": True,              # [布尔值] 是否使用固定百分比而非ATR计算屏障
    #     "triple_barrier_fixed_profit": 0.02,            # [浮点数] 固定止盈阈值 (2%)，仅当use_fixed=True时使用
    #     "triple_barrier_fixed_loss": 0.015,             # [浮点数] 固定止损阈值 (1.5%)，仅当use_fixed=True时使用
    #     "triple_barrier_min_profit": 0.008,             # [浮点数] 🎯 优化：最小止盈阈值 (0.8%)，提升信号质量
    #     "triple_barrier_max_profit": 0.05,              # [浮点数] 最大止盈阈值 (5%)，防止屏障过大
    #     "triple_barrier_min_loss": 0.003,               # [浮点数] 最小止损阈值 (0.3%)，防止屏障过小
    #     "triple_barrier_max_loss": 0.03,                # [浮点数] 最大止损阈值 (3%)，防止屏障过大
    #     "data_fetch_limit": 5000,                           # [整数] 数据获取限制
    #     "train_ratio": 0.7,                                 # [浮点数] 训练集比例
    #     "validation_ratio": 0.15,                           # [浮点数] 验证集比例
    # },


    { # 新增：元模型的GUI显示条目 和 APScheduler 触发配置
        "name": META_MODEL_GUI_DISPLAY_NAME,          # [字符串] 在GUI中标识元模型预测结果的名称，应与全局定义一致。
        "symbol": "BTCUSDT",                          # [字符串] 元模型关联的主要交易对
        "interval": "Meta-Analysis",                  # [字符串] GUI中显示的“周期”，表明这是综合分析。
        "prediction_periods": [2],                    # [列表] 🎯 统一：元模型使用2个周期(30分钟)，与基础模型保持一致
        "prediction_minutes_display": "实时决策",     # [字符串] GUI中显示的预测时效。
        "model_save_dir": "meta_model_display",       # [字符串] 元模型显示条目的目录（虽然不实际保存模型）
        "target_variable_type": "META_MODEL_DISPLAY", # [字符串] 特殊类型，标记此条目仅用于在GUI中显示元模型结果，不参与独立训练。
        "prediction_trigger_type": "apscheduler_driven", # [字符串] 表明此“目标”的“预测”（即元模型决策的更新）主要由APScheduler任务驱动。

        # --- 🎯 APScheduler 作业配置 (动态读取用户配置) ---
        "apscheduler_job_enabled": AUTO_PREDICTION_ENABLED,     # [布尔值] 从全局配置读取是否启用自动预测
        "apscheduler_trigger_type": AUTO_PREDICTION_TRIGGER_TYPE, # [字符串] 从全局配置读取触发类型

        # 🎯 动态配置：根据用户设置生成APScheduler配置
        # 如果用户选择interval模式，使用interval配置
        # 如果用户选择cron模式，使用cron配置
        "apscheduler_interval_config": {"minutes": AUTO_PREDICTION_INTERVAL_MINUTES} if AUTO_PREDICTION_TRIGGER_TYPE == "interval" and AUTO_PREDICTION_INTERVAL_MINUTES > 0 else None,
        "apscheduler_cron_config": {"minute": f"*/{AUTO_PREDICTION_INTERVAL_MINUTES}"} if AUTO_PREDICTION_TRIGGER_TYPE == "cron" and AUTO_PREDICTION_INTERVAL_MINUTES > 0 else None
    }
]

# --- 默认配置字典 ---
# 包含所有可能参数的默认配置，确保即使目标配置中遗漏了某个参数，也能回退到合理的默认值
DEFAULT_TARGET_CONFIG = {
    # 基本信息
    'name': 'default_target',  # 添加缺少的 name 字段
    'symbol': SYMBOL,
    'scaler_type': SCALER_TYPE,
    'device_type': 'cpu',
    'interval': '15m',
    'prediction_periods': [2],  # 🎯 统一：默认使用2个周期(30分钟)
    'prediction_minutes_display': 30,  # 🎯 统一：默认显示30分钟
    'model_save_dir': 'trained_models_default',
    'target_variable_type': 'UP_ONLY',
    'drop_neutral_targets': True,                    # 🎯 二元期权：移除中性样本，实现二分类
    'prediction_trigger_type': 'kline_close',

    # 特征工程开关
    'enable_price_change': True,
    'enable_volume': True,
    'enable_candle': True,
    'enable_ta': True,
    'enable_time': True,
    'enable_fund_flow': True,
    'enable_mtfa': True,
    'enable_pattern_recognition': False,
    'enable_trend_slope': True,

    # 特征工程参数
    'volume_avg_period': 20,
    'atr_period': 14,
    'rsi_period': 14,
    'willr_period': 14,                    # [整数] Williams %R 周期参数
    'cci_period': 14,                      # [整数] CCI 周期参数 (独立于RSI)
    'macd_fast': 12,
    'macd_slow': 26,
    'macd_sign': 9,
    'stoch_k': 14,
    'stoch_d': 3,
    'stoch_smooth_k': 3,
    'fund_flow_ratio_smoothing_period': 5,     # [整数] 资金流比率平滑周期 (统一为5以匹配期望特征名)
    'target_threshold': 0.001,
    'hma_period': 21,
    'kc_period': 20,
    'kc_atr_period': 10,
    'kc_multiplier': 2.0,
    'signal_threshold': 0.5,
    'labeling_method': 'threshold',  # 🎯 新增：默认使用传统阈值方法
    'trend_adx_period': 14,
    'trend_adx_threshold': 25,
    'trend_adx_strength_threshold': 30,  # 添加缺少的字段
    'trend_ema_short_period': 20,
    'trend_ema_long_period': 50,
    'trend_slope_period_1': 5,
    'trend_slope_period_2': 10,
    'doji_threshold': 0.1,
    'hammer_body_ratio': 0.3,
    'hammer_shadow_ratio': 2.0,
    'marubozu_threshold': 0.9,
    'spinning_top_threshold': 0.5,
    'engulfing_body_multiplier': 1.2,
    'morning_evening_star_body_ratio': 0.3,
    'doji_threshold_batac': 0.1,

    # 高级配置
    'price_change_periods': [1, 2, 3, 5, 10],
    'cci_constant': 0.015,
    'enable_time_trigonometric': True,
    'enable_adx_trend_features': True,
    'enable_ema_trend_features': True,
    'enable_ta_derived_features': True,
    'mtfa_timeframes': ['30m', '1h', '4h'],
    'mtfa_feature_lookback_periods': 200,
    'mtfa_specific_lookbacks': {},
    'mtfa_min_bars_to_fetch': 50,
    'mtfa_min_bars_for_calc': 50,
    'mtfa_fetch_buffer': 10,

    # 数据处理配置
    'enable_intelligent_nan_processing': True,
    'enable_safe_fill_nans': True,
    'min_historical_bars_for_prediction': 100,
    'enable_advanced_feature_validation': True,

    # RFE配置
    'rfe_enable': False,
    'rfe_cv_folds': 3,
    'rfe_step': 1,
    'rfe_scoring': 'binary_profit_precision_composite',  # 🎯 统一盈利导向
    'rfe_estimator_n_estimators': 100,
    'rfe_estimator_learning_rate': 0.1,
    'rfe_estimator_num_leaves': 31,
    'rfe_min_features_to_select': 10,

    # 特征选择配置
    'importance_thresholding_enable': False,
    'importance_threshold_value': 20,
    'importance_top_n_features': 50,
    'importance_model_n_estimators': 200,
    'learning_rate_initial_imp': 0.05,
    'num_leaves_initial_imp': 15,
    'max_depth_initial_imp': 5,
    'reg_alpha_initial_imp': 5.0,
    'reg_lambda_initial_imp': 5.0,
    'colsample_bytree_initial_imp': 0.7,
    'subsample_initial_imp': 0.7,
    'min_child_samples_initial_imp': 30,
    'metric_initial_imp': 'binary_logloss',

    # 🎯 V8.0专家委员会：重新启用自动化工具！
    'optuna_enable': True,  # ✅ 重新启用Optuna超参数优化
    'optuna_n_trials': 100,
    'optuna_timeout': None,
    'optuna_metric': 'precision_positive_focused',  # 🎯 盈利导向：复合指标平衡盈利能力和精确率
    'optuna_direction': 'maximize',
    'optuna_cv_folds': 3,
    'optuna_trial_n_estimators_max': 1500,
    'optuna_trial_early_stopping_rounds': 50,
    'optuna_trial_eval_metric': 'binary_logloss',

    # Purged K-Fold交叉验证配置
    'purged_cv_enable': True,                      # [布尔值] 是否启用Purged K-Fold交叉验证
    'purged_cv_purge_length': 10,                   # [整数] 清洗期长度（样本数量）
    'purged_cv_embargo_length': 0,                  # [整数] 禁运期长度（可选，进一步防止泄露）
    'purged_cv_min_train_size': None,               # [整数或None] 最小训练集大小，None为自动计算
    'purged_cv_auto_purge_calculation': True,       # [布尔值] 是否自动计算purge长度

    # LightGBM参数
    'objective': 'binary',
    'metric': 'binary_logloss',
    'class_weight': None,
    'boosting_type': 'gbdt',
    'random_state': 42,
    'n_estimators': 1000,
    'early_stopping_rounds': 100,
    'verbose': -1,
    'verbosity': -1,
    'ensemble_runs': 1,
    'ensemble_cv_folds': 3,
    'learning_rate': 0.1,
    'num_leaves': 31,
    'max_depth': -1,
    'reg_alpha': 0.0,
    'reg_lambda': 0.0,
    'colsample_bytree': 1.0,
    'subsample': 1.0,
    'min_child_samples': 20,
    'subsample_freq': 0,

    # SMOTE配置
    'smote_enable': True,
    'smote_k_neighbors': 3,
    'smote_min_samples_threshold': 5,
    'smote_random_state': 42,

    # 阈值优化配置
    'threshold_optimization_enable': False,
    'threshold_optimization_method': 'precision_constrained_recall',
    'threshold_save_to_metadata': True,
    'threshold_default_value': 0.5,
    'threshold_use_independent_validation': True,
    'threshold_independent_val_ratio': 0.15,
    'threshold_min_precision': 0.65,
    'threshold_precision_constraint_fallback': True,

    # 集成模型配置
    'ensemble_prediction_threshold': 0.5,
    'ensemble_prediction_threshold_override': False,

    # 概率校准配置
    'enable_probability_calibration': False,
    'calibration_brier_improvement_threshold': 0.001,

    # SHAP分析配置 - 🏆 只保留精英模型深度分析
    'enable_shap_analysis_per_fold': False,         # 🚫 已禁用：不再需要单折SHAP分析
    'enable_shap_analysis_ensemble': False,         # 🚫 已禁用：不再需要集成模型SHAP分析
    'enable_shap_analysis_elite': True,             # 🏆 元模型精英分析：唯一保留的SHAP分析方式
    'elite_selection_metric': 'test_f1_score',      # 元模型精英选择标准
    'elite_model_count': 3,                         # 元模型精英数量
    'enable_shap_for_live_prediction': False,

    # 信号过滤配置
    'enable_trend_detection': False,
    'trend_detection_timeframe': '30m',
    'trend_indicator_type': 'adx',
    'trend_filter_strategy': 'filter_only',
    'trend_chase_confidence_boost': 0.05,
    'enable_volatility_filter': False,
    'volatility_filter_timeframe': '30m',
    'volatility_atr_period': 14,
    'volatility_min_atr_percent': 0.1,
    'volatility_max_atr_percent': 1.2,
    'enable_dynamic_threshold': False,
    'signal_dynamic_threshold_base': 0.5,  # 🚀 修复：重命名避免与目标变量创建的dynamic_threshold_base冲突
    'dynamic_threshold_trend_adjust': 0.05,
    'dynamic_threshold_volatility_adjust': 0.03,

    # 动态波动率阈值配置 (用于目标变量创建)
    'enable_dynamic_thresholds': False,
    'dynamic_threshold_base': 'ATRr_14',
    'dynamic_threshold_multipliers': [1.5],
    'dynamic_threshold_min': 0.001,
    'dynamic_threshold_max': 0.02,
    'dynamic_threshold_smoothing': 3,

    # 三道屏障标签法配置
    'enable_triple_barrier': True,
    'triple_barrier_profit_multiplier': 1.8,
    'triple_barrier_loss_multiplier': 1.0,
    'triple_barrier_use_fixed': True,
    'triple_barrier_fixed_profit': 0.02,
    'triple_barrier_fixed_loss': 0.015,
    'triple_barrier_min_profit': 0.005,
    'triple_barrier_max_profit': 0.05,
    'triple_barrier_min_loss': 0.003,
    'triple_barrier_max_loss': 0.03,

    # OOF参数
    'meta_model_oof_cv_folds': 5,
    'meta_model_oof_n_estimators_large': 3000,
    'meta_model_oof_early_stopping_rounds': 100,
    'meta_model_oof_early_stop_eval_ratio': 0.15,
    'meta_model_oof_min_samples_for_eval': 50,
}


# --- 类型安全的辅助函数：获取目标配置 ---
@overload
def get_target_config(target_name: str, *, validate_types: Literal[True]) -> TargetConfigDict:
    """类型安全版本，返回完整类型化的配置字典"""
    ...

@overload
def get_target_config(target_name: str, *, validate_types: Literal[False] = False) -> ConfigDict:
    """标准版本，返回通用配置字典"""
    ...

def get_target_config(
    target_name: str,
    *,
    validate_types: bool = False
) -> Union[TargetConfigDict, ConfigDict]:
    """
    根据目标名称从 PREDICTION_TARGETS 列表中获取完整的配置字典。
    支持配置模板继承、全局开关覆盖、细化错误处理，保持原始键名。

    Args:
        target_name: 要获取配置的目标的名称。必须是非空字符串。
        validate_types: 是否进行严格的类型验证。如果为True，返回类型化的配置字典。

    Returns:
        包含该目标所有配置项的字典。根据validate_types参数返回不同类型：
        - validate_types=True: 返回TargetConfigDict（类型安全）
        - validate_types=False: 返回ConfigDict（通用字典）

    Raises:
        ValueError: 如果在 PREDICTION_TARGETS 中找不到指定名称的目标配置，或目标名称为空。
        TypeError: 如果目标名称不是字符串类型。

    Example:
        >>> config = get_target_config("BTC_15m_UP")
        >>> typed_config = get_target_config("BTC_15m_UP", validate_types=True)
    """
    # 参数类型检查
    if not isinstance(target_name, str):
        raise TypeError(f"目标名称必须是字符串类型，当前类型: {type(target_name)}")

    if not target_name.strip():
        raise ValueError("目标名称不能为空")

    # 从 PREDICTION_TARGETS 列表中查找具有匹配名称的目标配置
    target_setting_from_list = next((t for t in PREDICTION_TARGETS if t.get('name') == target_name), None)

    if not target_setting_from_list:
        available_targets = [t.get('name', 'Unknown') for t in PREDICTION_TARGETS if isinstance(t, dict)]
        raise ValueError(f"在 PREDICTION_TARGETS 中未找到名为 '{target_name}' 的配置。"
                        f"可用的目标配置: {available_targets}")

    # 1. 构建配置层次结构：默认配置 -> 全局默认值 -> 模板配置 -> 目标特定配置
    final_config = _build_hierarchical_config(target_setting_from_list, target_name)

    # 2. 应用全局硬性开关覆盖
    _apply_global_hard_switches(final_config, target_name)

    # 3. 验证和格式化配置参数
    _validate_and_format_config(final_config, target_name)

    # 4. 处理时间间隔转换
    _process_interval_timedelta(final_config, target_name)

    # 5. 运行时类型验证（如果需要）
    if validate_types:
        _validate_runtime_types(final_config, target_name)

    return final_config


# --- 类型安全的配置包装器获取函数 ---
def get_target_config_wrapper(target_name: str, use_cache: bool = True) -> 'TargetConfigWrapper':
    """
    获取目标配置的类型安全包装器

    这是推荐的配置访问方式，提供类型安全的配置获取方法，
    逐步替代直接的字典访问 config[key]。

    Args:
        target_name: 目标名称
        use_cache: 是否使用缓存

    Returns:
        TargetConfigWrapper: 类型安全的配置包装器

    Raises:
        ValueError: 当目标配置未找到时
        ConfigurationError: 当配置有问题时

    Example:
        >>> # 推荐的类型安全访问方式
        >>> wrapper = get_target_config_wrapper("BTC_15m_UP")
        >>> name = wrapper.get_str('name', required=True)
        >>> threshold = wrapper.get_float('target_threshold', default=0.001)
        >>> periods = wrapper.get_list('prediction_periods', default=[1])
        >>> enable_ta = wrapper.get_bool('enable_ta', default=True)
        >>>
        >>> # 替代原来的直接字典访问
        >>> # config = get_target_config("BTC_15m_UP")
        >>> # name = config['name']  # ❌ 不推荐
        >>> # threshold = config.get('target_threshold', 0.001)  # ❌ 不推荐
    """
    try:
        # 尝试使用强化配置管理器
        from src.core.config_validator import RobustConfigManager
        robust_manager = RobustConfigManager(sys.modules[__name__])
        return robust_manager.get_target_config(target_name, use_cache=use_cache)
    except Exception as e:
        # 回退到基础实现
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"强化配置管理器不可用，回退到基础实现: {e}")

        # 获取基础配置字典
        config_dict = get_target_config(target_name, validate_types=True)

        # 创建简单的包装器
        from src.core.config_validator import TargetConfigWrapper
        return TargetConfigWrapper(config_dict, target_name)


def safe_get_target_config(target_name: str, fallback_to_dict: bool = True) -> Union['TargetConfigWrapper', ConfigDict]:
    """
    安全获取目标配置，支持回退机制

    Args:
        target_name: 目标名称
        fallback_to_dict: 当包装器创建失败时是否回退到字典

    Returns:
        配置包装器或配置字典
    """
    try:
        return get_target_config_wrapper(target_name)
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"获取配置包装器失败: {e}")

        if fallback_to_dict:
            logger.info("回退到字典模式")
            return get_target_config(target_name)
        else:
            raise


def _build_hierarchical_config(target_config: ConfigDict, target_name: str) -> ConfigDict:
    """
    构建层次化配置：默认配置 -> 全局默认值 -> 模板配置 -> 目标特定配置

    Args:
        target_config: 目标特定配置字典
        target_name: 目标名称，用于错误信息和模板选择

    Returns:
        合并后的配置字典

    Raises:
        TypeError: 如果输入参数类型不正确
    """
    # 运行时类型检查
    if not isinstance(target_config, dict):
        raise TypeError(f"target_config 必须是字典类型，当前类型: {type(target_config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 1. 从基础默认配置开始
    final_config = DEFAULT_TARGET_CONFIG.copy()

    # 2. 应用全局默认值（暂时跳过，避免循环引用）
    # for key, value in GLOBAL_DEFAULTS.items():
    #     final_config[key] = value

    # 3. 检查是否需要应用模板配置
    template_name = target_config.get('template', None)
    if template_name == 'base_model' or _is_base_model_target(target_name):
        # 应用基础模型模板
        for key, value in BASE_MODEL_TEMPLATE.items():
            final_config[key] = value

    # 4. 应用目标特定配置（具有最高优先级）
    for key, value in target_config.items():
        final_config[key] = value

    return final_config


def _is_base_model_target(target_name: str) -> bool:
    """
    判断是否为基础模型目标（自动应用基础模型模板）

    Args:
        target_name: 目标名称，必须是非空字符串

    Returns:
        是否为基础模型目标

    Raises:
        TypeError: 如果target_name不是字符串类型
    """
    # 运行时类型检查
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 基础模型通常包含这些模式
    base_model_patterns = ['_UP', '_DOWN', 'BTC_', 'ETH_', 'USDT']
    meta_model_patterns = ['Meta', 'META', 'Signal']

    # 如果包含元模型模式，则不是基础模型
    if any(pattern in target_name for pattern in meta_model_patterns):
        return False

    # 如果包含基础模型模式，则是基础模型
    return any(pattern in target_name for pattern in base_model_patterns)


def _apply_global_hard_switches(config: ConfigDict, target_name: str) -> None:
    """
    应用全局硬性开关，这些开关会覆盖目标配置中的对应设置

    Args:
        config: 配置字典，将被就地修改
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果输入参数类型不正确
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 映射全局开关到目标配置键
    switch_mappings = {
        'smote_global_enable': 'smote_enable',
        'threshold_optimization_global_enable': 'threshold_optimization_enable',
        # 元模型相关开关只影响元模型目标
        'meta_model_training_enable': None,  # 这个在训练逻辑中处理
        'meta_model_prediction_enable': None,  # 这个在预测逻辑中处理
    }

    for global_switch, target_key in switch_mappings.items():
        if target_key and global_switch in GLOBAL_HARD_SWITCHES:
            global_value = GLOBAL_HARD_SWITCHES[global_switch]
            if not global_value:  # 如果全局开关为False，强制覆盖目标配置
                config[target_key] = False
                if config.get(target_key, True):  # 只在原来为True时打印警告
                    print(f"警告: 目标 '{target_name}' 的 {target_key} 被全局开关 {global_switch} 强制设置为 False")





def _validate_and_format_config(config: ConfigDict, target_name: str) -> None:
    """
    验证和格式化配置参数。

    Args:
        config: 配置字典，将被就地修改
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果输入参数类型不正确
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 设备类型验证和格式化 - 强制使用CPU
    if 'device_type' in config:
        device_type = str(config['device_type']).lower()
        if device_type == 'gpu':
            print(f"信息: 目标 '{target_name}' 配置了GPU，但已强制改为CPU（数据量小，CPU更快）")
            device_type = 'cpu'
        elif device_type not in ['cpu', 'gpu']:
            print(f"警告: 目标 '{target_name}' 的 device_type '{config['device_type']}' 无效，"
                  f"将使用默认值 'cpu'")
            device_type = 'cpu'
        config['device_type'] = 'cpu'  # 强制设置为CPU

    # 数值参数验证
    numeric_params = {
        'target_threshold': (0.0, 1.0),
        'signal_threshold': (0.0, 1.0),
        'learning_rate': (0.001, 1.0),
        'train_ratio': (0.1, 0.9),
        'validation_ratio': (0.05, 0.5),
    }

    for param, (min_val, max_val) in numeric_params.items():
        if param in config:
            try:
                value = float(config[param])
                if not (min_val <= value <= max_val):
                    print(f"警告: 目标 '{target_name}' 的 {param} 值 {value} 超出合理范围 "
                          f"[{min_val}, {max_val}]，将使用默认值")
                    # 使用默认值替换超出范围的值
                    config[param] = DEFAULT_TARGET_CONFIG.get(param, 0.5)
                else:
                    config[param] = value
            except (ValueError, TypeError):
                print(f"警告: 目标 '{target_name}' 的 {param} 值 '{config[param]}' 无法转换为数值，"
                      f"将使用默认值")
                config[param] = DEFAULT_TARGET_CONFIG.get(param, 0.5)


def _process_interval_timedelta(config: ConfigDict, target_name: str) -> None:
    """
    处理时间间隔字符串转换为 pandas.Timedelta 对象。

    Args:
        config: 配置字典，将被就地修改
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果输入参数类型不正确
        ValueError: 如果时间间隔配置无效
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    interval_str = config.get('interval')
    target_type = config.get('target_variable_type')

    # 如果是元模型的GUI显示条目，或者 'interval' 被明确设置为不适用
    if target_type == "META_MODEL_DISPLAY" or interval_str in ["N/A", "Meta-Analysis", None]:
        config['interval_timedelta'] = None
        return

    # 尝试转换时间间隔字符串
    if not interval_str:
        raise ValueError(f"目标 '{target_name}' 配置中缺少 'interval' 键或其值为空")

    try:
        # 验证时间间隔格式
        if not isinstance(interval_str, str):
            raise TypeError(f"interval 必须是字符串类型，当前类型: {type(interval_str)}")

        # 尝试转换
        config['interval_timedelta'] = pd.Timedelta(interval_str)

        # 验证转换结果的合理性
        timedelta_obj = config['interval_timedelta']
        if timedelta_obj <= pd.Timedelta(0):
            raise ValueError("时间间隔必须为正值")

        # 检查是否为常见的时间间隔
        total_seconds = timedelta_obj.total_seconds()
        common_intervals = [60, 300, 900, 1800, 3600, 14400, 86400]  # 1m, 5m, 15m, 30m, 1h, 4h, 1d
        if total_seconds not in common_intervals:
            print(f"警告: 目标 '{target_name}' 的时间间隔 '{interval_str}' 不是常见的交易时间框架")

    except (ValueError, TypeError) as e:
        # 具体的异常处理
        print(f"错误: 解析目标 '{target_name}' 的 interval 字符串 '{interval_str}' 失败: {e}")
        print(f"将使用默认的15分钟作为 interval_timedelta")
        config['interval_timedelta'] = pd.Timedelta(minutes=15)
    except Exception as e:
        # 其他未预期的异常
        print(f"未预期的错误: 处理目标 '{target_name}' 的时间间隔时发生异常: {e}")
        print(f"将使用默认的15分钟作为 interval_timedelta")
        config['interval_timedelta'] = pd.Timedelta(minutes=15)


def _validate_runtime_types(config: ConfigDict, target_name: str) -> None:
    """
    运行时类型验证，确保配置字典符合TargetConfig类型定义

    Args:
        config: 配置字典
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果配置项类型不匹配
        ValueError: 如果配置项值无效
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 定义类型验证规则
    type_validators = {
        # 基本信息类型验证
        'name': (str, "名称必须是字符串"),
        'symbol': (str, "交易对必须是字符串"),
        'device_type': (str, "设备类型必须是字符串"),
        'interval': (str, "时间间隔必须是字符串"),
        'prediction_periods': (list, "预测周期必须是列表"),
        'target_variable_type': (str, "目标变量类型必须是字符串"),
        'drop_neutral_targets': (bool, "drop_neutral_targets必须是布尔值"),
        'prediction_trigger_type': (str, "触发类型必须是字符串"),

        # 数值类型验证
        'target_threshold': ((int, float), "目标阈值必须是数值"),
        'signal_threshold': ((int, float), "信号阈值必须是数值"),
        'data_fetch_limit': (int, "数据获取限制必须是整数"),
        'train_ratio': ((int, float), "训练比例必须是数值"),
        'validation_ratio': ((int, float), "验证比例必须是数值"),

        # 布尔类型验证
        'enable_price_change': (bool, "价格变化特征开关必须是布尔值"),
        'enable_volume': (bool, "成交量特征开关必须是布尔值"),
        'enable_candle': (bool, "K线特征开关必须是布尔值"),
        'enable_ta': (bool, "技术指标特征开关必须是布尔值"),
        'enable_time': (bool, "时间特征开关必须是布尔值"),
        'enable_fund_flow': (bool, "资金流特征开关必须是布尔值"),
        'enable_mtfa': (bool, "多时间框架特征开关必须是布尔值"),
        'enable_pattern_recognition': (bool, "模式识别特征开关必须是布尔值"),
        'enable_trend_slope': (bool, "趋势斜率特征开关必须是布尔值"),

        # LightGBM参数类型验证
        'objective': (str, "目标函数必须是字符串"),
        'metric': (str, "评估指标必须是字符串"),
        'boosting_type': (str, "提升类型必须是字符串"),
        'random_state': (int, "随机种子必须是整数"),
        'n_estimators': (int, "估计器数量必须是整数"),
        'early_stopping_rounds': (int, "早停轮数必须是整数"),
        'learning_rate': ((int, float), "学习率必须是数值"),
        'num_leaves': (int, "叶子数量必须是整数"),
        'max_depth': (int, "最大深度必须是整数"),
        'reg_alpha': ((int, float), "L1正则化必须是数值"),
        'reg_lambda': ((int, float), "L2正则化必须是数值"),
        'colsample_bytree': ((int, float), "列采样比例必须是数值"),
        'subsample': ((int, float), "行采样比例必须是数值"),
        'min_child_samples': (int, "最小子样本数必须是整数"),

        # SMOTE参数类型验证
        'smote_enable': (bool, "SMOTE开关必须是布尔值"),
        'smote_k_neighbors': (int, "SMOTE邻居数必须是整数"),
        'smote_min_samples_threshold': (int, "SMOTE最小样本阈值必须是整数"),
        'smote_random_state': (int, "SMOTE随机种子必须是整数"),

        # 阈值优化参数类型验证
        'threshold_optimization_enable': (bool, "阈值优化开关必须是布尔值"),
        'threshold_optimization_method': (str, "阈值优化方法必须是字符串"),
        'threshold_save_to_metadata': (bool, "阈值保存开关必须是布尔值"),
        'threshold_default_value': ((int, float), "默认阈值必须是数值"),
        'threshold_use_independent_validation': (bool, "独立验证开关必须是布尔值"),
        'threshold_independent_val_ratio': ((int, float), "独立验证比例必须是数值"),
    }

    # 执行类型验证
    for key, value in config.items():
        if key in type_validators:
            expected_type, error_msg = type_validators[key]
            if not isinstance(value, expected_type):
                raise TypeError(f"目标 '{target_name}' 的配置项 '{key}': {error_msg}。"
                              f"期望类型: {expected_type}，实际类型: {type(value)}，实际值: {value}")

    # 特殊值验证
    _validate_special_values(config, target_name)


def _validate_special_values(config: ConfigDict, target_name: str) -> None:
    """
    验证特殊值的有效性（如枚举值、范围等）

    Args:
        config: 配置字典
        target_name: 目标名称，用于错误信息

    Raises:
        ValueError: 如果配置值无效
    """
    # 设备类型验证
    if 'device_type' in config:
        valid_devices = [d.value for d in DeviceType]
        if config['device_type'] not in valid_devices:
            raise ValueError(f"目标 '{target_name}' 的 device_type '{config['device_type']}' 无效。"
                           f"有效值: {valid_devices}")

    # 目标变量类型验证
    if 'target_variable_type' in config:
        valid_types = [t.value for t in TargetType]
        if config['target_variable_type'] not in valid_types:
            raise ValueError(f"目标 '{target_name}' 的 target_variable_type '{config['target_variable_type']}' 无效。"
                           f"有效值: {valid_types}")

    # 触发类型验证
    if 'prediction_trigger_type' in config:
        valid_triggers = [t.value for t in TriggerType]
        if config['prediction_trigger_type'] not in valid_triggers:
            raise ValueError(f"目标 '{target_name}' 的 prediction_trigger_type '{config['prediction_trigger_type']}' 无效。"
                           f"有效值: {valid_triggers}")

    # 缩放器类型验证
    if 'scaler_type' in config:
        valid_scalers = ['minmax', 'standard']
        if config['scaler_type'] not in valid_scalers:
            raise ValueError(f"目标 '{target_name}' 的 scaler_type '{config['scaler_type']}' 无效。"
                           f"有效值: {valid_scalers}")

    # 数值范围验证
    numeric_ranges = {
        'target_threshold': (0.0, 1.0),
        'signal_threshold': (0.0, 1.0),
        'train_ratio': (0.1, 0.9),
        'validation_ratio': (0.05, 0.5),
        'learning_rate': (0.001, 1.0),
        'colsample_bytree': (0.1, 1.0),
        'subsample': (0.1, 1.0),
        'threshold_default_value': (0.0, 1.0),
        'threshold_independent_val_ratio': (0.05, 0.5),
    }

    for param, (min_val, max_val) in numeric_ranges.items():
        if param in config:
            value = config[param]
            if isinstance(value, (int, float)) and not (min_val <= value <= max_val):
                raise ValueError(f"目标 '{target_name}' 的 {param} 值 {value} 超出有效范围 [{min_val}, {max_val}]")

    # 列表类型内容验证
    if 'prediction_periods' in config:
        periods = config['prediction_periods']
        if not all(isinstance(p, int) and p >= 0 for p in periods):  # 允许0值，用于元模型显示
            raise ValueError(f"目标 '{target_name}' 的 prediction_periods 必须是非负整数列表，当前值: {periods}")

    # 字典类型验证
    if 'class_weight' in config and config['class_weight'] is not None:
        class_weight = config['class_weight']
        if not isinstance(class_weight, dict):
            raise ValueError(f"目标 '{target_name}' 的 class_weight 必须是字典或None，当前类型: {type(class_weight)}")
        if not all(isinstance(k, int) and isinstance(v, (int, float)) for k, v in class_weight.items()):
            raise ValueError(f"目标 '{target_name}' 的 class_weight 字典的键必须是整数，值必须是数值")


# --- 用于快速访问所有已定义的目标名称的列表 ---
ALL_TARGET_NAMES = [t['name'] for t in PREDICTION_TARGETS if isinstance(t, dict) and 'name' in t]


# ===============================================================
# 🚀 全局默认配置 - 外部化硬编码参数
# ===============================================================

GLOBAL_DEFAULTS = {
    # === 数据获取配置 ===
    'data_processing': {
        # API请求配置
        'kline_limit_per_request': 1000,           # 每次请求的K线数量限制
        'max_api_retries': 7,                      # API请求最大重试次数
        'api_retry_delay': 7,                      # API重试延迟（秒）
        'api_timeout': 30,                         # API超时时间（秒）
        'data_fetch_buffer': 10,                   # 数据获取缓冲区大小

        # 数据处理配置
        'price_columns': ['open', 'high', 'low', 'close'],  # 价格列名
        'volume_columns': ['volume', 'qav', 'n', 'tbbav', 'tbqav'],  # 成交量列名
        'default_price_value': 0,                  # 价格默认值
        'default_volume_value': 0,                 # 成交量默认值
        'nan_fill_method': 'historical_only',      # NaN填充方法
        'duplicate_handling': 'first',             # 重复数据处理方式

        # 数据验证配置
        'min_data_points': 50,                     # 最小数据点数
        'max_outlier_ratio': 0.1,                 # 最大异常值比例
        'variance_threshold': 1e-10,               # 方差阈值（用于检测常数特征）
    },

    # === 特征工程配置 ===
    'feature_engineering': {
        # 窗口和周期配置
        'default_window_size': 20,                 # 默认窗口大小
        'certainty_window': 10,                    # 市场确定性窗口
        'price_range_lookback_periods': 20,       # 价格区间回看周期
        'trend_consistency_periods': 10,          # 趋势一致性周期
        'moving_average_windows': [5, 10, 20, 50], # 移动平均窗口

        # 特征质量配置
        'feature_quality_threshold': 0.01,        # 特征质量阈值
        'non_zero_ratio_threshold': 0.01,         # 非零值比例阈值
        'outlier_ratio_threshold': 0.1,           # 异常值比例阈值
        'min_feature_variance': 1e-10,            # 最小特征方差
        'max_features_to_log': 10,                # 最大日志记录特征数

        # 特征名模式
        'interaction_patterns': ['_x_', '_when_', '_in_'],  # 交互特征模式
        'state_patterns': ['_entering_', '_exiting_'],      # 状态转换模式
        'combination_patterns': ['_ratio_', '_strength_'],  # 组合特征模式

        # 特征计算配置
        'enable_feature_quality_evaluation': True, # 启用特征质量评估
        'enable_outlier_clipping': True,          # 启用异常值裁剪
        'feature_selection_top_k': 10,            # 特征选择前K个
    },

    # === 市场状态分析配置 ===
    'market_analysis': {
        # 市场状态阈值
        'panic_threshold': -3.0,                  # 恐慌性抛售阈值
        'bubble_threshold': 3.0,                  # 泡沫状态阈值
        'extreme_volatility_multiplier': 3.0,     # 极端波动率倍数
        'liquidity_drought_volume_ratio': 0.5,    # 流动性枯竭成交量比例

        # 市场状态名称
        'market_states': [
            'high_vol_sideways', 'low_vol_sideways', 'strong_uptrend',
            'strong_downtrend', 'normal_trend', 'high_certainty', 'low_certainty',
            'extreme_volatility', 'liquidity_drought', 'panic_selling', 'bubble_state'
        ],

        # 交互特征配置
        'market_state_interaction_indicators': [
            'rsi', 'macd', 'macd_histogram', 'williams_r',
            'bb_position', 'ATRr_14', 'bb_width',
            'volume_ratio', 'volume_vs_avg', 'volume_momentum',
            'close_pos_in_candle', 'price_change_1p',
            'ADX', 'ema_diff_pct', 'hma_slope',
            'rsi_velocity', 'macd_hist_accel', 'volume_change_accel'
        ],

        # 权重配置
        'danger_score_weights': {
            'high_volatility': 0.4,
            'low_trend_strength': 0.3,
            'price_whipsaw': 0.2,
            'volume_inconsistency': 0.1
        },

        # 🎯 强化样本权重配置 - 多地形作战策略
        'sample_weights': {
            # 🎯 极度稀有但极其重要 - 超高权重
            'strong_trend_up': 5.0,                # 强上升趋势：价值连城
            'strong_trend_down': 5.0,              # 强下降趋势：价值连城
            'panic_selling': 6.0,                  # 恐慌性抛售：极度稀有
            'bubble_state': 6.0,                   # 泡沫状态：极度稀有

            # 🎯 重要学习目标 - 高权重
            'low_vol_sideways': 2.5,               # 低波动盘整：趋势酝酿期
            'high_certainty': 3.0,                 # 高确定性状态
            'normal_trend': 1.0,                   # 正常趋势：基准权重

            # 🎯 噪音过滤 - 极低权重或忽略
            'high_vol_sideways': 0.1,              # 高波动盘整：噪音，极低权重
            'extreme_volatility': 0.0,             # 极端波动：直接忽略
            'low_certainty': 0.3                   # 低确定性：降低权重
        }
    },

    # === 日志和调试配置 ===
    'logging': {
        # 日志文件配置
        'log_file_name': 'data_utils.log',
        'log_encoding': 'utf-8',
        'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'log_level': 'INFO',
        'max_log_file_size': '10MB',
        'log_backup_count': 5,

        # 调试配置
        'debug_sample_size': 5,                   # 调试时显示的样本数量
        'max_debug_features': 10,                 # 调试时显示的最大特征数
        'enable_performance_logging': True,       # 启用性能日志
        'log_feature_statistics': False,          # 记录特征统计信息

        # 错误处理配置
        'max_error_details': 3,                   # 最大错误详情数量
        'enable_stack_trace': True,               # 启用堆栈跟踪
        'error_notification_threshold': 10,       # 错误通知阈值
    },

    # === 算法参数配置 ===
    'algorithms': {
        # LSTM配置
        'lstm_sequence_length': 60,               # LSTM序列长度
        'lstm_batch_size': 32,                    # LSTM批次大小
        'lstm_validation_split': 0.2,             # LSTM验证集比例

        # 技术指标配置
        'rsi_period': 14,                         # RSI周期
        'atr_period': 14,                         # ATR周期
        'macd_fast': 12,                          # MACD快线周期
        'macd_slow': 26,                          # MACD慢线周期
        'macd_signal': 9,                         # MACD信号线周期
        'bb_period': 20,                          # 布林带周期
        'bb_std': 2,                              # 布林带标准差倍数

        # 优化配置
        'optimization_trials': 100,               # 优化试验次数
        'optimization_timeout': 3600,             # 优化超时时间（秒）
        'cross_validation_folds': 5,              # 交叉验证折数
        'early_stopping_patience': 10,            # 早停耐心值
    },

    # === 字符串常量配置 ===
    'constants': {
        # 错误消息
        'error_messages': {
            'invalid_dataframe': 'DataFrame为空或None',
            'missing_columns': '缺少必需的列',
            'insufficient_data': '数据量不足',
            'invalid_parameters': '参数无效',
            'api_error': 'API请求失败',
            'calculation_error': '计算过程中出错'
        },

        # 成功消息
        'success_messages': {
            'data_fetched': '成功获取数据',
            'features_calculated': '特征计算完成',
            'model_trained': '模型训练完成',
            'prediction_made': '预测完成'
        },

        # 状态标识
        'status_indicators': {
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败',
            'pending': '等待中'
        }
    }
}


# =============================================================================
# 配置迁移辅助函数
# =============================================================================

def migrate_config_access_patterns():
    """
    配置访问模式迁移指南

    本函数提供从直接字典访问到类型安全包装器的迁移指南。
    """
    migration_guide = """
    🚀 配置访问模式迁移指南

    === 推荐的新模式 ===

    # 1. 获取类型安全的配置包装器
    wrapper = get_target_config_wrapper("BTC_15m_UP")

    # 2. 使用类型安全的访问方法
    name = wrapper.get_str('name', required=True)
    threshold = wrapper.get_float('target_threshold', default=0.001)
    periods = wrapper.get_list('prediction_periods', default=[1])
    enable_ta = wrapper.get_bool('enable_ta', default=True)

    # 3. 处理可能不存在的配置项
    optional_param = wrapper.get('optional_param', default='default_value')

    === 需要替换的旧模式 ===

    # ❌ 直接字典访问（不推荐）
    config = get_target_config("BTC_15m_UP")
    name = config['name']  # 可能抛出 KeyError
    threshold = config.get('target_threshold', 0.001)  # 无类型安全

    # ❌ 直接访问 PREDICTION_TARGETS（不推荐）
    target = next(t for t in PREDICTION_TARGETS if t['name'] == target_name)
    value = target['key']  # 无类型验证和错误处理

    === 迁移步骤 ===

    1. 导入必要的函数：
       from config import get_target_config_wrapper

    2. 替换配置获取：
       # 旧: config = get_target_config(target_name)
       # 新: wrapper = get_target_config_wrapper(target_name)

    3. 替换配置访问：
       # 旧: value = config['key']
       # 新: value = wrapper.get_str('key', required=True)

       # 旧: value = config.get('key', default)
       # 新: value = wrapper.get('key', default=default)

    4. 添加类型安全：
       # 字符串: wrapper.get_str('key', default='')
       # 数值: wrapper.get_float('key', default=0.0)
       # 整数: wrapper.get_int('key', default=0)
       # 布尔: wrapper.get_bool('key', default=False)
       # 列表: wrapper.get_list('key', default=[])

    === 错误处理 ===

    try:
        wrapper = get_target_config_wrapper(target_name)
        value = wrapper.get_str('required_key', required=True)
    except ConfigurationError as e:
        logger.error(f"配置错误: {e}")
        # 处理配置错误
    except ValueError as e:
        logger.error(f"目标未找到: {e}")
        # 处理目标未找到
    """
    print(migration_guide)


def validate_all_target_configs():
    """
    验证所有目标配置的完整性

    Returns:
        Dict[str, List[str]]: 验证结果，键为目标名称，值为错误列表
    """
    validation_results = {}

    for target in PREDICTION_TARGETS:
        target_name = target.get('name', 'Unknown')
        errors = []

        try:
            # 尝试创建配置包装器
            wrapper = get_target_config_wrapper(target_name)

            # 验证必需字段
            required_fields = ['name', 'symbol', 'interval', 'target_variable_type']
            for field in required_fields:
                try:
                    wrapper.get(field, required=True)
                except Exception as e:
                    errors.append(f"缺少必需字段 '{field}': {e}")

            # 验证数值字段的合理性
            try:
                threshold = wrapper.get_float('target_threshold', default=0.001)
                if threshold <= 0 or threshold > 1:
                    errors.append(f"target_threshold 值不合理: {threshold}")
            except Exception as e:
                errors.append(f"target_threshold 类型错误: {e}")

        except Exception as e:
            errors.append(f"配置包装器创建失败: {e}")

        validation_results[target_name] = errors

    return validation_results


# 确保所有目标配置都有必要的字段
for target in PREDICTION_TARGETS:
    if 'name' not in target:
        raise ValueError("每个预测目标配置都必须包含 'name' 字段")

    # 确保每个目标都有 model_save_dir
    if 'model_save_dir' not in target:
        # 🎯 修复：不添加models/前缀，直接使用目标名称
        target['model_save_dir'] = f"trained_models_{target['name'].lower()}"

