# enhanced_realtime_data_manager.py
"""
增强的实时数据管理器
使用WebSocketConnectionManager提供更稳定的实时数据获取
支持多交易对、多时间周期的数据管理
"""

import threading
import time
import json
import logging
import sys
import os
from typing import Dict, Optional, Callable, Any, List, Tuple
import pandas as pd

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from core.websocket_manager import WebSocketConnectionManager, ConnectionState
from core.robust_websocket_manager import RobustWebSocketManager, ConnectionStrategy

logger = logging.getLogger(__name__)

class EnhancedRealtimeDataManager:
    """
    增强的实时数据管理器
    
    功能：
    - 管理多个交易对的实时价格数据
    - 支持多个时间周期的K线数据
    - 使用WebSocketConnectionManager确保连接稳定性
    - 提供数据回调和状态监控
    - 自动重连和降级机制
    """
    
    def __init__(self, proxy_url: str = 'http://127.0.0.1:7897'):
        self.proxy_url = proxy_url
        
        # 数据存储
        self.data_lock = threading.RLock()
        self.latest_prices: Dict[str, float] = {}  # {'BTCUSDT': 30000.0}
        self.latest_klines: Dict[Tuple[str, str], dict] = {}  # {('BTCUSDT', '1m'): kline_dict}
        self.last_update_times: Dict[str, float] = {}  # {'BTCUSDT': timestamp}
        
        # 连接管理器
        self.connection_managers: Dict[str, WebSocketConnectionManager] = {}
        
        # 回调函数
        self.price_callbacks: List[Callable[[str, float], None]] = []
        self.kline_callbacks: List[Callable[[str, str, dict], None]] = []
        self.state_callbacks: List[Callable[[str, ConnectionState], None]] = []
        
        # 状态
        self.is_running = False
        self.managed_symbols: List[str] = []
        
        logger.info("增强实时数据管理器初始化完成")
    
    def add_price_callback(self, callback: Callable[[str, float], None]):
        """添加价格更新回调函数"""
        self.price_callbacks.append(callback)
    
    def add_kline_callback(self, callback: Callable[[str, str, dict], None]):
        """添加K线更新回调函数"""
        self.kline_callbacks.append(callback)
    
    def add_state_callback(self, callback: Callable[[str, ConnectionState], None]):
        """添加状态变化回调函数"""
        self.state_callbacks.append(callback)
    
    def _on_price_update(self, symbol: str, price: float):
        """价格更新回调"""
        with self.data_lock:
            self.latest_prices[symbol] = price
            self.last_update_times[symbol] = time.time()
        
        # 调用外部回调
        for callback in self.price_callbacks:
            try:
                callback(symbol, price)
            except Exception as e:
                logger.error(f"价格回调执行失败: {e}")
    
    def _on_state_change(self, symbol: str, state: ConnectionState):
        """连接状态变化回调"""
        logger.info(f"交易对 {symbol} 连接状态变化: {state.value}")
        
        # 调用外部回调
        for callback in self.state_callbacks:
            try:
                callback(symbol, state)
            except Exception as e:
                logger.error(f"状态回调执行失败: {e}")
    
    def add_symbol(self, symbol: str, use_robust_manager: bool = True) -> bool:
        """添加交易对监控"""
        symbol = symbol.upper()

        if symbol in self.connection_managers:
            logger.warning(f"交易对 {symbol} 已在监控中")
            return True

        logger.info(f"添加交易对监控: {symbol} (使用强化管理器: {use_robust_manager})")

        # 🔧 优先使用强化的WebSocket管理器
        if use_robust_manager:
            try:
                connection_manager = RobustWebSocketManager(
                    symbol=symbol,
                    proxy_url=self.proxy_url,
                    preferred_strategy=ConnectionStrategy.REST_API_POLLING
                )

                # 设置回调函数
                connection_manager.set_price_callback(
                    lambda price, sym=symbol: self._on_price_update(sym, price)
                )

                logger.info(f"使用强化WebSocket管理器 for {symbol}")

            except Exception as e:
                logger.warning(f"强化管理器创建失败，回退到标准管理器: {e}")
                use_robust_manager = False

        # 回退到标准WebSocket管理器
        if not use_robust_manager:
            connection_manager = WebSocketConnectionManager(
                symbol=symbol,
                proxy_url=self.proxy_url,
                max_reconnect_attempts=20,
                initial_retry_delay=1.0,
                max_retry_delay=60.0,
                health_check_interval=30.0,
                degraded_mode_poll_interval=5.0
            )

            # 设置回调函数
            connection_manager.set_price_callback(
                lambda price, sym=symbol: self._on_price_update(sym, price)
            )
            connection_manager.set_state_callback(
                lambda state, sym=symbol: self._on_state_change(sym, state)
            )

            logger.info(f"使用标准WebSocket管理器 for {symbol}")

        self.connection_managers[symbol] = connection_manager
        self.managed_symbols.append(symbol)

        # 如果管理器正在运行，立即启动新的连接
        if self.is_running:
            if connection_manager.start():
                logger.info(f"交易对 {symbol} 连接启动成功")
                return True
            else:
                logger.error(f"交易对 {symbol} 连接启动失败")
                return False

        return True
    
    def remove_symbol(self, symbol: str):
        """移除交易对监控"""
        symbol = symbol.upper()
        
        if symbol not in self.connection_managers:
            logger.warning(f"交易对 {symbol} 不在监控中")
            return
        
        logger.info(f"移除交易对监控: {symbol}")
        
        # 停止连接管理器
        connection_manager = self.connection_managers[symbol]
        connection_manager.stop()
        
        # 清理数据
        del self.connection_managers[symbol]
        if symbol in self.managed_symbols:
            self.managed_symbols.remove(symbol)
        
        with self.data_lock:
            if symbol in self.latest_prices:
                del self.latest_prices[symbol]
            if symbol in self.last_update_times:
                del self.last_update_times[symbol]
    
    def start(self) -> bool:
        """启动实时数据管理器"""
        if self.is_running:
            logger.warning("实时数据管理器已在运行")
            return True

        logger.info("启动实时数据管理器")

        success_count = 0
        total_count = len(self.connection_managers)

        # 如果没有连接管理器，直接标记为启动成功
        if total_count == 0:
            logger.info("实时数据管理器启动成功 - 暂无交易对监控")
            self.is_running = True
            return True

        # 启动所有连接管理器
        for symbol, connection_manager in self.connection_managers.items():
            if connection_manager.start():
                logger.info(f"交易对 {symbol} 连接启动成功")
                success_count += 1
            else:
                logger.error(f"交易对 {symbol} 连接启动失败")

        self.is_running = True

        if success_count > 0:
            logger.info(f"实时数据管理器启动完成 - 成功: {success_count}/{total_count}")
            return True
        else:
            logger.error("实时数据管理器启动失败 - 所有连接都失败")
            return False
    
    def stop(self):
        """停止实时数据管理器"""
        if not self.is_running:
            logger.warning("实时数据管理器未运行")
            return
        
        logger.info("停止实时数据管理器")
        
        # 停止所有连接管理器
        for symbol, connection_manager in self.connection_managers.items():
            logger.info(f"停止交易对 {symbol} 连接")
            connection_manager.stop()
        
        self.is_running = False
        logger.info("实时数据管理器已停止")
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        symbol = symbol.upper()
        with self.data_lock:
            return self.latest_prices.get(symbol)
    
    def get_all_prices(self) -> Dict[str, float]:
        """获取所有价格"""
        with self.data_lock:
            return self.latest_prices.copy()
    
    def get_connection_state(self, symbol: str) -> Optional[ConnectionState]:
        """获取连接状态"""
        symbol = symbol.upper()
        connection_manager = self.connection_managers.get(symbol)
        if connection_manager:
            return connection_manager.get_connection_state()
        return None
    
    def get_all_connection_states(self) -> Dict[str, ConnectionState]:
        """获取所有连接状态"""
        states = {}
        for symbol, connection_manager in self.connection_managers.items():
            states[symbol] = connection_manager.get_connection_state()
        return states
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'is_running': self.is_running,
            'managed_symbols': self.managed_symbols.copy(),
            'total_symbols': len(self.managed_symbols),
            'connection_states': {},
            'connection_stats': {},
            'latest_prices': {},
            'last_update_times': {}
        }
        
        # 收集每个交易对的统计信息
        for symbol, connection_manager in self.connection_managers.items():
            stats['connection_states'][symbol] = connection_manager.get_connection_state().value
            stats['connection_stats'][symbol] = connection_manager.get_stats()
        
        with self.data_lock:
            stats['latest_prices'] = self.latest_prices.copy()
            stats['last_update_times'] = self.last_update_times.copy()
        
        return stats
    
    def force_reconnect(self, symbol: Optional[str] = None):
        """强制重连"""
        if symbol:
            symbol = symbol.upper()
            connection_manager = self.connection_managers.get(symbol)
            if connection_manager:
                logger.info(f"强制重连交易对: {symbol}")
                connection_manager.force_reconnect()
            else:
                logger.warning(f"交易对 {symbol} 不存在")
        else:
            logger.info("强制重连所有交易对")
            for symbol, connection_manager in self.connection_managers.items():
                logger.info(f"强制重连交易对: {symbol}")
                connection_manager.force_reconnect()
    
    def reset_to_websocket(self, symbol: Optional[str] = None):
        """尝试从降级模式恢复到WebSocket模式"""
        if symbol:
            symbol = symbol.upper()
            connection_manager = self.connection_managers.get(symbol)
            if connection_manager:
                return connection_manager.reset_to_websocket()
            return False
        else:
            success_count = 0
            for symbol, connection_manager in self.connection_managers.items():
                if connection_manager.reset_to_websocket():
                    success_count += 1
            return success_count == len(self.connection_managers)
    
    def is_price_fresh(self, symbol: str, max_age_seconds: float = 60.0) -> bool:
        """检查价格数据是否新鲜"""
        symbol = symbol.upper()

        if symbol not in self.latest_prices:
            return False

        current_time = time.time()
        with self.data_lock:
            last_update = self.last_update_times.get(symbol, 0)

        age = current_time - last_update
        return age <= max_age_seconds

    def get_last_update_time(self, symbol: str) -> float:
        """获取指定交易对的最后更新时间"""
        symbol = symbol.upper()
        with self.data_lock:
            return self.last_update_times.get(symbol, 0)

    def get_price_age(self, symbol: str) -> float:
        """获取价格数据的年龄（秒）"""
        symbol = symbol.upper()
        current_time = time.time()
        last_update = self.get_last_update_time(symbol)

        if last_update == 0:
            return float('inf')  # 无数据时返回无穷大

        return current_time - last_update
    
    def wait_for_price(self, symbol: str, timeout_seconds: float = 30.0) -> Optional[float]:
        """等待价格数据"""
        symbol = symbol.upper()
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            price = self.get_current_price(symbol)
            if price is not None:
                return price
            time.sleep(0.1)
        
        logger.warning(f"等待价格数据超时 - {symbol}")
        return None

    def force_reconnect(self, symbol: str = None):
        """强制重连指定交易对或所有交易对"""
        if symbol:
            symbol = symbol.upper()
            if symbol in self.connection_managers:
                logger.info(f"强制重连交易对: {symbol}")
                try:
                    manager = self.connection_managers[symbol]

                    # 🔧 改进的重连策略：先尝试软重连
                    if hasattr(manager, 'force_reconnect'):
                        # 使用管理器自己的重连方法
                        manager.force_reconnect()
                        logger.info(f"交易对 {symbol} 软重连请求已发送")
                    else:
                        # 回退到硬重连
                        logger.info(f"使用硬重连方式重连 {symbol}")
                        # 停止当前连接
                        manager.stop()
                        time.sleep(3)  # 增加等待时间，确保完全停止
                        # 重新启动
                        if manager.start():
                            logger.info(f"交易对 {symbol} 重连成功")
                        else:
                            logger.error(f"交易对 {symbol} 重连失败")

                except Exception as e:
                    logger.error(f"重连交易对 {symbol} 时发生异常: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                logger.warning(f"交易对 {symbol} 不在监控列表中")
        else:
            logger.info("强制重连所有交易对")
            for sym in list(self.connection_managers.keys()):
                try:
                    self.force_reconnect(sym)
                    time.sleep(2)  # 增加间隔，避免同时重连造成冲突
                except Exception as e:
                    logger.error(f"重连交易对 {sym} 失败: {e}")

    def get_connection_state(self, symbol: str) -> str:
        """获取指定交易对的连接状态"""
        symbol = symbol.upper()
        if symbol in self.connection_managers:
            manager = self.connection_managers[symbol]
            if hasattr(manager, 'connection_state'):
                return manager.connection_state.value
            elif hasattr(manager, 'is_connected'):
                return "connected" if manager.is_connected else "disconnected"
        return "unknown"

    def get_all_connection_states(self) -> Dict[str, str]:
        """获取所有交易对的连接状态"""
        states = {}
        for symbol in self.managed_symbols:
            states[symbol] = self.get_connection_state(symbol)
        return states

    def get_connection_status_indicator(self, symbol: str) -> str:
        """获取连接状态指示器（用于GUI显示）"""
        symbol = symbol.upper()
        state = self.get_connection_state(symbol)

        if state == "connected":
            return "🟢"  # 绿色圆点表示正常连接
        elif state == "degraded":
            return "🟡"  # 黄色圆点表示降级模式
        elif state == "reconnecting":
            return "🔄"  # 循环箭头表示重连中
        elif state == "failed":
            return "🔴"  # 红色圆点表示连接失败
        elif state == "connecting":
            return "🔵"  # 蓝色圆点表示连接中
        else:
            return "⚪"  # 白色圆点表示其他状态

    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = {
            'is_running': self.is_running,
            'managed_symbols': self.managed_symbols.copy(),
            'total_symbols': len(self.managed_symbols),
            'price_count': len(self.latest_prices),
            'connection_states': self.get_all_connection_states(),
            'manager_stats': {}
        }

        # 获取每个连接管理器的统计信息
        for symbol, manager in self.connection_managers.items():
            try:
                if hasattr(manager, 'get_stats'):
                    stats['manager_stats'][symbol] = manager.get_stats()
                else:
                    stats['manager_stats'][symbol] = {'is_connected': getattr(manager, 'is_connected', False)}
            except Exception as e:
                stats['manager_stats'][symbol] = {'error': str(e)}

        return stats


# 全局实例（向后兼容）
enhanced_data_manager = EnhancedRealtimeDataManager()

# 向后兼容的函数
def get_latest_price(symbol: str) -> Optional[float]:
    """获取最新价格（向后兼容）"""
    return enhanced_data_manager.get_current_price(symbol)

def get_all_latest_prices() -> Dict[str, float]:
    """获取所有最新价格（向后兼容）"""
    return enhanced_data_manager.get_all_prices()

def add_symbol_monitoring(symbol: str) -> bool:
    """添加交易对监控（向后兼容）"""
    return enhanced_data_manager.add_symbol(symbol)

def remove_symbol_monitoring(symbol: str):
    """移除交易对监控（向后兼容）"""
    enhanced_data_manager.remove_symbol(symbol)

def start_data_manager() -> bool:
    """启动数据管理器（向后兼容）"""
    return enhanced_data_manager.start()

def stop_data_manager():
    """停止数据管理器（向后兼容）"""
    enhanced_data_manager.stop()


# 测试代码
if __name__ == "__main__":
    import logging
    from datetime import datetime
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    def price_callback(symbol: str, price: float):
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"[{current_time}] {symbol} 价格更新: {price:.2f}")
    
    def state_callback(symbol: str, state: ConnectionState):
        print(f"{symbol} 连接状态变化: {state.value}")
    
    # 创建数据管理器
    manager = EnhancedRealtimeDataManager()
    manager.add_price_callback(price_callback)
    manager.add_state_callback(state_callback)
    
    try:
        # 添加交易对
        manager.add_symbol("BTCUSDT")
        manager.add_symbol("ETHUSDT")
        
        # 启动管理器
        if manager.start():
            print("数据管理器启动成功")
            
            # 运行测试
            for i in range(60):  # 运行1分钟
                time.sleep(1)
                
                if i % 10 == 0:  # 每10秒打印一次状态
                    stats = manager.get_stats()
                    print(f"状态统计: {stats['connection_states']}")
                    print(f"价格数据: {stats['latest_prices']}")
        
        else:
            print("数据管理器启动失败")
    
    except KeyboardInterrupt:
        print("用户中断")
    
    finally:
        # 停止管理器
        manager.stop()
        print("测试结束")
