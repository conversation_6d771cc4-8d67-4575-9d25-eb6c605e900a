#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MTFA优化和列过滤单元测试
测试MTFA性能优化器和列过滤器功能
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 忽略警告
warnings.filterwarnings('ignore')

class TestMTFAColumnFilter(unittest.TestCase):
    """测试MTFA列过滤器"""
    
    def setUp(self):
        try:
            from src.core.mtfa_column_filter import (
                MTFAColumnFilter, filter_mtfa_columns, 
                get_default_mtfa_filter_config, get_permissive_mtfa_filter_config
            )
            self.filter_obj = MTFAColumnFilter()
            self.filter_mtfa_columns = filter_mtfa_columns
            self.get_default_config = get_default_mtfa_filter_config
            self.get_permissive_config = get_permissive_mtfa_filter_config
        except ImportError:
            self.skipTest("MTFA column filter not available")
        
        # 创建测试DataFrame
        self.test_df = self._create_test_dataframe()
    
    def _create_test_dataframe(self):
        """创建测试DataFrame"""
        np.random.seed(42)
        n_rows = 50
        
        data = {
            # 基础数据列（应被排除）
            'open': np.random.uniform(29000, 31000, n_rows),
            'high': np.random.uniform(30000, 32000, n_rows),
            'low': np.random.uniform(28000, 30000, n_rows),
            'close': np.random.uniform(29000, 31000, n_rows),
            'volume': np.random.uniform(100, 1000, n_rows),
            
            # 目标变量列（应被排除）
            'target_up_1p': np.random.choice([0, 1], n_rows),
            'future_close_1p': np.random.uniform(29000, 31000, n_rows),
            'label_binary': np.random.choice([0, 1], n_rows),
            
            # 字符串列（应被排除）
            'candlestick_pattern_name': np.random.choice(['doji', 'hammer'], n_rows),
            'signal_name': np.random.choice(['buy', 'sell'], n_rows),
            
            # 临时列（应被排除）
            'temp_calculation': np.random.uniform(0, 1, n_rows),
            'debug_info': np.random.uniform(0, 1, n_rows),
            
            # 元数据列（应被排除）
            'timestamp': pd.date_range('2023-01-01', periods=n_rows, freq='5T'),
            'symbol': ['BTCUSDT'] * n_rows,
            
            # 统计列（可配置排除）
            'feature_count': np.random.randint(10, 100, n_rows),
            'calculation_mean': np.random.uniform(0, 1, n_rows),
            
            # 业务列（可配置排除）
            'commission': np.random.uniform(0.001, 0.01, n_rows),
            'slippage': np.random.uniform(0.0001, 0.001, n_rows),
            
            # 技术指标（应被保留）
            'RSI_14': np.random.uniform(0, 100, n_rows),
            'MACD': np.random.uniform(-1, 1, n_rows),
            'HMA_20': np.random.uniform(29000, 31000, n_rows),
            'ATRr_14': np.random.uniform(0, 1000, n_rows),
            
            # 价格特征（应被保留）
            'price_change_1p': np.random.uniform(-0.05, 0.05, n_rows),
            'body_size': np.random.uniform(0, 1000, n_rows),
            'close_pos_in_candle': np.random.uniform(0, 1, n_rows),
            
            # 成交量特征（应被保留）
            'volume_vs_avg': np.random.uniform(0.5, 2.0, n_rows),
            'volume_change_1p': np.random.uniform(-0.5, 0.5, n_rows),
            
            # 波动率特征（应被保留）
            'volatility_5p': np.random.uniform(0, 0.1, n_rows),
            'atr_normalized': np.random.uniform(0, 1, n_rows),
        }
        
        return pd.DataFrame(data)
    
    def test_basic_filtering(self):
        """测试基础过滤功能"""
        config = self.get_default_config()
        filtered_columns = self.filter_mtfa_columns(self.test_df, '15m', config)
        
        # 检查基础列被排除
        base_cols = {'open', 'high', 'low', 'close', 'volume'}
        self.assertTrue(base_cols.isdisjoint(set(filtered_columns)))
        
        # 检查目标列被排除
        target_cols = {'target_up_1p', 'future_close_1p', 'label_binary'}
        self.assertTrue(target_cols.isdisjoint(set(filtered_columns)))
        
        # 检查字符串列被排除
        string_cols = {'candlestick_pattern_name', 'signal_name'}
        self.assertTrue(string_cols.isdisjoint(set(filtered_columns)))
        
        # 检查技术指标被保留
        tech_cols = {'RSI_14', 'MACD', 'HMA_20', 'ATRr_14'}
        self.assertTrue(tech_cols.issubset(set(filtered_columns)))
    
    def test_configurable_filtering(self):
        """测试可配置过滤"""
        # 严格模式
        strict_config = self.get_default_config()
        strict_columns = self.filter_mtfa_columns(self.test_df, '15m', strict_config)
        
        # 宽松模式
        permissive_config = self.get_permissive_config()
        permissive_columns = self.filter_mtfa_columns(self.test_df, '15m', permissive_config)
        
        # 宽松模式应该保留更多列
        self.assertGreater(len(permissive_columns), len(strict_columns))
        
        # 严格模式应该排除统计列
        self.assertNotIn('feature_count', strict_columns)
        self.assertNotIn('calculation_mean', strict_columns)
        
        # 宽松模式可能保留统计列
        # （这取决于具体实现，这里只检查不会出错）
        self.assertIsInstance(permissive_columns, list)
    
    def test_custom_exclusion_patterns(self):
        """测试自定义排除模式"""
        config = self.get_default_config()
        config['custom_exclusion_patterns'] = ['commission', 'slippage']
        
        filtered_columns = self.filter_mtfa_columns(self.test_df, '15m', config)
        
        # 检查自定义排除生效
        self.assertNotIn('commission', filtered_columns)
        self.assertNotIn('slippage', filtered_columns)
    
    def test_custom_inclusion_patterns(self):
        """测试自定义包含模式"""
        config = self.get_default_config()
        config['loose_inclusion_mode'] = False  # 关闭宽松模式
        config['custom_inclusion_patterns'] = ['RSI_', 'MACD']
        
        filtered_columns = self.filter_mtfa_columns(self.test_df, '15m', config)
        
        # 应该包含RSI和MACD
        rsi_cols = [col for col in filtered_columns if 'RSI_' in col]
        macd_cols = [col for col in filtered_columns if 'MACD' in col]
        
        self.assertGreater(len(rsi_cols), 0)
        self.assertGreater(len(macd_cols), 0)
    
    def test_filter_summary(self):
        """测试过滤摘要"""
        config = self.get_default_config()
        filtered_columns = self.filter_obj.filter_mtfa_columns(self.test_df, '15m', config)
        
        summary = self.filter_obj.get_filter_summary(
            self.test_df.columns.tolist(), 
            filtered_columns, 
            '15m'
        )
        
        # 检查摘要字段
        self.assertIn('timeframe', summary)
        self.assertIn('original_count', summary)
        self.assertIn('filtered_count', summary)
        self.assertIn('excluded_count', summary)
        self.assertIn('retention_rate', summary)
        
        # 检查数值合理性
        self.assertEqual(summary['original_count'], len(self.test_df.columns))
        self.assertEqual(summary['filtered_count'], len(filtered_columns))
        self.assertEqual(
            summary['excluded_count'], 
            summary['original_count'] - summary['filtered_count']
        )
        self.assertGreaterEqual(summary['retention_rate'], 0)
        self.assertLessEqual(summary['retention_rate'], 1)
    
    def test_empty_dataframe(self):
        """测试空DataFrame"""
        empty_df = pd.DataFrame()
        config = self.get_default_config()
        
        filtered_columns = self.filter_mtfa_columns(empty_df, '15m', config)
        self.assertEqual(len(filtered_columns), 0)
    
    def test_none_dataframe(self):
        """测试None输入"""
        config = self.get_default_config()
        
        filtered_columns = self.filter_mtfa_columns(None, '15m', config)
        self.assertEqual(len(filtered_columns), 0)


class TestMTFAPerformanceOptimizer(unittest.TestCase):
    """测试MTFA性能优化器"""
    
    def setUp(self):
        try:
            from src.optimization.mtfa_performance_optimizer import (
                MTFAPerformanceOptimizer, get_mtfa_optimized_config, 
                process_single_mtfa_timeframe
            )
            self.MTFAPerformanceOptimizer = MTFAPerformanceOptimizer
            self.get_mtfa_optimized_config = get_mtfa_optimized_config
            self.process_single_mtfa_timeframe = process_single_mtfa_timeframe
        except ImportError:
            self.skipTest("MTFA performance optimizer not available")
    
    def test_mtfa_optimized_config(self):
        """测试MTFA优化配置"""
        base_config = {
            'enable_ta': True,
            'enable_candle': True,
            'enable_time_trigonometric': True,
            'enable_pattern_recognition': True,
            'ema_periods': [10, 20, 50, 100],
            'rsi_period': 14
        }
        
        optimized_config = self.get_mtfa_optimized_config(base_config)
        
        # 检查优化配置
        self.assertFalse(optimized_config.get('enable_time_trigonometric', True))
        self.assertFalse(optimized_config.get('enable_pattern_recognition', True))
        self.assertFalse(optimized_config.get('enable_candle', True))
        
        # 检查EMA周期被简化
        ema_periods = optimized_config.get('ema_periods', [])
        self.assertLessEqual(len(ema_periods), len(base_config['ema_periods']))
        
        # 检查保留的关键配置
        self.assertTrue(optimized_config.get('enable_ta', False))
        self.assertEqual(optimized_config.get('rsi_period'), 14)
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        # 测试默认参数
        optimizer = self.MTFAPerformanceOptimizer()
        self.assertEqual(optimizer.max_workers, 2)
        self.assertTrue(optimizer.enable_parallel)
        
        # 测试自定义参数
        optimizer_custom = self.MTFAPerformanceOptimizer(max_workers=3, enable_parallel=False)
        self.assertEqual(optimizer_custom.max_workers, 3)
        self.assertFalse(optimizer_custom.enable_parallel)
    
    def test_performance_stats(self):
        """测试性能统计"""
        optimizer = self.MTFAPerformanceOptimizer()
        
        # 初始状态应该为空
        stats = optimizer.get_performance_stats()
        self.assertIsInstance(stats, dict)
        self.assertEqual(len(stats), 0)
    
    @patch('src.optimization.mtfa_performance_optimizer.fetch_binance_history')
    @patch('src.optimization.mtfa_performance_optimizer.add_classification_features')
    def test_single_timeframe_processing(self, mock_add_features, mock_fetch_history):
        """测试单个时间框架处理"""
        # 设置mock返回值
        mock_df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [95, 96, 97],
            'close': [100, 101, 102],
            'volume': [1000, 1100, 1200],
            'RSI_14': [50, 55, 60],
            'MACD': [0.1, 0.2, 0.3]
        })
        
        mock_fetch_history.return_value = mock_df
        mock_add_features.return_value = mock_df
        
        # 测试配置
        target_config = {
            'name': 'TEST',
            'symbol': 'BTCUSDT',
            'interval': '5m'
        }
        
        primary_df = pd.DataFrame({
            'close': [100, 101, 102]
        }, index=pd.date_range('2023-01-01', periods=3, freq='5T'))
        
        mock_client = Mock()
        fetch_params = {
            'start_dt': pd.Timestamp('2023-01-01'),
            'end_dt': pd.Timestamp('2023-01-02'),
            'limit': 100
        }
        
        # 执行测试
        timeframe, result = self.process_single_mtfa_timeframe(
            '15m', target_config, primary_df, mock_client, fetch_params
        )
        
        # 验证结果
        self.assertEqual(timeframe, '15m')
        if result is not None:
            self.assertIsInstance(result, pd.DataFrame)
            # 检查列名是否添加了时间框架后缀
            for col in result.columns:
                self.assertTrue(col.endswith('_15m'))


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
