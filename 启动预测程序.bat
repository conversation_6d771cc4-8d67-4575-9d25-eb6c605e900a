@echo off
title Prediction Program - Trading System

echo ========================================
echo    Starting Prediction Program
echo ========================================
echo.

echo [INFO] Starting prediction program...
echo [INFO] Current directory: %CD%
echo.

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not installed or not in PATH
    echo [ERROR] Please install Python and add to system PATH
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "main.py" (
    echo [ERROR] main.py file not found
    echo [ERROR] Please ensure running in correct project directory
    pause
    exit /b 1
)

echo [INFO] Python environment check passed
echo [INFO] Starting prediction program...
echo.

REM Start prediction program
python main.py

REM Check program exit status
if errorlevel 1 (
    echo.
    echo [ERROR] Prediction program exited abnormally
    echo [ERROR] Please check log files for detailed error information
) else (
    echo.
    echo [INFO] Prediction program exited normally
)

echo.
echo Press any key to close window...
pause >nul
