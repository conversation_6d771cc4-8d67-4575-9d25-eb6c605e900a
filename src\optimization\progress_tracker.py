#!/usr/bin/env python3
"""
进度跟踪器 - 跟踪训练过程中的进度和时间估算
支持嵌套进度跟踪和时间预测
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ProgressStage:
    """进度阶段数据类"""
    name: str
    total_steps: int
    current_step: int = 0
    start_time: Optional[float] = None
    estimated_duration: Optional[float] = None
    parent_stage: Optional[str] = None
    substages: List[str] = None
    
    def __post_init__(self):
        if self.substages is None:
            self.substages = []

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, enable_time_estimation: bool = True):
        """
        初始化进度跟踪器
        
        Args:
            enable_time_estimation: 是否启用时间估算
        """
        self.enable_time_estimation = enable_time_estimation
        self._lock = threading.RLock()
        
        # 进度阶段
        self._stages = {}
        self._stage_stack = []  # 用于嵌套阶段
        self._current_stage = None
        
        # 历史数据（用于时间估算）
        self._historical_durations = {}
        
        # 回调函数
        self._progress_callbacks = []
        
        logger.info("进度跟踪器初始化完成")
    
    def start_stage(self, 
                   stage_name: str, 
                   total_steps: int,
                   estimated_duration: Optional[float] = None) -> str:
        """
        开始一个新的进度阶段
        
        Args:
            stage_name: 阶段名称
            total_steps: 总步数
            estimated_duration: 预估持续时间（秒）
            
        Returns:
            阶段ID
        """
        with self._lock:
            # 生成唯一的阶段ID
            stage_id = f"{stage_name}_{int(time.time() * 1000)}"
            
            # 确定父阶段
            parent_stage = self._current_stage
            
            # 创建阶段对象
            stage = ProgressStage(
                name=stage_name,
                total_steps=total_steps,
                start_time=time.time(),
                estimated_duration=estimated_duration,
                parent_stage=parent_stage
            )
            
            # 存储阶段
            self._stages[stage_id] = stage
            
            # 更新阶段栈
            self._stage_stack.append(stage_id)
            self._current_stage = stage_id
            
            # 如果有父阶段，添加到父阶段的子阶段列表
            if parent_stage and parent_stage in self._stages:
                self._stages[parent_stage].substages.append(stage_id)
            
            # 使用历史数据估算时间
            if self.enable_time_estimation and estimated_duration is None:
                historical_duration = self._get_historical_duration(stage_name)
                if historical_duration:
                    stage.estimated_duration = historical_duration
            
            logger.debug(f"开始进度阶段: {stage_name} (ID: {stage_id}), 总步数: {total_steps}")
            
            # 触发回调
            self._trigger_callbacks('stage_start', stage_id, stage)
            
            return stage_id
    
    def update_progress(self, 
                       stage_id: Optional[str] = None, 
                       current_step: Optional[int] = None,
                       increment: int = 1,
                       message: Optional[str] = None):
        """
        更新进度
        
        Args:
            stage_id: 阶段ID，如果为None则使用当前阶段
            current_step: 当前步数，如果为None则增量更新
            increment: 增量步数（当current_step为None时使用）
            message: 进度消息
        """
        with self._lock:
            # 确定要更新的阶段
            if stage_id is None:
                stage_id = self._current_stage
            
            if stage_id is None or stage_id not in self._stages:
                logger.warning(f"无效的阶段ID: {stage_id}")
                return
            
            stage = self._stages[stage_id]
            
            # 更新步数
            if current_step is not None:
                stage.current_step = min(current_step, stage.total_steps)
            else:
                stage.current_step = min(stage.current_step + increment, stage.total_steps)
            
            # 计算进度百分比
            progress_percent = stage.current_step / stage.total_steps if stage.total_steps > 0 else 0
            
            logger.debug(f"更新进度: {stage.name} - {stage.current_step}/{stage.total_steps} "
                        f"({progress_percent:.1%})")
            
            # 触发回调
            self._trigger_callbacks('progress_update', stage_id, stage, {
                'progress_percent': progress_percent,
                'message': message
            })
    
    def finish_stage(self, stage_id: Optional[str] = None) -> Optional[float]:
        """
        完成一个进度阶段
        
        Args:
            stage_id: 阶段ID，如果为None则使用当前阶段
            
        Returns:
            阶段持续时间（秒）
        """
        with self._lock:
            # 确定要完成的阶段
            if stage_id is None:
                stage_id = self._current_stage
            
            if stage_id is None or stage_id not in self._stages:
                logger.warning(f"无效的阶段ID: {stage_id}")
                return None
            
            stage = self._stages[stage_id]
            
            # 计算持续时间
            duration = time.time() - stage.start_time if stage.start_time else 0
            
            # 确保进度完成
            stage.current_step = stage.total_steps
            
            # 更新历史数据
            if self.enable_time_estimation:
                self._update_historical_duration(stage.name, duration)
            
            # 从阶段栈中移除
            if stage_id in self._stage_stack:
                self._stage_stack.remove(stage_id)
            
            # 更新当前阶段
            self._current_stage = self._stage_stack[-1] if self._stage_stack else None
            
            logger.debug(f"完成进度阶段: {stage.name}, 耗时: {duration:.2f}秒")
            
            # 触发回调
            self._trigger_callbacks('stage_finish', stage_id, stage, {'duration': duration})
            
            return duration
    
    def get_overall_progress(self) -> Dict[str, Any]:
        """获取整体进度信息"""
        with self._lock:
            if not self._stages:
                return {
                    'progress_percent': 0,
                    'current_stage': None,
                    'estimated_remaining_time': None,
                    'total_stages': 0,
                    'completed_stages': 0
                }
            
            # 计算根阶段的进度
            root_stages = [stage_id for stage_id, stage in self._stages.items() 
                          if stage.parent_stage is None]
            
            if not root_stages:
                return {
                    'progress_percent': 0,
                    'current_stage': None,
                    'estimated_remaining_time': None,
                    'total_stages': 0,
                    'completed_stages': 0
                }
            
            # 计算加权平均进度
            total_weight = 0
            weighted_progress = 0
            completed_stages = 0
            
            for stage_id in root_stages:
                stage = self._stages[stage_id]
                stage_progress = stage.current_step / stage.total_steps if stage.total_steps > 0 else 0
                weight = stage.total_steps
                
                weighted_progress += stage_progress * weight
                total_weight += weight
                
                if stage.current_step >= stage.total_steps:
                    completed_stages += 1
            
            overall_progress = weighted_progress / total_weight if total_weight > 0 else 0
            
            # 估算剩余时间
            estimated_remaining_time = None
            if self.enable_time_estimation and self._current_stage:
                estimated_remaining_time = self._estimate_remaining_time()
            
            return {
                'progress_percent': overall_progress,
                'current_stage': self._get_current_stage_info(),
                'estimated_remaining_time': estimated_remaining_time,
                'total_stages': len(root_stages),
                'completed_stages': completed_stages
            }
    
    def _get_current_stage_info(self) -> Optional[Dict[str, Any]]:
        """获取当前阶段信息"""
        if not self._current_stage or self._current_stage not in self._stages:
            return None
        
        stage = self._stages[self._current_stage]
        progress_percent = stage.current_step / stage.total_steps if stage.total_steps > 0 else 0
        
        return {
            'stage_id': self._current_stage,
            'name': stage.name,
            'progress_percent': progress_percent,
            'current_step': stage.current_step,
            'total_steps': stage.total_steps
        }
    
    def _estimate_remaining_time(self) -> Optional[float]:
        """估算剩余时间"""
        if not self._current_stage or self._current_stage not in self._stages:
            return None
        
        stage = self._stages[self._current_stage]
        
        if stage.current_step == 0 or not stage.start_time:
            # 使用历史数据估算
            return self._get_historical_duration(stage.name)
        
        # 基于当前进度估算
        elapsed_time = time.time() - stage.start_time
        progress_percent = stage.current_step / stage.total_steps if stage.total_steps > 0 else 0
        
        if progress_percent > 0:
            estimated_total_time = elapsed_time / progress_percent
            remaining_time = estimated_total_time - elapsed_time
            return max(0, remaining_time)
        
        return None
    
    def _get_historical_duration(self, stage_name: str) -> Optional[float]:
        """获取历史持续时间"""
        if stage_name in self._historical_durations:
            durations = self._historical_durations[stage_name]
            return sum(durations) / len(durations)  # 平均值
        return None
    
    def _update_historical_duration(self, stage_name: str, duration: float):
        """更新历史持续时间"""
        if stage_name not in self._historical_durations:
            self._historical_durations[stage_name] = []
        
        # 保留最近10次的记录
        self._historical_durations[stage_name].append(duration)
        if len(self._historical_durations[stage_name]) > 10:
            self._historical_durations[stage_name].pop(0)
    
    def add_progress_callback(self, callback: Callable):
        """添加进度回调函数"""
        self._progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable):
        """移除进度回调函数"""
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)
    
    def _trigger_callbacks(self, event_type: str, stage_id: str, stage: ProgressStage, extra_data: Dict = None):
        """触发回调函数"""
        for callback in self._progress_callbacks:
            try:
                callback(event_type, stage_id, stage, extra_data or {})
            except Exception as e:
                logger.warning(f"进度回调函数执行失败: {e}")
    
    def print_progress_report(self):
        """打印进度报告"""
        overall = self.get_overall_progress()
        
        print(f"\n📈 进度跟踪报告")
        print(f"=" * 50)
        print(f"整体进度: {overall['progress_percent']:.1%}")
        print(f"总阶段数: {overall['total_stages']}")
        print(f"已完成阶段: {overall['completed_stages']}")
        
        if overall['current_stage']:
            current = overall['current_stage']
            print(f"当前阶段: {current['name']}")
            print(f"阶段进度: {current['current_step']}/{current['total_steps']} "
                  f"({current['progress_percent']:.1%})")
        
        if overall['estimated_remaining_time']:
            remaining_time = timedelta(seconds=int(overall['estimated_remaining_time']))
            print(f"预计剩余时间: {remaining_time}")
    
    def reset(self):
        """重置进度跟踪器"""
        with self._lock:
            self._stages.clear()
            self._stage_stack.clear()
            self._current_stage = None
            logger.info("进度跟踪器已重置")


# 全局进度跟踪器实例
_global_progress_tracker = None

def get_progress_tracker() -> ProgressTracker:
    """获取全局进度跟踪器实例"""
    global _global_progress_tracker
    if _global_progress_tracker is None:
        _global_progress_tracker = ProgressTracker()
    return _global_progress_tracker
