# trade_logger.py
"""
交易日志记录模块
用于记录加密货币交易系统的开仓、平仓和盈亏结果到CSV文件
支持完整的模型决策上下文信息记录
"""

import csv
import os
import threading
import time
import json
from datetime import datetime
from typing import Dict, Optional, Any, Union
import logging


class TradeLogger:
    """
    升级版交易日志记录器

    功能:
    - 记录交易开仓信息（包含完整的模型决策上下文）
    - 记录交易平仓结果
    - 线程安全的CSV文件写入
    - 自动计算盈亏
    - 支持模型预测信息、市场状态、核心特征快照记录
    """
    
    def __init__(self, log_file_path: str = "results/logs/trade_log.csv"):
        """
        初始化交易日志记录器
        
        Args:
            log_file_path: CSV日志文件路径
        """
        self.log_file_path = log_file_path
        self.pending_trades: Dict[str, Dict[str, Any]] = {}  # 暂存开仓信息
        self.file_lock = threading.Lock()  # 文件操作锁
        self.logger = logging.getLogger(__name__)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.log_file_path), exist_ok=True)
        
        # 初始化CSV文件头部
        self._initialize_csv_file()
        
        self.logger.info(f"TradeLogger 初始化完成，日志文件: {self.log_file_path}")
    
    def _initialize_csv_file(self):
        """初始化CSV文件，如果文件不存在或为空则创建并写入表头"""
        needs_header = False

        if not os.path.exists(self.log_file_path):
            needs_header = True
        else:
            # 检查文件是否为空或只有空白字符
            try:
                with open(self.log_file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        needs_header = True
            except Exception:
                needs_header = True

        if needs_header:
            with self.file_lock:
                # 再次检查（防止并发创建）
                needs_header_again = False
                if not os.path.exists(self.log_file_path):
                    needs_header_again = True
                else:
                    try:
                        with open(self.log_file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if not content:
                                needs_header_again = True
                    except Exception:
                        needs_header_again = True

                if needs_header_again:
                    try:
                        with open(self.log_file_path, 'w', newline='', encoding='utf-8') as csvfile:
                            fieldnames = self._get_csv_fieldnames()
                            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                            writer.writeheader()
                        self.logger.info(f"创建新的交易日志文件: {self.log_file_path}")
                    except Exception as e:
                        self.logger.error(f"初始化CSV文件失败: {e}")
                        raise

    def _get_csv_fieldnames(self) -> list:
        """
        获取CSV文件的完整字段名列表

        Returns:
            包含所有字段名的列表，按逻辑分组排列
        """
        return [
            # 基础交易信息
            'trade_id', 'entry_timestamp', 'exit_timestamp', 'target_name',
            'symbol', 'direction', 'entry_price', 'exit_price', 'amount',
            'payout_ratio', 'result', 'profit_loss', 'exit_reason',

            # 模型预测信息 (Metrics at Entry)
            'entry_signal_probability',      # 信号方向预测概率
            'entry_neutral_probability',     # 中性预测概率
            'entry_opposite_probability',    # 相反方向预测概率
            'meta_model_inputs',            # 元模型输入特征(JSON)

            # 市场状态信息 (Market State at Entry)
            'entry_market_regime',          # 市场状态
            'entry_atr_percent',           # ATR波动率百分比
            'entry_adx_value',             # ADX趋势强度值

            # 核心特征快照 (Key Features at Entry)
            'entry_top_features'           # Top 10重要特征值(JSON)
        ]

    def generate_trade_id(self, target_name: str) -> str:
        """
        生成唯一的交易ID
        
        Args:
            target_name: 策略/模型名称
            
        Returns:
            唯一的交易ID
        """
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        return f"{target_name}_{timestamp}"
    
    def record_trade_entry(self,
                          target_name: str,
                          symbol: str,
                          direction: str,
                          entry_price: float,
                          amount: float,
                          payout_ratio: float = 0.85,
                          trade_id: Optional[str] = None,
                          context_data: Optional[Dict[str, Any]] = None) -> str:
        """
        记录交易开仓信息（升级版，支持完整的模型决策上下文）

        Args:
            target_name: 策略/模型名称 (例如: BTC_15m_UP, MetaModel)
            symbol: 交易对 (例如: BTCUSDT)
            direction: 交易方向 (LONG 或 SHORT)
            entry_price: 开仓价格
            amount: 投入金额
            payout_ratio: 赔率 (默认0.85)
            trade_id: 可选的自定义交易ID，如果不提供则自动生成
            context_data: 可选的上下文数据字典，包含以下字段:
                模型预测信息:
                - signal_probability: 信号方向预测概率
                - neutral_probability: 中性预测概率
                - opposite_probability: 相反方向预测概率
                - meta_model_inputs: 元模型输入特征字典
                市场状态信息:
                - market_regime: 市场状态字符串
                - atr_percent: ATR波动率百分比
                - adx_value: ADX趋势强度值
                核心特征快照:
                - top_features: Top 10重要特征字典

        Returns:
            交易ID
        """
        if trade_id is None:
            trade_id = self.generate_trade_id(target_name)
        
        entry_timestamp = datetime.now().isoformat()
        
        # 验证输入参数
        direction = direction.upper()
        if direction not in ['LONG', 'SHORT', 'UP', 'DOWN']:
            raise ValueError(f"无效的交易方向: {direction}")
        
        # 标准化方向名称
        if direction in ['UP']:
            direction = 'LONG'
        elif direction in ['DOWN']:
            direction = 'SHORT'
        
        if amount <= 0:
            raise ValueError(f"交易金额必须大于0: {amount}")
        
        if entry_price <= 0:
            raise ValueError(f"开仓价格必须大于0: {entry_price}")
        
        # 处理上下文数据
        processed_context = self._process_context_data(context_data)

        # 暂存开仓信息（包含上下文数据）
        trade_entry_data = {
            'trade_id': trade_id,
            'entry_timestamp': entry_timestamp,
            'target_name': target_name,
            'symbol': symbol,
            'direction': direction,
            'entry_price': entry_price,
            'amount': amount,
            'payout_ratio': payout_ratio,
            **processed_context  # 合并上下文数据
        }

        self.pending_trades[trade_id] = trade_entry_data

        # 记录日志（包含上下文信息摘要）
        context_summary = self._get_context_summary(processed_context)
        self.logger.info(f"记录交易开仓: {trade_id} - {direction} {symbol} @ {entry_price:.2f}, "
                        f"金额: {amount:.2f}{context_summary}")

        return trade_id

    def _process_context_data(self, context_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理和验证上下文数据，转换为CSV字段格式

        Args:
            context_data: 原始上下文数据字典

        Returns:
            处理后的上下文数据字典，键名对应CSV字段名
        """
        processed = {}

        if context_data is None:
            # 如果没有提供上下文数据，填充空值
            processed.update({
                'entry_signal_probability': None,
                'entry_neutral_probability': None,
                'entry_opposite_probability': None,
                'meta_model_inputs': None,
                'entry_market_regime': None,
                'entry_atr_percent': None,
                'entry_adx_value': None,
                'entry_top_features': None
            })
            return processed

        # 处理模型预测信息
        processed['entry_signal_probability'] = context_data.get('signal_probability')
        processed['entry_neutral_probability'] = context_data.get('neutral_probability')
        processed['entry_opposite_probability'] = context_data.get('opposite_probability')

        # 处理元模型输入（转换为JSON字符串）
        meta_inputs = context_data.get('meta_model_inputs')
        if meta_inputs is not None:
            try:
                processed['meta_model_inputs'] = json.dumps(meta_inputs, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                self.logger.warning(f"无法序列化meta_model_inputs: {e}")
                processed['meta_model_inputs'] = str(meta_inputs)
        else:
            processed['meta_model_inputs'] = None

        # 处理市场状态信息
        processed['entry_market_regime'] = context_data.get('market_regime')
        processed['entry_atr_percent'] = context_data.get('atr_percent')
        processed['entry_adx_value'] = context_data.get('adx_value')

        # 处理核心特征快照（转换为JSON字符串）
        top_features = context_data.get('top_features')
        if top_features is not None:
            try:
                processed['entry_top_features'] = json.dumps(top_features, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                self.logger.warning(f"无法序列化top_features: {e}")
                processed['entry_top_features'] = str(top_features)
        else:
            processed['entry_top_features'] = None

        return processed

    def _get_context_summary(self, processed_context: Dict[str, Any]) -> str:
        """
        生成上下文信息的简要摘要用于日志记录

        Args:
            processed_context: 处理后的上下文数据

        Returns:
            上下文摘要字符串
        """
        summary_parts = []

        # 添加信号概率信息
        signal_prob = processed_context.get('entry_signal_probability')
        if signal_prob is not None:
            summary_parts.append(f"信号概率: {signal_prob:.3f}")

        # 添加市场状态信息
        market_regime = processed_context.get('entry_market_regime')
        if market_regime:
            summary_parts.append(f"市场状态: {market_regime}")

        # 添加ATR信息
        atr_percent = processed_context.get('entry_atr_percent')
        if atr_percent is not None:
            summary_parts.append(f"ATR: {atr_percent:.2f}%")

        if summary_parts:
            return f", {', '.join(summary_parts)}"
        else:
            return ""

    def record_trade_exit(self,
                         trade_id: str,
                         exit_price: float,
                         result: str,
                         exit_reason: str = "expired") -> bool:
        """
        记录交易平仓信息并写入CSV文件
        
        Args:
            trade_id: 交易ID
            exit_price: 平仓价格
            result: 交易结果 (WIN 或 LOSS)
            exit_reason: 平仓原因 (例如: expired, manual_close)
            
        Returns:
            是否成功记录
        """
        if trade_id not in self.pending_trades:
            self.logger.error(f"未找到交易ID: {trade_id}")
            return False
        
        # 验证结果
        result = result.upper()
        if result not in ['WIN', 'LOSS']:
            raise ValueError(f"无效的交易结果: {result}")
        
        if exit_price <= 0:
            raise ValueError(f"平仓价格必须大于0: {exit_price}")
        
        # 获取开仓信息
        entry_data = self.pending_trades[trade_id]
        
        # 计算盈亏
        amount = entry_data['amount']
        payout_ratio = entry_data['payout_ratio']
        
        if result == 'WIN':
            profit_loss = amount * payout_ratio
        else:  # LOSS
            profit_loss = -amount
        
        # 构建完整的交易记录（包含所有上下文信息）
        complete_trade_record = {
            # 基础交易信息
            'trade_id': trade_id,
            'entry_timestamp': entry_data['entry_timestamp'],
            'exit_timestamp': datetime.now().isoformat(),
            'target_name': entry_data['target_name'],
            'symbol': entry_data['symbol'],
            'direction': entry_data['direction'],
            'entry_price': entry_data['entry_price'],
            'exit_price': exit_price,
            'amount': amount,
            'payout_ratio': payout_ratio,
            'result': result,
            'profit_loss': profit_loss,
            'exit_reason': exit_reason,

            # 上下文信息（从开仓时保存的数据中获取）
            'entry_signal_probability': entry_data.get('entry_signal_probability'),
            'entry_neutral_probability': entry_data.get('entry_neutral_probability'),
            'entry_opposite_probability': entry_data.get('entry_opposite_probability'),
            'meta_model_inputs': entry_data.get('meta_model_inputs'),
            'entry_market_regime': entry_data.get('entry_market_regime'),
            'entry_atr_percent': entry_data.get('entry_atr_percent'),
            'entry_adx_value': entry_data.get('entry_adx_value'),
            'entry_top_features': entry_data.get('entry_top_features')
        }
        
        # 线程安全地写入CSV文件
        success = self._write_trade_to_csv(complete_trade_record)
        
        if success:
            # 从暂存中移除
            del self.pending_trades[trade_id]
            self.logger.info(f"交易平仓记录完成: {trade_id} - {result}, 盈亏: {profit_loss:.2f}")
        
        return success
    
    def _write_trade_to_csv(self, trade_record: Dict[str, Any]) -> bool:
        """
        线程安全地将交易记录写入CSV文件

        Args:
            trade_record: 完整的交易记录字典

        Returns:
            是否写入成功
        """
        with self.file_lock:
            try:
                with open(self.log_file_path, 'a', newline='', encoding='utf-8') as csvfile:
                    fieldnames = self._get_csv_fieldnames()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writerow(trade_record)
                return True
            except Exception as e:
                self.logger.error(f"写入CSV文件失败: {e}")
                return False
    
    def get_pending_trades_count(self) -> int:
        """获取待平仓交易数量"""
        return len(self.pending_trades)
    
    def get_pending_trade_ids(self) -> list:
        """获取所有待平仓交易ID列表"""
        return list(self.pending_trades.keys())
    
    def clear_pending_trades(self):
        """清空所有待平仓交易（谨慎使用）"""
        cleared_count = len(self.pending_trades)
        self.pending_trades.clear()
        self.logger.warning(f"清空了 {cleared_count} 个待平仓交易记录")


# 全局交易日志记录器实例
_global_trade_logger: Optional[TradeLogger] = None


def get_trade_logger(log_file_path: str = "results/logs/trade_log.csv") -> TradeLogger:
    """
    🚫 已废弃：重定向到简化的UnifiedTradeLogger

    为了确保只使用简化的28字段日志格式，此函数现在返回UnifiedTradeLogger的兼容包装器

    Args:
        log_file_path: CSV日志文件路径（将被忽略，强制使用trading_logs_unified）

    Returns:
        UnifiedTradeLogger的兼容包装器
    """
    import warnings
    warnings.warn(
        "TradeLogger已废弃，系统自动重定向到简化的UnifiedTradeLogger",
        DeprecationWarning,
        stacklevel=2
    )

    # 🎯 重定向到简化的UnifiedTradeLogger
    from .unified_trade_logger import get_unified_trade_logger
    return get_unified_trade_logger(base_log_dir="trading_logs_unified", auto_start=True)


if __name__ == "__main__":
    # 测试代码
    import tempfile
    import os
    
    # 创建临时测试文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        test_file_path = f.name
    
    try:
        print("=== TradeLogger 测试 ===")
        
        # 初始化日志记录器
        logger = TradeLogger(test_file_path)
        
        # 测试开仓记录
        trade_id1 = logger.record_trade_entry(
            target_name="BTC_15m_UP",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=10.0,
            payout_ratio=0.85
        )
        print(f"开仓记录1: {trade_id1}")
        
        trade_id2 = logger.record_trade_entry(
            target_name="MetaModel",
            symbol="ETHUSDT", 
            direction="SHORT",
            entry_price=3000.0,
            amount=15.0
        )
        print(f"开仓记录2: {trade_id2}")
        
        print(f"待平仓交易数量: {logger.get_pending_trades_count()}")
        
        # 测试平仓记录
        success1 = logger.record_trade_exit(
            trade_id=trade_id1,
            exit_price=51000.0,
            result="WIN",
            exit_reason="expired"
        )
        print(f"平仓记录1成功: {success1}")
        
        success2 = logger.record_trade_exit(
            trade_id=trade_id2,
            exit_price=2950.0,
            result="LOSS",
            exit_reason="manual_close"
        )
        print(f"平仓记录2成功: {success2}")
        
        print(f"剩余待平仓交易数量: {logger.get_pending_trades_count()}")
        
        # 读取并显示CSV内容
        print("\n=== CSV文件内容 ===")
        with open(test_file_path, 'r', encoding='utf-8') as f:
            print(f.read())
            
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)
        print("测试完成")
