#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 机器学习预测分析模块 V1.0
为统一综合日志系统提供机器学习预测和异常检测功能

核心功能：
- 交易结果预测
- 异常交易检测
- 策略性能预测
- 市场状态识别
- 风险评估模型
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import logging
from pathlib import Path
import joblib
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.cluster import KMeans
import xgboost as xgb


class PredictiveAnalytics:
    """机器学习预测分析器"""
    
    def __init__(self, comprehensive_logs_dir: str = "comprehensive_logs"):
        self.logs_dir = Path(comprehensive_logs_dir)
        self.models_dir = self.logs_dir / "ml_models"
        self.models_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(f"{__name__}.PredictiveAnalytics")
        
        # 模型存储
        self.models = {
            'trade_predictor': None,
            'anomaly_detector': None,
            'strategy_classifier': None,
            'risk_assessor': None
        }
        
        # 数据预处理器
        self.scalers = {
            'features': StandardScaler(),
            'targets': StandardScaler()
        }
        
        self.label_encoders = {}
        
        self.logger.info("机器学习预测分析器初始化完成")
    
    def load_training_data(self, days_back: int = 30) -> Optional[pd.DataFrame]:
        """加载训练数据"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            all_data = []
            
            # 加载交易数据
            trades_dir = self.logs_dir / "trades"
            if not trades_dir.exists():
                return None
            
            for year_dir in trades_dir.glob("*"):
                if not year_dir.is_dir():
                    continue
                
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue
                    
                    for csv_file in month_dir.glob("trades_*.csv"):
                        try:
                            df = pd.read_csv(csv_file)
                            if not df.empty:
                                df['entry_timestamp'] = pd.to_datetime(df['entry_timestamp'])
                                df = df[df['entry_timestamp'] >= start_date]
                                if not df.empty:
                                    all_data.append(df)
                        except Exception as e:
                            self.logger.error(f"读取文件失败 {csv_file}: {e}")
            
            if not all_data:
                return None
            
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 加载上下文数据并合并
            contexts_df = self._load_contexts_data(days_back)
            if contexts_df is not None:
                # 简单的时间匹配合并
                combined_df = self._merge_with_contexts(combined_df, contexts_df)
            
            return combined_df
            
        except Exception as e:
            self.logger.error(f"加载训练数据失败: {e}")
            return None
    
    def _load_contexts_data(self, days_back: int) -> Optional[pd.DataFrame]:
        """加载上下文数据"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            all_contexts = []
            
            contexts_dir = self.logs_dir / "contexts"
            if not contexts_dir.exists():
                return None
            
            for year_dir in contexts_dir.glob("*"):
                if not year_dir.is_dir():
                    continue
                
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue
                    
                    for csv_file in month_dir.glob("contexts_*.csv"):
                        try:
                            df = pd.read_csv(csv_file)
                            if not df.empty:
                                df['timestamp'] = pd.to_datetime(df['timestamp'])
                                df = df[df['timestamp'] >= start_date]
                                if not df.empty:
                                    all_contexts.append(df)
                        except Exception as e:
                            self.logger.error(f"读取上下文文件失败 {csv_file}: {e}")
            
            if not all_contexts:
                return None
            
            return pd.concat(all_contexts, ignore_index=True)
            
        except Exception as e:
            self.logger.error(f"加载上下文数据失败: {e}")
            return None
    
    def _merge_with_contexts(self, trades_df: pd.DataFrame, contexts_df: pd.DataFrame) -> pd.DataFrame:
        """合并交易数据和上下文数据"""
        try:
            # 简单的时间窗口匹配
            merged_data = []
            
            for _, trade in trades_df.iterrows():
                trade_time = pd.to_datetime(trade['entry_timestamp'])
                target_name = trade.get('target_name', '')
                
                # 查找匹配的上下文（5分钟窗口）
                time_window = contexts_df[
                    (contexts_df['timestamp'] >= trade_time - pd.Timedelta(minutes=5)) &
                    (contexts_df['timestamp'] <= trade_time) &
                    (contexts_df['target_name'] == target_name)
                ]
                
                if not time_window.empty:
                    # 选择时间最接近的上下文
                    closest_context = time_window.loc[time_window['timestamp'].idxmax()]
                    
                    # 合并数据
                    merged_row = trade.copy()
                    for col in closest_context.index:
                        if col not in merged_row.index:
                            merged_row[col] = closest_context[col]
                    
                    merged_data.append(merged_row)
                else:
                    merged_data.append(trade)
            
            return pd.DataFrame(merged_data)
            
        except Exception as e:
            self.logger.error(f"合并数据失败: {e}")
            return trades_df
    
    def prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备机器学习特征"""
        try:
            # 选择特征列
            feature_columns = [
                'entry_price', 'amount', 'payout_ratio',
                'entry_signal_probability', 'entry_neutral_probability', 'entry_opposite_probability',
                'direction_advantage', 'entry_atr_percent', 'entry_adx_value',
                'signal_strength', 'avg_up_prob', 'avg_down_prob',
                'atr_value', 'atr_percent', 'adx_value', 'pdi_value', 'mdi_value'
            ]
            
            # 过滤存在的列
            available_columns = [col for col in feature_columns if col in df.columns]
            
            if not available_columns:
                raise ValueError("没有可用的特征列")
            
            # 提取特征
            X = df[available_columns].copy()
            
            # 处理缺失值
            X = X.fillna(X.mean())
            
            # 添加衍生特征
            X = self._create_derived_features(X, df)
            
            # 准备目标变量
            y = (df['result'] == 'WIN').astype(int)
            
            # 标准化特征
            X_scaled = self.scalers['features'].fit_transform(X)
            
            return X_scaled, y.values, list(X.columns)
            
        except Exception as e:
            self.logger.error(f"准备特征失败: {e}")
            return np.array([]), np.array([]), []
    
    def _create_derived_features(self, X: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """创建衍生特征"""
        try:
            # 概率差异特征
            if 'avg_up_prob' in X.columns and 'avg_down_prob' in X.columns:
                X['prob_difference'] = X['avg_up_prob'] - X['avg_down_prob']
                X['prob_ratio'] = X['avg_up_prob'] / (X['avg_down_prob'] + 1e-8)
            
            # 信号强度特征
            if 'signal_strength' in X.columns and 'direction_advantage' in X.columns:
                X['signal_quality'] = X['signal_strength'] * X['direction_advantage']
            
            # 波动率特征
            if 'atr_percent' in X.columns:
                X['volatility_level'] = pd.cut(X['atr_percent'], bins=3, labels=[0, 1, 2]).astype(float)
            
            # 趋势强度特征
            if 'adx_value' in X.columns:
                X['trend_strength'] = pd.cut(X['adx_value'], bins=3, labels=[0, 1, 2]).astype(float)
            
            # 时间特征
            if 'entry_timestamp' in df.columns:
                df['entry_timestamp'] = pd.to_datetime(df['entry_timestamp'])
                X['hour'] = df['entry_timestamp'].dt.hour
                X['day_of_week'] = df['entry_timestamp'].dt.dayofweek
                X['is_weekend'] = (df['entry_timestamp'].dt.dayofweek >= 5).astype(int)
            
            # 方向编码
            if 'direction' in df.columns:
                if 'direction' not in self.label_encoders:
                    self.label_encoders['direction'] = LabelEncoder()
                    X['direction_encoded'] = self.label_encoders['direction'].fit_transform(df['direction'])
                else:
                    X['direction_encoded'] = self.label_encoders['direction'].transform(df['direction'])
            
            return X
            
        except Exception as e:
            self.logger.error(f"创建衍生特征失败: {e}")
            return X
    
    def train_trade_predictor(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练交易结果预测模型"""
        try:
            self.logger.info("开始训练交易结果预测模型...")
            
            # 准备数据
            X, y, feature_names = self.prepare_features(df)
            
            if len(X) == 0:
                return {'success': False, 'error': '没有可用的训练数据'}
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 训练XGBoost模型
            model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                eval_metric='logloss'
            )
            
            model.fit(X_train, y_train)
            
            # 评估模型
            train_score = model.score(X_train, y_train)
            test_score = model.score(X_test, y_test)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X, y, cv=5)
            
            # 预测和评估
            y_pred = model.predict(X_test)
            
            # 保存模型
            self.models['trade_predictor'] = model
            model_path = self.models_dir / "trade_predictor.joblib"
            joblib.dump({
                'model': model,
                'feature_names': feature_names,
                'scaler': self.scalers['features'],
                'label_encoders': self.label_encoders
            }, model_path)
            
            result = {
                'success': True,
                'train_score': train_score,
                'test_score': test_score,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'feature_importance': dict(zip(feature_names, model.feature_importances_)),
                'classification_report': classification_report(y_test, y_pred, output_dict=True),
                'model_path': str(model_path)
            }
            
            self.logger.info(f"交易预测模型训练完成，测试准确率: {test_score:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"训练交易预测模型失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def train_anomaly_detector(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练异常检测模型"""
        try:
            self.logger.info("开始训练异常检测模型...")
            
            # 准备数据
            X, _, feature_names = self.prepare_features(df)
            
            if len(X) == 0:
                return {'success': False, 'error': '没有可用的训练数据'}
            
            # 训练Isolation Forest模型
            model = IsolationForest(
                contamination=0.1,  # 假设10%的数据是异常
                random_state=42,
                n_estimators=100
            )
            
            model.fit(X)
            
            # 预测异常
            anomaly_scores = model.decision_function(X)
            anomalies = model.predict(X)
            
            # 统计异常
            anomaly_count = np.sum(anomalies == -1)
            anomaly_rate = anomaly_count / len(anomalies)
            
            # 保存模型
            self.models['anomaly_detector'] = model
            model_path = self.models_dir / "anomaly_detector.joblib"
            joblib.dump({
                'model': model,
                'feature_names': feature_names,
                'scaler': self.scalers['features'],
                'label_encoders': self.label_encoders
            }, model_path)
            
            result = {
                'success': True,
                'anomaly_count': int(anomaly_count),
                'anomaly_rate': float(anomaly_rate),
                'anomaly_threshold': float(np.percentile(anomaly_scores, 10)),
                'model_path': str(model_path)
            }
            
            self.logger.info(f"异常检测模型训练完成，异常率: {anomaly_rate:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"训练异常检测模型失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def train_strategy_classifier(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练策略分类模型"""
        try:
            self.logger.info("开始训练策略分类模型...")
            
            if 'target_name' not in df.columns:
                return {'success': False, 'error': '缺少策略名称列'}
            
            # 准备数据
            X, _, feature_names = self.prepare_features(df)
            
            # 准备策略标签
            if 'target_name' not in self.label_encoders:
                self.label_encoders['target_name'] = LabelEncoder()
            
            y = self.label_encoders['target_name'].fit_transform(df['target_name'])
            
            if len(X) == 0:
                return {'success': False, 'error': '没有可用的训练数据'}
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 训练随机森林模型
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
            
            model.fit(X_train, y_train)
            
            # 评估模型
            train_score = model.score(X_train, y_train)
            test_score = model.score(X_test, y_test)
            
            # 保存模型
            self.models['strategy_classifier'] = model
            model_path = self.models_dir / "strategy_classifier.joblib"
            joblib.dump({
                'model': model,
                'feature_names': feature_names,
                'scaler': self.scalers['features'],
                'label_encoders': self.label_encoders
            }, model_path)
            
            result = {
                'success': True,
                'train_score': train_score,
                'test_score': test_score,
                'n_strategies': len(self.label_encoders['target_name'].classes_),
                'strategies': list(self.label_encoders['target_name'].classes_),
                'model_path': str(model_path)
            }
            
            self.logger.info(f"策略分类模型训练完成，测试准确率: {test_score:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"训练策略分类模型失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def predict_trade_outcome(self, trade_features: Dict[str, Any]) -> Dict[str, Any]:
        """预测交易结果"""
        try:
            if self.models['trade_predictor'] is None:
                # 尝试加载模型
                model_path = self.models_dir / "trade_predictor.joblib"
                if model_path.exists():
                    model_data = joblib.load(model_path)
                    self.models['trade_predictor'] = model_data['model']
                    self.scalers['features'] = model_data['scaler']
                    self.label_encoders = model_data['label_encoders']
                else:
                    return {'success': False, 'error': '交易预测模型未训练'}
            
            # 准备特征
            feature_df = pd.DataFrame([trade_features])
            X, _, _ = self.prepare_features(feature_df)
            
            if len(X) == 0:
                return {'success': False, 'error': '特征准备失败'}
            
            # 预测
            model = self.models['trade_predictor']
            prediction = model.predict(X)[0]
            probability = model.predict_proba(X)[0]
            
            return {
                'success': True,
                'prediction': 'WIN' if prediction == 1 else 'LOSS',
                'win_probability': float(probability[1]),
                'loss_probability': float(probability[0]),
                'confidence': float(max(probability))
            }
            
        except Exception as e:
            self.logger.error(f"预测交易结果失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def detect_anomaly(self, trade_features: Dict[str, Any]) -> Dict[str, Any]:
        """检测异常交易"""
        try:
            if self.models['anomaly_detector'] is None:
                # 尝试加载模型
                model_path = self.models_dir / "anomaly_detector.joblib"
                if model_path.exists():
                    model_data = joblib.load(model_path)
                    self.models['anomaly_detector'] = model_data['model']
                    self.scalers['features'] = model_data['scaler']
                    self.label_encoders = model_data['label_encoders']
                else:
                    return {'success': False, 'error': '异常检测模型未训练'}
            
            # 准备特征
            feature_df = pd.DataFrame([trade_features])
            X, _, _ = self.prepare_features(feature_df)
            
            if len(X) == 0:
                return {'success': False, 'error': '特征准备失败'}
            
            # 检测异常
            model = self.models['anomaly_detector']
            anomaly_score = model.decision_function(X)[0]
            is_anomaly = model.predict(X)[0] == -1
            
            return {
                'success': True,
                'is_anomaly': bool(is_anomaly),
                'anomaly_score': float(anomaly_score),
                'risk_level': 'HIGH' if is_anomaly else 'NORMAL'
            }
            
        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def train_all_models(self, days_back: int = 30) -> Dict[str, Any]:
        """训练所有模型"""
        try:
            self.logger.info("开始训练所有机器学习模型...")
            
            # 加载训练数据
            df = self.load_training_data(days_back)
            
            if df is None or df.empty:
                return {'success': False, 'error': '没有可用的训练数据'}
            
            results = {}
            
            # 训练交易预测模型
            results['trade_predictor'] = self.train_trade_predictor(df)
            
            # 训练异常检测模型
            results['anomaly_detector'] = self.train_anomaly_detector(df)
            
            # 训练策略分类模型
            results['strategy_classifier'] = self.train_strategy_classifier(df)
            
            # 统计结果
            success_count = sum(1 for result in results.values() if result.get('success', False))
            
            return {
                'success': success_count > 0,
                'models_trained': success_count,
                'total_models': len(results),
                'results': results,
                'training_data_size': len(df)
            }
            
        except Exception as e:
            self.logger.error(f"训练所有模型失败: {e}")
            return {'success': False, 'error': str(e)}
