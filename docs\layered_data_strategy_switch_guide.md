# 分层数据策略开关使用指南

## 📋 概述

分层数据策略现在配备了一个全局开关，让您可以灵活控制是否启用这个功能。这个开关提供了完全的向后兼容性，确保您可以在新策略和传统方式之间自由切换。

## 🔧 开关配置

### 配置位置

在 `config.py` 中找到以下配置：

```python
# === 分层数据策略配置 ===
ENABLE_LAYERED_DATA_STRATEGY = True  # [布尔] 是否启用分层数据策略
DATA_FETCH_LIMIT = 7777              # 短期数据条数
LONG_TERM_DATA_LIMIT = 22222         # 长期数据条数  
MTFA_DATA_LIMIT = 2000               # MTFA数据条数
```

### 开关选项

- **`True`** (默认)：启用分层数据策略
  - 使用短期数据(7777)进行训练
  - 使用长期数据(22222)计算样本权重
  - MTFA特征使用独立数据限制(2000)

- **`False`**：禁用分层数据策略
  - 回退到传统单一数据获取方式
  - 所有功能使用DATA_FETCH_LIMIT(7777)
  - MTFA特征使用传统限制(1000)

## 🚀 使用方法

### 启用分层数据策略

```python
# 在 config.py 中设置
ENABLE_LAYERED_DATA_STRATEGY = True
```

**效果：**
- ✅ 使用7777条短期数据进行模型训练
- ✅ 使用22222条长期数据计算样本权重
- ✅ MTFA特征使用2000条独立数据
- ✅ 智能回退机制保证稳定性

### 禁用分层数据策略

```python
# 在 config.py 中设置
ENABLE_LAYERED_DATA_STRATEGY = False
```

**效果：**
- 📋 使用传统单一数据获取方式
- 📋 所有功能使用DATA_FETCH_LIMIT配置
- 📋 MTFA特征使用1000条传统限制
- 📋 完全向后兼容现有流程

## 📊 运行时行为

### 启用状态下的日志

```
[LayeredDataStrategy] 开关状态: ✅ 启用
[LayeredDataStrategy] 配置 - 短期:7777, 长期:22222, MTFA:2000
🎯 分层数据策略已启用，开始分层数据获取...
✅ 分层数据策略成功，使用短期数据进行训练: 7777 条
📊 长期上下文数据可用: 22222 条
🔄 MTFA上下文数据可用: 2000 条
⚖️ 分层数据增强样本权重已启用，开始计算...
✅ 分层数据样本权重计算成功，权重范围: [0.1234, 2.5678]
```

### 禁用状态下的日志

```
[LayeredDataStrategy] 开关状态: ❌ 禁用
[LayeredDataStrategy] 将使用传统单一数据获取方式
[LayeredDataStrategy] 传统配置 - 数据量:7777
📋 分层数据策略已禁用，使用传统数据获取方式...
📋 分层数据策略已禁用，使用标准增强样本权重...
⚖️ 标准增强样本权重计算成功，权重范围: [0.1234, 2.5678]
```

## 🔄 智能回退机制

无论开关状态如何，系统都具备完善的回退机制：

### 分层策略启用时的回退

```python
try:
    # 尝试分层数据策略
    layered_data = manager.fetch_layered_data()
    if layered_data.get('short_term') is None:
        # 自动回退到传统方式
        df_raw = data_utils.fetch_binance_history(...)
except Exception:
    # 异常时回退到传统方式
    df_raw = data_utils.fetch_binance_history(...)
```

### 样本权重计算的回退

```python
if enable_layered_strategy:
    # 尝试分层数据权重计算
    weights = layered_manager.calculate_enhanced_sample_weights(...)
    if weights is None:
        # 回退到标准增强权重
        weights = calculate_enhanced_sample_weights(...)
        if weights is None:
            # 最终回退到传统权重
            weights = _calculate_historical_sample_weights(...)
```

## 🧪 测试验证

运行测试脚本验证开关功能：

```bash
python test_layered_data_strategy.py
```

新增的开关测试包括：
- 开关配置正确性验证
- 启用/禁用状态行为测试
- 配置恢复机制测试

## 📈 使用场景

### 何时启用分层数据策略

✅ **推荐启用的情况：**
- 追求更高的模型性能
- 有充足的计算资源和网络带宽
- 希望利用长期市场数据的上下文信息
- 需要更稳定的MTFA特征

### 何时禁用分层数据策略

📋 **推荐禁用的情况：**
- 计算资源有限
- 网络带宽受限
- 追求最快的训练速度
- 调试或测试阶段
- 与现有系统保持完全一致

## ⚙️ 高级配置

### 动态开关控制

您可以在运行时动态修改开关：

```python
import config

# 动态启用
config.ENABLE_LAYERED_DATA_STRATEGY = True

# 动态禁用  
config.ENABLE_LAYERED_DATA_STRATEGY = False
```

### 条件性启用

根据特定条件启用分层策略：

```python
# 根据目标类型启用
if target_config.get('model_type') == 'LSTM':
    config.ENABLE_LAYERED_DATA_STRATEGY = True
else:
    config.ENABLE_LAYERED_DATA_STRATEGY = False

# 根据系统资源启用
import psutil
if psutil.virtual_memory().available > 8 * 1024**3:  # 8GB可用内存
    config.ENABLE_LAYERED_DATA_STRATEGY = True
```

## 🔍 监控和调试

### 检查当前开关状态

```python
import config
enable_status = getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True)
print(f"分层数据策略状态: {'启用' if enable_status else '禁用'}")
```

### 性能对比

启用和禁用分层策略时的性能对比：

| 指标 | 启用分层策略 | 禁用分层策略 |
|------|-------------|-------------|
| 训练数据量 | 7777条 | 7777条 |
| 权重计算数据量 | 22222条 | 7777条 |
| MTFA数据量 | 2000条 | 1000条 |
| 内存使用 | 较高 | 较低 |
| 训练时间 | 略长 | 较短 |
| 模型性能 | 更好 | 标准 |

## ⚠️ 注意事项

1. **配置一致性**：确保开关状态与您的预期一致
2. **资源监控**：启用时注意监控内存和网络使用
3. **日志观察**：通过日志确认实际使用的策略
4. **测试验证**：切换开关后进行充分测试
5. **备份配置**：修改前备份原始配置

## 🎯 最佳实践

### 开发阶段

```python
# 开发和调试时禁用，提高迭代速度
ENABLE_LAYERED_DATA_STRATEGY = False
```

### 生产环境

```python
# 生产环境启用，获得最佳性能
ENABLE_LAYERED_DATA_STRATEGY = True
```

### A/B测试

```python
# 可以轻松进行A/B测试对比
if experiment_group == 'A':
    ENABLE_LAYERED_DATA_STRATEGY = True
else:
    ENABLE_LAYERED_DATA_STRATEGY = False
```

## 📝 总结

分层数据策略开关提供了：

- ✅ **灵活控制**：一键启用/禁用分层策略
- ✅ **向后兼容**：禁用时完全回退到传统方式
- ✅ **智能回退**：启用时具备多层回退机制
- ✅ **运行时可见**：清晰的日志显示当前状态
- ✅ **零风险切换**：随时可以安全地切换状态

这个开关让您可以根据具体需求和环境条件，灵活选择最适合的数据获取策略，真正实现了"可控的智能优化"！🎛️
