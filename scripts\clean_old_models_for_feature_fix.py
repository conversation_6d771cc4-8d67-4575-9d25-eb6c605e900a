#!/usr/bin/env python3
"""
清理旧模型文件以修复特征命名不匹配问题
删除包含旧特征列表的模型文件，强制重新训练
"""

import os
import sys
import shutil
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_model_directories():
    """查找所有模型目录"""
    logger.info("🔍 查找所有模型目录...")
    
    model_dirs = []
    
    # 常见的模型目录模式
    patterns = [
        "trained_models_*",
        "models/*",
        "*_models",
        "model_*"
    ]
    
    for pattern in patterns:
        for path in Path(".").glob(pattern):
            if path.is_dir():
                model_dirs.append(str(path))
    
    # 手动添加已知的模型目录
    known_dirs = [
        "trained_models_btc_15m_up",
        "trained_models_btc_15m_down",
        "meta_model_data",
        "models"
    ]
    
    for dir_name in known_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            if dir_name not in model_dirs:
                model_dirs.append(dir_name)
    
    logger.info(f"发现 {len(model_dirs)} 个模型目录:")
    for dir_path in model_dirs:
        logger.info(f"  📁 {dir_path}")
    
    return model_dirs

def check_features_json_for_old_format(features_file):
    """检查features.json文件是否包含旧格式特征"""
    try:
        with open(features_file, 'r', encoding='utf-8') as f:
            features = json.load(f)
        
        # 检查是否包含_x_格式的特征
        old_format_features = [f for f in features if '_x_high_certainty' in f or '_x_' in f]
        
        return len(old_format_features) > 0, old_format_features
        
    except Exception as e:
        logger.warning(f"读取特征文件失败 {features_file}: {e}")
        return False, []

def analyze_model_directories(model_dirs):
    """分析模型目录，找出需要清理的"""
    logger.info("🔍 分析模型目录，查找包含旧特征格式的模型...")
    
    dirs_to_clean = []
    
    for model_dir in model_dirs:
        logger.info(f"\n📁 检查目录: {model_dir}")
        
        # 查找features.json文件
        features_files = []
        for root, dirs, files in os.walk(model_dir):
            for file in files:
                if file == 'features.json':
                    features_files.append(os.path.join(root, file))
        
        if not features_files:
            logger.info(f"  ℹ️ 未找到features.json文件")
            continue
        
        # 检查每个features.json文件
        has_old_format = False
        old_features_found = []
        
        for features_file in features_files:
            has_old, old_features = check_features_json_for_old_format(features_file)
            if has_old:
                has_old_format = True
                old_features_found.extend(old_features)
                logger.info(f"  ❌ 发现旧格式特征: {features_file}")
                for feature in old_features[:3]:  # 只显示前3个
                    logger.info(f"    - {feature}")
                if len(old_features) > 3:
                    logger.info(f"    - ... 还有 {len(old_features) - 3} 个")
        
        if has_old_format:
            dirs_to_clean.append({
                'path': model_dir,
                'features_files': features_files,
                'old_features_count': len(old_features_found)
            })
            logger.info(f"  🎯 标记为需要清理")
        else:
            logger.info(f"  ✅ 特征格式正确")
    
    return dirs_to_clean

def backup_important_files(model_dir):
    """备份重要文件"""
    logger.info(f"💾 备份重要文件: {model_dir}")
    
    backup_dir = f"{model_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # 创建备份目录
        os.makedirs(backup_dir, exist_ok=True)
        
        # 备份配置文件和重要元数据
        important_files = [
            'config.json',
            'training_config.json', 
            'model_metadata.json',
            'training_results.json'
        ]
        
        backed_up_files = []
        for root, dirs, files in os.walk(model_dir):
            for file in files:
                if any(important in file for important in important_files):
                    src_path = os.path.join(root, file)
                    rel_path = os.path.relpath(src_path, model_dir)
                    dst_path = os.path.join(backup_dir, rel_path)
                    
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                    shutil.copy2(src_path, dst_path)
                    backed_up_files.append(rel_path)
        
        if backed_up_files:
            logger.info(f"  ✅ 已备份 {len(backed_up_files)} 个重要文件到: {backup_dir}")
            return backup_dir
        else:
            # 如果没有重要文件，删除空的备份目录
            os.rmdir(backup_dir)
            logger.info(f"  ℹ️ 无重要文件需要备份")
            return None
            
    except Exception as e:
        logger.error(f"  ❌ 备份失败: {e}")
        return None

def clean_model_directory(dir_info, dry_run=True):
    """清理模型目录"""
    model_dir = dir_info['path']
    
    if dry_run:
        logger.info(f"🧪 [模拟] 清理目录: {model_dir}")
    else:
        logger.info(f"🗑️ 清理目录: {model_dir}")
    
    try:
        if not dry_run:
            # 备份重要文件
            backup_dir = backup_important_files(model_dir)
        
        # 查找需要删除的文件
        files_to_delete = []
        for root, dirs, files in os.walk(model_dir):
            for file in files:
                file_path = os.path.join(root, file)
                # 删除模型文件、特征文件、缓存文件
                if any(ext in file for ext in ['.joblib', '.pkl', 'features.json', '.h5']):
                    files_to_delete.append(file_path)
        
        logger.info(f"  📋 将删除 {len(files_to_delete)} 个文件")
        
        if not dry_run:
            # 实际删除文件
            deleted_count = 0
            for file_path in files_to_delete:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    logger.warning(f"    删除文件失败 {file_path}: {e}")
            
            logger.info(f"  ✅ 成功删除 {deleted_count} 个文件")
        else:
            logger.info(f"  🧪 [模拟] 将删除的文件类型: .joblib, .pkl, features.json, .h5")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 清理失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始清理旧模型文件以修复特征命名不匹配问题...")
    
    try:
        # 1. 查找模型目录
        model_dirs = find_model_directories()
        
        if not model_dirs:
            logger.warning("⚠️ 未找到任何模型目录")
            return False
        
        # 2. 分析需要清理的目录
        dirs_to_clean = analyze_model_directories(model_dirs)
        
        if not dirs_to_clean:
            logger.info("✅ 所有模型目录的特征格式都是正确的，无需清理")
            return True
        
        logger.info(f"\n🎯 发现 {len(dirs_to_clean)} 个目录需要清理:")
        for dir_info in dirs_to_clean:
            logger.info(f"  📁 {dir_info['path']} (包含 {dir_info['old_features_count']} 个旧格式特征)")
        
        # 3. 询问用户确认（模拟模式）
        logger.info("\n🧪 首先进行模拟清理...")
        for dir_info in dirs_to_clean:
            clean_model_directory(dir_info, dry_run=True)
        
        # 4. 实际清理
        logger.info("\n🗑️ 开始实际清理...")
        success_count = 0
        
        for dir_info in dirs_to_clean:
            if clean_model_directory(dir_info, dry_run=False):
                success_count += 1
        
        logger.info(f"\n🎉 清理完成！成功清理 {success_count}/{len(dirs_to_clean)} 个目录")
        
        if success_count == len(dirs_to_clean):
            logger.info("📋 下一步操作:")
            logger.info("  1. 重新训练基础模型（UP/DOWN模型）")
            logger.info("  2. 重新训练元模型")
            logger.info("  3. 验证不再出现特征缺失错误")
            return True
        else:
            logger.warning("⚠️ 部分目录清理失败，请检查上述错误")
            return False
        
    except Exception as e:
        logger.error(f"❌ 清理过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 旧模型文件清理成功！")
        print("🚀 现在可以重新训练模型，特征命名问题将得到解决")
    else:
        print("\n❌ 旧模型文件清理失败！")
