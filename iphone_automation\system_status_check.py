#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone7自动交易系统状态检查
验证所有组件是否正常工作
"""

import requests
import paramiko
import time
import socket
from datetime import datetime

def check_simulator_server():
    """检查模拟盘服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:5008", timeout=5)
        print("✅ 模拟盘服务器运行正常 (端口5008)")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ 模拟盘服务器未运行 (端口5008)")
        return False
    except Exception as e:
        print(f"❌ 模拟盘服务器检查失败: {e}")
        return False

def check_iphone_ssh():
    """检查iPhone SSH连接"""
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect('**************', username='mobile', password='sdzddhy', timeout=10)
        
        # 检查ZXTouch
        stdin, stdout, stderr = ssh.exec_command('ls -la /var/jb/usr/bin/zxtouchb', timeout=10)
        result = stdout.read().decode().strip()
        if result:
            print("✅ iPhone7 SSH连接正常")
            print("✅ ZXTouch命令可用")
        else:
            print("❌ ZXTouch命令不存在")
            ssh.close()
            return False
        
        # 检查Python 3.9
        stdin, stdout, stderr = ssh.exec_command('python3.9 --version', timeout=10)
        result = stdout.read().decode().strip()
        if result:
            print(f"✅ Python 3.9可用: {result}")
        else:
            print("❌ Python 3.9不可用")
            ssh.close()
            return False
        
        # 检查ZXTouch模块
        stdin, stdout, stderr = ssh.exec_command(
            'python3.9 -c "import sys; sys.path.insert(0, \'/var/jb/usr/lib/python3.9/site-packages\'); from zxtouch.client import zxtouch; print(\'ZXTouch模块正常\')"',
            timeout=10
        )
        result = stdout.read().decode().strip()
        if "ZXTouch模块正常" in result:
            print("✅ ZXTouch Python模块正常")
        else:
            print("❌ ZXTouch Python模块异常")
            ssh.close()
            return False
        
        ssh.close()
        return True
        
    except Exception as e:
        print(f"❌ iPhone SSH连接失败: {e}")
        return False

def test_signal_sending():
    """测试信号发送"""
    try:
        url = "http://127.0.0.1:5008/signal"
        signal_data = {
            "signal_type": "UP",
            "amount": 5,
            "target_name": "SYSTEM_CHECK",
            "timestamp": datetime.now().isoformat(),
            "test_mode": True,
            "symbol": "BTCUSDT"
        }
        
        response = requests.post(url, json=signal_data, timeout=10)
        if response.status_code == 200:
            print("✅ 信号发送测试成功")
            return True
        else:
            print(f"❌ 信号发送失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 信号发送测试失败: {e}")
        return False

def test_direct_iphone_automation():
    """测试直接iPhone自动化"""
    try:
        import sys
        import os
        
        # 添加路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        from ssh_zxtouch_trader import execute_binance_trade
        
        print("🚀 测试直接iPhone自动化...")
        success = execute_binance_trade("UP", 5)
        
        if success:
            print("✅ 直接iPhone自动化测试成功")
            return True
        else:
            print("❌ 直接iPhone自动化测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 直接iPhone自动化测试异常: {e}")
        return False

def check_network_connectivity():
    """检查网络连通性"""
    try:
        # 检查iPhone连通性
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('**************', 22))
        sock.close()
        
        if result == 0:
            print("✅ iPhone7网络连通性正常")
            return True
        else:
            print("❌ iPhone7网络连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 网络连通性检查失败: {e}")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("📱 iPhone7自动交易系统状态检查")
    print("=" * 60)
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = {}
    
    # 1. 检查网络连通性
    print("🌐 检查网络连通性...")
    results['network'] = check_network_connectivity()
    print()
    
    # 2. 检查模拟盘服务器
    print("🖥️ 检查模拟盘服务器...")
    results['simulator'] = check_simulator_server()
    print()
    
    # 3. 检查iPhone SSH
    print("📱 检查iPhone7 SSH连接...")
    results['iphone_ssh'] = check_iphone_ssh()
    print()
    
    # 4. 测试信号发送
    if results['simulator']:
        print("📡 测试信号发送...")
        results['signal'] = test_signal_sending()
        print()
    else:
        results['signal'] = False
        print("⏭️ 跳过信号发送测试 (模拟盘服务器未运行)")
        print()
    
    # 5. 测试直接iPhone自动化
    if results['iphone_ssh']:
        print("🤖 测试直接iPhone自动化...")
        results['automation'] = test_direct_iphone_automation()
        print()
    else:
        results['automation'] = False
        print("⏭️ 跳过iPhone自动化测试 (SSH连接失败)")
        print()
    
    # 总结
    print("=" * 60)
    print("📊 系统状态总结")
    print("=" * 60)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        component_name = {
            'network': '网络连通性',
            'simulator': '模拟盘服务器',
            'iphone_ssh': 'iPhone SSH',
            'signal': '信号发送',
            'automation': 'iPhone自动化'
        }.get(component, component)
        
        print(f"{status_icon} {component_name}: {'正常' if status else '异常'}")
    
    print()
    print(f"📈 系统健康度: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
    
    if passed_checks == total_checks:
        print("🎉 系统完全正常，可以开始自动交易！")
    elif passed_checks >= total_checks * 0.8:
        print("⚠️ 系统基本正常，但有部分组件需要检查")
    else:
        print("❌ 系统存在严重问题，需要修复后再使用")
    
    print()
    print("💡 使用建议:")
    if not results.get('simulator', False):
        print("   - 启动模拟盘: python SimMain.py --port 5008")
    if not results.get('iphone_ssh', False):
        print("   - 检查iPhone7网络连接和SSH服务")
    if results.get('simulator', False) and results.get('iphone_ssh', False):
        print("   - 系统就绪，可以发送交易信号测试")
        print("   - 测试命令: python iphone_automation/test_signal_sender.py UP 10")

if __name__ == "__main__":
    main()
