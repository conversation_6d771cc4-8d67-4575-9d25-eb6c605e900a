# 数据泄露修复后重新训练指导

## 🎯 概述

在修复数据泄露问题后，需要重新训练所有模型以获得真实的性能基线。本指导提供详细的重新训练步骤和注意事项。

## 📋 重新训练前的准备工作

### 1. 验证修复效果

```bash
# 运行综合数据泄露测试
python scripts/comprehensive_data_leakage_test.py

# 运行基础验证脚本
python scripts/validate_data_leakage_fix.py
```

### 2. 备份现有模型

```bash
# 创建备份目录
mkdir -p models_backup_before_leakage_fix

# 备份现有模型
cp -r models/* models_backup_before_leakage_fix/

# 备份性能记录
cp -r logs/training_logs models_backup_before_leakage_fix/
```

### 3. 清理缓存和临时文件

```bash
# 清理特征缓存
rm -rf cache/features/*

# 清理模型缓存
rm -rf cache/models/*

# 清理预测缓存
rm -rf cache/predictions/*
```

## 🚀 重新训练步骤

### 第一阶段：基础模型重新训练

#### 1. 单个目标重新训练

```python
# 示例：重新训练BTC_15m_UP模型
from src.training.training_pipeline import run_training_pipeline
from config import get_target_config_wrapper

# 获取目标配置
target_config = get_target_config_wrapper('BTC_15m_UP')

# 重新训练
results = run_training_pipeline(
    target_config=target_config,
    force_retrain=True,  # 强制重新训练
    clear_cache=True,    # 清理缓存
    validate_no_leakage=True  # 启用数据泄露验证
)

print(f"重新训练结果: {results}")
```

#### 2. 批量重新训练

```python
# 重新训练所有基础模型
from config import PREDICTION_TARGETS

for target_name in PREDICTION_TARGETS.keys():
    print(f"🔄 重新训练 {target_name}...")
    
    target_config = get_target_config_wrapper(target_name)
    
    try:
        results = run_training_pipeline(
            target_config=target_config,
            force_retrain=True,
            clear_cache=True,
            validate_no_leakage=True
        )
        
        print(f"✅ {target_name} 重新训练完成")
        
    except Exception as e:
        print(f"❌ {target_name} 重新训练失败: {e}")
```

### 第二阶段：元模型重新训练

```python
# 重新训练元模型
from src.training.elite_meta_model import train_elite_meta_model

# 等待所有基础模型训练完成后
meta_results = train_elite_meta_model(
    force_retrain=True,
    validate_base_models=True,  # 验证基础模型
    validate_no_leakage=True    # 验证无数据泄露
)

print(f"元模型重新训练结果: {meta_results}")
```

## 📊 性能对比分析

### 1. 性能下降预期

修复数据泄露后，预期会看到以下变化：

```python
# 预期的性能变化
expected_changes = {
    'accuracy': '可能下降 5-15%',
    'precision': '可能下降 10-20%',
    'recall': '可能下降 5-15%',
    'f1_score': '可能下降 10-20%',
    'auc_roc': '可能下降 5-10%'
}
```

### 2. 性能对比脚本

```python
import pandas as pd
import json

def compare_model_performance(old_results_path, new_results_path):
    """对比修复前后的模型性能"""
    
    # 加载结果
    with open(old_results_path, 'r') as f:
        old_results = json.load(f)
    
    with open(new_results_path, 'r') as f:
        new_results = json.load(f)
    
    # 对比分析
    comparison = []
    
    for target_name in old_results.keys():
        if target_name in new_results:
            old_metrics = old_results[target_name]['test_metrics']
            new_metrics = new_results[target_name]['test_metrics']
            
            for metric in ['accuracy', 'precision', 'recall', 'f1']:
                old_val = old_metrics.get(metric, 0)
                new_val = new_metrics.get(metric, 0)
                change = new_val - old_val
                change_pct = (change / old_val * 100) if old_val > 0 else 0
                
                comparison.append({
                    'target': target_name,
                    'metric': metric,
                    'old_value': old_val,
                    'new_value': new_val,
                    'change': change,
                    'change_pct': change_pct
                })
    
    return pd.DataFrame(comparison)

# 使用示例
comparison_df = compare_model_performance(
    'models_backup_before_leakage_fix/performance_summary.json',
    'models/performance_summary.json'
)

print(comparison_df.groupby('metric')['change_pct'].describe())
```

## ⚠️ 重要注意事项

### 1. 性能下降是正常的

- **不要惊慌**：性能下降表明修复成功
- **真实性能**：新的性能指标更接近实际交易表现
- **可靠性提升**：虽然指标下降，但模型更可靠

### 2. 超参数重新调优

```python
# 重新进行超参数优化
from src.training.hyperparameter_optimization import optimize_hyperparameters

for target_name in PREDICTION_TARGETS.keys():
    print(f"🔧 重新优化 {target_name} 超参数...")
    
    best_params = optimize_hyperparameters(
        target_name=target_name,
        n_trials=100,  # 增加试验次数
        timeout=3600,  # 1小时超时
        validate_no_leakage=True
    )
    
    print(f"✅ {target_name} 最佳参数: {best_params}")
```

### 3. 特征重要性重新评估

```python
# 重新评估特征重要性
from src.analysis.feature_importance_analyzer import analyze_feature_importance

for target_name in PREDICTION_TARGETS.keys():
    importance_results = analyze_feature_importance(
        target_name=target_name,
        method='permutation',  # 使用置换重要性
        validate_no_leakage=True
    )
    
    # 保存结果
    importance_results.to_csv(f'analysis/feature_importance_{target_name}_post_fix.csv')
```

## 🔍 验证和监控

### 1. 持续验证

```python
# 定期运行数据泄露检查
import schedule
import time

def daily_leakage_check():
    """每日数据泄露检查"""
    result = os.system('python scripts/validate_data_leakage_fix.py')
    if result != 0:
        print("⚠️  发现潜在数据泄露问题！")
        # 发送警报
        send_alert("数据泄露检查失败")

# 每天运行检查
schedule.every().day.at("02:00").do(daily_leakage_check)

while True:
    schedule.run_pending()
    time.sleep(3600)  # 每小时检查一次调度
```

### 2. 性能监控

```python
# 监控模型性能稳定性
def monitor_model_performance():
    """监控模型性能"""
    
    # 获取最近的预测结果
    recent_predictions = load_recent_predictions()
    
    # 计算性能指标
    current_metrics = calculate_current_metrics(recent_predictions)
    
    # 与基线对比
    baseline_metrics = load_baseline_metrics()
    
    for metric, current_val in current_metrics.items():
        baseline_val = baseline_metrics.get(metric, 0)
        
        if current_val < baseline_val * 0.8:  # 性能下降超过20%
            print(f"⚠️  {metric} 性能显著下降: {current_val:.3f} vs {baseline_val:.3f}")
```

## 📈 后续优化建议

### 1. 特征工程改进

- **增加更多历史特征**：补偿信息损失
- **优化特征组合**：寻找新的有效特征组合
- **时间窗口调整**：优化技术指标的时间窗口

### 2. 模型架构优化

- **尝试新的模型架构**：如Transformer、LSTM等
- **集成学习改进**：优化模型集成策略
- **在线学习**：考虑增量学习方法

### 3. 数据质量提升

- **数据清洗改进**：更严格的数据质量控制
- **异常值处理**：改进异常值检测和处理
- **数据增强**：考虑合理的数据增强技术

## 🎯 成功标准

重新训练成功的标准：

1. **✅ 所有数据泄露测试通过**
2. **✅ 模型性能在合理范围内**（准确率 > 0.52，精确率 > 0.55）
3. **✅ 性能指标稳定**（方差 < 0.05）
4. **✅ 特征重要性合理**（没有异常高的重要性）
5. **✅ 时间序列交叉验证一致**（各折性能差异 < 10%）

## 📞 支持和故障排除

如果在重新训练过程中遇到问题：

1. **检查日志文件**：`logs/training_logs/`
2. **运行诊断脚本**：`python scripts/training_diagnostics.py`
3. **验证数据完整性**：`python scripts/data_integrity_check.py`
4. **检查资源使用**：确保有足够的内存和存储空间

记住：数据泄露修复后的性能下降是正常的，这表明我们的模型现在更加诚实和可靠！
