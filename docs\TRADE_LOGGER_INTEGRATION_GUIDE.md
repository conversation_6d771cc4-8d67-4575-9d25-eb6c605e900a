# TradeLogger 集成指南

## 概述

TradeLogger 是一个健壮的交易日志记录系统，用于记录加密货币交易系统的开仓、平仓和盈亏结果到CSV文件中。

## 核心特性

- **结构化CSV存储**: 每笔完整交易占一行，包含所有关键信息
- **线程安全**: 使用 `threading.Lock` 保护文件写入操作
- **两阶段记录**: 开仓时暂存信息，平仓时完成记录
- **自动盈亏计算**: 根据交易结果和赔率自动计算盈亏
- **唯一交易ID**: 基于目标名称和时间戳生成唯一ID

## CSV数据字段

| 字段名 | 描述 | 示例 |
|--------|------|------|
| trade_id | 交易唯一ID | BTC_15m_UP_1751704295500 |
| entry_timestamp | 开仓时间(ISO 8601) | 2025-07-05T16:31:35.500720 |
| exit_timestamp | 平仓时间(ISO 8601) | 2025-07-05T16:31:35.501719 |
| target_name | 策略/模型名称 | BTC_15m_UP, MetaModel |
| symbol | 交易对 | BTCUSDT, ETHUSDT |
| direction | 交易方向 | LONG, SHORT |
| entry_price | 开仓价格 | 50000.0 |
| exit_price | 平仓价格 | 51000.0 |
| amount | 投入金额 | 10.0 |
| payout_ratio | 赔率 | 0.85 |
| result | 交易结果 | WIN, LOSS |
| profit_loss | 盈亏金额 | 8.5, -10.0 |
| exit_reason | 平仓原因 | expired, manual_close |

## 基本使用方法

### 1. 初始化

```python
from src.core.trade_logger import TradeLogger, get_trade_logger

# 方法1: 直接创建实例
trade_logger = TradeLogger("results/logs/trade_log.csv")

# 方法2: 使用全局单例（推荐）
trade_logger = get_trade_logger("results/logs/trade_log.csv")
```

### 2. 记录开仓

```python
trade_id = trade_logger.record_trade_entry(
    target_name="BTC_15m_UP",
    symbol="BTCUSDT",
    direction="LONG",  # 或 "SHORT", "UP", "DOWN"
    entry_price=50000.0,
    amount=10.0,
    payout_ratio=0.85
)
```

### 3. 记录平仓

```python
success = trade_logger.record_trade_exit(
    trade_id=trade_id,
    exit_price=51000.0,
    result="WIN",  # 或 "LOSS"
    exit_reason="expired"  # 或 "manual_close"
)
```

## 集成方案

### 方案1: 在 prediction.py 中集成

在预测系统发送信号时记录开仓，收到交易结果时记录平仓：

```python
# 在 prediction.py 的信号发送部分
from src.core.trade_logger import get_trade_logger

def run_prediction_cycle_for_target(target_config_static, binance_client, ...):
    # ... 现有预测逻辑 ...
    
    if should_send_signal_this_time:
        trade_logger = get_trade_logger()
        
        # 记录开仓
        trade_id = trade_logger.record_trade_entry(
            target_name=target_name,
            symbol=symbol_to_use,
            direction=actual_signal_for_sound_and_simulator,
            entry_price=current_price,
            amount=trade_amount_to_send_float,
            payout_ratio=0.85
        )
        
        # 发送信号给模拟盘
        _notify_simulator(...)
        
        # 将trade_id存储起来，用于后续平仓记录
        # 可以存储在全局状态管理器中
```

### 方案2: 在 SimTrading.py 中集成

在交易引擎的开仓和平仓方法中集成：

```python
# 在 SimTrading.py 的 Trade 类中
from src.core.trade_logger import get_trade_logger

class Trade:
    def __init__(self, direction, entry_price, amount_staked, target_name="SimTrading", symbol="BTCUSDT"):
        # ... 现有初始化代码 ...
        
        # 集成TradeLogger
        self.trade_logger = get_trade_logger()
        self.logger_trade_id = self.trade_logger.record_trade_entry(
            target_name=target_name,
            symbol=symbol,
            direction=direction,
            entry_price=entry_price,
            amount=amount_staked,
            payout_ratio=self.PAYOUT_RATIO
        )
    
    def check_expiry(self, current_market_price):
        # ... 现有到期检查逻辑 ...
        
        if self.status in ["WON", "LOST"]:
            # 记录平仓
            result = "WIN" if self.status == "WON" else "LOSS"
            self.trade_logger.record_trade_exit(
                trade_id=self.logger_trade_id,
                exit_price=self.exit_price,
                result=result,
                exit_reason="expired"
            )
```

### 方案3: 在 SimMain.py 中集成

在模拟盘主程序中集成，监听交易状态变化：

```python
# 在 SimMain.py 中
from src.core.trade_logger import get_trade_logger

class SimApplication:
    def __init__(self, ...):
        # ... 现有初始化代码 ...
        self.trade_logger = get_trade_logger("results/logs/sim_trade_log.csv")
        self.active_trade_ids = {}  # 存储活跃交易的logger_trade_id
    
    def _check_and_process_external_signals(self):
        # ... 现有信号处理逻辑 ...
        
        if signal_data and should_execute_trade:
            # 记录开仓
            logger_trade_id = self.trade_logger.record_trade_entry(
                target_name=target_name_from_signal,
                symbol=signal_symbol_from_payload,
                direction=signal_type,
                entry_price=current_price,
                amount=final_bet_amount_for_sim_int,
                payout_ratio=0.85
            )
            
            # 开仓
            trade = self.trading_engine.open_trade(signal_type, final_bet_amount_for_sim_int)
            if trade:
                self.active_trade_ids[trade.trade_id] = logger_trade_id
    
    def _process_trades(self):
        # ... 现有交易处理逻辑 ...
        
        # 检查已结算的交易
        for trade in self.trading_engine.trade_history:
            if trade.trade_id in self.active_trade_ids:
                logger_trade_id = self.active_trade_ids[trade.trade_id]
                
                # 记录平仓
                result = "WIN" if trade.status == "WON" else "LOSS"
                self.trade_logger.record_trade_exit(
                    trade_id=logger_trade_id,
                    exit_price=trade.exit_price,
                    result=result,
                    exit_reason="expired"
                )
                
                # 清理记录
                del self.active_trade_ids[trade.trade_id]
```

## 配置建议

### 1. 日志文件路径配置

在 `config.py` 中添加配置：

```python
# 交易日志配置
TRADE_LOG_FILE_PATH = "results/logs/trade_log.csv"
TRADE_LOG_ENABLED = True
```

### 2. 日志轮转

对于长期运行的系统，建议实现日志轮转：

```python
from datetime import datetime

def get_daily_log_path():
    today = datetime.now().strftime("%Y%m%d")
    return f"results/logs/trade_log_{today}.csv"

trade_logger = get_trade_logger(get_daily_log_path())
```

## 监控和分析

### 1. 实时监控

```python
# 获取待平仓交易数量
pending_count = trade_logger.get_pending_trades_count()

# 获取待平仓交易ID列表
pending_ids = trade_logger.get_pending_trade_ids()
```

### 2. 数据分析

生成的CSV文件可以直接用于数据分析：

```python
import pandas as pd

# 读取交易日志
df = pd.read_csv("results/logs/trade_log.csv")

# 计算胜率
win_rate = (df['result'] == 'WIN').mean()

# 计算总盈亏
total_pnl = df['profit_loss'].sum()

# 按策略分析
strategy_stats = df.groupby('target_name').agg({
    'profit_loss': ['sum', 'mean'],
    'result': lambda x: (x == 'WIN').mean()
})
```

## 注意事项

1. **线程安全**: TradeLogger 使用锁保护文件操作，可以在多线程环境中安全使用
2. **错误处理**: 记录失败时会返回 False，建议检查返回值
3. **内存管理**: 待平仓交易存储在内存中，长期运行时注意监控内存使用
4. **文件权限**: 确保程序有写入日志文件的权限
5. **备份策略**: 建议定期备份重要的交易日志文件

## 故障排除

### 常见问题

1. **文件写入失败**: 检查目录权限和磁盘空间
2. **交易ID重复**: 确保系统时间正确，避免时钟回拨
3. **平仓记录失败**: 检查trade_id是否正确，是否已经记录过平仓

### 调试技巧

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.INFO)

# 检查待平仓交易
print(f"待平仓交易: {trade_logger.get_pending_trade_ids()}")
```
