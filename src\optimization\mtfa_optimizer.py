#!/usr/bin/env python3
# mtfa_optimizer.py
"""
MTFA (多时间框架分析) 数据优化器
实现高效的数据获取、缓存和重采样机制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class MTFAOptimizer:
    """MTFA数据优化器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_cache = {}
        self.resampled_cache = {}
        
    def optimize_mtfa_data_acquisition(self, 
                                     main_data: pd.DataFrame,
                                     target_timeframes: List[str]) -> Dict[str, pd.DataFrame]:
        """
        优化的MTFA数据获取策略
        
        Args:
            main_data: 主时间框架数据
            target_timeframes: 目标时间框架列表
            
        Returns:
            优化后的MTFA数据字典
        """
        print("🔄 开始优化MTFA数据获取...")
        
        main_timeframe = self.config.get('interval', '15m')
        mtfa_data = {}
        
        # 策略1: 尝试从主数据重采样生成更高时间框架
        for timeframe in target_timeframes:
            if timeframe == main_timeframe:
                continue
                
            print(f"  📊 处理 {timeframe} 时间框架...")
            
            # 检查是否可以从主数据重采样
            if self._can_resample_from_main(main_timeframe, timeframe):
                print(f"    🔄 从 {main_timeframe} 重采样生成 {timeframe}")
                resampled_data = self._resample_data(main_data, main_timeframe, timeframe)
                if resampled_data is not None and not resampled_data.empty:
                    mtfa_data[timeframe] = resampled_data
                    continue
            
            # 策略2: 从API获取数据（带缓存）
            print(f"    📡 从API获取 {timeframe} 数据")
            api_data = self._fetch_with_cache(timeframe, main_data)
            if api_data is not None and not api_data.empty:
                mtfa_data[timeframe] = api_data
        
        print(f"✅ MTFA数据获取完成，共 {len(mtfa_data)} 个时间框架")
        return mtfa_data
    
    def _can_resample_from_main(self, main_tf: str, target_tf: str) -> bool:
        """
        判断是否可以从主时间框架重采样生成目标时间框架
        
        Args:
            main_tf: 主时间框架
            target_tf: 目标时间框架
            
        Returns:
            是否可以重采样
        """
        tf_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        main_min = tf_minutes.get(main_tf, 15)
        target_min = tf_minutes.get(target_tf, 60)
        
        # 只能从小时间框架重采样到大时间框架，且必须是整数倍
        return target_min > main_min and target_min % main_min == 0
    
    def _resample_data(self, 
                      data: pd.DataFrame,
                      from_tf: str,
                      to_tf: str) -> Optional[pd.DataFrame]:
        """
        重采样数据到目标时间框架
        
        Args:
            data: 源数据
            from_tf: 源时间框架
            to_tf: 目标时间框架
            
        Returns:
            重采样后的数据
        """
        try:
            # 转换时间框架格式
            resample_rule = self._convert_timeframe_to_pandas(to_tf)
            
            if resample_rule is None:
                return None
            
            # 确保数据有正确的OHLCV列
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in data.columns for col in required_cols):
                print(f"    ⚠️  缺少必要的OHLCV列")
                return None
            
            # 执行重采样
            resampled = data.resample(resample_rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'qav': 'sum' if 'qav' in data.columns else 'first',
                'n': 'sum' if 'n' in data.columns else 'first',
                'tbbav': 'sum' if 'tbbav' in data.columns else 'first',
                'tbqav': 'sum' if 'tbqav' in data.columns else 'first'
            })
            
            # 移除空值行
            resampled = resampled.dropna()
            
            print(f"    ✅ 重采样成功: {len(data)} -> {len(resampled)} 条数据")
            return resampled
            
        except Exception as e:
            print(f"    ❌ 重采样失败: {e}")
            return None
    
    def _convert_timeframe_to_pandas(self, timeframe: str) -> Optional[str]:
        """
        将时间框架转换为pandas重采样规则
        
        Args:
            timeframe: 时间框架 (如 '1h', '4h', '1d')
            
        Returns:
            pandas重采样规则
        """
        conversion_map = {
            '1m': '1T',
            '5m': '5T', 
            '15m': '15T',
            '30m': '30T',
            '1h': '1H',
            '4h': '4H',
            '1d': '1D'
        }
        
        return conversion_map.get(timeframe)
    
    def _fetch_with_cache(self, 
                         timeframe: str,
                         main_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        带缓存的数据获取
        
        Args:
            timeframe: 时间框架
            main_data: 主数据（用于确定时间范围）
            
        Returns:
            获取的数据
        """
        cache_key = f"{self.config['symbol']}_{timeframe}"
        
        # 检查缓存
        if cache_key in self.data_cache:
            cached_data = self.data_cache[cache_key]
            if self._is_cache_valid(cached_data, main_data):
                print(f"    💾 使用缓存数据")
                return cached_data
        
        # 从API获取新数据
        try:
            from binance.client import Client
            client = Client()
            
            # 计算需要的数据量
            data_start = main_data.index[0]
            data_end = main_data.index[-1]
            
            # 添加缓冲时间以确保有足够的历史数据
            buffer_days = self._get_buffer_days(timeframe)
            extended_start = data_start - pd.Timedelta(days=buffer_days)
            
            # 计算需要的K线数量
            required_bars = self._calculate_required_bars(
                extended_start, data_end, timeframe
            )
            
            import data_utils
            import config
            # 🚀 检查分层数据策略开关
            enable_layered_strategy = getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True)

            if enable_layered_strategy:
                mtfa_limit = getattr(config, 'MTFA_DATA_LIMIT', 2000)
                actual_limit = min(required_bars, mtfa_limit)
            else:
                # 使用传统限制
                actual_limit = min(required_bars, 1000)

            fetched_data = data_utils.fetch_binance_history(
                binance_client=client,
                symbol=self.config['symbol'],
                interval=timeframe,
                limit=actual_limit
            )
            
            if fetched_data is not None and not fetched_data.empty:
                # 过滤到相关时间范围
                filtered_data = fetched_data[
                    (fetched_data.index >= extended_start) &
                    (fetched_data.index <= data_end + pd.Timedelta(hours=24))
                ]
                
                # 缓存数据
                self.data_cache[cache_key] = filtered_data
                
                print(f"    📡 API获取成功: {len(filtered_data)} 条数据")
                return filtered_data
            
        except Exception as e:
            print(f"    ❌ API获取失败: {e}")
            
        return None
    
    def _is_cache_valid(self, 
                       cached_data: pd.DataFrame,
                       main_data: pd.DataFrame) -> bool:
        """
        检查缓存数据是否有效
        
        Args:
            cached_data: 缓存的数据
            main_data: 主数据
            
        Returns:
            缓存是否有效
        """
        if cached_data.empty:
            return False
        
        # 检查时间范围覆盖
        main_start = main_data.index[0]
        main_end = main_data.index[-1]
        
        cache_start = cached_data.index[0]
        cache_end = cached_data.index[-1]
        
        # 缓存数据应该覆盖主数据的时间范围（加上缓冲）
        buffer_hours = 24
        required_start = main_start - pd.Timedelta(hours=buffer_hours)
        required_end = main_end + pd.Timedelta(hours=buffer_hours)
        
        return (cache_start <= required_start and cache_end >= required_end)
    
    def _get_buffer_days(self, timeframe: str) -> int:
        """
        根据时间框架获取缓冲天数
        
        Args:
            timeframe: 时间框架
            
        Returns:
            缓冲天数
        """
        buffer_map = {
            '1m': 1,
            '5m': 2,
            '15m': 3,
            '30m': 5,
            '1h': 7,
            '4h': 14,
            '1d': 30
        }
        
        return buffer_map.get(timeframe, 7)
    
    def _calculate_required_bars(self,
                               start_time: pd.Timestamp,
                               end_time: pd.Timestamp,
                               timeframe: str) -> int:
        """
        计算需要的K线数量
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            timeframe: 时间框架
            
        Returns:
            需要的K线数量
        """
        tf_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        interval_minutes = tf_minutes.get(timeframe, 60)
        total_minutes = (end_time - start_time).total_seconds() / 60
        
        required_bars = int(total_minutes / interval_minutes) + 50  # 额外缓冲
        
        return min(required_bars, 1000)  # API限制
    
    def clear_cache(self):
        """清空缓存"""
        self.data_cache.clear()
        self.resampled_cache.clear()
        print("🗑️  MTFA缓存已清空")
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        cache_info = {
            'cached_timeframes': list(self.data_cache.keys()),
            'cache_sizes': {
                key: len(df) for key, df in self.data_cache.items()
            },
            'total_cached_rows': sum(len(df) for df in self.data_cache.values())
        }
        
        return cache_info
