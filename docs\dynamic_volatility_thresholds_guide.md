# 动态波动率阈值使用指南

## 概述

动态波动率阈值是对传统固定阈值的重要改进，通过使用ATR（平均真实波幅）等波动率指标来动态调整目标变量的阈值，使其能够更好地适应不同的市场环境。

## 核心优势

### ✅ 解决的问题

1. **固定阈值的局限性**
   - 高波动率市场：固定阈值过小，产生过多噪音信号
   - 低波动率市场：固定阈值过大，错失有效信号
   - 不同时间段：市场状态变化时阈值不适应

2. **动态阈值的优势**
   - **自适应性**：阈值随市场波动率自动调整
   - **一致性**：在不同市场环境下保持相对一致的信号质量
   - **灵活性**：可配置的倍数和限制范围
   - **稳定性**：可选的平滑处理减少噪音

## 实现原理

### 动态阈值计算公式

```
动态阈值 = ATR × 倍数
最终阈值 = clip(动态阈值, 最小阈值, 最大阈值)
```

### 目标条件判断

```python
# 传统固定阈值
上涨条件 = 未来价格 > 当前价格 × (1 + 固定阈值)
下跌条件 = 未来价格 < 当前价格 × (1 - 固定阈值)

# 动态波动率阈值
上涨条件 = 未来价格 > 当前价格 × (1 + 动态阈值)
下跌条件 = 未来价格 < 当前价格 × (1 - 动态阈值)
```

## 配置参数

### 基础配置

```python
target_config = {
    # 启用动态阈值
    'enable_dynamic_thresholds': True,
    
    # 波动率基准指标
    'dynamic_threshold_base': 'ATRr_14',  # 使用14期ATR
    
    # ATR倍数
    'dynamic_threshold_multipliers': [1.5],  # 1.5倍ATR
    
    # 阈值限制
    'dynamic_threshold_min': 0.005,  # 最小0.5%
    'dynamic_threshold_max': 0.05,   # 最大5%
    
    # 平滑处理（可选）
    'dynamic_threshold_smoothing': 3,  # 3期移动平均
}
```

### 参数详解

#### 1. `enable_dynamic_thresholds`
- **类型**: Boolean
- **默认值**: False
- **说明**: 是否启用动态波动率阈值

#### 2. `dynamic_threshold_base`
- **类型**: String
- **默认值**: 'ATRr_14'
- **说明**: 用作阈值基准的波动率指标列名
- **常用选项**: 'ATRr_14', 'ATRr_21', 'volatility_5p'

#### 3. `dynamic_threshold_multipliers`
- **类型**: List[Float]
- **默认值**: [1.5]
- **说明**: ATR的倍数，当前使用第一个值
- **建议范围**: 0.5 - 3.0

#### 4. `dynamic_threshold_min`
- **类型**: Float
- **默认值**: 0.005
- **说明**: 动态阈值的最小值（防止过小）
- **建议范围**: 0.001 - 0.01

#### 5. `dynamic_threshold_max`
- **类型**: Float
- **默认值**: 0.05
- **说明**: 动态阈值的最大值（防止过大）
- **建议范围**: 0.02 - 0.1

#### 6. `dynamic_threshold_smoothing`
- **类型**: Integer
- **默认值**: 0
- **说明**: 平滑周期，0表示不平滑
- **建议范围**: 0, 3, 5

## 使用示例

### 1. 基础使用

```python
from src.core.data_utils import add_classification_features, create_target_variable

# 1. 确保计算ATR特征
feature_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    'enable_ta': True,
    'atr_period': 14,
    # ... 其他特征配置
}

df_with_features = add_classification_features(df, feature_config)

# 2. 使用动态阈值创建目标变量
target_config = {
    'name': 'BTC_5M',
    'prediction_periods': [1],
    'target_variable_type': 'BOTH',
    
    # 启用动态阈值
    'enable_dynamic_thresholds': True,
    'dynamic_threshold_base': 'ATRr_14',
    'dynamic_threshold_multipliers': [1.5],
    'dynamic_threshold_min': 0.005,
    'dynamic_threshold_max': 0.05,
    'dynamic_threshold_smoothing': 3
}

df_with_targets, target_col = create_target_variable(df_with_features, target_config)
```

### 2. 不同策略配置

#### 保守策略
```python
conservative_config = {
    'enable_dynamic_thresholds': True,
    'dynamic_threshold_base': 'ATRr_14',
    'dynamic_threshold_multipliers': [1.0],  # 1倍ATR
    'dynamic_threshold_min': 0.003,  # 最小0.3%
    'dynamic_threshold_max': 0.03,   # 最大3%
    'dynamic_threshold_smoothing': 5  # 5期平滑
}
```

#### 平衡策略
```python
balanced_config = {
    'enable_dynamic_thresholds': True,
    'dynamic_threshold_base': 'ATRr_14',
    'dynamic_threshold_multipliers': [1.5],  # 1.5倍ATR
    'dynamic_threshold_min': 0.005,  # 最小0.5%
    'dynamic_threshold_max': 0.05,   # 最大5%
    'dynamic_threshold_smoothing': 3  # 3期平滑
}
```

#### 激进策略
```python
aggressive_config = {
    'enable_dynamic_thresholds': True,
    'dynamic_threshold_base': 'ATRr_14',
    'dynamic_threshold_multipliers': [2.0],  # 2倍ATR
    'dynamic_threshold_min': 0.01,   # 最小1%
    'dynamic_threshold_max': 0.08,   # 最大8%
    'dynamic_threshold_smoothing': 0  # 无平滑
}
```

### 3. 多时间框架配置

```python
# 不同时间框架使用不同的ATR周期
timeframe_configs = {
    '1m': {
        'dynamic_threshold_base': 'ATRr_14',
        'dynamic_threshold_multipliers': [2.0],  # 短周期需要更大倍数
        'dynamic_threshold_min': 0.002,
        'dynamic_threshold_max': 0.03,
    },
    '5m': {
        'dynamic_threshold_base': 'ATRr_14',
        'dynamic_threshold_multipliers': [1.5],
        'dynamic_threshold_min': 0.005,
        'dynamic_threshold_max': 0.05,
    },
    '1h': {
        'dynamic_threshold_base': 'ATRr_21',  # 长周期使用更长ATR
        'dynamic_threshold_multipliers': [1.0],
        'dynamic_threshold_min': 0.01,
        'dynamic_threshold_max': 0.08,
    }
}
```

## 效果对比

### 测试结果示例

```
📊 目标变量分布对比:
动态阈值分布: {1: 0.005, 2: 0.995}  # 0.5%上涨, 99.5%中性
固定阈值分布: {2: 1.0}              # 100%中性

动态阈值统计:
  均值: 0.0466 (4.66%)
  标准差: 0.0117
  范围: [0.0050, 0.0500]
  中位数: 0.0500
```

### 优势体现

1. **信号质量**：动态阈值能够在高波动期产生信号，固定阈值可能完全无信号
2. **适应性**：阈值随市场状态自动调整
3. **一致性**：不同市场环境下保持相对稳定的信号频率

## 最佳实践

### 1. ATR周期选择

```python
# 根据交易时间框架选择ATR周期
atr_periods = {
    '1m': 14,   # 短周期
    '5m': 14,   # 标准周期
    '15m': 14,  # 标准周期
    '1h': 21,   # 稍长周期
    '4h': 21,   # 长周期
    '1d': 14    # 日线标准
}
```

### 2. 倍数选择指南

```python
# 根据市场特性选择倍数
multiplier_guide = {
    '高频交易': 0.5,   # 小倍数，敏感
    '日内交易': 1.0,   # 标准倍数
    '短线交易': 1.5,   # 平衡倍数
    '中线交易': 2.0,   # 大倍数，稳定
    '长线交易': 2.5    # 更大倍数
}
```

### 3. 限制范围设置

```python
# 根据资产特性设置限制
limit_ranges = {
    'BTC': {'min': 0.005, 'max': 0.08},    # 主流币
    'ETH': {'min': 0.005, 'max': 0.08},    # 主流币
    'ALT': {'min': 0.01, 'max': 0.15},     # 山寨币
    'MEME': {'min': 0.02, 'max': 0.30}     # 模因币
}
```

### 4. 平滑处理建议

```python
# 根据数据噪音程度选择平滑
smoothing_guide = {
    '1m': 5,    # 高频数据需要更多平滑
    '5m': 3,    # 中等平滑
    '15m': 3,   # 中等平滑
    '1h': 0,    # 低频数据可以不平滑
    '4h': 0     # 低频数据可以不平滑
}
```

## 监控和调试

### 1. 日志信息

系统会自动记录详细的动态阈值信息：

```
create_target_variable (BTC_5M): 使用动态波动率阈值模式
create_target_variable (BTC_5M): 动态阈值统计 - 均值: 0.0466, 标准差: 0.0117, 范围: [0.0050, 0.0500], 中位数: 0.0500
create_target_variable (BTC_5M): 动态阈值条件统计 - 上涨: 1/199 (0.5%), 下跌: 0/199 (0.0%)
create_target_variable (BTC_5M): 成功应用动态波动率阈值
```

### 2. 性能指标

监控以下指标来评估动态阈值效果：

```python
# 信号分布
signal_distribution = target_series.value_counts(normalize=True)

# 阈值统计
threshold_stats = {
    'mean': dynamic_thresholds.mean(),
    'std': dynamic_thresholds.std(),
    'min': dynamic_thresholds.min(),
    'max': dynamic_thresholds.max(),
    'median': dynamic_thresholds.median()
}

# 信号频率
signal_frequency = (target_series != 2).mean()  # 非中性信号比例
```

## 故障排除

### 1. ATR列不存在

**问题**: `动态阈值基准列 'ATRr_14' 不存在`
**解决**: 
- 确保在目标变量创建前启用技术指标计算
- 检查ATR周期配置是否正确
- 验证特征计算是否成功

### 2. 全部为中性信号

**问题**: 动态阈值过大，没有产生上涨/下跌信号
**解决**:
- 减小ATR倍数
- 降低最小阈值限制
- 检查ATR数据是否异常

### 3. 信号过于频繁

**问题**: 动态阈值过小，产生过多噪音信号
**解决**:
- 增大ATR倍数
- 提高最小阈值限制
- 增加平滑周期

### 4. 阈值计算失败

**问题**: 动态阈值计算返回None
**解决**:
- 检查ATR数据质量
- 验证配置参数有效性
- 查看详细错误日志

## 向后兼容性

动态波动率阈值完全向后兼容：

```python
# 传统固定阈值（默认行为）
traditional_config = {
    'target_threshold': 0.01,
    'enable_dynamic_thresholds': False  # 或不设置此参数
}

# 新的动态阈值
dynamic_config = {
    'enable_dynamic_thresholds': True,
    'dynamic_threshold_base': 'ATRr_14',
    'dynamic_threshold_multipliers': [1.5]
}
```

## 总结

动态波动率阈值提供了：

1. **智能适应** - 根据市场波动率自动调整阈值
2. **配置灵活** - 丰富的参数支持不同策略
3. **稳定可靠** - 完善的错误处理和回退机制
4. **向后兼容** - 不影响现有固定阈值功能
5. **易于监控** - 详细的日志和统计信息

通过使用动态波动率阈值，可以显著提高目标变量的质量和模型的适应性，特别是在波动率变化较大的市场环境中。
