#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 数据类型验证和转换系统

提供严格的数据类型检查和强制转换功能，确保特征计算的数据质量和一致性。
解决数据类型不一致导致的计算错误和性能问题。
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings

# 导入配置管理器
try:
    from .config_manager import get_data_processing_param, get_constant
    CONFIG_MANAGER_AVAILABLE = True
except ImportError:
    CONFIG_MANAGER_AVAILABLE = False

logger = logging.getLogger(__name__)


class DataTypeValidator:
    """
    🚀 数据类型验证器
    
    提供严格的数据类型检查、转换和验证功能，确保数据质量。
    """
    
    def __init__(self, target_config: Optional[Dict[str, Any]] = None):
        """
        初始化数据类型验证器
        
        Args:
            target_config: 目标配置字典
        """
        self.target_config = target_config or {}
        
        # 🚀 从配置获取参数
        if CONFIG_MANAGER_AVAILABLE:
            self.price_columns = get_data_processing_param('price_columns', target_config, 
                                                         ['open', 'high', 'low', 'close'], list)
            self.volume_columns = get_data_processing_param('volume_columns', target_config,
                                                          ['volume', 'qav', 'n', 'tbbav', 'tbqav'], list)
            self.default_price_value = get_data_processing_param('default_price_value', target_config, 0.0, float)
            self.default_volume_value = get_data_processing_param('default_volume_value', target_config, 0.0, float)
            self.variance_threshold = get_data_processing_param('variance_threshold', target_config, 1e-10, float)
        else:
            self.price_columns = ['open', 'high', 'low', 'close']
            self.volume_columns = ['volume', 'qav', 'n', 'tbbav', 'tbqav']
            self.default_price_value = 0.0
            self.default_volume_value = 0.0
            self.variance_threshold = 1e-10
        
        # 数据类型映射
        self.dtype_mapping = {
            'price': np.float64,
            'volume': np.float64,
            'feature': np.float64,
            'target': np.int32,
            'boolean': np.bool_,
            'categorical': 'category'
        }
        
        # 验证统计
        self.validation_stats = {
            'columns_processed': 0,
            'columns_converted': 0,
            'conversion_errors': 0,
            'nan_values_found': 0,
            'inf_values_found': 0
        }
    
    def validate_and_convert_dataframe(
        self,
        df: pd.DataFrame,
        column_types: Optional[Dict[str, str]] = None,
        strict_mode: bool = True,
        verbose: bool = True
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        🚀 验证和转换整个DataFrame的数据类型
        
        Args:
            df: 输入DataFrame
            column_types: 列类型映射字典 {'column_name': 'type'}
            strict_mode: 是否启用严格模式（转换失败时抛出异常）
            verbose: 是否显示详细信息
            
        Returns:
            tuple: (转换后的DataFrame, 验证统计信息)
            
        Raises:
            TypeError: 严格模式下转换失败时抛出
            ValueError: 数据验证失败时抛出
        """
        if not isinstance(df, pd.DataFrame):
            error_msg = f"输入必须是DataFrame，得到 {type(df)}"
            if CONFIG_MANAGER_AVAILABLE:
                error_msg = get_constant('error_messages', 'invalid_dataframe', error_msg)
            raise TypeError(error_msg)
        
        if df.empty:
            if verbose:
                logger.warning("validate_and_convert_dataframe: 输入DataFrame为空")
            return df.copy(), self.validation_stats
        
        if verbose:
            logger.info(f"validate_and_convert_dataframe: 开始验证DataFrame，形状: {df.shape}")
        
        # 重置统计
        self._reset_stats()
        
        # 复制DataFrame避免修改原始数据
        df_converted = df.copy()
        
        # 自动检测列类型（如果未提供）
        if column_types is None:
            column_types = self._auto_detect_column_types(df_converted)
        
        # 逐列进行类型转换
        conversion_errors = []
        
        for column in df_converted.columns:
            self.validation_stats['columns_processed'] += 1
            
            try:
                # 获取目标类型
                target_type = column_types.get(column, 'feature')
                
                # 执行类型转换
                df_converted[column], column_stats = self._convert_column_type(
                    df_converted[column], target_type, column, strict_mode, verbose
                )
                
                # 更新统计
                if column_stats['converted']:
                    self.validation_stats['columns_converted'] += 1
                self.validation_stats['nan_values_found'] += column_stats['nan_count']
                self.validation_stats['inf_values_found'] += column_stats['inf_count']
                
            except Exception as e:
                self.validation_stats['conversion_errors'] += 1
                error_info = f"列 '{column}' 转换失败: {e}"
                conversion_errors.append(error_info)
                
                if strict_mode:
                    logger.error(f"validate_and_convert_dataframe: {error_info}")
                    raise TypeError(error_info) from e
                else:
                    logger.warning(f"validate_and_convert_dataframe: {error_info}")
        
        # 最终验证
        final_stats = self._perform_final_validation(df_converted, verbose)
        self.validation_stats.update(final_stats)
        
        if verbose:
            self._log_validation_summary(conversion_errors)
        
        return df_converted, self.validation_stats.copy()
    
    def validate_essential_columns(
        self,
        df: pd.DataFrame,
        required_columns: Optional[List[str]] = None,
        strict_mode: bool = True,
        verbose: bool = True
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        🚀 验证和转换关键列的数据类型（用于特征计算入口）
        
        Args:
            df: 输入DataFrame
            required_columns: 必需的列名列表
            strict_mode: 是否启用严格模式
            verbose: 是否显示详细信息
            
        Returns:
            tuple: (转换后的DataFrame, 验证统计信息)
        """
        if required_columns is None:
            required_columns = self.price_columns + ['volume']
        
        if verbose:
            logger.info(f"validate_essential_columns: 验证关键列 {required_columns}")
        
        # 检查必需列是否存在
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            error_msg = f"缺少必需的列: {missing_columns}"
            if CONFIG_MANAGER_AVAILABLE:
                error_msg = get_constant('error_messages', 'missing_columns', error_msg)
            raise ValueError(error_msg)
        
        # 定义关键列的类型映射
        essential_column_types = {}
        for col in required_columns:
            if col in self.price_columns:
                essential_column_types[col] = 'price'
            elif col in self.volume_columns:
                essential_column_types[col] = 'volume'
            else:
                essential_column_types[col] = 'feature'
        
        # 只转换关键列
        df_subset = df[required_columns].copy()
        df_converted, stats = self.validate_and_convert_dataframe(
            df_subset, essential_column_types, strict_mode, verbose
        )
        
        # 将转换后的列合并回原DataFrame
        df_result = df.copy()
        for col in required_columns:
            df_result[col] = df_converted[col]
        
        return df_result, stats
    
    def convert_to_lstm_format(
        self,
        df: pd.DataFrame,
        feature_columns: List[str],
        target_column: str,
        verbose: bool = True
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        🚀 转换数据为LSTM格式（强制float64）
        
        Args:
            df: 输入DataFrame
            feature_columns: 特征列名列表
            target_column: 目标列名
            verbose: 是否显示详细信息
            
        Returns:
            tuple: (转换后的DataFrame, 验证统计信息)
        """
        if verbose:
            logger.info(f"convert_to_lstm_format: 转换 {len(feature_columns)} 个特征列为LSTM格式")
        
        df_lstm = df.copy()
        conversion_stats = {'feature_errors': 0, 'target_errors': 0}
        
        # 转换特征列为float64
        for col in feature_columns:
            if col not in df_lstm.columns:
                logger.warning(f"convert_to_lstm_format: 特征列 '{col}' 不存在，跳过")
                continue
            
            try:
                # 强制转换为float64
                df_lstm[col] = pd.to_numeric(df_lstm[col], errors='coerce').astype(np.float64)
                
                # 处理NaN值
                nan_count = df_lstm[col].isna().sum()
                if nan_count > 0:
                    if verbose:
                        logger.warning(f"convert_to_lstm_format: 列 '{col}' 有 {nan_count} 个NaN值，填充为0")
                    df_lstm[col] = df_lstm[col].fillna(0.0)
                
            except Exception as e:
                conversion_stats['feature_errors'] += 1
                logger.error(f"convert_to_lstm_format: 转换特征列 '{col}' 失败: {e}")
                # 设置为默认值
                df_lstm[col] = 0.0
        
        # 转换目标列
        if target_column in df_lstm.columns:
            try:
                # 检查目标列的数据类型
                if df_lstm[target_column].dtype.kind in ['f', 'i']:
                    # 数值类型，检查NaN
                    nan_count = df_lstm[target_column].isna().sum()
                    if nan_count > 0:
                        logger.warning(f"convert_to_lstm_format: 目标列 '{target_column}' 有 {nan_count} 个NaN值")
                else:
                    # 非数值类型，尝试转换
                    df_lstm[target_column] = pd.to_numeric(df_lstm[target_column], errors='coerce')
                    
            except Exception as e:
                conversion_stats['target_errors'] += 1
                logger.error(f"convert_to_lstm_format: 处理目标列 '{target_column}' 失败: {e}")
        
        if verbose:
            logger.info(f"convert_to_lstm_format: 转换完成，特征错误: {conversion_stats['feature_errors']}, "
                       f"目标错误: {conversion_stats['target_errors']}")
        
        return df_lstm, conversion_stats
    
    def _auto_detect_column_types(self, df: pd.DataFrame) -> Dict[str, str]:
        """自动检测列的数据类型"""
        column_types = {}
        
        for column in df.columns:
            if column in self.price_columns:
                column_types[column] = 'price'
            elif column in self.volume_columns:
                column_types[column] = 'volume'
            elif column.endswith('_target') or column.startswith('target_'):
                column_types[column] = 'target'
            elif 'pattern' in column.lower() and df[column].dtype == 'object':
                column_types[column] = 'categorical'
            elif df[column].dtype == 'bool':
                column_types[column] = 'boolean'
            else:
                column_types[column] = 'feature'
        
        return column_types
    
    def _convert_column_type(
        self,
        series: pd.Series,
        target_type: str,
        column_name: str,
        strict_mode: bool,
        verbose: bool
    ) -> Tuple[pd.Series, Dict[str, Any]]:
        """转换单列的数据类型"""
        stats = {
            'converted': False,
            'nan_count': 0,
            'inf_count': 0,
            'original_dtype': str(series.dtype),
            'final_dtype': str(series.dtype)
        }
        
        # 获取目标数据类型
        target_dtype = self.dtype_mapping.get(target_type, np.float64)
        
        # 如果已经是目标类型，直接返回
        if series.dtype == target_dtype:
            stats['nan_count'] = series.isna().sum()
            if target_type in ['price', 'volume', 'feature']:
                stats['inf_count'] = np.isinf(series).sum()
            return series, stats
        
        try:
            # 执行类型转换
            if target_type in ['price', 'volume', 'feature']:
                # 数值类型转换
                converted_series = pd.to_numeric(series, errors='coerce')
                
                # 处理无穷大值
                inf_mask = np.isinf(converted_series)
                stats['inf_count'] = inf_mask.sum()
                if stats['inf_count'] > 0:
                    if verbose:
                        logger.warning(f"列 '{column_name}' 有 {stats['inf_count']} 个无穷大值")
                    converted_series = converted_series.replace([np.inf, -np.inf], np.nan)
                
                # 处理NaN值
                stats['nan_count'] = converted_series.isna().sum()
                if stats['nan_count'] > 0:
                    default_value = self._get_default_value(target_type)
                    if verbose:
                        logger.debug(f"列 '{column_name}' 有 {stats['nan_count']} 个NaN值，填充为 {default_value}")
                    converted_series = converted_series.fillna(default_value)
                
                # 转换为目标数据类型
                converted_series = converted_series.astype(target_dtype)
                
            elif target_type == 'target':
                # 目标变量转换
                converted_series = pd.to_numeric(series, errors='coerce')
                stats['nan_count'] = converted_series.isna().sum()
                if stats['nan_count'] > 0 and strict_mode:
                    raise ValueError(f"目标列 '{column_name}' 包含无法转换的值")
                converted_series = converted_series.astype(target_dtype)
                
            elif target_type == 'boolean':
                # 布尔类型转换
                if series.dtype == 'object':
                    # 字符串到布尔的转换
                    bool_map = {'true': True, 'false': False, '1': True, '0': False}
                    converted_series = series.str.lower().map(bool_map)
                else:
                    converted_series = series.astype(bool)
                    
            elif target_type == 'categorical':
                # 分类类型转换
                converted_series = series.astype('category')
                
            else:
                # 默认转换
                converted_series = series.astype(target_dtype)
            
            stats['converted'] = True
            stats['final_dtype'] = str(converted_series.dtype)
            
            return converted_series, stats
            
        except Exception as e:
            if strict_mode:
                raise TypeError(f"列 '{column_name}' 类型转换失败: {e}") from e
            else:
                logger.warning(f"列 '{column_name}' 类型转换失败: {e}，保持原始类型")
                return series, stats
    
    def _get_default_value(self, value_type: str) -> Union[float, int]:
        """获取默认填充值"""
        if value_type == 'price':
            return self.default_price_value
        elif value_type == 'volume':
            return self.default_volume_value
        else:
            return 0.0
    
    def _perform_final_validation(self, df: pd.DataFrame, verbose: bool) -> Dict[str, Any]:
        """执行最终验证"""
        stats = {
            'final_shape': df.shape,
            'numeric_columns': 0,
            'total_nan_values': 0,
            'total_inf_values': 0,
            'constant_columns': 0
        }
        
        # 统计数值列
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        stats['numeric_columns'] = len(numeric_columns)
        
        if len(numeric_columns) > 0:
            # 统计NaN和无穷大值
            stats['total_nan_values'] = df[numeric_columns].isna().sum().sum()
            stats['total_inf_values'] = np.isinf(df[numeric_columns]).sum().sum()
            
            # 检查常数列
            for col in numeric_columns:
                if df[col].var() < self.variance_threshold:
                    stats['constant_columns'] += 1
                    if verbose:
                        logger.warning(f"检测到常数列: '{col}' (方差: {df[col].var():.2e})")
        
        return stats
    
    def _reset_stats(self):
        """重置验证统计"""
        self.validation_stats = {
            'columns_processed': 0,
            'columns_converted': 0,
            'conversion_errors': 0,
            'nan_values_found': 0,
            'inf_values_found': 0
        }
    
    def _log_validation_summary(self, conversion_errors: List[str]):
        """记录验证摘要"""
        logger.info(f"数据类型验证完成:")
        logger.info(f"  处理列数: {self.validation_stats['columns_processed']}")
        logger.info(f"  转换列数: {self.validation_stats['columns_converted']}")
        logger.info(f"  转换错误: {self.validation_stats['conversion_errors']}")
        logger.info(f"  NaN值数量: {self.validation_stats['nan_values_found']}")
        logger.info(f"  无穷大值数量: {self.validation_stats['inf_values_found']}")
        
        if conversion_errors:
            logger.warning(f"转换错误详情:")
            for error in conversion_errors[:5]:  # 只显示前5个错误
                logger.warning(f"  - {error}")


# 便捷函数
def validate_dataframe_types(
    df: pd.DataFrame,
    target_config: Optional[Dict[str, Any]] = None,
    column_types: Optional[Dict[str, str]] = None,
    strict_mode: bool = True,
    verbose: bool = True
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    🚀 便捷的DataFrame类型验证函数
    
    Args:
        df: 输入DataFrame
        target_config: 目标配置字典
        column_types: 列类型映射
        strict_mode: 是否启用严格模式
        verbose: 是否显示详细信息
        
    Returns:
        tuple: (验证后的DataFrame, 统计信息)
    """
    validator = DataTypeValidator(target_config)
    return validator.validate_and_convert_dataframe(df, column_types, strict_mode, verbose)


def validate_essential_columns(
    df: pd.DataFrame,
    target_config: Optional[Dict[str, Any]] = None,
    required_columns: Optional[List[str]] = None,
    strict_mode: bool = True,
    verbose: bool = True
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    🚀 便捷的关键列验证函数

    Args:
        df: 输入DataFrame
        target_config: 目标配置字典
        required_columns: 必需的列名列表
        strict_mode: 是否启用严格模式
        verbose: 是否显示详细信息

    Returns:
        tuple: (验证后的DataFrame, 统计信息)
    """
    validator = DataTypeValidator(target_config)
    return validator.validate_essential_columns(df, required_columns, strict_mode, verbose)


# 装饰器函数
def ensure_data_types(
    required_columns: Optional[List[str]] = None,
    strict_mode: bool = False,
    verbose: bool = False
):
    """
    🚀 数据类型检查装饰器

    用于自动为函数添加数据类型检查和转换功能。

    Args:
        required_columns: 必需的列名列表
        strict_mode: 是否启用严格模式
        verbose: 是否显示详细信息

    Usage:
        @ensure_data_types(['open', 'high', 'low', 'close'], strict_mode=False)
        def my_feature_function(df, target_config):
            # 函数会自动获得类型检查的df
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 假设第一个参数是DataFrame，第二个是target_config
            if len(args) >= 2 and isinstance(args[0], pd.DataFrame):
                df = args[0]
                target_config = args[1] if isinstance(args[1], dict) else {}

                try:
                    # 执行数据类型验证
                    if DATA_TYPE_VALIDATOR_AVAILABLE:
                        validator = DataTypeValidator(target_config)

                        if required_columns:
                            df_validated, _ = validator.validate_essential_columns(
                                df, required_columns, strict_mode, verbose
                            )
                        else:
                            df_validated, _ = validator.validate_and_convert_dataframe(
                                df, None, strict_mode, verbose
                            )

                        # 替换第一个参数为验证后的DataFrame
                        args = (df_validated,) + args[1:]

                except Exception as e:
                    if verbose:
                        logger.warning(f"数据类型检查装饰器失败: {e}")
                    # 如果验证失败，继续使用原始数据

            # 调用原始函数
            return func(*args, **kwargs)

        return wrapper
    return decorator


def validate_numeric_columns(
    df: pd.DataFrame,
    columns: List[str],
    target_config: Optional[Dict[str, Any]] = None,
    fill_method: str = 'zero',
    verbose: bool = True
) -> pd.DataFrame:
    """
    🚀 验证和转换指定的数值列

    Args:
        df: 输入DataFrame
        columns: 要验证的列名列表
        target_config: 目标配置字典
        fill_method: NaN填充方法 ('zero', 'forward', 'mean')
        verbose: 是否显示详细信息

    Returns:
        验证后的DataFrame
    """
    df_result = df.copy()

    for col in columns:
        if col not in df_result.columns:
            if verbose:
                logger.warning(f"validate_numeric_columns: 列 '{col}' 不存在，跳过")
            continue

        try:
            # 记录原始类型
            original_dtype = df_result[col].dtype

            # 转换为数值类型
            df_result[col] = pd.to_numeric(df_result[col], errors='coerce')

            # 处理NaN值
            nan_count = df_result[col].isna().sum()
            if nan_count > 0:
                if fill_method == 'zero':
                    df_result[col] = df_result[col].fillna(0.0)
                elif fill_method == 'forward':
                    df_result[col] = df_result[col].fillna(method='ffill')
                elif fill_method == 'mean':
                    # 🚀 修复数据泄露：使用第一个有效值而不是全局均值
                    first_valid = df_result[col].dropna()
                    mean_val = first_valid.iloc[0] if len(first_valid) > 0 else 0.0
                    df_result[col] = df_result[col].fillna(mean_val)

                if verbose:
                    logger.debug(f"validate_numeric_columns: 列 '{col}' 填充了 {nan_count} 个NaN值")

            # 强制转换为float64
            df_result[col] = df_result[col].astype(np.float64)

            if verbose:
                logger.debug(f"validate_numeric_columns: 列 '{col}' 类型转换: {original_dtype} -> {df_result[col].dtype}")

        except Exception as e:
            logger.error(f"validate_numeric_columns: 验证列 '{col}' 失败: {e}")
            # 设置为默认值
            df_result[col] = 0.0

    return df_result


def check_data_quality(
    df: pd.DataFrame,
    target_config: Optional[Dict[str, Any]] = None,
    verbose: bool = True
) -> Dict[str, Any]:
    """
    🚀 检查DataFrame的数据质量

    Args:
        df: 输入DataFrame
        target_config: 目标配置字典
        verbose: 是否显示详细信息

    Returns:
        数据质量报告字典
    """
    quality_report = {
        'shape': df.shape,
        'total_cells': df.size,
        'numeric_columns': 0,
        'non_numeric_columns': 0,
        'total_nan_values': 0,
        'total_inf_values': 0,
        'constant_columns': [],
        'high_nan_columns': [],
        'data_type_issues': [],
        'memory_usage_mb': 0
    }

    try:
        # 基本统计
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        non_numeric_columns = df.select_dtypes(exclude=[np.number]).columns

        quality_report['numeric_columns'] = len(numeric_columns)
        quality_report['non_numeric_columns'] = len(non_numeric_columns)

        # NaN值统计
        if len(numeric_columns) > 0:
            quality_report['total_nan_values'] = df[numeric_columns].isna().sum().sum()
            quality_report['total_inf_values'] = np.isinf(df[numeric_columns]).sum().sum()

        # 检查常数列
        variance_threshold = 1e-10
        if CONFIG_MANAGER_AVAILABLE:
            variance_threshold = get_data_processing_param('variance_threshold', target_config, 1e-10, float)

        for col in numeric_columns:
            if df[col].var() < variance_threshold:
                quality_report['constant_columns'].append(col)

        # 检查高NaN比例的列
        nan_threshold = 0.5  # 50%以上为NaN的列
        for col in df.columns:
            nan_ratio = df[col].isna().sum() / len(df)
            if nan_ratio > nan_threshold:
                quality_report['high_nan_columns'].append({
                    'column': col,
                    'nan_ratio': nan_ratio
                })

        # 检查数据类型问题
        for col in df.columns:
            if df[col].dtype == 'object':
                # 检查是否应该是数值类型
                try:
                    numeric_converted = pd.to_numeric(df[col], errors='coerce')
                    conversion_success_ratio = (1 - numeric_converted.isna().sum() / len(df))
                    if conversion_success_ratio > 0.8:  # 80%以上可以转换为数值
                        quality_report['data_type_issues'].append({
                            'column': col,
                            'issue': 'should_be_numeric',
                            'conversion_success_ratio': conversion_success_ratio
                        })
                except:
                    pass

        # 内存使用情况
        quality_report['memory_usage_mb'] = df.memory_usage(deep=True).sum() / 1024 / 1024

        if verbose:
            logger.info(f"数据质量检查完成:")
            logger.info(f"  数据形状: {quality_report['shape']}")
            logger.info(f"  数值列: {quality_report['numeric_columns']}, 非数值列: {quality_report['non_numeric_columns']}")
            logger.info(f"  NaN值: {quality_report['total_nan_values']}, 无穷大值: {quality_report['total_inf_values']}")
            logger.info(f"  常数列: {len(quality_report['constant_columns'])}")
            logger.info(f"  高NaN比例列: {len(quality_report['high_nan_columns'])}")
            logger.info(f"  数据类型问题: {len(quality_report['data_type_issues'])}")
            logger.info(f"  内存使用: {quality_report['memory_usage_mb']:.2f} MB")

    except Exception as e:
        logger.error(f"check_data_quality: 数据质量检查失败: {e}")
        quality_report['error'] = str(e)

    return quality_report
