#!/usr/bin/env python3
"""
性能监控脚本
实时监控系统资源使用情况
"""

import psutil
import time
import threading
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval=5):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        print("🔍 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("⏹️  性能监控已停止")
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # 内存使用情况
                memory = psutil.virtual_memory()
                
                # 磁盘使用情况
                disk = psutil.disk_usage('/')
                
                # GPU使用情况（如果可用）
                gpu_info = self._get_gpu_info()
                
                # 打印监控信息
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"\n[{timestamp}] 系统性能监控:")
                print(f"  CPU: {cpu_percent:.1f}%")
                print(f"  内存: {memory.percent:.1f}% ({memory.used/(1024**3):.1f}GB/{memory.total/(1024**3):.1f}GB)")
                print(f"  磁盘: {disk.percent:.1f}%")
                
                if gpu_info:
                    print(f"  GPU: {gpu_info}")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(interval)
    
    def _get_gpu_info(self):
        """获取GPU信息"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]
                return f"{gpu.load*100:.1f}% ({gpu.memoryUsed}MB/{gpu.memoryTotal}MB)"
        except ImportError:
            pass
        return None
    
    def get_current_stats(self):
        """获取当前统计信息"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'process_count': len(psutil.pids())
        }

# 全局监控实例
monitor = PerformanceMonitor()

def start_performance_monitoring(interval=10):
    """启动性能监控"""
    monitor.start_monitoring(interval)

def stop_performance_monitoring():
    """停止性能监控"""
    monitor.stop_monitoring()

def get_performance_stats():
    """获取性能统计"""
    return monitor.get_current_stats()

if __name__ == "__main__":
    print("🚀 启动性能监控...")
    monitor.start_monitoring(5)
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n停止监控...")
        monitor.stop_monitoring()
