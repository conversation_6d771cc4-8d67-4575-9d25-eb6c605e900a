# 增强样本权重与数据质量监控指南

## 📋 概述

本指南介绍了优化建议三的实现：**优化样本质量与权重，让模型"学得更聪明"**。

### 🎯 核心改进

1. **细化动态样本权重策略**
   - 时间衰减权重：越近的数据权重越高
   - 波动率权重：极端高波动或低波动时期的样本权重调整
   - 成交量权重：放量突破时的样本权重更高
   - 市场状态权重：基于市场状态的权重分配

2. **训练数据质量监控**
   - 正负样本比例检查
   - 特征稳定性检查（方差分析）
   - 特征相关性检查（多重共线性检测）
   - 缺失值和异常值检查

## 🚀 快速开始

### 1. 启用增强样本权重

在目标配置中添加以下配置：

```python
target_config = {
    'name': 'BTC_15m_UP',
    'symbol': 'BTCUSDT',
    'interval': '15m',
    
    # 启用增强样本权重
    'enable_enhanced_sample_weighting': True,
    
    # 增强权重配置
    'enhanced_weighting_config': {
        'enable_time_decay': True,
        'enable_volatility_weighting': True,
        'enable_volume_weighting': True,
        'enable_market_state_weighting': True,
        
        # 权重组合策略
        'combination_method': 'weighted_average',
        'strategy_weights': {
            'time_decay': 0.3,
            'volatility': 0.3,
            'volume': 0.2,
            'market_state': 0.2
        }
    },
    
    # 数据质量监控配置
    'data_quality_config': {
        'quality_thresholds': {
            'min_positive_ratio': 0.01,
            'max_positive_ratio': 0.30,
            'min_feature_variance': 1e-10,
            'max_correlation': 0.98,
            'max_nan_ratio': 0.1,
            'min_samples': 100,
        }
    }
}
```

### 2. 使用预设配置

```python
from config.enhanced_sample_weighting_config import get_enhanced_weighting_config

# 根据市场条件和时间框架获取配置
config = get_enhanced_weighting_config(
    market_condition='bull_market',  # 'bull_market', 'bear_market', 'sideways_market'
    timeframe='short_term'           # 'short_term', 'medium_term', 'long_term'
)

# 合并到目标配置
target_config.update(config)
```

## 📊 权重策略详解

### 1. 时间衰减权重

**原理**：越近的数据对当前预测越重要

**配置参数**：
- `decay_rate`: 衰减率 (0.5-1.0)，越小衰减越快
- `min_weight`: 最小权重，防止过度衰减
- `max_weight`: 最大权重，限制权重范围

**示例**：
```python
'time_decay': {
    'decay_rate': 0.95,     # 每个时间步衰减5%
    'min_weight': 0.1,      # 最小权重10%
    'max_weight': 2.0,      # 最大权重200%
}
```

### 2. 波动率权重

**原理**：
- 低波动期：趋势酝酿期，权重提高
- 高波动期：噪音较多，权重降低

**配置参数**：
- `lookback_period`: 波动率计算回望期
- `extreme_vol_threshold`: 极端波动阈值（标准差倍数）
- `low_vol_multiplier`: 低波动权重倍数
- `high_vol_multiplier`: 高波动权重倍数

**示例**：
```python
'volatility_weighting': {
    'lookback_period': 20,
    'extreme_vol_threshold': 2.0,   # 2倍标准差
    'low_vol_multiplier': 1.5,      # 低波动权重×1.5
    'high_vol_multiplier': 0.3,     # 高波动权重×0.3
}
```

### 3. 成交量权重

**原理**：
- 放量突破：确认信号，权重提高
- 缩量整理：信号较弱，权重降低

**配置参数**：
- `lookback_period`: 成交量计算回望期
- `volume_threshold`: 放量阈值（平均成交量倍数）
- `high_volume_multiplier`: 放量权重倍数
- `low_volume_multiplier`: 缩量权重倍数

**示例**：
```python
'volume_weighting': {
    'lookback_period': 20,
    'volume_threshold': 1.5,        # 1.5倍平均成交量
    'high_volume_multiplier': 2.0,  # 放量权重×2.0
    'low_volume_multiplier': 0.8,   # 缩量权重×0.8
}
```

### 4. 权重组合策略

**加权平均** (推荐)：
```python
'combination_method': 'weighted_average'
'strategy_weights': {
    'time_decay': 0.3,      # 30%权重
    'volatility': 0.3,      # 30%权重
    'volume': 0.2,          # 20%权重
    'market_state': 0.2     # 20%权重
}
```

**乘积组合** (保守)：
```python
'combination_method': 'multiply'
# 最终权重 = 时间权重 × 波动率权重 × 成交量权重 × 市场状态权重
```

**最大值组合** (激进)：
```python
'combination_method': 'max'
# 最终权重 = max(时间权重, 波动率权重, 成交量权重, 市场状态权重)
```

## 🔍 数据质量监控

### 1. 正负样本比例检查

**检查内容**：
- 正样本比例是否在合理范围内 (1%-30%)
- 是否存在严重的类别不平衡

**建议**：
- 比例过低：调整`target_threshold`或使用SMOTE
- 比例过高：调整`target_threshold`降低正样本比例

### 2. 特征稳定性检查

**检查内容**：
- 识别近似常数特征（方差接近0）
- 检测特征的有效性

**建议**：
- 移除常数特征以提高训练效率
- 检查特征工程逻辑

### 3. 特征相关性检查

**检查内容**：
- 识别高度相关的特征对（相关性>0.98）
- 检测多重共线性问题

**建议**：
- 移除其中一个高相关特征
- 使用PCA等降维技术

### 4. 缺失值和异常值检查

**检查内容**：
- 特征和目标变量的缺失值比例
- 基于IQR的异常值检测

**建议**：
- 处理高缺失率特征
- 对异常值进行截断或变换

## 📈 使用效果

### 权重分布示例

```
🔍 训练数据质量报告 - BTC_15m_UP
============================================================
📊 数据形状: (5000, 150)
⏰ 检查时间: 2024-01-15 10:30:00
📈 总体状态: PASS - 数据质量良好
✅ 通过检查: 6/7 (85.7%)

⚠️  警告 (1):
  1. 发现 3 个近似常数特征: ['feature_1', 'feature_2', 'feature_3']

💡 建议 (1):
  1. 建议移除这些常数特征以提高训练效率
============================================================

⚖️ 增强样本权重计算成功，权重范围: [0.1234, 3.4567]
```

### 性能提升

- **训练效率**：通过权重引导，模型更快收敛
- **预测精度**：重点学习关键样本，提高泛化能力
- **稳定性**：数据质量监控减少训练失败

## 🛠️ 高级配置

### 不同市场条件配置

```python
# 牛市：重视趋势延续
bull_config = get_enhanced_weighting_config('bull_market', 'medium_term')

# 熊市：重视反转信号
bear_config = get_enhanced_weighting_config('bear_market', 'medium_term')

# 震荡市：平衡各因素
sideways_config = get_enhanced_weighting_config('sideways_market', 'medium_term')
```

### 不同时间框架配置

```python
# 短期交易：更快衰减，更敏感
short_config = get_enhanced_weighting_config('normal', 'short_term')

# 长期交易：更慢衰减，更稳定
long_config = get_enhanced_weighting_config('normal', 'long_term')
```

## 🔧 故障排除

### 常见问题

1. **权重计算失败**
   - 检查数据是否包含必要列 (OHLCV)
   - 确认数据格式正确

2. **数据质量检查失败**
   - 检查特征和目标变量的数据类型
   - 确认没有严重的数据质量问题

3. **权重效果不明显**
   - 调整权重策略参数
   - 尝试不同的组合方法

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('src.core.enhanced_sample_weighting').setLevel(logging.DEBUG)
logging.getLogger('src.core.training_data_quality_monitor').setLevel(logging.DEBUG)

# 验证配置
from config.enhanced_sample_weighting_config import validate_enhanced_weighting_config
is_valid, errors = validate_enhanced_weighting_config(your_config)
if not is_valid:
    print("配置错误:", errors)
```

## 📚 参考资料

- [动态样本权重原理](docs/dynamic_sample_weighting_guide.md)
- [数据质量最佳实践](docs/data_quality_best_practices.md)
- [配置示例](config/enhanced_sample_weighting_config.py)
