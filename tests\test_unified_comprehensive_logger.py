#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 统一综合日志系统测试用例
测试新的统一综合日志系统的各项功能
"""

import os
import sys
import unittest
import tempfile
import shutil
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.unified_comprehensive_logger import (
    UnifiedComprehensiveLogger,
    get_unified_comprehensive_logger,
    reset_comprehensive_logger
)
from src.core.logger_compatibility_layer import (
    get_unified_trade_logger,
    log_prediction_context,
    log_trade_settlement,
    initialize_loggers
)
from src.core.data_migration_manager import DataMigrationManager


class TestUnifiedComprehensiveLogger(unittest.TestCase):
    """统一综合日志系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.test_log_dir = os.path.join(self.temp_dir, "test_comprehensive_logs")
        
        # 重置全局实例
        reset_comprehensive_logger()
        
        # 创建测试用的日志系统
        self.logger = UnifiedComprehensiveLogger(
            base_log_dir=self.test_log_dir,
            queue_maxsize=100,
            batch_size=5,
            flush_interval=0.1,
            auto_start=True
        )
    
    def tearDown(self):
        """测试后清理"""
        # 停止日志系统
        if hasattr(self, 'logger'):
            self.logger.stop()
        
        # 重置全局实例
        reset_comprehensive_logger()
        
        # 清理临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_basic_trade_logging(self):
        """测试基础交易日志记录"""
        # 记录开仓
        trade_id = self.logger.log_trade_opened(
            target_name="TestStrategy",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=10.0,
            payout_ratio=0.85
        )
        
        self.assertIsNotNone(trade_id)
        self.assertIn("TestStrategy", trade_id)
        
        # 记录平仓
        success = self.logger.log_trade_closed(
            trade_id=trade_id,
            exit_price=51000.0,
            result="WIN",
            exit_reason="expired"
        )
        
        self.assertTrue(success)
        
        # 等待异步写入完成
        time.sleep(0.5)
        
        # 验证文件是否创建
        trades_dir = Path(self.test_log_dir) / "trades"
        self.assertTrue(trades_dir.exists())
        
        # 查找CSV文件
        csv_files = list(trades_dir.rglob("*.csv"))
        self.assertGreater(len(csv_files), 0)
    
    def test_prediction_context_logging(self):
        """测试预测上下文日志记录"""
        signal_data = {
            'signal_type': 'UP',
            'signal_strength': 0.85,
            'avg_up_prob': 0.75,
            'avg_down_prob': 0.25
        }
        
        market_data = {
            'current_price': 50000.0,
            'last_kline_close': 49950.0
        }
        
        model_data = {
            'model_type': 'LightGBM',
            'feature_names': ['rsi', 'macd', 'volume'],
            'feature_values': [65.5, 0.02, 1000000]
        }
        
        filter_data = {
            'trend_signal': 1,
            'volatility_level': 2,
            'atr_percent': 2.5
        }
        
        success = self.logger.log_prediction_context(
            target_name="TestStrategy",
            symbol="BTCUSDT",
            signal_data=signal_data,
            market_data=market_data,
            model_data=model_data,
            filter_data=filter_data
        )
        
        self.assertTrue(success)
        
        # 等待异步写入完成
        time.sleep(0.5)
        
        # 验证文件是否创建
        contexts_dir = Path(self.test_log_dir) / "contexts"
        self.assertTrue(contexts_dir.exists())
        
        # 查找CSV文件
        csv_files = list(contexts_dir.rglob("*.csv"))
        self.assertGreater(len(csv_files), 0)
    
    def test_complete_trade_logging(self):
        """测试完整交易日志记录"""
        trade_data = {
            'trade_id': 'test_complete_trade_001',
            'entry_timestamp': datetime.now().isoformat(),
            'exit_timestamp': datetime.now().isoformat(),
            'target_name': 'CompleteTestStrategy',
            'symbol': 'BTCUSDT',
            'direction': 'SHORT',
            'entry_price': 50000.0,
            'exit_price': 49500.0,
            'amount': 15.0,
            'payout_ratio': 0.85,
            'result': 'WIN',
            'profit_loss': 12.75,
            'exit_reason': 'expired'
        }
        
        context_data = {
            'timestamp': datetime.now().isoformat(),
            'target_name': 'CompleteTestStrategy',
            'symbol': 'BTCUSDT',
            'signal_type': 'DOWN',
            'signal_strength': 0.9
        }
        
        success = self.logger.log_complete_trade(trade_data, context_data)
        self.assertTrue(success)
        
        # 等待异步写入完成
        time.sleep(0.5)
        
        # 验证统计信息
        stats = self.logger.get_statistics()
        self.assertGreater(stats['basic_stats']['total_entries'], 0)
        self.assertGreater(stats['basic_stats']['total_exits'], 0)
        self.assertGreater(stats['basic_stats']['total_contexts'], 0)
    
    def test_failure_analysis(self):
        """测试失败案例分析"""
        # 记录一些失败交易
        for i in range(3):
            trade_data = {
                'trade_id': f'failed_trade_{i}',
                'entry_timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
                'exit_timestamp': datetime.now().isoformat(),
                'target_name': f'FailStrategy_{i}',
                'symbol': 'BTCUSDT',
                'direction': 'LONG',
                'entry_price': 50000.0 + i * 100,
                'exit_price': 49000.0 + i * 50,
                'amount': 10.0,
                'payout_ratio': 0.85,
                'result': 'LOSS',
                'profit_loss': -10.0,
                'exit_reason': 'expired'
            }
            
            self.logger.log_complete_trade(trade_data)
        
        # 等待异步写入完成
        time.sleep(0.5)
        
        # 执行失败案例分析
        analysis_result = self.logger.analyze_failures(days_back=1)
        
        # 验证分析结果
        self.assertIsNotNone(analysis_result)
        if 'basic_statistics' in analysis_result:
            self.assertGreater(analysis_result['basic_statistics']['failed_trades'], 0)
    
    def test_data_loading(self):
        """测试数据加载功能"""
        # 先记录一些数据
        trade_id = self.logger.log_trade_opened(
            target_name="LoadTestStrategy",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=10.0
        )
        
        self.logger.log_trade_closed(
            trade_id=trade_id,
            exit_price=51000.0,
            result="WIN"
        )
        
        # 等待异步写入完成
        time.sleep(0.5)
        
        # 加载交易数据
        trades_df = self.logger.load_trade_logs()
        
        if trades_df is not None:
            self.assertGreater(len(trades_df), 0)
            self.assertIn('trade_id', trades_df.columns)
            self.assertIn('target_name', trades_df.columns)
    
    def test_storage_statistics(self):
        """测试存储统计功能"""
        # 记录一些数据
        for i in range(5):
            trade_id = self.logger.log_trade_opened(
                target_name=f"StatsTestStrategy_{i}",
                symbol="BTCUSDT",
                direction="LONG",
                entry_price=50000.0 + i * 100,
                amount=10.0
            )
            
            self.logger.log_trade_closed(
                trade_id=trade_id,
                exit_price=51000.0 + i * 100,
                result="WIN" if i % 2 == 0 else "LOSS"
            )
        
        # 等待异步写入完成
        time.sleep(0.5)
        
        # 获取存储统计
        storage_stats = self.logger.storage_manager.get_storage_statistics()
        
        self.assertIn('layers', storage_stats)
        self.assertIn('trades', storage_stats['layers'])
        
        # 验证统计信息
        trades_stats = storage_stats['layers']['trades']
        self.assertGreaterEqual(trades_stats['total_files'], 0)


class TestCompatibilityLayer(unittest.TestCase):
    """兼容层测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        reset_comprehensive_logger()
    
    def tearDown(self):
        """测试后清理"""
        reset_comprehensive_logger()
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_unified_trade_logger_compatibility(self):
        """测试统一交易日志记录器兼容性"""
        # 使用兼容接口
        logger = get_unified_trade_logger()
        
        # 记录开仓
        trade_id = logger.record_trade_entry(
            target_name="CompatTestStrategy",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=10.0
        )
        
        self.assertIsNotNone(trade_id)
        
        # 记录平仓
        success = logger.record_trade_exit(
            trade_id=trade_id,
            exit_price=51000.0,
            result="WIN"
        )
        
        self.assertTrue(success)
        
        # 获取统计信息
        stats = logger.get_statistics()
        self.assertIn('total_entries', stats)
        self.assertIn('total_exits', stats)
    
    def test_analysis_logger_compatibility(self):
        """测试分析日志记录器兼容性"""
        # 初始化日志记录器
        result = initialize_loggers()
        self.assertTrue(result)
        
        # 测试预测上下文记录
        signal_data = {'signal_type': 'UP', 'signal_strength': 0.8}
        market_data = {'current_price': 50000.0}
        model_data = {'model_type': 'LightGBM'}
        filter_data = {'trend_signal': 1}
        
        success = log_prediction_context(
            target_name="CompatTestStrategy",
            symbol="BTCUSDT",
            signal_data=signal_data,
            market_data=market_data,
            model_data=model_data,
            filter_data=filter_data
        )
        
        # 由于使用了兼容层，应该返回成功
        self.assertTrue(success)


class MockTradeObject:
    """模拟交易对象"""
    
    def __init__(self):
        self.trade_id = "mock_trade_123"
        self.direction = "LONG"
        self.entry_price = 50000.0
        self.exit_price = 51000.0
        self.amount_staked = 10.0
        self.entry_time = datetime.now()
        self.status = "WON"
        self.profit_loss = 8.5
        self.PAYOUT_RATIO = 0.85
        self.CONTRACT_DURATION_MINUTES = 30
        self.target_name = "MockStrategy"
        self.symbol = "BTCUSDT"


class TestDataMigration(unittest.TestCase):
    """数据迁移测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模拟的旧数据结构
        self.old_trading_logs = os.path.join(self.temp_dir, "trading_logs_unified")
        self.old_analysis_logs = os.path.join(self.temp_dir, "analysis_logs")
        self.new_comprehensive_logs = os.path.join(self.temp_dir, "comprehensive_logs")
        
        # 创建模拟数据
        self._create_mock_old_data()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _create_mock_old_data(self):
        """创建模拟的旧数据"""
        # 创建旧交易日志目录和文件
        os.makedirs(os.path.join(self.old_trading_logs, "2025", "07"), exist_ok=True)
        
        # 创建模拟交易数据
        import pandas as pd
        mock_trades = pd.DataFrame({
            'trade_id': ['trade_001', 'trade_002'],
            'entry_timestamp': [datetime.now().isoformat(), datetime.now().isoformat()],
            'target_name': ['MockStrategy1', 'MockStrategy2'],
            'symbol': ['BTCUSDT', 'BTCUSDT'],
            'direction': ['LONG', 'SHORT'],
            'entry_price': [50000.0, 49000.0],
            'amount': [10.0, 15.0],
            'result': ['WIN', 'LOSS']
        })
        
        trades_file = os.path.join(self.old_trading_logs, "2025", "07", "trades_2025-07-15.csv")
        mock_trades.to_csv(trades_file, index=False)
        
        # 创建旧分析日志目录和文件
        os.makedirs(self.old_analysis_logs, exist_ok=True)
        
        # 创建模拟预测上下文数据
        mock_contexts = pd.DataFrame({
            'timestamp': [datetime.now().isoformat(), datetime.now().isoformat()],
            'target_name': ['MockStrategy1', 'MockStrategy2'],
            'symbol': ['BTCUSDT', 'BTCUSDT'],
            'signal_type': ['UP', 'DOWN']
        })
        
        context_file = os.path.join(self.old_analysis_logs, "prediction_context.csv")
        mock_contexts.to_csv(context_file, index=False)
    
    def test_migration_manager(self):
        """测试数据迁移管理器"""
        migration_manager = DataMigrationManager(
            old_trading_logs_dir=self.old_trading_logs,
            old_analysis_logs_dir=self.old_analysis_logs,
            new_comprehensive_logs_dir=self.new_comprehensive_logs
        )
        
        # 执行试运行迁移
        result = migration_manager.migrate_all_data(
            backup_old_data=False,
            dry_run=True
        )
        
        self.assertIn('migration_summary', result)
        
        # 执行实际迁移
        result = migration_manager.migrate_all_data(
            backup_old_data=False,
            dry_run=False
        )
        
        self.assertIn('migration_summary', result)
        self.assertEqual(result.get('migration_status'), 'completed')
        
        # 验证迁移结果
        verification_result = migration_manager.verify_migration()
        self.assertIn('overall_status', verification_result)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
