#!/usr/bin/env python3
"""
元模型上涨偏向修复脚本
解决元模型77%上涨机会错失问题

问题诊断：
- Class_1 recall = 23.1% (77%上涨机会被错过)
- Class_0 recall = 83.8% (过度偏向下跌)
- LogLoss = 0.6912 (概率校准不佳)

解决方案：
1. 调整类别权重 (6倍上涨权重)
2. 增强上涨信号特征工程
3. 优化样本权重策略
4. 改进概率校准
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
# 避免导入问题，只导入必要的模块

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_current_meta_model_bias():
    """分析当前元模型的偏向问题"""
    logger.info("🔍 分析当前元模型偏向问题...")
    
    # 检查元模型配置
    meta_config = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', None)
    logger.info(f"当前元模型类别权重: {meta_config}")
    
    # 检查基础模型配置
    up_config = config.get_target_config('BTC_15m_UP')
    down_config = config.get_target_config('BTC_15m_DOWN')
    
    up_weight = up_config.get('class_weight', {})
    down_weight = down_config.get('class_weight', {})
    
    logger.info(f"UP模型类别权重: {up_weight}")
    logger.info(f"DOWN模型类别权重: {down_weight}")
    
    return {
        'meta_weight': meta_config,
        'up_weight': up_weight,
        'down_weight': down_weight
    }

def create_enhanced_bullish_features(oof_data_df):
    """创建增强的上涨偏向特征"""
    logger.info("🚀 创建增强的上涨偏向特征...")
    
    enhanced_df = oof_data_df.copy()
    
    # 1. 上涨动量特征
    if 'oof_proba_BTC_15m_UP_p_up' in enhanced_df.columns:
        up_prob = enhanced_df['oof_proba_BTC_15m_UP_p_up']
        
        # 上涨动量强度
        enhanced_df['bullish_momentum_strength'] = np.where(
            up_prob > 0.6, 
            (up_prob - 0.6) * 2.5,  # 放大上涨信号
            0
        )
        
        # 上涨信心指数
        enhanced_df['bullish_confidence_index'] = np.maximum(
            up_prob - 0.5, 0
        ) * 2.0
    
    # 2. 概率差异增强
    if ('oof_proba_BTC_15m_UP_p_up' in enhanced_df.columns and 
        'oof_proba_BTC_15m_DOWN_p_down' in enhanced_df.columns):
        
        up_prob = enhanced_df['oof_proba_BTC_15m_UP_p_up']
        down_prob = enhanced_df['oof_proba_BTC_15m_DOWN_p_down']
        
        # 增强的概率差异（上涨偏向）
        prob_diff = up_prob - down_prob
        enhanced_df['enhanced_bullish_diff'] = np.where(
            prob_diff > 0,
            prob_diff * 1.5,  # 放大正向差异
            prob_diff * 0.8   # 缩小负向差异
        )
        
        # 上涨优势指标
        enhanced_df['bullish_advantage'] = np.maximum(
            (up_prob - down_prob + 0.1), 0
        )
    
    # 3. 全局市场状态调整
    if 'global_ema_long' in enhanced_df.columns:
        # 趋势上涨时增强上涨信号
        enhanced_df['trend_adjusted_bullish'] = np.where(
            enhanced_df['global_ema_long'] > 0,
            enhanced_df.get('oof_proba_BTC_15m_UP_p_up', 0.5) * 1.2,
            enhanced_df.get('oof_proba_BTC_15m_UP_p_up', 0.5)
        )
    
    logger.info(f"增强特征创建完成，新增 {len(enhanced_df.columns) - len(oof_data_df.columns)} 个特征")
    return enhanced_df

def optimize_sample_weights_for_bullish_signals(y_series, enhanced_df):
    """优化样本权重以增强上涨信号学习"""
    logger.info("⚖️ 优化样本权重以增强上涨信号学习...")
    
    sample_weights = np.ones(len(y_series))
    
    # 1. 基础类别权重
    class_1_mask = (y_series == 1)  # 上涨样本
    class_0_mask = (y_series == 0)  # 下跌样本
    
    # 给上涨样本更高权重
    sample_weights[class_1_mask] = 3.0
    sample_weights[class_0_mask] = 1.0
    
    # 2. 质量权重：高质量上涨信号额外加权
    if 'bullish_momentum_strength' in enhanced_df.columns:
        high_quality_bullish = (
            class_1_mask & 
            (enhanced_df['bullish_momentum_strength'] > 0.2)
        )
        sample_weights[high_quality_bullish] *= 1.5
    
    # 3. 趋势一致性权重
    if 'trend_adjusted_bullish' in enhanced_df.columns:
        trend_consistent_bullish = (
            class_1_mask & 
            (enhanced_df['trend_adjusted_bullish'] > 0.6)
        )
        sample_weights[trend_consistent_bullish] *= 1.3
    
    logger.info(f"样本权重优化完成:")
    logger.info(f"  上涨样本平均权重: {sample_weights[class_1_mask].mean():.3f}")
    logger.info(f"  下跌样本平均权重: {sample_weights[class_0_mask].mean():.3f}")
    logger.info(f"  权重比例: {sample_weights[class_1_mask].mean() / sample_weights[class_0_mask].mean():.2f}:1")
    
    return sample_weights

def validate_bias_fix_effectiveness(enhanced_df, y_series, sample_weights):
    """验证偏向修复的有效性"""
    logger.info("✅ 验证偏向修复的有效性...")
    
    # 1. 样本分布分析
    class_counts = pd.Series(y_series).value_counts()
    logger.info(f"类别分布: {dict(class_counts)}")
    
    # 2. 权重分布分析
    class_1_mask = (y_series == 1)
    class_0_mask = (y_series == 0)
    
    weighted_class_1 = sample_weights[class_1_mask].sum()
    weighted_class_0 = sample_weights[class_0_mask].sum()
    
    logger.info(f"加权后类别分布:")
    logger.info(f"  Class_1 (上涨): {weighted_class_1:.1f}")
    logger.info(f"  Class_0 (下跌): {weighted_class_0:.1f}")
    logger.info(f"  加权比例: {weighted_class_1/weighted_class_0:.2f}:1")
    
    # 3. 特征质量分析
    bullish_features = [col for col in enhanced_df.columns if 'bullish' in col.lower()]
    logger.info(f"上涨偏向特征数量: {len(bullish_features)}")
    
    for feature in bullish_features[:3]:  # 显示前3个特征的统计
        if feature in enhanced_df.columns:
            feature_stats = enhanced_df[feature].describe()
            logger.info(f"  {feature}: mean={feature_stats['mean']:.3f}, std={feature_stats['std']:.3f}")
    
    return True

def main():
    """主函数：执行元模型上涨偏向修复"""
    logger.info("🎯 开始执行元模型上涨偏向修复...")
    
    try:
        # 1. 分析当前偏向问题
        bias_analysis = analyze_current_meta_model_bias()
        
        # 2. 检查配置修改是否生效
        current_meta_weight = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', None)
        if isinstance(current_meta_weight, dict) and current_meta_weight.get(1, 1) >= 6:
            logger.info("✅ 元模型类别权重已修改为上涨偏向")
        else:
            logger.warning("⚠️ 元模型类别权重未正确修改，请检查config.py")
        
        # 3. 检查特征工程配置
        feature_config = getattr(config, 'META_MODEL_FEATURE_ENGINEERING_CONFIG', {})
        logger.info(f"特征工程配置: {feature_config}")
        if feature_config.get('enable_bullish_bias_features', False):
            logger.info("✅ 上涨偏向特征工程已启用")
        else:
            logger.warning("⚠️ 上涨偏向特征工程未启用，请检查config.py")
        
        logger.info("🎯 元模型上涨偏向修复配置检查完成！")
        logger.info("📋 下一步操作建议：")
        logger.info("   1. 重新训练元模型")
        logger.info("   2. 验证新模型的Class_1 recall是否提升")
        logger.info("   3. 监控实际交易中的上涨信号捕获率")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 元模型偏向修复失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 元模型上涨偏向修复脚本执行成功！")
    else:
        print("\n❌ 元模型上涨偏向修复脚本执行失败！")
