# websocket_initializer.py
"""
WebSocket初始化器 - 专门解决事件循环冲突问题

这个模块提供了一个专门的WebSocket初始化器，用于在复杂的异步环境中
安全地初始化ThreadedWebsocketManager，避免与现有事件循环的冲突。

主要特性：
- 独立线程初始化，完全隔离事件循环
- 智能冲突检测和处理
- 模拟盘成功配置的复制
- 详细的诊断和日志记录
"""

import threading
import time
import logging
import traceback
import asyncio
from typing import Optional, Dict, Any
from binance import ThreadedWebsocketManager

logger = logging.getLogger(__name__)

class WebSocketInitializer:
    """
    WebSocket初始化器 - 解决事件循环冲突
    
    这个类专门用于在有事件循环冲突的环境中安全地初始化WebSocket连接。
    它使用独立线程和智能检测机制来避免冲突。
    """
    
    def __init__(self, proxy_url: str = 'http://127.0.0.1:7897'):
        self.proxy_url = proxy_url
        self.init_timeout = 30  # 初始化超时时间
        self.start_timeout = 15  # 启动超时时间
        
    def detect_event_loop_conflicts(self) -> Dict[str, Any]:
        """
        检测当前环境中的事件循环冲突
        
        Returns:
            Dict包含冲突检测结果和诊断信息
        """
        conflicts = {
            'has_running_loop': False,
            'loop_info': None,
            'thread_info': threading.current_thread().name,
            'recommendations': []
        }
        
        try:
            loop = asyncio.get_running_loop()
            conflicts['has_running_loop'] = True
            conflicts['loop_info'] = {
                'is_running': loop.is_running(),
                'is_closed': loop.is_closed(),
                'debug': loop.get_debug()
            }
            conflicts['recommendations'].append("使用独立线程初始化")
            logger.warning(f"检测到运行中的事件循环: {conflicts['loop_info']}")
            
        except RuntimeError:
            conflicts['recommendations'].append("可以使用标准初始化")
            logger.info("没有检测到运行中的事件循环")
            
        return conflicts
    
    def initialize_twm_safe(self) -> Optional[ThreadedWebsocketManager]:
        """
        安全地初始化ThreadedWebsocketManager
        
        使用独立线程初始化，避免事件循环冲突
        
        Returns:
            初始化成功的TWM实例，失败返回None
        """
        logger.info("开始安全TWM初始化流程")
        
        # 检测冲突
        conflicts = self.detect_event_loop_conflicts()
        logger.info(f"事件循环冲突检测结果: {conflicts}")
        
        # 使用独立线程初始化（无论是否有冲突，都使用这种方式确保稳定性）
        return self._initialize_in_thread()
    
    def _initialize_in_thread(self) -> Optional[ThreadedWebsocketManager]:
        """在独立线程中初始化TWM"""
        init_result = {
            "success": False, 
            "twm": None, 
            "error": None,
            "config_used": None
        }
        init_event = threading.Event()
        
        def _init_worker():
            """初始化工作线程"""
            try:
                import aiohttp
                
                logger.info("在独立线程中初始化TWM，使用模拟盘成功配置")
                
                # 使用模拟盘的成功配置
                session_timeout_config = aiohttp.ClientTimeout(
                    total=60,
                    connect=25,
                )

                session_init_params = {
                    "timeout": session_timeout_config,
                    "trust_env": True,
                    "connector": None,
                }

                twm_init_kwargs = {
                    "api_key": None,
                    "api_secret": None,
                    "requests_params": {
                        "timeout": session_timeout_config
                    },
                    "https_proxy": self.proxy_url,
                    "session_params": session_init_params
                }
                
                init_result["config_used"] = twm_init_kwargs.copy()
                init_result["config_used"]["session_params"] = "aiohttp.ClientTimeout配置"
                
                logger.info(f"TWM初始化配置: {init_result['config_used']}")
                
                # 创建TWM实例
                twm = ThreadedWebsocketManager(**twm_init_kwargs)
                
                init_result["success"] = True
                init_result["twm"] = twm
                logger.info("TWM在独立线程中创建成功")
                
            except Exception as e:
                init_result["error"] = str(e)
                logger.error(f"独立线程TWM初始化失败: {e}")
                traceback.print_exc()
            finally:
                init_event.set()
        
        # 启动初始化线程
        logger.info("启动TWM初始化线程")
        init_thread = threading.Thread(
            target=_init_worker, 
            name="TWM-Initializer",
            daemon=True
        )
        init_thread.start()
        
        # 等待初始化完成
        if init_event.wait(timeout=self.init_timeout):
            if init_result["success"]:
                logger.info("TWM独立线程初始化成功")
                return init_result["twm"]
            else:
                logger.error(f"TWM独立线程初始化失败: {init_result['error']}")
                return None
        else:
            logger.error(f"TWM独立线程初始化超时 ({self.init_timeout}秒)")
            return None
    
    def start_twm_safe(self, twm: ThreadedWebsocketManager) -> bool:
        """
        安全地启动ThreadedWebsocketManager
        
        Args:
            twm: 要启动的TWM实例
            
        Returns:
            启动是否成功
        """
        if not twm:
            logger.error("TWM实例为None，无法启动")
            return False
            
        if twm.is_alive():
            logger.info("TWM已经在运行中")
            return True
            
        logger.info("开始安全TWM启动流程")
        
        # 使用独立线程启动
        start_result = {"success": False, "error": None}
        start_event = threading.Event()
        
        def _start_worker():
            """启动工作线程"""
            try:
                twm.start()
                start_result["success"] = True
                logger.info("TWM在独立线程中启动成功")
            except Exception as e:
                start_result["error"] = str(e)
                logger.error(f"独立线程TWM启动失败: {e}")
                traceback.print_exc()
            finally:
                start_event.set()
        
        # 启动线程
        start_thread = threading.Thread(
            target=_start_worker,
            name="TWM-Starter", 
            daemon=True
        )
        start_thread.start()
        
        # 等待启动完成
        if start_event.wait(timeout=self.start_timeout):
            if start_result["success"]:
                # 等待TWM完全初始化
                for i in range(20):  # 最多等待10秒
                    time.sleep(0.5)
                    if twm.is_alive():
                        logger.info(f"TWM启动并运行成功，耗时 {(i+1)*0.5:.1f} 秒")
                        return True
                
                logger.error("TWM启动后状态检查超时")
                return False
            else:
                logger.error(f"TWM启动失败: {start_result['error']}")
                return False
        else:
            logger.error(f"TWM启动超时 ({self.start_timeout}秒)")
            return False
    
    def initialize_and_start(self) -> Optional[ThreadedWebsocketManager]:
        """
        一站式初始化和启动TWM
        
        Returns:
            成功启动的TWM实例，失败返回None
        """
        logger.info("开始一站式TWM初始化和启动")
        
        # 初始化
        twm = self.initialize_twm_safe()
        if not twm:
            logger.error("TWM初始化失败")
            return None
        
        # 启动
        if self.start_twm_safe(twm):
            logger.info("TWM初始化和启动完成")
            return twm
        else:
            logger.error("TWM启动失败")
            return None


def create_websocket_initializer(proxy_url: str = 'http://127.0.0.1:7897') -> WebSocketInitializer:
    """
    创建WebSocket初始化器实例
    
    Args:
        proxy_url: 代理URL
        
    Returns:
        WebSocket初始化器实例
    """
    return WebSocketInitializer(proxy_url)


def diagnose_websocket_environment() -> Dict[str, Any]:
    """
    诊断WebSocket运行环境
    
    Returns:
        环境诊断信息
    """
    initializer = WebSocketInitializer()
    conflicts = initializer.detect_event_loop_conflicts()
    
    diagnosis = {
        'conflicts': conflicts,
        'thread_count': threading.active_count(),
        'current_thread': threading.current_thread().name,
        'recommendations': []
    }
    
    if conflicts['has_running_loop']:
        diagnosis['recommendations'].extend([
            "使用WebSocketInitializer进行安全初始化",
            "考虑在独立进程中运行WebSocket组件",
            "检查是否有其他异步组件冲突"
        ])
    else:
        diagnosis['recommendations'].append("可以使用标准初始化方式")
    
    return diagnosis
