#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级特征工程模块
实现从"多"到"精"的特征工程优化：
1. 高阶特征（速度、加速度信号）
2. 智能交互特征（强强联合）
3. 深化衍生品特征（聪明钱背离指标）
4. SHAP基础的精准特征选择
"""

import logging
import numpy as np
import pandas as pd
import traceback
from typing import Dict, List, Tuple, Optional, Any

logger = logging.getLogger(__name__)

class AdvancedFeatureEngineer:
    """高级特征工程器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化高级特征工程器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.feature_creation_stats = {}
        
        # 默认配置
        self.default_config = {
            'enable_higher_order_features': True,
            'enable_interaction_features': True,
            'enable_smart_money_features': True,
            'higher_order_smoothing': 3,  # 高阶特征平滑周期
            'interaction_threshold': 0.1,  # 交互特征相关性阈值
            'smart_money_lookback': 14,    # 聪明钱指标回看周期
        }
        
        # 合并配置
        self.effective_config = {**self.default_config, **self.config}
        
        logger.info(f"[AdvancedFeatureEngineer] 初始化完成，配置: {self.effective_config}")

    def calculate_higher_order_features(self, df: pd.DataFrame, target_name: str) -> pd.DataFrame:
        """
        计算高阶特征（速度、加速度信号）
        
        Args:
            df: 输入DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            包含高阶特征的DataFrame
        """
        if not self.effective_config.get('enable_higher_order_features', True):
            logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 高阶特征计算已禁用")
            return df
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 开始计算高阶特征...")
        
        df_out = df.copy()
        smoothing = self.effective_config.get('higher_order_smoothing', 3)
        
        # 需要计算高阶导数的关键指标
        key_indicators = {
            'RSI_14': {'name': 'RSI', 'neutral': 50.0},
            'MACD': {'name': 'MACD', 'neutral': 0.0},
            'MACD_histogram': {'name': 'MACD_hist', 'neutral': 0.0},
            'ADX': {'name': 'ADX', 'neutral': 25.0},
            'ATRr_14': {'name': 'ATR', 'neutral': None},
            'BB_position': {'name': 'BB_pos', 'neutral': 0.5},
            'WILLR_14': {'name': 'WILLR', 'neutral': -50.0}
        }
        
        created_features = []
        
        for indicator_col, info in key_indicators.items():
            if indicator_col not in df_out.columns:
                continue
                
            try:
                indicator_name = info['name']
                neutral_value = info['neutral']
                
                # 原始序列
                series = df_out[indicator_col].copy()
                
                # 🚀 一阶导数（速度）
                velocity_col = f"{indicator_name}_velocity"
                df_out[velocity_col] = series.diff()
                
                # 平滑处理
                if smoothing > 1:
                    df_out[velocity_col] = df_out[velocity_col].rolling(
                        window=smoothing, min_periods=1
                    ).mean()
                
                created_features.append(velocity_col)
                
                # 🚀 二阶导数（加速度）
                acceleration_col = f"{indicator_name}_acceleration"
                df_out[acceleration_col] = df_out[velocity_col].diff()
                
                # 平滑处理
                if smoothing > 1:
                    df_out[acceleration_col] = df_out[acceleration_col].rolling(
                        window=smoothing, min_periods=1
                    ).mean()
                
                created_features.append(acceleration_col)
                
                # 🚀 动量强度（距离中性值的加速度）
                if neutral_value is not None:
                    momentum_strength_col = f"{indicator_name}_momentum_strength"
                    distance_from_neutral = series - neutral_value
                    df_out[momentum_strength_col] = distance_from_neutral * df_out[acceleration_col]
                    created_features.append(momentum_strength_col)
                
                # 🚀 自适应速度（考虑当前位置的速度）
                if neutral_value is not None:
                    adaptive_velocity_col = f"{indicator_name}_adaptive_velocity"
                    position_factor = np.abs(series - neutral_value) / (np.abs(series - neutral_value).max() + 1e-8)
                    df_out[adaptive_velocity_col] = df_out[velocity_col] * position_factor
                    created_features.append(adaptive_velocity_col)
                
            except Exception as e:
                logger.warning(f"[AdvancedFeatureEngineer] ({target_name}) 计算 {indicator_col} 高阶特征失败: {e}")
        
        # 记录统计信息
        self.feature_creation_stats['higher_order'] = {
            'created_count': len(created_features),
            'created_features': created_features[:10],  # 只记录前10个
            'smoothing_applied': smoothing
        }
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 高阶特征计算完成，新增 {len(created_features)} 个特征")
        
        return df_out

    def calculate_interaction_features(self, df: pd.DataFrame, target_name: str) -> pd.DataFrame:
        """
        计算智能交互特征（强强联合）
        
        Args:
            df: 输入DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            包含交互特征的DataFrame
        """
        if not self.effective_config.get('enable_interaction_features', True):
            logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 交互特征计算已禁用")
            return df
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 开始计算智能交互特征...")
        
        df_out = df.copy()
        created_features = []
        
        try:
            # 🚀 1. 波动率-趋势交互：ADX * ATR
            if 'ADX' in df_out.columns and 'ATRr_14' in df_out.columns:
                volatility_trend_interaction = 'volatility_trend_strength'
                df_out[volatility_trend_interaction] = df_out['ADX'] * df_out['ATRr_14']
                created_features.append(volatility_trend_interaction)
                
                # 标准化版本
                volatility_trend_norm = 'volatility_trend_strength_norm'
                df_out[volatility_trend_norm] = (
                    (df_out['ADX'] / 100.0) * 
                    (df_out['ATRr_14'] / df_out['ATRr_14'].rolling(50).mean())
                )
                created_features.append(volatility_trend_norm)
            
            # 🚀 2. 量价交互：价格变化 * 成交量变化
            price_change_cols = [col for col in df_out.columns if col.startswith('price_change_')]
            volume_change_cols = [col for col in df_out.columns if col.startswith('volume_change_')]
            
            for price_col in price_change_cols[:3]:  # 限制数量
                for volume_col in volume_change_cols[:2]:
                    if price_col in df_out.columns and volume_col in df_out.columns:
                        interaction_name = f"price_volume_interaction_{price_col.split('_')[-1]}_{volume_col.split('_')[-1]}"
                        df_out[interaction_name] = df_out[price_col] * df_out[volume_col]
                        created_features.append(interaction_name)
            
            # 🚀 3. RSI-MACD动量共振
            if 'RSI_14' in df_out.columns and 'MACD' in df_out.columns:
                # RSI偏离中性值的程度
                rsi_deviation = np.abs(df_out['RSI_14'] - 50) / 50
                # MACD强度
                macd_strength = np.abs(df_out['MACD'])
                
                momentum_resonance = 'rsi_macd_momentum_resonance'
                df_out[momentum_resonance] = rsi_deviation * macd_strength
                created_features.append(momentum_resonance)
            
            # 🚀 4. 布林带-ATR压缩/扩张指标
            if 'BB_position' in df_out.columns and 'ATRr_14' in df_out.columns:
                # 布林带位置的极端程度
                bb_extremity = np.abs(df_out['BB_position'] - 0.5) * 2  # 0-1范围
                # ATR相对强度
                atr_relative = df_out['ATRr_14'] / df_out['ATRr_14'].rolling(20).mean()
                
                compression_expansion = 'bb_atr_compression_expansion'
                df_out[compression_expansion] = bb_extremity * atr_relative
                created_features.append(compression_expansion)
            
            # 🚀 5. 多时间框架RSI背离
            rsi_cols = [col for col in df_out.columns if 'RSI' in col and '_' in col]
            if len(rsi_cols) >= 2:
                for i, rsi1 in enumerate(rsi_cols[:3]):
                    for rsi2 in rsi_cols[i+1:3]:
                        if rsi1 != rsi2:
                            divergence_name = f"rsi_divergence_{rsi1.split('_')[-1]}_{rsi2.split('_')[-1]}"
                            df_out[divergence_name] = df_out[rsi1] - df_out[rsi2]
                            created_features.append(divergence_name)
            
            # 🚀 6. 成交量-波动率效率指标
            if 'volume_vs_avg' in df_out.columns and 'ATRr_14' in df_out.columns:
                volume_efficiency = 'volume_volatility_efficiency'
                # 成交量效率 = 波动率 / 成交量，衡量单位成交量产生的波动
                df_out[volume_efficiency] = (
                    df_out['ATRr_14'] / (df_out['volume_vs_avg'] + 1e-8)
                )
                created_features.append(volume_efficiency)
            
        except Exception as e:
            logger.error(f"[AdvancedFeatureEngineer] ({target_name}) 交互特征计算失败: {e}")
            logger.debug(traceback.format_exc())
        
        # 记录统计信息
        self.feature_creation_stats['interaction'] = {
            'created_count': len(created_features),
            'created_features': created_features,
        }
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 交互特征计算完成，新增 {len(created_features)} 个特征")
        
        return df_out

    def calculate_smart_money_features(self, df: pd.DataFrame, target_name: str) -> pd.DataFrame:
        """
        计算聪明钱背离指标和深化衍生品特征
        
        Args:
            df: 输入DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            包含聪明钱特征的DataFrame
        """
        if not self.effective_config.get('enable_smart_money_features', True):
            logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 聪明钱特征计算已禁用")
            return df
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 开始计算聪明钱特征...")
        
        df_out = df.copy()
        created_features = []
        lookback = self.effective_config.get('smart_money_lookback', 14)
        
        try:
            # 检查必要的衍生品数据列
            top_trader_cols = [col for col in df_out.columns if 'top_longshort' in col.lower()]
            global_ratio_cols = [col for col in df_out.columns if 'global_longshort' in col.lower()]
            funding_rate_cols = [col for col in df_out.columns if 'funding_rate' in col.lower()]
            
            # 🚀 1. 核心聪明钱背离指标
            if top_trader_cols and global_ratio_cols:
                top_trader_col = top_trader_cols[0]  # 使用第一个找到的列
                global_ratio_col = global_ratio_cols[0]
                
                # 基础背离指标
                smart_money_divergence = 'smart_money_divergence'
                df_out[smart_money_divergence] = df_out[top_trader_col] - df_out[global_ratio_col]
                created_features.append(smart_money_divergence)
                
                # 背离强度（标准化）
                divergence_strength = 'smart_money_divergence_strength'
                divergence_std = df_out[smart_money_divergence].rolling(lookback).std()
                df_out[divergence_strength] = df_out[smart_money_divergence] / (divergence_std + 1e-8)
                created_features.append(divergence_strength)
                
                # 背离趋势（背离的变化率）
                divergence_trend = 'smart_money_divergence_trend'
                df_out[divergence_trend] = df_out[smart_money_divergence].diff()
                created_features.append(divergence_trend)
                
                # 背离持续性（连续同向背离的天数）
                divergence_persistence = 'smart_money_divergence_persistence'
                divergence_sign = np.sign(df_out[smart_money_divergence])
                persistence = (divergence_sign.groupby((divergence_sign != divergence_sign.shift()).cumsum()).cumcount() + 1)
                df_out[divergence_persistence] = persistence * divergence_sign
                created_features.append(divergence_persistence)
                
                logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 聪明钱背离指标创建完成")
            
            # 🚀 2. 资金费率与价格背离
            if funding_rate_cols and 'price_change_1p' in df_out.columns:
                funding_col = funding_rate_cols[0]
                
                # 资金费率-价格背离
                funding_price_divergence = 'funding_price_divergence'
                funding_change = df_out[funding_col].diff()
                price_change = df_out['price_change_1p']
                
                # 当资金费率上升但价格下跌时为正背离（看涨信号）
                df_out[funding_price_divergence] = funding_change * (-price_change)
                created_features.append(funding_price_divergence)
                
                # 资金费率极值指标
                funding_extremity = 'funding_rate_extremity'
                funding_percentile = df_out[funding_col].rolling(lookback*2).rank(pct=True)
                df_out[funding_extremity] = np.where(
                    funding_percentile > 0.9, 1,  # 极高资金费率
                    np.where(funding_percentile < 0.1, -1, 0)  # 极低资金费率
                )
                created_features.append(funding_extremity)
            
            # 🚀 3. 多空比变化率与价格变化率的比值
            if top_trader_cols and 'price_change_1p' in df_out.columns:
                top_trader_col = top_trader_cols[0]
                
                # 多空比变化率
                longshort_change_rate = df_out[top_trader_col].pct_change()
                price_change_rate = df_out['price_change_1p']
                
                # 背离比率（多空比变化率 / 价格变化率）
                divergence_ratio = 'longshort_price_divergence_ratio'
                df_out[divergence_ratio] = longshort_change_rate / (price_change_rate + 1e-8)
                created_features.append(divergence_ratio)
                
                # 背离信号强度
                divergence_signal = 'longshort_price_divergence_signal'
                # 当价格上涨但多空比下降时为负信号，反之为正信号
                df_out[divergence_signal] = np.sign(price_change_rate) * np.sign(-longshort_change_rate)
                created_features.append(divergence_signal)
            
            # 🚀 4. 聪明钱情绪指数
            if len(created_features) >= 3:
                # 综合聪明钱情绪指数
                smart_money_sentiment = 'smart_money_sentiment_index'
                
                # 使用已创建的关键指标
                sentiment_components = []
                if 'smart_money_divergence_strength' in df_out.columns:
                    sentiment_components.append(df_out['smart_money_divergence_strength'])
                if 'funding_price_divergence' in df_out.columns:
                    sentiment_components.append(df_out['funding_price_divergence'])
                if 'longshort_price_divergence_signal' in df_out.columns:
                    sentiment_components.append(df_out['longshort_price_divergence_signal'])
                
                if sentiment_components:
                    # 标准化并平均
                    normalized_components = []
                    for component in sentiment_components:
                        normalized = (component - component.rolling(lookback).mean()) / (component.rolling(lookback).std() + 1e-8)
                        normalized_components.append(normalized)
                    
                    df_out[smart_money_sentiment] = np.mean(normalized_components, axis=0)
                    created_features.append(smart_money_sentiment)
            
        except Exception as e:
            logger.error(f"[AdvancedFeatureEngineer] ({target_name}) 聪明钱特征计算失败: {e}")
            logger.debug(traceback.format_exc())
        
        # 记录统计信息
        self.feature_creation_stats['smart_money'] = {
            'created_count': len(created_features),
            'created_features': created_features,
            'lookback_period': lookback
        }
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 聪明钱特征计算完成，新增 {len(created_features)} 个特征")
        
        return df_out

    def apply_advanced_feature_engineering(self, df: pd.DataFrame, target_name: str) -> pd.DataFrame:
        """
        应用完整的高级特征工程流程
        
        Args:
            df: 输入DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            包含所有高级特征的DataFrame
        """
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 开始高级特征工程...")
        
        original_feature_count = len(df.columns)
        df_result = df.copy()
        
        # 1. 高阶特征
        df_result = self.calculate_higher_order_features(df_result, target_name)
        
        # 2. 交互特征
        df_result = self.calculate_interaction_features(df_result, target_name)
        
        # 3. 聪明钱特征
        df_result = self.calculate_smart_money_features(df_result, target_name)
        
        # 统计总结
        final_feature_count = len(df_result.columns)
        new_features_count = final_feature_count - original_feature_count
        
        total_stats = {
            'original_features': original_feature_count,
            'final_features': final_feature_count,
            'new_features': new_features_count,
            'feature_categories': self.feature_creation_stats
        }
        
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 高级特征工程完成")
        logger.info(f"[AdvancedFeatureEngineer] ({target_name}) 特征数量: {original_feature_count} -> {final_feature_count} (+{new_features_count})")
        
        return df_result

    def get_feature_creation_stats(self) -> Dict[str, Any]:
        """获取特征创建统计信息"""
        return self.feature_creation_stats.copy()


# 便捷函数
def apply_advanced_feature_engineering(df: pd.DataFrame, 
                                     target_config: Dict[str, Any], 
                                     target_name: str) -> pd.DataFrame:
    """
    应用高级特征工程的便捷函数
    
    Args:
        df: 输入DataFrame
        target_config: 目标配置
        target_name: 目标名称
        
    Returns:
        包含高级特征的DataFrame
    """
    # 提取高级特征工程配置
    advanced_config = target_config.get('advanced_feature_engineering', {})
    
    # 创建特征工程器
    engineer = AdvancedFeatureEngineer(advanced_config)
    
    # 应用特征工程
    return engineer.apply_advanced_feature_engineering(df, target_name)
