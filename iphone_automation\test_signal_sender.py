#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone自动交易信号发送测试器
用于测试完整版ZXTouch的自动化交易功能
"""

import requests
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def send_trade_signal(signal_type="UP", amount=10):
    """发送交易信号到模拟盘"""

    url = "http://127.0.0.1:5008/signal"

    signal_data = {
        "signal_type": signal_type,
        "amount": amount,
        "target_name": "ZXTOUCH_TEST",  # 这个会触发iPhone自动化
        "symbol": "BTCUSDT"  # 必须提供symbol字段
    }

    try:
        print(f"🚀 发送{signal_type}信号 - {amount} USDT")
        print(f"📱 目标: iPhone7自动交易")
        print(f"🎯 target_name: {signal_data['target_name']}")
        print(f"⏰ 时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📡 URL: {url}")

        response = requests.post(url, json=signal_data, timeout=10)

        print(f"📊 响应状态码: {response.status_code}")
        if hasattr(response, 'text'):
            print(f"📄 响应内容: {response.text}")

        if response.status_code == 200:
            print("✅ 信号发送成功!")
            print("📲 iPhone7应该开始执行交易...")
            print("💡 请观察模拟盘控制台输出和iPhone7屏幕")
            return True
        else:
            print(f"❌ 发送失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"❌ 错误详情: {error_data}")
            except:
                print(f"❌ 响应内容: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接模拟盘!")
        print("💡 请先运行: python SimMain.py --port 5008")
        print("💡 确保模拟盘正在监听5008端口")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时!")
        print("💡 模拟盘可能响应缓慢，请检查模拟盘状态")
        return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        print(f"❌ 详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    
    print("=" * 50)
    print("📱 iPhone7自动交易信号测试器")
    print("=" * 50)
    
    if len(sys.argv) == 3:
        # 命令行模式
        signal_type = sys.argv[1].upper()
        amount = float(sys.argv[2])
        send_trade_signal(signal_type, amount)
    else:
        # 交互模式
        while True:
            print("\n📋 选择操作:")
            print("1. 发送上涨信号 (UP)")
            print("2. 发送下跌信号 (DOWN)")
            print("3. 自定义信号")
            print("0. 退出")
            
            choice = input("\n请选择 (0-3): ").strip()
            
            if choice == "0":
                print("👋 退出")
                break
            elif choice == "1":
                amount = float(input("交易金额 (5-250): ") or "10")
                send_trade_signal("UP", amount)
            elif choice == "2":
                amount = float(input("交易金额 (5-250): ") or "10")
                send_trade_signal("DOWN", amount)
            elif choice == "3":
                signal_type = input("信号类型 (UP/DOWN): ").strip().upper()
                amount = float(input("交易金额 (5-250): ") or "10")
                send_trade_signal(signal_type, amount)
            else:
                print("❌ 无效选择")
            
            print("\n" + "-" * 30)

if __name__ == "__main__":
    main()
