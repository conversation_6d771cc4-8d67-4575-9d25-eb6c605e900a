# data_utils.py
import pandas as pd
import numpy as np
import math
import os
import json
import logging
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceRequestException
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import precision_recall_curve, roc_curve, f1_score, precision_score, recall_score, accuracy_score
import traceback
import config
import time
from datetime import datetime, timedelta, timezone
import pandas_ta as pta
import ssl
from ssl import SSLError

# 导入新的阈值优化系统
try:
    from .threshold_optimization import (
        ThresholdOptimizer,
        ThresholdMetadataManager,
        ThresholdApplicator,
        optimize_and_save_threshold,
        load_and_apply_threshold
    )
    THRESHOLD_OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    logging.warning(f"阈值优化系统导入失败: {e}")
    THRESHOLD_OPTIMIZATION_AVAILABLE = False

# 🚀 导入配置管理器 - 外部化硬编码参数
try:
    from .config_manager import (
        get_config_manager,
        get_data_processing_param,
        get_feature_engineering_param,
        get_constant
    )
    CONFIG_MANAGER_AVAILABLE = True
except ImportError as e:
    logging.warning(f"配置管理器导入失败: {e}")
    CONFIG_MANAGER_AVAILABLE = False

# 🚀 导入数据类型验证器 - 增强数据类型检查和转换
try:
    from .data_type_validator import (
        DataTypeValidator,
        validate_dataframe_types,
        validate_essential_columns
    )
    DATA_TYPE_VALIDATOR_AVAILABLE = True
except ImportError as e:
    logging.warning(f"数据类型验证器导入失败: {e}")
    DATA_TYPE_VALIDATOR_AVAILABLE = False

# 🚀 导入内存优化器 - 内存使用优化
try:
    from .memory_optimizer import (
        MemoryOptimizer,
        get_memory_optimizer,
        memory_optimized,
        optimize_dataframe_memory,
        monitor_memory_usage
    )
    MEMORY_OPTIMIZER_AVAILABLE = True
except ImportError as e:
    logging.warning(f"内存优化器导入失败: {e}")
    MEMORY_OPTIMIZER_AVAILABLE = False

# 🚀 导入错误处理器 - 完善的错误处理回退策略
try:
    from .error_handler import (
        ErrorHandler,
        get_error_handler,
        safe_execute,
        validate_data_safety,
        ModuleType,
        ErrorSeverity,
        handle_training_exception,
        get_enhanced_logger
    )
    ERROR_HANDLER_AVAILABLE = True
except ImportError as e:
    logging.warning(f"错误处理器导入失败: {e}")
    ERROR_HANDLER_AVAILABLE = False

# 配置日志
logger = logging.getLogger(__name__)

# 🎯 全局辅助函数：从全局配置读取市场状态自适应特征配置
def _get_market_state_config():
    """从全局config模块读取市场状态自适应特征配置"""
    try:
        import config
        market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
        return market_config
    except Exception as e:
        logger.warning(f"读取市场状态配置失败: {e}")
        return {}
if not logger.handlers:
    # 设置日志级别
    logger.setLevel(logging.INFO)

    # 创建控制台处理器，明确指定UTF-8编码
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(console_handler)

    # 可选：添加文件处理器，明确指定UTF-8编码
    try:
        file_handler = logging.FileHandler('data_utils.log', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except (PermissionError, IOError) as e:
        logger.warning(f"无法创建日志文件: {e}")




def create_meta_target_variable(df_full_hist_data, base_model_config, meta_target_suffix="_meta_target", drop_neutral_targets=False):
    """
    为元模型创建二分类目标变量 (明确上涨=1, 明确下跌=0)，移除中性样本。

    Args:
        df_full_hist_data (pd.DataFrame): 包含 'close' 列的K线数据。
        base_model_config (dict): 基础模型的配置字典，用于获取 prediction_periods。
        meta_target_suffix (str): 添加到目标列名后的后缀。
        drop_neutral_targets (bool): 已弃用，保留用于兼容性。

    Returns:
        tuple: (pd.DataFrame, str or None)
               - DataFrame: 带有新的元模型目标列的DataFrame (已移除未来价格不可用的行和中性样本)。
               - str: 元模型目标列的名称，如果创建失败则为 None。

    标签体系 (二分类):
        1: 明确上涨 (价格变化 > threshold)
        0: 明确下跌 (价格变化 < -threshold)
        中性样本 (-threshold <= 价格变化 <= threshold) 被移除
        -1: 无效 (未来数据不足，将被移除)
    """
    # 检查df_full_hist_data是否为DataFrame类型
    if not isinstance(df_full_hist_data, pd.DataFrame):
        logger.error("create_meta_target_variable: df_full_hist_data 必须是DataFrame类型，收到的是 %s", type(df_full_hist_data))
        return None, None
        
    if not isinstance(base_model_config, dict):
        logger.error("create_meta_target_variable: base_model_config 必须是字典，收到的是 %s", type(base_model_config))
        return None, None
        
    if 'close' not in df_full_hist_data.columns:
        logger.error("create_meta_target_variable: 'close' 列不存在于DataFrame中。可用列: %s", df_full_hist_data.columns.tolist())
        return None, None

    periods_list = base_model_config.get('prediction_periods', [1])
    if not isinstance(periods_list, list) or not periods_list:
        logger.warning("create_meta_target_variable: base_model_config 中的 'prediction_periods' 无效 (%s)。使用默认值 [1]。", periods_list)
        periods_list = [1]
    period = periods_list[0]

    threshold = base_model_config.get('target_threshold', 0.001) # 默认一个较小的阈值
    target_name_base = base_model_config.get('name', 'unknown_base_model')

    df = df_full_hist_data.copy()
    current_close = df['close']
    future_close_col_temp = f'future_close_{period}p_for_{target_name_base}_meta_temp'
    meta_target_col_name = f'target_{period}p_{target_name_base}{meta_target_suffix}'

    df[future_close_col_temp] = current_close.shift(-period)

    # 计算价格变化百分比
    price_change_pct = (df[future_close_col_temp] - current_close) / current_close

    # 定义条件 - 真正的二分类：明确上涨 vs 明确下跌
    cond_invalid = df[future_close_col_temp].isna()
    cond_up = price_change_pct > threshold      # 明确上涨：价格变化 > 阈值
    cond_down = price_change_pct < -threshold   # 明确下跌：价格变化 < -阈值
    cond_neutral = (price_change_pct >= -threshold) & (price_change_pct <= threshold)  # 中性：在阈值范围内

    # 标签: 1 = 明确上涨, 0 = 明确下跌, -1 = 无效, -2 = 中性(将被移除)
    df[meta_target_col_name] = np.select(
        [cond_invalid, cond_up, cond_down, cond_neutral],
        [-1, 1, 0, -2],     # -1 无效, 1 明确上涨, 0 明确下跌, -2 中性
        default=-1          # 默认为无效
    )

    # 首先移除未来价格不可用的行 (标记为-1)
    original_len = len(df)
    df_filtered = df[df[meta_target_col_name] != -1].copy()
    invalid_removed_count = original_len - len(df_filtered)

    if invalid_removed_count > 0:
        logger.info("[MetaTargetCreation for %s]: 移除了 %d 行 (因未来价格不可用)。", target_name_base, invalid_removed_count)

    # 记录过滤前的分布
    if not df_filtered.empty:
        try:
            df_filtered[meta_target_col_name] = df_filtered[meta_target_col_name].astype(int)
            counts_before_filter = df_filtered[meta_target_col_name].value_counts(normalize=True).sort_index()
            dist_str_before = ", ".join([f"Label {idx}:{val*100:.2f}%" for idx, val in counts_before_filter.items()])
            logger.info("[MetaTargetCreation for %s]: 过滤前元目标 '%s' 分布: [%s] (样本数: %d)",
                       target_name_base, meta_target_col_name, dist_str_before, len(df_filtered))
        except (ValueError, TypeError) as e_dist_before:
            logger.warning("[MetaTargetCreation for %s]: 分析过滤前元目标分布时出错: %s", target_name_base, e_dist_before)

    # 🎯 关键修复：移除中性样本 (标记为-2)
    neutral_count_before = len(df_filtered)
    df_filtered = df_filtered[df_filtered[meta_target_col_name] != -2].copy()
    neutral_removed_count = neutral_count_before - len(df_filtered)

    if neutral_removed_count > 0:
        logger.info("[MetaTargetCreation for %s]: 移除了 %d 个中性样本 (价格变化在±%.4f阈值内)。",
                   target_name_base, neutral_removed_count, threshold)

    # 检查移除中性样本后是否还有数据
    if df_filtered.empty:
        logger.error("create_meta_target_variable (%s): 移除中性样本后DataFrame为空。", target_name_base)
        return None, None

    df_filtered.drop(columns=[future_close_col_temp], inplace=True, errors='ignore')

    if meta_target_col_name not in df_filtered.columns or df_filtered[meta_target_col_name].isnull().all():
        logger.error("create_meta_target_variable (%s): 元目标列 '%s' 创建失败或全为空。", target_name_base, meta_target_col_name)
        return None, None

    # 记录最终分布和标签含义
    try:
        if not df_filtered.empty:
            counts_final = df_filtered[meta_target_col_name].value_counts(normalize=True).sort_index()
            dist_str_final = ", ".join([f"Label {idx}:{val*100:.2f}%" for idx, val in counts_final.items()])

            # 修复后的二分类标签含义
            label_meaning = f"1=明确上涨(>{threshold:.4f}), 0=明确下跌(<-{threshold:.4f}), 中性样本已移除"

            logger.info("[MetaTargetCreation for %s]: 最终元目标 '%s' 分布: [%s] (样本数: %d, %s)",
                       target_name_base, meta_target_col_name, dist_str_final, len(df_filtered), label_meaning)
        else:
            logger.warning("[MetaTargetCreation for %s]: 元目标创建后DataFrame为空。", target_name_base)
    except (ValueError, TypeError) as e_dist_final:
        logger.error("[MetaTargetCreation for %s]: 分析最终元目标分布时出错: %s", target_name_base, e_dist_final)

    return df_filtered, meta_target_col_name




# --- 辅助函数 (interval_to_timedelta, ms_to_dt_str) ---
def interval_to_timedelta(interval_str):
    """将时间间隔字符串转换为 Timedelta 对象
    
    Args:
        interval_str (str): 时间间隔字符串，如 '1m', '4h', '1d', '1w'
        
    Returns:
        pd.Timedelta: 对应的时间间隔对象，如果转换失败则返回默认值(1小时)
    """
    if not isinstance(interval_str, str):
        logger.warning("interval_to_timedelta: 输入不是字符串类型，而是 %s，返回默认值(1小时)", type(interval_str))
        return pd.Timedelta(hours=1)
        
    if not interval_str or len(interval_str) < 2:
        logger.warning("interval_to_timedelta: 输入字符串 '%s' 格式无效，返回默认值(1小时)", interval_str)
        return pd.Timedelta(hours=1)
        
    try:
        num = int(interval_str[:-1])
        unit = interval_str[-1].lower()
        if unit == 'm': return pd.Timedelta(minutes=num)
        elif unit == 'h': return pd.Timedelta(hours=num)
        elif unit == 'd': return pd.Timedelta(days=num)
        elif unit == 'w': return pd.Timedelta(weeks=num)
        else: 
            logger.warning("interval_to_timedelta: 未知的间隔单位: '%s'，返回默认值(1小时)", unit)
            return pd.Timedelta(hours=1)
    except (ValueError, TypeError) as e:
        logger.warning("interval_to_timedelta: 无法解析间隔字符串 '%s': %s，返回默认值(1小时)", interval_str, e)
        return pd.Timedelta(hours=1)
    except IndexError as e:
        logger.warning("interval_to_timedelta: 间隔字符串 '%s' 格式错误: %s，返回默认值(1小时)", interval_str, e)
        return pd.Timedelta(hours=1)

def ms_to_dt_str(ms):
    """将毫秒时间戳转换为可读的日期时间字符串
    
    Args:
        ms (int/float): 毫秒时间戳
        
    Returns:
        str: 格式化的日期时间字符串，如果转换失败则返回错误信息
    """
    if ms is None: 
        return "N/A"
    try:
        if not isinstance(ms, (int, float)) or pd.isna(ms): 
            return "无效输入(非数字)"
        ms_int = int(ms)
        dt_utc = datetime.fromtimestamp(ms_int / 1000, tz=timezone.utc)
        return dt_utc.strftime('%Y-%m-%d %H:%M:%S %Z%z')
    except (ValueError, OSError) as e: 
        logger.debug("ms_to_dt_str: 无效时间戳(%s): %s", ms, e)
        return f"无效时间戳({ms}):{e}"
    except OverflowError as e: 
        logger.debug("ms_to_dt_str: 时间戳超出范围(%s): %s", ms, e)
        return f"时间戳超出范围({ms})"
    except TypeError as e:
        logger.debug("ms_to_dt_str: 类型错误(%s): %s", ms, e)
        return f"类型错误({ms}):{e}"
    except Exception as e_gen: 
        logger.warning("ms_to_dt_str: 转换出错(%s): %s", ms, e_gen)
        return f"转换出错({ms}):{e_gen}"

# --- 辅助函数：时间间隔转换 ---
def _interval_to_minutes(interval):
    """将Binance时间间隔转换为分钟数"""
    interval = interval.lower()
    if interval.endswith('m'):
        return int(interval[:-1])
    elif interval.endswith('h'):
        return int(interval[:-1]) * 60
    elif interval.endswith('d'):
        return int(interval[:-1]) * 1440
    elif interval.endswith('w'):
        return int(interval[:-1]) * 10080
    else:
        logger.warning("未知的时间间隔格式: %s", interval)
        return 0

# --- 辅助函数：获取最新N条数据 ---
def _fetch_latest_klines(binance_client, symbol, interval, limit, kline_limit_per_req, max_retries, retry_delay):
    """获取最新的N条K线数据"""
    all_klines = []
    fetched_count = 0
    current_end_ts_for_req = None
    total_batches = 0

    while fetched_count < limit:
        retries = 0
        success = False
        current_req_limit = min(kline_limit_per_req, limit - fetched_count)
        if current_req_limit <= 0:
            break
        total_batches += 1

        while retries < max_retries and not success:
            try:
                kwargs = {'symbol': symbol, 'interval': interval, 'limit': current_req_limit}
                if current_end_ts_for_req:
                    kwargs['endTime'] = current_end_ts_for_req - 1

                klines = binance_client.get_klines(**kwargs)

                if not klines:
                    logger.debug("已达到数据限制，没有更多K线数据")
                    return all_klines

                if not isinstance(klines, list) or not all(isinstance(k, list) for k in klines) or not klines[0]:
                    logger.error("收到无效K线数据 (类型: %s)", type(klines))
                    break

                earliest_ts_in_batch = klines[0][0]
                all_klines = klines + all_klines
                fetched_count += len(klines)
                current_end_ts_for_req = earliest_ts_in_batch
                success = True

                logger.debug("成功获取批次 #%d: %d 条K线，总计: %d/%d",
                           total_batches, len(klines), fetched_count, limit)

            except BinanceAPIException as e:
                if e.code == -1003:
                    logger.error("IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                    return []
                logger.warning("Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s",
                             retries+1, max_retries, e.code, e.message)
                time.sleep(retry_delay * (retries + 1))
                retries += 1

            except BinanceRequestException as e:
                logger.warning("Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
                time.sleep(retry_delay * (retries + 1))
                retries += 1

            except (ConnectionError, TimeoutError) as e_conn:
                logger.warning("连接错误 (重试 %d/%d): %s", retries+1, max_retries, str(e_conn))
                time.sleep(retry_delay * (retries + 1))
                retries += 1

            except Exception as e_fetch_inner:
                logger.error("未知获取错误: %s - %s", type(e_fetch_inner).__name__, e_fetch_inner)
                return []

        if not success:
            logger.error("获取批次失败 (%d次尝试)，已停止", max_retries)
            break

    return all_klines

# --- 辅助函数：获取时间范围内的数据 ---
def _fetch_timerange_klines(binance_client, symbol, interval, start_ts_ms, end_ts_ms, kline_limit_per_req, max_retries, retry_delay):
    """获取指定时间范围内的所有K线数据"""
    all_klines = []
    current_end_ts_for_req = end_ts_ms
    total_batches = 0
    consecutive_failures = 0
    max_consecutive_failures = 3  # 允许连续失败3次后停止

    logger.debug("开始获取时间范围数据: start_ts=%s, end_ts=%s", start_ts_ms, end_ts_ms)

    while True:
        retries = 0
        success = False
        total_batches += 1

        while retries < max_retries and not success:
            try:
                kwargs = {'symbol': symbol, 'interval': interval, 'limit': kline_limit_per_req}
                if current_end_ts_for_req:
                    kwargs['endTime'] = current_end_ts_for_req - 1
                if start_ts_ms:
                    kwargs['startTime'] = start_ts_ms

                logger.debug("API请求参数: %s", kwargs)
                klines = binance_client.get_klines(**kwargs)

                if not klines:
                    logger.debug("API返回空数据，可能已达到历史数据限制")
                    return all_klines  # 直接返回已获取的数据

                if not isinstance(klines, list) or not all(isinstance(k, list) for k in klines) or not klines[0]:
                    logger.error("收到无效K线数据 (类型: %s)", type(klines))
                    retries += 1
                    continue

                earliest_ts_in_batch = klines[0][0]
                latest_ts_in_batch = klines[-1][0]
                all_klines = klines + all_klines
                success = True
                consecutive_failures = 0  # 重置连续失败计数

                logger.debug("成功获取批次 #%d: %d 条K线，时间范围: %s - %s，总计: %d 条",
                           total_batches, len(klines),
                           pd.to_datetime(earliest_ts_in_batch, unit='ms'),
                           pd.to_datetime(latest_ts_in_batch, unit='ms'),
                           len(all_klines))

                # 检查退出条件
                should_stop = False

                # 1. 如果已经获取到开始时间之前的数据（最重要的条件）
                if start_ts_ms and earliest_ts_in_batch <= start_ts_ms:
                    logger.debug("已获取到开始时间之前的数据，停止获取")
                    should_stop = True

                # 2. 检查是否真的到达了历史数据的尽头
                elif len(klines) < kline_limit_per_req:
                    logger.debug("获取的数据量(%d) < 请求量(%d)，已达到时间范围或API历史数据限制", len(klines), kline_limit_per_req)
                    should_stop = True

                # 3. 检查是否已经覆盖了请求的时间范围
                elif start_ts_ms and end_ts_ms:
                    # 检查当前获取的数据是否已经覆盖了完整的时间范围
                    current_earliest = min([k[0] for k in all_klines]) if all_klines else earliest_ts_in_batch
                    current_latest = max([k[0] for k in all_klines]) if all_klines else latest_ts_in_batch

                    # 如果已经覆盖了请求的时间范围，停止获取
                    if current_earliest <= start_ts_ms and current_latest >= end_ts_ms - 3600000:  # 允许1小时误差
                        logger.debug("已覆盖完整的请求时间范围，停止获取")
                        should_stop = True

                # 不在这里break，让外层循环处理should_stop
                current_end_ts_for_req = earliest_ts_in_batch

            except BinanceAPIException as e:
                if e.code == -1003:
                    logger.error("IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                    return all_klines  # 返回已获取的数据而不是空列表
                logger.warning("Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s",
                             retries+1, max_retries, e.code, e.message)
                time.sleep(retry_delay * (retries + 1))
                retries += 1

            except BinanceRequestException as e:
                logger.warning("Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
                time.sleep(retry_delay * (retries + 1))
                retries += 1

            except (ConnectionError, TimeoutError) as e_conn:
                logger.warning("连接错误 (重试 %d/%d): %s", retries+1, max_retries, str(e_conn))
                time.sleep(retry_delay * (retries + 1))
                retries += 1

            except Exception as e_fetch_inner:
                logger.error("未知获取错误: %s - %s", type(e_fetch_inner).__name__, e_fetch_inner)
                retries += 1
                if retries >= max_retries:
                    logger.error("达到最大重试次数，返回已获取的数据")
                    return all_klines

        if not success:
            consecutive_failures += 1
            logger.warning("获取批次失败 (%d次尝试)，连续失败次数: %d/%d", max_retries, consecutive_failures, max_consecutive_failures)

            # 如果连续失败次数过多，停止获取
            if consecutive_failures >= max_consecutive_failures:
                logger.error("连续失败次数过多，停止获取。已获取 %d 条数据", len(all_klines))
                break

            # 否则继续尝试下一批数据（向前移动时间窗口）
            if current_end_ts_for_req and len(all_klines) > 0:
                # 使用已获取数据的最早时间作为下一次请求的结束时间
                earliest_existing = min([k[0] for k in all_klines])
                current_end_ts_for_req = earliest_existing
                logger.debug("调整时间窗口，继续尝试获取更早的数据")
            else:
                break

        # 如果成功获取了数据，检查是否应该继续
        if success:
            if should_stop:
                break
            # 继续下一批数据获取

    # 添加数据完整性检查和警告
    if all_klines:
        actual_start_ts = min([k[0] for k in all_klines])
        actual_end_ts = max([k[0] for k in all_klines])

        if start_ts_ms and actual_start_ts > start_ts_ms:
            logger.warning("警告：未能获取到完整的时间范围数据。请求开始时间: %s, 实际开始时间: %s",
                         pd.to_datetime(start_ts_ms, unit='ms'), pd.to_datetime(actual_start_ts, unit='ms'))
            logger.warning("这可能是由于Binance API的历史数据限制。建议缩短时间范围或使用更大的时间间隔。")

    return all_klines

# --- fetch_binance_history ---
@handle_training_exception(
    function_name="fetch_binance_history",
    fallback_result=pd.DataFrame(),
    include_traceback=True,
    max_traceback_lines=5
)
def fetch_binance_history(binance_client, symbol, interval, limit=None, end_dt=None, start_dt=None):
    """从Binance获取历史K线数据（支持智能分页和时间范围获取）

    Args:
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号，如 'BTCUSDT'
        interval (str): K线间隔，如 '1m', '1h', '1d'
        limit (int, optional): 要获取的K线数量上限（仅在获取最新数据时使用）
        end_dt (datetime, optional): 结束时间
        start_dt (datetime, optional): 开始时间

    Returns:
        pd.DataFrame: 包含K线数据的DataFrame，如果获取失败则返回None或空DataFrame

    使用模式：
        模式1 - 获取最新N条数据: fetch_binance_history(client, symbol, interval, limit=1000)
        模式2 - 获取时间段数据: fetch_binance_history(client, symbol, interval, start_dt=start, end_dt=end)
    """
    # 🚀 内存优化：在数据获取前强制垃圾回收
    if MEMORY_OPTIMIZER_AVAILABLE:
        memory_optimizer = get_memory_optimizer()
        memory_optimizer.force_garbage_collection(verbose=False)

    if not binance_client:
        logger.error("fetch_binance_history: Binance 客户端未初始化")
        return None

    # 参数验证和模式判断
    is_latest_mode = (start_dt is None and end_dt is None)
    is_timerange_mode = (start_dt is not None or end_dt is not None)

    # 🎯 缓存系统集成：只对最新数据模式启用缓存
    if is_latest_mode and limit is not None:
        try:
            from src.core.kline_cache import get_kline_cache
            cache = get_kline_cache()

            # 尝试从缓存获取数据
            cached_data = cache.get_cached_data(symbol, interval, limit)
            if cached_data is not None:
                logger.info(f"✅ 从缓存获取数据: {symbol}@{interval} ({len(cached_data)} 条)")
                return cached_data

        except Exception as e:
            logger.warning(f"缓存系统异常，继续使用API获取: {e}")

    if is_latest_mode and limit is None:
        logger.error("fetch_binance_history: 获取最新数据模式需要指定 limit 参数")
        return None

    if is_timerange_mode and limit is not None:
        logger.warning("fetch_binance_history: 时间范围模式将忽略 limit 参数，获取完整时间段数据")

    start_ts_ms, end_ts_ms = None, None
    try:
        if start_dt:
            if not isinstance(start_dt, datetime):
                logger.error("fetch_binance_history: start_dt 不是 datetime 对象，而是 %s", type(start_dt))
                return None
            start_dt_aware = start_dt.astimezone(timezone.utc) if start_dt.tzinfo else start_dt.replace(tzinfo=timezone.utc)
            start_ts_ms = int(start_dt_aware.timestamp() * 1000)
        if end_dt:
            if not isinstance(end_dt, datetime):
                logger.error("fetch_binance_history: end_dt 不是 datetime 对象，而是 %s", type(end_dt))
                return None
            end_dt_aware = end_dt.astimezone(timezone.utc) if end_dt.tzinfo else end_dt.replace(tzinfo=timezone.utc)
            end_ts_ms = int(end_dt_aware.timestamp() * 1000)
    except (AttributeError, TypeError, ValueError, OSError) as e_ts:
        logger.error("fetch_binance_history: 时间戳转换错误: %s", e_ts)
        return None

    # 🚀 使用配置管理器获取参数，外部化硬编码值
    if CONFIG_MANAGER_AVAILABLE:
        kline_limit_per_req = get_data_processing_param('kline_limit_per_request', None, 1000, int)
        max_retries = get_data_processing_param('max_api_retries', None, 7, int)
        retry_delay = get_data_processing_param('api_retry_delay', None, 7, int)
    else:
        kline_limit_per_req = 1000
        max_retries = 7
        retry_delay = 7

    # 根据模式选择不同的获取策略
    all_klines = []

    if is_latest_mode:
        logger.info("模式1: 获取最新 %d 条 %s@%s 数据", limit, symbol, interval)
        all_klines = _fetch_latest_klines(binance_client, symbol, interval, limit, kline_limit_per_req, max_retries, retry_delay)
    else:
        # 时间范围模式 - 添加数据量估算和警告
        if start_dt and end_dt:
            time_diff = end_dt - start_dt
            # 估算需要的数据量
            interval_minutes = _interval_to_minutes(interval)
            if interval_minutes > 0:
                estimated_bars = int(time_diff.total_seconds() / 60 / interval_minutes)
                logger.info("模式2: 获取 %s@%s 时间范围 [%s, %s] 内的数据", symbol, interval, start_dt, end_dt)
                logger.info("估算需要获取约 %d 条数据（时间跨度: %.1f 天）", estimated_bars, time_diff.days + time_diff.seconds/86400)

                # 如果估算的数据量很大，给出警告
                if estimated_bars > 1500:
                    logger.warning("⚠️  警告：请求的时间范围很大（约%d条数据），可能受到Binance API历史数据限制", estimated_bars)
                    logger.warning("建议：1) 缩短时间范围，2) 使用更大的时间间隔，3) 分批获取数据")
            else:
                logger.info("模式2: 获取 %s@%s 时间范围 [%s, %s] 内的数据", symbol, interval, start_dt, end_dt)
        else:
            logger.info("模式2: 获取 %s@%s 时间范围内的数据", symbol, interval)

        all_klines = _fetch_timerange_klines(binance_client, symbol, interval, start_ts_ms, end_ts_ms, kline_limit_per_req, max_retries, retry_delay)
    # 检查获取结果
    if not all_klines:
        logger.warning("最终未能获取到 %s@%s 的K线数据", symbol, interval)
        return pd.DataFrame()
        
    try:
        # 处理获取到的K线数据
        cols=['ts_ms','o','h','l','c','v','ct_ms','qav','n','tbbav','tbqav','ignore']
        df_out_fetch=pd.DataFrame(all_klines, columns=cols)
        df_out_fetch['timestamp']=pd.to_datetime(df_out_fetch['ts_ms'], unit='ms', utc=True)
        df_out_fetch=df_out_fetch.set_index('timestamp')
        df_out_fetch=df_out_fetch[['o','h','l','c','v','qav','n','tbbav','tbqav']]
        
        # 转换为数值类型
        num_cols = df_out_fetch.columns.tolist()
        for col in num_cols: 
            df_out_fetch[col]=pd.to_numeric(df_out_fetch[col], errors='coerce')
            
        # 处理NaN值
        nan_cols = df_out_fetch.columns[df_out_fetch.isnull().any()].tolist()
        if nan_cols:
            logger.debug("发现含有NaN值的列: %s，将进行填充", nan_cols)
            # 使用安全填充方法替代ffill().bfill()
            for col in nan_cols:
                # 为不同列设置合适的默认值
                default_val = 0
                if col in ['o', 'h', 'l', 'c']:
                    # 价格列使用第一个非NaN值
                    non_nan_vals = df_out_fetch[col].dropna()
                    default_val = non_nan_vals.iloc[0] if len(non_nan_vals) > 0 else 0
                elif col in ['v', 'qav', 'n', 'tbbav', 'tbqav']:
                    default_val = 0  # 交易量默认为0
                
                df_out_fetch[col] = safe_fill_nans(df_out_fetch[col], default_val, use_historical_only=True)
                
        # 重命名列并处理重复索引
        df_out_fetch=df_out_fetch.rename(columns={'o':'open','h':'high','l':'low','c':'close','v':'volume'})
        df_out_fetch=df_out_fetch[~df_out_fetch.index.duplicated(keep='first')]
        df_out_fetch.sort_index(ascending=True, inplace=True)

        # 在最新数据模式下，确保返回的数据量不超过请求的limit
        if is_latest_mode and limit is not None:
            df_out_fetch = df_out_fetch.tail(limit)

        # 🚀 内存优化：对获取的数据进行内存优化
        if MEMORY_OPTIMIZER_AVAILABLE:
            try:
                from src.core.memory_optimizer import optimize_dataframe_memory
                df_out_fetch = optimize_dataframe_memory(
                    df_out_fetch,
                    target_config=None,
                    aggressive=False,
                    verbose=False
                )
                logger.debug("fetch_binance_history: 数据内存优化完成")
            except Exception as e_memory:
                logger.debug("fetch_binance_history: 内存优化失败: %s", e_memory)

        logger.info("成功获取并处理 %s@%s 的K线数据，共 %d 条", symbol, interval, len(df_out_fetch))

        # 🎯 缓存系统集成：保存数据到缓存（仅最新数据模式）
        if is_latest_mode and limit is not None and df_out_fetch is not None and not df_out_fetch.empty:
            try:
                from src.core.kline_cache import get_kline_cache
                cache = get_kline_cache()
                cache.save_to_cache(symbol, interval, limit, df_out_fetch)
            except Exception as e:
                logger.warning(f"保存到缓存失败: {e}")

        return df_out_fetch
        
    except (KeyError, ValueError, TypeError) as e_process:
        logger.error("处理K线数据时出错: %s", e_process)
        logger.exception("处理K线数据时的详细错误信息:")
        return pd.DataFrame()
    except Exception as e_unknown:
        logger.error("处理K线数据时发生未知错误: %s", e_unknown)
        logger.exception("处理K线数据时未知错误的详细堆栈跟踪:")
        return pd.DataFrame()

# --- 衍生品数据获取函数 ---

@handle_training_exception(
    function_name="fetch_funding_rate_history",
    fallback_result=pd.DataFrame(),
    include_traceback=True,
    max_traceback_lines=5
)
def fetch_funding_rate_history(binance_client, symbol, limit=100, start_time=None, end_time=None):
    """
    获取资金费率历史数据

    Args:
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号，如 'BTCUSDT'
        limit (int): 要获取的数据条数，最大500
        start_time (int, optional): 开始时间戳(毫秒)
        end_time (int, optional): 结束时间戳(毫秒)

    Returns:
        pd.DataFrame: 包含资金费率数据的DataFrame，失败时返回None
    """
    if not binance_client:
        logger.error("fetch_funding_rate_history: Binance 客户端未初始化")
        return None

    # 获取重试参数
    if CONFIG_MANAGER_AVAILABLE:
        max_retries = get_data_processing_param('max_api_retries', None, 7, int)
        retry_delay = get_data_processing_param('api_retry_delay', None, 7, int)
    else:
        max_retries = 7
        retry_delay = 7

    retries = 0
    while retries < max_retries:
        try:
            kwargs = {'symbol': symbol, 'limit': min(limit, 500)}
            if start_time:
                kwargs['startTime'] = start_time
            if end_time:
                kwargs['endTime'] = end_time

            funding_rates = binance_client.futures_funding_rate(**kwargs)

            if not funding_rates:
                logger.warning("fetch_funding_rate_history: 未获取到 %s 的资金费率数据", symbol)
                return pd.DataFrame()

            df = pd.DataFrame(funding_rates)
            df['fundingTime'] = pd.to_datetime(df['fundingTime'], unit='ms', utc=True)
            df['fundingRate'] = pd.to_numeric(df['fundingRate'], errors='coerce')
            df = df.set_index('fundingTime')

            logger.info("成功获取 %s 的资金费率历史数据，共 %d 条", symbol, len(df))
            return df

        except BinanceAPIException as e:
            if e.code == -1003:  # IP被禁
                logger.error("fetch_funding_rate_history: IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                return None
            logger.warning("fetch_funding_rate_history: Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s",
                         retries+1, max_retries, e.code, e.message)
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except BinanceRequestException as e:
            logger.warning("fetch_funding_rate_history: Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except (ConnectionError, TimeoutError, SSLError) as e:
            logger.warning("fetch_funding_rate_history: 连接/SSL错误 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except Exception as e:
            logger.error("fetch_funding_rate_history: 获取资金费率数据时出错: %s", e)
            return None

    logger.error("fetch_funding_rate_history: 获取资金费率数据失败 (%d次尝试)", max_retries)
    return None

@handle_training_exception(
    function_name="fetch_open_interest_history",
    fallback_result=pd.DataFrame(),
    include_traceback=True,
    max_traceback_lines=5
)
def fetch_open_interest_history(binance_client, symbol, period='5m', limit=30):
    """
    获取持仓量历史数据

    Args:
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号，如 'BTCUSDT'
        period (str): 时间周期，可选值: '5m', '15m', '30m', '1h', '2h', '4h', '6h', '12h', '1d'
        limit (int): 要获取的数据条数，最大500

    Returns:
        pd.DataFrame: 包含持仓量数据的DataFrame，失败时返回None
    """
    if not binance_client:
        logger.error("fetch_open_interest_history: Binance 客户端未初始化")
        return None

    # 获取重试参数
    if CONFIG_MANAGER_AVAILABLE:
        max_retries = get_data_processing_param('max_api_retries', None, 7, int)
        retry_delay = get_data_processing_param('api_retry_delay', None, 7, int)
    else:
        max_retries = 7
        retry_delay = 7

    retries = 0
    while retries < max_retries:
        try:
            kwargs = {'symbol': symbol, 'period': period, 'limit': min(limit, 500)}

            open_interest_data = binance_client.futures_open_interest_hist(**kwargs)

            if not open_interest_data:
                logger.warning("fetch_open_interest_history: 未获取到 %s 的持仓量数据", symbol)
                return pd.DataFrame()

            df = pd.DataFrame(open_interest_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            df['sumOpenInterest'] = pd.to_numeric(df['sumOpenInterest'], errors='coerce')
            df['sumOpenInterestValue'] = pd.to_numeric(df['sumOpenInterestValue'], errors='coerce')
            df = df.set_index('timestamp')

            logger.info("成功获取 %s 的持仓量历史数据，共 %d 条", symbol, len(df))
            return df

        except BinanceAPIException as e:
            if e.code == -1003:  # IP被禁
                logger.error("fetch_open_interest_history: IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                return None
            logger.warning("fetch_open_interest_history: Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s",
                         retries+1, max_retries, e.code, e.message)
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except BinanceRequestException as e:
            logger.warning("fetch_open_interest_history: Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except (ConnectionError, TimeoutError, SSLError) as e:
            logger.warning("fetch_open_interest_history: 连接/SSL错误 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except Exception as e:
            logger.error("fetch_open_interest_history: 获取持仓量数据时出错: %s", e)
            return None

    logger.error("fetch_open_interest_history: 获取持仓量数据失败 (%d次尝试)", max_retries)
    return None

def fetch_long_short_ratio(binance_client, symbol, period='5m', limit=30):
    """
    获取多空账户数比数据

    Args:
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号，如 'BTCUSDT'
        period (str): 时间周期，可选值: '5m', '15m', '30m', '1h', '2h', '4h', '6h', '12h', '1d'
        limit (int): 要获取的数据条数，最大500

    Returns:
        pd.DataFrame: 包含多空账户数比数据的DataFrame，失败时返回None
    """
    if not binance_client:
        logger.error("fetch_long_short_ratio: Binance 客户端未初始化")
        return None

    # 获取重试参数
    if CONFIG_MANAGER_AVAILABLE:
        max_retries = get_data_processing_param('max_api_retries', None, 7, int)
        retry_delay = get_data_processing_param('api_retry_delay', None, 7, int)
    else:
        max_retries = 7
        retry_delay = 7

    retries = 0
    while retries < max_retries:
        try:
            kwargs = {'symbol': symbol, 'period': period, 'limit': min(limit, 500)}

            ratio_data = binance_client.futures_global_longshort_ratio(**kwargs)

            if not ratio_data:
                logger.warning("fetch_long_short_ratio: 未获取到 %s 的多空账户数比数据", symbol)
                return pd.DataFrame()

            df = pd.DataFrame(ratio_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            df['longShortRatio'] = pd.to_numeric(df['longShortRatio'], errors='coerce')
            df['longAccount'] = pd.to_numeric(df['longAccount'], errors='coerce')
            df['shortAccount'] = pd.to_numeric(df['shortAccount'], errors='coerce')
            df = df.set_index('timestamp')

            logger.info("成功获取 %s 的多空账户数比数据，共 %d 条", symbol, len(df))
            return df

        except BinanceAPIException as e:
            if e.code == -1003:  # IP被禁
                logger.error("fetch_long_short_ratio: IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                return None
            logger.warning("fetch_long_short_ratio: Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s",
                         retries+1, max_retries, e.code, e.message)
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except BinanceRequestException as e:
            logger.warning("fetch_long_short_ratio: Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except (ConnectionError, TimeoutError, SSLError) as e:
            logger.warning("fetch_long_short_ratio: 连接/SSL错误 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except Exception as e:
            logger.error("fetch_long_short_ratio: 获取多空账户数比数据时出错: %s", e)
            return None

    logger.error("fetch_long_short_ratio: 获取多空账户数比数据失败 (%d次尝试)", max_retries)
    return None

def fetch_top_trader_long_short_ratio(binance_client, symbol, period='5m', limit=30):
    """
    获取大户持仓账户数比数据

    Args:
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号，如 'BTCUSDT'
        period (str): 时间周期，可选值: '5m', '15m', '30m', '1h', '2h', '4h', '6h', '12h', '1d'
        limit (int): 要获取的数据条数，最大500

    Returns:
        pd.DataFrame: 包含大户持仓账户数比数据的DataFrame，失败时返回None
    """
    if not binance_client:
        logger.error("fetch_top_trader_long_short_ratio: Binance 客户端未初始化")
        return None

    # 获取重试参数
    if CONFIG_MANAGER_AVAILABLE:
        max_retries = get_data_processing_param('max_api_retries', None, 7, int)
        retry_delay = get_data_processing_param('api_retry_delay', None, 7, int)
    else:
        max_retries = 7
        retry_delay = 7

    retries = 0
    while retries < max_retries:
        try:
            kwargs = {'symbol': symbol, 'period': period, 'limit': min(limit, 500)}

            ratio_data = binance_client.futures_top_longshort_account_ratio(**kwargs)

            if not ratio_data:
                logger.warning("fetch_top_trader_long_short_ratio: 未获取到 %s 的大户持仓账户数比数据", symbol)
                return pd.DataFrame()

            df = pd.DataFrame(ratio_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            df['longShortRatio'] = pd.to_numeric(df['longShortRatio'], errors='coerce')
            df['longAccount'] = pd.to_numeric(df['longAccount'], errors='coerce')
            df['shortAccount'] = pd.to_numeric(df['shortAccount'], errors='coerce')
            df = df.set_index('timestamp')

            logger.info("成功获取 %s 的大户持仓账户数比数据，共 %d 条", symbol, len(df))
            return df

        except BinanceAPIException as e:
            if e.code == -1003:  # IP被禁
                logger.error("fetch_top_trader_long_short_ratio: IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                return None
            logger.warning("fetch_top_trader_long_short_ratio: Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s",
                         retries+1, max_retries, e.code, e.message)
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except BinanceRequestException as e:
            logger.warning("fetch_top_trader_long_short_ratio: Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except (ConnectionError, TimeoutError, SSLError) as e:
            logger.warning("fetch_top_trader_long_short_ratio: 连接/SSL错误 (重试 %d/%d): %s", retries+1, max_retries, str(e))
            time.sleep(retry_delay * (retries + 1))
            retries += 1

        except Exception as e:
            logger.error("fetch_top_trader_long_short_ratio: 获取大户持仓账户数比数据时出错: %s", e)
            return None

    logger.error("fetch_top_trader_long_short_ratio: 获取大户持仓账户数比数据失败 (%d次尝试)", max_retries)
    return None

def _calculate_funding_rate_features(df_out, funding_df, params, interval_str):
    """计算资金费率特征"""
    try:
        # 将资金费率数据对齐到主时间框架
        funding_aligned = funding_df.reindex(df_out.index, method='ffill')

        # 基础特征
        df_out['funding_rate'] = funding_aligned['fundingRate'].fillna(0)

        # 移动平均
        for window in params.get("moving_averages", []):
            df_out[f'funding_rate_ma_{window}'] = df_out['funding_rate'].rolling(window=window, min_periods=1).mean()

        # 变化率
        for period in params.get("pct_changes", []):
            df_out[f'funding_rate_change_{period}'] = df_out['funding_rate'].pct_change(periods=period).fillna(0) * 100

        # 滚动标准差
        for window in params.get("rolling_std", []):
            df_out[f'funding_rate_std_{window}'] = df_out['funding_rate'].rolling(window=window, min_periods=1).std().fillna(0)

        logger.debug("_calculate_funding_rate_features: (%s) 成功计算资金费率特征", interval_str)

    except Exception as e:
        logger.error("_calculate_funding_rate_features: (%s) 计算资金费率特征时出错: %s", interval_str, e)

def _calculate_open_interest_features(df_out, oi_df, params, interval_str):
    """计算持仓量特征"""
    try:
        # 将持仓量数据对齐到主时间框架
        oi_aligned = oi_df.reindex(df_out.index, method='ffill')

        # 基础特征
        df_out['open_interest'] = oi_aligned['sumOpenInterest'].fillna(0)
        df_out['open_interest_value'] = oi_aligned['sumOpenInterestValue'].fillna(0)

        # 移动平均
        for window in params.get("moving_averages", []):
            df_out[f'oi_ma_{window}'] = df_out['open_interest'].rolling(window=window, min_periods=1).mean()
            df_out[f'oi_value_ma_{window}'] = df_out['open_interest_value'].rolling(window=window, min_periods=1).mean()

        # 变化率
        for period in params.get("pct_changes", []):
            df_out[f'oi_change_{period}'] = df_out['open_interest'].pct_change(periods=period).fillna(0) * 100
            df_out[f'oi_value_change_{period}'] = df_out['open_interest_value'].pct_change(periods=period).fillna(0) * 100

        # 滚动标准差
        for window in params.get("rolling_std", []):
            df_out[f'oi_std_{window}'] = df_out['open_interest'].rolling(window=window, min_periods=1).std().fillna(0)

        logger.debug("_calculate_open_interest_features: (%s) 成功计算持仓量特征", interval_str)

    except Exception as e:
        logger.error("_calculate_open_interest_features: (%s) 计算持仓量特征时出错: %s", interval_str, e)

def _calculate_long_short_ratio_features(df_out, ls_df, params, interval_str):
    """计算多空账户数比特征"""
    try:
        # 将多空比数据对齐到主时间框架
        ls_aligned = ls_df.reindex(df_out.index, method='ffill')

        # 基础特征
        df_out['long_short_ratio'] = ls_aligned['longShortRatio'].fillna(1.0)
        df_out['long_account_pct'] = ls_aligned['longAccount'].fillna(50.0)
        df_out['short_account_pct'] = ls_aligned['shortAccount'].fillna(50.0)

        # 移动平均
        for window in params.get("moving_averages", []):
            df_out[f'ls_ratio_ma_{window}'] = df_out['long_short_ratio'].rolling(window=window, min_periods=1).mean()

        # 变化率
        for period in params.get("pct_changes", []):
            df_out[f'ls_ratio_change_{period}'] = df_out['long_short_ratio'].pct_change(periods=period).fillna(0) * 100

        # 情绪指标
        if params.get("sentiment_indicators", False):
            df_out['sentiment_bias'] = (df_out['long_account_pct'] - df_out['short_account_pct']) / 100
            df_out['sentiment_extreme'] = np.where(
                (df_out['long_account_pct'] > 70) | (df_out['short_account_pct'] > 70), 1, 0
            )

        logger.debug("_calculate_long_short_ratio_features: (%s) 成功计算多空账户数比特征", interval_str)

    except Exception as e:
        logger.error("_calculate_long_short_ratio_features: (%s) 计算多空账户数比特征时出错: %s", interval_str, e)

def _calculate_top_trader_ratio_features(df_out, tt_df, params, interval_str):
    """计算大户持仓账户数比特征"""
    try:
        # 将大户持仓比数据对齐到主时间框架
        tt_aligned = tt_df.reindex(df_out.index, method='ffill')

        # 基础特征
        df_out['top_trader_long_short_ratio'] = tt_aligned['longShortRatio'].fillna(1.0)
        df_out['top_trader_long_pct'] = tt_aligned['longAccount'].fillna(50.0)
        df_out['top_trader_short_pct'] = tt_aligned['shortAccount'].fillna(50.0)

        # 移动平均
        for window in params.get("moving_averages", []):
            df_out[f'tt_ratio_ma_{window}'] = df_out['top_trader_long_short_ratio'].rolling(window=window, min_periods=1).mean()

        # 大户情绪强度
        df_out['top_trader_sentiment'] = (df_out['top_trader_long_pct'] - df_out['top_trader_short_pct']) / 100

        logger.debug("_calculate_top_trader_ratio_features: (%s) 成功计算大户持仓账户数比特征", interval_str)

    except Exception as e:
        logger.error("_calculate_top_trader_ratio_features: (%s) 计算大户持仓账户数比特征时出错: %s", interval_str, e)

def _calculate_cross_source_features(df_out, derivatives_data, feature_params, interval_str):
    """计算跨数据源的组合特征"""
    try:
        # 计算聪明钱分歧指标
        if 'long_short_ratio' in df_out.columns and 'top_trader_long_short_ratio' in df_out.columns:
            df_out['smart_money_divergence'] = df_out['top_trader_long_short_ratio'] - df_out['long_short_ratio']

            # 计算分歧强度
            df_out['divergence_strength'] = np.abs(df_out['smart_money_divergence'])

            # 计算分歧方向
            df_out['divergence_direction'] = np.where(df_out['smart_money_divergence'] > 0, 1, -1)

        # 计算综合情绪指标
        if 'sentiment_bias' in df_out.columns and 'top_trader_sentiment' in df_out.columns:
            df_out['combined_sentiment'] = (df_out['sentiment_bias'] + df_out['top_trader_sentiment']) / 2

            # 计算情绪一致性
            df_out['sentiment_consensus'] = np.where(
                np.sign(df_out['sentiment_bias']) == np.sign(df_out['top_trader_sentiment']), 1, 0
            )

        logger.debug("_calculate_cross_source_features: (%s) 成功计算跨数据源组合特征", interval_str)

    except Exception as e:
        logger.error("_calculate_cross_source_features: (%s) 计算跨数据源组合特征时出错: %s", interval_str, e)

def _add_derivatives_features(df_out, cfg, binance_client, symbol, interval_str):
    """
    添加衍生品市场数据特征 (动态自适应版)

    Args:
        df_out (pd.DataFrame): 主要的K线数据DataFrame
        cfg (dict): 配置字典
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号
        interval_str (str): 时间周期字符串
    """
    if not cfg.get('enable_derivatives_features', False):
        logger.debug("_add_derivatives_features: (%s) 衍生品特征已禁用", interval_str)
        return

    if not binance_client:
        logger.warning("_add_derivatives_features: (%s) Binance客户端未初始化，跳过衍生品特征", interval_str)
        return

    logger.info("_add_derivatives_features: (%s) 开始计算衍生品特征 (动态自适应模式)", interval_str)

    try:
        # --- 1. 动态计算所需的数据量 ---
        if df_out.empty or df_out.index.empty:
            logger.warning("_add_derivatives_features: (%s) DataFrame为空，跳过衍生品特征", interval_str)
            return

        required_history_length = len(df_out)

        # 从特征参数中找到需要的最长回看周期
        max_lookback = 0
        feature_params = cfg.get("derivatives_feature_params", {})
        for source, params in feature_params.items():
            if "moving_averages" in params:
                max_lookback = max(max_lookback, max(params["moving_averages"]))
            if "pct_changes" in params:
                max_lookback = max(max_lookback, max(params["pct_changes"]))
            if "rolling_std" in params:
                max_lookback = max(max_lookback, max(params["rolling_std"]))

        # 计算最终需要获取的数据条数
        buffer_multiplier = cfg.get("derivatives_fetch_buffer_multiplier", 1.5)
        min_limit = cfg.get("derivatives_min_fetch_limit", 200)
        max_limit = cfg.get("derivatives_max_fetch_limit", 1000)

        final_fetch_limit = int((required_history_length + max_lookback) * buffer_multiplier)
        final_fetch_limit = max(final_fetch_limit, min_limit)
        final_fetch_limit = min(final_fetch_limit, max_limit)

        logger.info("_add_derivatives_features: (%s) 动态计算出的数据获取条数: %d (历史长度: %d, 最大回看: %d)",
                   interval_str, final_fetch_limit, required_history_length, max_lookback)

        # 获取数据的时间范围
        start_time = df_out.index.min()
        end_time = df_out.index.max()

        # 转换为毫秒时间戳
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)

        # --- 2. 根据配置获取数据源 ---
        data_sources = cfg.get("derivatives_data_sources", [])
        period_alignment = cfg.get("derivatives_period_alignment", True)

        # 确定衍生品数据的时间周期
        derivatives_period = interval_str if period_alignment else "15m"

        logger.info("_add_derivatives_features: (%s) 配置的数据源: %s, 使用周期: %s",
                   interval_str, data_sources, derivatives_period)

        # 初始化数据容器
        derivatives_data = {}

        # 获取各种衍生品数据
        if "funding_rate" in data_sources:
            try:
                funding_df = fetch_funding_rate_history(
                    binance_client, symbol,
                    limit=final_fetch_limit,
                    start_time=start_ts,
                    end_time=end_ts
                )
                if funding_df is not None and not funding_df.empty:
                    derivatives_data["funding_rate"] = funding_df
                    logger.info("_add_derivatives_features: (%s) 成功获取资金费率数据: %d 条",
                               interval_str, len(funding_df))
                else:
                    logger.warning("_add_derivatives_features: (%s) 未获取到资金费率数据", interval_str)
            except Exception as e_funding:
                logger.error("_add_derivatives_features: (%s) 获取资金费率数据时出错: %s", interval_str, e_funding)

        if "open_interest" in data_sources:
            try:
                oi_df = fetch_open_interest_history(
                    binance_client, symbol,
                    period=derivatives_period,
                    limit=final_fetch_limit
                )
                if oi_df is not None and not oi_df.empty:
                    derivatives_data["open_interest"] = oi_df
                    logger.info("_add_derivatives_features: (%s) 成功获取持仓量数据: %d 条",
                               interval_str, len(oi_df))
                else:
                    logger.warning("_add_derivatives_features: (%s) 未获取到持仓量数据", interval_str)
            except Exception as e_oi:
                logger.error("_add_derivatives_features: (%s) 获取持仓量数据时出错: %s", interval_str, e_oi)

        if "long_short_ratio" in data_sources:
            try:
                ls_df = fetch_long_short_ratio(
                    binance_client, symbol,
                    period=derivatives_period,
                    limit=final_fetch_limit
                )
                if ls_df is not None and not ls_df.empty:
                    derivatives_data["long_short_ratio"] = ls_df
                    logger.info("_add_derivatives_features: (%s) 成功获取多空账户数比数据: %d 条",
                               interval_str, len(ls_df))
                else:
                    logger.warning("_add_derivatives_features: (%s) 未获取到多空账户数比数据", interval_str)
            except Exception as e_ls:
                logger.error("_add_derivatives_features: (%s) 获取多空账户数比数据时出错: %s", interval_str, e_ls)

        if "top_trader_ratio" in data_sources:
            try:
                tt_df = fetch_top_trader_long_short_ratio(
                    binance_client, symbol,
                    period=derivatives_period,
                    limit=final_fetch_limit
                )
                if tt_df is not None and not tt_df.empty:
                    derivatives_data["top_trader_ratio"] = tt_df
                    logger.info("_add_derivatives_features: (%s) 成功获取大户持仓账户数比数据: %d 条",
                               interval_str, len(tt_df))
                else:
                    logger.warning("_add_derivatives_features: (%s) 未获取到大户持仓账户数比数据", interval_str)
            except Exception as e_tt:
                logger.error("_add_derivatives_features: (%s) 获取大户持仓账户数比数据时出错: %s", interval_str, e_tt)
        # --- 3. 合并数据并计算特征 ---
        logger.info("_add_derivatives_features: (%s) 开始计算动态特征，获取到 %d 个数据源",
                   interval_str, len(derivatives_data))

        # 计算资金费率特征
        if "funding_rate" in derivatives_data:
            _calculate_funding_rate_features(df_out, derivatives_data["funding_rate"], feature_params.get("funding_rate", {}), interval_str)

        # 计算持仓量特征
        if "open_interest" in derivatives_data:
            _calculate_open_interest_features(df_out, derivatives_data["open_interest"], feature_params.get("open_interest", {}), interval_str)

        # 计算多空比特征
        if "long_short_ratio" in derivatives_data:
            _calculate_long_short_ratio_features(df_out, derivatives_data["long_short_ratio"], feature_params.get("long_short_ratio", {}), interval_str)

        # 计算大户持仓比特征
        if "top_trader_ratio" in derivatives_data:
            _calculate_top_trader_ratio_features(df_out, derivatives_data["top_trader_ratio"], feature_params.get("top_trader_ratio", {}), interval_str)

        # 计算跨数据源的组合特征
        if "long_short_ratio" in derivatives_data and "top_trader_ratio" in derivatives_data:
            _calculate_cross_source_features(df_out, derivatives_data, feature_params, interval_str)

        logger.info("_add_derivatives_features: (%s) 衍生品特征计算完成", interval_str)

    except Exception as e_main:
        logger.error("_add_derivatives_features: (%s) 计算衍生品特征时发生意外错误: %s", interval_str, e_main)
        logger.debug(traceback.format_exc())

# --- 模块化特征计算辅助函数 ---

def safe_fill_nans(series, default_value=None, use_historical_only=None, target_config=None):
    """
    🚀 使用高效的向量化方法填充NaN，避免数据泄露 - 外部化硬编码参数

    Args:
        series: 要填充的pandas Series
        default_value: 默认填充值（如果为None，从配置获取）
        use_historical_only: 是否严格只使用历史数据（如果为None，从配置获取）
        target_config: 目标配置字典

    Returns:
        填充后的pandas Series
    """
    # 🚀 使用配置管理器获取参数
    if CONFIG_MANAGER_AVAILABLE:
        if default_value is None:
            default_value = get_data_processing_param('default_price_value', target_config, 0, (int, float))
        if use_historical_only is None:
            nan_fill_method = get_data_processing_param('nan_fill_method', target_config, 'historical_only', str)
            use_historical_only = (nan_fill_method == 'historical_only')
    else:
        if default_value is None:
            default_value = 0
        if use_historical_only is None:
            use_historical_only = True
    # 检查输入是否为None
    if series is None:
        logger.warning("safe_fill_nans: 输入series为None，返回None")
        return None

    # 检查输入是否为pandas Series
    if not isinstance(series, pd.Series):
        logger.warning("safe_fill_nans: 输入不是pandas Series，尝试转换")
        try:
            series = pd.Series(series)
        except Exception as e:
            logger.error(f"safe_fill_nans: 无法转换输入为pandas Series: {e}")
            return None

    # 如果series为空，直接返回
    if len(series) == 0:
        return series

    # 如果所有值都是NaN，直接用默认值填充
    if series.isna().all():
        return series.fillna(default_value)

    if use_historical_only:
        # 🚀 优化：使用向前填充（ffill），这本质上就是只使用历史数据
        # ffill()只会用前面的有效值填充后面的NaN，不会产生数据泄露
        filled = series.ffill()

        # 对于开头的NaN值（没有历史数据可用），使用默认值填充
        filled = filled.fillna(default_value)

        # 验证填充值的有效性
        if filled.isna().any() or np.isinf(filled).any():
            logger.warning("safe_fill_nans: 发现无效填充值，使用默认值替换")
            filled = filled.replace([np.inf, -np.inf], default_value).fillna(default_value)
    else:
        # 传统方法：先向前填充，然后用全局统计值填充剩余NaN
        filled = series.ffill()
        if filled.isna().any():
            # 使用非NaN值的均值（可能包含未来数据）
            hist_value = series.dropna().mean()
            if pd.isna(hist_value) or np.isinf(hist_value):
                hist_value = default_value
            filled = filled.fillna(hist_value)

    return filled

def _add_price_change_features(df_out, cfg, C, interval_str):
    """🚀 添加价格变化特征 - 通过特征注册表管理"""
    try:
        # 使用特征计算器
        from src.core.feature_calculator import get_feature_calculator

        calculator = get_feature_calculator()

        # 创建临时DataFrame用于计算
        temp_df = pd.DataFrame({'close': C}, index=df_out.index)

        # 通过注册表计算特征
        features = calculator.calculate_feature_group(temp_df, 'price_change', cfg, interval_str)

        # 将计算结果添加到输出DataFrame
        for feature_name, feature_series in features.items():
            df_out[feature_name] = feature_series
            logger.debug("_add_price_change_features: (%s) 添加特征 %s", interval_str, feature_name)

        logger.info("_add_price_change_features: (%s) 🚀 通过注册表成功添加 %d 个价格变化特征", interval_str, len(features))

    except ImportError as e:
        logger.warning("_add_price_change_features: (%s) 特征计算器不可用，使用传统方法: %s", interval_str, e)
        _add_price_change_features_legacy(df_out, cfg, C, interval_str)
    except Exception as e:
        logger.error("_add_price_change_features: (%s) 通过注册表计算特征失败，使用传统方法: %s", interval_str, e)
        _add_price_change_features_legacy(df_out, cfg, C, interval_str)

def _add_price_change_features_legacy(df_out, cfg, C, interval_str):
    """传统的价格变化特征计算方法（保持向后兼容）"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if _get_cfg('enable_price_change', default_value=False):
            periods = _get_cfg('price_change_periods', default_value=[1, 3, 5, 10])
            if periods and isinstance(periods, list):
                for p_val in periods:
                    if isinstance(p_val, int) and p_val > 0 and len(C) > p_val:
                        try:
                            df_out[f'price_change_{p_val}p'] = C.pct_change(periods=p_val).fillna(0) * 100
                        except (ValueError, TypeError) as e_pc_inner:
                            logger.warning("_add_price_change_features_legacy: (%s) 计算price_change_%dp时出错: %s", interval_str, p_val, e_pc_inner)
    except (ValueError, TypeError) as e_pc:
        logger.error("_add_price_change_features_legacy: (%s) 计算价格变化特征时出错: %s", interval_str, e_pc)
    except Exception as e_pc_unexpected:
        logger.error("_add_price_change_features_legacy: (%s) 计算价格变化特征时发生意外错误: %s", interval_str, e_pc_unexpected)

def _add_volume_features(df_out, cfg, V_feat, interval_str):
    """🚀 添加成交量特征 - 通过特征注册表管理"""
    try:
        # 使用特征计算器
        from src.core.feature_calculator import get_feature_calculator

        calculator = get_feature_calculator()

        # 创建临时DataFrame用于计算
        temp_df = pd.DataFrame({'volume': V_feat}, index=df_out.index)

        # 通过注册表计算特征
        features = calculator.calculate_feature_group(temp_df, 'volume', cfg, interval_str)

        # 将计算结果添加到输出DataFrame
        for feature_name, feature_series in features.items():
            df_out[feature_name] = feature_series
            logger.debug("_add_volume_features: (%s) 添加特征 %s", interval_str, feature_name)

        logger.info("_add_volume_features: (%s) 🚀 通过注册表成功添加 %d 个成交量特征", interval_str, len(features))

    except ImportError as e:
        logger.warning("_add_volume_features: (%s) 特征计算器不可用，使用传统方法: %s", interval_str, e)
        _add_volume_features_legacy(df_out, cfg, V_feat, interval_str)
    except Exception as e:
        logger.error("_add_volume_features: (%s) 通过注册表计算特征失败，使用传统方法: %s", interval_str, e)
        _add_volume_features_legacy(df_out, cfg, V_feat, interval_str)

def _add_volume_features_legacy(df_out, cfg, V_feat, interval_str):
    """传统的成交量特征计算方法（保持向后兼容）"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        enable_volume = _get_cfg('enable_volume', default_value=False)
        logger.info("_add_volume_features_legacy: (%s) enable_volume=%s, 数据长度=%d", interval_str, enable_volume, len(V_feat))

        if enable_volume:
            vol_avg_p = _get_cfg('volume_avg_period', default_value=20)
            if isinstance(vol_avg_p, int) and vol_avg_p > 0:
                # 计算成交量变化
                if len(V_feat) > 1:
                    try:
                        df_out['volume_change_1p'] = V_feat.pct_change(periods=1).fillna(0) * 100
                    except (ValueError, TypeError) as e_vol_change:
                        logger.warning("_add_volume_features_legacy: (%s) 计算volume_change_1p时出错: %s", interval_str, e_vol_change)
                        df_out['volume_change_1p'] = 0.0
                else:
                    df_out['volume_change_1p'] = 0.0

                # 计算成交量相对于平均值的比率
                if len(V_feat) >= max(1, vol_avg_p // 2):
                    try:
                        vol_avg = V_feat.rolling(window=vol_avg_p, min_periods=max(1, vol_avg_p // 2)).mean()
                        df_out['volume_vs_avg'] = (V_feat / vol_avg.replace(0, 1e-9)).fillna(1)
                    except (ValueError, TypeError, ZeroDivisionError) as e_vol_avg:
                        logger.warning("_add_volume_features_legacy: (%s) 计算volume_vs_avg时出错: %s", interval_str, e_vol_avg)
                        df_out['volume_vs_avg'] = 1.0
    except (ValueError, TypeError) as e_vol:
        logger.error("_add_volume_features_legacy: (%s) 计算成交量特征时出错: %s", interval_str, e_vol)
    except Exception as e_vol_unexpected:
        logger.error("_add_volume_features_legacy: (%s) 计算成交量特征时发生意外错误: %s", interval_str, e_vol_unexpected)

def _identify_candlestick_patterns(O, H, L, C, interval_str, pattern_thresholds=None):
    """
    🚀 识别常见的K线形态 - 优化版本，支持优先级/互斥处理

    Args:
        O, H, L, C: 开高低收价格序列
        interval_str: 时间间隔字符串（用于日志）
        pattern_thresholds: 形态识别阈值字典

    Returns:
        pd.Series: 包含K线形态名称的序列
    """
    try:
        # 计算基础K线特征
        body_size = (C - O).abs()
        candle_range = H - L
        upper_shadow = H - C.combine(O, max)
        lower_shadow = O.combine(C, min) - L
        is_green = C > O

        # 避免除零错误
        candle_range_safe = candle_range.replace(0, 1e-9)
        body_ratio = body_size / candle_range_safe
        upper_shadow_ratio = upper_shadow / candle_range_safe
        lower_shadow_ratio = lower_shadow / candle_range_safe

        # 初始化形态名称
        patterns = pd.Series(['Unknown'] * len(C), index=C.index)

        # 🚀 优先级形态识别系统
        # 使用配置的形态识别阈值，如果没有提供则使用默认值
        if pattern_thresholds is None:
            pattern_thresholds = {}

        # 获取阈值参数
        DOJI_THRESHOLD = pattern_thresholds.get('doji_threshold', 0.1)
        HAMMER_BODY_RATIO = pattern_thresholds.get('hammer_body_ratio', 0.3)
        HAMMER_SHADOW_RATIO = pattern_thresholds.get('hammer_shadow_ratio', 2.0)
        SHOOTING_STAR_RATIO = pattern_thresholds.get('shooting_star_ratio', 2.0)
        MARUBOZU_THRESHOLD = pattern_thresholds.get('marubozu_threshold', 0.95)
        SPINNING_TOP_THRESHOLD = pattern_thresholds.get('spinning_top_threshold', 0.6)

        # 🚀 定义形态识别函数列表，使用配置驱动的优先级系统
        pattern_detectors = [
            # 强烈反转信号形态
            {
                'name': 'Doji',
                'condition': lambda: body_ratio < DOJI_THRESHOLD,
                'description': '十字星 - 强烈反转信号'
            },

            # 特殊反转形态
            {
                'name': 'Hammer',
                'condition': lambda: (
                    (body_ratio < HAMMER_BODY_RATIO) &
                    (lower_shadow > body_size * HAMMER_SHADOW_RATIO) &
                    (upper_shadow < body_size * 0.5)
                ),
                'description': '锤子线 - 看涨反转信号'
            },
            {
                'name': 'Inverted_Hammer',
                'condition': lambda: (
                    (body_ratio < HAMMER_BODY_RATIO) &
                    (upper_shadow > body_size * HAMMER_SHADOW_RATIO) &
                    (lower_shadow < body_size * 0.5)
                ),
                'description': '倒锤子线 - 潜在反转信号'
            },
            {
                'name': 'Shooting_Star',
                'condition': lambda: (
                    (body_ratio < HAMMER_BODY_RATIO) &
                    (upper_shadow > body_size * SHOOTING_STAR_RATIO) &
                    (lower_shadow < body_size * 0.3)
                ),
                'description': '流星线 - 看跌反转信号'
            },

            # 强势延续形态
            {
                'name': 'Green_Marubozu',
                'condition': lambda: (
                    (body_ratio > MARUBOZU_THRESHOLD) &
                    (upper_shadow_ratio < 0.02) &
                    (lower_shadow_ratio < 0.02) &
                    is_green
                ),
                'description': '绿色光头光脚 - 强烈看涨'
            },
            {
                'name': 'Red_Marubozu',
                'condition': lambda: (
                    (body_ratio > MARUBOZU_THRESHOLD) &
                    (upper_shadow_ratio < 0.02) &
                    (lower_shadow_ratio < 0.02) &
                    (~is_green)
                ),
                'description': '红色光头光脚 - 强烈看跌'
            },

            # 中等强度形态
            {
                'name': 'Spinning_Top',
                'condition': lambda: (
                    (body_ratio < SPINNING_TOP_THRESHOLD) &
                    (upper_shadow_ratio > 0.15) &
                    (lower_shadow_ratio > 0.15)
                ),
                'description': '陀螺线 - 市场犹豫'
            },
            {
                'name': 'Green_Long_Body',
                'condition': lambda: (
                    (body_ratio > 0.7) &
                    (upper_shadow_ratio < 0.15) &
                    (lower_shadow_ratio < 0.15) &
                    is_green
                ),
                'description': '绿色长实体 - 看涨延续'
            },
            {
                'name': 'Red_Long_Body',
                'condition': lambda: (
                    (body_ratio > 0.7) &
                    (upper_shadow_ratio < 0.15) &
                    (lower_shadow_ratio < 0.15) &
                    (~is_green)
                ),
                'description': '红色长实体 - 看跌延续'
            },

            # 普通形态
            {
                'name': 'Normal_Green',
                'condition': lambda: is_green,
                'description': '普通绿K线'
            },
            {
                'name': 'Normal_Red',
                'condition': lambda: ~is_green,
                'description': '普通红K线'
            }
        ]

        # 🚀 使用配置驱动的优先级系统对形态检测器进行排序
        # 为每个检测器添加动态优先级
        for detector in pattern_detectors:
            detector['priority'] = _get_pattern_priority(detector['name'], pattern_thresholds)

        # 按优先级排序（数字越小优先级越高）
        pattern_detectors.sort(key=lambda x: x['priority'])

        # 🚀 使用智能覆盖逻辑进行形态识别
        for detector in pattern_detectors:
            try:
                # 计算当前形态的条件
                condition_mask = detector['condition']()

                if condition_mask.any():
                    # 对于每个匹配的位置，检查是否应该应用或覆盖
                    for idx in condition_mask[condition_mask].index:
                        current_pattern = patterns.loc[idx]
                        new_pattern = detector['name']

                        # 如果当前位置是Unknown，直接应用新形态
                        if current_pattern == 'Unknown':
                            patterns.loc[idx] = new_pattern
                        # 如果当前位置已有形态，检查是否应该覆盖
                        elif _should_override_pattern(current_pattern, new_pattern, pattern_thresholds):
                            patterns.loc[idx] = new_pattern
                            logger.debug("_identify_candlestick_patterns: (%s) 形态覆盖: %s -> %s",
                                       interval_str, current_pattern, new_pattern)

                    # 记录识别到的形态数量
                    count = condition_mask.sum()
                    if count > 0:
                        logger.debug("_identify_candlestick_patterns: (%s) 识别到 %d 个 %s (%s)",
                                   interval_str, count, detector['name'], detector['description'])

            except Exception as e:
                logger.warning("_identify_candlestick_patterns: (%s) 形态 %s 识别失败: %s",
                             interval_str, detector['name'], e)

        # 统计形态分布
        pattern_counts = patterns.value_counts()
        logger.debug("_identify_candlestick_patterns: (%s) 识别的K线形态分布: %s",
                    interval_str, dict(pattern_counts))

        return patterns

    except Exception as e:
        logger.error("_identify_candlestick_patterns: (%s) K线形态识别出错: %s", interval_str, e)
        # 返回默认值
        return pd.Series(['Unknown'] * len(C), index=C.index)

def _identify_multi_candle_patterns(O, H, L, C, patterns, interval_str, pattern_thresholds=None):
    """
    🚀 识别多K线组合形态 - 优化版本，支持优先级/互斥处理

    Args:
        O, H, L, C: 开高低收价格序列
        patterns: 单K线形态序列
        interval_str: 时间间隔字符串
        pattern_thresholds: 形态识别阈值配置字典

    Returns:
        pd.Series: 更新后的形态序列
    """
    try:
        if len(C) < 2:
            return patterns

        patterns_updated = patterns.copy()

        # 🚀 优先级多K线形态识别系统
        # 使用配置的形态识别阈值，如果没有提供则使用默认值
        if pattern_thresholds is None:
            pattern_thresholds = {}

        # 获取阈值参数
        ENGULFING_BODY_MULTIPLIER = pattern_thresholds.get('engulfing_body_multiplier', 1.2)
        MORNING_EVENING_STAR_BODY_RATIO = pattern_thresholds.get('morning_evening_star_body_ratio', 0.3)
        PIERCING_LINE_RATIO = pattern_thresholds.get('piercing_line_ratio', 0.5)  # 刺透线比例
        DARK_CLOUD_RATIO = pattern_thresholds.get('dark_cloud_ratio', 0.5)  # 乌云盖顶比例

        # 计算基础特征
        is_green = C > O
        body_size = (C - O).abs()

        # 🚀 定义多K线形态检测器列表，使用配置驱动的优先级系统
        multi_pattern_detectors = [
            {
                'name': 'Bullish_Engulfing',
                'min_candles': 2,
                'detect_func': lambda i: (
                    not is_green.iloc[i-1] and is_green.iloc[i] and
                    C.iloc[i] > O.iloc[i-1] and O.iloc[i] < C.iloc[i-1] and
                    body_size.iloc[i] > body_size.iloc[i-1] * ENGULFING_BODY_MULTIPLIER
                ),
                'description': '看涨吞没形态'
            },
            {
                'name': 'Bearish_Engulfing',
                'min_candles': 2,
                'detect_func': lambda i: (
                    is_green.iloc[i-1] and not is_green.iloc[i] and
                    O.iloc[i] > C.iloc[i-1] and C.iloc[i] < O.iloc[i-1] and
                    body_size.iloc[i] > body_size.iloc[i-1] * ENGULFING_BODY_MULTIPLIER
                ),
                'description': '看跌吞没形态'
            },
            {
                'name': 'Morning_Star',
                'min_candles': 3,
                'detect_func': lambda i: (
                    not is_green.iloc[i-2] and  # 第一根：红K线
                    body_size.iloc[i-1] < body_size.iloc[i-2] * MORNING_EVENING_STAR_BODY_RATIO and  # 第二根：小实体
                    is_green.iloc[i] and  # 第三根：绿K线
                    C.iloc[i] > (O.iloc[i-2] + C.iloc[i-2]) / 2  # 第三根收盘价超过第一根中点
                ),
                'description': '启明星形态'
            },
            {
                'name': 'Evening_Star',
                'min_candles': 3,
                'detect_func': lambda i: (
                    is_green.iloc[i-2] and  # 第一根：绿K线
                    body_size.iloc[i-1] < body_size.iloc[i-2] * MORNING_EVENING_STAR_BODY_RATIO and  # 第二根：小实体
                    not is_green.iloc[i] and  # 第三根：红K线
                    C.iloc[i] < (O.iloc[i-2] + C.iloc[i-2]) / 2  # 第三根收盘价低于第一根中点
                ),
                'description': '黄昏星形态'
            }
        ]

        # 🚀 为每个检测器添加动态优先级并排序
        for detector in multi_pattern_detectors:
            detector['priority'] = _get_pattern_priority(detector['name'], pattern_thresholds)

        # 按优先级排序（数字越小优先级越高）
        multi_pattern_detectors.sort(key=lambda x: x['priority'])

        # 🚀 使用智能覆盖逻辑进行多K线形态识别
        for detector in multi_pattern_detectors:
            try:
                min_candles = detector['min_candles']
                if len(C) < min_candles:
                    continue

                detected_indices = []
                for i in range(min_candles - 1, len(C)):
                    try:
                        if detector['detect_func'](i):
                            current_pattern = patterns_updated.iloc[i]
                            new_pattern = detector['name']

                            # 检查是否应该应用或覆盖
                            should_apply = False
                            if current_pattern == 'Unknown':
                                should_apply = True
                            elif _should_override_pattern(current_pattern, new_pattern, pattern_thresholds):
                                should_apply = True
                                logger.debug("_identify_multi_candle_patterns: (%s) 形态覆盖: %s -> %s",
                                           interval_str, current_pattern, new_pattern)

                            if should_apply:
                                patterns_updated.iloc[i] = new_pattern
                                detected_indices.append(i)

                    except Exception as e_inner:
                        logger.warning("_identify_multi_candle_patterns: (%s) 形态 %s 在位置 %d 检测失败: %s",
                                     interval_str, detector['name'], i, e_inner)

                if detected_indices:
                    logger.debug("_identify_multi_candle_patterns: (%s) 识别到 %d 个 %s",
                               interval_str, len(detected_indices), detector['description'])

            except Exception as e:
                logger.warning("_identify_multi_candle_patterns: (%s) 形态 %s 识别失败: %s",
                             interval_str, detector['name'], e)

        # 🚀 统计多K线形态，使用_is_multi_candle_pattern函数
        multi_pattern_count = 0
        for pattern in patterns_updated:
            if _is_multi_candle_pattern(pattern):
                multi_pattern_count += 1

        if multi_pattern_count > 0:
            logger.debug("_identify_multi_candle_patterns: (%s) 识别到 %d 个多K线组合形态",
                        interval_str, multi_pattern_count)

        return patterns_updated

    except Exception as e:
        logger.error("_identify_multi_candle_patterns: (%s) 多K线形态识别出错: %s", interval_str, e)
        return patterns


def _get_pattern_priority(pattern_name, pattern_thresholds=None):
    """
    🚀 获取K线形态的优先级

    Args:
        pattern_name: 形态名称
        pattern_thresholds: 配置字典

    Returns:
        int: 优先级数字（数字越小优先级越高）
    """
    if pattern_thresholds is None:
        pattern_thresholds = {}

    # 获取优先级配置
    priority_levels = pattern_thresholds.get('pattern_priority_levels', {})

    # 检查各个优先级
    if pattern_name in priority_levels.get('high_priority', []):
        return 1
    elif pattern_name in priority_levels.get('medium_priority', []):
        return 2
    elif pattern_name in priority_levels.get('low_priority', []):
        return 3
    else:
        return 4  # 默认最低优先级


def _is_multi_candle_pattern(pattern_name):
    """
    🚀 判断是否为多K线形态

    Args:
        pattern_name: 形态名称

    Returns:
        bool: 是否为多K线形态
    """
    multi_candle_patterns = {
        'Bullish_Engulfing', 'Bearish_Engulfing',
        'Morning_Star', 'Evening_Star',
        'Piercing_Line', 'Dark_Cloud_Cover',
        'Three_White_Soldiers', 'Three_Black_Crows'
    }

    return pattern_name in multi_candle_patterns


def _should_override_pattern(current_pattern, new_pattern, pattern_thresholds=None):
    """
    🚀 判断是否应该用新形态覆盖当前形态

    Args:
        current_pattern: 当前形态名称
        new_pattern: 新形态名称
        pattern_thresholds: 配置字典

    Returns:
        bool: 是否应该覆盖
    """
    if pattern_thresholds is None:
        pattern_thresholds = {}

    # 如果禁用覆盖，则不允许覆盖
    if not pattern_thresholds.get('allow_pattern_override', False):
        return False

    # 获取优先级
    current_priority = _get_pattern_priority(current_pattern, pattern_thresholds)
    new_priority = _get_pattern_priority(new_pattern, pattern_thresholds)

    # 只有新形态优先级更高时才覆盖
    return new_priority < current_priority


def _add_candle_features(df_out, cfg, C, H, L, O, interval_str):
    """添加K线形态特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        candle_cols_to_init = ['body_size','candle_range','upper_shadow','lower_shadow','is_green_candle','is_doji','close_pos_in_candle',
                       'body_size_norm','upper_shadow_norm','lower_shadow_norm','candle_range_norm']
        default_candle_vals = {k: 0.0 for k in candle_cols_to_init}; default_candle_vals['close_pos_in_candle'] = 0.5
        for col_c, def_val_c in default_candle_vals.items(): df_out[col_c] = def_val_c

        # 初始化K线形态名称列
        df_out['candlestick_pattern_name'] = 'Unknown'

        if _get_cfg('enable_candle', default_value=False):
            try:
                if len(C)>0 and len(O)>0:
                    df_out['body_size']=(C-O).abs()
                    df_out['is_green_candle']=(C>O).astype(int)
                if len(H)>0 and len(L)>0:
                    df_out['candle_range']=H-L
                    df_out['upper_shadow']=H-C.combine(O,max)
                    df_out['lower_shadow']=O.combine(C,min) - L
                    candle_range_safe = df_out['candle_range'].replace(0,1e-9)
                    df_out['is_doji']=(df_out['body_size'] < candle_range_safe*0.1).astype(int)
                    hl_diff=df_out['candle_range'].replace(0,1e-9)
                    df_out['close_pos_in_candle']=((C-L)/hl_diff).fillna(0.5).clip(0,1)

                # K线形态识别
                if _get_cfg('enable_pattern_recognition', default_value=True):
                    try:
                        logger.debug("_add_candle_features: (%s) 开始K线形态识别", interval_str)

                        # 获取形态识别阈值配置（现在参数直接在配置顶层）
                        pattern_thresholds = {
                            'doji_threshold': _get_cfg('doji_threshold', 0.1),
                            'hammer_body_ratio': _get_cfg('hammer_body_ratio', 0.3),
                            'hammer_shadow_ratio': _get_cfg('hammer_shadow_ratio', 2.0),
                            'shooting_star_ratio': _get_cfg('shooting_star_ratio', 2.0),
                            'marubozu_threshold': _get_cfg('marubozu_threshold', 0.9),
                            'spinning_top_threshold': _get_cfg('spinning_top_threshold', 0.6),
                            'engulfing_body_multiplier': _get_cfg('engulfing_body_multiplier', 1.2),
                            'morning_evening_star_body_ratio': _get_cfg('morning_evening_star_body_ratio', 0.3),
                            'doji_threshold_batac': _get_cfg('doji_threshold_batac', 0.1)
                        }

                        # 单K线形态识别
                        single_patterns = _identify_candlestick_patterns(O, H, L, C, interval_str, pattern_thresholds)

                        # 多K线组合形态识别
                        final_patterns = _identify_multi_candle_patterns(O, H, L, C, single_patterns, interval_str, pattern_thresholds)

                        # 更新形态名称列
                        df_out['candlestick_pattern_name'] = final_patterns

                        # 统计最终形态分布
                        pattern_counts = final_patterns.value_counts()
                        top_patterns = pattern_counts.head(5)
                        logger.info("_add_candle_features: (%s) K线形态识别完成，识别到 %d 种形态，前5种: %s",
                                   interval_str, len(pattern_counts), dict(top_patterns))

                    except Exception as e_pattern:
                        logger.warning("_add_candle_features: (%s) K线形态识别出错: %s", interval_str, e_pattern)
                        df_out['candlestick_pattern_name'] = 'Unknown'

            except (ValueError, TypeError, ZeroDivisionError) as e_candle_basic:
                logger.warning("_add_candle_features: (%s) 计算基本K线特征时出错: %s", interval_str, e_candle_basic)

            atr_p_for_candle_norm = _get_cfg('atr_period', default_value=14)
            if isinstance(atr_p_for_candle_norm, int) and atr_p_for_candle_norm > 0:
                try:
                    if len(H) >= atr_p_for_candle_norm and len(L) >= atr_p_for_candle_norm and len(C) >= atr_p_for_candle_norm:
                        atr_for_candle = pta.atr(H,L,C,length=atr_p_for_candle_norm)
                        if atr_for_candle is not None and not atr_for_candle.isnull().all():
                            # 使用安全填充替代bfill
                            atr_replaced = atr_for_candle.replace(0,1e-9)
                            atr_safe = safe_fill_nans(atr_replaced, default_value=1e-9, use_historical_only=True)
                            df_out['body_size_norm']=(df_out['body_size']/atr_safe).fillna(0)
                            df_out['upper_shadow_norm']=(df_out['upper_shadow']/atr_safe).fillna(0)
                            df_out['lower_shadow_norm']=(df_out['lower_shadow']/atr_safe).fillna(0)
                            df_out['candle_range_norm']=(df_out['candle_range']/atr_safe).fillna(0)
                except (ValueError, TypeError, ZeroDivisionError) as e_candle_norm:
                    logger.warning("_add_candle_features: (%s) 计算K线形态归一化部分出错: %s", interval_str, e_candle_norm)
                except Exception as e_candle_norm_unexpected:
                    logger.error("_add_candle_features: (%s) 计算K线形态归一化部分发生意外错误: %s", interval_str, e_candle_norm_unexpected)

        # 添加平滑K线特征
        if _get_cfg('enable_candle', default_value=False):
            smoothing_period_candle = 3; min_periods_candle = max(1, smoothing_period_candle -1)
            try:
                if 'upper_shadow' in df_out.columns and len(df_out['upper_shadow']) >= min_periods_candle:
                    rolling_mean = df_out['upper_shadow'].rolling(window=smoothing_period_candle, min_periods=min_periods_candle).mean()
                    df_out[f'upper_shadow_smooth{smoothing_period_candle}p'] = safe_fill_nans(rolling_mean, default_value=0, use_historical_only=True)
                else:
                    df_out[f'upper_shadow_smooth{smoothing_period_candle}p'] = df_out.get('upper_shadow', pd.Series(0.0, index=df_out.index))

                if 'close_pos_in_candle' in df_out.columns and len(df_out['close_pos_in_candle']) >= min_periods_candle:
                    rolling_mean = df_out['close_pos_in_candle'].rolling(window=smoothing_period_candle, min_periods=min_periods_candle).mean()
                    df_out[f'close_pos_in_candle_smooth{smoothing_period_candle}p'] = safe_fill_nans(rolling_mean, default_value=0.5, use_historical_only=True)
                else:
                    df_out[f'close_pos_in_candle_smooth{smoothing_period_candle}p'] = df_out.get('close_pos_in_candle', pd.Series(0.5, index=df_out.index))

                if 'body_size' in df_out.columns and len(df_out['body_size']) >= min_periods_candle:
                    rolling_mean = df_out['body_size'].rolling(window=smoothing_period_candle, min_periods=min_periods_candle).mean()
                    df_out[f'body_size_smooth{smoothing_period_candle}p'] = safe_fill_nans(rolling_mean, default_value=0, use_historical_only=True)
                else:
                    df_out[f'body_size_smooth{smoothing_period_candle}p'] = df_out.get('body_size', pd.Series(0.0, index=df_out.index))
            except (ValueError, TypeError) as e_candle_smooth:
                logger.warning("_add_candle_features: (%s) 计算K线平滑特征时出错: %s", interval_str, e_candle_smooth)
    except (ValueError, TypeError) as e_candle_sec:
        logger.error("_add_candle_features: (%s) K线特征部分出错: %s", interval_str, e_candle_sec)
    except Exception as e_candle_sec_unexpected:
        logger.error("_add_candle_features: (%s) K线特征部分发生意外错误: %s", interval_str, e_candle_sec_unexpected)

def _add_technical_indicators(df_out, cfg, C, H, L, interval_str):
    """添加技术指标特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        enable_ta_in_cfg = _get_cfg('enable_ta', default_value=False)
        hma_period = _get_cfg('hma_period', 14)
        rsi_period = _get_cfg('rsi_period', 14)
        logger.info("_add_technical_indicators: (%s) enable_ta=%s, hma_period=%s, rsi_period=%s, 数据长度=%d",
                   interval_str, enable_ta_in_cfg, hma_period, rsi_period, len(C))

        if enable_ta_in_cfg:
            rsi_p = _get_cfg('rsi_period', 14); macd_f, macd_s, macd_sg = _get_cfg('macd_fast', 12), _get_cfg('macd_slow', 26), _get_cfg('macd_sign', 9)
            atr_p_ta = _get_cfg('atr_period', 14); stoch_k, stoch_d, stoch_sk = _get_cfg('stoch_k', 14), _get_cfg('stoch_d', 3), _get_cfg('stoch_smooth_k', 3)
            hma_p = _get_cfg('hma_period', 14); kc_p = _get_cfg('kc_period', 20); kc_atr_p = _get_cfg('kc_atr_period', 10); kc_mult = _get_cfg('kc_multiplier', 2.0)
            # 🚨 修复：为WILLR和CCI使用专门的周期参数
            willr_p = _get_cfg('willr_period', 14)  # Williams %R 专门周期
            cci_p = _get_cfg('cci_period', 14)      # CCI 专门周期
            cci_constant_cfg = _get_cfg('cci_constant', 0.015)
            enable_ta_derived = _get_cfg('enable_ta_derived_features', default_value=True)

            # 计算HMA
            if isinstance(hma_p, int) and hma_p > 0 and len(C) >= hma_p:
                try:
                    df_out[f'HMA_{hma_p}'] = pta.hma(C, length=hma_p)
                except (ValueError, TypeError) as e_hma:
                    logger.warning("_add_technical_indicators: (%s) 计算HMA时出错: %s", interval_str, e_hma)

            # 计算KC
            if isinstance(kc_p, int) and kc_p > 0 and isinstance(kc_atr_p, int) and kc_atr_p > 0 and isinstance(kc_mult, (float, int)) and kc_mult > 0 and len(C) >= max(kc_p, kc_atr_p):
                try:
                    kc_df = pta.kc(H, L, C, length=kc_p, atr_length=kc_atr_p, scalar=kc_mult, mamode="ema")
                    if kc_df is not None and not kc_df.empty:
                        for col in kc_df.columns: df_out[col] = kc_df[col]
                except (ValueError, TypeError) as e_kc:
                    logger.warning("_add_technical_indicators: (%s) 计算KC时出错: %s", interval_str, e_kc)

            # 计算RSI
            if isinstance(rsi_p, int) and rsi_p > 0 and len(C) >= rsi_p:
                try:
                    df_out[f'RSI_{rsi_p}'] = pta.rsi(C, length=rsi_p)
                except (ValueError, TypeError) as e_rsi:
                    logger.warning("_add_technical_indicators: (%s) 计算RSI时出错: %s", interval_str, e_rsi)

            # 计算MACD
            if isinstance(macd_f,int) and macd_f > 0 and isinstance(macd_s,int) and macd_s > macd_f and isinstance(macd_sg,int) and macd_sg > 0 and len(C) >= macd_s + macd_sg -1 :
                try:
                    macd_df = pta.macd(C, fast=macd_f, slow=macd_s, signal=macd_sg)
                    if macd_df is not None and not macd_df.empty:
                        for col in macd_df.columns: df_out[col] = macd_df[col]
                except (ValueError, TypeError) as e_macd:
                    logger.warning("_add_technical_indicators: (%s) 计算MACD时出错: %s", interval_str, e_macd)

            # 计算ATR
            if isinstance(atr_p_ta, int) and atr_p_ta > 0 and len(H) >= atr_p_ta and len(L) >= atr_p_ta and len(C) >= atr_p_ta:
                try:
                    df_out[f'ATRr_{atr_p_ta}'] = pta.atr(H, L, C, length=atr_p_ta)
                except (ValueError, TypeError) as e_atr:
                    logger.warning("_add_technical_indicators: (%s) 计算ATR时出错: %s", interval_str, e_atr)

            # 计算STOCH
            if isinstance(stoch_k,int) and stoch_k > 0 and isinstance(stoch_d,int) and stoch_d > 0 and isinstance(stoch_sk,int) and stoch_sk > 0 and len(H) >= stoch_k :
                try:
                    stoch_df = pta.stoch(H, L, C, k=stoch_k, d=stoch_d, smooth_k=stoch_sk)
                    if stoch_df is not None and not stoch_df.empty:
                        for col in stoch_df.columns: df_out[col] = stoch_df[col]
                except (ValueError, TypeError) as e_stoch:
                    logger.warning("_add_technical_indicators: (%s) 计算STOCH时出错: %s", interval_str, e_stoch)

            # 🚨 修复：计算WILLR，使用专门的周期参数
            if isinstance(willr_p, int) and willr_p > 0 and len(H) >= willr_p and len(L) >= willr_p and len(C) >= willr_p:
                try:
                    df_out[f'WILLR_{willr_p}'] = pta.willr(H, L, C, length=willr_p)
                    logger.debug("_add_technical_indicators: (%s) 成功计算WILLR_%d", interval_str, willr_p)
                except (ValueError, TypeError) as e_willr:
                    logger.warning("_add_technical_indicators: (%s) 计算WILLR_%d时出错: %s", interval_str, willr_p, e_willr)

            # 🚨 修复：计算CCI，使用专门的周期参数
            if isinstance(cci_p, int) and cci_p > 0 and len(H) >= cci_p and len(L) >= cci_p and len(C) >= cci_p:
                try:
                    df_out[f'CCI_{cci_p}_{cci_constant_cfg}'] = pta.cci(H, L, C, length=cci_p, constant=cci_constant_cfg)
                    logger.debug("_add_technical_indicators: (%s) 成功计算CCI_%d_%s", interval_str, cci_p, cci_constant_cfg)
                except (ValueError, TypeError) as e_cci:
                    logger.warning("_add_technical_indicators: (%s) 计算CCI_%d时出错: %s", interval_str, cci_p, e_cci)

            # 🎯 新增：EMA距离特征（适用于UP和DOWN模型）
            # 根据模型类型选择合适的参数
            ema_short_period = _get_cfg('ema_short_period_up', _get_cfg('ema_short_period_down', 10))
            ema_long_period = _get_cfg('ema_long_period_up', _get_cfg('ema_long_period_down', 30))

            if (isinstance(ema_short_period, int) and ema_short_period > 0 and
                isinstance(ema_long_period, int) and ema_long_period > 0 and
                len(C) >= max(ema_short_period, ema_long_period)):
                try:
                    ema_short = pta.ema(C, length=ema_short_period)
                    ema_long = pta.ema(C, length=ema_long_period)

                    if ema_short is not None and ema_long is not None:
                        # 计算EMA距离（绝对值和百分比）
                        ema_distance_abs = ema_short - ema_long
                        ema_distance_pct = ((ema_short - ema_long) / ema_long.replace(0, 1e-9)) * 100

                        df_out['ema_distance_abs'] = ema_distance_abs.fillna(0)
                        df_out['ema_distance_pct'] = ema_distance_pct.fillna(0)

                        # 计算多头趋势强度（距离越大且短期在上，代表越强的多头趋势）
                        bullish_strength = np.where(ema_distance_abs > 0, ema_distance_pct, 0)
                        df_out['ema_bullish_strength'] = pd.Series(bullish_strength, index=df_out.index).fillna(0)

                        # 🎯 新增：计算空头趋势强度（距离越大且短期在下，代表越强的空头趋势）
                        bearish_strength = np.where(ema_distance_abs < 0, -ema_distance_pct, 0)
                        df_out['ema_bearish_strength'] = pd.Series(bearish_strength, index=df_out.index).fillna(0)

                        logger.debug("_add_technical_indicators: (%s) 成功计算EMA距离特征 (短期:%d, 长期:%d)",
                                   interval_str, ema_short_period, ema_long_period)
                    else:
                        df_out['ema_distance_abs'] = 0.0
                        df_out['ema_distance_pct'] = 0.0
                        df_out['ema_bullish_strength'] = 0.0
                        df_out['ema_bearish_strength'] = 0.0
                except (ValueError, TypeError) as e_ema_distance:
                    logger.warning("_add_technical_indicators: (%s) 计算EMA距离特征时出错: %s", interval_str, e_ema_distance)
                    df_out['ema_distance_abs'] = 0.0
                    df_out['ema_distance_pct'] = 0.0
                    df_out['ema_bullish_strength'] = 0.0
                    df_out['ema_bearish_strength'] = 0.0
            else:
                df_out['ema_distance_abs'] = 0.0
                df_out['ema_distance_pct'] = 0.0
                df_out['ema_bullish_strength'] = 0.0
                df_out['ema_bearish_strength'] = 0.0

            # 🎯 新增：布林带突破强度特征（适用于UP和DOWN模型）
            bb_period = _get_cfg('bb_period', 20)
            bb_std = _get_cfg('bb_std', 2.0)
            if (isinstance(bb_period, int) and bb_period > 0 and
                isinstance(bb_std, (int, float)) and bb_std > 0 and
                len(C) >= bb_period):
                try:
                    bb_df = pta.bbands(C, length=bb_period, std=bb_std)

                    if bb_df is not None and not bb_df.empty:
                        # 获取布林带上轨和下轨
                        bb_upper_col = [col for col in bb_df.columns if 'BBU' in col.upper() or 'UPPER' in col.upper()]
                        bb_lower_col = [col for col in bb_df.columns if 'BBL' in col.upper() or 'LOWER' in col.upper()]

                        if bb_upper_col:
                            bb_upper = bb_df[bb_upper_col[0]]

                            # 计算价格突破布林带上轨的强度
                            # 强度 = (价格 - 上轨) / 上轨 * 100，只有当价格 > 上轨时才为正值
                            upper_breakout_strength = np.where(
                                C > bb_upper,
                                ((C - bb_upper) / bb_upper.replace(0, 1e-9)) * 100,
                                0
                            )
                            df_out['bb_upper_breakout_strength'] = pd.Series(upper_breakout_strength, index=df_out.index).fillna(0)

                            # 额外特征：价格相对于布林带上轨的位置
                            price_vs_bb_upper = ((C - bb_upper) / bb_upper.replace(0, 1e-9)) * 100
                            df_out['price_vs_bb_upper_pct'] = price_vs_bb_upper.fillna(0)
                        else:
                            df_out['bb_upper_breakout_strength'] = 0.0
                            df_out['price_vs_bb_upper_pct'] = 0.0

                        # 🎯 新增：DOWN模型专用 - 布林带下轨突破特征
                        if bb_lower_col:
                            bb_lower = bb_df[bb_lower_col[0]]

                            # 计算价格突破布林带下轨的强度
                            # 强度 = (下轨 - 价格) / 下轨 * 100，只有当价格 < 下轨时才为正值
                            lower_breakout_strength = np.where(
                                C < bb_lower,
                                ((bb_lower - C) / bb_lower.replace(0, 1e-9)) * 100,
                                0
                            )
                            df_out['bb_lower_breakout_strength'] = pd.Series(lower_breakout_strength, index=df_out.index).fillna(0)

                            # 额外特征：价格相对于布林带下轨的位置
                            price_vs_bb_lower = ((C - bb_lower) / bb_lower.replace(0, 1e-9)) * 100
                            df_out['price_vs_bb_lower_pct'] = price_vs_bb_lower.fillna(0)
                        else:
                            df_out['bb_lower_breakout_strength'] = 0.0
                            df_out['price_vs_bb_lower_pct'] = 0.0

                        logger.debug("_add_technical_indicators: (%s) 成功计算布林带突破强度特征 (周期:%d, 标准差:%.1f)",
                                   interval_str, bb_period, bb_std)
                    else:
                        df_out['bb_upper_breakout_strength'] = 0.0
                        df_out['price_vs_bb_upper_pct'] = 0.0
                        df_out['bb_lower_breakout_strength'] = 0.0
                        df_out['price_vs_bb_lower_pct'] = 0.0
                except (ValueError, TypeError) as e_bb:
                    logger.warning("_add_technical_indicators: (%s) 计算布林带突破强度特征时出错: %s", interval_str, e_bb)
                    df_out['bb_upper_breakout_strength'] = 0.0
                    df_out['price_vs_bb_upper_pct'] = 0.0
                    df_out['bb_lower_breakout_strength'] = 0.0
                    df_out['price_vs_bb_lower_pct'] = 0.0
            else:
                df_out['bb_upper_breakout_strength'] = 0.0
                df_out['price_vs_bb_upper_pct'] = 0.0
                df_out['bb_lower_breakout_strength'] = 0.0
                df_out['price_vs_bb_lower_pct'] = 0.0

            # 重命名列和设置默认值 (传入新的周期参数)
            _rename_and_set_ta_defaults(df_out, C, hma_p, kc_p, kc_mult, rsi_p, macd_f, macd_s, macd_sg, atr_p_ta, stoch_k, stoch_d, stoch_sk, willr_p, cci_p, cci_constant_cfg, interval_str)

            # 🎯 新增：计算自适应归一化特征
            _calculate_adaptive_normalized_features(df_out, cfg, interval_str)

            # 计算衍生指标（如果启用）
            if enable_ta_derived:
                _calculate_ta_derived_features(df_out, C, hma_p, kc_p, kc_mult, interval_str)

    except (ValueError, TypeError) as e_ta_sec:
        logger.error("_add_technical_indicators: (%s) TA指标部分出错: %s", interval_str, e_ta_sec)
    except Exception as e_ta_sec_unexpected:
        logger.error("_add_technical_indicators: (%s) TA指标部分发生意外错误: %s", interval_str, e_ta_sec_unexpected)
        logger.debug(traceback.format_exc(limit=1))

def _rename_and_set_ta_defaults(df_out, C, hma_p, kc_p, kc_mult, rsi_p, macd_f, macd_s, macd_sg, atr_p_ta, stoch_k, stoch_d, stoch_sk, willr_p, cci_p, cci_constant_cfg, interval_str):
    """重命名TA列并设置默认值 - 修复版，支持独立的WILLR和CCI周期"""
    try:
        # 重命名列
        rename_map = {
            f'KCLe_{kc_p}_{kc_mult}': 'KC_lower',
            f'KCBe_{kc_p}_{kc_mult}': 'KC_middle',
            f'KCUe_{kc_p}_{kc_mult}': 'KC_upper',
            f'MACD_{macd_f}_{macd_s}_{macd_sg}': 'MACD',
            f'MACDh_{macd_f}_{macd_s}_{macd_sg}': 'MACD_histogram',
            f'MACDs_{macd_f}_{macd_s}_{macd_sg}': 'MACD_signal',
            f'STOCHk_{stoch_k}_{stoch_d}_{stoch_sk}': 'STOCH_k',
            f'STOCHd_{stoch_k}_{stoch_d}_{stoch_sk}': 'STOCH_d'
        }
        df_out.rename(columns=rename_map, inplace=True)

        # 🚨 修复：设置默认值，使用正确的周期参数
        ta_defaults = {
            f'HMA_{hma_p}': C.iloc[-1] if len(C) > 0 else 0.0,
            'KC_lower': C.iloc[-1] * 0.98 if len(C) > 0 else 0.0,
            'KC_middle': C.iloc[-1] if len(C) > 0 else 0.0,
            'KC_upper': C.iloc[-1] * 1.02 if len(C) > 0 else 0.0,
            f'RSI_{rsi_p}': 50.0,
            'MACD': 0.0,
            'MACD_histogram': 0.0,
            'MACD_signal': 0.0,
            f'ATRr_{atr_p_ta}': 0.0,
            'STOCH_k': 50.0,
            'STOCH_d': 50.0,
            f'WILLR_{willr_p}': -50.0,                    # 使用专门的WILLR周期
            f'CCI_{cci_p}_{cci_constant_cfg}': 0.0,       # 使用专门的CCI周期
            # 🎯 新增：EMA距离特征默认值（UP和DOWN模型通用）
            'ema_distance_abs': 0.0,
            'ema_distance_pct': 0.0,
            'ema_bullish_strength': 0.0,
            'ema_bearish_strength': 0.0,
            # 🎯 新增：布林带突破强度特征默认值（UP和DOWN模型通用）
            'bb_upper_breakout_strength': 0.0,
            'price_vs_bb_upper_pct': 0.0,
            'bb_lower_breakout_strength': 0.0,
            'price_vs_bb_lower_pct': 0.0,
        }

        for col, default_val in ta_defaults.items():
            if col in df_out.columns:
                df_out[col] = safe_fill_nans(df_out[col], default_value=default_val)
            else:
                df_out[col] = default_val

    except Exception as e_rename:
        logger.warning("_rename_and_set_ta_defaults: (%s) 重命名和设置默认值时出错: %s", interval_str, e_rename)

def _calculate_ta_derived_features(df_out, C, hma_p, kc_p, kc_mult, interval_str):
    """计算TA衍生特征"""
    try:
        # 计算价格相对于HMA的位置
        if f'HMA_{hma_p}' in df_out.columns and len(C) > 0:
            hma_col = df_out[f'HMA_{hma_p}']
            df_out['price_vs_hma'] = ((C - hma_col) / hma_col.replace(0, 1e-9)).fillna(0)
        else:
            df_out['price_vs_hma'] = 0.0

        # 计算价格在KC通道中的位置
        if all(col in df_out.columns for col in ['KC_lower', 'KC_upper']) and len(C) > 0:
            kc_range = (df_out['KC_upper'] - df_out['KC_lower']).replace(0, 1e-9)
            df_out['price_pos_in_kc'] = ((C - df_out['KC_lower']) / kc_range).fillna(0.5).clip(0, 1)
        else:
            df_out['price_pos_in_kc'] = 0.5

        # 计算MACD信号
        if all(col in df_out.columns for col in ['MACD', 'MACD_signal']):
            df_out['macd_above_signal'] = (df_out['MACD'] > df_out['MACD_signal']).astype(int)
        else:
            df_out['macd_above_signal'] = 0

    except Exception as e_derived:
        logger.warning("_calculate_ta_derived_features: (%s) 计算衍生特征时出错: %s", interval_str, e_derived)


def _calculate_adaptive_normalized_features(df_out, cfg, interval_str):
    """
    🎯 计算自适应归一化特征 - 核心优化建议2.2的实现

    为震荡指标、趋势指标和波动率指标创建自适应归一化版本，
    提供更稳定和可比的特征信号

    Args:
        df_out: 输出DataFrame
        cfg: 配置字典
        interval_str: 时间间隔字符串
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 是否启用自适应归一化
        if not _get_cfg('enable_adaptive_normalization', default_value=True):
            logger.debug("_calculate_adaptive_normalized_features: (%s) 自适应归一化未启用", interval_str)
            return

        logger.info("_calculate_adaptive_normalized_features: (%s) 开始计算自适应归一化特征", interval_str)

        # 归一化窗口配置
        short_window = _get_cfg('adaptive_norm_short_window', default_value=20)
        medium_window = _get_cfg('adaptive_norm_medium_window', default_value=50)
        long_window = _get_cfg('adaptive_norm_long_window', default_value=100)

        # === 1. 震荡指标自适应归一化 ===
        _normalize_oscillator_indicators(df_out, short_window, medium_window, long_window, interval_str)

        # === 2. 趋势指标相对化 ===
        _normalize_trend_indicators(df_out, short_window, medium_window, long_window, interval_str)

        # === 3. 波动率指标标准化 ===
        _normalize_volatility_indicators(df_out, short_window, medium_window, long_window, interval_str)

        # === 4. 成交量指标归一化 ===
        _normalize_volume_indicators(df_out, short_window, medium_window, long_window, interval_str)

        logger.info("_calculate_adaptive_normalized_features: (%s) 自适应归一化特征计算完成", interval_str)

    except Exception as e:
        logger.error("_calculate_adaptive_normalized_features: (%s) 计算自适应归一化特征时出错: %s", interval_str, e)


def _normalize_oscillator_indicators(df_out, short_window, medium_window, long_window, interval_str):
    """归一化震荡指标"""
    try:
        # 1. RSI自适应归一化
        rsi_cols = [col for col in df_out.columns if col.startswith('RSI_')]
        for rsi_col in rsi_cols:
            if rsi_col in df_out.columns:
                rsi_values = df_out[rsi_col]

                # 短期Z-Score（快速反应）
                rsi_mean_short = rsi_values.rolling(window=short_window, min_periods=5).mean()
                rsi_std_short = rsi_values.rolling(window=short_window, min_periods=5).std()
                df_out[f'{rsi_col}_zscore_short'] = (rsi_values - rsi_mean_short) / (rsi_std_short + 1e-9)

                # 中期Z-Score（平衡）
                rsi_mean_medium = rsi_values.rolling(window=medium_window, min_periods=10).mean()
                rsi_std_medium = rsi_values.rolling(window=medium_window, min_periods=10).std()
                df_out[f'{rsi_col}_zscore_medium'] = (rsi_values - rsi_mean_medium) / (rsi_std_medium + 1e-9)

                # 长期Z-Score（稳定）
                rsi_mean_long = rsi_values.rolling(window=long_window, min_periods=20).mean()
                rsi_std_long = rsi_values.rolling(window=long_window, min_periods=20).std()
                df_out[f'{rsi_col}_zscore_long'] = (rsi_values - rsi_mean_long) / (rsi_std_long + 1e-9)

                # 百分位数归一化（更稳健）
                rsi_percentile = rsi_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
                df_out[f'{rsi_col}_percentile'] = rsi_percentile

                # 相对强度（相对于历史极值）
                rsi_min = rsi_values.rolling(window=long_window, min_periods=20).min()
                rsi_max = rsi_values.rolling(window=long_window, min_periods=20).max()
                df_out[f'{rsi_col}_relative_strength'] = (rsi_values - rsi_min) / (rsi_max - rsi_min + 1e-9)

        # 2. Stochastic自适应归一化
        stoch_cols = [col for col in df_out.columns if col.startswith('STOCH_')]
        for stoch_col in stoch_cols:
            if stoch_col in df_out.columns:
                stoch_values = df_out[stoch_col]

                # Z-Score归一化
                stoch_mean = stoch_values.rolling(window=medium_window, min_periods=10).mean()
                stoch_std = stoch_values.rolling(window=medium_window, min_periods=10).std()
                df_out[f'{stoch_col}_zscore'] = (stoch_values - stoch_mean) / (stoch_std + 1e-9)

                # 百分位数归一化
                stoch_percentile = stoch_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
                df_out[f'{stoch_col}_percentile'] = stoch_percentile

        # 3. Williams %R自适应归一化
        willr_cols = [col for col in df_out.columns if col.startswith('WILLR_')]
        for willr_col in willr_cols:
            if willr_col in df_out.columns:
                willr_values = df_out[willr_col]

                # Z-Score归一化
                willr_mean = willr_values.rolling(window=medium_window, min_periods=10).mean()
                willr_std = willr_values.rolling(window=medium_window, min_periods=10).std()
                df_out[f'{willr_col}_zscore'] = (willr_values - willr_mean) / (willr_std + 1e-9)

                # 相对位置（转换为0-1范围）
                willr_min = willr_values.rolling(window=long_window, min_periods=20).min()
                willr_max = willr_values.rolling(window=long_window, min_periods=20).max()
                df_out[f'{willr_col}_relative_pos'] = (willr_values - willr_min) / (willr_max - willr_min + 1e-9)

        # 4. CCI自适应归一化
        cci_cols = [col for col in df_out.columns if col.startswith('CCI_')]
        for cci_col in cci_cols:
            if cci_col in df_out.columns:
                cci_values = df_out[cci_col]

                # Z-Score归一化
                cci_mean = cci_values.rolling(window=medium_window, min_periods=10).mean()
                cci_std = cci_values.rolling(window=medium_window, min_periods=10).std()
                df_out[f'{cci_col}_zscore'] = (cci_values - cci_mean) / (cci_std + 1e-9)

                # 百分位数归一化
                cci_percentile = cci_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
                df_out[f'{cci_col}_percentile'] = cci_percentile

        logger.debug("_normalize_oscillator_indicators: (%s) 震荡指标归一化完成", interval_str)

    except Exception as e:
        logger.error("_normalize_oscillator_indicators: (%s) 震荡指标归一化失败: %s", interval_str, e)


def _normalize_trend_indicators(df_out, short_window, medium_window, long_window, interval_str):
    """归一化趋势指标"""
    try:
        # 1. MACD自适应归一化
        if 'MACD' in df_out.columns:
            macd_values = df_out['MACD']

            # Z-Score归一化
            macd_mean = macd_values.rolling(window=medium_window, min_periods=10).mean()
            macd_std = macd_values.rolling(window=medium_window, min_periods=10).std()
            df_out['MACD_zscore'] = (macd_values - macd_mean) / (macd_std + 1e-9)

            # 百分位数归一化
            macd_percentile = macd_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['MACD_percentile'] = macd_percentile

            # 相对强度（相对于历史极值）
            macd_min = macd_values.rolling(window=long_window, min_periods=20).min()
            macd_max = macd_values.rolling(window=long_window, min_periods=20).max()
            df_out['MACD_relative_strength'] = (macd_values - macd_min) / (macd_max - macd_min + 1e-9)

        # 2. MACD Histogram自适应归一化
        if 'MACD_histogram' in df_out.columns:
            macd_hist_values = df_out['MACD_histogram']

            # Z-Score归一化
            macd_hist_mean = macd_hist_values.rolling(window=medium_window, min_periods=10).mean()
            macd_hist_std = macd_hist_values.rolling(window=medium_window, min_periods=10).std()
            df_out['MACD_histogram_zscore'] = (macd_hist_values - macd_hist_mean) / (macd_hist_std + 1e-9)

            # 百分位数归一化
            macd_hist_percentile = macd_hist_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['MACD_histogram_percentile'] = macd_hist_percentile

        # 3. ADX自适应归一化
        adx_cols = [col for col in df_out.columns if 'adx' in col.lower() and 'value' in col.lower()]
        for adx_col in adx_cols:
            if adx_col in df_out.columns:
                adx_values = df_out[adx_col]

                # Z-Score归一化
                adx_mean = adx_values.rolling(window=medium_window, min_periods=10).mean()
                adx_std = adx_values.rolling(window=medium_window, min_periods=10).std()
                df_out[f'{adx_col}_zscore'] = (adx_values - adx_mean) / (adx_std + 1e-9)

                # 百分位数归一化
                adx_percentile = adx_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
                df_out[f'{adx_col}_percentile'] = adx_percentile

                # 趋势强度分级（0-1标准化）
                df_out[f'{adx_col}_strength_norm'] = np.clip(adx_values / 100.0, 0, 1)

        # 4. EMA距离特征归一化
        if 'ema_distance_pct' in df_out.columns:
            ema_dist_values = df_out['ema_distance_pct']

            # Z-Score归一化
            ema_dist_mean = ema_dist_values.rolling(window=medium_window, min_periods=10).mean()
            ema_dist_std = ema_dist_values.rolling(window=medium_window, min_periods=10).std()
            df_out['ema_distance_pct_zscore'] = (ema_dist_values - ema_dist_mean) / (ema_dist_std + 1e-9)

            # 百分位数归一化
            ema_dist_percentile = ema_dist_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['ema_distance_pct_percentile'] = ema_dist_percentile

        logger.debug("_normalize_trend_indicators: (%s) 趋势指标归一化完成", interval_str)

    except Exception as e:
        logger.error("_normalize_trend_indicators: (%s) 趋势指标归一化失败: %s", interval_str, e)


def _normalize_volatility_indicators(df_out, short_window, medium_window, long_window, interval_str):
    """归一化波动率指标"""
    try:
        # 1. ATR自适应归一化
        atr_cols = [col for col in df_out.columns if col.startswith('ATRr_')]
        for atr_col in atr_cols:
            if atr_col in df_out.columns:
                atr_values = df_out[atr_col]

                # Z-Score归一化
                atr_mean = atr_values.rolling(window=medium_window, min_periods=10).mean()
                atr_std = atr_values.rolling(window=medium_window, min_periods=10).std()
                df_out[f'{atr_col}_zscore'] = (atr_values - atr_mean) / (atr_std + 1e-9)

                # 百分位数归一化
                atr_percentile = atr_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
                df_out[f'{atr_col}_percentile'] = atr_percentile

                # 相对波动率强度
                atr_min = atr_values.rolling(window=long_window, min_periods=20).min()
                atr_max = atr_values.rolling(window=long_window, min_periods=20).max()
                df_out[f'{atr_col}_relative_volatility'] = (atr_values - atr_min) / (atr_max - atr_min + 1e-9)

                # 波动率状态分类（低、中、高）
                atr_low_threshold = atr_values.rolling(window=long_window, min_periods=20).quantile(0.33)
                atr_high_threshold = atr_values.rolling(window=long_window, min_periods=20).quantile(0.67)
                df_out[f'{atr_col}_volatility_regime'] = np.where(
                    atr_values <= atr_low_threshold, 0,  # 低波动
                    np.where(atr_values >= atr_high_threshold, 2, 1)  # 高波动 vs 中波动
                )

        # 2. 布林带宽度归一化
        if 'bb_width' in df_out.columns:
            bb_width_values = df_out['bb_width']

            # Z-Score归一化
            bb_width_mean = bb_width_values.rolling(window=medium_window, min_periods=10).mean()
            bb_width_std = bb_width_values.rolling(window=medium_window, min_periods=10).std()
            df_out['bb_width_zscore'] = (bb_width_values - bb_width_mean) / (bb_width_std + 1e-9)

            # 百分位数归一化
            bb_width_percentile = bb_width_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['bb_width_percentile'] = bb_width_percentile

        # 3. 布林带位置归一化
        if 'bb_position' in df_out.columns:
            bb_pos_values = df_out['bb_position']

            # Z-Score归一化（相对于0.5中位）
            bb_pos_centered = bb_pos_values - 0.5
            bb_pos_mean = bb_pos_centered.rolling(window=medium_window, min_periods=10).mean()
            bb_pos_std = bb_pos_centered.rolling(window=medium_window, min_periods=10).std()
            df_out['bb_position_zscore'] = (bb_pos_centered - bb_pos_mean) / (bb_pos_std + 1e-9)

            # 极值位置检测
            df_out['bb_position_extreme'] = np.where(
                (bb_pos_values <= 0.05) | (bb_pos_values >= 0.95), 1, 0
            )

        logger.debug("_normalize_volatility_indicators: (%s) 波动率指标归一化完成", interval_str)

    except Exception as e:
        logger.error("_normalize_volatility_indicators: (%s) 波动率指标归一化失败: %s", interval_str, e)


def _normalize_volume_indicators(df_out, short_window, medium_window, long_window, interval_str):
    """归一化成交量指标"""
    try:
        # 1. 成交量比率归一化
        if 'volume_ratio' in df_out.columns:
            vol_ratio_values = df_out['volume_ratio']

            # Z-Score归一化
            vol_ratio_mean = vol_ratio_values.rolling(window=medium_window, min_periods=10).mean()
            vol_ratio_std = vol_ratio_values.rolling(window=medium_window, min_periods=10).std()
            df_out['volume_ratio_zscore'] = (vol_ratio_values - vol_ratio_mean) / (vol_ratio_std + 1e-9)

            # 百分位数归一化
            vol_ratio_percentile = vol_ratio_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['volume_ratio_percentile'] = vol_ratio_percentile

            # 成交量异常检测（相对于历史）
            vol_ratio_q95 = vol_ratio_values.rolling(window=long_window, min_periods=20).quantile(0.95)
            df_out['volume_ratio_anomaly'] = (vol_ratio_values > vol_ratio_q95).astype(int)

        # 2. 成交量vs平均值归一化
        if 'volume_vs_avg' in df_out.columns:
            vol_vs_avg_values = df_out['volume_vs_avg']

            # Z-Score归一化
            vol_vs_avg_mean = vol_vs_avg_values.rolling(window=medium_window, min_periods=10).mean()
            vol_vs_avg_std = vol_vs_avg_values.rolling(window=medium_window, min_periods=10).std()
            df_out['volume_vs_avg_zscore'] = (vol_vs_avg_values - vol_vs_avg_mean) / (vol_vs_avg_std + 1e-9)

            # 百分位数归一化
            vol_vs_avg_percentile = vol_vs_avg_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['volume_vs_avg_percentile'] = vol_vs_avg_percentile

        # 3. 成交量动量归一化
        if 'volume_momentum' in df_out.columns:
            vol_momentum_values = df_out['volume_momentum']

            # Z-Score归一化
            vol_momentum_mean = vol_momentum_values.rolling(window=medium_window, min_periods=10).mean()
            vol_momentum_std = vol_momentum_values.rolling(window=medium_window, min_periods=10).std()
            df_out['volume_momentum_zscore'] = (vol_momentum_values - vol_momentum_mean) / (vol_momentum_std + 1e-9)

            # 百分位数归一化
            vol_momentum_percentile = vol_momentum_values.rolling(window=medium_window, min_periods=10).rank(pct=True)
            df_out['volume_momentum_percentile'] = vol_momentum_percentile

        # 4. 🎯 新增：综合成交量强度指标
        volume_indicators = ['volume_ratio', 'volume_vs_avg', 'volume_momentum']
        available_vol_indicators = [col for col in volume_indicators if col in df_out.columns]

        if len(available_vol_indicators) >= 2:
            # 计算成交量指标的平均Z-Score
            vol_zscore_cols = [f'{col}_zscore' for col in available_vol_indicators if f'{col}_zscore' in df_out.columns]
            if vol_zscore_cols:
                df_out['volume_composite_zscore'] = df_out[vol_zscore_cols].mean(axis=1)

                # 成交量强度分级
                df_out['volume_strength_level'] = np.where(
                    df_out['volume_composite_zscore'] > 1.5, 3,  # 极强
                    np.where(df_out['volume_composite_zscore'] > 0.5, 2,  # 强
                            np.where(df_out['volume_composite_zscore'] > -0.5, 1, 0))  # 中等 vs 弱
                )

        logger.debug("_normalize_volume_indicators: (%s) 成交量指标归一化完成", interval_str)

    except Exception as e:
        logger.error("_normalize_volume_indicators: (%s) 成交量指标归一化失败: %s", interval_str, e)


# --- 🚀 长期数据归一化系统 (Long-term Data Normalization) ---

def calculate_long_term_feature_statistics(df_long_term, feature_columns=None):
    """
    基于长期数据计算特征统计量，用于时间一致性归一化

    Args:
        df_long_term: 长期历史数据DataFrame
        feature_columns: 需要计算统计量的特征列名列表，如果为None则自动检测

    Returns:
        dict: 特征统计量字典 {feature_name: {'mean': float, 'std': float, 'percentiles': dict}}
    """
    try:
        logger.info("calculate_long_term_feature_statistics: 开始计算长期特征统计量")

        if df_long_term is None or df_long_term.empty:
            logger.warning("calculate_long_term_feature_statistics: 长期数据为空，返回空统计量")
            return {}

        # 自动检测需要归一化的特征
        if feature_columns is None:
            feature_columns = _detect_normalizable_features(df_long_term)

        feature_stats = {}

        for feature_col in feature_columns:
            if feature_col not in df_long_term.columns:
                continue

            feature_values = df_long_term[feature_col].dropna()
            if len(feature_values) < 100:  # 需要足够的数据点
                logger.warning(f"calculate_long_term_feature_statistics: {feature_col} 数据点不足({len(feature_values)})，跳过")
                continue

            # 计算统计量
            feature_stats[feature_col] = {
                'mean': float(feature_values.mean()),
                'std': float(feature_values.std()),
                'percentiles': {
                    'p05': float(feature_values.quantile(0.05)),
                    'p25': float(feature_values.quantile(0.25)),
                    'p50': float(feature_values.quantile(0.50)),
                    'p75': float(feature_values.quantile(0.75)),
                    'p95': float(feature_values.quantile(0.95))
                },
                'min': float(feature_values.min()),
                'max': float(feature_values.max()),
                'count': len(feature_values)
            }

        logger.info(f"calculate_long_term_feature_statistics: 完成{len(feature_stats)}个特征的统计量计算")
        return feature_stats

    except Exception as e:
        logger.error(f"calculate_long_term_feature_statistics: 计算失败: {e}")
        return {}


def _detect_normalizable_features(df):
    """自动检测需要归一化的特征"""
    normalizable_features = []

    for col in df.columns:
        col_lower = col.lower()

        # RSI类指标
        if any(keyword in col_lower for keyword in ['rsi', 'stoch', 'willr', 'cci']):
            normalizable_features.append(col)

        # ATR类指标
        elif any(keyword in col_lower for keyword in ['atr', 'volatility']):
            normalizable_features.append(col)

        # MACD类指标
        elif 'macd' in col_lower:
            normalizable_features.append(col)

        # EMA距离类指标
        elif any(keyword in col_lower for keyword in ['ema_distance', 'bb_', 'kc_']):
            normalizable_features.append(col)

        # 成交量比率类指标
        elif any(keyword in col_lower for keyword in ['volume_ratio', 'vol_']):
            normalizable_features.append(col)

    return normalizable_features


def apply_long_term_normalization(df_short_term, long_term_stats, normalization_method='zscore'):
    """
    使用长期统计量对短期数据进行归一化

    Args:
        df_short_term: 短期训练数据DataFrame
        long_term_stats: 长期特征统计量字典
        normalization_method: 归一化方法 ('zscore', 'percentile', 'minmax')

    Returns:
        pd.DataFrame: 归一化后的DataFrame
    """
    try:
        logger.info(f"apply_long_term_normalization: 开始应用长期归一化，方法: {normalization_method}")

        df_normalized = df_short_term.copy()
        normalized_count = 0

        for feature_col, stats in long_term_stats.items():
            if feature_col not in df_normalized.columns:
                continue

            feature_values = df_normalized[feature_col]

            if normalization_method == 'zscore':
                # Z-Score归一化：(x - long_term_mean) / long_term_std
                normalized_values = (feature_values - stats['mean']) / (stats['std'] + 1e-9)
                df_normalized[f'{feature_col}_lt_zscore'] = normalized_values

            elif normalization_method == 'percentile':
                # 百分位数归一化：将当前值映射到长期数据的百分位数
                def map_to_percentile(x):
                    if pd.isna(x):
                        return 0.5
                    percentiles = stats['percentiles']
                    if x <= percentiles['p05']:
                        return 0.05
                    elif x <= percentiles['p25']:
                        return 0.05 + 0.20 * (x - percentiles['p05']) / (percentiles['p25'] - percentiles['p05'])
                    elif x <= percentiles['p50']:
                        return 0.25 + 0.25 * (x - percentiles['p25']) / (percentiles['p50'] - percentiles['p25'])
                    elif x <= percentiles['p75']:
                        return 0.50 + 0.25 * (x - percentiles['p50']) / (percentiles['p75'] - percentiles['p50'])
                    elif x <= percentiles['p95']:
                        return 0.75 + 0.20 * (x - percentiles['p75']) / (percentiles['p95'] - percentiles['p75'])
                    else:
                        return 0.95

                df_normalized[f'{feature_col}_lt_percentile'] = feature_values.apply(map_to_percentile)

            elif normalization_method == 'minmax':
                # MinMax归一化：(x - long_term_min) / (long_term_max - long_term_min)
                value_range = stats['max'] - stats['min']
                if value_range > 1e-9:
                    normalized_values = (feature_values - stats['min']) / value_range
                    df_normalized[f'{feature_col}_lt_minmax'] = normalized_values.clip(0, 1)

            normalized_count += 1

        logger.info(f"apply_long_term_normalization: 完成{normalized_count}个特征的长期归一化")
        return df_normalized

    except Exception as e:
        logger.error(f"apply_long_term_normalization: 归一化失败: {e}")
        return df_short_term


def apply_layered_data_feature_engineering(df_short_term, df_long_term, target_config):
    """
    应用分层数据策略的特征工程

    Args:
        df_short_term: 短期训练数据DataFrame
        df_long_term: 长期历史数据DataFrame（用于统计量计算）
        target_config: 目标配置

    Returns:
        pd.DataFrame: 增强后的特征DataFrame
    """
    try:
        logger.info("apply_layered_data_feature_engineering: 开始应用分层数据特征工程")

        # 检查是否启用分层数据策略
        enable_layered = getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True)
        if not enable_layered or df_long_term is None or df_long_term.empty:
            logger.info("apply_layered_data_feature_engineering: 分层数据策略未启用或长期数据为空，跳过")
            return df_short_term

        # 1. 计算长期特征统计量
        logger.info("apply_layered_data_feature_engineering: 计算长期特征统计量...")
        long_term_stats = calculate_long_term_feature_statistics(df_long_term)

        if not long_term_stats:
            logger.warning("apply_layered_data_feature_engineering: 长期统计量计算失败，跳过归一化")
            return df_short_term

        # 2. 应用长期归一化
        logger.info("apply_layered_data_feature_engineering: 应用长期归一化...")
        df_enhanced = df_short_term.copy()

        # 应用多种归一化方法
        normalization_methods = target_config.get('long_term_normalization_methods', ['zscore', 'percentile'])

        for method in normalization_methods:
            df_enhanced = apply_long_term_normalization(df_enhanced, long_term_stats, method)

        # 3. 添加宏观特征质量指标
        logger.info("apply_layered_data_feature_engineering: 添加宏观特征质量指标...")
        df_enhanced = _add_macro_feature_quality_indicators(df_enhanced, long_term_stats)

        logger.info(f"apply_layered_data_feature_engineering: 完成，新增特征数: {df_enhanced.shape[1] - df_short_term.shape[1]}")
        return df_enhanced

    except Exception as e:
        logger.error(f"apply_layered_data_feature_engineering: 处理失败: {e}")
        return df_short_term


def _add_macro_feature_quality_indicators(df, long_term_stats):
    """添加基于长期统计量的宏观特征质量指标"""
    try:
        # 为关键特征添加质量指标
        key_features = ['RSI_14', 'ATRr_14', 'MACD']

        for feature in key_features:
            if feature in df.columns and feature in long_term_stats:
                stats = long_term_stats[feature]
                feature_values = df[feature]

                # 特征稳定性指标（相对于长期均值的偏离程度）
                stability_score = 1.0 - np.abs(feature_values - stats['mean']) / (stats['std'] + 1e-9)
                df[f'{feature}_stability_score'] = stability_score.clip(0, 1)

                # 特征极值指标（是否处于历史极值区域）
                extreme_threshold = 2.0  # 2个标准差
                is_extreme = np.abs(feature_values - stats['mean']) > (extreme_threshold * stats['std'])
                df[f'{feature}_is_extreme'] = is_extreme.astype(int)

        return df

    except Exception as e:
        logger.error(f"_add_macro_feature_quality_indicators: 处理失败: {e}")
        return df


def _add_time_features(df_out, cfg, interval_str):
    """添加时间特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if _get_cfg('enable_time', default_value=False):
            try:
                df_out['hour'] = df_out.index.hour
                df_out['day_of_week'] = df_out.index.dayofweek
                df_out['is_weekend'] = (df_out.index.dayofweek >= 5).astype(int)

                # 添加三角函数编码的时间特征
                if _get_cfg('enable_time_trigonometric', default_value=True):
                    # 小时的周期性编码 (24小时周期)
                    df_out['hour_sin'] = np.sin(2 * np.pi * df_out['hour'] / 24)
                    df_out['hour_cos'] = np.cos(2 * np.pi * df_out['hour'] / 24)

                    # 星期的周期性编码 (7天周期)
                    df_out['day_sin'] = np.sin(2 * np.pi * df_out['day_of_week'] / 7)
                    df_out['day_cos'] = np.cos(2 * np.pi * df_out['day_of_week'] / 7)
                else:
                    # 如果不启用三角函数编码，设置默认值
                    df_out['hour_sin'] = 0.0
                    df_out['hour_cos'] = 1.0
                    df_out['day_sin'] = 0.0
                    df_out['day_cos'] = 1.0

            except (AttributeError, ValueError, TypeError) as e_time:
                logger.warning("_add_time_features: (%s) 计算时间特征时出错: %s", interval_str, e_time)
                df_out['hour'] = 0
                df_out['day_of_week'] = 0
                df_out['is_weekend'] = 0
                df_out['hour_sin'] = 0.0
                df_out['hour_cos'] = 1.0
                df_out['day_sin'] = 0.0
                df_out['day_cos'] = 1.0
        else:
            df_out['hour'] = 0
            df_out['day_of_week'] = 0
            df_out['is_weekend'] = 0
            df_out['hour_sin'] = 0.0
            df_out['hour_cos'] = 1.0
            df_out['day_sin'] = 0.0
            df_out['day_cos'] = 1.0
    except (ValueError, TypeError) as e_time_sec:
        logger.error("_add_time_features: (%s) 时间特征部分出错: %s", interval_str, e_time_sec)
    except Exception as e_time_sec_unexpected:
        logger.error("_add_time_features: (%s) 时间特征部分发生意外错误: %s", interval_str, e_time_sec_unexpected)

def _add_fund_flow_features(df_out, cfg, interval_str):
    """添加资金流向特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 获取资金流平滑周期配置（无论是否启用fund_flow都需要）
        smoothing_period = _get_cfg('fund_flow_ratio_smoothing_period', default_value=5)

        if _get_cfg('enable_fund_flow', default_value=False):

            # 这里可以添加资金流向相关的特征计算
            # 目前设置为默认值，但保留了配置参数的使用
            df_out['fund_flow_indicator'] = 0.0

            # 示例：如果有taker_buy_ratio数据，可以进行平滑处理
            if 'tbbav' in df_out.columns and 'qav' in df_out.columns:
                try:
                    # 计算主动买入比例
                    taker_buy_ratio = df_out['tbbav'] / df_out['qav'].replace(0, 1e-9)
                    df_out['taker_buy_ratio'] = taker_buy_ratio.fillna(0.5).clip(0, 1)

                    # 应用平滑处理
                    if len(df_out) >= smoothing_period:
                        smoothed_ratio = df_out['taker_buy_ratio'].rolling(
                            window=smoothing_period,
                            min_periods=max(1, smoothing_period // 2)
                        ).mean()
                        df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = safe_fill_nans(smoothed_ratio, default_value=0.5)
                    else:
                        df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = df_out['taker_buy_ratio']

                except (ValueError, TypeError, ZeroDivisionError) as e_taker_ratio:
                    logger.warning("_add_fund_flow_features: (%s) 计算主动买入比例时出错: %s", interval_str, e_taker_ratio)
                    df_out['taker_buy_ratio'] = 0.5
                    df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = 0.5
            else:
                df_out['taker_buy_ratio'] = 0.5
                df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = 0.5
        else:
            df_out['fund_flow_indicator'] = 0.0
            df_out['taker_buy_ratio'] = 0.5
            # 使用配置中的平滑周期，而不是硬编码
            df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = 0.5
    except (ValueError, TypeError) as e_fund_sec:
        logger.error("_add_fund_flow_features: (%s) 资金流向特征部分出错: %s", interval_str, e_fund_sec)
    except Exception as e_fund_sec_unexpected:
        logger.error("_add_fund_flow_features: (%s) 资金流向特征部分发生意外错误: %s", interval_str, e_fund_sec_unexpected)

def _add_trend_features(df_out, cfg, C, H, L, interval_str):
    """添加趋势斜率特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if _get_cfg('enable_trend_slope', default_value=False):
            # 获取趋势斜率周期参数
            trend_slope_period_1 = _get_cfg('trend_slope_period_1', default_value=8)
            trend_slope_period_2 = _get_cfg('trend_slope_period_2', default_value=17)

            # 计算两个趋势斜率特征
            for period_name, period_value in [('1', trend_slope_period_1), ('2', trend_slope_period_2)]:
                if isinstance(period_value, int) and period_value > 0 and len(C) >= period_value:
                    try:
                        # 计算趋势斜率
                        slope = C.rolling(window=period_value).apply(
                            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == period_value else 0,
                            raw=False
                        )
                        df_out[f'trend_slope_period_{period_name}'] = slope.fillna(0)
                    except (ValueError, TypeError) as e_trend_inner:
                        logger.warning("_add_trend_features: (%s) 计算trend_slope_period_%s时出错: %s",
                                     interval_str, period_name, e_trend_inner)
                        df_out[f'trend_slope_period_{period_name}'] = 0.0
                else:
                    df_out[f'trend_slope_period_{period_name}'] = 0.0

            # 添加ADX趋势特征
            if _get_cfg('enable_adx_trend_features', default_value=True):
                try:
                    trend_adx_period = _get_cfg('trend_adx_period', default_value=19)
                    trend_adx_threshold = _get_cfg('trend_adx_threshold', default_value=29)

                    # 计算ADX相关特征（使用pandas_ta）
                    if len(H) >= trend_adx_period and len(L) >= trend_adx_period and len(C) >= trend_adx_period:
                        try:
                            import pandas_ta as pta
                            adx_df = pta.adx(H, L, C, length=trend_adx_period)
                            if adx_df is not None and not adx_df.empty:
                                # 提取ADX相关列
                                adx_cols = [col for col in adx_df.columns if 'ADX' in col.upper()]
                                pdi_cols = [col for col in adx_df.columns if 'DMP' in col.upper() or 'PDI' in col.upper()]
                                mdi_cols = [col for col in adx_df.columns if 'DMN' in col.upper() or 'MDI' in col.upper()]

                                # 设置ADX值
                                if adx_cols:
                                    df_out['adx_value'] = adx_df[adx_cols[0]].fillna(0)
                                else:
                                    df_out['adx_value'] = 0.0

                                # 设置PDI值
                                if pdi_cols:
                                    df_out['adx_pdi'] = adx_df[pdi_cols[0]].fillna(0)
                                else:
                                    df_out['adx_pdi'] = 0.0

                                # 设置MDI值
                                if mdi_cols:
                                    df_out['adx_mdi'] = adx_df[mdi_cols[0]].fillna(0)
                                else:
                                    df_out['adx_mdi'] = 0.0

                                # 计算ADX信号（基于阈值）
                                df_out['trend_adx_signal'] = (df_out['adx_value'] > trend_adx_threshold).astype(int)
                            else:
                                # ADX计算失败，设置默认值
                                df_out['trend_adx_signal'] = 0.0
                                df_out['adx_value'] = 0.0
                                df_out['adx_pdi'] = 0.0
                                df_out['adx_mdi'] = 0.0
                        except ImportError:
                            logger.warning("_add_trend_features: (%s) pandas_ta未安装，无法计算ADX", interval_str)
                            df_out['trend_adx_signal'] = 0.0
                            df_out['adx_value'] = 0.0
                            df_out['adx_pdi'] = 0.0
                            df_out['adx_mdi'] = 0.0
                    else:
                        # 数据不足，设置默认值
                        df_out['trend_adx_signal'] = 0.0
                        df_out['adx_value'] = 0.0
                        df_out['adx_pdi'] = 0.0
                        df_out['adx_mdi'] = 0.0
                except Exception as e_adx:
                    logger.warning("_add_trend_features: (%s) 计算ADX趋势特征时出错: %s", interval_str, e_adx)
                    df_out['trend_adx_signal'] = 0.0
                    df_out['adx_value'] = 0.0
                    df_out['adx_pdi'] = 0.0
                    df_out['adx_mdi'] = 0.0
            else:
                df_out['trend_adx_signal'] = 0.0
                df_out['adx_value'] = 0.0
                df_out['adx_pdi'] = 0.0
                df_out['adx_mdi'] = 0.0

            # 添加EMA交叉趋势特征
            if _get_cfg('enable_ema_trend_features', default_value=True):
                try:
                    trend_ema_short_period = _get_cfg('trend_ema_short_period', default_value=21)
                    trend_ema_long_period = _get_cfg('trend_ema_long_period', default_value=53)

                    # 计算EMA交叉信号
                    if len(C) >= max(trend_ema_short_period, trend_ema_long_period):
                        try:
                            import pandas_ta as pta
                            ema_short = pta.ema(C, length=trend_ema_short_period)
                            ema_long = pta.ema(C, length=trend_ema_long_period)

                            if ema_short is not None and ema_long is not None:
                                # EMA交叉信号：短期EMA > 长期EMA为1，否则为0
                                df_out['trend_ema_signal'] = (ema_short > ema_long).astype(int).fillna(0)
                                # 保存EMA值用于调试
                                df_out['ema_short'] = ema_short.fillna(0)
                                df_out['ema_long'] = ema_long.fillna(0)
                            else:
                                df_out['trend_ema_signal'] = 0.0
                                df_out['ema_short'] = 0.0
                                df_out['ema_long'] = 0.0
                        except ImportError:
                            logger.warning("_add_trend_features: (%s) pandas_ta未安装，无法计算EMA", interval_str)
                            df_out['trend_ema_signal'] = 0.0
                            df_out['ema_short'] = 0.0
                            df_out['ema_long'] = 0.0
                    else:
                        df_out['trend_ema_signal'] = 0.0
                        df_out['ema_short'] = 0.0
                        df_out['ema_long'] = 0.0
                except Exception as e_ema:
                    logger.warning("_add_trend_features: (%s) 计算EMA趋势特征时出错: %s", interval_str, e_ema)
                    df_out['trend_ema_signal'] = 0.0
                    df_out['ema_short'] = 0.0
                    df_out['ema_long'] = 0.0
            else:
                df_out['trend_ema_signal'] = 0.0
                df_out['ema_short'] = 0.0
                df_out['ema_long'] = 0.0

            # 🚀 新增：宏观市场状态特征 (Macro Market State Features)
            if _get_cfg('enable_macro_trend_features', default_value=True):
                try:
                    # 长期EMA配置（宏观趋势过滤器）
                    macro_ema_period = _get_cfg('macro_ema_period', default_value=200)

                    if len(C) >= macro_ema_period:
                        try:
                            import pandas_ta as pta
                            # 计算长期EMA（宏观趋势基准）
                            ema_macro = pta.ema(C, length=macro_ema_period)

                            if ema_macro is not None:
                                df_out['ema_macro'] = ema_macro.fillna(C.iloc[0] if len(C) > 0 else 0)

                                # 🎯 核心特征：价格相对于长期EMA的位置
                                price_vs_macro_ema = ((C - ema_macro) / ema_macro.replace(0, 1e-9)) * 100
                                df_out['price_vs_long_term_ema'] = price_vs_macro_ema.fillna(0)

                                # 🎯 宏观趋势方向（长期EMA的斜率）
                                macro_ema_slope = ema_macro.diff(periods=5).rolling(window=10).mean()
                                df_out['macro_trend_direction'] = np.where(
                                    macro_ema_slope > 0, 1,  # 上升趋势
                                    np.where(macro_ema_slope < 0, -1, 0)  # 下降趋势 vs 横盘
                                )

                                # 🎯 宏观趋势强度（价格偏离程度）
                                price_deviation_abs = np.abs(price_vs_macro_ema)
                                df_out['macro_trend_strength'] = np.where(
                                    price_deviation_abs > 10, 3,  # 强偏离
                                    np.where(price_deviation_abs > 5, 2,  # 中等偏离
                                            np.where(price_deviation_abs > 2, 1, 0))  # 轻微偏离 vs 贴近
                                )

                                # 🎯 宏观支撑/阻力信号
                                df_out['macro_support_signal'] = (
                                    (C > ema_macro) & (df_out['macro_trend_direction'] == 1)
                                ).astype(int)
                                df_out['macro_resistance_signal'] = (
                                    (C < ema_macro) & (df_out['macro_trend_direction'] == -1)
                                ).astype(int)

                                logger.debug("_add_trend_features: (%s) 宏观市场状态特征计算完成", interval_str)
                            else:
                                _set_macro_defaults(df_out)
                        except ImportError:
                            logger.warning("_add_trend_features: (%s) pandas_ta未安装，无法计算宏观EMA", interval_str)
                            _set_macro_defaults(df_out)
                    else:
                        logger.debug("_add_trend_features: (%s) 数据不足以计算宏观EMA(%d)，设置默认值", interval_str, macro_ema_period)
                        _set_macro_defaults(df_out)
                except Exception as e_macro:
                    logger.warning("_add_trend_features: (%s) 计算宏观市场状态特征时出错: %s", interval_str, e_macro)
                    _set_macro_defaults(df_out)
            else:
                _set_macro_defaults(df_out)
        else:
            # 设置默认趋势特征
            df_out['trend_slope_period_1'] = 0.0
            df_out['trend_slope_period_2'] = 0.0
            df_out['trend_adx_signal'] = 0.0
            df_out['trend_ema_signal'] = 0.0
            df_out['adx_value'] = 0.0
            df_out['adx_pdi'] = 0.0
            df_out['adx_mdi'] = 0.0
            df_out['ema_short'] = 0.0
            df_out['ema_long'] = 0.0
            # 设置宏观特征默认值
            _set_macro_defaults(df_out)
    except (ValueError, TypeError) as e_trend_sec:
        logger.error("_add_trend_features: (%s) 趋势特征部分出错: %s", interval_str, e_trend_sec)
    except Exception as e_trend_sec_unexpected:
        logger.error("_add_trend_features: (%s) 趋势特征部分发生意外错误: %s", interval_str, e_trend_sec_unexpected)


def _set_macro_defaults(df_out):
    """设置宏观市场状态特征的默认值"""
    df_out['ema_macro'] = 0.0
    df_out['price_vs_long_term_ema'] = 0.0
    df_out['macro_trend_direction'] = 0
    df_out['macro_trend_strength'] = 0
    df_out['macro_support_signal'] = 0
    df_out['macro_resistance_signal'] = 0

def _add_interaction_features(df_out, cfg, C, H, L, O, V_feat, interval_str):
    """
    添加交互特征（特征组合的力量）- 优化版本

    增强功能：
    1. 改进数学稳健性和异常值处理
    2. 添加市场微观结构交互特征
    3. 引入多层次特征组合
    4. 优化计算效率和内存使用
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    def _safe_divide(numerator, denominator, default_value=0.0, min_denominator=1e-10):
        """安全除法，避免除零和异常值"""
        try:
            # 处理分母为零或极小值的情况
            safe_denominator = np.where(np.abs(denominator) < min_denominator, min_denominator, denominator)
            result = numerator / safe_denominator
            # 处理无穷大和NaN
            result = np.where(np.isfinite(result), result, default_value)
            # 异常值截断（99.5%分位数）
            if len(result) > 10:  # 只有足够数据时才进行分位数截断
                upper_bound = np.percentile(result[np.isfinite(result)], 99.5)
                lower_bound = np.percentile(result[np.isfinite(result)], 0.5)
                result = np.clip(result, lower_bound, upper_bound)
            return result
        except Exception:
            return np.full_like(numerator, default_value)

    def _safe_multiply(a, b, default_value=0.0):
        """安全乘法，处理异常值"""
        try:
            result = a * b
            result = np.where(np.isfinite(result), result, default_value)
            return result
        except Exception:
            return np.full_like(a, default_value)

    try:
        if _get_cfg('enable_interaction_features', default_value=True):
            logger.debug("_add_interaction_features: (%s) 开始计算优化版交互特征", interval_str)

            # 1. 增强版量价结合特征：成交量 × 价格变动
            try:
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]
                if price_change_cols and 'volume' in df_out.columns:
                    price_change_col = price_change_cols[0]
                    # 使用安全乘法
                    df_out['volume_x_price_change'] = _safe_multiply(
                        df_out['volume'], df_out[price_change_col]
                    )

                    # 🎯 新增：标准化量价特征（相对于历史波动）
                    volume_std = df_out['volume'].rolling(window=20, min_periods=5).std()
                    price_change_std = df_out[price_change_col].rolling(window=20, min_periods=5).std()
                    df_out['volume_x_price_change_normalized'] = _safe_divide(
                        df_out['volume_x_price_change'],
                        volume_std * price_change_std,
                        default_value=0.0
                    )

                    logger.debug("_add_interaction_features: (%s) 成功计算增强版量价结合特征", interval_str)
                else:
                    df_out['volume_x_price_change'] = 0.0
                    df_out['volume_x_price_change_normalized'] = 0.0
                    logger.debug("_add_interaction_features: (%s) 价格变化或成交量列不存在，使用默认值", interval_str)
            except Exception as e_volume_price:
                logger.warning("_add_interaction_features: (%s) 计算量价结合特征时出错: %s", interval_str, e_volume_price)
                df_out['volume_x_price_change'] = 0.0
                df_out['volume_x_price_change_normalized'] = 0.0

            # 2. 增强版波动率与趋势结合特征：ATR × ADX
            try:
                # 查找ATR和ADX列
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                adx_cols = [col for col in df_out.columns if 'adx_value' in col]

                if atr_cols and adx_cols:
                    atr_col = atr_cols[0]  # 使用第一个找到的ATR列
                    adx_col = adx_cols[0]  # 使用第一个找到的ADX列

                    # 基础ATR×ADX特征
                    df_out['atr_x_adx'] = _safe_multiply(df_out[atr_col], df_out[adx_col])

                    # 🎯 新增：相对波动趋势强度（ATR相对于历史均值 × ADX）
                    atr_ma = df_out[atr_col].rolling(window=20, min_periods=5).mean()
                    atr_relative = _safe_divide(df_out[atr_col], atr_ma, default_value=1.0)
                    df_out['atr_relative_x_adx'] = _safe_multiply(atr_relative, df_out[adx_col])

                    # 🎯 新增：波动率趋势一致性指标
                    atr_trend = df_out[atr_col].diff().rolling(window=5).mean()  # ATR趋势
                    adx_trend = df_out[adx_col].diff().rolling(window=5).mean()  # ADX趋势
                    # 当ATR和ADX同向变化时，市场状态更明确
                    df_out['atr_adx_trend_consistency'] = np.where(
                        atr_trend * adx_trend > 0, 1.0, -1.0
                    )

                    logger.debug("_add_interaction_features: (%s) 成功计算增强版ATR×ADX特征", interval_str)
                else:
                    df_out['atr_x_adx'] = 0.0
                    df_out['atr_relative_x_adx'] = 0.0
                    df_out['atr_adx_trend_consistency'] = 0.0
                    logger.debug("_add_interaction_features: (%s) ATR或ADX列不存在，使用默认值", interval_str)
            except Exception as e_atr_adx:
                logger.warning("_add_interaction_features: (%s) 计算ATR×ADX特征时出错: %s", interval_str, e_atr_adx)
                df_out['atr_x_adx'] = 0.0
                df_out['atr_relative_x_adx'] = 0.0
                df_out['atr_adx_trend_consistency'] = 0.0

            # 3. 增强版K线实体与波动率结合特征：实体大小 / ATR
            try:
                # 查找实体大小和ATR列
                body_size_cols = [col for col in df_out.columns if 'body_size' in col]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]

                if body_size_cols and atr_cols:
                    body_size_col = body_size_cols[0]
                    atr_col = atr_cols[0]

                    # 使用安全除法
                    df_out['body_over_atr'] = _safe_divide(
                        df_out[body_size_col], df_out[atr_col], default_value=0.0
                    )

                    # 🎯 新增：实体强度相对排名（过去20期的百分位数）
                    df_out['body_over_atr_percentile'] = (
                        df_out['body_over_atr'].rolling(window=20, min_periods=5)
                        .rank(pct=True)
                    )

                    # 🎯 新增：实体与影线的相对强度
                    upper_shadow_cols = [col for col in df_out.columns if 'upper_shadow' in col]
                    lower_shadow_cols = [col for col in df_out.columns if 'lower_shadow' in col]

                    if upper_shadow_cols and lower_shadow_cols:
                        upper_shadow_col = upper_shadow_cols[0]
                        lower_shadow_col = lower_shadow_cols[0]
                        total_shadow = df_out[upper_shadow_col] + df_out[lower_shadow_col]
                        df_out['body_vs_shadow_strength'] = _safe_divide(
                            df_out[body_size_col], total_shadow, default_value=1.0
                        )
                    else:
                        df_out['body_vs_shadow_strength'] = 1.0

                    logger.debug("_add_interaction_features: (%s) 成功计算增强版实体/ATR特征", interval_str)
                else:
                    df_out['body_over_atr'] = 0.0
                    df_out['body_over_atr_percentile'] = 0.5
                    df_out['body_vs_shadow_strength'] = 1.0
                    logger.debug("_add_interaction_features: (%s) 实体大小或ATR列不存在，使用默认值", interval_str)
            except Exception as e_body_atr:
                logger.warning("_add_interaction_features: (%s) 计算实体/ATR特征时出错: %s", interval_str, e_body_atr)
                df_out['body_over_atr'] = 0.0
                df_out['body_over_atr_percentile'] = 0.5
                df_out['body_vs_shadow_strength'] = 1.0

            # 4. 额外的交互特征：RSI × 成交量比率
            try:
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                volume_ratio_cols = [col for col in df_out.columns if 'volume_vs_avg' in col]

                if rsi_cols and volume_ratio_cols:
                    rsi_col = rsi_cols[0]
                    volume_ratio_col = volume_ratio_cols[0]
                    df_out['rsi_x_volume_ratio'] = df_out[rsi_col] * df_out[volume_ratio_col]
                    logger.debug("_add_interaction_features: (%s) 成功计算RSI×成交量比率特征", interval_str)
                else:
                    df_out['rsi_x_volume_ratio'] = 0.0
            except Exception as e_rsi_volume:
                logger.warning("_add_interaction_features: (%s) 计算RSI×成交量比率特征时出错: %s", interval_str, e_rsi_volume)
                df_out['rsi_x_volume_ratio'] = 0.0

            # 5. MACD × 成交量特征
            try:
                macd_cols = [col for col in df_out.columns if col == 'MACD']
                if macd_cols and 'volume' in df_out.columns:
                    df_out['macd_x_volume'] = df_out['MACD'] * df_out['volume']
                    logger.debug("_add_interaction_features: (%s) 成功计算MACD×成交量特征", interval_str)
                else:
                    df_out['macd_x_volume'] = 0.0
            except Exception as e_macd_volume:
                logger.warning("_add_interaction_features: (%s) 计算MACD×成交量特征时出错: %s", interval_str, e_macd_volume)
                df_out['macd_x_volume'] = 0.0

            # 🎯 6. 新增：高级交互特征 - 挖掘更深的"阿尔法"
            try:
                # 6.1 RSI × 价格动量交互
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]
                if rsi_cols and price_change_cols:
                    rsi_col = rsi_cols[0]
                    price_change_col = price_change_cols[0]
                    df_out['rsi_x_momentum'] = df_out[rsi_col] * df_out[price_change_col]
                    logger.debug("_add_interaction_features: (%s) 成功计算RSI×价格动量特征", interval_str)
                else:
                    df_out['rsi_x_momentum'] = 0.0
            except Exception as e_rsi_momentum:
                logger.warning("_add_interaction_features: (%s) 计算RSI×价格动量特征时出错: %s", interval_str, e_rsi_momentum)
                df_out['rsi_x_momentum'] = 0.0

            try:
                # 6.2 布林带宽度 × 成交量异常
                bb_upper_cols = [col for col in df_out.columns if 'bb_upper' in col.lower()]
                bb_lower_cols = [col for col in df_out.columns if 'bb_lower' in col.lower()]
                if bb_upper_cols and bb_lower_cols and 'volume' in df_out.columns:
                    bb_width = df_out[bb_upper_cols[0]] - df_out[bb_lower_cols[0]]
                    volume_ma = df_out['volume'].rolling(window=20, min_periods=1).mean()
                    volume_ratio = df_out['volume'] / volume_ma
                    df_out['bb_width_x_volume_anomaly'] = bb_width * volume_ratio
                    logger.debug("_add_interaction_features: (%s) 成功计算布林带宽度×成交量异常特征", interval_str)
                else:
                    df_out['bb_width_x_volume_anomaly'] = 0.0
            except Exception as e_bb_volume:
                logger.warning("_add_interaction_features: (%s) 计算布林带宽度×成交量异常特征时出错: %s", interval_str, e_bb_volume)
                df_out['bb_width_x_volume_anomaly'] = 0.0

            try:
                # 6.3 威廉指标 × ATR（超买超卖与波动率结合）
                willr_cols = [col for col in df_out.columns if 'willr' in col.lower()]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                if willr_cols and atr_cols:
                    df_out['willr_x_atr'] = df_out[willr_cols[0]] * df_out[atr_cols[0]]
                    logger.debug("_add_interaction_features: (%s) 成功计算威廉指标×ATR特征", interval_str)
                else:
                    df_out['willr_x_atr'] = 0.0
            except Exception as e_willr_atr:
                logger.warning("_add_interaction_features: (%s) 计算威廉指标×ATR特征时出错: %s", interval_str, e_willr_atr)
                df_out['willr_x_atr'] = 0.0

            try:
                # 6.4 MACD信号线交叉强度 × 成交量确认
                macd_cols = [col for col in df_out.columns if col == 'MACD']
                macd_signal_cols = [col for col in df_out.columns if 'macd_signal' in col.lower()]
                if macd_cols and macd_signal_cols and 'volume' in df_out.columns:
                    macd_diff = df_out[macd_cols[0]] - df_out[macd_signal_cols[0]]
                    volume_ma = df_out['volume'].rolling(window=10, min_periods=1).mean()
                    volume_strength = df_out['volume'] / volume_ma
                    df_out['macd_cross_x_volume_confirm'] = macd_diff * volume_strength
                    logger.debug("_add_interaction_features: (%s) 成功计算MACD交叉×成交量确认特征", interval_str)
                else:
                    df_out['macd_cross_x_volume_confirm'] = 0.0
            except Exception as e_macd_cross:
                logger.warning("_add_interaction_features: (%s) 计算MACD交叉×成交量确认特征时出错: %s", interval_str, e_macd_cross)
                df_out['macd_cross_x_volume_confirm'] = 0.0

            try:
                # 6.5 价格位置 × 成交量分布（价格在区间内的位置与成交量的关系）
                if all(col in df_out.columns for col in ['high', 'low', 'close', 'volume']):
                    # 计算价格在高低点区间内的相对位置
                    high_low_range = df_out['high'] - df_out['low']
                    price_position = (df_out['close'] - df_out['low']) / (high_low_range + 1e-8)  # 避免除零

                    # 计算成交量相对强度
                    volume_ma = df_out['volume'].rolling(window=14, min_periods=1).mean()
                    volume_relative = df_out['volume'] / (volume_ma + 1e-8)

                    df_out['price_position_x_volume_strength'] = price_position * volume_relative
                    logger.debug("_add_interaction_features: (%s) 成功计算价格位置×成交量强度特征", interval_str)
                else:
                    df_out['price_position_x_volume_strength'] = 0.0
            except Exception as e_price_position:
                logger.warning("_add_interaction_features: (%s) 计算价格位置×成交量强度特征时出错: %s", interval_str, e_price_position)
                df_out['price_position_x_volume_strength'] = 0.0

            # 🚀 7. 全新市场微观结构交互特征 - 挖掘深层阿尔法
            try:
                # 7.1 买卖压力不平衡 × 价格位置
                if all(col in df_out.columns for col in ['high', 'low', 'close', 'volume']):
                    # 计算买卖压力代理指标
                    price_range = df_out['high'] - df_out['low']
                    close_position = _safe_divide(
                        df_out['close'] - df_out['low'], price_range, default_value=0.5
                    )

                    # 成交量分布不平衡（基于价格位置推断）
                    volume_imbalance = np.where(close_position > 0.5, 1, -1) * df_out['volume']
                    df_out['volume_imbalance_x_price_pos'] = _safe_multiply(
                        volume_imbalance, close_position
                    )

                    # 7.2 订单流强度 × 波动率
                    volume_velocity = df_out['volume'].diff().rolling(window=3).mean()
                    atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                    if atr_cols:
                        df_out['order_flow_x_volatility'] = _safe_multiply(
                            volume_velocity, df_out[atr_cols[0]]
                        )
                    else:
                        df_out['order_flow_x_volatility'] = 0.0

                    logger.debug("_add_interaction_features: (%s) 成功计算市场微观结构特征", interval_str)
                else:
                    df_out['volume_imbalance_x_price_pos'] = 0.0
                    df_out['order_flow_x_volatility'] = 0.0
            except Exception as e_microstructure:
                logger.warning("_add_interaction_features: (%s) 计算市场微观结构特征时出错: %s", interval_str, e_microstructure)
                df_out['volume_imbalance_x_price_pos'] = 0.0
                df_out['order_flow_x_volatility'] = 0.0

            # 🎯 8. 多层次特征组合 - 三元交互特征
            try:
                # 8.1 RSI × 成交量 × 波动率三元组合
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]

                if rsi_cols and atr_cols and 'volume' in df_out.columns:
                    rsi_normalized = (df_out[rsi_cols[0]] - 50) / 50  # 标准化到[-1,1]
                    volume_ma = df_out['volume'].rolling(window=20, min_periods=5).mean()
                    volume_normalized = _safe_divide(df_out['volume'], volume_ma, default_value=1.0)
                    atr_ma = df_out[atr_cols[0]].rolling(window=20, min_periods=5).mean()
                    atr_normalized = _safe_divide(df_out[atr_cols[0]], atr_ma, default_value=1.0)

                    df_out['rsi_volume_atr_combo'] = (
                        rsi_normalized * volume_normalized * atr_normalized
                    )

                    # 8.2 趋势一致性指标（价格趋势 × RSI趋势 × 成交量趋势）
                    price_trend = df_out['close'].diff().rolling(window=5).mean()
                    rsi_trend = df_out[rsi_cols[0]].diff().rolling(window=5).mean()
                    volume_trend = df_out['volume'].diff().rolling(window=5).mean()

                    # 计算趋势一致性（同向为正，反向为负）
                    trend_consistency = np.sign(price_trend) * np.sign(rsi_trend) * np.sign(volume_trend)
                    df_out['trend_consistency_score'] = trend_consistency

                    logger.debug("_add_interaction_features: (%s) 成功计算多层次特征组合", interval_str)
                else:
                    df_out['rsi_volume_atr_combo'] = 0.0
                    df_out['trend_consistency_score'] = 0.0
            except Exception as e_multilevel:
                logger.warning("_add_interaction_features: (%s) 计算多层次特征组合时出错: %s", interval_str, e_multilevel)
                df_out['rsi_volume_atr_combo'] = 0.0
                df_out['trend_consistency_score'] = 0.0

            # 🎯 新增：用户建议的高阶复合交互特征
            try:
                logger.debug("_add_interaction_features: (%s) 开始计算用户建议的复合交互特征", interval_str)

                # 1. 动量 × 波动率：rsi_velocity * atr_percent
                # 表征"在当前波动水平下，动量的强度如何？"
                if 'rsi_velocity' in df_out.columns and 'market_atr_percent' in df_out.columns:
                    df_out['momentum_x_volatility'] = _safe_multiply(
                        df_out['rsi_velocity'],
                        df_out['market_atr_percent']
                    )
                    logger.debug("_add_interaction_features: (%s) 成功计算动量×波动率特征", interval_str)
                else:
                    df_out['momentum_x_volatility'] = 0.0
                    logger.debug("_add_interaction_features: (%s) rsi_velocity或market_atr_percent不存在，设置默认值", interval_str)

                # 🎯 用户建议2：速度 × 成交量 - 动量的加速是否有成交量支持？
                if 'rsi_velocity' in df_out.columns and 'volume_vs_avg' in df_out.columns:
                    df_out['interaction_rsi_velocity_x_volume'] = _safe_multiply(
                        df_out['rsi_velocity'],
                        df_out['volume_vs_avg']
                    )
                    logger.debug("_add_interaction_features: (%s) 🎯 用户建议2：成功计算RSI速度×成交量特征", interval_str)
                else:
                    df_out['interaction_rsi_velocity_x_volume'] = 0.0
                    logger.debug("_add_interaction_features: (%s) 🎯 用户建议2：rsi_velocity或volume_vs_avg不存在，设置默认值", interval_str)

                # 🎯 用户建议2：速度 × 市场确定性 - 动量信号是否发生在清晰的市场环境中？
                if 'rsi_velocity' in df_out.columns and 'market_certainty_composite' in df_out.columns:
                    df_out['interaction_rsi_velocity_x_certainty'] = _safe_multiply(
                        df_out['rsi_velocity'],
                        df_out['market_certainty_composite']
                    )
                    logger.debug("_add_interaction_features: (%s) 🎯 用户建议2：成功计算RSI速度×市场确定性特征", interval_str)
                else:
                    df_out['interaction_rsi_velocity_x_certainty'] = 0.0
                    logger.debug("_add_interaction_features: (%s) 🎯 用户建议2：rsi_velocity或market_certainty_composite不存在，设置默认值", interval_str)

                # 🎯 最终优化建议1：校准核心Alpha - volume_imbalance × 市场确定性
                # 让模型学会在使用volume_imbalance特征时"看市场环境的脸色"
                if 'volume_imbalance_x_price_pos_1h' in df_out.columns and 'market_certainty_composite' in df_out.columns:
                    df_out['interaction_vol_imbalance_x_certainty'] = _safe_multiply(
                        df_out['volume_imbalance_x_price_pos_1h'],
                        df_out['market_certainty_composite']
                    )
                    logger.debug("_add_interaction_features: (%s) 🎯 最终优化：成功添加核心Alpha与市场确定性的交互特征", interval_str)
                    print("    ✅ 已添加核心Alpha与市场确定性的交互特征")
                else:
                    df_out['interaction_vol_imbalance_x_certainty'] = 0.0
                    logger.debug("_add_interaction_features: (%s) 🎯 最终优化：volume_imbalance_x_price_pos_1h或market_certainty_composite不存在，设置默认值", interval_str)

                # 🎯 DOWN模型镜像核心Alpha：收盘于最低点附近的大量卖盘
                # 核心逻辑: volume_imbalance是负数(卖盘), close_pos_in_candle接近0(收于最低点)
                # (1 - close_pos_in_candle) 会让收于低点的K线得到高分
                if 'close_pos_in_candle' in df_out.columns and 'volume_imbalance' in df_out.columns:
                    df_out['volume_imbalance_downside_signal'] = (
                        df_out['volume_imbalance'].abs() * (1 - df_out['close_pos_in_candle'])
                    )
                    logger.debug("_add_interaction_features: (%s) 🎯 DOWN模型：成功添加镜像版核心下跌Alpha特征", interval_str)
                    print("    ✅ 已添加镜像版的核心下跌Alpha特征")
                else:
                    df_out['volume_imbalance_downside_signal'] = 0.0
                    logger.debug("_add_interaction_features: (%s) 🎯 DOWN模型：close_pos_in_candle或volume_imbalance不存在，设置默认值", interval_str)

                # 2. 趋势强度 × 价格位置：adx_value * price_pos_in_kc
                # 当趋势强劲时（ADX高），价格在Keltner通道中的位置就尤为关键
                if 'adx_value' in df_out.columns and 'price_pos_in_kc' in df_out.columns:
                    df_out['trend_strength_x_price_position'] = _safe_multiply(
                        df_out['adx_value'],
                        df_out['price_pos_in_kc']
                    )
                    logger.debug("_add_interaction_features: (%s) 成功计算趋势强度×价格位置特征", interval_str)
                else:
                    df_out['trend_strength_x_price_position'] = 0.0
                    logger.debug("_add_interaction_features: (%s) adx_value或price_pos_in_kc不存在，设置默认值", interval_str)

                # 3. 成交量确认的趋势：volume_vs_avg * trend_slope
                # 成交量是否支持当前的短期趋势斜率
                volume_trend_features = []
                if 'volume_vs_avg' in df_out.columns:
                    # 检查可用的趋势斜率特征
                    trend_slope_cols = [col for col in df_out.columns if 'trend_slope_period_' in col]
                    if trend_slope_cols:
                        # 使用第一个可用的趋势斜率特征
                        trend_slope_col = trend_slope_cols[0]
                        df_out['volume_confirmed_trend'] = _safe_multiply(
                            df_out['volume_vs_avg'],
                            df_out[trend_slope_col]
                        )
                        volume_trend_features.append(f'volume_vs_avg × {trend_slope_col}')
                        logger.debug("_add_interaction_features: (%s) 成功计算成交量确认趋势特征: %s",
                                   interval_str, volume_trend_features[0])
                    else:
                        df_out['volume_confirmed_trend'] = 0.0
                        logger.debug("_add_interaction_features: (%s) 未找到趋势斜率特征，设置默认值", interval_str)
                else:
                    df_out['volume_confirmed_trend'] = 0.0
                    logger.debug("_add_interaction_features: (%s) volume_vs_avg不存在，设置默认值", interval_str)

                # 🎯 额外增强：组合所有三个复合特征的综合信号
                # 当动量、趋势强度和成交量确认都同时发生时，这是一个强信号
                df_out['comprehensive_signal_strength'] = (
                    np.abs(df_out['momentum_x_volatility']) * 0.4 +
                    df_out['trend_strength_x_price_position'] * 0.3 +
                    np.abs(df_out['volume_confirmed_trend']) * 0.3
                )

                logger.debug("_add_interaction_features: (%s) 用户建议的复合交互特征计算完成", interval_str)

            except Exception as e_user_features:
                logger.warning("_add_interaction_features: (%s) 计算用户建议的复合特征时出错: %s", interval_str, e_user_features)
                # 设置默认值
                df_out['momentum_x_volatility'] = 0.0
                df_out['trend_strength_x_price_position'] = 0.0
                df_out['volume_confirmed_trend'] = 0.0
                df_out['comprehensive_signal_strength'] = 0.0

            # 🎯 根本性优化：添加时间特征与市场状态的交互项，防止模型过度依赖时间特征
            try:
                logger.debug("_add_interaction_features: (%s) 开始计算时间特征上下文感知交互项", interval_str)

                # 1. hour_cos × 波动率稳定性：不同时间点在不同波动环境下的效应
                if 'hour_cos' in df_out.columns and 'volatility_stability_score' in df_out.columns:
                    df_out['hour_cos_x_vol_stability'] = df_out['hour_cos'] * df_out['volatility_stability_score']
                    logger.debug("_add_interaction_features: (%s) 成功计算hour_cos×波动率稳定性特征", interval_str)
                else:
                    df_out['hour_cos_x_vol_stability'] = 0.0
                    logger.debug("_add_interaction_features: (%s) hour_cos或volatility_stability_score不存在，设置默认值", interval_str)

                # 2. hour_cos × ATR百分比：时间效应在不同波动水平下的表现
                if 'hour_cos' in df_out.columns and 'market_atr_percent' in df_out.columns:
                    df_out['hour_cos_x_atr_pct'] = df_out['hour_cos'] * df_out['market_atr_percent']
                    logger.debug("_add_interaction_features: (%s) 成功计算hour_cos×ATR百分比特征", interval_str)
                else:
                    df_out['hour_cos_x_atr_pct'] = 0.0
                    logger.debug("_add_interaction_features: (%s) hour_cos或market_atr_percent不存在，设置默认值", interval_str)

                # 3. hour_sin × 趋势强度：时间周期性与趋势状态的结合
                if 'hour_sin' in df_out.columns and 'adx_value' in df_out.columns:
                    df_out['hour_sin_x_trend_strength'] = df_out['hour_sin'] * df_out['adx_value']
                    logger.debug("_add_interaction_features: (%s) 成功计算hour_sin×趋势强度特征", interval_str)
                else:
                    df_out['hour_sin_x_trend_strength'] = 0.0
                    logger.debug("_add_interaction_features: (%s) hour_sin或adx_value不存在，设置默认值", interval_str)

                logger.debug("_add_interaction_features: (%s) 时间特征上下文感知交互项计算完成", interval_str)

            except Exception as e_time_features:
                logger.warning("_add_interaction_features: (%s) 计算时间特征交互项时出错: %s", interval_str, e_time_features)
                # 设置默认值
                df_out['hour_cos_x_vol_stability'] = 0.0
                df_out['hour_cos_x_atr_pct'] = 0.0
                df_out['hour_sin_x_trend_strength'] = 0.0

            logger.debug("_add_interaction_features: (%s) 优化版交互特征计算完成（包含微观结构特征、用户建议特征和时间上下文特征）", interval_str)
        else:
            # 如果禁用交互特征，设置所有默认值
            df_out['volume_x_price_change'] = 0.0
            df_out['volume_x_price_change_normalized'] = 0.0
            df_out['atr_x_adx'] = 0.0
            df_out['atr_relative_x_adx'] = 0.0
            df_out['atr_adx_trend_consistency'] = 0.0
            df_out['body_over_atr'] = 0.0
            df_out['body_over_atr_percentile'] = 0.5
            df_out['body_vs_shadow_strength'] = 1.0
            df_out['rsi_x_volume_ratio'] = 0.0
            df_out['macd_x_volume'] = 0.0
            # 原有高级交互特征的默认值
            df_out['rsi_x_momentum'] = 0.0
            df_out['bb_width_x_volume_anomaly'] = 0.0
            df_out['willr_x_atr'] = 0.0
            df_out['macd_cross_x_volume_confirm'] = 0.0
            df_out['price_position_x_volume_strength'] = 0.0
            # 新增微观结构特征的默认值
            df_out['volume_imbalance_x_price_pos'] = 0.0
            df_out['order_flow_x_volatility'] = 0.0
            # 用户建议的复合特征默认值
            df_out['momentum_x_volatility'] = 0.0
            df_out['trend_strength_x_price_position'] = 0.0
            df_out['volume_confirmed_trend'] = 0.0
            df_out['comprehensive_signal_strength'] = 0.0
            df_out['rsi_volume_atr_combo'] = 0.0
            df_out['trend_consistency_score'] = 0.0
            # 时间特征交互项的默认值
            df_out['hour_cos_x_vol_stability'] = 0.0
            df_out['hour_cos_x_atr_pct'] = 0.0
            df_out['hour_sin_x_trend_strength'] = 0.0
            # 🎯 用户建议2：新增信号确认特征的默认值
            df_out['interaction_rsi_velocity_x_volume'] = 0.0
            df_out['interaction_rsi_velocity_x_certainty'] = 0.0
            # 🎯 最终优化建议1：核心Alpha校准特征的默认值
            df_out['interaction_vol_imbalance_x_certainty'] = 0.0
            # 🎯 DOWN模型镜像核心Alpha特征的默认值
            df_out['volume_imbalance_downside_signal'] = 0.0

    except (ValueError, TypeError) as e_interaction_sec:
        logger.error("_add_interaction_features: (%s) 交互特征部分出错: %s", interval_str, e_interaction_sec)
    except Exception as e_interaction_sec_unexpected:
        logger.error("_add_interaction_features: (%s) 交互特征部分发生意外错误: %s", interval_str, e_interaction_sec_unexpected)

def _add_higher_order_features(df_out, cfg, interval_str):
    """
    添加高阶特征（从"速度"到"加速度"）- 优化版本

    增强功能：
    1. 数学稳定性优化（平滑化处理）
    2. 自适应窗口大小
    3. 异常值检测和处理
    4. 新增复合高阶特征
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    def _smooth_derivative(series, window=3, method='ema'):
        """计算平滑化的导数"""
        try:
            if method == 'ema':
                # 使用EMA平滑
                alpha = 2.0 / (window + 1)
                return series.diff().ewm(alpha=alpha, adjust=False).mean()
            else:
                # 使用简单移动平均平滑
                return series.diff().rolling(window=window, min_periods=1).mean()
        except Exception:
            return series.diff().fillna(0)

    def _adaptive_window(series, base_window=20, volatility_factor=0.5):
        """根据数据波动性自适应调整窗口大小"""
        try:
            # 计算滚动标准差作为波动性指标
            volatility = series.rolling(window=base_window, min_periods=5).std()
            volatility_normalized = volatility / volatility.mean()

            # 高波动时使用更小的窗口，低波动时使用更大的窗口
            adaptive_window = np.clip(
                base_window * (1 - volatility_factor * (volatility_normalized - 1)),
                base_window * 0.5,  # 最小窗口
                base_window * 1.5   # 最大窗口
            ).astype(int)

            return adaptive_window
        except Exception:
            return np.full(len(series), base_window)

    try:
        if _get_cfg('enable_higher_order_features', default_value=True):
            logger.debug("_add_higher_order_features: (%s) 开始计算优化版高阶特征", interval_str)

            # 1. 增强版RSI的变化率（平滑化一阶导数）
            try:
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                if rsi_cols:
                    rsi_col = rsi_cols[0]  # 使用第一个找到的RSI列

                    # 基础RSI变化率
                    df_out['rsi_change_1p'] = _smooth_derivative(df_out[rsi_col], window=3)

                    # 🎯 新增：RSI动量强度（变化率的绝对值）
                    df_out['rsi_momentum_strength'] = np.abs(df_out['rsi_change_1p'])

                    # 🎯 新增：RSI变化方向一致性（连续同向变化的程度）
                    rsi_direction = np.sign(df_out['rsi_change_1p'])
                    rsi_consistency = rsi_direction.rolling(window=5).apply(
                        lambda x: np.abs(x.sum()) / len(x), raw=True
                    )
                    df_out['rsi_direction_consistency'] = rsi_consistency.fillna(0)

                    # 🎯 新增：RSI超买超卖动量（在极值区域的变化率）
                    rsi_extreme_momentum = np.where(
                        (df_out[rsi_col] > 70) | (df_out[rsi_col] < 30),
                        df_out['rsi_change_1p'],
                        0
                    )
                    df_out['rsi_extreme_momentum'] = rsi_extreme_momentum

                    logger.debug("_add_higher_order_features: (%s) 成功计算增强版RSI变化率特征", interval_str)
                else:
                    df_out['rsi_change_1p'] = 0.0
                    df_out['rsi_momentum_strength'] = 0.0
                    df_out['rsi_direction_consistency'] = 0.0
                    df_out['rsi_extreme_momentum'] = 0.0
                    logger.debug("_add_higher_order_features: (%s) RSI列不存在，使用默认值", interval_str)
            except Exception as e_rsi_change:
                logger.warning("_add_higher_order_features: (%s) 计算RSI变化率时出错: %s", interval_str, e_rsi_change)
                df_out['rsi_change_1p'] = 0.0
                df_out['rsi_momentum_strength'] = 0.0
                df_out['rsi_direction_consistency'] = 0.0
                df_out['rsi_extreme_momentum'] = 0.0

            # 2. 增强版MACD柱状图的加速度（平滑化二阶导数）
            try:
                macd_hist_cols = [col for col in df_out.columns if 'MACD' in col and ('histogram' in col.lower() or 'hist' in col.lower())]
                if not macd_hist_cols:
                    # 如果没有找到histogram列，查找MACDh列
                    macd_hist_cols = [col for col in df_out.columns if col == 'MACDh']

                if macd_hist_cols:
                    macd_hist_col = macd_hist_cols[0]

                    # 计算平滑化的一阶导数（速度）
                    macd_hist_velocity = _smooth_derivative(df_out[macd_hist_col], window=3)
                    # 计算平滑化的二阶导数（加速度）
                    df_out['macd_hist_accel'] = _smooth_derivative(macd_hist_velocity, window=3)

                    # 🎯 新增：MACD柱状图动量转折点检测
                    # 当加速度从正转负或从负转正时，可能是动量转折点
                    accel_sign_change = (
                        np.sign(df_out['macd_hist_accel']) !=
                        np.sign(df_out['macd_hist_accel'].shift(1))
                    ).astype(int)
                    df_out['macd_momentum_turning_point'] = accel_sign_change

                    # 🎯 新增：MACD柱状图强度指标（绝对值的移动平均）
                    macd_strength = np.abs(df_out[macd_hist_col]).rolling(window=10, min_periods=3).mean()
                    df_out['macd_histogram_strength'] = macd_strength.fillna(0)

                    logger.debug("_add_higher_order_features: (%s) 成功计算增强版MACD柱状图加速度", interval_str)
                else:
                    df_out['macd_hist_accel'] = 0.0
                    df_out['macd_momentum_turning_point'] = 0
                    df_out['macd_histogram_strength'] = 0.0
                    logger.debug("_add_higher_order_features: (%s) MACD柱状图列不存在，使用默认值", interval_str)
            except Exception as e_macd_accel:
                logger.warning("_add_higher_order_features: (%s) 计算MACD柱状图加速度时出错: %s", interval_str, e_macd_accel)
                df_out['macd_hist_accel'] = 0.0
                df_out['macd_momentum_turning_point'] = 0
                df_out['macd_histogram_strength'] = 0.0

            # 3. 价格变化的加速度
            try:
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]
                if price_change_cols:
                    price_change_col = price_change_cols[0]
                    # 计算价格变化的变化率（加速度）
                    df_out['price_change_accel'] = df_out[price_change_col].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算价格变化加速度", interval_str)
                else:
                    df_out['price_change_accel'] = 0.0
            except Exception as e_price_accel:
                logger.warning("_add_higher_order_features: (%s) 计算价格变化加速度时出错: %s", interval_str, e_price_accel)
                df_out['price_change_accel'] = 0.0

            # 4. 成交量变化率
            try:
                if 'volume' in df_out.columns:
                    df_out['volume_change_1p'] = df_out['volume'].pct_change()
                    # 成交量变化的加速度
                    df_out['volume_change_accel'] = df_out['volume_change_1p'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算成交量变化率和加速度", interval_str)
                else:
                    df_out['volume_change_1p'] = 0.0
                    df_out['volume_change_accel'] = 0.0
            except Exception as e_volume_change:
                logger.warning("_add_higher_order_features: (%s) 计算成交量变化率时出错: %s", interval_str, e_volume_change)
                df_out['volume_change_1p'] = 0.0
                df_out['volume_change_accel'] = 0.0

            # 5. ATR的变化率（波动率趋势）
            try:
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                if atr_cols:
                    atr_col = atr_cols[0]
                    df_out['atr_change_1p'] = df_out[atr_col].pct_change()
                    logger.debug("_add_higher_order_features: (%s) 成功计算ATR变化率", interval_str)
                else:
                    df_out['atr_change_1p'] = 0.0
            except Exception as e_atr_change:
                logger.warning("_add_higher_order_features: (%s) 计算ATR变化率时出错: %s", interval_str, e_atr_change)
                df_out['atr_change_1p'] = 0.0

            # 🎯 6. 新增：高级高阶特征 - 捕捉市场动态的"速度"和"加速度"
            try:
                # 6.1 RSI速度（RSI变化率，别名为rsi_velocity）
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                if rsi_cols:
                    rsi_col = rsi_cols[0]
                    df_out['rsi_velocity'] = df_out[rsi_col].diff()  # RSI的一阶导数
                    df_out['rsi_acceleration'] = df_out['rsi_velocity'].diff()  # RSI的二阶导数

                    # 🎯 用户建议4：创建"动量衰竭"特征 - time_since_rsi_velocity_peak
                    # 寻找 rsi_velocity 的波峰
                    is_peak = (
                        (df_out['rsi_velocity'] > df_out['rsi_velocity'].shift(1)) &
                        (df_out['rsi_velocity'] > df_out['rsi_velocity'].shift(-1)) &
                        (df_out['rsi_velocity'] > 0.5)  # 只考虑显著的波峰
                    )

                    # 计算距离上一个波峰的时间
                    peak_indices = df_out.index[is_peak]
                    if len(peak_indices) > 0:
                        # 为每个时间点找到最近的前一个波峰
                        time_since_peak = pd.Series(999.0, index=df_out.index)  # 初始化为大数
                        for i, idx in enumerate(df_out.index):
                            # 找到当前时间点之前的最近波峰
                            previous_peaks = peak_indices[peak_indices < idx]
                            if len(previous_peaks) > 0:
                                latest_peak = previous_peaks[-1]
                                # 计算时间差（以K线数量为单位，可以转换为小时）
                                time_diff = df_out.index.get_loc(idx) - df_out.index.get_loc(latest_peak)
                                time_since_peak.loc[idx] = time_diff

                        df_out['time_since_rsi_velocity_peak'] = time_since_peak
                    else:
                        df_out['time_since_rsi_velocity_peak'] = 999.0  # 没有波峰时设为大数

                    logger.debug("_add_higher_order_features: (%s) 🎯 用户建议4：成功计算RSI速度、加速度和动量衰竭特征", interval_str)
                else:
                    df_out['rsi_velocity'] = 0.0
                    df_out['rsi_acceleration'] = 0.0
                    df_out['time_since_rsi_velocity_peak'] = 999.0
            except Exception as e_rsi_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算RSI速度特征时出错: %s", interval_str, e_rsi_velocity)
                df_out['rsi_velocity'] = 0.0
                df_out['rsi_acceleration'] = 0.0
                df_out['time_since_rsi_velocity_peak'] = 999.0

            try:
                # 6.2 MACD柱状图加速度（动量的动量）
                macd_hist_cols = [col for col in df_out.columns if 'macd_hist' in col.lower()]
                if macd_hist_cols:
                    macd_hist_col = macd_hist_cols[0]
                    df_out['macd_hist_velocity'] = df_out[macd_hist_col].diff()
                    df_out['macd_hist_acceleration'] = df_out['macd_hist_velocity'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算MACD柱状图速度和加速度", interval_str)
                else:
                    df_out['macd_hist_velocity'] = 0.0
                    df_out['macd_hist_acceleration'] = 0.0
            except Exception as e_macd_accel:
                logger.warning("_add_higher_order_features: (%s) 计算MACD柱状图加速度时出错: %s", interval_str, e_macd_accel)
                df_out['macd_hist_velocity'] = 0.0
                df_out['macd_hist_acceleration'] = 0.0

            try:
                # 6.3 布林带位置变化率（价格在布林带中位置的变化速度）
                bb_upper_cols = [col for col in df_out.columns if 'bb_upper' in col.lower()]
                bb_lower_cols = [col for col in df_out.columns if 'bb_lower' in col.lower()]
                if bb_upper_cols and bb_lower_cols and 'close' in df_out.columns:
                    bb_position = (df_out['close'] - df_out[bb_lower_cols[0]]) / (df_out[bb_upper_cols[0]] - df_out[bb_lower_cols[0]] + 1e-8)
                    df_out['bb_position_velocity'] = bb_position.diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算布林带位置变化率", interval_str)
                else:
                    df_out['bb_position_velocity'] = 0.0
            except Exception as e_bb_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算布林带位置变化率时出错: %s", interval_str, e_bb_velocity)
                df_out['bb_position_velocity'] = 0.0

            try:
                # 6.4 ADX变化率（趋势强度的变化）
                adx_cols = [col for col in df_out.columns if 'ADX' in col.upper()]
                if adx_cols:
                    adx_col = adx_cols[0]
                    df_out['adx_velocity'] = df_out[adx_col].diff()
                    df_out['adx_acceleration'] = df_out['adx_velocity'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算ADX速度和加速度", interval_str)
                else:
                    df_out['adx_velocity'] = 0.0
                    df_out['adx_acceleration'] = 0.0
            except Exception as e_adx_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算ADX速度特征时出错: %s", interval_str, e_adx_velocity)
                df_out['adx_velocity'] = 0.0
                df_out['adx_acceleration'] = 0.0

            # 🎯 6.5 新增：ATR变化率和加速度（波动率的动态变化）
            try:
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                if atr_cols:
                    atr_col = atr_cols[0]
                    # ATR的变化率（波动率的速度）
                    df_out['atr_velocity'] = df_out[atr_col].pct_change()
                    # ATR变化的加速度（波动率变化的加速度）
                    df_out['atr_acceleration'] = df_out['atr_velocity'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算ATR速度和加速度", interval_str)
                else:
                    df_out['atr_velocity'] = 0.0
                    df_out['atr_acceleration'] = 0.0
            except Exception as e_atr_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算ATR速度特征时出错: %s", interval_str, e_atr_velocity)
                df_out['atr_velocity'] = 0.0
                df_out['atr_acceleration'] = 0.0

            try:
                # 6.5 成交量动量的动量（成交量变化的加速度）
                if 'volume' in df_out.columns:
                    volume_momentum = df_out['volume'].pct_change()
                    df_out['volume_momentum_acceleration'] = volume_momentum.diff()

                    # 成交量相对强度指数（类似RSI但用于成交量）
                    volume_changes = df_out['volume'].diff()
                    volume_gains = volume_changes.where(volume_changes > 0, 0)
                    volume_losses = (-volume_changes).where(volume_changes < 0, 0)

                    avg_volume_gain = volume_gains.rolling(window=14, min_periods=1).mean()
                    avg_volume_loss = volume_losses.rolling(window=14, min_periods=1).mean()

                    volume_rs = avg_volume_gain / (avg_volume_loss + 1e-8)
                    df_out['volume_rsi'] = 100 - (100 / (1 + volume_rs))

                    logger.debug("_add_higher_order_features: (%s) 成功计算成交量高阶特征", interval_str)
                else:
                    df_out['volume_momentum_acceleration'] = 0.0
                    df_out['volume_rsi'] = 50.0  # 中性值
            except Exception as e_volume_advanced:
                logger.warning("_add_higher_order_features: (%s) 计算成交量高阶特征时出错: %s", interval_str, e_volume_advanced)
                df_out['volume_momentum_acceleration'] = 0.0
                df_out['volume_rsi'] = 50.0

            # 🚀 7. 全新复合高阶特征 - 捕捉市场动态的深层模式
            try:
                # 7.1 多指标协同加速度（RSI、MACD、价格的综合加速度）
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                macd_cols = [col for col in df_out.columns if col == 'MACD']
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]

                if rsi_cols and macd_cols and price_change_cols:
                    # 计算各指标的标准化加速度
                    rsi_accel = _smooth_derivative(df_out[rsi_cols[0]], window=3)
                    macd_accel = _smooth_derivative(df_out[macd_cols[0]], window=3)
                    price_accel = _smooth_derivative(df_out[price_change_cols[0]], window=3)

                    # 标准化到相同尺度
                    rsi_accel_norm = (rsi_accel - rsi_accel.mean()) / (rsi_accel.std() + 1e-8)
                    macd_accel_norm = (macd_accel - macd_accel.mean()) / (macd_accel.std() + 1e-8)
                    price_accel_norm = (price_accel - price_accel.mean()) / (price_accel.std() + 1e-8)

                    # 综合加速度指标
                    df_out['multi_indicator_acceleration'] = (
                        rsi_accel_norm + macd_accel_norm + price_accel_norm
                    ) / 3

                    # 7.2 动量分歧指标（价格动量与技术指标动量的分歧）
                    price_momentum = df_out[price_change_cols[0]].rolling(window=5).mean()
                    rsi_momentum = (df_out[rsi_cols[0]] - 50) / 50  # 标准化RSI动量

                    momentum_divergence = np.abs(
                        np.sign(price_momentum) - np.sign(rsi_momentum)
                    )
                    df_out['momentum_divergence_signal'] = momentum_divergence

                    logger.debug("_add_higher_order_features: (%s) 成功计算复合高阶特征", interval_str)
                else:
                    df_out['multi_indicator_acceleration'] = 0.0
                    df_out['momentum_divergence_signal'] = 0.0
            except Exception as e_composite:
                logger.warning("_add_higher_order_features: (%s) 计算复合高阶特征时出错: %s", interval_str, e_composite)
                df_out['multi_indicator_acceleration'] = 0.0
                df_out['momentum_divergence_signal'] = 0.0

            # 🎯 8. 自适应高阶特征 - 根据市场状态调整计算窗口
            try:
                # 8.1 自适应RSI变化率（根据波动率调整窗口）
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]

                if rsi_cols and atr_cols:
                    # 根据ATR调整RSI变化率的计算窗口
                    atr_percentile = df_out[atr_cols[0]].rolling(window=20, min_periods=5).rank(pct=True)

                    # 高波动时使用短窗口，低波动时使用长窗口
                    adaptive_window = np.where(atr_percentile > 0.7, 3,
                                             np.where(atr_percentile < 0.3, 7, 5))

                    # 计算自适应RSI变化率（简化版本，使用固定窗口近似）
                    df_out['rsi_adaptive_velocity'] = _smooth_derivative(
                        df_out[rsi_cols[0]], window=5
                    )

                    # 8.2 市场状态感知的价格加速度
                    # 在趋势市场和震荡市场中使用不同的加速度计算方法
                    adx_cols = [col for col in df_out.columns if 'adx_value' in col]
                    if adx_cols and price_change_cols:
                        adx_strength = df_out[adx_cols[0]]
                        is_trending = adx_strength > 25  # ADX > 25表示趋势市场

                        # 趋势市场：使用较长窗口平滑
                        # 震荡市场：使用较短窗口捕捉快速变化
                        trend_accel = _smooth_derivative(df_out[price_change_cols[0]], window=7)
                        range_accel = _smooth_derivative(df_out[price_change_cols[0]], window=3)

                        df_out['market_aware_price_accel'] = np.where(
                            is_trending, trend_accel, range_accel
                        )
                    else:
                        df_out['market_aware_price_accel'] = 0.0

                    logger.debug("_add_higher_order_features: (%s) 成功计算自适应高阶特征", interval_str)
                else:
                    df_out['rsi_adaptive_velocity'] = 0.0
                    df_out['market_aware_price_accel'] = 0.0
            except Exception as e_adaptive:
                logger.warning("_add_higher_order_features: (%s) 计算自适应高阶特征时出错: %s", interval_str, e_adaptive)
                df_out['rsi_adaptive_velocity'] = 0.0
                df_out['market_aware_price_accel'] = 0.0

            logger.debug("_add_higher_order_features: (%s) 优化版高阶特征计算完成（包含复合和自适应特征）", interval_str)
        else:
            # 如果禁用高阶特征，设置所有默认值
            df_out['rsi_change_1p'] = 0.0
            df_out['rsi_momentum_strength'] = 0.0
            df_out['rsi_direction_consistency'] = 0.0
            df_out['rsi_extreme_momentum'] = 0.0
            df_out['macd_hist_accel'] = 0.0
            df_out['macd_momentum_turning_point'] = 0
            df_out['macd_histogram_strength'] = 0.0
            df_out['price_change_accel'] = 0.0
            df_out['volume_change_1p'] = 0.0
            df_out['volume_change_accel'] = 0.0
            df_out['atr_change_1p'] = 0.0
            # 原有高级高阶特征的默认值
            df_out['rsi_velocity'] = 0.0
            df_out['rsi_acceleration'] = 0.0
            df_out['time_since_rsi_velocity_peak'] = 999.0  # 🎯 用户建议4：动量衰竭特征默认值
            df_out['macd_hist_velocity'] = 0.0
            df_out['macd_hist_acceleration'] = 0.0
            df_out['bb_position_velocity'] = 0.0
            df_out['adx_velocity'] = 0.0
            df_out['adx_acceleration'] = 0.0
            df_out['volume_momentum_acceleration'] = 0.0
            df_out['volume_rsi'] = 50.0
            # 新增复合和自适应特征的默认值
            df_out['multi_indicator_acceleration'] = 0.0
            df_out['momentum_divergence_signal'] = 0.0
            df_out['rsi_adaptive_velocity'] = 0.0
            df_out['market_aware_price_accel'] = 0.0

    except (ValueError, TypeError) as e_higher_order_sec:
        logger.error("_add_higher_order_features: (%s) 高阶特征部分出错: %s", interval_str, e_higher_order_sec)
    except Exception as e_higher_order_sec_unexpected:
        logger.error("_add_higher_order_features: (%s) 高阶特征部分发生意外错误: %s", interval_str, e_higher_order_sec_unexpected)

def _add_timeframe_sensitivity_features(df_out, cfg, interval_str):
    """添加时间框架敏感度特征 - 对比15分钟与4小时数据的差异"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if not _get_cfg('enable_timeframe_sensitivity', default_value=False):
            return

        reference_tf = _get_cfg('tf_sensitivity_reference_timeframe', default_value='4h')
        sensitivity_features = _get_cfg('tf_sensitivity_features', default_value=['rsi', 'close_pos_in_candle', 'macd', 'volume_ratio'])

        logger.info("_add_timeframe_sensitivity_features: (%s) 开始计算时间框架敏感度特征，参考时间框架: %s",
                   interval_str, reference_tf)

        # 检查是否有MTFA特征可用
        if not isinstance(sensitivity_features, list):
            sensitivity_features = ['rsi', 'close_pos_in_candle', 'macd', 'volume_ratio']

        # 1. RSI对比特征
        if 'rsi' in sensitivity_features:
            try:
                rsi_period = _get_cfg('rsi_period', 22)
                current_rsi_col = f'RSI_{rsi_period}'
                reference_rsi_col = f'RSI_{rsi_period}_{reference_tf}'

                if current_rsi_col in df_out.columns and reference_rsi_col in df_out.columns:
                    rsi_diff = df_out[current_rsi_col] - df_out[reference_rsi_col]
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_diff'] = rsi_diff.fillna(0)

                    # 额外特征：RSI相对强度
                    rsi_ratio = df_out[current_rsi_col] / df_out[reference_rsi_col].replace(0, 1e-9)
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_ratio'] = rsi_ratio.fillna(1.0)

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算RSI对比特征", interval_str)
                else:
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_ratio'] = 1.0
                    logger.debug("_add_timeframe_sensitivity_features: (%s) RSI列不存在，使用默认值", interval_str)
            except Exception as e_rsi:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算RSI对比特征时出错: %s", interval_str, e_rsi)
                df_out[f'rsi_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'rsi_{interval_str}_vs_{reference_tf}_ratio'] = 1.0

        # 2. 收盘价在K线中位置对比特征
        if 'close_pos_in_candle' in sensitivity_features:
            try:
                current_pos_col = 'close_pos_in_candle'
                reference_pos_col = f'close_pos_in_candle_{reference_tf}'

                if current_pos_col in df_out.columns and reference_pos_col in df_out.columns:
                    pos_diff = df_out[current_pos_col] - df_out[reference_pos_col]
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_diff'] = pos_diff.fillna(0)

                    # 额外特征：位置偏离程度（绝对值）
                    pos_deviation = np.abs(pos_diff)
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_deviation'] = pos_deviation.fillna(0)

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算收盘价位置对比特征", interval_str)
                else:
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_deviation'] = 0.0
                    logger.debug("_add_timeframe_sensitivity_features: (%s) 收盘价位置列不存在，使用默认值", interval_str)
            except Exception as e_pos:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算收盘价位置对比特征时出错: %s", interval_str, e_pos)
                df_out[f'close_pos_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'close_pos_{interval_str}_vs_{reference_tf}_deviation'] = 0.0

        # 3. MACD对比特征
        if 'macd' in sensitivity_features:
            try:
                current_macd_col = 'MACD'
                reference_macd_col = f'MACD_{reference_tf}'

                if current_macd_col in df_out.columns and reference_macd_col in df_out.columns:
                    macd_diff = df_out[current_macd_col] - df_out[reference_macd_col]
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_diff'] = macd_diff.fillna(0)

                    # 额外特征：MACD信号一致性
                    current_macd_signal = (df_out[current_macd_col] > 0).astype(int)
                    reference_macd_signal = (df_out[reference_macd_col] > 0).astype(int)
                    macd_signal_agreement = (current_macd_signal == reference_macd_signal).astype(int)
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_signal_agreement'] = macd_signal_agreement

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算MACD对比特征", interval_str)
                else:
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_signal_agreement'] = 1
                    logger.debug("_add_timeframe_sensitivity_features: (%s) MACD列不存在，使用默认值", interval_str)
            except Exception as e_macd:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算MACD对比特征时出错: %s", interval_str, e_macd)
                df_out[f'macd_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'macd_{interval_str}_vs_{reference_tf}_signal_agreement'] = 1

        # 4. 成交量比率特征
        if 'volume_ratio' in sensitivity_features:
            try:
                volume_avg_period = _get_cfg('volume_avg_period', 20)
                current_vol_col = f'volume_vs_avg_{volume_avg_period}p'
                reference_vol_col = f'volume_vs_avg_{volume_avg_period}p_{reference_tf}'

                if current_vol_col in df_out.columns and reference_vol_col in df_out.columns:
                    vol_ratio_diff = df_out[current_vol_col] - df_out[reference_vol_col]
                    df_out[f'volume_ratio_{interval_str}_vs_{reference_tf}_diff'] = vol_ratio_diff.fillna(0)

                    # 额外特征：成交量活跃度对比
                    vol_activity_ratio = df_out[current_vol_col] / df_out[reference_vol_col].replace(0, 1e-9)
                    df_out[f'volume_activity_{interval_str}_vs_{reference_tf}_ratio'] = vol_activity_ratio.fillna(1.0)

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算成交量对比特征", interval_str)
                else:
                    df_out[f'volume_ratio_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'volume_activity_{interval_str}_vs_{reference_tf}_ratio'] = 1.0
                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成交量比率列不存在，使用默认值", interval_str)
            except Exception as e_vol:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算成交量对比特征时出错: %s", interval_str, e_vol)
                df_out[f'volume_ratio_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'volume_activity_{interval_str}_vs_{reference_tf}_ratio'] = 1.0

        logger.info("_add_timeframe_sensitivity_features: (%s) 时间框架敏感度特征计算完成", interval_str)

    except (ValueError, TypeError) as e_tf_sensitivity_sec:
        logger.error("_add_timeframe_sensitivity_features: (%s) 时间框架敏感度特征部分出错: %s", interval_str, e_tf_sensitivity_sec)
    except Exception as e_tf_sensitivity_sec_unexpected:
        logger.error("_add_timeframe_sensitivity_features: (%s) 时间框架敏感度特征部分发生意外错误: %s", interval_str, e_tf_sensitivity_sec_unexpected)

def _get_intelligent_default_value(col_name, series, feature_defaults):
    """
    为特征列智能选择默认值

    Args:
        col_name (str): 列名
        series (pd.Series): 数据序列
        feature_defaults (dict): 预定义的默认值字典

    Returns:
        float: 智能选择的默认值
    """
    # 首先检查是否有预定义的默认值
    if col_name in feature_defaults:
        return feature_defaults[col_name]

    # 根据列名模式智能选择默认值
    col_lower = col_name.lower()

    # 价格变化特征 - 优先处理，避免被价格相关特征捕获
    if any(keyword in col_lower for keyword in ['change', 'pct']) and 'volume' not in col_lower:
        return 0.0  # 变化率默认为0

    # 价格相关特征
    elif any(keyword in col_lower for keyword in ['price', 'close', 'open', 'high', 'low', 'hma']) and 'change' not in col_lower:
        # 🚀 修复数据泄露：使用第一个有效值而不是全局中位数
        non_nan_values = series.dropna()
        if len(non_nan_values) > 0:
            return float(non_nan_values.iloc[0])  # 使用第一个有效值（历史数据）
        return 0.0

    # 成交量相关特征
    elif any(keyword in col_lower for keyword in ['volume', 'vol']):
        if 'ratio' in col_lower or 'vs' in col_lower:
            return 1.0  # 比率默认为1
        return 0.0  # 成交量变化默认为0

    # RSI类指标 (0-100范围)
    elif any(keyword in col_lower for keyword in ['rsi', 'stoch']):
        return 50.0  # 中性值

    # Williams %R (-100到0范围)
    elif 'willr' in col_lower or 'williams' in col_lower:
        return -50.0  # 中性值

    # MACD相关
    elif 'macd' in col_lower:
        return 0.0  # MACD默认为0

    # 位置相关特征 (0-1范围)
    elif any(keyword in col_lower for keyword in ['pos_in', 'position', 'percent_b']):
        return 0.5  # 中间位置

    # 布尔型特征
    elif any(keyword in col_lower for keyword in ['is_', 'above', 'signal', 'cross']):
        return 0  # 布尔型默认为False(0)

    # 时间特征
    elif any(keyword in col_lower for keyword in ['hour', 'day', 'week', 'month']):
        return 0  # 时间特征默认为0

    # 三角函数特征
    elif any(keyword in col_lower for keyword in ['sin', 'cos']):
        if 'cos' in col_lower:
            return 1.0  # cos(0) = 1
        elif 'sin' in col_lower:
            return 0.0  # sin(0) = 0
        return 0.0

    # 趋势斜率
    elif 'slope' in col_lower or 'trend' in col_lower:
        return 0.0  # 无趋势

    # 波动率相关 (ATR等)
    elif any(keyword in col_lower for keyword in ['atr', 'volatility', 'range']):
        # 🚀 修复数据泄露：使用第一个有效值而不是全局中位数
        non_nan_values = series.dropna()
        if len(non_nan_values) > 0:
            return float(non_nan_values.iloc[0])  # 使用第一个有效值（历史数据）
        return 0.0

    # K线形态名称特征
    elif 'pattern' in col_lower and 'name' in col_lower:
        return 'Unknown'  # 字符串类型的默认值

    # 默认情况：尝试使用历史数据
    non_nan_values = series.dropna()
    if len(non_nan_values) > 0:
        # 🚀 修复数据泄露：使用第一个有效值而不是全局中位数
        return float(non_nan_values.iloc[0])  # 使用第一个有效值（历史数据）

    # 最后的兜底值
    return 0.0

def _apply_final_processing(df_out, feature_defaults, interval_str):
    """应用智能的最终NaN/Inf处理和默认值填充"""
    try:
        # 第一步：处理无穷大值并收集统计信息
        numeric_cols = df_out.select_dtypes(include=[np.number]).columns
        inf_stats = {}

        logger.debug("_apply_final_processing: (%s) 开始处理 %d 个数值列", interval_str, len(numeric_cols))

        for col in numeric_cols:
            if col in df_out.columns:
                # 统计无穷大值
                inf_count = np.isinf(df_out[col]).sum()
                if inf_count > 0:
                    inf_stats[col] = inf_count

                # 替换无穷大值为NaN
                df_out[col] = df_out[col].replace([np.inf, -np.inf], np.nan)

        # 记录无穷大值统计
        if inf_stats:
            inf_summary = ", ".join([f"{col}:{count}" for col, count in inf_stats.items()])
            logger.info("_apply_final_processing: (%s) 发现并替换无穷大值: [%s]", interval_str, inf_summary)

        # 第二步：智能填充NaN值
        nan_fill_stats = {}

        for col in numeric_cols:
            if col in df_out.columns:
                nan_count_before = df_out[col].isnull().sum()

                if nan_count_before > 0:
                    # 获取智能默认值
                    intelligent_default = _get_intelligent_default_value(col, df_out[col], feature_defaults)

                    # 🚀 修复数据泄露：严格使用历史数据填充，避免使用未来数据
                    df_out[col] = safe_fill_nans(df_out[col], default_value=intelligent_default, use_historical_only=True)

                    # 检查填充后的NaN数量
                    nan_count_after = df_out[col].isnull().sum()
                    filled_count = nan_count_before - nan_count_after

                    if filled_count > 0:
                        nan_fill_stats[col] = {
                            'filled_count': filled_count,
                            'default_value': intelligent_default,
                            'remaining_nan': nan_count_after
                        }

        # 记录NaN填充统计
        if nan_fill_stats:
            logger.info("_apply_final_processing: (%s) 智能填充NaN值统计:", interval_str)
            for col, stats in nan_fill_stats.items():
                logger.info("  %s: 填充 %d 个值 (默认值: %.4f), 剩余NaN: %d",
                           col, stats['filled_count'], stats['default_value'], stats['remaining_nan'])

        # 第三步：最终兜底处理 - 处理仍然存在的NaN值
        final_nan_stats = {}
        total_remaining_nans = 0

        for col in numeric_cols:
            if col in df_out.columns:
                remaining_nans = df_out[col].isnull().sum()
                if remaining_nans > 0:
                    total_remaining_nans += remaining_nans

                    # 使用更保守的兜底策略
                    fallback_value = _get_intelligent_default_value(col, df_out[col], feature_defaults)
                    df_out[col] = df_out[col].fillna(fallback_value)

                    final_nan_stats[col] = {
                        'count': remaining_nans,
                        'fallback_value': fallback_value
                    }

        # 记录最终兜底填充
        if final_nan_stats:
            logger.warning("_apply_final_processing: (%s) 最终兜底填充 %d 个NaN值:", interval_str, total_remaining_nans)
            for col, stats in final_nan_stats.items():
                logger.warning("  %s: %d 个NaN → %.4f", col, stats['count'], stats['fallback_value'])

        # 第四步：确保所有预期特征列都存在
        missing_features_dict = {}
        for feature_name, default_val in feature_defaults.items():
            if feature_name not in df_out.columns:
                missing_features_dict[feature_name] = default_val

        if missing_features_dict:
            # 批量创建缺失的特征列，避免DataFrame碎片化
            missing_df = pd.DataFrame(
                {col: [default_val] * len(df_out) for col, default_val in missing_features_dict.items()},
                index=df_out.index
            )
            df_out = pd.concat([df_out, missing_df], axis=1)

            missing_features = list(missing_features_dict.keys())
            logger.info("_apply_final_processing: (%s) 创建缺失的特征列: %s", interval_str, missing_features[:5])
            if len(missing_features) > 5:
                logger.debug("_apply_final_processing: (%s) 总共创建了 %d 个缺失特征列", interval_str, len(missing_features))

        # 第五步：最终验证
        final_numeric_cols = df_out.select_dtypes(include=[np.number]).columns
        final_nan_count = df_out[final_numeric_cols].isnull().sum().sum()
        final_inf_count = np.isinf(df_out[final_numeric_cols]).sum().sum()

        if final_nan_count > 0:
            logger.error("_apply_final_processing: (%s) 警告：处理后仍有 %d 个NaN值！", interval_str, final_nan_count)

        if final_inf_count > 0:
            logger.error("_apply_final_processing: (%s) 警告：处理后仍有 %d 个无穷大值！", interval_str, final_inf_count)

        logger.info("_apply_final_processing: (%s) 完成智能最终处理。数值列: %d, 最终NaN: %d, 最终Inf: %d",
                   interval_str, len(final_numeric_cols), final_nan_count, final_inf_count)

        return df_out

    except Exception as e_final:
        logger.error("_apply_final_processing: (%s) 最终处理时出错: %s", interval_str, e_final)
        logger.debug(traceback.format_exc())
        return df_out

def _generate_dynamic_feature_defaults(cfg):
    """
    🚀 使用特征注册表统一管理特征名和默认值

    根据配置参数动态生成特征默认值字典，确保特征名与实际计算的特征名一致

    注意：此函数现在强制使用特征注册表，不再支持legacy方法
    """
    try:
        # 使用新的特征注册表
        from src.core.feature_registry import get_feature_registry
        from src.core.feature_constants import BASE_FEATURE_DEFAULTS

        registry = get_feature_registry()

        # 使用注册表生成默认值
        feature_defaults = registry.get_feature_defaults(cfg)

        # 合并基础默认值（向后兼容）
        feature_defaults.update(BASE_FEATURE_DEFAULTS)

        logger.info(f"🚀 使用特征注册表生成了 {len(feature_defaults)} 个特征默认值")
        return feature_defaults

    except Exception as e:
        logger.error(f"🚨 特征注册表生成默认值失败: {e}")
        # 返回基础默认值作为最后的回退
        from src.core.feature_constants import BASE_FEATURE_DEFAULTS
        logger.warning("使用基础默认值作为回退")
        return BASE_FEATURE_DEFAULTS.copy()

# 🚨 Legacy方法已移除 - 强制使用特征注册表
# _generate_dynamic_feature_defaults_legacy 函数已被移除
# 所有特征默认值现在通过特征注册表统一管理


def _add_features_for_lstm(df, cfg):
    """
    专门为LSTM模型创建特征，聚焦于动态变化和速率。
    """
    # 🚀 重要修复：保留原始OHLCV列，因为后续目标变量创建需要这些列
    df_out = df.copy()  # 先复制原始数据，保留所有原始列

    # --- 核心思想：在原始数据基础上添加"变化率"特征 ---

    # 1. 价格的百分比变化（1-period price change）
    df_out['price_pct_change_1p'] = df['close'].pct_change(periods=1).fillna(0)

    # 2. 成交量的百分比变化
    # 使用log1p处理成交量，避免极端值影响，然后计算变化
    df_out['volume_pct_change_1p'] = np.log1p(df['volume']).pct_change(periods=1).fillna(0)

    # 3. K线实体大小的变化率
    body_size = (df['close'] - df['open']).abs()
    df_out['body_size_pct_change'] = body_size.pct_change().fillna(0)

    # 4. 价格在K线中的位置（0到1之间）
    candle_range = (df['high'] - df['low']).replace(0, 1e-9)
    df_out['close_pos_in_candle'] = ((df['close'] - df['low']) / candle_range).clip(0, 1).fillna(0.5)

    # 5. 短期RSI的变化率（一阶导数）
    rsi = pta.rsi(df['close'], length=14)
    df_out['rsi_14_diff'] = rsi.diff().fillna(0)

    # 6. 短期MACD柱状图的变化率（二阶导数）
    macd = pta.macd(df['close'], fast=12, slow=26, signal=9)
    if macd is not None and not macd.empty:
        macd_hist_col = [col for col in macd.columns if 'MACDH' in col]
        if macd_hist_col:
            df_out['macd_hist_diff'] = macd[macd_hist_col[0]].diff().fillna(0)
        else:
            df_out['macd_hist_diff'] = 0.0
    else:
        df_out['macd_hist_diff'] = 0.0

    # 🚀 为LSTM添加基础ATR特征，以支持动态阈值计算
    try:
        atr = pta.atr(df['high'], df['low'], df['close'], length=14)
        if atr is not None and not atr.empty:
            df_out['ATRr_14'] = atr.fillna(0)
        else:
            # 如果ATR计算失败，使用简单的价格波动率替代
            df_out['ATRr_14'] = df['close'].rolling(window=14).std().fillna(0)
    except Exception as e:
        logger.warning(f"_add_features_for_lstm: ATR计算失败，使用价格标准差替代: {e}")
        df_out['ATRr_14'] = df['close'].rolling(window=14).std().fillna(0)

    # 统计新增的LSTM专属特征
    lstm_features = ['price_pct_change_1p', 'volume_pct_change_1p', 'body_size_pct_change',
                     'close_pos_in_candle', 'rsi_14_diff', 'macd_hist_diff', 'ATRr_14']

    print(f"  LSTM特征创建完成，新增 {len(lstm_features)} 个专属特征: {lstm_features}")
    print(f"  总列数: {len(df_out.columns)} (原始: {len(df.columns)}, 新增: {len(lstm_features)})")

    # 最终处理，确保没有NaN或Inf
    df_out.replace([np.inf, -np.inf], np.nan, inplace=True)
    df_out.fillna(0, inplace=True)

    return df_out


def add_classification_features(df, target_config):
    """
    为分类模型添加特征，采用模块化设计。

    Args:
        df (pd.DataFrame): 包含OHLCV数据的DataFrame
        target_config (dict): 目标配置字典

    Returns:
        pd.DataFrame or None: 包含特征的DataFrame，失败时返回None
    """
    # 🚀 输入验证 - 集成错误处理器
    if ERROR_HANDLER_AVAILABLE:
        error_handler = get_error_handler(target_config)

        # 验证输入类型
        if not isinstance(df, pd.DataFrame):
            logger.error("add_classification_features: df必须是DataFrame类型，而不是 %s", type(df))
            raise TypeError("Error: df必须是DataFrame类型")

        if not isinstance(target_config, dict):
            logger.error("add_classification_features: target_config必须是dict类型，而不是 %s", type(target_config))
            raise TypeError("Config Error in add_classification_features")

        # 验证关键数据
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        is_valid, error_msg = error_handler.validate_critical_data(df, required_columns, min_rows=10)

        if not is_valid:
            logger.error(f"add_classification_features: 数据验证失败: {error_msg}")

            # 尝试创建安全的默认DataFrame
            try:
                safe_df = error_handler.create_safe_default_dataframe(df, required_columns)
                logger.warning("add_classification_features: 使用安全默认DataFrame继续处理")
                df = safe_df
            except Exception as e:
                logger.critical(f"add_classification_features: 无法创建安全默认DataFrame: {e}")
                return None
    else:
        # 传统验证方法
        if not isinstance(df, pd.DataFrame):
            logger.error("add_classification_features: df必须是DataFrame类型，而不是 %s", type(df))
            raise TypeError("Error: df必须是DataFrame类型")

        if not isinstance(target_config, dict):
            logger.error("add_classification_features: target_config必须是dict类型，而不是 %s", type(target_config))
            raise TypeError("Config Error in add_classification_features")

    interval_str = target_config.get('interval', '未知周期')
    target_name = target_config.get('name', 'unknown_target')

    # =================================================================
    # === 修复：为LSTM模型使用统一的特征工程流程 ===
    # =================================================================
    model_type = target_config.get('model_type', 'LGBM')
    if model_type == 'LSTM':
        print("✅ 检测到LSTM模型，正在为其创建专属的精简特征集...")
        # 🎯 修复：使用与预测时一致的add_lstm_features函数
        df_out = add_lstm_features(df, target_config)
        # 直接返回结果，跳过下面为LGBM准备的复杂特征
        return df_out
    # =================================================================
    # === 修复结束 ===================================================
    # =================================================================

    # 调试信息：打印关键配置参数
    logger.info("add_classification_features: (%s) 开始特征计算，目标: %s", interval_str, target_name)
    logger.info("add_classification_features: (%s) 关键配置参数:", interval_str)
    logger.info("  enable_volume: %s", target_config.get('enable_volume', 'NOT_SET'))
    logger.info("  enable_ta: %s", target_config.get('enable_ta', 'NOT_SET'))
    logger.info("  volume_avg_period: %s", target_config.get('volume_avg_period', 'NOT_SET'))
    logger.info("  rsi_period: %s", target_config.get('rsi_period', 'NOT_SET'))
    logger.info("  hma_period: %s", target_config.get('hma_period', 'NOT_SET'))
    logger.info("  数据行数: %d", len(df))

    # 🚀 基础数据准备 - 内存优化
    if MEMORY_OPTIMIZER_AVAILABLE:
        try:
            # 使用内存优化器智能管理DataFrame拷贝
            memory_optimizer = get_memory_optimizer(target_config)

            with memory_optimizer.memory_monitor(f"add_classification_features_{interval_str}", verbose=True):
                # 智能拷贝：根据后续操作类型决定是否需要拷贝
                df_out = memory_optimizer.optimize_dataframe_copy(df, 'modify', verbose=True)

                # 数据类型优化
                if len(df_out) > 1000:  # 只对较大数据集进行优化
                    df_out = memory_optimizer.optimize_dtype(df_out, aggressive=False, verbose=True)

        except Exception as e_memory:
            logger.warning("add_classification_features: (%s) 内存优化器失败，使用传统方法: %s", interval_str, e_memory)
            df_out = df.copy()
    else:
        df_out = df.copy()

    feature_count_start = df_out.shape[1]
    cfg = target_config

    # 动态生成特征默认值，根据配置参数确定正确的特征名
    feature_defaults = _generate_dynamic_feature_defaults(cfg)

    # 🚀 新增：特征名验证（如果启用）
    enable_feature_validation = cfg.get('enable_feature_validation', False)
    if enable_feature_validation:
        logger.debug(f"特征验证已启用 ({interval_str})")
    else:
        logger.debug(f"特征验证已禁用 ({interval_str})")

    # 基础列验证和预处理
    base_cols = ['open', 'high', 'low', 'close', 'volume']
    if cfg.get('enable_fund_flow', False) and 'tbbav' not in base_cols:
        base_cols.append('tbbav')

    # 🚀 增强的数据类型检查和转换
    if DATA_TYPE_VALIDATOR_AVAILABLE:
        try:
            logger.debug("add_classification_features: (%s) 使用增强的数据类型验证器", interval_str)

            # 检查必要的基础列是否存在
            required_cols = [col for col in base_cols if col != 'tbbav' or cfg.get('enable_fund_flow', False)]
            missing_cols = [col for col in required_cols if col not in df_out.columns]

            if missing_cols:
                logger.error("add_classification_features: 为 %s 计算特征缺少基础列: %s", interval_str, missing_cols)
                return None

            # 使用数据类型验证器进行严格的类型检查和转换
            df_out, validation_stats = validate_essential_columns(
                df_out,
                target_config=cfg,
                required_columns=required_cols,
                strict_mode=False,  # 非严格模式，允许优雅降级
                verbose=True
            )

            logger.info("add_classification_features: (%s) 数据类型验证完成 - 处理列数: %d, 转换列数: %d, 错误数: %d",
                       interval_str, validation_stats['columns_processed'],
                       validation_stats['columns_converted'], validation_stats['conversion_errors'])

            # 检查是否还有问题
            cols_to_check = [col for col in required_cols if col in df_out.columns]
            remaining_nan_count = df_out[cols_to_check].isnull().sum().sum()

            if remaining_nan_count > 0:
                logger.warning("add_classification_features: (%s) 验证后仍有 %d 个NaN值", interval_str, remaining_nan_count)
                # 使用传统方法作为后备
                for col in cols_to_check:
                    if df_out[col].isnull().any():
                        default_val = 0.0
                        if col in ['open', 'high', 'low', 'close']:
                            non_nan_vals = df_out[col].dropna()
                            default_val = non_nan_vals.iloc[0] if len(non_nan_vals) > 0 else 0.0
                        df_out[col] = safe_fill_nans(df_out[col], default_val, use_historical_only=True, target_config=cfg)

        except Exception as e_validator:
            logger.warning("add_classification_features: (%s) 数据类型验证器失败，回退到传统方法: %s", interval_str, e_validator)
            # 回退到原有的处理方法
            use_traditional_method = True
        else:
            use_traditional_method = False
    else:
        use_traditional_method = True

    # 传统的数据类型处理方法（作为后备或主要方法）
    if use_traditional_method:
        try:
            # 检查必要的基础列是否存在
            if not all(col in df_out.columns for col in base_cols if col != 'tbbav' or cfg.get('enable_fund_flow', False)):
                missing_cols = [col for col in base_cols if (col != 'tbbav' or cfg.get('enable_fund_flow', False)) and col not in df_out.columns]
                logger.error("add_classification_features: 为 %s 计算特征缺少基础列: %s", interval_str, missing_cols)
                return None

            cols_to_process = [col for col in base_cols if col in df_out.columns]

            # 🚀 改进的数据类型转换 - 更严格的错误处理
            for col in cols_to_process:
                try:
                    # 先检查原始数据类型
                    original_dtype = df_out[col].dtype

                    # 强制转换为数值类型
                    df_out[col] = pd.to_numeric(df_out[col], errors='coerce')

                    # 检查转换后的数据类型
                    new_dtype = df_out[col].dtype

                    logger.debug("add_classification_features: (%s) 列 '%s' 类型转换: %s -> %s",
                               interval_str, col, original_dtype, new_dtype)

                except Exception as e_col:
                    logger.error("add_classification_features: (%s) 转换列 '%s' 失败: %s", interval_str, col, e_col)
                    return None

            # 检查并处理NaN值
            if df_out[cols_to_process].isnull().values.any():
                logger.debug("add_classification_features: 为 %s 填充基础列中的NaN值", interval_str)
                # 使用安全填充方法替代ffill().bfill()
                for col in cols_to_process:
                    # 为不同列设置合适的默认值
                    default_val = 0.0
                    if col in ['open', 'high', 'low', 'close']:
                        # 价格列使用第一个非NaN值
                        non_nan_vals = df_out[col].dropna()
                        default_val = non_nan_vals.iloc[0] if len(non_nan_vals) > 0 else 0.0
                    elif col == 'volume' or col == 'tbbav':
                        default_val = 0.0  # 交易量默认为0

                    df_out[col] = safe_fill_nans(df_out[col], default_val, use_historical_only=True, target_config=cfg)

            # 再次检查是否还有NaN值
            if df_out[cols_to_process].isnull().values.any():
                logger.error("add_classification_features: (%s) 填充后基础列中仍有NaN值！", interval_str)
                return None

        except (ValueError, TypeError) as e_convert_numeric:
            logger.error("add_classification_features: (%s) 转换基础列为数值时出错: %s", interval_str, e_convert_numeric)
            return None
        except Exception as e_unexpected:
            logger.error("add_classification_features: (%s) 处理基础列时发生意外错误: %s", interval_str, e_unexpected)
            return None

    # 提取基础价格和成交量数据
    C = df_out['close']
    H = df_out['high']
    L = df_out['low']
    O = df_out['open']
    V_feat = df_out['volume']

    logger.info("add_classification_features: (%s) 开始模块化特征计算，初始列数: %d", interval_str, feature_count_start)

    # 调用模块化特征计算函数
    try:
        # 1. 价格变化特征
        _add_price_change_features(df_out, cfg, C, interval_str)

        # 2. 成交量特征
        _add_volume_features(df_out, cfg, V_feat, interval_str)

        # 3. K线形态特征
        _add_candle_features(df_out, cfg, C, H, L, O, interval_str)

        # 4. 技术指标特征
        _add_technical_indicators(df_out, cfg, C, H, L, interval_str)

        # 5. 时间特征
        _add_time_features(df_out, cfg, interval_str)

        # 6. 资金流向特征
        _add_fund_flow_features(df_out, cfg, interval_str)

        # 7. 趋势特征
        _add_trend_features(df_out, cfg, C, H, L, interval_str)

        # 8. 交互特征（特征组合）
        _add_interaction_features(df_out, cfg, C, H, L, O, V_feat, interval_str)

        # 9. 高阶特征（导数特征）
        _add_higher_order_features(df_out, cfg, interval_str)

        # 10. 🎯 新增：市场状态自适应特征
        _add_market_state_adaptive_features(df_out, cfg, C, H, L, O, V_feat, interval_str)

        # 11. 🎯 新增：衍生品市场数据特征
        try:
            # 获取binance客户端 - 从应用状态管理器获取
            from .application_state import ApplicationState
            app_state = ApplicationState.get_instance()
            binance_client = app_state.get_binance_client()

            if binance_client:
                symbol = cfg.get('symbol', 'BTCUSDT')
                _add_derivatives_features(df_out, cfg, binance_client, symbol, interval_str)
            else:
                logger.warning("add_classification_features: (%s) Binance客户端未初始化，跳过衍生品特征", interval_str)
        except Exception as e_derivatives:
            logger.error("add_classification_features: (%s) 添加衍生品特征时出错: %s", interval_str, e_derivatives)
            logger.debug(traceback.format_exc())

    except Exception as e_feature_calc:
        logger.error("add_classification_features: (%s) 模块化特征计算时出错: %s", interval_str, e_feature_calc)
        logger.debug(traceback.format_exc())

        # 🚀 错误处理回退策略
        if ERROR_HANDLER_AVAILABLE:
            error_handler = get_error_handler(target_config)

            # 处理特征计算错误
            should_continue, fallback_result = error_handler.handle_module_error(
                'feature_calculation',
                e_feature_calc,
                {
                    'function_name': 'add_classification_features',
                    'interval': interval_str,
                    'target_name': target_name,
                    'input_shape': df.shape,
                    'current_columns': df_out.shape[1] if 'df_out' in locals() else 0
                }
            )

            if not should_continue:
                logger.critical("add_classification_features: (%s) 特征计算失败且无法回退，中断处理", interval_str)
                return None

            # 尝试创建最小特征集
            try:
                logger.warning("add_classification_features: (%s) 使用最小特征集回退策略", interval_str)

                # 确保基本价格变化特征存在
                if 'price_change_1p' not in df_out.columns:
                    df_out['price_change_1p'] = df_out['close'].pct_change().fillna(0)

                # 确保基本技术指标存在
                if 'rsi' not in df_out.columns:
                    df_out['rsi'] = 50.0  # RSI中性值

                if 'volume_ratio' not in df_out.columns:
                    df_out['volume_ratio'] = 1.0  # 成交量比率默认值

                logger.info("add_classification_features: (%s) 最小特征集回退成功", interval_str)

            except Exception as e_fallback:
                logger.critical("add_classification_features: (%s) 最小特征集回退失败: %s", interval_str, e_fallback)
                return None
        else:
            return None

    # 最终处理：NaN/Inf处理和默认值填充
    df_out = _apply_final_processing(df_out, feature_defaults, interval_str)

    # 🚀 新增：高级特征工程
    if cfg.get('enable_advanced_feature_engineering', True):
        try:
            from src.core.advanced_feature_engineering import apply_advanced_feature_engineering

            logger.info(f"add_classification_features ({interval_str}): 开始高级特征工程...")
            df_out = apply_advanced_feature_engineering(df_out, cfg, interval_str)
            logger.info(f"add_classification_features ({interval_str}): 高级特征工程完成，当前特征数: {len(df_out.columns)}")

        except ImportError:
            logger.warning(f"add_classification_features ({interval_str}): 高级特征工程模块不可用")
        except Exception as e:
            logger.error(f"add_classification_features ({interval_str}): 高级特征工程失败: {e}")
            logger.debug(traceback.format_exc())

    # 🚀 新增：特征名验证和一致性检查
    if enable_feature_validation:
        try:
            from src.core.feature_registry import get_feature_registry

            registry = get_feature_registry()
            validation_result = registry.validate_feature_names(df_out.columns.tolist(), cfg)

            if not validation_result['validation_passed']:
                logger.warning(f"特征验证失败 ({interval_str}):")
                if validation_result['missing_features']:
                    logger.warning(f"  缺失特征 ({len(validation_result['missing_features'])}): {validation_result['missing_features'][:5]}")
                if validation_result['unexpected_features']:
                    logger.warning(f"  意外特征 ({len(validation_result['unexpected_features'])}): {validation_result['unexpected_features'][:5]}")

                # 添加断言用于开发阶段验证
                if cfg.get('strict_feature_validation', False):
                    raise ValueError(f"严格特征验证失败: 缺失 {len(validation_result['missing_features'])} 个特征, "
                                   f"发现 {len(validation_result['unexpected_features'])} 个意外特征")
            else:
                logger.debug(f"特征验证通过 ({interval_str}): 注册 {validation_result['total_registered']} 个, "
                           f"发现 {validation_result['total_found']} 个")

        except ImportError:
            logger.debug(f"特征注册表不可用，跳过验证 ({interval_str})")
        except Exception as e:
            logger.error(f"特征验证过程中出错 ({interval_str}): {e}")

    # 🚀 最终内存优化：对完成的特征DataFrame进行内存优化
    if MEMORY_OPTIMIZER_AVAILABLE:
        try:
            memory_optimizer = get_memory_optimizer(target_config)

            # 强制垃圾回收，清理特征计算过程中的临时变量
            memory_optimizer.force_garbage_collection(verbose=False)

            # 对最终的特征DataFrame进行内存优化
            df_out = memory_optimizer.optimize_dtype(df_out, aggressive=True, verbose=False)

            logger.debug("add_classification_features: (%s) 最终内存优化完成", interval_str)
        except Exception as e_final_memory:
            logger.debug("add_classification_features: (%s) 最终内存优化失败: %s", interval_str, e_final_memory)

    # 统计和日志
    feature_count_end = df_out.shape[1]
    new_features_count = feature_count_end - feature_count_start

    logger.info("add_classification_features: (%s) 特征计算完成。新增特征: %d, 总列数: %d",
               interval_str, new_features_count, feature_count_end)
    logger.debug("add_classification_features: (%s) EXIT for target %s, interval %s", target_name, interval_str)

    return df_out



# --- add_mtfa_features_to_df ---
def add_mtfa_features_to_df(primary_df, target_config, client):
    target_name_for_logs = target_config.get('name', 'UnknownTarget') # For clearer logs
    if not isinstance(target_config, dict):
        logger.error("add_mtfa_features_to_df: 配置类型错误，期望dict，得到%s", type(target_config))
        raise TypeError(f"Config Error in add_mtfa_features_to_df: Expected dict, got {type(target_config)}")

    mtfa_timeframes = target_config.get('mtfa_timeframes', [])
    primary_interval = target_config.get('interval', 'unknown')
    enable_mtfa_globally = target_config.get('enable_mtfa', False)

    if not enable_mtfa_globally or not mtfa_timeframes:
        if enable_mtfa_globally and not mtfa_timeframes:
            logger.warning("add_mtfa_features_to_df: MTFA已启用但未配置mtfa_timeframes，跳过MTFA特征添加")
        return primary_df

    # 🚀 性能优化：使用MTFA性能优化器
    use_performance_optimizer = target_config.get('enable_mtfa_performance_optimization', True)

    if use_performance_optimizer:
        try:
            from src.optimization.mtfa_performance_optimizer import MTFAPerformanceOptimizer

            # 配置优化器
            max_workers = target_config.get('mtfa_max_workers', 2)  # 考虑API限制
            enable_parallel = target_config.get('enable_mtfa_parallel', True)

            optimizer = MTFAPerformanceOptimizer(
                max_workers=max_workers,
                enable_parallel=enable_parallel
            )

            # 使用优化器处理MTFA
            result_df = optimizer.optimize_mtfa_processing(primary_df, target_config, client)

            # 记录性能统计
            stats = optimizer.get_performance_stats()
            if target_name_for_logs in stats:
                stat = stats[target_name_for_logs]
                logger.info(f"MTFA性能优化完成 ({target_name_for_logs}): "
                           f"处理 {stat['timeframes_processed']} 个时间框架, "
                           f"添加 {stat['features_added']} 个特征, "
                           f"耗时 {stat['total_time']:.2f}秒, "
                           f"并行: {stat['parallel_enabled']}")

            return result_df

        except ImportError as e:
            logger.warning(f"MTFA性能优化器导入失败，使用传统方法: {e}")
        except Exception as e:
            logger.error(f"MTFA性能优化器执行失败，使用传统方法: {e}")
            logger.debug(traceback.format_exc())

    # 传统方法（保持向后兼容）
    logger.info(f"使用传统MTFA处理方法 ({target_name_for_logs})")
    return _add_mtfa_features_traditional(primary_df, target_config, client)

def _filter_mtfa_columns_legacy(df_features_raw, timeframe, target_name_for_logs):
    """传统的MTFA列过滤方法（保持向后兼容）"""
    base_cols_excl = {'open','high','low','close','volume','qav','n','tbbav','tbqav'}

    # 过滤列：排除基础列、目标列和字符串类型列
    cols_to_keep = []
    for col in df_features_raw.columns:
        if (col not in base_cols_excl and
            not col.startswith('target_') and
            not col.endswith('_name') and  # 排除形态名称列
            col != 'candlestick_pattern_name'):  # 明确排除K线形态名称列

            # 检查列的数据类型，只保留数值类型的列
            if df_features_raw[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']:
                cols_to_keep.append(col)
            else:
                logger.debug("_filter_mtfa_columns_legacy: (%s/%s) 跳过非数值列: %s (类型: %s)",
                           target_name_for_logs, timeframe, col, df_features_raw[col].dtype)

    return cols_to_keep

def _add_mtfa_features_traditional(primary_df, target_config, client):
    """传统的MTFA特征添加方法（保持向后兼容）"""
    target_name_for_logs = target_config.get('name', 'UnknownTarget')
    mtfa_timeframes = target_config.get('mtfa_timeframes', [])
    primary_interval = target_config.get('interval', 'unknown')

    try:
        # 🚀 内存优化的MTFA处理
        if MEMORY_OPTIMIZER_AVAILABLE:
            memory_optimizer = get_memory_optimizer(target_config)

            with memory_optimizer.memory_monitor(f"add_mtfa_features_{target_name_for_logs}", verbose=True):
                # 智能拷贝：MTFA需要修改数据，但可以优化拷贝策略
                df_merged = memory_optimizer.optimize_dataframe_copy(primary_df, 'modify', verbose=False)

                # 检查是否需要分块处理
                if len(df_merged) > 50000:  # 大数据集分块处理
                    logger.info("add_mtfa_features_to_df: (%s) 检测到大数据集 (%d 行)，启用分块处理",
                               target_name_for_logs, len(df_merged))
        else:
            df_merged = primary_df.copy()

        if not isinstance(df_merged.index, pd.DatetimeIndex):
            df_merged.index = pd.to_datetime(df_merged.index)
        if df_merged.index.tz is None:
            df_merged.index = df_merged.index.tz_localize('UTC')
        elif df_merged.index.tz != timezone.utc:
            df_merged.index = df_merged.index.tz_convert('UTC')
    except (ValueError, TypeError, AttributeError) as e_index:
        logger.error("_add_mtfa_features_traditional: 处理DataFrame索引时出错: %s", e_index)
        return primary_df

    try:
        primary_start_time = df_merged.index.min()
        primary_end_time = df_merged.index.max()
        primary_timedelta = interval_to_timedelta(primary_interval)
    except (ValueError, TypeError, AttributeError) as e_time:
        logger.error("_add_mtfa_features_traditional: 无法确定主时间范围: %s", e_time)
        return primary_df

    if not isinstance(mtfa_timeframes, list):
        logger.error("_add_mtfa_features_traditional: 错误: MTFA配置不是列表")
        return primary_df

    symbol_to_fetch = target_config.get('symbol', config.SYMBOL)
    logger.info("_add_mtfa_features_traditional: (%s) 开始处理MTFA特征，时间框架: %s", target_name_for_logs, mtfa_timeframes)
    
    for tf in mtfa_timeframes:
        if not isinstance(tf, str): 
            logger.warning("add_mtfa_features_to_df: 无效MTFA时间框架 '%s', 跳过", tf)
            continue
            
        try:
            tf_timedelta = interval_to_timedelta(tf)
            if tf_timedelta <= primary_timedelta: 
                logger.warning("add_mtfa_features_to_df: (%s/%s) MTFA周期 '%s' (%s) <= 主周期 '%s' (%s), 跳过", 
                              target_name_for_logs, tf, tf, tf_timedelta, primary_interval, primary_timedelta)
                continue

            # Determine effective lookback periods for the current MTFA timeframe
            specific_lookbacks = target_config.get('mtfa_specific_lookbacks', {})
            current_tf_lookback = specific_lookbacks.get(tf)

            if current_tf_lookback is not None and isinstance(current_tf_lookback, int) and current_tf_lookback > 0:
                effective_lookback_periods = current_tf_lookback
                logger.debug(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Using specific lookback of {effective_lookback_periods} periods for MTFA timeframe {tf}.")
            else:
                effective_lookback_periods = target_config.get('mtfa_feature_lookback_periods', 200) # Fallback
                if current_tf_lookback is not None: # Log if a value was present but invalid
                    logger.warning(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Invalid specific lookback value '{current_tf_lookback}' for MTFA timeframe {tf}. Using fallback: {effective_lookback_periods}.")
                else:
                    logger.debug(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Using default/fallback lookback of {effective_lookback_periods} periods for MTFA timeframe {tf}.")

            # Determine the start and end datetimes needed for MTFA data fetching
            mtfa_calc_start_dt = primary_start_time - (effective_lookback_periods * tf_timedelta)
            mtfa_fetch_end_dt = primary_end_time + tf_timedelta 

            # Estimate the number of bars needed for the MTFA timeframe
            total_duration_to_fetch = mtfa_fetch_end_dt - mtfa_calc_start_dt
            if tf_timedelta.total_seconds() > 0:
                estimated_bars_for_duration = int(total_duration_to_fetch / tf_timedelta) + 1 # +1 for safety with division
            else:
                logger.warning(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) MTFA timeframe {tf} results in zero timedelta. Cannot estimate bars accurately.")
                estimated_bars_for_duration = target_config.get('mtfa_min_bars_to_fetch', 300) # Fallback to min_bars_to_fetch
            
            estimated_bars_needed = estimated_bars_for_duration + target_config.get('mtfa_fetch_buffer', 10) # Add a small buffer
            
            # Apply overall minimum and maximum fetch limits
            final_fetch_limit = max(target_config.get('mtfa_min_bars_to_fetch', 50), estimated_bars_needed)
            final_fetch_limit = min(config.DATA_FETCH_LIMIT, final_fetch_limit)

            logger.debug(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Calculated fetch parameters: "
                         f"effective_lookback={effective_lookback_periods}, mtfa_calc_start_dt={mtfa_calc_start_dt}, "
                         f"mtfa_fetch_end_dt={mtfa_fetch_end_dt}, estimated_bars_for_duration={estimated_bars_for_duration}, "
                         f"final_fetch_limit={final_fetch_limit}")
            
            try:
                # 使用时间范围模式获取数据，不传递limit参数以获取完整时间段数据
                df_tf = fetch_binance_history(client, symbol_to_fetch, tf,
                                             start_dt=mtfa_calc_start_dt, end_dt=mtfa_fetch_end_dt)
                                             
                if df_tf is None or df_tf.empty or len(df_tf) < target_config.get('mtfa_min_bars_for_calc', 50):
                    logger.warning("add_mtfa_features_to_df: (%s/%s) 未获取到足够 %s 数据 (获取 %d 条, 至少需 %d 条), 跳过", 
                                  target_name_for_logs, tf, tf, len(df_tf) if df_tf is not None else 0, target_config.get('mtfa_min_bars_for_calc', 50))
                    continue
            except (ValueError, TypeError, ConnectionError) as e_fetch:
                logger.error("add_mtfa_features_to_df: (%s/%s) 获取 %s 数据时出错: %s", target_name_for_logs, tf, tf, e_fetch)
                continue
                
            try:
                # 🚀 内存优化的MTFA配置和特征计算
                if MEMORY_OPTIMIZER_AVAILABLE:
                    # 避免不必要的配置拷贝
                    mtfa_temp_config = target_config.copy()
                    mtfa_temp_config['interval'] = tf
                    mtfa_temp_config['enable_mtfa'] = False
                    mtfa_temp_config['enable_ta'] = True

                    # 智能拷贝：特征计算需要修改数据
                    memory_optimizer = get_memory_optimizer(target_config)
                    df_tf_optimized = memory_optimizer.optimize_dataframe_copy(df_tf, 'modify', verbose=False)
                    df_tf_features_raw = add_classification_features(df_tf_optimized, mtfa_temp_config)
                else:
                    mtfa_temp_config = target_config.copy()
                    mtfa_temp_config['interval'] = tf
                    mtfa_temp_config['enable_mtfa'] = False
                    mtfa_temp_config['enable_ta'] = True
                    df_tf_features_raw = add_classification_features(df_tf.copy(), mtfa_temp_config)
                
                if df_tf_features_raw is None or df_tf_features_raw.empty: 
                    logger.warning("add_mtfa_features_to_df: (%s/%s) 未能计算 %s 特征 (add_classification_features返回空), 跳过", target_name_for_logs, tf, tf)
                    continue
            except (ValueError, TypeError) as e_features:
                logger.error("add_mtfa_features_to_df: (%s/%s) 计算 %s 特征时出错: %s", target_name_for_logs, tf, tf, e_features)
                continue
                
            try:
                # 🚀 优化：使用完善的MTFA列过滤系统
                # 检查是否强制使用新过滤器
                force_new_filter = target_config.get('force_new_mtfa_filter', True)

                if force_new_filter:
                    # 强制使用新过滤器，导入失败时报错
                    try:
                        from src.core.mtfa_column_filter import filter_mtfa_columns, get_default_mtfa_filter_config
                    except ImportError as e:
                        error_msg = (f"MTFA列过滤器导入失败 ({target_name_for_logs}/{tf}): {e}\n"
                                   f"请确保 src.core.mtfa_column_filter 模块可用，或在配置中设置 "
                                   f"'force_new_mtfa_filter': False 来使用传统方法")
                        logger.error(error_msg)
                        raise ImportError(error_msg) from e

                    # 使用新的列过滤器
                    filter_config = get_default_mtfa_filter_config()
                    # 可以从target_config中获取自定义过滤配置
                    if 'mtfa_filter_config' in target_config:
                        filter_config.update(target_config['mtfa_filter_config'])

                    cols_to_keep = filter_mtfa_columns(df_tf_features_raw, tf, filter_config)

                    logger.debug("add_mtfa_features_to_df: (%s/%s) 使用新列过滤器，保留 %d 个特征列",
                               target_name_for_logs, tf, len(cols_to_keep))
                else:
                    # 兼容模式：尝试新过滤器，失败时回退到旧方法
                    try:
                        from src.core.mtfa_column_filter import filter_mtfa_columns, get_default_mtfa_filter_config

                        # 使用新的列过滤器
                        filter_config = get_default_mtfa_filter_config()
                        if 'mtfa_filter_config' in target_config:
                            filter_config.update(target_config['mtfa_filter_config'])

                        cols_to_keep = filter_mtfa_columns(df_tf_features_raw, tf, filter_config)

                        logger.debug("add_mtfa_features_to_df: (%s/%s) 使用新列过滤器，保留 %d 个特征列",
                                   target_name_for_logs, tf, len(cols_to_keep))

                    except ImportError as e:
                        logger.warning("add_mtfa_features_to_df: (%s/%s) 列过滤器不可用，使用传统方法: %s",
                                     target_name_for_logs, tf, e)
                        cols_to_keep = _filter_mtfa_columns_legacy(df_tf_features_raw, tf, target_name_for_logs)
                    except Exception as e:
                        logger.warning("add_mtfa_features_to_df: (%s/%s) 列过滤器执行失败，使用传统方法: %s",
                                     target_name_for_logs, tf, e)
                        cols_to_keep = _filter_mtfa_columns_legacy(df_tf_features_raw, tf, target_name_for_logs)

                if not cols_to_keep:
                    logger.warning("add_mtfa_features_to_df: (%s/%s) 为 %s 未找到可用特征列 (可能因为禁用了TA且无其他特征), 跳过", target_name_for_logs, tf, tf)
                    continue

                # 🚀 内存优化的特征选择和重命名
                if MEMORY_OPTIMIZER_AVAILABLE:
                    # 避免不必要的拷贝，直接选择列
                    df_tf_features = df_tf_features_raw[cols_to_keep]

                    # 使用内存优化器进行原地操作
                    memory_optimizer = get_memory_optimizer(target_config)
                    df_tf_features = memory_optimizer.optimize_inplace_operation(
                        df_tf_features,
                        lambda df: df.rename(columns=lambda x: f"{x}_{tf}"),
                        use_inplace=True
                    )
                else:
                    df_tf_features = df_tf_features_raw[cols_to_keep].copy()
                    df_tf_features.rename(columns=lambda x: f"{x}_{tf}", inplace=True)
                
                if not isinstance(df_tf_features.index, pd.DatetimeIndex): 
                    df_tf_features.index = pd.to_datetime(df_tf_features.index)
                if df_tf_features.index.tz is None: 
                    df_tf_features.index = df_tf_features.index.tz_localize('UTC')
                elif df_tf_features.index.tz != timezone.utc: 
                    df_tf_features.index = df_tf_features.index.tz_convert('UTC')
                    
                # 🚀 严格防止数据泄露：仅使用ffill()进行对齐
                df_tf_aligned = df_tf_features.reindex(df_merged.index, method='ffill')

                # 🚨 关键改进：严格防止MTFA数据泄露
                if df_tf_aligned.isnull().sum().sum() > 0:
                    # 获取主时间框架开始时间之前的MTFA数据的最后一个值作为默认填充值
                    pre_start_values = {}
                    main_start_time = df_merged.index.min()

                    # 查找主时间框架开始之前的MTFA数据
                    pre_start_mtfa = df_tf_features[df_tf_features.index < main_start_time]

                    if not pre_start_mtfa.empty:
                        # 使用主时间框架开始前的最后一个MTFA值
                        pre_start_values = pre_start_mtfa.iloc[-1].to_dict()
                        logger.debug("add_mtfa_features_to_df: (%s/%s) 使用主时间框架开始前的MTFA值填充开头NaN", target_name_for_logs, tf)
                    else:
                        # 如果没有历史MTFA数据，使用0作为默认值
                        pre_start_values = {col: 0.0 for col in df_tf_aligned.columns}
                        logger.debug("add_mtfa_features_to_df: (%s/%s) 无历史MTFA数据，使用0填充开头NaN", target_name_for_logs, tf)

                    # 🚀 严格的历史数据填充：绝不使用bfill()
                    for col in df_tf_aligned.columns:
                        if col in pre_start_values:
                            df_tf_aligned[col] = df_tf_aligned[col].fillna(pre_start_values[col])
                        else:
                            df_tf_aligned[col] = df_tf_aligned[col].fillna(0.0)

                    # 验证是否还有NaN值
                    remaining_nans = df_tf_aligned.isnull().sum().sum()
                    if remaining_nans > 0:
                        logger.warning("add_mtfa_features_to_df: (%s/%s) 仍有 %d 个NaN值，使用0填充",
                                     target_name_for_logs, tf, remaining_nans)
                        df_tf_aligned.fillna(0.0, inplace=True)
                        
                df_merged = pd.merge(df_merged, df_tf_aligned, left_index=True, right_index=True, how='left')

                # 🚀 严格防止数据泄露：合并后只使用ffill()和固定值填充
                if df_merged.isnull().sum().sum() > 0:
                    # 只使用前向填充，绝不使用后向填充
                    df_merged.ffill(inplace=True)
                    # 剩余的NaN值用0填充（通常是数据开头的部分）
                    df_merged.fillna(0.0, inplace=True)
                    logger.debug("add_mtfa_features_to_df: (%s/%s) 合并后使用ffill()和0值填充剩余NaN", target_name_for_logs, tf)
                    
                logger.info("add_mtfa_features_to_df: (%s/%s) 成功添加 %s 时间框架的 %d 个特征", target_name_for_logs, tf, tf, len(cols_to_keep))
            except (ValueError, TypeError, KeyError) as e_process:
                logger.error("add_mtfa_features_to_df: (%s/%s) 处理 %s 特征数据时出错: %s", target_name_for_logs, tf, tf, e_process)
                continue
        except (ValueError, TypeError) as e_tf_proc:
            logger.error("add_mtfa_features_to_df: (%s/%s) 处理MTFA时间框架 '%s' 时出错: %s", target_name_for_logs, tf, tf, e_tf_proc)
            continue
        except Exception as e_tf_unexpected:
            logger.error("add_mtfa_features_to_df: (%s/%s) 处理MTFA时间框架 '%s' 时发生意外错误: %s", target_name_for_logs, tf, tf, e_tf_unexpected)
            continue
            
    try:
        if df_merged.isnull().sum().sum() > 0:
            logger.debug("add_mtfa_features_to_df: 最终数据存在NaN值，进行填充")
            # 🚀 严格防止数据泄露：只使用ffill()和固定值填充
            df_merged.ffill(inplace=True)
            df_merged.fillna(0.0, inplace=True)
    except (ValueError, TypeError) as e_final:
        logger.error("add_mtfa_features_to_df: 最终数据处理出错: %s", e_final)
        
    # 🎯 新增：在MTFA特征添加完成后，计算时间框架敏感度特征
    try:
        if target_config.get('enable_timeframe_sensitivity', False):
            logger.info("add_mtfa_features_to_df: (%s) 开始计算时间框架敏感度特征", target_name_for_logs)
            _add_timeframe_sensitivity_features(df_merged, target_config, primary_interval)
            logger.info("add_mtfa_features_to_df: (%s) 时间框架敏感度特征计算完成", target_name_for_logs)
    except Exception as e_tf_sensitivity:
        logger.error("add_mtfa_features_to_df: (%s) 计算时间框架敏感度特征时出错: %s", target_name_for_logs, e_tf_sensitivity)
        logger.debug(traceback.format_exc())

    # 🚀 MTFA最终内存优化：对合并后的DataFrame进行内存优化
    if MEMORY_OPTIMIZER_AVAILABLE:
        try:
            memory_optimizer = get_memory_optimizer(target_config)

            # 强制垃圾回收，清理MTFA处理过程中的临时变量
            memory_optimizer.force_garbage_collection(verbose=False)

            # 对最终的MTFA特征DataFrame进行内存优化
            df_merged = memory_optimizer.optimize_dtype(df_merged, aggressive=True, verbose=False)

            logger.debug("add_mtfa_features_to_df: (%s) MTFA最终内存优化完成", target_name_for_logs)
        except Exception as e_mtfa_memory:
            logger.debug("add_mtfa_features_to_df: (%s) MTFA最终内存优化失败: %s", target_name_for_logs, e_mtfa_memory)

    logger.info("add_mtfa_features_to_df: (%s) 完成MTFA特征添加，最终特征数: %d", target_name_for_logs, df_merged.shape[1])
    return df_merged

# --- 动态波动率阈值辅助函数 ---
def _calculate_dynamic_thresholds(df, target_config, target_name_for_log):
    """
    计算动态波动率阈值

    Args:
        df: 包含价格和波动率数据的DataFrame
        target_config: 目标配置字典
        target_name_for_log: 用于日志的目标名称

    Returns:
        pd.Series: 动态阈值序列，如果失败则返回None
    """
    try:
        # 获取动态阈值配置
        dynamic_base = target_config.get('dynamic_threshold_base', 'ATRr_14')
        multipliers = target_config.get('dynamic_threshold_multipliers', [1.5])
        min_threshold = target_config.get('dynamic_threshold_min', 0.005)
        max_threshold = target_config.get('dynamic_threshold_max', 0.05)
        smoothing_period = target_config.get('dynamic_threshold_smoothing', 0)

        # 使用第一个倍数（支持多个倍数的扩展）
        multiplier = multipliers[0] if isinstance(multipliers, list) and multipliers else 1.5

        # 检查波动率指标是否存在
        if dynamic_base not in df.columns:
            logger.warning(f"create_target_variable ({target_name_for_log}): 动态阈值基准列 '{dynamic_base}' 不存在，可用列: {list(df.columns)[:10]}...")
            return None

        # 获取波动率数据
        volatility_data = df[dynamic_base].copy()

        # 检查数据有效性
        if volatility_data.isna().all():
            logger.warning(f"create_target_variable ({target_name_for_log}): 波动率数据 '{dynamic_base}' 全为NaN")
            return None

        # 🚀 严格防止数据泄露：只使用前向填充
        volatility_data = volatility_data.fillna(method='ffill')
        # 对于开头的NaN值，使用第一个有效值或默认值填充
        if volatility_data.isna().any():
            first_valid = volatility_data.dropna()
            if not first_valid.empty:
                volatility_data = volatility_data.fillna(first_valid.iloc[0])
            else:
                volatility_data = volatility_data.fillna(multiplier)  # 使用倍数作为默认值

        # 计算动态阈值
        dynamic_thresholds = volatility_data * multiplier

        # 应用最小/最大限制
        dynamic_thresholds = dynamic_thresholds.clip(lower=min_threshold, upper=max_threshold)

        # 可选的平滑处理
        if smoothing_period > 1:
            dynamic_thresholds = dynamic_thresholds.rolling(window=smoothing_period, min_periods=1).mean()

        # 统计信息
        threshold_stats = {
            'mean': dynamic_thresholds.mean(),
            'std': dynamic_thresholds.std(),
            'min': dynamic_thresholds.min(),
            'max': dynamic_thresholds.max(),
            'median': dynamic_thresholds.median()
        }

        logger.info(f"create_target_variable ({target_name_for_log}): 动态阈值统计 - "
                   f"均值: {threshold_stats['mean']:.4f}, "
                   f"标准差: {threshold_stats['std']:.4f}, "
                   f"范围: [{threshold_stats['min']:.4f}, {threshold_stats['max']:.4f}], "
                   f"中位数: {threshold_stats['median']:.4f}")

        return dynamic_thresholds

    except Exception as e:
        logger.error(f"create_target_variable ({target_name_for_log}): 计算动态阈值时出错: {e}")
        logger.debug(traceback.format_exc())
        return None

def _apply_dynamic_thresholds(df_out, future_close_col, close_prices, dynamic_thresholds, target_name_for_log):
    """
    应用动态阈值创建条件

    Args:
        df_out: 输出DataFrame
        future_close_col: 未来收盘价列名
        close_prices: 当前收盘价序列
        dynamic_thresholds: 动态阈值序列
        target_name_for_log: 用于日志的目标名称

    Returns:
        tuple: (cond_up, cond_down) 上涨和下跌条件
    """
    try:
        # 计算动态上涨和下跌阈值
        upper_thresholds = close_prices * (1 + dynamic_thresholds)
        lower_thresholds = close_prices * (1 - dynamic_thresholds)

        # 创建条件
        cond_up = df_out[future_close_col] > upper_thresholds
        cond_down = df_out[future_close_col] < lower_thresholds

        # 统计条件满足情况
        up_count = cond_up.sum()
        down_count = cond_down.sum()
        total_valid = (~df_out[future_close_col].isna()).sum()

        logger.info(f"create_target_variable ({target_name_for_log}): 动态阈值条件统计 - "
                   f"上涨: {up_count}/{total_valid} ({up_count/total_valid*100:.1f}%), "
                   f"下跌: {down_count}/{total_valid} ({down_count/total_valid*100:.1f}%)")

        return cond_up, cond_down

    except Exception as e:
        logger.error(f"create_target_variable ({target_name_for_log}): 应用动态阈值时出错: {e}")
        logger.debug(traceback.format_exc())
        return None, None


# --- 🚀 三道屏障标签法 (Triple-Barrier Method) ---

def _calculate_triple_barrier_thresholds(df, target_config, target_name_for_log):
    """
    计算三道屏障的止盈和止损阈值

    Args:
        df: 包含价格和波动率数据的DataFrame
        target_config: 目标配置字典
        target_name_for_log: 用于日志的目标名称

    Returns:
        tuple: (profit_thresholds, loss_thresholds) 止盈和止损阈值序列，如果失败则返回(None, None)
    """
    try:
        use_fixed = target_config.get('triple_barrier_use_fixed', False)

        if use_fixed:
            # 使用固定百分比
            profit_threshold = target_config.get('triple_barrier_fixed_profit', 0.02)
            loss_threshold = target_config.get('triple_barrier_fixed_loss', 0.015)

            profit_thresholds = pd.Series([profit_threshold] * len(df), index=df.index)
            loss_thresholds = pd.Series([loss_threshold] * len(df), index=df.index)

            logger.info(f"create_target_variable ({target_name_for_log}): 使用固定三道屏障阈值 - "
                       f"止盈: {profit_threshold:.3f}, 止损: {loss_threshold:.3f}")
        else:
            # 使用基于ATR的动态阈值
            atr_base = target_config.get('dynamic_threshold_base', 'ATRr_14')
            profit_multiplier = target_config.get('triple_barrier_profit_multiplier', 1.5)
            loss_multiplier = target_config.get('triple_barrier_loss_multiplier', 1.0)

            # 检查ATR指标是否存在
            if atr_base not in df.columns:
                logger.warning(f"create_target_variable ({target_name_for_log}): 三道屏障ATR基准列 '{atr_base}' 不存在")
                return None, None

            # 获取ATR数据
            atr_data = df[atr_base].copy()
            if atr_data.isna().all():
                logger.warning(f"create_target_variable ({target_name_for_log}): ATR数据全为NaN")
                return None, None

            # 🚀 严格防止数据泄露：只使用前向填充
            atr_data = atr_data.fillna(method='ffill')
            # 对于开头的NaN值，使用第一个有效值或默认值填充
            if atr_data.isna().any():
                first_valid = atr_data.dropna()
                if not first_valid.empty:
                    atr_data = atr_data.fillna(first_valid.iloc[0])
                else:
                    # 使用合理的默认ATR值（通常是价格的1-2%）
                    default_atr = df['close'].mean() * 0.02 if 'close' in df.columns else 0.02
                    atr_data = atr_data.fillna(default_atr)

            # 计算动态阈值
            profit_thresholds = atr_data * profit_multiplier
            loss_thresholds = atr_data * loss_multiplier

            # 应用最小/最大限制
            min_profit = target_config.get('triple_barrier_min_profit', 0.005)
            max_profit = target_config.get('triple_barrier_max_profit', 0.05)
            min_loss = target_config.get('triple_barrier_min_loss', 0.003)
            max_loss = target_config.get('triple_barrier_max_loss', 0.03)

            profit_thresholds = profit_thresholds.clip(lower=min_profit, upper=max_profit)
            loss_thresholds = loss_thresholds.clip(lower=min_loss, upper=max_loss)

            logger.info(f"create_target_variable ({target_name_for_log}): 使用动态三道屏障阈值 - "
                       f"止盈: {profit_thresholds.mean():.4f}±{profit_thresholds.std():.4f}, "
                       f"止损: {loss_thresholds.mean():.4f}±{loss_thresholds.std():.4f}")

        return profit_thresholds, loss_thresholds

    except Exception as e:
        logger.error(f"create_target_variable ({target_name_for_log}): 计算三道屏障阈值时出错: {e}")
        return None, None


def _apply_triple_barrier_method(df, target_config, target_name_for_log, period):
    """
    应用三道屏障方法生成标签（优化版本）

    Args:
        df: 包含OHLCV数据的DataFrame
        target_config: 目标配置字典
        target_name_for_log: 用于日志的目标名称
        period: 预测周期

    Returns:
        pd.Series: 三道屏障标签序列，如果失败则返回None
    """
    try:
        # 检查是否使用向量化版本
        use_vectorized = target_config.get('triple_barrier_vectorized', True)

        if use_vectorized:
            logger.info(f"create_target_variable ({target_name_for_log}): 使用向量化三道屏障方法")
            return _apply_triple_barrier_method_vectorized(df, target_config, target_name_for_log, period)
        else:
            logger.info(f"create_target_variable ({target_name_for_log}): 使用传统三道屏障方法")
            return _apply_triple_barrier_method_legacy(df, target_config, target_name_for_log, period)

    except Exception as e:
        logger.error(f"create_target_variable ({target_name_for_log}): 应用三道屏障方法时出错: {e}")
        return None


def _apply_triple_barrier_method_vectorized(df, target_config, target_name_for_log, period):
    """
    🚀 向量化版本的三道屏障方法，大幅提升性能

    使用pandas的向量化操作来计算未来周期的最高价和最低价，
    避免双重循环，时间复杂度从O(n*period)降低到O(n)
    """
    try:
        # 计算屏障阈值
        profit_thresholds, loss_thresholds = _calculate_triple_barrier_thresholds(df, target_config, target_name_for_log)
        if profit_thresholds is None or loss_thresholds is None:
            return None

        # 获取价格数据
        close_prices = df['close']
        high_prices = df['high']
        low_prices = df['low']

        # 🚀 核心优化：使用向量化操作计算未来周期的最值
        future_highs, future_lows = _calculate_future_extremes_vectorized(high_prices, low_prices, period)

        # 计算屏障价格（向量化）
        profit_barriers = close_prices * (1 + profit_thresholds)
        loss_barriers = close_prices * (1 - loss_thresholds)

        # 🚀 向量化比较：检查是否触及屏障
        profit_hit = future_highs >= profit_barriers
        loss_hit = future_lows <= loss_barriers

        # 获取最终价格用于时间屏障判断
        final_prices = close_prices.shift(-period)

        # 初始化标签
        labels = pd.Series([-1] * len(df), index=df.index)

        # 生成标签（向量化）
        target_variable_type = target_config.get('target_variable_type', 'UP_ONLY').upper()

        # 有效数据掩码（排除数据末尾无法计算未来价格的部分）
        valid_mask = ~final_prices.isna()

        if target_variable_type == "UP_ONLY":
            # UP_ONLY: 1=触及止盈屏障或最终价格超过止盈阈值, 0=其他
            up_condition = profit_hit & valid_mask
            no_hit_up_condition = (~profit_hit & ~loss_hit & (final_prices >= profit_barriers)) & valid_mask
            labels[up_condition | no_hit_up_condition] = 1
            labels[valid_mask & ~(up_condition | no_hit_up_condition)] = 0

        elif target_variable_type == "DOWN_ONLY":
            # DOWN_ONLY: 1=触及止损屏障或最终价格低于止损阈值, 0=其他
            down_condition = loss_hit & valid_mask
            no_hit_down_condition = (~profit_hit & ~loss_hit & (final_prices <= loss_barriers)) & valid_mask
            labels[down_condition | no_hit_down_condition] = 1
            labels[valid_mask & ~(down_condition | no_hit_down_condition)] = 0

        elif target_variable_type == "BOTH":
            # BOTH: 1=上涨, 0=下跌, 2=中性
            # 优先处理触及屏障的情况（模拟传统方法的逐步检查）

            # 1. 只触及止盈屏障
            only_profit_hit = profit_hit & ~loss_hit & valid_mask
            labels[only_profit_hit] = 1

            # 2. 只触及止损屏障
            only_loss_hit = loss_hit & ~profit_hit & valid_mask
            labels[only_loss_hit] = 0

            # 3. 同时触及两个屏障的情况（这种情况在实际中较少，简化处理）
            both_hit = profit_hit & loss_hit & valid_mask
            # 对于同时触及的情况，我们需要更精确的逻辑来判断哪个先触及
            # 这里简化为按最终价格判断
            labels[both_hit & (final_prices >= close_prices * (1 + profit_thresholds))] = 1
            labels[both_hit & (final_prices <= close_prices * (1 - loss_thresholds))] = 0

            # 4. 未触及任何屏障的情况（时间屏障）
            no_hit_mask = ~profit_hit & ~loss_hit & valid_mask
            labels[no_hit_mask & (final_prices >= profit_barriers)] = 1
            labels[no_hit_mask & (final_prices <= loss_barriers)] = 0
            labels[no_hit_mask & (final_prices > loss_barriers) & (final_prices < profit_barriers)] = 2

        # 统计信息
        valid_labels = labels[labels != -1]
        if len(valid_labels) > 0:
            profit_hits = (profit_hit & valid_mask).sum()
            loss_hits = (loss_hit & valid_mask).sum()
            time_barrier_hits = (~profit_hit & ~loss_hit & valid_mask).sum()
            total_valid = len(valid_labels)

            logger.info(f"create_target_variable ({target_name_for_log}): 向量化三道屏障统计 - "
                       f"止盈触发: {profit_hits}/{total_valid} ({profit_hits/total_valid*100:.1f}%), "
                       f"止损触发: {loss_hits}/{total_valid} ({loss_hits/total_valid*100:.1f}%), "
                       f"时间屏障: {time_barrier_hits}/{total_valid} ({time_barrier_hits/total_valid*100:.1f}%)")

        return labels

    except Exception as e:
        logger.error(f"create_target_variable ({target_name_for_log}): 向量化三道屏障方法出错: {e}")
        return None


def _calculate_future_extremes_vectorized(high_prices, low_prices, period):
    """
    🚀 向量化计算未来period个周期的最高价和最低价

    Args:
        high_prices: 高价序列
        low_prices: 低价序列
        period: 未来周期数

    Returns:
        tuple: (future_highs, future_lows) 未来最高价和最低价序列
    """
    try:
        n = len(high_prices)

        # 🚀 核心优化：使用pandas的rolling配合shift来实现高效的未来窗口计算
        # 方法：将数据反转，应用rolling，再反转回来，最后shift到正确位置

        # 反转数据
        high_reversed = high_prices[::-1]
        low_reversed = low_prices[::-1]

        # 应用rolling计算（在反转的数据上，这相当于计算"过去"的最值）
        future_highs_reversed = high_reversed.rolling(window=period, min_periods=1).max()
        future_lows_reversed = low_reversed.rolling(window=period, min_periods=1).min()

        # 反转回来并shift到正确位置
        future_highs = future_highs_reversed[::-1].shift(-period)
        future_lows = future_lows_reversed[::-1].shift(-period)

        return future_highs, future_lows

    except Exception as e:
        logger.error(f"_calculate_future_extremes_vectorized: 计算未来极值时出错: {e}")
        # 回退到简单方法
        future_highs = pd.Series([np.nan] * len(high_prices), index=high_prices.index)
        future_lows = pd.Series([np.nan] * len(low_prices), index=low_prices.index)

        for i in range(len(high_prices) - period):
            future_highs.iloc[i] = high_prices.iloc[i+1:i+period+1].max()
            future_lows.iloc[i] = low_prices.iloc[i+1:i+period+1].min()

        return future_highs, future_lows


def _apply_triple_barrier_method_legacy(df, target_config, target_name_for_log, period):
    """
    传统版本的三道屏障方法（保留用于对比和调试）
    """
    try:
        # 计算屏障阈值
        profit_thresholds, loss_thresholds = _calculate_triple_barrier_thresholds(df, target_config, target_name_for_log)
        if profit_thresholds is None or loss_thresholds is None:
            return None

        # 获取价格数据
        close_prices = df['close']
        high_prices = df['high']
        low_prices = df['low']

        # 初始化标签数组
        labels = pd.Series([-1] * len(df), index=df.index)  # -1表示无效

        # 统计计数器
        profit_hits = 0
        loss_hits = 0
        time_barrier_hits = 0

        # 对每个时间点应用三道屏障
        for i in range(len(df) - period):
            entry_price = close_prices.iloc[i]
            profit_threshold = profit_thresholds.iloc[i]
            loss_threshold = loss_thresholds.iloc[i]

            # 计算屏障价格
            profit_barrier = entry_price * (1 + profit_threshold)
            loss_barrier = entry_price * (1 - loss_threshold)

            # 检查未来period个周期内的价格路径
            barrier_hit = False
            for j in range(1, period + 1):
                if i + j >= len(df):
                    break

                current_high = high_prices.iloc[i + j]
                current_low = low_prices.iloc[i + j]

                # 检查是否触及止盈屏障（上屏障）
                if current_high >= profit_barrier:
                    labels.iloc[i] = 1  # 上涨
                    profit_hits += 1
                    barrier_hit = True
                    break

                # 检查是否触及止损屏障（下屏障）
                if current_low <= loss_barrier:
                    labels.iloc[i] = 0  # 下跌
                    loss_hits += 1
                    barrier_hit = True
                    break

            # 如果未触及任何价格屏障，根据时间屏障（最终价格）判断
            if not barrier_hit and i + period < len(df):
                final_price = close_prices.iloc[i + period]
                target_variable_type = target_config.get('target_variable_type', 'UP_ONLY').upper()

                if target_variable_type == "UP_ONLY":
                    # UP_ONLY: 根据最终价格是否超过止盈阈值
                    labels.iloc[i] = 1 if final_price >= profit_barrier else 0
                elif target_variable_type == "DOWN_ONLY":
                    # DOWN_ONLY: 根据最终价格是否低于止损阈值
                    labels.iloc[i] = 1 if final_price <= loss_barrier else 0
                elif target_variable_type == "BOTH":
                    # BOTH: 三分类
                    if final_price >= profit_barrier:
                        labels.iloc[i] = 1  # 上涨
                    elif final_price <= loss_barrier:
                        labels.iloc[i] = 0  # 下跌
                    else:
                        labels.iloc[i] = 2  # 中性

                time_barrier_hits += 1

        # 统计信息
        valid_labels = labels[labels != -1]
        total_valid = len(valid_labels)

        if total_valid > 0:
            logger.info(f"create_target_variable ({target_name_for_log}): 传统三道屏障统计 - "
                       f"止盈触发: {profit_hits}/{total_valid} ({profit_hits/total_valid*100:.1f}%), "
                       f"止损触发: {loss_hits}/{total_valid} ({loss_hits/total_valid*100:.1f}%), "
                       f"时间屏障: {time_barrier_hits}/{total_valid} ({time_barrier_hits/total_valid*100:.1f}%)")

        return labels

    except Exception as e:
        logger.error(f"create_target_variable ({target_name_for_log}): 传统三道屏障方法出错: {e}")
        return None


def _apply_multi_timeframe_enhancement(df_main, target_config, binance_client,
                                     cond_up, cond_down, target_name_for_log):
    """
    🚀 多周期融合增强：结合辅助时间周期的K线形态来增强目标变量定义

    Args:
        df_main: 主时间周期的DataFrame
        target_config: 目标配置字典
        binance_client: Binance客户端
        cond_up: 原始上涨条件
        cond_down: 原始下跌条件
        target_name_for_log: 用于日志的目标名称

    Returns:
        tuple: (enhanced_cond_up, enhanced_cond_down) 增强后的条件
    """
    try:
        # 获取配置参数
        secondary_interval = target_config.get('multi_timeframe_secondary_interval', '5m')
        min_bearish_ratio = target_config.get('multi_timeframe_min_bearish_ratio', 0.67)
        symbol = target_config.get('symbol', 'BTCUSDT')
        main_interval = target_config.get('interval', '15m')

        logger.info(f"多周期融合 ({target_name_for_log}): 主周期={main_interval}, 辅助周期={secondary_interval}, 最小下跌比例={min_bearish_ratio}")

        # 计算需要获取的辅助数据量
        data_length = len(df_main)
        # 15分钟对应3根5分钟K线，需要额外缓冲
        secondary_limit = max(data_length * 3 + 100, 500)

        # 获取辅助时间周期数据
        logger.info(f"多周期融合 ({target_name_for_log}): 获取{symbol}@{secondary_interval}数据，限制{secondary_limit}条")
        df_secondary = fetch_binance_history(
            binance_client=binance_client,
            symbol=symbol,
            interval=secondary_interval,
            limit=secondary_limit
        )

        if df_secondary is None or df_secondary.empty:
            logger.warning(f"多周期融合 ({target_name_for_log}): 无法获取{secondary_interval}数据，跳过增强")
            return cond_up, cond_down

        logger.info(f"多周期融合 ({target_name_for_log}): 成功获取{len(df_secondary)}条{secondary_interval}数据")

        # 应用多周期增强逻辑
        enhanced_cond_up, enhanced_cond_down = _enhance_conditions_with_secondary_timeframe(
            df_main, df_secondary, cond_up, cond_down,
            main_interval, secondary_interval, min_bearish_ratio, target_name_for_log
        )

        return enhanced_cond_up, enhanced_cond_down

    except Exception as e:
        logger.error(f"多周期融合 ({target_name_for_log}): 处理失败: {e}")
        logger.info(f"多周期融合 ({target_name_for_log}): 回退到原始条件")
        return cond_up, cond_down


def _enhance_conditions_with_secondary_timeframe(df_main, df_secondary, cond_up, cond_down,
                                               main_interval, secondary_interval, min_bearish_ratio, target_name_for_log):
    """
    使用辅助时间周期数据增强上涨/下跌条件

    Args:
        df_main: 主时间周期DataFrame
        df_secondary: 辅助时间周期DataFrame
        cond_up: 原始上涨条件
        cond_down: 原始下跌条件
        main_interval: 主时间周期
        secondary_interval: 辅助时间周期
        min_bearish_ratio: 最小下跌K线比例
        target_name_for_log: 日志标识

    Returns:
        tuple: (enhanced_cond_up, enhanced_cond_down)
    """
    try:
        # 计算时间周期比例（例如15m/5m = 3）
        main_minutes = _interval_to_minutes(main_interval)
        secondary_minutes = _interval_to_minutes(secondary_interval)
        ratio = main_minutes // secondary_minutes

        if ratio <= 1:
            logger.warning(f"多周期融合 ({target_name_for_log}): 时间周期比例无效 ({main_interval}/{secondary_interval}={ratio})，跳过增强")
            return cond_up, cond_down

        logger.info(f"多周期融合 ({target_name_for_log}): 时间周期比例 {main_interval}/{secondary_interval} = {ratio}")

        # 为每个主周期K线计算对应的辅助周期K线形态
        enhanced_cond_down_list = []
        enhanced_cond_up_list = []

        for i, main_timestamp in enumerate(df_main.index):
            # 计算对应的辅助周期时间范围
            period_start = main_timestamp - pd.Timedelta(minutes=main_minutes)
            period_end = main_timestamp

            # 获取这个时间段内的辅助周期K线
            secondary_period = df_secondary[
                (df_secondary.index > period_start) &
                (df_secondary.index <= period_end)
            ]

            # 计算辅助周期内的下跌K线比例
            if len(secondary_period) >= ratio * 0.5:  # 至少有一半预期的K线数量
                bearish_count = (secondary_period['close'] < secondary_period['open']).sum()
                bearish_ratio = bearish_count / len(secondary_period)

                # 增强下跌条件：原始下跌 OR (价格轻微下跌 AND 辅助周期下跌比例足够)
                original_down = cond_down.iloc[i] if i < len(cond_down) else False

                # 检查是否有轻微价格下跌（即使不满足原始阈值）
                if i < len(df_main) - 1:  # 确保有未来价格数据
                    current_close = df_main['close'].iloc[i]
                    future_close = df_main['close'].iloc[i + 1] if i + 1 < len(df_main) else current_close
                    price_declined = future_close < current_close
                else:
                    price_declined = False

                # 计算辅助周期内的上涨K线比例
                bullish_count = (secondary_period['close'] > secondary_period['open']).sum()
                bullish_ratio = bullish_count / len(secondary_period)

                # 检查是否有轻微价格上涨（即使不满足原始阈值）
                price_increased = future_close > current_close

                # 🚀 V12.0 优化：为UP模型实现完全对称的时间维度增强策略
                # DOWN模型增强逻辑：原始下跌 OR (价格下跌 AND 辅助周期确认)
                enhanced_down = original_down or (price_declined and bearish_ratio >= min_bearish_ratio)
                enhanced_cond_down_list.append(enhanced_down)

                # 🚀 UP模型平权：完全对称的上涨增强逻辑，使用相同的阈值标准
                original_up = cond_up.iloc[i] if i < len(cond_up) else False
                # 新的"明确上涨"定义：原始上涨 OR (价格上涨 AND 辅助周期至少有相同比例的上涨K线)
                enhanced_up = original_up or (price_increased and bullish_ratio >= min_bearish_ratio)  # 使用相同的比例阈值确保公平
                enhanced_cond_up_list.append(enhanced_up)

            else:
                # 数据不足时保持原始条件
                enhanced_cond_down_list.append(cond_down.iloc[i] if i < len(cond_down) else False)
                enhanced_cond_up_list.append(cond_up.iloc[i] if i < len(cond_up) else False)

        # 转换为pandas Series
        enhanced_cond_up = pd.Series(enhanced_cond_up_list, index=df_main.index)
        enhanced_cond_down = pd.Series(enhanced_cond_down_list, index=df_main.index)

        # 统计增强效果
        original_up_count = cond_up.sum()
        original_down_count = cond_down.sum()
        enhanced_up_count = enhanced_cond_up.sum()
        enhanced_down_count = enhanced_cond_down.sum()

        logger.info(f"多周期融合 ({target_name_for_log}): 上涨信号变化 {original_up_count} -> {enhanced_up_count} "
                   f"({enhanced_up_count/len(df_main)*100:.1f}%), "
                   f"下跌信号变化 {original_down_count} -> {enhanced_down_count} "
                   f"({enhanced_down_count/len(df_main)*100:.1f}%)")

        return enhanced_cond_up, enhanced_cond_down

    except Exception as e:
        logger.error(f"多周期融合条件增强 ({target_name_for_log}): 失败: {e}")
        return cond_up, cond_down


def _interval_to_minutes(interval_str):
    """将时间间隔字符串转换为分钟数"""
    try:
        if interval_str.endswith('m'):
            return int(interval_str[:-1])
        elif interval_str.endswith('h'):
            return int(interval_str[:-1]) * 60
        elif interval_str.endswith('d'):
            return int(interval_str[:-1]) * 24 * 60
        else:
            logger.warning(f"未知的时间间隔格式: {interval_str}")
            return 15  # 默认15分钟
    except (ValueError, IndexError):
        logger.warning(f"解析时间间隔失败: {interval_str}")
        return 15


def _validate_target_variable_quality(target_series, target_col, target_config):
    """
    🎯 验证目标变量质量，监控正负类样本比例

    确保正类样本比例在合理范围（5%-15%），避免严重的类别不平衡
    """
    try:
        # 计算样本分布
        value_counts = target_series.value_counts()
        total_samples = len(target_series)

        # 计算比例
        negative_count = value_counts.get(0, 0)
        positive_count = value_counts.get(1, 0)
        neutral_count = value_counts.get(2, 0)  # 三分类情况
        negative_ratio = negative_count / total_samples if total_samples > 0 else 0
        positive_ratio = positive_count / total_samples if total_samples > 0 else 0
        neutral_ratio = neutral_count / total_samples if total_samples > 0 else 0

        # 打印详细统计
        logger.info(f"🎯 目标变量质量验证 - {target_col}:")
        logger.info(f"  总样本数: {total_samples}")
        logger.info(f"  负类样本(0): {negative_count} ({negative_ratio:.2%})")
        logger.info(f"  正类样本(1): {positive_count} ({positive_ratio:.2%})")
        if neutral_count > 0:
            logger.info(f"  中性样本(2): {neutral_count} ({neutral_ratio:.2%})")

        # 质量评估
        if positive_ratio < 0.01:  # 小于1%
            logger.warning(f"⚠️  正类样本比例过低 ({positive_ratio:.2%})，可能导致模型无法学习")
            logger.warning(f"   建议：降低动态阈值乘数 (当前: {target_config.get('dynamic_threshold_atr_multiplier', 'N/A')})")
        elif positive_ratio < 0.05:  # 小于5%
            logger.warning(f"⚠️  正类样本比例较低 ({positive_ratio:.2%})，建议关注类别不平衡处理")
        elif positive_ratio > 0.15:  # 大于15%
            logger.warning(f"⚠️  正类样本比例较高 ({positive_ratio:.2%})，可能包含过多噪音")
            logger.warning(f"   建议：提高动态阈值乘数 (当前: {target_config.get('dynamic_threshold_atr_multiplier', 'N/A')})")
        else:
            logger.info(f"✅ 正类样本比例合理 ({positive_ratio:.2%})，在5%-15%范围内")

        # 动态阈值效果分析
        if target_config.get('enable_dynamic_thresholds', False):
            atr_multiplier = target_config.get('dynamic_threshold_atr_multiplier', 'N/A')
            logger.info(f"  动态阈值配置: ATR乘数 = {atr_multiplier}")

            # 给出调整建议
            if positive_ratio < 0.05:
                suggested_multiplier = max(0.5, float(atr_multiplier) * 0.8) if atr_multiplier != 'N/A' else 0.6
                logger.info(f"  💡 建议降低ATR乘数至 {suggested_multiplier:.1f} 以增加正类样本")
            elif positive_ratio > 0.15:
                suggested_multiplier = min(2.0, float(atr_multiplier) * 1.2) if atr_multiplier != 'N/A' else 1.0
                logger.info(f"  💡 建议提高ATR乘数至 {suggested_multiplier:.1f} 以提升信号质量")

        # 类别权重建议
        current_class_weight = target_config.get('class_weight', {})
        if positive_ratio < 0.05:
            suggested_weight = min(20.0, 1.0 / positive_ratio * 0.1)
            logger.info(f"  💡 建议正类权重: {{0: 1.0, 1: {suggested_weight:.1f}}} (当前: {current_class_weight})")

        # SMOTE建议
        if positive_ratio < 0.1:
            smote_enabled = target_config.get('smote_enable', False)
            logger.info(f"  💡 SMOTE过采样状态: {'已启用' if smote_enabled else '未启用'}")
            if not smote_enabled:
                logger.info(f"  💡 建议启用SMOTE过采样以缓解类别不平衡")

    except Exception as e:
        logger.error(f"_validate_target_variable_quality: 验证目标变量质量时出错: {e}")

# --- create_target_variable (重构统一标签体系，支持动态波动率阈值) ---
def create_target_variable(df, target_config, binance_client=None):
    """
    增强版目标变量创建函数，集成三道屏障法和多周期确认为一体。
    """
    target_name = target_config.get('name', 'UnknownTarget')
    logger.info(f"create_target_variable ({target_name}): 开始使用增强版目标变量创建流程。")

    # --- 1. 基础参数验证 ---
    if not isinstance(df, pd.DataFrame) or df.empty:
        logger.error(f"({target_name}): 输入的DataFrame无效。")
        return None, None

    periods_list = target_config.get('prediction_periods', [1])
    period = periods_list[0] if periods_list else 1
    target_col = f"target_{period}p_{target_name}"

    # --- 2. 检查并选择标签生成方法 ---
    labeling_method = target_config.get('labeling_method', 'threshold')
    enable_triple_barrier = target_config.get('enable_triple_barrier', False)
    enable_dynamic_thresholds = target_config.get('enable_dynamic_thresholds', False)

    df_out = df.copy()
    labels = pd.Series(-1, index=df_out.index) # 默认标签为-1 (无效)

    # 🎯 支持纯方向标签法 (pure_direction)
    if labeling_method == 'pure_direction':
        logger.info(f"({target_name}): 使用纯方向标签法 (Pure Direction Method)。")

        # 计算未来价格
        future_close_col = f'future_close_{period}p_temp'
        df_out[future_close_col] = df_out['close'].shift(-period)

        # 简单的价格比较：上涨=1, 下跌=0
        cond_invalid = df_out[future_close_col].isna()
        cond_up = df_out[future_close_col] > df_out['close']
        cond_down = df_out[future_close_col] < df_out['close']

        # 根据模型类型应用条件
        target_type = target_config.get('target_variable_type', 'BOTH').upper()

        if target_type == "UP_ONLY":
            labels = np.select([cond_invalid, cond_up], [-1, 1], default=0)
        elif target_type == "DOWN_ONLY":
            labels = np.select([cond_invalid, cond_down], [-1, 1], default=0)
        elif target_type == "BOTH":
            labels = np.select([cond_invalid, cond_up, cond_down], [-1, 1, 0], default=2)

        df_out.drop(columns=[future_close_col], inplace=True)
        labels = pd.Series(labels, index=df_out.index)

        # 统计信息
        valid_labels = labels[labels != -1]
        if len(valid_labels) > 0:
            up_count = (labels == 1).sum()
            down_count = (labels == 0).sum()
            neutral_count = (labels == 2).sum() if target_type == "BOTH" else 0
            total_valid = len(valid_labels)

            logger.info(f"({target_name}): 纯方向标签统计 - "
                       f"上涨: {up_count}/{total_valid} ({up_count/total_valid*100:.1f}%), "
                       f"下跌: {down_count}/{total_valid} ({down_count/total_valid*100:.1f}%)" +
                       (f", 中性: {neutral_count}/{total_valid} ({neutral_count/total_valid*100:.1f}%)" if target_type == "BOTH" else ""))

    elif enable_triple_barrier:
        logger.info(f"({target_name}): 启用三道屏障法 (Triple-Barrier Method)。")

        # 检查是否有必要的ATR列
        atr_base = target_config.get('dynamic_threshold_base', 'ATRr_14')
        if atr_base not in df_out.columns:
            logger.warning(f"({target_name}): 三道屏障法需要的ATR列 '{atr_base}' 不存在，回退到动态阈值法。")
            enable_triple_barrier = False
            enable_dynamic_thresholds = True
        else:
            barrier_labels = _apply_triple_barrier_method_vectorized(df_out, target_config, target_name, period)
            if barrier_labels is not None:
                labels = barrier_labels
            else:
                logger.warning(f"({target_name}): 三道屏障法执行失败，回退到动态阈值法。")
                enable_triple_barrier = False
                enable_dynamic_thresholds = True

    if enable_dynamic_thresholds:
        logger.info(f"({target_name}): 启用动态波动率阈值法。")
        dynamic_thresholds = _calculate_dynamic_thresholds(df_out, target_config, target_name)
        if dynamic_thresholds is not None:
            future_close_col = f'future_close_{period}p_temp'
            df_out[future_close_col] = df_out['close'].shift(-period)
            cond_up, cond_down = _apply_dynamic_thresholds(df_out, future_close_col, df_out['close'], dynamic_thresholds, target_name)

            # --- 多周期融合增强 ---
            if target_config.get('enable_multi_timeframe_target', False) and binance_client:
                cond_up, cond_down = _apply_multi_timeframe_enhancement(df_out, target_config, binance_client, cond_up, cond_down, target_name)

            # 根据模型类型应用条件
            target_type = target_config.get('target_variable_type', 'BOTH').upper()
            cond_invalid = df_out[future_close_col].isna()

            if target_type == "UP_ONLY":
                labels = np.select([cond_invalid, cond_up], [-1, 1], default=0)
            elif target_type == "DOWN_ONLY":
                labels = np.select([cond_invalid, cond_down], [-1, 1], default=0)
            elif target_type == "BOTH":
                 labels = np.select([cond_invalid, cond_up, cond_down], [-1, 1, 0], default=2)

            df_out.drop(columns=[future_close_col], inplace=True)
            labels = pd.Series(labels, index=df_out.index)
        else:
            logger.error(f"({target_name}): 动态阈值计算失败，回退到固定阈值法。")
            # 回退到固定阈值法
            future_close_col = f'future_close_{period}p_temp'
            df_out[future_close_col] = df_out['close'].shift(-period)
            threshold = target_config.get('target_threshold', 0.002)

            cond_up = df_out[future_close_col] > df_out['close'] * (1 + threshold)
            cond_down = df_out[future_close_col] < df_out['close'] * (1 - threshold)

            # 根据模型类型应用条件
            target_type = target_config.get('target_variable_type', 'BOTH').upper()
            cond_invalid = df_out[future_close_col].isna()

            if target_type == "UP_ONLY":
                labels = np.select([cond_invalid, cond_up], [-1, 1], default=0)
            elif target_type == "DOWN_ONLY":
                labels = np.select([cond_invalid, cond_down], [-1, 1], default=0)
            elif target_type == "BOTH":
                 labels = np.select([cond_invalid, cond_up, cond_down], [-1, 1, 0], default=2)

            df_out.drop(columns=[future_close_col], inplace=True)
            labels = pd.Series(labels, index=df_out.index)
            logger.info(f"({target_name}): 使用固定阈值法 (threshold={threshold:.4f}) 作为最终回退。")

    # 检查标签生成方法的有效性
    if labeling_method != 'pure_direction' and not enable_triple_barrier and not enable_dynamic_thresholds:
        logger.error(f"({target_name}): 必须启用三道屏障法或动态波动率阈值法之一，或使用纯方向标签法。")
        return None, None

    # --- 3. 最终处理和验证 ---
    df_out[target_col] = labels

    # 移除无效样本
    df_final = df_out[df_out[target_col] != -1].copy()

    if df_final.empty:
        logger.error(f"({target_name}): 创建目标变量后，没有剩余的有效样本。")
        return None, None

    # 质量验证
    _validate_target_variable_quality(df_final[target_col], target_col, target_config)

    return df_final, target_col




# --- MODIFIED: prepare_features_for_prediction ---
def prepare_features_for_prediction(df_klines_recent_primary, client, scaler, target_config, model_meta=None):
    target_name = target_config.get('name', 'unknown_target_prep_feat')

    if not isinstance(target_config, dict):
        logger.error(f"Config Error in prepare_features_for_prediction for target '{target_name}': Expected dict, got {type(target_config)}.")
        return None
    
    if not scaler or not hasattr(scaler, 'transform'):
        logger.error(f"错误 [{target_name}]: 预测需要有效 Scaler. 当前 scaler: {scaler}, 类型: {type(scaler)}.")
        return None

    if not isinstance(df_klines_recent_primary, pd.DataFrame):
        logger.error(f"错误 [{target_name}]: df_klines_recent_primary 必须是DataFrame类型，而不是 {type(df_klines_recent_primary)}.")
        return None
        
    required_primary_lookback = target_config.get('min_historical_bars_for_prediction', 100)
    if len(df_klines_recent_primary) < required_primary_lookback:
        logger.error(f"错误 [{target_name}]: 主 K 线数据不足 (获取 {len(df_klines_recent_primary)} 条, 至少需 {required_primary_lookback} 条 for target '{target_name}').")
        return None
    
    try:
        df_primary_features = add_classification_features(df_klines_recent_primary.copy(), target_config)
        if df_primary_features is None or df_primary_features.empty:
            logger.error(f"错误 [{target_name}]: add_classification_features 返回空 (primary features) for target '{target_name}'.")
            return None
    except ValueError as ve:
        logger.error(f"错误 [{target_name}]: 计算基础特征时发生 ValueError: {ve}. Input df shape: {df_klines_recent_primary.shape}")
        logger.debug(traceback.format_exc())
        return None
    except KeyError as ke:
        logger.error(f"错误 [{target_name}]: 计算基础特征时发生 KeyError: {ke}. Available columns: {df_klines_recent_primary.columns.tolist()}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e:
        logger.error(f"错误 [{target_name}]: 计算基础特征时发生未知错误: {e}")
        logger.debug(traceback.format_exc())
        return None
  
    df_combined_features_intermediate = df_primary_features
    if target_config.get('enable_mtfa', False):
        try:
            df_mtfa_added = add_mtfa_features_to_df(df_primary_features.copy(), target_config, client)
            if df_mtfa_added is None or df_mtfa_added.empty:
                logger.warning(f"警告 [{target_name}]: add_mtfa_features_to_df 返回空，但MTFA已启用。将仅使用主周期特征。 Primary features shape: {df_primary_features.shape}")
            else:
                df_combined_features_intermediate = df_mtfa_added
        except BinanceAPIException as bae:
            logger.error(f"错误 [{target_name}]: 获取MTFA数据时发生 BinanceAPIException: {bae}. 将仅使用主周期特征.")
            logger.debug(traceback.format_exc())
        except BinanceRequestException as bre:
            logger.error(f"错误 [{target_name}]: 获取MTFA数据时发生 BinanceRequestException: {bre}. 将仅使用主周期特征.")
            logger.debug(traceback.format_exc())
        except Exception as e_mtfa:
            logger.error(f"错误 [{target_name}]: 添加MTFA特征时发生未知错误: {e_mtfa}. 将仅使用主周期特征.")
            logger.debug(traceback.format_exc())
    
    try:
        if df_combined_features_intermediate.empty or df_combined_features_intermediate.iloc[-1:].empty:
            logger.error(f"错误 [{target_name}]: df_combined_features_intermediate (shape: {df_combined_features_intermediate.shape if isinstance(df_combined_features_intermediate, pd.DataFrame) else 'N/A'}) 为空或没有最后一行可供提取。")
            return None
        latest_features_row_series = df_combined_features_intermediate.iloc[-1].copy()
        latest_features_index = df_combined_features_intermediate.index[-1:]
        latest_combined_df_for_selection = pd.DataFrame([latest_features_row_series.to_dict()], index=latest_features_index)
    except IndexError as ie:
        logger.error(f"错误 [{target_name}]: 提取最新特征行时发生 IndexError: {ie}. df_combined_features_intermediate shape: {df_combined_features_intermediate.shape}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e_latest_feat:
        logger.error(f"错误 [{target_name}]: 提取最新特征行时发生未知错误: {e_latest_feat}")
        logger.debug(traceback.format_exc())
        return None
    
    expected_features = []
    model_dir = target_config.get('model_save_dir')
    
    if model_meta is not None and 'feature_list_filename' in model_meta:
        feature_list_filename = model_meta.get('feature_list_filename')
        if model_dir and feature_list_filename:
            feature_list_path = os.path.join(model_dir, feature_list_filename)
            if os.path.exists(feature_list_path):
                try:
                    with open(feature_list_path, 'r') as f_feat:
                        feature_list_from_file = json.load(f_feat)
                        if isinstance(feature_list_from_file, list) and feature_list_from_file:
                            expected_features = feature_list_from_file
                            logger.info(f"  [{target_name}] 从文件 '{feature_list_path}' 加载了 {len(expected_features)} 个特征名。")
                except json.JSONDecodeError as jde:
                    logger.warning(f"警告 [{target_name}]: 特征列表文件 '{feature_list_path}' 包含无效JSON: {jde}。将尝试使用scaler中的特征名。")
                except FileNotFoundError:
                    logger.warning(f"警告 [{target_name}]: 特征列表文件 '{feature_list_path}' 未找到。将尝试使用scaler中的特征名。") # Corrected from print
                except PermissionError:
                    logger.warning(f"警告 [{target_name}]: 无权限读取特征列表文件 '{feature_list_path}'。将尝试使用scaler中的特征名。")
                except Exception as e_feat_load:
                    logger.warning(f"警告 [{target_name}]: 从文件 '{feature_list_path}' 加载特征列表失败: {e_feat_load}。将尝试使用scaler中的特征名。")
                    logger.debug(traceback.format_exc())
            else:
                 logger.warning(f"警告 [{target_name}]: 特征列表文件路径不存在 '{feature_list_path}'. 将尝试使用scaler中的特征名.")

    if not expected_features:
        if hasattr(scaler, 'feature_names_in_') and getattr(scaler, 'feature_names_in_', None) is not None and len(getattr(scaler, 'feature_names_in_', [])) > 0:
            expected_features = list(getattr(scaler, 'feature_names_in_', []))
            logger.info(f"  [{target_name}] 使用scaler.feature_names_in_中的 {len(expected_features)} 个特征名。 ({expected_features[:5]}...)")
        else:
            # If not loaded from file and scaler.feature_names_in_ is not available or empty, then we cannot proceed.
            logger.error(f"错误 [{target_name} prep_feat]: 无法确定预期的特征列表。未从文件加载特征列表，且scaler.feature_names_in_不可用或为空。")
            return None
        # No 'pass' here, if expected_features is still empty after this, the next block handles it.

    if not expected_features: # This block should ideally not be reached if the logic above is sound.
        logger.error(f"错误 [{target_name} prep_feat]: 最终未能确定expected_features列表。这表示之前的逻辑分支存在问题，未能从文件或scaler.feature_names_in_获取有效特征列表。")
        return None

    data_for_aligned_df = {}
    missing_in_pred = []
    present_cols_in_latest = latest_combined_df_for_selection.columns

    for col_name in expected_features:
        if col_name in present_cols_in_latest:
            data_for_aligned_df[col_name] = latest_combined_df_for_selection[col_name].values 
        else:
            data_for_aligned_df[col_name] = np.array([0.0]) 
            missing_in_pred.append(col_name)
            
    try:
        aligned_df_pred = pd.DataFrame(data_for_aligned_df, index=latest_features_index)
        final_df_to_scale = aligned_df_pred[expected_features]
    except KeyError as ke:
        logger.error(f"错误 [{target_name} prep_feat]: 从字典创建对齐的DataFrame时发生 KeyError: {ke}. Expected: {expected_features}, Available in dict: {list(data_for_aligned_df.keys())}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e_df_create:
        logger.error(f"错误 [{target_name} prep_feat]: 从字典创建对齐的DataFrame失败: {e_df_create}")
        logger.debug(traceback.format_exc())
        return None

    if missing_in_pred:
        logger.warning(f"警告 [{target_name} prep_feat]: 预测时缺失Scaler期望特征 (已用0填充): {missing_in_pred}. Expected: {expected_features[:10]}..., Available: {present_cols_in_latest.tolist()[:10]}...")
    
    try:
        if final_df_to_scale.isnull().values.any() or np.isinf(final_df_to_scale.values).any():
            nan_cols = final_df_to_scale.columns[final_df_to_scale.isnull().any()].tolist()
            inf_cols = final_df_to_scale.columns[np.isinf(final_df_to_scale).any()].tolist()
            logger.warning(f"警告 [{target_name} prep_feat]: final_df_to_scale (shape {final_df_to_scale.shape}) 包含 NaN/Inf，将用0填充. NaN cols: {nan_cols}, Inf cols: {inf_cols}")
            final_df_to_scale = final_df_to_scale.fillna(0).replace([np.inf, -np.inf], 0)
    except Exception as e_fill_na:
        logger.error(f"错误 [{target_name} prep_feat]: 填充 NaN/Inf 时发生错误: {e_fill_na}")
        logger.debug(traceback.format_exc())
        return None

    X_scaled_np = None
    try:
        X_scaled_np = scaler.transform(final_df_to_scale)
    except ValueError as ve:
        logger.error(f"缩放错误 [{target_name} prep_feat]: 特征数量或顺序不匹配! Scaler期望特征: {len(expected_features)} (名称: {expected_features[:5]}...), 当前传递特征: {final_df_to_scale.shape[1]} (名称: {list(final_df_to_scale.columns)[:5]}...). 错误: {ve}")
        logger.debug(traceback.format_exc())
        return None
    except TypeError as te:
        logger.error(f"缩放时发生 TypeError [{target_name} prep_feat]: {te}. Input dtypes: {final_df_to_scale.dtypes.to_dict()}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e_scale_pred:
        logger.error(f"缩放时发生未知错误 [{target_name} prep_feat]: {e_scale_pred}")
        logger.debug(traceback.format_exc())
        return None
    
    if X_scaled_np is None or not isinstance(X_scaled_np, np.ndarray) or X_scaled_np.shape[0] != 1:
        logger.error(f"错误 [{target_name} prep_feat]: 缩放结果无效或形状不为单行 (得到 shape: {X_scaled_np.shape if hasattr(X_scaled_np, 'shape') else 'N/A'}, type: {type(X_scaled_np)}). Scaler: {type(scaler)}")
        return None
    
    try:
        feature_names_for_output_df = list(final_df_to_scale.columns)
        if X_scaled_np.shape[1] != len(feature_names_for_output_df):
            logger.error(f"错误 [{target_name} prep_feat]: 缩放后特征数量 ({X_scaled_np.shape[1]}) 与预期列名数量 ({len(feature_names_for_output_df)}) 不符! Expected names: {feature_names_for_output_df[:5]}...")
            return None
        X_scaled_df_with_names = pd.DataFrame(X_scaled_np, columns=feature_names_for_output_df, index=final_df_to_scale.index)
    except Exception as e_final_df:
        logger.error(f"错误 [{target_name} prep_feat]: 创建最终缩放后DataFrame时发生错误: {e_final_df}. X_scaled_np shape: {X_scaled_np.shape}, feature_names_for_output_df len: {len(feature_names_for_output_df) if 'feature_names_for_output_df' in locals() else 'N/A'}")
        logger.debug(traceback.format_exc())
        return None
        
    logger.debug(f"  DEBUG [{target_name} prep_feat]: 成功准备并缩放了预测特征。Shape: {X_scaled_df_with_names.shape}")
    return X_scaled_df_with_names


def find_optimal_threshold(y_true, y_proba, target_name="", method="f1", verbose=True,
                          min_precision=None, precision_constraint=None,
                          payout_ratio=0.85, min_trades=5):
    """
    寻找最优决策阈值 (支持基于盈利能力的优化)

    注意：此函数使用简单的阈值遍历方法。如需高级优化算法（网格搜索、贝叶斯优化），
    请使用 src.core.threshold_optimization.ThresholdOptimizer 类。

    Args:
        y_true (array-like): 真实标签 (0或1)
        y_proba (array-like): 正类的预测概率
        target_name (str): 目标名称，用于日志记录
        method (str): 优化方法，可选:
            - "f1": 最大化F1分数 (默认)
            - "precision_recall": 基于精确率-召回率曲线的最优点
            - "youden": Youden指数 (敏感性 + 特异性 - 1)
            - "balanced": 平衡精确率和召回率
            - "precision_constrained_recall": 在精确率约束下最大化召回率
            - "simulated_profit": 🎯 基于模拟交易盈利能力优化 (新增)
            - "risk_adjusted_return": 基于风险调整收益优化 (新增)
            - "expected_profit": 基于期望收益优化 (新增)
        verbose (bool): 是否输出详细信息
        min_precision (float): 最小精确率约束 (仅在method="precision_constrained_recall"时使用)
        precision_constraint (float): 精确率约束 (向后兼容参数，等同于min_precision)
        payout_ratio (float): 盈亏比，用于盈利能力计算 (默认0.85)
        min_trades (int): 最小交易次数约束，用于盈利能力计算 (默认5)

    Returns:
        dict: 包含最优阈值和相关指标的字典
            {
                'optimal_threshold': float,
                'f1_score': float,
                'precision': float,
                'recall': float,
                'accuracy': float,
                'method': str,
                'threshold_range': tuple,
                'n_samples': int,
                'precision_constraint': float (如果使用了约束),
                'valid_thresholds_count': int (满足约束的阈值数量),
                # 🎯 新增盈利能力指标 (当method为盈利相关时)
                'expected_profit_per_trade': float,
                'total_trades': int,
                'win_rate': float,
                'risk_adjusted_return': float
            }
    """
    try:
        # 方法验证 - 检查是否为支持的方法
        supported_methods = [
            "f1", "precision_recall", "youden", "balanced",
            "precision_constrained_recall", "simulated_profit",
            "risk_adjusted_return", "expected_profit"
        ]

        if method not in supported_methods:
            if method in ["grid_search", "bayesian"]:
                logger.error(f"find_optimal_threshold [{target_name}]: 方法 '{method}' 需要使用 ThresholdOptimizer 类。"
                           f"请使用 src.core.threshold_optimization.ThresholdOptimizer 进行高级优化。")
                logger.info(f"find_optimal_threshold [{target_name}]: 当前函数支持的方法: {supported_methods}")
                return None
            else:
                logger.error(f"find_optimal_threshold [{target_name}]: 不支持的方法 '{method}'。"
                           f"支持的方法: {supported_methods}")
                return None

        # 输入验证
        y_true = np.array(y_true)
        y_proba = np.array(y_proba)

        if len(y_true) != len(y_proba):
            logger.error(f"find_optimal_threshold [{target_name}]: y_true和y_proba长度不匹配: {len(y_true)} vs {len(y_proba)}")
            return None

        if len(y_true) == 0:
            logger.error(f"find_optimal_threshold [{target_name}]: 输入数据为空")
            return None

        # 检查标签是否为二分类
        unique_labels = np.unique(y_true)
        if len(unique_labels) != 2 or not all(label in [0, 1] for label in unique_labels):
            logger.error(f"find_optimal_threshold [{target_name}]: 标签必须是二分类(0,1)，当前标签: {unique_labels}")
            return None

        # 检查概率范围
        if np.any(y_proba < 0) or np.any(y_proba > 1):
            logger.warning(f"find_optimal_threshold [{target_name}]: 概率值超出[0,1]范围，将进行裁剪")
            y_proba = np.clip(y_proba, 0, 1)

        # 处理精确率约束参数（向后兼容）
        precision_constraint_value = min_precision or precision_constraint
        if method == "precision_constrained_recall" and precision_constraint_value is None:
            logger.warning(f"find_optimal_threshold [{target_name}]: 使用precision_constrained_recall方法但未指定精确率约束，使用默认值0.65")
            precision_constraint_value = 0.65

        if verbose:
            logger.info(f"find_optimal_threshold [{target_name}]: 开始寻找最优阈值，方法={method}，样本数={len(y_true)}")
            pos_ratio = np.mean(y_true) * 100
            logger.info(f"find_optimal_threshold [{target_name}]: 正类比例: {pos_ratio:.2f}%")
            if precision_constraint_value is not None:
                logger.info(f"find_optimal_threshold [{target_name}]: 精确率约束: >= {precision_constraint_value:.3f}")

        # 生成候选阈值
        # 使用更细粒度的阈值范围
        thresholds_coarse = np.linspace(0.1, 0.9, 81)  # 0.1到0.9，步长0.01
        thresholds_fine = np.linspace(0.4, 0.6, 41)    # 0.4到0.6，步长0.005
        thresholds = np.unique(np.concatenate([thresholds_coarse, thresholds_fine]))

        best_threshold = 0.5
        best_score = 0.0
        best_metrics = {}
        valid_thresholds_count = 0  # 满足约束的阈值数量

        # 存储所有阈值的结果用于分析
        threshold_results = []

        for threshold in thresholds:
            y_pred = (y_proba >= threshold).astype(int)

            # 计算各种指标
            try:
                f1 = f1_score(y_true, y_pred, zero_division=0)
                precision = precision_score(y_true, y_pred, zero_division=0)
                recall = recall_score(y_true, y_pred, zero_division=0)
                accuracy = accuracy_score(y_true, y_pred)

                # 计算特异性 (真负率)
                tn = np.sum((y_true == 0) & (y_pred == 0))
                fp = np.sum((y_true == 0) & (y_pred == 1))
                specificity = tn / (tn + fp) if (tn + fp) > 0 else 0

                # 🎯 新增：计算盈利能力指标 (当使用盈利相关方法时)
                profit_metrics = {}
                if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                    profit_metrics = _calculate_binary_simulated_profit(
                        y_true, y_proba, threshold, payout_ratio
                    )

                # 🎯 核心修改：支持多种优化方法，包括盈利能力优化
                # 初始化分数为0
                score = 0.0
                min_precision_req = precision_constraint_value if precision_constraint_value is not None else 0.0

                # 检查是否满足精确率约束
                precision_constraint_satisfied = precision >= min_precision_req

                # 🎯 新增：检查最小交易次数约束 (仅对盈利相关方法)
                min_trades_satisfied = True
                if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                    min_trades_satisfied = profit_metrics.get('total_trades', 0) >= min_trades

                # 只有在满足所有约束时，才使用相应方法的评分
                if precision_constraint_satisfied and min_trades_satisfied:
                    valid_thresholds_count += 1

                    if method == "precision_constrained_recall":
                        score = recall  # 在满足精确率约束的前提下最大化召回率
                    elif method == "f1":
                        score = f1  # 在满足精确率约束的前提下最大化F1分数
                    elif method == "precision_recall":
                        # 平衡精确率和召回率
                        score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                    elif method == "youden":
                        # Youden指数 = 敏感性 + 特异性 - 1
                        score = recall + specificity - 1
                    elif method == "balanced":
                        # 平衡精确率、召回率和准确率
                        score = (precision + recall + accuracy) / 3
                    # 🎯 新增：盈利能力优化方法
                    elif method == "simulated_profit":
                        # 基于模拟交易盈利能力优化
                        score = profit_metrics.get('expected_profit_per_trade', 0.0)
                    elif method == "risk_adjusted_return":
                        # 基于风险调整收益优化
                        score = profit_metrics.get('risk_adjusted_return', 0.0)
                    elif method == "expected_profit":
                        # 基于期望收益优化 (与simulated_profit相同，但名称更明确)
                        score = profit_metrics.get('expected_profit_per_trade', 0.0)
                    else:
                        logger.warning(f"find_optimal_threshold [{target_name}]: 未知方法 '{method}'，使用F1分数")
                        score = f1
                else:
                    # 🎯 如果不满足约束，给一个巨大的负分
                    if not precision_constraint_satisfied:
                        # 精确率约束惩罚
                        score = -1 + (precision - min_precision_req)
                        score = min(score, -0.001)
                    elif not min_trades_satisfied:
                        # 最小交易次数约束惩罚
                        score = -0.5
                    else:
                        score = -0.001

                # 🎯 存储结果时包含盈利能力指标
                result_entry = {
                    'threshold': threshold,
                    'f1': f1,
                    'precision': precision,
                    'recall': recall,
                    'accuracy': accuracy,
                    'specificity': specificity,
                    'score': score
                }

                # 如果使用盈利相关方法，添加盈利指标
                if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                    result_entry.update(profit_metrics)

                threshold_results.append(result_entry)

                # 更新最佳结果
                if score > best_score:
                    best_score = score
                    best_threshold = threshold
                    best_metrics = {
                        'f1_score': f1,
                        'precision': precision,
                        'recall': recall,
                        'accuracy': accuracy,
                        'specificity': specificity
                    }

                    # 🎯 如果使用盈利相关方法，添加盈利指标到最佳结果
                    if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                        best_metrics.update(profit_metrics)

            except Exception as e_metric:
                logger.warning(f"find_optimal_threshold [{target_name}]: 计算阈值{threshold:.3f}的指标时出错: {e_metric}")
                continue

        # 🎯 硬约束机制：处理完全没有满足约束的情况
        if precision_constraint_value is not None and valid_thresholds_count == 0:
            logger.warning(f"find_optimal_threshold [{target_name}]: 没有任何阈值满足精确率约束 >= {precision_constraint_value:.3f}")

            # 降级策略：在所有阈值中，选择一个F1分数最高的，即使它不满足精确率要求
            # 这比选择一个"最接近"但可能F1很低的阈值更有参考意义
            if threshold_results:
                best_fallback_result = max(threshold_results, key=lambda x: x['f1'])
                best_threshold = best_fallback_result['threshold']
                best_metrics = {
                    'f1_score': best_fallback_result['f1'],
                    'precision': best_fallback_result['precision'],
                    'recall': best_fallback_result['recall'],
                    'accuracy': best_fallback_result['accuracy'],
                    'specificity': best_fallback_result['specificity']
                }
                logger.warning(f"find_optimal_threshold [{target_name}]: 降级策略 - 选择F1分数最高的阈值 {best_threshold:.4f} "
                             f"(P:{best_metrics['precision']:.4f}, R:{best_metrics['recall']:.4f}, F1:{best_metrics['f1_score']:.4f})")
            else:
                # 极端情况：没有任何有效结果，使用默认值
                logger.error(f"find_optimal_threshold [{target_name}]: 没有任何有效的阈值结果，使用默认阈值 0.5")
                best_threshold = 0.5
                best_metrics = {
                    'f1_score': 0.0,
                    'precision': 0.0,
                    'recall': 0.0,
                    'accuracy': 0.0,
                    'specificity': 0.0
                }

        # 构建返回结果
        result = {
            'optimal_threshold': best_threshold,
            'f1_score': best_metrics.get('f1_score', 0),
            'precision': best_metrics.get('precision', 0),
            'recall': best_metrics.get('recall', 0),
            'accuracy': best_metrics.get('accuracy', 0),
            'specificity': best_metrics.get('specificity', 0),
            'method': method,
            'threshold_range': (thresholds.min(), thresholds.max()),
            'n_samples': len(y_true),
            'positive_ratio': np.mean(y_true)
        }

        # 🎯 如果使用盈利相关方法，添加盈利指标到返回结果
        if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
            result.update({
                'expected_profit_per_trade': best_metrics.get('expected_profit_per_trade', 0.0),
                'total_trades': best_metrics.get('total_trades', 0),
                'win_rate': best_metrics.get('win_rate', 0.0),
                'total_profit': best_metrics.get('total_profit', 0.0),
                'risk_adjusted_return': best_metrics.get('risk_adjusted_return', 0.0),
                'trade_frequency': best_metrics.get('trade_frequency', 0.0)
            })

        # 添加精确率约束相关信息
        if precision_constraint_value is not None:
            result['precision_constraint'] = precision_constraint_value
            result['valid_thresholds_count'] = valid_thresholds_count
            result['constraint_satisfied'] = best_metrics.get('precision', 0) >= precision_constraint_value

        if verbose:
            logger.info(f"find_optimal_threshold [{target_name}]: 最优阈值 = {best_threshold:.4f}")
            logger.info(f"find_optimal_threshold [{target_name}]: F1={best_metrics.get('f1_score', 0):.4f}, "
                       f"Precision={best_metrics.get('precision', 0):.4f}, "
                       f"Recall={best_metrics.get('recall', 0):.4f}, "
                       f"Accuracy={best_metrics.get('accuracy', 0):.4f}")

            # 🎯 如果使用盈利相关方法，显示盈利指标
            if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                logger.info(f"find_optimal_threshold [{target_name}]: 🎯 盈利指标 - "
                           f"期望收益/交易={best_metrics.get('expected_profit_per_trade', 0):.4f}, "
                           f"总交易={best_metrics.get('total_trades', 0)}, "
                           f"胜率={best_metrics.get('win_rate', 0):.3f}, "
                           f"风险调整收益={best_metrics.get('risk_adjusted_return', 0):.4f}")

            if precision_constraint_value is not None:
                constraint_status = "[OK] 满足" if result.get('constraint_satisfied', False) else "[FAIL] 不满足"
                logger.info(f"find_optimal_threshold [{target_name}]: 精确率约束 >= {precision_constraint_value:.3f}: {constraint_status}")
                logger.info(f"find_optimal_threshold [{target_name}]: 满足约束的阈值数量: {valid_thresholds_count}/{len(thresholds)}")

            # 🎯 如果使用盈利相关方法，显示最小交易次数约束
            if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                trades_constraint_status = "[OK] 满足" if best_metrics.get('total_trades', 0) >= min_trades else "[FAIL] 不满足"
                logger.info(f"find_optimal_threshold [{target_name}]: 最小交易次数约束 >= {min_trades}: {trades_constraint_status}")

        # 可选：保存详细的阈值分析结果
        if verbose and len(threshold_results) > 0:
            if precision_constraint_value is not None:
                # 对于有精确率约束的情况，优先显示满足约束的最佳阈值
                valid_results = [res for res in threshold_results if res['precision'] >= precision_constraint_value]
                if valid_results:
                    if method == "precision_constrained_recall":
                        sorted_results = sorted(valid_results, key=lambda x: x['recall'], reverse=True)[:5]
                        logger.info(f"find_optimal_threshold [{target_name}]: 满足精确率约束的前5个最佳阈值 (按召回率排序):")
                    else:
                        sorted_results = sorted(valid_results, key=lambda x: x['f1'], reverse=True)[:5]
                        logger.info(f"find_optimal_threshold [{target_name}]: 满足精确率约束的前5个最佳阈值 (按F1分数排序):")
                else:
                    sorted_results = sorted(threshold_results, key=lambda x: x['f1'], reverse=True)[:5]
                    logger.info(f"find_optimal_threshold [{target_name}]: 无阈值满足约束，显示F1分数最高的前5个阈值:")
            else:
                # 无精确率约束时，显示评分最高的阈值
                sorted_results = sorted(threshold_results, key=lambda x: x['score'], reverse=True)[:5]
                logger.info(f"find_optimal_threshold [{target_name}]: 前5个最佳阈值:")

            for i, res in enumerate(sorted_results, 1):
                constraint_status = ""
                if precision_constraint_value is not None:
                    constraint_status = " ✓" if res['precision'] >= precision_constraint_value else " ✗"
                logger.info(f"  {i}. 阈值={res['threshold']:.4f}, "
                           f"F1={res['f1']:.4f}, "
                           f"Precision={res['precision']:.4f}, "
                           f"Recall={res['recall']:.4f}{constraint_status}")

        return result

    except Exception as e:
        logger.error(f"find_optimal_threshold [{target_name}]: 寻找最优阈值时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return None


def save_threshold_to_model_metadata(model_path, optimal_threshold, target_name=""):
    """
    将最优阈值保存到模型元数据中

    Args:
        model_path (str): 模型文件路径
        optimal_threshold (float): 最优阈值
        target_name (str): 目标名称

    Returns:
        bool: 是否保存成功
    """
    try:
        if not os.path.exists(model_path):
            logger.error(f"save_threshold_to_model_metadata [{target_name}]: 模型文件不存在: {model_path}")
            return False

        # 构建元数据文件路径
        model_dir = os.path.dirname(model_path)
        model_filename = os.path.basename(model_path)
        model_name_without_ext = os.path.splitext(model_filename)[0]
        metadata_path = os.path.join(model_dir, f"{model_name_without_ext}_metadata.json")

        # 读取现有元数据或创建新的
        metadata = {}
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception as e_read:
                logger.warning(f"save_threshold_to_model_metadata [{target_name}]: 读取现有元数据失败: {e_read}")
                metadata = {}

        # 更新阈值信息
        metadata['optimal_decision_threshold'] = optimal_threshold
        metadata['threshold_updated_at'] = datetime.now().isoformat()
        metadata['target_name'] = target_name

        # 保存元数据
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        logger.info(f"save_threshold_to_model_metadata [{target_name}]: 已保存最优阈值 {optimal_threshold:.4f} 到 {metadata_path}")
        return True

    except Exception as e:
        logger.error(f"save_threshold_to_model_metadata [{target_name}]: 保存阈值元数据时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return False


def split_independent_validation_set(X, y, val_ratio=0.15, random_state=42):
    """
    从训练数据中分离出独立的验证集用于阈值优化

    Args:
        X (np.ndarray): 特征数据
        y (np.ndarray): 标签数据
        val_ratio (float): 验证集比例
        random_state (int): 随机种子

    Returns:
        tuple: (X_train, X_val_threshold, y_train, y_val_threshold)
    """
    try:
        from sklearn.model_selection import train_test_split

        if len(X) < 10:  # 数据太少，不分离
            logger.warning(f"split_independent_validation_set: 数据量太少 ({len(X)})，不分离独立验证集")
            return X, None, y, None

        # 确保验证集至少有5个样本
        min_val_samples = max(5, int(len(X) * 0.05))
        val_samples = int(len(X) * val_ratio)
        val_samples = max(min_val_samples, val_samples)

        if val_samples >= len(X) * 0.3:  # 验证集不能超过30%
            val_samples = int(len(X) * 0.2)

        actual_val_ratio = val_samples / len(X)

        X_train, X_val_threshold, y_train, y_val_threshold = train_test_split(
            X, y,
            test_size=actual_val_ratio,
            random_state=random_state,
            stratify=y if len(np.unique(y)) > 1 else None
        )

        logger.info(f"split_independent_validation_set: 分离独立验证集 - 训练集: {len(X_train)}, 阈值验证集: {len(X_val_threshold)}")
        return X_train, X_val_threshold, y_train, y_val_threshold

    except Exception as e:
        logger.error(f"split_independent_validation_set: 分离独立验证集失败: {e}")
        return X, None, y, None


def optimize_threshold_with_independent_validation(models, scaler, X_val_threshold, y_val_threshold,
                                                 feature_names, target_name, method="f1", verbose=True):
    """
    使用独立验证集进行阈值优化

    Args:
        models (list): 训练好的模型列表（单模型时为[model]，多折时为[model1, model2, ...]）
        scaler: 数据缩放器
        X_val_threshold (np.ndarray): 独立验证集特征
        y_val_threshold (np.ndarray): 独立验证集标签
        feature_names (list): 特征名称列表
        target_name (str): 目标名称
        method (str): 优化方法
        verbose (bool): 是否显示详细信息

    Returns:
        dict: 阈值优化结果
    """
    try:
        if X_val_threshold is None or y_val_threshold is None or len(X_val_threshold) == 0:
            logger.warning(f"optimize_threshold_with_independent_validation [{target_name}]: 无独立验证集，跳过阈值优化")
            return None

        if len(models) == 0:
            logger.error(f"optimize_threshold_with_independent_validation [{target_name}]: 模型列表为空")
            return None

        # 缩放验证集数据
        X_val_scaled = scaler.transform(X_val_threshold)
        X_val_df = pd.DataFrame(X_val_scaled, columns=feature_names)

        # 获取集成预测概率
        if len(models) == 1:
            # 单模型
            y_proba_val = models[0].predict_proba(X_val_df)[:, 1]
            model_type = "单模型"
        else:
            # 多模型集成
            all_probas = []
            for i, model in enumerate(models):
                if model is not None:
                    proba = model.predict_proba(X_val_df)[:, 1]
                    all_probas.append(proba)
                else:
                    logger.warning(f"optimize_threshold_with_independent_validation [{target_name}]: 模型 {i} 为None，跳过")

            if len(all_probas) == 0:
                logger.error(f"optimize_threshold_with_independent_validation [{target_name}]: 没有有效的模型")
                return None

            # 平均集成
            y_proba_val = np.mean(all_probas, axis=0)
            model_type = f"{len(all_probas)}折集成"

        if verbose:
            print(f"    使用独立验证集进行阈值优化 ({model_type})")
            print(f"    验证集大小: {len(X_val_threshold)}, 正类比例: {np.mean(y_val_threshold):.3f}")

        # 使用独立验证集进行阈值优化
        threshold_result = find_optimal_threshold(
            y_val_threshold,
            y_proba_val,
            target_name=target_name + "_independent_val",
            method=method,
            verbose=verbose
        )

        if threshold_result and verbose:
            print(f"    独立验证集阈值优化完成: {threshold_result['optimal_threshold']:.4f}")

        return threshold_result

    except Exception as e:
        logger.error(f"optimize_threshold_with_independent_validation [{target_name}]: 阈值优化失败: {e}")
        logger.debug(traceback.format_exc())
        return None


def save_ensemble_threshold_to_main_metadata(main_meta_path, optimal_threshold_ensemble, target_name=""):
    """
    将集成模型最优阈值保存到主模型元数据文件中

    Args:
        main_meta_path (str): 主模型元数据文件路径
        optimal_threshold_ensemble (float): 集成模型最优阈值
        target_name (str): 目标名称

    Returns:
        bool: 是否保存成功
    """
    try:
        # 读取现有主元数据或创建新的
        metadata = {}
        if os.path.exists(main_meta_path):
            try:
                with open(main_meta_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception as e_read:
                logger.warning(f"save_ensemble_threshold_to_main_metadata [{target_name}]: 读取现有主元数据失败: {e_read}")
                metadata = {}

        # 更新集成模型阈值信息
        metadata['optimal_decision_threshold_ensemble'] = optimal_threshold_ensemble
        metadata['ensemble_threshold_updated_at'] = datetime.now().isoformat()
        metadata['ensemble_target_name'] = target_name

        # 保存主元数据
        with open(main_meta_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        logger.info(f"save_ensemble_threshold_to_main_metadata [{target_name}]: 已保存集成模型最优阈值 {optimal_threshold_ensemble:.4f} 到 {main_meta_path}")
        return True

    except Exception as e:
        logger.error(f"save_ensemble_threshold_to_main_metadata [{target_name}]: 保存集成阈值到主元数据时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return False


def load_ensemble_threshold_from_main_metadata(main_meta_path, target_name="", default_threshold=0.5):
    """
    从主模型元数据文件中加载集成模型最优阈值

    Args:
        main_meta_path (str): 主模型元数据文件路径
        target_name (str): 目标名称
        default_threshold (float): 默认阈值

    Returns:
        float: 加载的集成阈值或默认阈值
    """
    try:
        if not os.path.exists(main_meta_path):
            logger.warning(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 主元数据文件不存在: {main_meta_path}")
            return default_threshold

        # 读取主元数据
        with open(main_meta_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # 获取集成模型阈值
        ensemble_threshold = metadata.get('optimal_decision_threshold_ensemble', default_threshold)

        if isinstance(ensemble_threshold, (int, float)) and 0 <= ensemble_threshold <= 1:
            logger.info(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 成功加载集成模型最优阈值 {ensemble_threshold:.4f}")
            return float(ensemble_threshold)
        else:
            logger.warning(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 无效的集成阈值 {ensemble_threshold}，使用默认值 {default_threshold}")
            return default_threshold

    except Exception as e:
        logger.error(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 加载集成阈值时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return default_threshold


def load_threshold_from_model_metadata(model_path, target_name="", default_threshold=0.5):
    """
    从模型元数据中加载最优阈值

    Args:
        model_path (str): 模型文件路径
        target_name (str): 目标名称
        default_threshold (float): 默认阈值

    Returns:
        float: 加载的阈值或默认阈值
    """
    try:
        if not os.path.exists(model_path):
            logger.warning(f"load_threshold_from_model_metadata [{target_name}]: 模型文件不存在: {model_path}")
            return default_threshold

        # 构建元数据文件路径
        model_dir = os.path.dirname(model_path)
        model_filename = os.path.basename(model_path)
        model_name_without_ext = os.path.splitext(model_filename)[0]
        metadata_path = os.path.join(model_dir, f"{model_name_without_ext}_metadata.json")

        if not os.path.exists(metadata_path):
            logger.info(f"load_threshold_from_model_metadata [{target_name}]: 元数据文件不存在，使用默认阈值 {default_threshold}")
            return default_threshold

        # 读取元数据
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # 获取阈值
        threshold = metadata.get('optimal_decision_threshold', default_threshold)

        if isinstance(threshold, (int, float)) and 0 <= threshold <= 1:
            logger.info(f"load_threshold_from_model_metadata [{target_name}]: 成功加载最优阈值 {threshold:.4f}")
            return float(threshold)
        else:
            logger.warning(f"load_threshold_from_model_metadata [{target_name}]: 无效的阈值 {threshold}，使用默认值 {default_threshold}")
            return default_threshold

    except Exception as e:
        logger.error(f"load_threshold_from_model_metadata [{target_name}]: 加载阈值元数据时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return default_threshold


def predict_with_optimal_threshold(model, X, optimal_threshold=0.5):
    """
    使用最优阈值进行预测

    Args:
        model: 训练好的模型
        X: 特征数据
        optimal_threshold (float): 最优阈值

    Returns:
        np.ndarray: 预测结果 (0或1)
    """
    try:
        # 获取预测概率
        y_proba = model.predict_proba(X)[:, 1]

        # 使用最优阈值进行预测
        y_pred = (y_proba >= optimal_threshold).astype(int)

        return y_pred

    except Exception as e:
        logger.error(f"predict_with_optimal_threshold: 使用最优阈值预测时发生错误: {e}")
        # 回退到默认预测
        return model.predict(X)


def evaluate_model_with_optimal_threshold(model, X_test, y_test, optimal_threshold=0.5, target_name="", verbose=True):
    """
    使用最优阈值评估模型性能

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        optimal_threshold (float): 最优阈值
        target_name (str): 目标名称
        verbose (bool): 是否输出详细信息

    Returns:
        dict: 评估结果字典
    """
    try:
        from sklearn.metrics import (accuracy_score, classification_report,
                                   brier_score_loss, f1_score, precision_score, recall_score)

        # 使用最优阈值进行预测
        y_pred_optimal = predict_with_optimal_threshold(model, X_test, optimal_threshold)

        # 获取预测概率
        y_proba = model.predict_proba(X_test)[:, 1]

        # 计算评估指标
        results = {
            'optimal_threshold_used': optimal_threshold,
            'test_accuracy_optimal': accuracy_score(y_test, y_pred_optimal),
            'test_brier_optimal': brier_score_loss(y_test, y_proba),
            'test_f1_optimal': f1_score(y_test, y_pred_optimal),
            'test_precision_optimal': precision_score(y_test, y_pred_optimal, zero_division=0),
            'test_recall_optimal': recall_score(y_test, y_pred_optimal, zero_division=0),
            'test_classification_report_optimal': classification_report(
                y_test, y_pred_optimal, output_dict=True, zero_division=0,
                target_names=['下跌 (0)', '上涨 (1)']
            )
        }

        if verbose:
            print(f"    📊 使用最优阈值 {optimal_threshold:.4f} 的测试集性能:")
            print(f"       准确率: {results['test_accuracy_optimal']:.4f}")
            print(f"       F1分数: {results['test_f1_optimal']:.4f}")
            print(f"       精确率: {results['test_precision_optimal']:.4f}")
            print(f"       召回率: {results['test_recall_optimal']:.4f}")
            print(f"       Brier分数: {results['test_brier_optimal']:.4f}")

        return results

    except Exception as e:
        logger.error(f"evaluate_model_with_optimal_threshold [{target_name}]: 评估时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return {}


def _calculate_binary_simulated_profit(y_true, y_proba, threshold, payout_ratio=0.85):
    """
    🎯 简化的二分类模拟交易盈利计算函数

    从 calculate_simulated_profit_meta 中提取核心逻辑，适配二分类场景

    Args:
        y_true (array-like): 真实标签 (0或1)
        y_proba (array-like): 正类的预测概率
        threshold (float): 决策阈值
        payout_ratio (float): 盈亏比 (胜利时的收益率)

    Returns:
        dict: 包含盈利指标的字典
    """
    try:
        y_true = np.array(y_true)
        y_proba = np.array(y_proba)

        if len(y_true) == 0 or len(y_proba) == 0:
            return {
                'expected_profit_per_trade': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'total_profit': 0.0,
                'risk_adjusted_return': 0.0,
                'trade_frequency': 0.0
            }

        # 生成预测信号 (概率 >= 阈值时做多)
        y_pred_signal = (y_proba >= threshold).astype(int)

        # 计算交易统计
        total_trades = np.sum(y_pred_signal)  # 做多信号的次数
        total_wins = 0
        total_profit = 0.0

        if total_trades > 0:
            # 计算每笔交易的盈亏
            for i in range(len(y_true)):
                if y_pred_signal[i] == 1:  # 有做多信号
                    if y_true[i] == 1:  # 预测正确 (实际上涨)
                        total_profit += payout_ratio  # 盈利
                        total_wins += 1
                    else:  # 预测错误 (实际下跌)
                        total_profit -= 1.0  # 亏损全部本金

        # 计算指标
        win_rate = total_wins / total_trades if total_trades > 0 else 0.0
        expected_profit_per_trade = total_profit / total_trades if total_trades > 0 else 0.0
        trade_frequency = total_trades / len(y_true) if len(y_true) > 0 else 0.0

        # 风险调整收益 (考虑交易频率)
        risk_adjusted_return = expected_profit_per_trade * np.sqrt(total_trades) if total_trades > 0 else 0.0

        return {
            'expected_profit_per_trade': expected_profit_per_trade,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'risk_adjusted_return': risk_adjusted_return,
            'trade_frequency': trade_frequency
        }

    except Exception as e:
        logger.error(f"_calculate_binary_simulated_profit: 计算盈利指标时出错: {e}")
        return {
            'expected_profit_per_trade': 0.0,
            'total_trades': 0,
            'win_rate': 0.0,
            'total_profit': 0.0,
            'risk_adjusted_return': 0.0,
            'trade_frequency': 0.0
        }


def enhanced_feature_selection_with_shap(X_df, y_array, target_config, target_name, device_type="cpu", verbose=True, force_include_features=None):
    """
    🚀 增强的特征选择：SHAP阈值过滤 + LightGBM重要性 + RFE精选

    新增第0阶段：使用SHAP重要性绝对阈值进行精准过滤
    第一阶段：使用LightGBM特征重要性进行初步筛选
    第二阶段：使用RFE递归特征消除进行精细选择

    Args:
        X_df (pd.DataFrame): 特征数据
        y_array (np.ndarray): 目标变量
        target_config (dict): 目标配置
        target_name (str): 目标名称
        device_type (str): 设备类型
        verbose (bool): 是否显示详细信息
        force_include_features (list): 强制保留的特征列表

    Returns:
        tuple: (selected_features, selection_stats)
            - selected_features: 选择的特征列表
            - selection_stats: 选择统计信息
    """
    if verbose:
        logger.info(f"enhanced_feature_selection_with_shap [{target_name}]: 开始增强特征选择")
        logger.info(f"enhanced_feature_selection_with_shap [{target_name}]: 输入特征数={len(X_df.columns)}, 样本数={len(y_array)}")

    selection_stats = {
        'original_features': len(X_df.columns),
        'method': 'enhanced_shap_lgb_rfe',
        'target_name': target_name
    }

    try:
        import time
        total_start = time.time()

        # === 第0阶段：SHAP阈值过滤 ===
        enable_shap_filtering = target_config.get('enable_shap_feature_filtering', True)
        shap_threshold = target_config.get('shap_importance_threshold', 0.001)

        current_features = list(X_df.columns)
        X_current = X_df.copy()

        if enable_shap_filtering and len(current_features) > 50:  # 只在特征较多时使用
            if verbose:
                print(f"    🎯 第0阶段: SHAP重要性阈值过滤 (阈值={shap_threshold})...")

            stage0_start = time.time()

            try:
                # 训练快速模型用于SHAP分析
                from lightgbm import LGBMClassifier

                # 快速模型配置
                quick_model = LGBMClassifier(
                    objective='multiclass' if len(np.unique(y_array)) > 2 else 'binary',
                    num_class=len(np.unique(y_array)) if len(np.unique(y_array)) > 2 else None,
                    n_estimators=50,  # 快速训练
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42,
                    verbose=-1,
                    device_type=device_type
                )

                # 训练模型
                quick_model.fit(X_current, y_array)

                # 计算SHAP重要性
                try:
                    import shap
                    explainer = shap.TreeExplainer(quick_model)
                    shap_values = explainer.shap_values(X_current.sample(min(1000, len(X_current)), random_state=42))

                    # 计算平均绝对SHAP值
                    if isinstance(shap_values, list):
                        mean_abs_shap = np.mean([np.abs(sv).mean(axis=0) for sv in shap_values], axis=0)
                    else:
                        mean_abs_shap = np.abs(shap_values).mean(axis=0)

                    # 创建特征重要性字典
                    feature_importance_shap = dict(zip(current_features, mean_abs_shap))

                    # 应用阈值过滤（保护强制保留特征）
                    shap_filtered_features = []
                    for feature, importance in feature_importance_shap.items():
                        if importance >= shap_threshold or (force_include_features and feature in force_include_features):
                            shap_filtered_features.append(feature)

                    if shap_filtered_features:
                        current_features = shap_filtered_features
                        X_current = X_df[current_features]

                        stage0_end = time.time()
                        selection_stats['stage0_method'] = 'shap_threshold'
                        selection_stats['stage0_features'] = len(current_features)
                        selection_stats['stage0_time'] = stage0_end - stage0_start
                        selection_stats['shap_threshold'] = shap_threshold

                        if verbose:
                            print(f"      ✅ SHAP过滤完成: {len(X_df.columns)} → {len(current_features)} 特征")
                            print(f"      SHAP重要性范围: {min(feature_importance_shap.values()):.6f} - {max(feature_importance_shap.values()):.6f}")
                            print(f"      耗时: {stage0_end - stage0_start:.2f}秒")
                    else:
                        if verbose:
                            print(f"      ⚠️ SHAP过滤后无特征保留，跳过此阶段")
                        selection_stats['stage0_method'] = 'shap_skipped'
                        selection_stats['stage0_features'] = len(current_features)

                except ImportError:
                    if verbose:
                        print(f"      ⚠️ SHAP库不可用，跳过SHAP过滤")
                    selection_stats['stage0_method'] = 'shap_unavailable'
                    selection_stats['stage0_features'] = len(current_features)
                except Exception as e:
                    if verbose:
                        print(f"      ⚠️ SHAP过滤失败: {e}")
                    selection_stats['stage0_method'] = 'shap_failed'
                    selection_stats['stage0_features'] = len(current_features)

            except Exception as e:
                if verbose:
                    print(f"      ⚠️ SHAP阶段整体失败: {e}")
                selection_stats['stage0_method'] = 'failed'
                selection_stats['stage0_features'] = len(current_features)
        else:
            if verbose and enable_shap_filtering:
                print(f"    ⏭️ 跳过SHAP过滤 (特征数={len(current_features)} <= 50)")
            selection_stats['stage0_method'] = 'skipped'
            selection_stats['stage0_features'] = len(current_features)

        # 继续使用传统的两阶段选择
        # 调用原有的two_stage_feature_selection，但使用过滤后的特征
        selected_features, stage_stats = two_stage_feature_selection(X_current, y_array, target_config, target_name, device_type, verbose, force_include_features)

        # 合并统计信息
        if stage_stats:
            selection_stats.update(stage_stats)

        total_end = time.time()
        selection_stats['total_time'] = total_end - total_start
        selection_stats['final_features'] = len(selected_features)

        if verbose:
            print(f"    🎉 增强特征选择完成: {len(X_df.columns)} → {len(selected_features)} 特征")
            print(f"    总耗时: {selection_stats['total_time']:.2f}秒")

        return selected_features, selection_stats

    except Exception as e:
        logger.error(f"enhanced_feature_selection_with_shap [{target_name}]: 增强特征选择失败: {e}")
        if verbose:
            print(f"    ❌ 增强特征选择失败: {e}")

        # 回退到传统方法
        return two_stage_feature_selection(X_df, y_array, target_config, target_name, device_type, verbose, force_include_features)

def _calculate_meta_label_weights(df_with_target, target_col, config, target_name_for_log="unknown"):
    """
    🚀 质量权重计算函数 - 基于交易路径质量的动态样本权重

    为标签"提纯"，区分"教科书级别的完美答案"和"勉强及格的答案"

    Args:
        df_with_target (pd.DataFrame): 包含目标变量的完整数据
        target_col (str): 目标变量列名
        config (dict): 目标配置
        target_name_for_log (str): 用于日志的目标名称

    Returns:
        pd.Series: 质量权重序列，索引与df_with_target对齐
    """
    try:
        logger.info(f"_calculate_meta_label_weights ({target_name_for_log}): 开始计算质量权重")

        # 获取质量权重配置
        from src.core.meta_label_quality_config import get_meta_label_quality_config
        quality_config = get_meta_label_quality_config(config)

        # 初始化权重序列
        weights = pd.Series(1.0, index=df_with_target.index)

        # 获取配置参数
        prediction_periods = config.get('prediction_periods', [2])
        prediction_period = prediction_periods[0] if prediction_periods else 2

        # 获取阈值配置
        if config.get('enable_triple_barrier', False):
            # 使用三道屏障配置
            if config.get('triple_barrier_use_fixed', False):
                profit_threshold = config.get('triple_barrier_fixed_profit', 0.02)
                loss_threshold = config.get('triple_barrier_fixed_loss', 0.015)
            else:
                # 使用动态阈值的默认值
                profit_threshold = quality_config['fallback_thresholds']['default_profit_threshold']
                loss_threshold = quality_config['fallback_thresholds']['default_loss_threshold']
        else:
            # 使用传统阈值或质量配置的默认值
            base_threshold = config.get('target_threshold', quality_config['fallback_thresholds']['default_profit_threshold'])
            profit_threshold = base_threshold
            loss_threshold = base_threshold

        # 获取价格数据
        close_prices = df_with_target['close']
        high_prices = df_with_target['high']
        low_prices = df_with_target['low']

        # 统计计数器
        processed_count = 0
        high_quality_count = 0

        # 遍历每个样本计算质量权重
        for i in range(len(df_with_target) - prediction_period):
            label = df_with_target[target_col].iloc[i]

            # 只对非中性标签进行质量评估
            if label == 1 or label == 0:
                entry_price = close_prices.iloc[i]

                # 获取未来价格路径
                future_end_idx = min(i + prediction_period + 1, len(df_with_target))
                future_path_high = high_prices.iloc[i+1:future_end_idx]
                future_path_low = low_prices.iloc[i+1:future_end_idx]
                future_path_close = close_prices.iloc[i+1:future_end_idx]

                if len(future_path_high) == 0:
                    continue

                # 计算目标价格
                profit_target = entry_price * (1 + profit_threshold)
                loss_target = entry_price * (1 - loss_threshold)

                # 1. 计算盈利/亏损效率 (越快达到目标越好)
                time_to_target = prediction_period
                target_reached = False

                if label == 1:  # 上涨信号
                    for j in range(len(future_path_high)):
                        if future_path_high.iloc[j] >= profit_target:
                            time_to_target = j + 1
                            target_reached = True
                            break
                elif label == 0:  # 下跌信号
                    for j in range(len(future_path_low)):
                        if future_path_low.iloc[j] <= loss_target:
                            time_to_target = j + 1
                            target_reached = True
                            break

                # 效率权重：越快达到目标，权重越高
                eff_config = quality_config['efficiency_weight_config']

                if target_reached:
                    efficiency_weight = 1.0 + (1.0 - (time_to_target / prediction_period)) * eff_config['max_efficiency_multiplier']
                else:
                    # 未达到目标，检查最终价格方向是否正确
                    final_price = future_path_close.iloc[-1] if len(future_path_close) > 0 else entry_price
                    if label == 1 and final_price > entry_price:
                        efficiency_weight = 1.0 + (final_price - entry_price) / entry_price * eff_config['final_price_weight_multiplier']
                    elif label == 0 and final_price < entry_price:
                        efficiency_weight = 1.0 + (entry_price - final_price) / entry_price * eff_config['final_price_weight_multiplier']
                    else:
                        efficiency_weight = eff_config['direction_penalty']  # 方向错误，降低权重

                efficiency_weight = max(eff_config['min_efficiency_weight'],
                                      min(eff_config['max_efficiency_weight'], efficiency_weight))

                # 2. 计算路径平滑度 (回撤越小越好)
                smooth_config = quality_config['smoothness_weight_config']
                smoothness_weight = 1.0

                if label == 1:  # 上涨信号 - 计算下行回撤
                    peak_price = entry_price
                    max_drawdown = 0.0

                    for j in range(len(future_path_high)):
                        current_high = future_path_high.iloc[j]
                        current_low = future_path_low.iloc[j]

                        # 更新峰值
                        peak_price = max(peak_price, current_high)

                        # 计算从峰值的回撤
                        if peak_price > entry_price:
                            drawdown = (peak_price - current_low) / peak_price
                            max_drawdown = max(max_drawdown, drawdown)

                    # 回撤越小，权重越高
                    smoothness_weight = 1.0 + max(0, (smooth_config['drawdown_threshold'] - max_drawdown) * smooth_config['smoothness_multiplier'])

                elif label == 0:  # 下跌信号 - 计算上行回撤
                    trough_price = entry_price
                    max_retracement = 0.0

                    for j in range(len(future_path_low)):
                        current_high = future_path_high.iloc[j]
                        current_low = future_path_low.iloc[j]

                        # 更新谷底
                        trough_price = min(trough_price, current_low)

                        # 计算从谷底的反弹
                        if trough_price < entry_price:
                            retracement = (current_high - trough_price) / entry_price
                            max_retracement = max(max_retracement, retracement)

                    # 反弹越小，权重越高
                    smoothness_weight = 1.0 + max(0, (smooth_config['drawdown_threshold'] - max_retracement) * smooth_config['smoothness_multiplier'])

                smoothness_weight = max(smooth_config['min_smoothness_weight'],
                                      min(smooth_config['max_smoothness_weight'], smoothness_weight))

                # 3. 最终质量权重 = 效率权重 * 平滑度权重
                final_config = quality_config['final_weight_limits']
                final_weight = efficiency_weight * smoothness_weight
                final_weight = max(final_config['min_final_weight'],
                                 min(final_config['max_final_weight'], final_weight))

                weights.iloc[i] = final_weight
                processed_count += 1

                # 统计高质量样本
                if final_weight > final_config['high_quality_threshold']:
                    high_quality_count += 1

        # 归一化权重，使平均值为1.0
        if processed_count > 0:
            mean_weight = weights.mean()
            if mean_weight > 0:
                weights = weights / mean_weight

        # 详细日志输出
        log_config = quality_config['logging_config']
        if log_config['enable_detailed_logging']:
            logger.info(f"_calculate_meta_label_weights ({target_name_for_log}): 质量权重计算完成")

            if log_config['log_sample_statistics']:
                logger.info(f"  - 处理样本数: {processed_count}")
                logger.info(f"  - 高质量样本数: {high_quality_count} ({high_quality_count/max(1,processed_count)*100:.1f}%)")

            if log_config['log_weight_distribution']:
                logger.info(f"  - 权重范围: [{weights.min():.3f}, {weights.max():.3f}]")
                logger.info(f"  - 权重均值: {weights.mean():.3f}")
                logger.info(f"  - 权重标准差: {weights.std():.3f}")

        return weights

    except Exception as e:
        logger.error(f"_calculate_meta_label_weights ({target_name_for_log}): 质量权重计算失败: {e}")
        logger.debug(traceback.format_exc())
        # 返回均匀权重作为后备方案
        return pd.Series(1.0, index=df_with_target.index)

def calculate_training_sample_weights(X_df, target_config, target_name, df_with_target=None, target_col=None):
    """
    计算训练样本权重 - 增强版，支持质量权重

    Args:
        X_df (pd.DataFrame): 特征数据
        target_config (dict): 目标配置
        target_name (str): 目标名称
        df_with_target (pd.DataFrame, optional): 包含目标变量和价格数据的完整DataFrame
        target_col (str, optional): 目标变量列名

    Returns:
        np.ndarray: 样本权重数组，如果禁用则返回None
    """
    try:
        # 检查是否启用任何形式的样本加权
        enable_dynamic = target_config.get('enable_dynamic_sample_weighting', False)
        enable_quality = target_config.get('enable_meta_label_quality_weighting', False)

        if not enable_dynamic and not enable_quality:
            return None

        logger.info(f"calculate_training_sample_weights [{target_name}]: 开始计算样本权重")

        # 初始化权重
        final_weights = None

        # 1. 计算传统动态样本权重
        if enable_dynamic:
            try:
                from src.core.dynamic_sample_weighting import calculate_dynamic_sample_weights
                dynamic_weights = calculate_dynamic_sample_weights(X_df, target_config, target_name)

                if dynamic_weights is not None:
                    final_weights = dynamic_weights
                    logger.info(f"calculate_training_sample_weights [{target_name}]: 动态样本权重计算完成，"
                               f"范围: [{dynamic_weights.min():.4f}, {dynamic_weights.max():.4f}]")

            except ImportError:
                logger.warning(f"calculate_training_sample_weights [{target_name}]: 动态样本加权模块不可用")
            except Exception as e:
                logger.warning(f"calculate_training_sample_weights [{target_name}]: 动态样本权重计算失败: {e}")

        # 2. 计算质量权重
        if enable_quality and df_with_target is not None and target_col is not None:
            try:
                quality_weights = _calculate_meta_label_weights(df_with_target, target_col, target_config, target_name)

                if quality_weights is not None:
                    # 确保索引对齐
                    if final_weights is not None:
                        # 将质量权重转换为与X_df索引对齐的数组
                        aligned_quality_weights = quality_weights.reindex(X_df.index, fill_value=1.0).values
                        # 组合动态权重和质量权重
                        final_weights = final_weights * aligned_quality_weights
                        logger.info(f"calculate_training_sample_weights [{target_name}]: 质量权重已与动态权重组合")
                    else:
                        # 只使用质量权重
                        final_weights = quality_weights.reindex(X_df.index, fill_value=1.0).values
                        logger.info(f"calculate_training_sample_weights [{target_name}]: 仅使用质量权重")

                    logger.info(f"calculate_training_sample_weights [{target_name}]: 质量权重计算完成，"
                               f"范围: [{quality_weights.min():.4f}, {quality_weights.max():.4f}]")

            except Exception as e:
                logger.warning(f"calculate_training_sample_weights [{target_name}]: 质量权重计算失败: {e}")

        # 3. 最终权重处理
        if final_weights is not None:
            # 确保权重为正数
            final_weights = np.maximum(final_weights, 0.01)

            # 归一化权重
            final_weights = final_weights / final_weights.mean()

            logger.info(f"calculate_training_sample_weights [{target_name}]: 最终样本权重计算完成，"
                       f"范围: [{final_weights.min():.4f}, {final_weights.max():.4f}], "
                       f"均值: {final_weights.mean():.4f}")

            return final_weights
        else:
            logger.info(f"calculate_training_sample_weights [{target_name}]: 未计算任何权重，返回None")
            return None

    except Exception as e:
        logger.error(f"calculate_training_sample_weights [{target_name}]: 样本权重计算失败: {e}")
        logger.debug(traceback.format_exc())
        return None

def find_baseline_optimal_threshold(X_df, y_array, target_config, target_name, device_type="cpu", verbose=True):
    """
    🚀 寻找基准模型的最优阈值，用于RFE特征选择

    在全特征集上训练基准模型，在独立验证集上寻找最优阈值，
    确保RFE评估的一致性和准确性。

    Args:
        X_df (pd.DataFrame): 特征数据
        y_array (np.ndarray): 目标变量
        target_config (dict): 目标配置
        target_name (str): 目标名称
        device_type (str): 设备类型
        verbose (bool): 是否显示详细信息

    Returns:
        dict: 包含最优阈值和相关信息的字典
            {
                'optimal_threshold': float,
                'baseline_score': float,
                'validation_size': int,
                'threshold_method': str
            }
    """
    try:
        from sklearn.model_selection import train_test_split
        from sklearn.preprocessing import StandardScaler
        from lightgbm import LGBMClassifier

        # 获取RFE阈值策略配置
        threshold_strategy = target_config.get('rfe_threshold_strategy', 'baseline_optimal')

        if threshold_strategy == 'fixed':
            # 使用固定阈值
            fixed_threshold = target_config.get('rfe_fixed_threshold', 0.5)
            if verbose:
                logger.info(f"find_baseline_optimal_threshold [{target_name}]: 使用固定阈值 {fixed_threshold}")
            return {
                'optimal_threshold': fixed_threshold,
                'baseline_score': 0.0,
                'validation_size': 0,
                'threshold_method': 'fixed'
            }

        # 获取配置参数
        validation_ratio = target_config.get('rfe_baseline_validation_ratio', 0.2)
        threshold_range = target_config.get('rfe_threshold_search_range', [0.1, 0.9])
        threshold_step = target_config.get('rfe_threshold_search_step', 0.05)
        min_validation_size = target_config.get('rfe_min_validation_size', 50)

        if verbose:
            logger.info(f"find_baseline_optimal_threshold [{target_name}]: 开始寻找基准最优阈值")
            logger.info(f"find_baseline_optimal_threshold [{target_name}]: 验证集比例={validation_ratio}, 阈值范围={threshold_range}")

        # 检查数据大小
        if len(y_array) < min_validation_size / validation_ratio:
            if verbose:
                logger.warning(f"find_baseline_optimal_threshold [{target_name}]: 数据量不足，回退到固定阈值")
            return {
                'optimal_threshold': 0.5,
                'baseline_score': 0.0,
                'validation_size': len(y_array),
                'threshold_method': 'fixed_fallback'
            }

        # 分割数据为训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X_df, y_array,
            test_size=validation_ratio,
            random_state=42,
            stratify=y_array
        )

        # 数据标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)

        # 基准模型参数（与RFE估计器保持一致）
        baseline_params = {
            'n_estimators': target_config.get('rfe_estimator_n_estimators', 100),
            'learning_rate': target_config.get('rfe_estimator_learning_rate', 0.1),
            'num_leaves': target_config.get('rfe_estimator_num_leaves', 31),
            'random_state': 42,
            'verbose': -1,
            'device_type': device_type
        }

        # 训练基准模型
        baseline_model = LGBMClassifier(**baseline_params)
        baseline_model.fit(X_train_scaled, y_train)

        # 获取验证集预测概率
        y_val_proba = baseline_model.predict_proba(X_val_scaled)[:, 1]

        # 寻找最优阈值
        rfe_scoring = target_config.get('rfe_scoring', 'f1')
        threshold_method = 'simulated_profit' if 'profit' in rfe_scoring else 'f1'

        optimal_result = find_optimal_threshold(
            y_true=y_val,
            y_proba=y_val_proba,
            target_name=target_name,
            method=threshold_method,
            verbose=False  # 避免过多日志
        )

        optimal_threshold = optimal_result['optimal_threshold']
        baseline_score = optimal_result.get('f1_score', optimal_result.get('expected_profit_per_trade', 0.0))

        if verbose:
            logger.info(f"find_baseline_optimal_threshold [{target_name}]: 找到最优阈值 {optimal_threshold:.3f}")
            logger.info(f"find_baseline_optimal_threshold [{target_name}]: 基准评分 {baseline_score:.4f}")
            logger.info(f"find_baseline_optimal_threshold [{target_name}]: 验证集大小 {len(y_val)}")

        return {
            'optimal_threshold': optimal_threshold,
            'baseline_score': baseline_score,
            'validation_size': len(y_val),
            'threshold_method': threshold_strategy
        }

    except Exception as e:
        logger.error(f"find_baseline_optimal_threshold [{target_name}]: 寻找基准阈值失败: {e}")
        if verbose:
            logger.warning(f"find_baseline_optimal_threshold [{target_name}]: 回退到固定阈值 0.5")

        return {
            'optimal_threshold': 0.5,
            'baseline_score': 0.0,
            'validation_size': len(y_array),
            'threshold_method': 'error_fallback'
        }





def two_stage_feature_selection(X_df, y_array, target_config, target_name, device_type="cpu", verbose=True, force_include_features=None):
    """
    🎯 两阶段特征选择：LightGBM重要性初筛 + RFE精选

    第一阶段：使用LightGBM特征重要性进行初步筛选，快速移除明显无用的特征
    第二阶段：使用RFE递归特征消除进行精细选择，找到最优特征子集

    Args:
        X_df (pd.DataFrame): 特征数据
        y_array (np.ndarray): 目标变量
        target_config (dict): 目标配置
        target_name (str): 目标名称
        device_type (str): 设备类型
        verbose (bool): 是否显示详细信息
        force_include_features (list): 强制保留的特征列表

    Returns:
        tuple: (selected_features, selection_stats)
            - selected_features: 选择的特征列表
            - selection_stats: 选择统计信息
    """
    if verbose:
        logger.info(f"two_stage_feature_selection [{target_name}]: 开始两阶段特征选择")
        logger.info(f"two_stage_feature_selection [{target_name}]: 输入特征数={len(X_df.columns)}, 样本数={len(y_array)}")

    # 🌍 处理强制保留特征
    if force_include_features is None:
        force_include_features = []

    # 过滤存在的强制保留特征
    valid_force_features = [f for f in force_include_features if f in X_df.columns]
    if valid_force_features and verbose:
        logger.info(f"two_stage_feature_selection [{target_name}]: 强制保留特征 {len(valid_force_features)} 个: {valid_force_features[:5]}...")
    elif force_include_features and verbose:
        missing_features = [f for f in force_include_features if f not in X_df.columns]
        logger.warning(f"two_stage_feature_selection [{target_name}]: 部分强制保留特征不存在: {missing_features[:3]}...")

    try:
        from sklearn.feature_selection import RFECV
        from sklearn.model_selection import StratifiedKFold
        from lightgbm import LGBMClassifier
        from sklearn.preprocessing import StandardScaler

        # 初始化统计信息
        selection_stats = {
            'stage1_method': 'lightgbm_importance',
            'stage2_method': 'rfe',
            'original_features': len(X_df.columns),
            'stage1_features': 0,
            'stage2_features': 0,
            'stage1_time': 0,
            'stage2_time': 0,
            'total_time': 0
        }

        import time
        start_time = time.time()

        # === 第一阶段：LightGBM重要性初筛 ===
        if verbose:
            print(f"    🔍 第一阶段: LightGBM重要性初筛...")

        stage1_start = time.time()

        # 获取重要性筛选配置
        prescreening_ratio = target_config.get('importance_prescreening_ratio', 0.6)
        importance_model_params = {
            'n_estimators': target_config.get('importance_model_n_estimators', 200),
            'learning_rate': target_config.get('learning_rate_initial_imp', 0.05),
            'num_leaves': target_config.get('num_leaves_initial_imp', 15),
            'max_depth': target_config.get('max_depth_initial_imp', 5),
            'reg_alpha': target_config.get('reg_alpha_initial_imp', 5.0),
            'reg_lambda': target_config.get('reg_lambda_initial_imp', 5.0),
            'colsample_bytree': target_config.get('colsample_bytree_initial_imp', 0.7),
            'subsample': target_config.get('subsample_initial_imp', 0.7),
            'min_child_samples': target_config.get('min_child_samples_initial_imp', 30),
            'random_state': 42,
            'verbose': -1,
            'device_type': device_type
        }

        # 数据预处理
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_df)
        X_scaled_df = pd.DataFrame(X_scaled, columns=X_df.columns, index=X_df.index)

        # 训练重要性评估模型
        importance_model = LGBMClassifier(**importance_model_params)
        importance_model.fit(X_scaled_df, y_array)

        # 获取特征重要性
        importances = importance_model.feature_importances_
        feature_importance_df = pd.DataFrame({
            'feature': X_df.columns,
            'importance': importances
        }).sort_values('importance', ascending=False)

        # 第一阶段筛选：保留前N%的特征 + 强制保留特征
        n_features_stage1 = max(
            int(len(X_df.columns) * prescreening_ratio),
            target_config.get('rfe_min_features_to_select', 40)  # 确保不少于RFE最小特征数
        )

        # 🌍 优先保留强制特征，然后按重要性选择其他特征
        stage1_features = []

        # 首先添加强制保留特征
        for feature in valid_force_features:
            if feature not in stage1_features:
                stage1_features.append(feature)

        # 然后按重要性添加其他特征，直到达到目标数量
        remaining_slots = n_features_stage1 - len(stage1_features)
        if remaining_slots > 0:
            # 从重要性排序中选择非强制特征
            for _, row in feature_importance_df.iterrows():
                if len(stage1_features) >= n_features_stage1:
                    break
                if row['feature'] not in stage1_features:
                    stage1_features.append(row['feature'])

        # 如果强制特征数量超过了目标数量，调整目标数量
        if len(valid_force_features) > n_features_stage1:
            n_features_stage1 = len(valid_force_features)
            if verbose:
                logger.info(f"two_stage_feature_selection [{target_name}]: 调整第一阶段目标特征数: {n_features_stage1} (适应强制保留特征)")

        stage1_end = time.time()
        selection_stats['stage1_features'] = len(stage1_features)
        selection_stats['stage1_time'] = stage1_end - stage1_start

        if verbose:
            print(f"      ✅ 第一阶段完成: {len(X_df.columns)} → {len(stage1_features)} 特征")
            print(f"      筛选比例: {len(stage1_features)/len(X_df.columns):.1%}")
            print(f"      耗时: {stage1_end - stage1_start:.2f}秒")
            print(f"      重要性范围: {feature_importance_df['importance'].max():.4f} - {feature_importance_df['importance'].min():.4f}")

        # === 🚀 寻找基准最优阈值 ===
        if verbose:
            print(f"    🎯 寻找基准最优阈值...")

        baseline_threshold_result = find_baseline_optimal_threshold(
            X_scaled_df, y_array, target_config, target_name, device_type, verbose
        )

        optimal_threshold = baseline_threshold_result['optimal_threshold']
        threshold_method = baseline_threshold_result['threshold_method']

        if verbose:
            print(f"      ✅ 基准最优阈值: {optimal_threshold:.3f} (方法: {threshold_method})")

        # === 第二阶段：RFE精选 ===
        if verbose:
            print(f"    🎯 第二阶段: RFE递归特征消除...")

        stage2_start = time.time()

        # 获取RFE配置
        rfe_cv_folds = target_config.get('rfe_cv_folds', 3)
        rfe_step = target_config.get('rfe_step', 1)
        rfe_scoring = target_config.get('rfe_scoring', 'f1')
        rfe_min_features = target_config.get('rfe_min_features_to_select', 40)

        # 🚀 处理自定义盈利导向评分函数 - 使用基准最优阈值
        if rfe_scoring in ['binary_simulated_profit', 'binary_profit_precision_composite', 'binary_risk_adjusted_return']:
            # 创建改进的自定义评分函数
            from sklearn.metrics import make_scorer

            def custom_profit_scorer_with_optimal_threshold(y_true, y_proba, **kwargs):
                """
                🚀 改进的自定义盈利导向评分函数 - 使用基准最优阈值

                Args:
                    y_true: 真实标签
                    y_proba: 预测概率
                """
                try:
                    # 确保y_proba是概率值
                    if hasattr(y_proba, 'shape') and len(y_proba.shape) == 2:
                        # 如果是二维数组，取正类概率
                        y_proba = y_proba[:, 1]

                    # 将概率限制在合理范围内
                    y_proba = np.clip(y_proba, 0.01, 0.99)

                    # 🚀 关键改进：使用基准最优阈值而不是固定的0.5
                    if rfe_scoring == 'binary_simulated_profit':
                        profit_result = _calculate_binary_simulated_profit(y_true, y_proba, threshold=optimal_threshold)
                        return profit_result.get('expected_profit_per_trade', 0.0)
                    elif rfe_scoring == 'binary_risk_adjusted_return':
                        profit_result = _calculate_binary_simulated_profit(y_true, y_proba, threshold=optimal_threshold)
                        return profit_result.get('risk_adjusted_return', 0.0)
                    elif rfe_scoring == 'binary_profit_precision_composite':
                        from sklearn.metrics import precision_score
                        profit_result = _calculate_binary_simulated_profit(y_true, y_proba, threshold=optimal_threshold)
                        expected_profit = profit_result.get('expected_profit_per_trade', 0.0)
                        y_pred_binary = (y_proba >= optimal_threshold).astype(int)
                        precision = precision_score(y_true, y_pred_binary, zero_division=0)
                        return 0.7 * expected_profit + 0.3 * precision
                    else:
                        return 0.0
                except Exception as e:
                    if verbose:
                        print(f"      ⚠️ 自定义评分函数计算失败: {e}")
                    return 0.0

            # 🎯 Critical Bug Fix: 设置 needs_proba=True
            rfe_scoring = make_scorer(custom_profit_scorer_with_optimal_threshold, greater_is_better=True, needs_proba=True)
            if verbose:
                print(f"      🚀 使用优化的盈利导向评分函数: {target_config.get('rfe_scoring')}")
                print(f"      🎯 评分阈值: {optimal_threshold:.3f}")
        else:
            if verbose:
                print(f"      📊 使用标准评分函数: {rfe_scoring}")

        # RFE估计器参数
        rfe_estimator_params = {
            'n_estimators': target_config.get('rfe_estimator_n_estimators', 100),
            'learning_rate': target_config.get('rfe_estimator_learning_rate', 0.1),
            'num_leaves': target_config.get('rfe_estimator_num_leaves', 31),
            'random_state': 42,
            'verbose': -1,
            'device_type': device_type
        }

        # 准备第二阶段数据
        X_stage1 = X_scaled_df[stage1_features]

        # 创建RFE估计器
        rfe_estimator = LGBMClassifier(**rfe_estimator_params)

        # 创建交叉验证策略
        cv_strategy = StratifiedKFold(n_splits=rfe_cv_folds, shuffle=True, random_state=42)

        # 执行RFE
        rfe_selector = RFECV(
            estimator=rfe_estimator,
            step=rfe_step,
            cv=cv_strategy,
            scoring=rfe_scoring,
            min_features_to_select=rfe_min_features,
            n_jobs=1,  # LightGBM已经使用多线程
            verbose=0
        )


        rfe_selector.fit(X_stage1, y_array)

        # 获取RFE选择的特征
        rfe_selected_features = X_stage1.columns[rfe_selector.support_].tolist()

        # 🌍 确保强制保留特征在最终结果中
        stage2_features = []

        # 首先添加所有强制保留特征（在stage1中的）
        stage1_force_features = [f for f in valid_force_features if f in stage1_features]
        for feature in stage1_force_features:
            if feature not in stage2_features:
                stage2_features.append(feature)

        # 然后添加RFE选择的其他特征
        for feature in rfe_selected_features:
            if feature not in stage2_features:
                stage2_features.append(feature)

        # 如果强制特征导致最终特征数超过预期，记录这个情况
        if len(stage1_force_features) > len(rfe_selected_features):
            if verbose:
                logger.info(f"two_stage_feature_selection [{target_name}]: 强制保留特征增加了最终特征数: {len(rfe_selected_features)} → {len(stage2_features)}")

        stage2_end = time.time()
        selection_stats['stage2_features'] = len(stage2_features)
        selection_stats['stage2_time'] = stage2_end - stage2_start
        selection_stats['total_time'] = stage2_end - start_time

        # 添加RFE详细信息
        selection_stats['rfe_details'] = {
            'optimal_features': rfe_selector.n_features_,
            'cv_scores': rfe_selector.cv_results_,
            'ranking': rfe_selector.ranking_.tolist() if hasattr(rfe_selector, 'ranking_') else None
        }

        if verbose:
            print(f"      ✅ 第二阶段完成: {len(stage1_features)} → {len(stage2_features)} 特征")
            print(f"      最终压缩比: {len(stage2_features)/len(X_df.columns):.1%}")
            print(f"      RFE最优特征数: {rfe_selector.n_features_}")
            print(f"      耗时: {stage2_end - stage2_start:.2f}秒")
            print(f"    🎉 两阶段特征选择完成，总耗时: {stage2_end - start_time:.2f}秒")

        # 记录最终统计
        selection_stats['compression_ratio'] = len(stage2_features) / len(X_df.columns)
        selection_stats['features_removed'] = len(X_df.columns) - len(stage2_features)

        return stage2_features, selection_stats

    except Exception as e:
        logger.error(f"two_stage_feature_selection [{target_name}]: 特征选择失败: {e}")
        if verbose:
            print(f"    ❌ 两阶段特征选择失败: {e}")

        # 返回原始特征列表作为备选
        fallback_stats = {
            'stage1_method': 'failed',
            'stage2_method': 'failed',
            'original_features': len(X_df.columns),
            'stage1_features': len(X_df.columns),
            'stage2_features': len(X_df.columns),
            'error': str(e)
        }
        return list(X_df.columns), fallback_stats


def _add_market_state_adaptive_features(df_out, cfg, C, H, L, O, V_feat, interval_str):
    """
    🎯 市场状态自适应特征工程 - 核心优化建议2.1的实现

    让模型自主学习在不同市场状态下哪些特征更重要，从"特征堆砌"走向"特征智能"

    功能：
    1. 引入市场状态作为特征
    2. 创建交互特征（技术指标 × 市场状态）
    3. 强化特征选择的上下文感知

    Args:
        df_out: 输出DataFrame
        cfg: 配置字典
        C, H, L, O: 价格序列
        V_feat: 成交量序列
        interval_str: 时间间隔字符串
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)



    try:
        # 🎯 新增：从全局配置读取启用状态
        market_state_config = _get_market_state_config()
        global_enabled = market_state_config.get('enable', False)
        local_enabled = _get_cfg('enable_market_state_adaptive', default_value=True)

        # 优先使用全局配置，如果全局配置不存在则使用本地配置
        is_enabled = global_enabled if market_state_config else local_enabled

        if not is_enabled:
            logger.debug("_add_market_state_adaptive_features: (%s) 市场状态自适应特征未启用", interval_str)
            return

        logger.info("_add_market_state_adaptive_features: (%s) 开始计算市场状态自适应特征", interval_str)

        # === 1. 市场状态识别 ===
        market_states = _identify_market_regimes(df_out, cfg, C, H, L, interval_str)

        # 将市场状态作为分类特征（独热编码）
        for state_name, state_values in market_states.items():
            df_out[f'market_state_{state_name}'] = state_values

        # === 2. 创建交互特征 ===
        _create_regime_aware_interaction_features(df_out, cfg, market_states, interval_str)

        # === 3. 市场确定性特征 ===
        _add_market_certainty_features(df_out, cfg, market_states, C, H, L, interval_str)

        # === 🎯 新增：4. 特征质量评估和优化 ===
        _evaluate_and_optimize_features(df_out, cfg, market_states, interval_str)

        logger.info("_add_market_state_adaptive_features: (%s) 市场状态自适应特征计算完成", interval_str)

    except Exception as e:
        logger.error("_add_market_state_adaptive_features: (%s) 计算市场状态自适应特征时出错: %s", interval_str, e)
        logger.debug(traceback.format_exc())


def _identify_market_regimes(df_out, cfg, C, H, L, interval_str):
    """
    识别市场状态/制度 - V9.0统一大市场优化版本

    🎯 V9.0核心理念：为统一模型提供完整的"市场状态仪表盘"
    不再用于物理分割数据，而是作为特征为模型提供市场环境上下文

    核心状态识别：
    1. Strong_Trend_Up (强上升趋势) - 高权重学习目标
    2. Strong_Trend_Down (强下降趋势) - 高权重学习目标
    3. High_Volatility_Sideways (高波动盘整) - 低权重噪音过滤
    4. Low_Volatility_Sideways (低波动盘整) - 中权重酝酿期
    5. Normal_Trend (正常趋势) - 标准权重基准状态

    Returns:
        dict: 包含不同市场状态的字典，每个状态都是0/1的序列
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # === V8.0专家委员会系统配置参数 ===
        atr_period = _get_cfg('expert_system_atr_period', default_value=14)
        adx_period = _get_cfg('expert_system_adx_period', default_value=14)
        ema_fast = _get_cfg('expert_system_ema_fast', default_value=12)
        ema_slow = _get_cfg('expert_system_ema_slow', default_value=26)

        # 🎯 V8.0优化：更精确的阈值设定
        high_vol_threshold = _get_cfg('expert_system_high_vol_threshold', default_value=2.0)  # 降低阈值，更敏感
        low_trend_threshold = _get_cfg('expert_system_low_trend_threshold', default_value=25)  # 提高阈值，更严格
        strong_trend_threshold = _get_cfg('expert_system_strong_trend_threshold', default_value=35)  # 提高阈值，更严格

        market_states = {}

        # 计算ATR百分比（波动率指标）
        if len(C) >= atr_period:
            tr = np.maximum(H - L, np.maximum(abs(H - C.shift(1)), abs(L - C.shift(1))))
            atr = tr.rolling(window=atr_period, min_periods=1).mean()
            atr_percent = (atr / C) * 100
        else:
            atr_percent = pd.Series([1.0] * len(C), index=C.index)

        # 计算ADX（趋势强度指标）
        if len(C) >= adx_period:
            # 简化的ADX计算
            plus_dm = np.maximum(H.diff(), 0)
            minus_dm = np.maximum(-L.diff(), 0)
            plus_dm[H.diff() <= L.diff()] = 0
            minus_dm[L.diff() <= H.diff()] = 0

            tr_smooth = tr.rolling(window=adx_period, min_periods=1).mean()
            plus_di = 100 * (plus_dm.rolling(window=adx_period, min_periods=1).mean() / tr_smooth)
            minus_di = 100 * (minus_dm.rolling(window=adx_period, min_periods=1).mean() / tr_smooth)

            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di + 1e-10)
            adx = dx.rolling(window=adx_period, min_periods=1).mean()
        else:
            adx = pd.Series([15.0] * len(C), index=C.index)

        # 计算EMA差值（趋势方向）
        if len(C) >= ema_slow:
            ema_fast_val = C.ewm(span=ema_fast, min_periods=1).mean()
            ema_slow_val = C.ewm(span=ema_slow, min_periods=1).mean()
            ema_diff_pct = ((ema_fast_val - ema_slow_val) / ema_slow_val) * 100
        else:
            ema_diff_pct = pd.Series([0.0] * len(C), index=C.index)

        # === 🎯 V9.0统一大市场：完整市场状态仪表盘 ===

        # 🎯 V9.0核心理念：为统一模型提供丰富的市场环境上下文
        high_vol_condition = atr_percent > high_vol_threshold
        low_trend_condition = adx < low_trend_threshold
        strong_trend_condition = adx > strong_trend_threshold

        # 🎯 V9.0优化：更精确的趋势方向判断
        strong_uptrend_condition = ema_diff_pct > _get_cfg('v9_strong_up_threshold', default_value=0.8)
        strong_downtrend_condition = ema_diff_pct < _get_cfg('v9_strong_down_threshold', default_value=-0.8)
        moderate_uptrend_condition = (ema_diff_pct > 0.2) & (ema_diff_pct <= 0.8)
        moderate_downtrend_condition = (ema_diff_pct < -0.2) & (ema_diff_pct >= -0.8)

        # === 🎯 V9.0核心状态定义（用于样本权重分配）===

        # 1. 🔴 高波动盘整（权重0.1x）- 噪音状态，最低学习优先级
        market_states['high_vol_sideways'] = (high_vol_condition & low_trend_condition).astype(int)

        # 2. 🟢 强上升趋势（权重3.0x）- 最高学习优先级，送分题
        market_states['strong_trend_up'] = (strong_trend_condition & strong_uptrend_condition).astype(int)

        # 3. 🔴 强下降趋势（权重3.0x）- 最高学习优先级，送分题
        market_states['strong_trend_down'] = (strong_trend_condition & strong_downtrend_condition).astype(int)

        # 4. 🟡 低波动盘整（权重1.5x）- 中高优先级，趋势酝酿期
        low_vol_condition = atr_percent < (high_vol_threshold * 0.5)
        market_states['low_vol_sideways'] = (low_vol_condition & low_trend_condition).astype(int)

        # 5. 🟦 正常趋势（权重1.0x）- 标准基准状态
        moderate_trend_condition = (adx >= low_trend_threshold) & (adx <= strong_trend_threshold)
        market_states['normal_trend'] = moderate_trend_condition.astype(int)

        # === 🎯 V9.0新增：细分趋势状态（为交互特征提供更丰富上下文）===

        # 6. 中等上升趋势
        market_states['moderate_uptrend'] = (moderate_trend_condition & moderate_uptrend_condition).astype(int)

        # 7. 中等下降趋势
        market_states['moderate_downtrend'] = (moderate_trend_condition & moderate_downtrend_condition).astype(int)

        # 8. 横盘震荡（非高波动）
        sideways_condition = (low_trend_condition & ~high_vol_condition)
        market_states['sideways_normal'] = sideways_condition.astype(int)

        # === 保持兼容性 ===
        market_states['strong_uptrend'] = market_states['strong_trend_up']
        market_states['strong_downtrend'] = market_states['strong_trend_down']

        # 6. 高确定性状态（强趋势 + 低波动）
        high_certainty_condition = strong_trend_condition & (atr_percent < high_vol_threshold)
        market_states['high_certainty'] = high_certainty_condition.astype(int)

        # 7. 低确定性状态（弱趋势 + 高波动）
        low_certainty_condition = low_trend_condition & high_vol_condition
        market_states['low_certainty'] = low_certainty_condition.astype(int)

        # 🎯 新增：极端市场状态识别（专门处理Fold 4类型问题）

        # 8. 极端波动状态（ATR > 5%，类似312/519暴跌）
        extreme_vol_threshold = _get_cfg('market_state_extreme_vol_threshold', default_value=5.0)
        market_states['extreme_volatility'] = (atr_percent > extreme_vol_threshold).astype(int)

        # 9. 流动性枯竭状态（低成交量 + 低波动）
        if 'volume' in df_out.columns:
            volume_ma = df_out['volume'].rolling(window=20, min_periods=1).mean()
            low_volume_condition = df_out['volume'] < (volume_ma * 0.5)
            market_states['liquidity_drought'] = (low_volume_condition & low_vol_condition).astype(int)
        else:
            market_states['liquidity_drought'] = pd.Series([0] * len(C), index=C.index)

        # 10. 恐慌性抛售状态（大幅下跌 + 高波动）
        panic_threshold = _get_cfg('market_state_panic_threshold', default_value=-3.0)
        large_drop_condition = ema_diff_pct < panic_threshold
        market_states['panic_selling'] = (large_drop_condition & high_vol_condition).astype(int)

        # 11. 泡沫状态（大幅上涨 + 高波动）
        bubble_threshold = _get_cfg('market_state_bubble_threshold', default_value=3.0)
        large_rise_condition = ema_diff_pct > bubble_threshold
        market_states['bubble_state'] = (large_rise_condition & high_vol_condition).astype(int)

        # 存储原始指标供后续使用
        df_out['market_atr_percent'] = atr_percent
        df_out['market_adx'] = adx
        df_out['market_ema_diff_pct'] = ema_diff_pct

        logger.debug("_identify_market_regimes: (%s) 识别了 %d 种市场状态", interval_str, len(market_states))

        return market_states

    except Exception as e:
        logger.error("_identify_market_regimes: (%s) 市场状态识别失败: %s", interval_str, e)
        # 返回默认状态（包含新增的极端市场状态）
        default_states = {
            'high_vol_sideways': pd.Series([0] * len(C), index=C.index),
            'low_vol_sideways': pd.Series([0] * len(C), index=C.index),
            'strong_uptrend': pd.Series([0] * len(C), index=C.index),
            'strong_downtrend': pd.Series([0] * len(C), index=C.index),
            'normal_trend': pd.Series([1] * len(C), index=C.index),  # 默认为正常趋势
            'high_certainty': pd.Series([0] * len(C), index=C.index),
            'low_certainty': pd.Series([0] * len(C), index=C.index),
            # 🎯 新增的极端市场状态默认值
            'extreme_volatility': pd.Series([0] * len(C), index=C.index),
            'liquidity_drought': pd.Series([0] * len(C), index=C.index),
            'panic_selling': pd.Series([0] * len(C), index=C.index),
            'bubble_state': pd.Series([0] * len(C), index=C.index)
        }
        return default_states


def _create_regime_aware_interaction_features(df_out, cfg, market_states, interval_str):
    """
    创建市场状态感知的交互特征

    将关键技术指标与市场状态进行交互，让模型学会在不同市场环境下关注不同的特征
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 🎯 新增：从全局配置读取交互指标
        market_state_config = _get_market_state_config()

        # 优先使用全局配置的指标列表
        if market_state_config and 'market_state_interaction_indicators' in market_state_config:
            interaction_indicators = market_state_config['market_state_interaction_indicators']
            logger.info("_create_regime_aware_interaction_features: (%s) 使用全局配置的交互指标，数量: %d",
                       interval_str, len(interaction_indicators))
        else:
            # 回退到本地配置或默认值
            interaction_indicators = _get_cfg('market_state_interaction_indicators',
                                            default_value=[
                                                # 核心动量指标
                                                'rsi', 'macd', 'macd_histogram', 'williams_r',
                                                # 波动率指标
                                                'bb_position', 'ATRr_14', 'bb_width',
                                                # 成交量指标
                                                'volume_ratio', 'volume_vs_avg', 'volume_momentum',
                                                # 价格位置指标
                                                'close_pos_in_candle', 'price_change_1p',
                                                # 趋势指标
                                                'ADX', 'ema_diff_pct', 'hma_slope',
                                                # 🎯 新增：高阶特征（速度和加速度）
                                                'rsi_velocity', 'rsi_acceleration',
                                                'macd_hist_velocity', 'macd_hist_acceleration',
                                                'volume_change_accel', 'atr_velocity', 'atr_acceleration',
                                                'adx_velocity', 'adx_acceleration', 'price_change_accel'
                                            ])
            logger.info("_create_regime_aware_interaction_features: (%s) 使用默认交互指标，数量: %d",
                       interval_str, len(interaction_indicators))

        # 🎯 新增：从全局配置读取市场状态列表
        if market_state_config and 'market_state_interaction_states' in market_state_config:
            interaction_states = market_state_config['market_state_interaction_states']
            logger.info("_create_regime_aware_interaction_features: (%s) 使用全局配置的市场状态，数量: %d",
                       interval_str, len(interaction_states))
        else:
            # 回退到本地配置或默认值
            interaction_states = _get_cfg('v9_market_state_interaction_states',
                                        default_value=[
                                            # === 核心权重状态（用于样本权重分配）===
                                            'strong_trend_up',      # 权重3.0x - 最高优先级
                                            'strong_trend_down',    # 权重3.0x - 最高优先级
                                            'low_vol_sideways',     # 权重1.5x - 中高优先级
                                            'high_vol_sideways',    # 权重0.1x - 最低优先级
                                            'normal_trend',         # 权重1.0x - 基准状态

                                            # === V9.0新增：细分状态（丰富交互特征）===
                                            'moderate_uptrend',     # 中等上升趋势
                                            'moderate_downtrend',   # 中等下降趋势
                                            'sideways_normal',      # 正常横盘

                                            # === 保持兼容性 ===
                                            'strong_uptrend', 'strong_downtrend',

                                            # === 其他有价值状态 ===
                                            'high_certainty', 'low_certainty',
                                            'extreme_volatility', 'panic_selling', 'bubble_state'
                                        ])
            logger.info("_create_regime_aware_interaction_features: (%s) 使用默认市场状态，数量: %d",
                       interval_str, len(interaction_states))

        interaction_count = 0

        # 🎯 优化：智能交互特征生成
        for indicator in interaction_indicators:
            if indicator in df_out.columns:
                indicator_values = df_out[indicator]

                for state_name in interaction_states:
                    if state_name in market_states:
                        state_mask = market_states[state_name]

                        # 1. 基础交互特征：指标值 × 市场状态 (修复命名格式)
                        interaction_feature_name = f'{indicator}_IN_{state_name}'
                        df_out[interaction_feature_name] = indicator_values * state_mask
                        interaction_count += 1

                        # 2. 条件特征：仅在特定市场状态下的指标值
                        conditional_feature_name = f'{indicator}_when_{state_name}'
                        df_out[conditional_feature_name] = np.where(
                            state_mask == 1,
                            indicator_values,
                            0
                        )
                        interaction_count += 1

                        # 🎯 新增：3. 状态相对强度特征（指标在该状态下的相对表现）
                        if state_mask.sum() > 10:  # 确保有足够的样本
                            state_mean = indicator_values[state_mask == 1].mean()
                            overall_mean = indicator_values.mean()
                            if not pd.isna(state_mean) and not pd.isna(overall_mean) and overall_mean != 0:
                                relative_strength_name = f'{indicator}_strength_in_{state_name}'
                                df_out[relative_strength_name] = np.where(
                                    state_mask == 1,
                                    (indicator_values - overall_mean) / abs(overall_mean),
                                    0
                                )
                                interaction_count += 1

                        # 🎯 新增：4. 状态转换特征（进入/退出状态时的指标变化）
                        state_diff = state_mask.diff().fillna(0)
                        entering_state = (state_diff == 1)  # 进入状态
                        exiting_state = (state_diff == -1)   # 退出状态

                        if entering_state.sum() > 5:  # 确保有足够的状态转换
                            entering_feature_name = f'{indicator}_entering_{state_name}'
                            df_out[entering_feature_name] = np.where(
                                entering_state,
                                indicator_values,
                                0
                            )
                            interaction_count += 1

                        if exiting_state.sum() > 5:
                            exiting_feature_name = f'{indicator}_exiting_{state_name}'
                            df_out[exiting_feature_name] = np.where(
                                exiting_state,
                                indicator_values,
                                0
                            )
                            interaction_count += 1
            else:
                logger.debug("_create_regime_aware_interaction_features: (%s) 指标 %s 不存在，跳过交互特征",
                           interval_str, indicator)

        # 🎯 新增：5. 创建高级多指标交互特征
        advanced_count = _create_advanced_multi_indicator_interactions(df_out, market_states, interaction_indicators, interval_str)
        interaction_count += advanced_count

        # 创建市场状态组合特征
        _create_market_state_combinations(df_out, market_states, interval_str)

        logger.info("_create_regime_aware_interaction_features: (%s) 创建了 %d 个交互特征",
                   interval_str, interaction_count)

    except Exception as e:
        logger.error("_create_regime_aware_interaction_features: (%s) 创建交互特征失败: %s", interval_str, e)


def _create_advanced_multi_indicator_interactions(df_out, market_states, interaction_indicators, interval_str):
    """
    🎯 创建高级多指标交互特征

    在特定市场状态下，创建多个指标之间的智能组合特征
    """
    try:
        created_count = 0

        # 🎯 扩展有意义的指标组合，包含高阶特征
        meaningful_combinations = [
            # 动量-波动率组合
            (['rsi', 'ATRr_14'], ['strong_uptrend', 'strong_downtrend']),
            (['macd', 'bb_width'], ['high_vol_sideways', 'low_vol_sideways']),

            # 成交量-价格组合
            (['volume_ratio', 'price_change_1p'], ['extreme_volatility', 'panic_selling']),
            (['volume_vs_avg', 'close_pos_in_candle'], ['bubble_state', 'high_certainty']),

            # 趋势-动量组合
            (['ADX', 'rsi'], ['normal_trend', 'high_certainty']),
            (['ema_diff_pct', 'macd_histogram'], ['strong_uptrend', 'strong_downtrend']),

            # 🎯 新增：高阶特征组合 - 捕捉动量变化的市场状态敏感性
            (['rsi_velocity', 'atr_velocity'], ['high_vol_sideways', 'extreme_volatility']),
            (['macd_hist_acceleration', 'volume_change_accel'], ['strong_uptrend', 'strong_downtrend']),
            (['atr_acceleration', 'adx_velocity'], ['panic_selling', 'bubble_state']),

            # 🎯 新增：速度-加速度组合 - 识别动量转折点
            (['rsi_velocity', 'rsi_acceleration'], ['high_certainty', 'low_certainty']),
            (['volume_change_accel', 'price_change_accel'], ['extreme_volatility', 'normal_trend']),
        ]

        for indicators, states in meaningful_combinations:
            # 检查指标是否存在
            available_indicators = [ind for ind in indicators if ind in df_out.columns]
            available_states = [state for state in states if state in market_states]

            if len(available_indicators) >= 2 and len(available_states) >= 1:
                for state_name in available_states:
                    state_mask = market_states[state_name]

                    # 创建指标乘积特征（在特定状态下）
                    if len(available_indicators) == 2:
                        ind1, ind2 = available_indicators[0], available_indicators[1]
                        combo_name = f'{ind1}_x_{ind2}_in_{state_name}'
                        df_out[combo_name] = np.where(
                            state_mask == 1,
                            df_out[ind1] * df_out[ind2],
                            0
                        )
                        created_count += 1

                        # 创建指标比值特征（避免除零）
                        ratio_name = f'{ind1}_ratio_{ind2}_in_{state_name}'
                        df_out[ratio_name] = np.where(
                            (state_mask == 1) & (df_out[ind2] != 0),
                            df_out[ind1] / df_out[ind2],
                            0
                        )
                        created_count += 1

        # 🎯 特殊组合：市场压力指标
        if all(col in df_out.columns for col in ['rsi', 'bb_position', 'volume_ratio']):
            for state_name in ['panic_selling', 'extreme_volatility']:
                if state_name in market_states:
                    state_mask = market_states[state_name]
                    pressure_name = f'market_pressure_in_{state_name}'
                    df_out[pressure_name] = np.where(
                        state_mask == 1,
                        (df_out['rsi'] / 100.0) * df_out['bb_position'] * np.log1p(df_out['volume_ratio']),
                        0
                    )
                    created_count += 1

        logger.debug("_create_advanced_multi_indicator_interactions: (%s) 创建了 %d 个高级交互特征",
                    interval_str, created_count)
        return created_count

    except Exception as e:
        logger.error("_create_advanced_multi_indicator_interactions: (%s) 创建高级交互特征失败: %s",
                    interval_str, e)
        return 0


def _create_market_state_combinations(df_out, market_states, interval_str):
    """创建市场状态组合特征"""
    try:
        # 趋势 + 波动率组合
        if 'strong_uptrend' in market_states and 'high_certainty' in market_states:
            df_out['bullish_high_certainty'] = (market_states['strong_uptrend'] &
                                              market_states['high_certainty']).astype(int)

        if 'strong_downtrend' in market_states and 'high_certainty' in market_states:
            df_out['bearish_high_certainty'] = (market_states['strong_downtrend'] &
                                              market_states['high_certainty']).astype(int)

        # 危险状态组合（包含新的极端状态）
        dangerous_conditions = []
        if 'high_vol_sideways' in market_states:
            dangerous_conditions.append(market_states['high_vol_sideways'])
        if 'low_certainty' in market_states:
            dangerous_conditions.append(market_states['low_certainty'])
        if 'extreme_volatility' in market_states:
            dangerous_conditions.append(market_states['extreme_volatility'])
        if 'panic_selling' in market_states:
            dangerous_conditions.append(market_states['panic_selling'])

        if dangerous_conditions:
            df_out['dangerous_market'] = np.logical_or.reduce(dangerous_conditions).astype(int)

        # 🎯 新增：极端危险状态（Fold 4类型）
        extreme_dangerous_conditions = []
        if 'extreme_volatility' in market_states:
            extreme_dangerous_conditions.append(market_states['extreme_volatility'])
        if 'panic_selling' in market_states:
            extreme_dangerous_conditions.append(market_states['panic_selling'])
        if 'bubble_state' in market_states:
            extreme_dangerous_conditions.append(market_states['bubble_state'])

        if extreme_dangerous_conditions:
            df_out['extreme_dangerous_market'] = np.logical_or.reduce(extreme_dangerous_conditions).astype(int)

        # 理想交易环境
        if 'high_certainty' in market_states:
            df_out['ideal_trading_env'] = market_states['high_certainty']

        # 🎯 新增：流动性相关组合
        if 'liquidity_drought' in market_states and 'low_vol_sideways' in market_states:
            df_out['low_liquidity_environment'] = (market_states['liquidity_drought'] |
                                                 market_states['low_vol_sideways']).astype(int)

        logger.debug("_create_market_state_combinations: (%s) 创建市场状态组合特征完成", interval_str)

    except Exception as e:
        logger.error("_create_market_state_combinations: (%s) 创建市场状态组合特征失败: %s", interval_str, e)


def _add_market_certainty_features(df_out, cfg, market_states, C, H, L, interval_str):
    """
    添加市场确定性特征

    这些特征帮助模型识别市场的"可预测性"程度，为动态风险管理提供依据
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 配置参数
        certainty_window = _get_cfg('market_certainty_window', default_value=10)

        # 1. 趋势一致性评分
        if 'market_ema_diff_pct' in df_out.columns:
            # 计算趋势方向的一致性
            trend_direction = np.sign(df_out['market_ema_diff_pct'])
            trend_consistency = trend_direction.rolling(window=certainty_window, min_periods=1).apply(
                lambda x: abs(x.sum()) / len(x), raw=True
            )
            df_out['trend_consistency_score'] = trend_consistency

        # 2. 波动率稳定性评分
        if 'market_atr_percent' in df_out.columns:
            atr_stability = 1.0 / (1.0 + df_out['market_atr_percent'].rolling(
                window=certainty_window, min_periods=1).std())
            df_out['volatility_stability_score'] = atr_stability.fillna(0.5)

        # 3. 价格行为可预测性
        price_momentum = C.pct_change()
        momentum_consistency = price_momentum.rolling(window=certainty_window, min_periods=1).apply(
            lambda x: 1.0 - abs(x.std()) if len(x) > 1 else 0.5, raw=True
        )
        df_out['price_predictability_score'] = momentum_consistency.fillna(0.5)

        # 4. 综合确定性评分
        certainty_components = []
        if 'trend_consistency_score' in df_out.columns:
            certainty_components.append(df_out['trend_consistency_score'])
        if 'volatility_stability_score' in df_out.columns:
            certainty_components.append(df_out['volatility_stability_score'])
        if 'price_predictability_score' in df_out.columns:
            certainty_components.append(df_out['price_predictability_score'])

        if certainty_components:
            df_out['market_certainty_composite'] = np.mean(certainty_components, axis=0)
        else:
            df_out['market_certainty_composite'] = 0.5

        # 5. 确定性分级
        certainty_score = df_out['market_certainty_composite']
        df_out['certainty_level_high'] = (certainty_score > 0.7).astype(int)
        df_out['certainty_level_medium'] = ((certainty_score >= 0.4) & (certainty_score <= 0.7)).astype(int)
        df_out['certainty_level_low'] = (certainty_score < 0.4).astype(int)

        # 6. 市场状态持续性
        for state_name, state_values in market_states.items():
            if isinstance(state_values, pd.Series):
                # 计算状态持续时间
                state_persistence = state_values.rolling(window=certainty_window, min_periods=1).sum()
                df_out[f'{state_name}_persistence'] = state_persistence / certainty_window

        logger.info("_add_market_certainty_features: (%s) 市场确定性特征计算完成", interval_str)

    except Exception as e:
        logger.error("_add_market_certainty_features: (%s) 计算市场确定性特征失败: %s", interval_str, e)


def _evaluate_and_optimize_features(df_out, cfg, market_states, interval_str):
    """
    🎯 特征质量评估和优化

    评估交互特征的质量，移除低质量特征，优化特征空间
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 是否启用特征质量评估
        if not _get_cfg('enable_feature_quality_evaluation', default_value=True):
            return

        logger.debug("_evaluate_and_optimize_features: (%s) 开始特征质量评估", interval_str)

        # 获取所有交互特征
        interaction_features = [col for col in df_out.columns if '_x_' in col or '_when_' in col or '_in_' in col]

        if len(interaction_features) == 0:
            logger.debug("_evaluate_and_optimize_features: (%s) 没有找到交互特征", interval_str)
            return

        # 🚀 特征质量评估指标 - 使用配置管理器外部化硬编码参数
        low_quality_features = []
        if CONFIG_MANAGER_AVAILABLE:
            quality_threshold = get_feature_engineering_param('feature_quality_threshold', cfg, 0.01, float)
            outlier_ratio_threshold = get_feature_engineering_param('outlier_ratio_threshold', cfg, 0.1, float)
            non_zero_ratio_threshold = get_feature_engineering_param('non_zero_ratio_threshold', cfg, 0.01, float)
        else:
            quality_threshold = _get_cfg('feature_quality_threshold', default_value=0.01)
            outlier_ratio_threshold = 0.1
            non_zero_ratio_threshold = 0.01

        for feature in interaction_features:
            feature_values = df_out[feature]

            # 1. 检查特征方差（去除常数特征）
            if feature_values.var() < 1e-10:
                low_quality_features.append((feature, 'zero_variance'))
                continue

            # 2. 检查非零值比例（去除过于稀疏的特征）
            non_zero_ratio = (feature_values != 0).mean()
            if non_zero_ratio < quality_threshold:
                low_quality_features.append((feature, f'sparse_{non_zero_ratio:.3f}'))
                continue

            # 3. 检查异常值比例
            if non_zero_ratio > 0:
                q99 = feature_values[feature_values != 0].quantile(0.99)
                q01 = feature_values[feature_values != 0].quantile(0.01)
                outlier_ratio = ((feature_values > q99) | (feature_values < q01)).mean()
                if outlier_ratio > outlier_ratio_threshold:  # 🚀 使用配置参数
                    # 进行异常值处理而不是删除特征
                    df_out[feature] = feature_values.clip(lower=q01, upper=q99)

        # 移除低质量特征
        features_to_remove = [feat for feat, reason in low_quality_features]
        if features_to_remove:
            df_out.drop(columns=features_to_remove, inplace=True)
            logger.info("_evaluate_and_optimize_features: (%s) 移除了 %d 个低质量特征",
                       interval_str, len(features_to_remove))

            # 记录移除原因（仅记录前5个）
            for feat, reason in low_quality_features[:5]:
                logger.debug("  移除特征 %s: %s", feat, reason)

        # 🎯 特征重要性排序（基于方差和分布）
        remaining_interaction_features = [col for col in df_out.columns if '_x_' in col or '_when_' in col or '_in_' in col]
        if remaining_interaction_features:
            feature_scores = {}
            for feature in remaining_interaction_features:
                feature_values = df_out[feature]
                non_zero_values = feature_values[feature_values != 0]

                if len(non_zero_values) > 10:
                    # 综合评分：方差 + 非零比例 + 分布均匀性
                    variance_score = non_zero_values.var()
                    coverage_score = len(non_zero_values) / len(feature_values)
                    distribution_score = 1.0 / (1.0 + non_zero_values.skew()**2)  # 偏度越小越好

                    feature_scores[feature] = variance_score * coverage_score * distribution_score

            # 记录前10个高质量特征
            if feature_scores:
                top_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)[:10]
                logger.debug("_evaluate_and_optimize_features: (%s) 前10个高质量交互特征:", interval_str)
                for feat, score in top_features:
                    logger.debug("  %s: %.4f", feat, score)

        logger.debug("_evaluate_and_optimize_features: (%s) 特征质量评估完成", interval_str)

    except Exception as e:
        logger.error("_evaluate_and_optimize_features: (%s) 特征质量评估失败: %s", interval_str, e)
        logger.debug(traceback.format_exc())


# ===============================================================
# LSTM 数据预处理函数
# ===============================================================

def add_lstm_features(df, target_config):
    """
    🎯 为LSTM模型生成精简的"纯粹"特征集

    设计原则：
    1. 只包含基础的价格和成交量变化率、K线形状等原始特征
    2. 避免长回看窗口的技术指标和复杂的特征组合
    3. 让LSTM自己去发现高阶模式和时间序列关系

    Args:
        df: 原始K线数据DataFrame
        target_config: 目标配置字典

    Returns:
        包含LSTM专用特征的DataFrame，如果失败返回None
    """
    try:
        logger.info("add_lstm_features: 开始为LSTM生成精简特征集")

        # 复制数据避免修改原始数据
        df_out = df.copy()

        # 检查必要的基础列
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df_out.columns]
        if missing_cols:
            logger.error(f"add_lstm_features: 缺少必要的基础列: {missing_cols}")
            return None

        # 确保数据类型正确
        for col in required_cols:
            df_out[col] = pd.to_numeric(df_out[col], errors='coerce')

        # 检查数据长度
        if len(df_out) < 10:
            logger.error("add_lstm_features: 数据长度不足，至少需要10行数据")
            return None

        # 提取基础价格和成交量数据
        O, H, L, C, V = df_out['open'], df_out['high'], df_out['low'], df_out['close'], df_out['volume']

        # === 1. 基础价格变化特征 ===
        logger.debug("add_lstm_features: 计算基础价格变化特征")

        # 价格变化率（1、3、5周期）
        df_out['price_change_1p'] = C.pct_change(1).fillna(0)
        df_out['price_change_3p'] = C.pct_change(3).fillna(0)
        df_out['price_change_5p'] = C.pct_change(5).fillna(0)

        # 对数收益率（更稳定的价格变化表示）
        df_out['log_return_1p'] = np.log(C / C.shift(1)).fillna(0)
        df_out['log_return_3p'] = np.log(C / C.shift(3)).fillna(0)

        # === 2. 基础成交量特征 ===
        logger.debug("add_lstm_features: 计算基础成交量特征")

        # 成交量变化率
        df_out['volume_change_1p'] = V.pct_change(1).fillna(0)
        df_out['volume_change_3p'] = V.pct_change(3).fillna(0)

        # 成交量相对强度（与短期均值比较）
        volume_ma5 = V.rolling(window=5, min_periods=1).mean()
        df_out['volume_ratio_5p'] = (V / volume_ma5).fillna(1.0)

        # === 3. 基础K线形态特征 ===
        logger.debug("add_lstm_features: 计算基础K线形态特征")

        # K线实体大小（标准化）
        body_size = abs(C - O)
        candle_range = H - L
        df_out['body_size_norm'] = (body_size / candle_range).fillna(0)

        # 上下影线长度（标准化）
        upper_shadow = H - np.maximum(C, O)
        lower_shadow = np.minimum(C, O) - L
        df_out['upper_shadow_norm'] = (upper_shadow / candle_range).fillna(0)
        df_out['lower_shadow_norm'] = (lower_shadow / candle_range).fillna(0)

        # 收盘价在K线中的位置
        df_out['close_pos_in_candle'] = ((C - L) / candle_range).fillna(0.5)

        # K线方向（绿红）
        df_out['is_green_candle'] = (C > O).astype(int)

        # === 4. 基础价格位置特征 ===
        logger.debug("add_lstm_features: 计算基础价格位置特征")

        # 价格相对于近期高低点的位置
        high_5p = H.rolling(window=5, min_periods=1).max()
        low_5p = L.rolling(window=5, min_periods=1).min()
        df_out['price_pos_5p'] = ((C - low_5p) / (high_5p - low_5p)).fillna(0.5)

        high_10p = H.rolling(window=10, min_periods=1).max()
        low_10p = L.rolling(window=10, min_periods=1).min()
        df_out['price_pos_10p'] = ((C - low_10p) / (high_10p - low_10p)).fillna(0.5)

        # === 5. 基础波动率特征 ===
        logger.debug("add_lstm_features: 计算基础波动率特征")

        # 简单的价格波动率（标准差）
        df_out['price_volatility_5p'] = C.rolling(window=5, min_periods=1).std().fillna(0)
        df_out['price_volatility_10p'] = C.rolling(window=10, min_periods=1).std().fillna(0)

        # 真实波动幅度（简化版ATR）
        tr1 = H - L
        tr2 = abs(H - C.shift(1))
        tr3 = abs(L - C.shift(1))
        true_range = np.maximum(tr1, np.maximum(tr2, tr3))
        df_out['true_range'] = true_range.fillna(0)
        df_out['atr_simple_5p'] = true_range.rolling(window=5, min_periods=1).mean().fillna(0)

        # === 6. 基础动量特征 ===
        logger.debug("add_lstm_features: 计算基础动量特征")

        # 价格动量（简单差值）
        df_out['momentum_3p'] = (C - C.shift(3)).fillna(0)
        df_out['momentum_5p'] = (C - C.shift(5)).fillna(0)

        # 成交量加权价格（VWAP简化版）
        typical_price = (H + L + C) / 3
        volume_price = typical_price * V
        df_out['vwap_5p'] = (volume_price.rolling(window=5, min_periods=1).sum() /
                            V.rolling(window=5, min_periods=1).sum()).fillna(C)
        df_out['price_vs_vwap'] = (C / df_out['vwap_5p']).fillna(1.0)

        # === 7. 时间特征（简化版） ===
        logger.debug("add_lstm_features: 计算基础时间特征")

        # 如果有时间戳，提取基础时间特征
        if 'timestamp' in df_out.columns:
            try:
                df_out['timestamp'] = pd.to_datetime(df_out['timestamp'])
                df_out['hour'] = df_out['timestamp'].dt.hour / 24.0  # 标准化到[0,1]
                df_out['day_of_week'] = df_out['timestamp'].dt.dayofweek / 6.0  # 标准化到[0,1]
            except:
                df_out['hour'] = 0.5
                df_out['day_of_week'] = 0.5
        else:
            df_out['hour'] = 0.5
            df_out['day_of_week'] = 0.5

        # 处理所有NaN值
        feature_columns = [col for col in df_out.columns if col not in required_cols + ['timestamp']]
        for col in feature_columns:
            if df_out[col].isnull().any():
                df_out[col] = df_out[col].fillna(0.0)

        # 处理无穷大值
        df_out = df_out.replace([np.inf, -np.inf], 0.0)

        logger.info(f"add_lstm_features: 成功生成 {len(feature_columns)} 个LSTM专用特征")
        logger.info(f"add_lstm_features: 特征列表: {feature_columns}")

        return df_out

    except Exception as e:
        logger.error(f"add_lstm_features: 生成LSTM特征时出错: {e}")
        logger.debug(traceback.format_exc())
        return None


def create_lstm_sequences(df, feature_columns, target_column, sequence_length, target_name="LSTM"):
    """
    将2D表格数据转换为LSTM需要的3D时序数据格式

    Args:
        df (pd.DataFrame): 包含特征和目标的DataFrame
        feature_columns (list): 特征列名列表
        target_column (str): 目标列名
        sequence_length (int): LSTM输入序列长度（时间步数）
        target_name (str): 目标名称，用于日志记录

    Returns:
        tuple: (X_sequences, y_sequences, valid_indices)
            - X_sequences: 3D numpy数组，形状为 (样本数, 时间步长, 特征数)
            - y_sequences: 1D numpy数组，对应的目标值
            - valid_indices: 有效样本的原始索引
    """
    logger.info(f"create_lstm_sequences: 开始为 '{target_name}' 创建LSTM序列数据")
    logger.info(f"  输入数据形状: {df.shape}, 序列长度: {sequence_length}")

    try:
        # 检查输入参数
        if df is None or df.empty:
            logger.error(f"create_lstm_sequences: DataFrame为空或None")
            return None, None, None

        if sequence_length <= 0:
            logger.error(f"create_lstm_sequences: 序列长度必须大于0，当前值: {sequence_length}")
            return None, None, None

        if len(df) < sequence_length:
            logger.error(f"create_lstm_sequences: 数据长度({len(df)})小于序列长度({sequence_length})")
            return None, None, None

        # 检查特征列是否存在
        missing_features = [col for col in feature_columns if col not in df.columns]
        if missing_features:
            logger.error(f"create_lstm_sequences: 缺少特征列: {missing_features}")
            return None, None, None

        if target_column not in df.columns:
            logger.error(f"create_lstm_sequences: 缺少目标列: {target_column}")
            return None, None, None

        # 🚀 增强的数据类型检查和转换
        try:
            if DATA_TYPE_VALIDATOR_AVAILABLE:
                logger.debug(f"create_lstm_sequences: 使用增强的数据类型验证器进行LSTM格式转换")

                # 使用数据类型验证器进行LSTM格式转换
                validator = DataTypeValidator()
                df_lstm, conversion_stats = validator.convert_to_lstm_format(
                    df, feature_columns, target_column, verbose=True
                )

                logger.info(f"create_lstm_sequences: LSTM格式转换完成 - 特征错误: {conversion_stats['feature_errors']}, "
                           f"目标错误: {conversion_stats['target_errors']}")

                # 提取转换后的数据
                X_df = df_lstm[feature_columns].copy()
                X_data = X_df.values.astype(np.float64)  # 确保为float64
                y_data = df_lstm[target_column].values

            else:
                # 传统的数据类型处理方法
                logger.debug(f"create_lstm_sequences: 使用传统方法进行数据类型转换")

                # 先检查特征列的数据类型
                logger.debug(f"create_lstm_sequences: 检查特征列数据类型")
                for col in feature_columns:
                    col_dtype = df[col].dtype
                    logger.debug(f"  {col}: {col_dtype}")

                # 提取特征数据并确保为数值类型
                X_df = df[feature_columns].copy()

                # 🚀 改进的类型转换 - 更严格的错误处理
                conversion_errors = 0
                for col in feature_columns:
                    try:
                        # 记录原始类型
                        original_dtype = X_df[col].dtype

                        # 强制转换为数值类型
                        X_df[col] = pd.to_numeric(X_df[col], errors='coerce')

                        # 检查转换结果
                        nan_count = X_df[col].isna().sum()
                        if nan_count > 0:
                            logger.warning(f"create_lstm_sequences: 列 '{col}' 转换后有 {nan_count} 个NaN值，填充为0.0")
                            X_df[col] = X_df[col].fillna(0.0)

                        # 强制转换为float64
                        X_df[col] = X_df[col].astype(np.float64)

                        logger.debug(f"create_lstm_sequences: 列 '{col}' 类型转换: {original_dtype} -> {X_df[col].dtype}")

                    except Exception as e_col:
                        conversion_errors += 1
                        logger.error(f"create_lstm_sequences: 转换列 {col} 时出错: {e_col}")
                        # 如果转换失败，设置为默认值
                        X_df[col] = 0.0

                if conversion_errors > 0:
                    logger.warning(f"create_lstm_sequences: 共有 {conversion_errors} 个列转换失败")

                # 转换为numpy数组
                X_data = X_df.values.astype(np.float64)  # 强制转换为float64
                y_data = df[target_column].values    # 形状: (时间点数,)

            logger.debug(f"create_lstm_sequences: 特征数据最终类型: {X_data.dtype}, 形状: {X_data.shape}")

        except Exception as e_extract:
            logger.error(f"create_lstm_sequences: 提取数据时出错: {e_extract}")
            return None, None, None

        # 🎯 检查特征数据中的NaN值（现在数据类型已经是float64）
        try:
            logger.debug(f"create_lstm_sequences: 特征数据形状: {X_data.shape}, 数据类型: {X_data.dtype}")

            # 检查NaN值（现在可以安全使用np.isnan）
            if np.isnan(X_data).any():
                nan_count = np.isnan(X_data).sum()
                logger.warning(f"create_lstm_sequences: 特征数据中存在 {nan_count} 个NaN值，将进行填充")
                X_data = np.nan_to_num(X_data, nan=0.0)
                logger.debug(f"create_lstm_sequences: NaN值填充完成")
            else:
                logger.debug(f"create_lstm_sequences: 特征数据无NaN值")

        except Exception as e_x_check:
            logger.error(f"create_lstm_sequences: 检查特征数据时出错: {e_x_check}")
            logger.debug(f"create_lstm_sequences: 特征数据样本: {X_data[:2, :5] if X_data.size > 0 else 'Empty'}")
            return None, None, None

        # 🎯 安全地检查目标数据中的NaN值
        try:
            # 首先检查数据类型
            logger.debug(f"create_lstm_sequences: 目标数据类型: {y_data.dtype}")

            if y_data.dtype.kind in ['f', 'i']:  # 浮点数或整数类型
                if np.isnan(y_data).any():
                    logger.warning(f"create_lstm_sequences: 目标数据中存在NaN值")
                    nan_mask = np.isnan(y_data)
                    if nan_mask.any():
                        logger.warning(f"create_lstm_sequences: 发现 {nan_mask.sum()} 个NaN目标值")
            else:
                # 对于非数值类型，检查None或空值
                logger.warning(f"create_lstm_sequences: 目标数据为非数值类型 ({y_data.dtype})，检查空值")
                null_mask = pd.isna(y_data)
                if null_mask.any():
                    logger.warning(f"create_lstm_sequences: 发现 {null_mask.sum()} 个空目标值")
        except Exception as e_nan_check:
            logger.warning(f"create_lstm_sequences: 检查NaN值时出错: {e_nan_check}")
            logger.warning(f"create_lstm_sequences: 目标数据样本: {y_data[:5]}")

        # 创建序列
        X_sequences = []
        y_sequences = []
        valid_indices = []

        # 从sequence_length开始，为每个时间点创建一个序列
        for i in range(sequence_length, len(df)):
            # 提取前sequence_length个时间步的特征作为输入序列
            sequence = X_data[i-sequence_length:i]  # 形状: (sequence_length, 特征数)
            target = y_data[i]  # 当前时间点的目标值

            # 🎯 安全地跳过包含NaN或无效的目标值
            is_valid_target = False
            try:
                if y_data.dtype.kind in ['f', 'i']:  # 数值类型
                    is_valid_target = not np.isnan(target)
                else:  # 非数值类型
                    is_valid_target = not pd.isna(target)
            except Exception as e_target_check:
                logger.debug(f"create_lstm_sequences: 检查目标值时出错: {e_target_check}, target: {target}")
                is_valid_target = target is not None and str(target).strip() != ''

            if is_valid_target:
                X_sequences.append(sequence)
                y_sequences.append(target)
                valid_indices.append(df.index[i])  # 保存原始索引

        if len(X_sequences) == 0:
            logger.error(f"create_lstm_sequences: 没有生成有效的序列数据")
            return None, None, None

        # 转换为numpy数组
        X_sequences = np.array(X_sequences)  # 形状: (样本数, sequence_length, 特征数)
        y_sequences = np.array(y_sequences)  # 形状: (样本数,)

        logger.info(f"create_lstm_sequences: 成功创建LSTM序列数据")
        logger.info(f"  X_sequences形状: {X_sequences.shape}")
        logger.info(f"  y_sequences形状: {y_sequences.shape}")
        logger.info(f"  有效样本数: {len(valid_indices)}")
        logger.info(f"  特征数: {X_sequences.shape[2]}")

        return X_sequences, y_sequences, valid_indices

    except Exception as e:
        logger.error(f"create_lstm_sequences: 创建LSTM序列时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return None, None, None


def prepare_lstm_prediction_data(df, feature_columns, sequence_length, scaler=None, target_name="LSTM"):
    """
    为LSTM模型预测准备3D序列数据

    Args:
        df (pd.DataFrame): 包含特征的DataFrame
        feature_columns (list): 特征列名列表
        sequence_length (int): LSTM输入序列长度
        scaler: 已训练的缩放器（可选）
        target_name (str): 目标名称，用于日志记录

    Returns:
        tuple: (X_sequence, valid_index)
            - X_sequence: 3D numpy数组，形状为 (1, sequence_length, 特征数)
            - valid_index: 有效样本的原始索引
    """
    logger.debug(f"prepare_lstm_prediction_data: 为 '{target_name}' 准备预测序列")

    try:
        # 检查输入参数
        if df is None or df.empty:
            logger.error(f"prepare_lstm_prediction_data: DataFrame为空或None")
            return None, None

        if len(df) < sequence_length:
            logger.error(f"prepare_lstm_prediction_data: 数据长度({len(df)})小于序列长度({sequence_length})")
            return None, None

        # 🎯 新增：智能特征对齐机制
        missing_features = [col for col in feature_columns if col not in df.columns]
        if missing_features:
            logger.warning(f"prepare_lstm_prediction_data: 缺少 {len(missing_features)} 个特征，将使用默认值填充")
            logger.debug(f"缺失特征前10个: {missing_features[:10]}")

            # 为缺失特征创建默认值
            for feature in missing_features:
                if 'entering_' in feature or 'exiting_' in feature:
                    # 状态转换特征默认为0
                    df[feature] = 0.0
                elif 'strength_in_' in feature:
                    # 强度特征默认为0
                    df[feature] = 0.0
                elif 'rsi' in feature.lower():
                    # RSI相关特征默认为50
                    df[feature] = 50.0
                elif 'volume' in feature.lower():
                    # 成交量相关特征默认为1
                    df[feature] = 1.0
                elif 'atr' in feature.lower():
                    # ATR相关特征默认为0.01
                    df[feature] = 0.01
                elif 'adx' in feature.lower():
                    # ADX相关特征默认为25
                    df[feature] = 25.0
                elif 'macd' in feature.lower():
                    # MACD相关特征默认为0
                    df[feature] = 0.0
                elif 'price' in feature.lower() and 'change' in feature.lower():
                    # 价格变化特征默认为0
                    df[feature] = 0.0
                else:
                    # 其他特征默认为0
                    df[feature] = 0.0

            logger.info(f"prepare_lstm_prediction_data: 已为 {len(missing_features)} 个缺失特征填充默认值")

        # 提取特征数据
        X_data = df[feature_columns].values

        # 处理NaN值
        if np.isnan(X_data).any():
            logger.warning(f"prepare_lstm_prediction_data: 特征数据中存在NaN值，将进行填充")
            X_data = np.nan_to_num(X_data, nan=0.0)

        # 应用缩放器（如果提供）
        if scaler is not None:
            try:
                X_data = scaler.transform(X_data)
                logger.debug(f"prepare_lstm_prediction_data: 已应用缩放器")
            except Exception as e:
                logger.warning(f"prepare_lstm_prediction_data: 应用缩放器失败: {e}")

        # 取最后sequence_length个时间步作为预测输入
        sequence = X_data[-sequence_length:]  # 形状: (sequence_length, 特征数)

        # 添加批次维度
        X_sequence = np.expand_dims(sequence, axis=0)  # 形状: (1, sequence_length, 特征数)

        # 获取最后一个时间点的索引
        valid_index = df.index[-1]

        logger.debug(f"prepare_lstm_prediction_data: 成功准备预测序列，形状: {X_sequence.shape}")

        return X_sequence, valid_index

    except Exception as e:
        logger.error(f"prepare_lstm_prediction_data: 准备预测序列时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return None, None

