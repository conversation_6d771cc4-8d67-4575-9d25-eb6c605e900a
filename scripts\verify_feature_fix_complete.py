#!/usr/bin/env python3
"""
验证特征命名修复是否完成
确认所有旧格式特征文件已被清理
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def scan_for_old_format_features():
    """扫描所有文件，查找旧格式特征"""
    logger.info("🔍 扫描所有文件，查找旧格式特征...")
    
    old_format_files = []
    old_format_patterns = [
        '_x_high_certainty',
        'adx_velocity_x_',
        'adx_acceleration_x_'
    ]
    
    # 扫描所有JSON文件
    for root, dirs, files in os.walk('.'):
        # 跳过一些不相关的目录
        if any(skip in root for skip in ['.git', '__pycache__', 'node_modules', '.vscode']):
            continue
            
        for file in files:
            if file.endswith('.json'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否包含旧格式特征
                    found_patterns = []
                    for pattern in old_format_patterns:
                        if pattern in content:
                            found_patterns.append(pattern)
                    
                    if found_patterns:
                        old_format_files.append({
                            'file': file_path,
                            'patterns': found_patterns
                        })
                        
                except Exception as e:
                    logger.debug(f"读取文件失败 {file_path}: {e}")
                    continue
    
    return old_format_files

def check_config_consistency():
    """检查配置一致性"""
    logger.info("🔍 检查配置一致性...")
    
    try:
        market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
        
        if not market_config:
            logger.error("❌ MARKET_STATE_ADAPTIVE_FEATURES_CONFIG 不存在")
            return False
        
        enabled = market_config.get('enable', False)
        if not enabled:
            logger.error("❌ 市场状态自适应特征未启用")
            return False
        
        indicators = market_config.get('market_state_interaction_indicators', [])
        states = market_config.get('market_state_interaction_states', [])
        
        # 检查关键指标
        required_indicators = ['adx_velocity', 'adx_acceleration']
        missing_indicators = [ind for ind in required_indicators if ind not in indicators]
        
        if missing_indicators:
            logger.error(f"❌ 配置中缺失关键指标: {missing_indicators}")
            return False
        
        # 检查关键状态
        required_states = ['high_certainty']
        missing_states = [state for state in required_states if state not in states]
        
        if missing_states:
            logger.error(f"❌ 配置中缺失关键状态: {missing_states}")
            return False
        
        logger.info("✅ 配置一致性检查通过")
        logger.info(f"   - 交互指标: {len(indicators)} 个")
        logger.info(f"   - 市场状态: {len(states)} 个")
        logger.info(f"   - 预计生成特征: {len(indicators) * len(states)} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False

def verify_code_modifications():
    """验证代码修改"""
    logger.info("🔍 验证代码修改...")
    
    try:
        with open("src/core/data_utils.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键修改
        key_modifications = [
            "f'{indicator}_IN_{state_name}'",  # 新的命名格式
            "_get_market_state_config",        # 配置读取函数
            "使用全局配置的交互指标"            # 日志信息
        ]
        
        found_modifications = []
        for mod in key_modifications:
            if mod in content:
                found_modifications.append(mod)
        
        logger.info(f"代码修改验证: {len(found_modifications)}/{len(key_modifications)}")
        
        if len(found_modifications) == len(key_modifications):
            logger.info("✅ 所有关键代码修改都已应用")
            return True
        else:
            missing = set(key_modifications) - set(found_modifications)
            logger.error(f"❌ 缺失的代码修改: {missing}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 代码验证失败: {e}")
        return False

def generate_expected_features():
    """生成预期的特征名称"""
    logger.info("📋 生成预期的特征名称...")
    
    try:
        market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
        indicators = market_config.get('market_state_interaction_indicators', [])
        states = market_config.get('market_state_interaction_states', [])
        
        # 生成问题特征的正确格式
        problem_features = [
            'adx_velocity_IN_high_certainty',
            'adx_acceleration_IN_high_certainty'
        ]
        
        logger.info("预期生成的问题特征（正确格式）:")
        for feature in problem_features:
            logger.info(f"  ✅ {feature}")
        
        # 生成一些关键SHAP特征的交互
        shap_features = ['meta_prob_diff_up_vs_down', 'global_ema_short', 'global_mdi']
        key_states = ['strong_uptrend', 'bull_momentum', 'high_certainty']
        
        key_interactions = []
        for feature in shap_features:
            if feature in indicators:
                for state in key_states:
                    if state in states:
                        interaction = f"{feature}_IN_{state}"
                        key_interactions.append(interaction)
        
        logger.info("关键SHAP特征交互示例:")
        for interaction in key_interactions[:6]:
            logger.info(f"  🚀 {interaction}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 特征生成失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始验证特征命名修复是否完成...")
    
    try:
        # 1. 扫描旧格式特征
        old_format_files = scan_for_old_format_features()
        
        if old_format_files:
            logger.error("❌ 仍然发现旧格式特征文件:")
            for file_info in old_format_files:
                logger.error(f"  📁 {file_info['file']}")
                for pattern in file_info['patterns']:
                    logger.error(f"    - {pattern}")
            return False
        else:
            logger.info("✅ 未发现任何旧格式特征文件")
        
        # 2. 检查配置一致性
        config_ok = check_config_consistency()
        
        # 3. 验证代码修改
        code_ok = verify_code_modifications()
        
        # 4. 生成预期特征
        features_ok = generate_expected_features()
        
        # 总结结果
        if config_ok and code_ok and features_ok:
            logger.info("🎉 特征命名修复验证完全通过！")
            logger.info("📋 修复总结:")
            logger.info("  ✅ 所有旧格式特征文件已清理")
            logger.info("  ✅ 配置一致性检查通过")
            logger.info("  ✅ 代码修改已正确应用")
            logger.info("  ✅ 预期特征生成正常")
            logger.info("")
            logger.info("🚀 现在可以重新训练模型:")
            logger.info("  1. 重新训练基础模型（UP/DOWN模型）")
            logger.info("  2. 重新训练元模型")
            logger.info("  3. 验证不再出现特征缺失错误")
            logger.info("  4. 监控Class_1 recall从23.1%提升至≥50%")
            return True
        else:
            logger.warning("⚠️ 部分验证失败")
            logger.info("验证结果:")
            logger.info(f"  旧格式清理: {'✅' if not old_format_files else '❌'}")
            logger.info(f"  配置一致性: {'✅' if config_ok else '❌'}")
            logger.info(f"  代码修改: {'✅' if code_ok else '❌'}")
            logger.info(f"  特征生成: {'✅' if features_ok else '❌'}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 特征命名修复验证完全通过！")
        print("🚀 现在可以重新训练模型，问题将得到解决")
    else:
        print("\n❌ 特征命名修复验证失败！")
