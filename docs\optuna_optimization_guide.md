# 优化的 Optuna 目标函数使用指南

## 概述

本指南介绍了如何使用优化的 Optuna 目标函数来改进超参数优化和阈值优化。主要改进包括：

1. **🎯 硬约束优化**: 使用 `trial.suggest_float(low=..., high=...)` 进行参数范围约束
2. **🚫 避免 -inf 惩罚**: 使用有限惩罚值替代 `-float('inf')`，改善解空间探索
3. **📊 详细试验记录**: 使用 `trial.set_user_attr()` 记录每个试验的详细指标
4. **🔍 结果分析**: 提供参数重要性分析和约束满足情况统计

## 主要组件

### 1. OptimizedOptunaObjective 类

核心优化目标函数类，提供改进的约束处理和试验记录。

```python
from src.core.optuna_optimizer import OptimizedOptunaObjective, OptimizationConstraints

# 创建约束配置
constraints = OptimizationConstraints(
    min_trades=20,
    min_win_rate=0.4,
    max_consecutive_losses=10,
    constraint_penalty_base=-1000.0,  # 🎯 使用有限惩罚值
    constraint_penalty_multiplier=10.0
)

# 创建优化器
optimizer = OptimizedOptunaObjective(
    constraints=constraints,
    optimization_strategy='composite_score',
    direction='maximize'
)
```

### 2. 约束处理改进

#### 传统方式（问题）
```python
# ❌ 传统方式：使用 -inf 惩罚
if metrics['num_trades'] < MIN_TRADES:
    return -float('inf')  # 这会阻碍 Optuna 探索解空间
```

#### 优化方式（推荐）
```python
# ✅ 优化方式：使用有限惩罚值
violations, penalty = self._check_constraints(metrics)
if violations:
    return penalty  # 例如 -1000.0，允许 Optuna 继续探索
else:
    return self._calculate_objective(metrics)
```

### 3. 参数约束改进

#### 传统方式
```python
# ❌ 传统方式：后置约束检查
threshold = trial.suggest_float('threshold', 0.1, 0.9)
if threshold < 0.5 or threshold > 0.8:  # 运行时检查
    return -float('inf')
```

#### 优化方式（推荐）
```python
# ✅ 优化方式：使用 Optuna 内置约束
threshold = trial.suggest_float('threshold', low=0.5, high=0.8, step=0.01)
# Optuna 自动确保参数在范围内
```

## 使用示例

### 1. 阈值优化

```python
from src.core.optuna_integration import optimize_meta_model_thresholds

# 使用优化的阈值优化
result = optimize_meta_model_thresholds(
    y_true_meta_val=y_true,
    y_proba_meta_val=y_proba,
    calculate_metrics_func=your_metrics_function,
    n_trials=100,
    timeout=3600,
    verbose=True
)

print(f"最佳参数: {result['threshold_up']}, {result['threshold_down']}")
print(f"约束满足率: {result['constraint_satisfaction_rate']:.2%}")
print(f"参数重要性: {result['parameter_importance']}")
```

### 2. 模型超参数优化

```python
from src.core.optuna_integration import optimize_model_hyperparameters

# 使用优化的模型超参数优化
result = optimize_model_hyperparameters(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    model_type='lgbm',
    n_trials=50,
    cv_folds=3,
    verbose=True
)

print(f"最佳分数: {result['best_score']:.4f}")
print(f"最佳参数: {result['best_params']}")
```

### 3. 自定义目标函数

```python
# 创建自定义阈值优化目标函数
param_ranges = {
    'threshold_up': (0.5, 0.9),
    'threshold_down': (0.5, 0.9),
    'confidence_gap_up': (0.0, 0.3),
    'confidence_gap_down': (0.0, 0.3)
}

objective = optimizer.create_threshold_objective(
    y_true=y_true,
    y_proba=y_proba,
    calculate_metrics_func=your_function,
    param_ranges=param_ranges
)

# 运行优化
study = create_optimized_study(direction='maximize')
study.optimize(objective, n_trials=100)
```

## 配置选项

### 约束配置

```python
constraints = OptimizationConstraints(
    min_trades=20,                    # 最小交易次数
    min_win_rate=0.4,                # 最小胜率
    max_consecutive_losses=10,        # 最大连续亏损
    min_profit_per_trade=0.0,        # 最小平均收益
    require_trade_balance=True,       # 是否要求交易平衡
    trade_balance_min_ratio=0.1,     # 最小方向占比
    constraint_penalty_base=-1000.0, # 基础惩罚值
    constraint_penalty_multiplier=10.0 # 惩罚倍数
)
```

### Study 配置

```python
# 采样器配置
sampler_config = {
    'seed': 42,
    'n_startup_trials': 20,      # 随机探索试验数
    'n_ei_candidates': 24,       # EI 候选点数
    'multivariate': True,        # 多变量优化
    'warn_independent_sampling': False
}

# 剪枝器配置
pruner_config = {
    'n_startup_trials': 10,      # 不剪枝的试验数
    'n_warmup_steps': 5,         # 预热步数
    'interval_steps': 1          # 剪枝检查间隔
}

study = create_optimized_study(
    direction='maximize',
    sampler_config=sampler_config,
    pruner_config=pruner_config
)
```

## 试验结果分析

### 详细记录的指标

每个试验会自动记录以下指标：

```python
# 基础指标
trial.set_user_attr('profit_per_trade', metrics.profit_per_trade)
trial.set_user_attr('total_trades', metrics.total_trades)
trial.set_user_attr('win_rate', metrics.win_rate)
trial.set_user_attr('total_profit', metrics.total_profit)

# 约束相关
trial.set_user_attr('constraint_violations', violations)
trial.set_user_attr('constraint_penalty', penalty)
trial.set_user_attr('constraints_satisfied', len(violations) == 0)

# 优化相关
trial.set_user_attr('optimization_strategy', strategy)
trial.set_user_attr('final_objective', objective_value)

# 性能分析
trial.set_user_attr('parameter_importance', importance_dict)
trial.set_user_attr('improvement_reasons', reasons_list)
```

### 结果分析函数

```python
from src.core.optuna_optimizer import analyze_study_results

# 分析优化结果
analysis = analyze_study_results(study, top_n=5)

print("Study 统计:")
print(f"  总试验数: {analysis['study_stats']['n_trials']}")
print(f"  完成试验: {analysis['study_stats']['n_complete_trials']}")
print(f"  剪枝试验: {analysis['study_stats']['n_pruned_trials']}")

print("约束分析:")
print(f"  约束满足率: {analysis['constraint_analysis']['constraint_satisfaction_rate']:.2%}")

print("参数重要性:")
for param, importance in analysis['parameter_importance'].items():
    print(f"  {param}: {importance:.4f}")
```

## 与现有代码集成

### 1. 替换现有 Optuna 实现

```python
# 在 config.py 中启用优化的 Optuna
USE_OPTIMIZED_OPTUNA = True

# 使用装饰器替换现有函数
from src.core.optuna_integration import replace_original_optuna_optimization

@replace_original_optuna_optimization
def your_existing_optuna_function(*args, **kwargs):
    # 原有的实现
    pass
```

### 2. 配置文件设置

```python
# config.py 中的相关配置
OPTUNA_CONSTRAINT_PENALTY_BASE = -1000.0
OPTUNA_CONSTRAINT_PENALTY_MULTIPLIER = 10.0
META_MODEL_MIN_TRADES_CONSTRAINT = 20
META_MODEL_MIN_WIN_RATE_CONSTRAINT = 0.4
META_MODEL_MAX_CONSECUTIVE_LOSSES = 10
```

## 最佳实践

### 1. 约束设计
- **使用有限惩罚值**: 避免 `-float('inf')`，使用如 `-1000.0` 的有限值
- **分层惩罚**: 不同约束使用不同的惩罚强度
- **渐进惩罚**: 惩罚程度与违反程度成正比

### 2. 参数范围
- **使用硬约束**: 优先使用 `suggest_float(low=..., high=...)` 
- **合理步长**: 为连续参数设置合适的 `step` 值
- **对数尺度**: 对于跨度很大的参数使用 `log=True`

### 3. 试验记录
- **记录关键指标**: 使用 `trial.set_user_attr()` 记录所有重要指标
- **记录约束状态**: 明确记录哪些约束被违反
- **记录改进原因**: 为最佳试验记录为什么表现更好

### 4. 结果分析
- **参数重要性**: 分析哪些参数对目标函数影响最大
- **约束满足率**: 监控约束满足情况，调整约束设置
- **收敛分析**: 观察优化过程是否收敛

## 故障排除

### 常见问题

1. **对数尺度参数错误**
   ```
   错误: The `low` value must be larger than 0 for a log distribution
   解决: 确保使用对数尺度时 low > 0
   ```

2. **约束过于严格**
   ```
   现象: 约束满足率很低 (<10%)
   解决: 放宽约束条件或调整惩罚值
   ```

3. **优化不收敛**
   ```
   现象: 最佳值长时间不改善
   解决: 增加试验数量或调整采样器参数
   ```

### 调试技巧

1. **启用详细日志**: 设置 `verbose=True` 查看优化过程
2. **检查约束满足率**: 确保有足够的试验满足约束
3. **分析参数重要性**: 关注重要性高的参数
4. **可视化优化过程**: 使用 Optuna 的可视化工具

## 总结

优化的 Optuna 目标函数通过以下改进显著提升了超参数优化的效果：

1. **更好的解空间探索**: 使用有限惩罚值替代 `-inf`
2. **更精确的约束控制**: 使用 Optuna 内置的参数范围约束
3. **更详细的结果记录**: 全面记录试验指标和约束状态
4. **更深入的结果分析**: 提供参数重要性和约束满足情况分析

这些改进使得 Optuna 能够更有效地找到满足约束的最优参数组合。
