"""
LSTM模型定义、训练和预测模块

这个模块封装了LSTM模型的完整生命周期：
- 模型架构定义
- 训练流程
- 预测逻辑
- 模型保存和加载
"""

import os
import numpy as np
import pandas as pd
import logging
import traceback
from typing import Tuple, Optional, Dict, Any
import joblib

# TensorFlow/Keras imports
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    from tensorflow.keras.utils import to_categorical
    from sklearn.preprocessing import MinMaxScaler, StandardScaler
    from sklearn.metrics import classification_report, confusion_matrix
    from sklearn.utils.class_weight import compute_class_weight
    TF_AVAILABLE = True
except ImportError as e:
    print(f"警告: TensorFlow/Keras导入失败: {e}")
    TF_AVAILABLE = False

# 设置日志
logger = logging.getLogger(__name__)

class LSTMModel:
    """
    LSTM模型类，封装了模型的定义、训练和预测功能
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LSTM模型
        
        Args:
            config: 模型配置字典，包含LSTM相关参数
        """
        if not TF_AVAILABLE:
            raise ImportError("TensorFlow/Keras未安装，无法使用LSTM模型")
            
        self.config = config
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.target_name = config.get('name', 'LSTM')
        
        # LSTM参数
        self.sequence_length = config.get('lstm_sequence_length', 60)
        self.hidden_units = config.get('lstm_hidden_units', 50)
        self.dropout_rate = config.get('lstm_dropout_rate', 0.2)
        self.learning_rate = config.get('lstm_learning_rate', 0.001)
        self.batch_size = config.get('lstm_batch_size', 32)
        self.epochs = config.get('lstm_epochs', 100)
        self.early_stopping_patience = config.get('lstm_early_stopping_patience', 10)
        self.validation_split = config.get('lstm_validation_split', 0.2)
        
        # 目标类型
        self.target_variable_type = config.get('target_variable_type', 'BOTH')
        self.num_classes = 3 if self.target_variable_type == 'BOTH' else 2
        
        # 强制使用CPU训练（数据量小，CPU更快）
        self._setup_cpu_only()
        
        logger.info(f"LSTM模型初始化完成: {self.target_name}")
        logger.info(f"  序列长度: {self.sequence_length}")
        logger.info(f"  隐藏单元: {self.hidden_units}")
        logger.info(f"  类别数: {self.num_classes}")
    
    def _setup_cpu_only(self):
        """强制使用CPU配置"""
        try:
            # 禁用GPU，强制使用CPU
            import os
            os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
            os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'

            # 禁用所有GPU设备
            try:
                tf.config.set_visible_devices([], 'GPU')
            except:
                pass

            # 设置TensorFlow CPU优化
            tf.config.threading.set_inter_op_parallelism_threads(4)
            tf.config.threading.set_intra_op_parallelism_threads(16)  # AMD 16核

            logger.info("✅ 强制CPU模式已启用 - AMD 16核优化配置")
            logger.info("  - 禁用GPU使用")
            logger.info("  - 启用oneDNN优化")
            logger.info("  - 16核并行处理")
        except Exception as e:
            logger.warning(f"CPU配置失败: {e}")
    
    def build_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """
        构建LSTM模型架构
        
        Args:
            input_shape: 输入形状 (sequence_length, num_features)
            
        Returns:
            编译后的Keras模型
        """
        logger.info(f"构建LSTM模型，输入形状: {input_shape}")
        
        model = Sequential([
            # 第一层LSTM
            LSTM(
                units=self.hidden_units,
                return_sequences=True,
                input_shape=input_shape,
                name='lstm_1'
            ),
            BatchNormalization(),
            Dropout(self.dropout_rate),
            
            # 第二层LSTM
            LSTM(
                units=self.hidden_units // 2,
                return_sequences=False,
                name='lstm_2'
            ),
            BatchNormalization(),
            Dropout(self.dropout_rate),
            
            # 全连接层
            Dense(32, activation='relu', name='dense_1'),
            Dropout(self.dropout_rate),
            Dense(16, activation='relu', name='dense_2'),
            
            # 输出层
            Dense(
                self.num_classes,
                activation='softmax' if self.num_classes > 2 else 'sigmoid',
                name='output'
            )
        ])
        
        # 编译模型
        optimizer = Adam(learning_rate=self.learning_rate)
        
        if self.num_classes > 2:
            # 多分类
            model.compile(
                optimizer=optimizer,
                loss='categorical_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
        else:
            # 二分类
            model.compile(
                optimizer=optimizer,
                loss='binary_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
        
        logger.info("LSTM模型构建完成")
        model.summary(print_fn=logger.info)
        
        return model
    
    def prepare_data(self, X_sequences: np.ndarray, y_sequences: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            X_sequences: 3D特征序列
            y_sequences: 目标序列
            
        Returns:
            处理后的特征和目标数据
        """
        logger.info(f"准备训练数据，输入形状: X={X_sequences.shape}, y={y_sequences.shape}")
        
        # 处理目标变量
        if self.num_classes > 2:
            # 多分类：转换为one-hot编码
            y_processed = to_categorical(y_sequences, num_classes=self.num_classes)
            logger.info(f"目标变量转换为one-hot编码，形状: {y_processed.shape}")
        else:
            # 二分类：保持原样
            y_processed = y_sequences.astype(np.float32)
            logger.info(f"二分类目标变量，形状: {y_processed.shape}")
        
        # 确保特征数据类型正确
        X_processed = X_sequences.astype(np.float32)
        
        logger.info(f"数据准备完成: X={X_processed.shape}, y={y_processed.shape}")
        return X_processed, y_processed
    
    def get_callbacks(self, model_save_path: str) -> list:
        """
        获取训练回调函数
        
        Args:
            model_save_path: 模型保存路径
            
        Returns:
            回调函数列表
        """
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=self.early_stopping_patience,
                restore_best_weights=True,
                verbose=1
            ),
            ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        return callbacks

    def _evaluate_model_performance(self, X_train: np.ndarray, y_train: np.ndarray,
                                   X_val: np.ndarray, y_val: np.ndarray,
                                   history) -> Dict[str, Any]:
        """
        评估LSTM模型性能

        Args:
            X_train: 训练集特征
            y_train: 训练集目标
            X_val: 验证集特征
            y_val: 验证集目标
            history: 训练历史

        Returns:
            性能指标字典
        """
        from sklearn.metrics import (
            accuracy_score, precision_score, recall_score, f1_score,
            classification_report, confusion_matrix, log_loss
        )

        try:
            # 获取训练和验证预测
            train_pred_proba = self.model.predict(X_train, verbose=0)
            val_pred_proba = self.model.predict(X_val, verbose=0)

            # 处理不同的目标类型
            if self.num_classes > 2:
                # 多分类
                train_pred = np.argmax(train_pred_proba, axis=1)
                val_pred = np.argmax(val_pred_proba, axis=1)

                # 转换y_train和y_val为类别标签
                if y_train.ndim > 1:
                    y_train_labels = np.argmax(y_train, axis=1)
                    y_val_labels = np.argmax(y_val, axis=1)
                else:
                    y_train_labels = y_train.astype(int)
                    y_val_labels = y_val.astype(int)

                # 计算概率用于log_loss
                train_proba_for_loss = train_pred_proba
                val_proba_for_loss = val_pred_proba

            else:
                # 二分类
                train_pred = (train_pred_proba > 0.5).astype(int).flatten()
                val_pred = (val_pred_proba > 0.5).astype(int).flatten()

                y_train_labels = y_train.astype(int).flatten()
                y_val_labels = y_val.astype(int).flatten()

                # 确保概率是二维的用于log_loss
                if train_pred_proba.ndim == 1:
                    train_proba_for_loss = np.column_stack([1-train_pred_proba, train_pred_proba])
                    val_proba_for_loss = np.column_stack([1-val_pred_proba, val_pred_proba])
                else:
                    train_proba_for_loss = train_pred_proba
                    val_proba_for_loss = val_pred_proba

            # 计算基本指标
            train_accuracy = accuracy_score(y_train_labels, train_pred)
            val_accuracy = accuracy_score(y_val_labels, val_pred)

            # 计算损失
            try:
                train_loss = log_loss(y_train_labels, train_proba_for_loss)
                val_loss = log_loss(y_val_labels, val_proba_for_loss)
            except:
                train_loss = history.history['loss'][-1]
                val_loss = history.history['val_loss'][-1]

            # 计算分类指标
            average_method = 'macro' if self.num_classes > 2 else 'binary'

            train_precision = precision_score(y_train_labels, train_pred, average=average_method, zero_division=0)
            train_recall = recall_score(y_train_labels, train_pred, average=average_method, zero_division=0)
            train_f1 = f1_score(y_train_labels, train_pred, average=average_method, zero_division=0)

            val_precision = precision_score(y_val_labels, val_pred, average=average_method, zero_division=0)
            val_recall = recall_score(y_val_labels, val_pred, average=average_method, zero_division=0)
            val_f1 = f1_score(y_val_labels, val_pred, average=average_method, zero_division=0)

            # 生成分类报告
            if self.num_classes > 2:
                target_names = ['下跌 (0)', '上涨 (1)', '中性 (2)']
            else:
                target_names = ['下跌 (0)', '上涨 (1)']

            val_classification_report = classification_report(
                y_val_labels, val_pred,
                target_names=target_names,
                output_dict=True,
                zero_division=0
            )

            # 混淆矩阵
            val_confusion_matrix = confusion_matrix(y_val_labels, val_pred)

            # 从训练历史获取最终指标
            final_train_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]
            final_train_acc = history.history['accuracy'][-1]
            final_val_acc = history.history['val_accuracy'][-1]

            # 检查过拟合
            loss_diff = final_train_loss - final_val_loss
            acc_diff = final_train_acc - final_val_acc
            is_overfitting = loss_diff < -0.1 or acc_diff > 0.1

            return {
                'final_train_loss': final_train_loss,
                'final_val_loss': final_val_loss,
                'final_train_accuracy': final_train_acc,
                'final_val_accuracy': final_val_acc,
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_precision': train_precision,
                'train_recall': train_recall,
                'train_f1': train_f1,
                'val_precision': val_precision,
                'val_recall': val_recall,
                'val_f1': val_f1,
                'val_classification_report': val_classification_report,
                'val_confusion_matrix': val_confusion_matrix.tolist(),
                'is_overfitting': is_overfitting,
                'loss_difference': loss_diff,
                'accuracy_difference': acc_diff,
                'best_epoch': np.argmin(history.history['val_loss']) + 1,
                'total_epochs': len(history.history['loss'])
            }

        except Exception as e:
            logger.error(f"性能评估失败: {e}")
            logger.debug(traceback.format_exc())

            # 返回基本指标
            return {
                'final_train_loss': history.history['loss'][-1],
                'final_val_loss': history.history['val_loss'][-1],
                'final_train_accuracy': history.history['accuracy'][-1],
                'final_val_accuracy': history.history['val_accuracy'][-1],
                'evaluation_error': str(e)
            }

    def train(self, X_sequences: np.ndarray, y_sequences: np.ndarray,
              feature_columns: list, model_save_dir: str) -> Dict[str, Any]:
        """
        训练LSTM模型

        Args:
            X_sequences: 3D特征序列
            y_sequences: 目标序列
            feature_columns: 特征列名列表
            model_save_dir: 模型保存目录

        Returns:
            训练结果字典
        """
        logger.info(f"开始训练LSTM模型: {self.target_name}")

        try:
            # 创建保存目录
            os.makedirs(model_save_dir, exist_ok=True)

            # 保存特征列名
            self.feature_columns = feature_columns

            # 准备数据
            X_processed, y_processed = self.prepare_data(X_sequences, y_sequences)

            # 🎯 使用时间序列分割避免数据泄露（修复前瞻偏差）
            from sklearn.model_selection import TimeSeriesSplit

            # 计算分割点（保持与validation_split相同的比例）
            split_point = int(len(X_processed) * (1 - self.validation_split))

            # 时间序列分割：训练集在前，验证集在后
            X_train = X_processed[:split_point]
            X_val = X_processed[split_point:]
            y_train = y_processed[:split_point]
            y_val = y_processed[split_point:]

            logger.info(f"数据分割完成: 训练集={X_train.shape}, 验证集={X_val.shape}")

            # 构建模型
            input_shape = (X_train.shape[1], X_train.shape[2])  # (sequence_length, num_features)
            self.model = self.build_model(input_shape)

            # 设置回调函数
            model_save_path = os.path.join(model_save_dir, 'lstm_model.h5')
            callbacks = self.get_callbacks(model_save_path)

            # =================================================================
            # === 核心修改：计算并应用类别权重 (Category Weighting) ===
            # =================================================================

            # 从 y_train (one-hot编码) 中获取原始的类别标签
            if y_train.ndim > 1:
                y_train_labels_for_weight = np.argmax(y_train, axis=1)
            else:
                y_train_labels_for_weight = y_train.astype(int)

            try:
                # 使用sklearn的工具自动计算平衡权重
                class_weights_array = compute_class_weight(
                    'balanced',
                    classes=np.unique(y_train_labels_for_weight),
                    y=y_train_labels_for_weight
                )
                # 转换为Keras需要的字典格式
                class_weight_dict = dict(enumerate(class_weights_array))
                logger.info(f"✅ 为LSTM训练应用类别权重: {class_weight_dict}")
            except Exception as e_cw:
                logger.error(f"❌ 计算类别权重失败: {e_cw}。将不使用类别权重。")
                class_weight_dict = None

            # =================================================================
            # === 修改结束 =================================================
            # =================================================================

            # 训练模型
            logger.info("开始模型训练...")
            history = self.model.fit(
                X_train, y_train,
                batch_size=self.batch_size,
                epochs=self.epochs,
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=1,
                shuffle=True, # 保持shuffle=True，对于非时序严格依赖的模型训练有好处
                class_weight=class_weight_dict # <-- 关键新增参数！
            )

            # 🎯 详细性能评估
            performance_metrics = self._evaluate_model_performance(
                X_train, y_train, X_val, y_val, history
            )

            # 保存训练历史
            history_path = os.path.join(model_save_dir, 'training_history.pkl')
            joblib.dump(history.history, history_path)

            # 保存特征列名
            features_path = os.path.join(model_save_dir, 'feature_columns.pkl')
            joblib.dump(self.feature_columns, features_path)

            # 保存配置
            config_path = os.path.join(model_save_dir, 'model_config.pkl')
            joblib.dump(self.config, config_path)

            # 构建训练结果
            training_result = {
                'status': '训练完成',
                'target_name': self.target_name,
                'model_type': 'LSTM',
                'epochs_trained': len(history.history['loss']),
                'model_save_path': model_save_path,
                'feature_count': len(self.feature_columns),
                **performance_metrics  # 包含所有性能指标
            }

            # 🎯 打印详细的控制台性能报告
            self._print_performance_report(training_result, history)

            return training_result

        except Exception as e:
            error_msg = f"LSTM模型训练失败: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())

            return {
                'status': '训练失败',
                'target_name': self.target_name,
                'error_message': str(e)
            }

    def _print_performance_report(self, training_result: Dict[str, Any], history) -> None:
        """
        打印详细的LSTM模型性能报告

        Args:
            training_result: 训练结果字典
            history: 训练历史
        """
        print("\n" + "="*80)
        print(f"🧠 LSTM模型训练完成 - {self.target_name}")
        print("="*80)

        # 基本信息
        print(f"📊 模型基本信息:")
        print(f"  模型类型: LSTM")
        print(f"  目标名称: {self.target_name}")
        print(f"  分类类型: {'三分类' if self.num_classes > 2 else '二分类'} ({self.num_classes} 类)")
        print(f"  特征数量: {training_result.get('feature_count', 'N/A')}")
        print(f"  序列长度: {self.sequence_length}")
        print(f"  隐藏单元: {self.hidden_units}")
        print()

        # 训练过程信息
        print(f"🏃 训练过程:")
        print(f"  总训练轮数: {training_result.get('total_epochs', 'N/A')}")
        print(f"  最佳轮数: {training_result.get('best_epoch', 'N/A')}")
        print(f"  批次大小: {self.batch_size}")
        print(f"  学习率: {self.learning_rate}")
        print(f"  验证集比例: {self.validation_split}")
        print()

        # 损失和准确率对比
        print(f"📈 训练 vs 验证性能对比:")
        print(f"  {'指标':<15} {'训练集':<12} {'验证集':<12} {'差值':<10} {'状态'}")
        print(f"  {'-'*15} {'-'*12} {'-'*12} {'-'*10} {'-'*10}")

        train_loss = training_result.get('final_train_loss', 0)
        val_loss = training_result.get('final_val_loss', 0)
        loss_diff = training_result.get('loss_difference', 0)
        loss_status = "⚠️过拟合" if loss_diff < -0.1 else "✅正常"

        train_acc = training_result.get('final_train_accuracy', 0)
        val_acc = training_result.get('final_val_accuracy', 0)
        acc_diff = training_result.get('accuracy_difference', 0)
        acc_status = "⚠️过拟合" if acc_diff > 0.1 else "✅正常"

        print(f"  {'损失 (Loss)':<15} {train_loss:<12.4f} {val_loss:<12.4f} {loss_diff:<10.4f} {loss_status}")
        print(f"  {'准确率 (Acc)':<15} {train_acc:<12.4f} {val_acc:<12.4f} {acc_diff:<10.4f} {acc_status}")
        print()

        # 详细分类指标
        if 'val_precision' in training_result:
            print(f"🎯 验证集详细指标:")
            print(f"  准确率 (Accuracy): {training_result.get('val_accuracy', 0):.4f}")
            print(f"  精确率 (Precision): {training_result.get('val_precision', 0):.4f}")
            print(f"  召回率 (Recall): {training_result.get('val_recall', 0):.4f}")
            print(f"  F1分数: {training_result.get('val_f1', 0):.4f}")
            print()

        # 分类报告
        if 'val_classification_report' in training_result:
            print(f"📋 验证集分类报告:")
            report = training_result['val_classification_report']

            # 打印每个类别的详细指标
            for class_name, metrics in report.items():
                if class_name in ['accuracy', 'macro avg', 'weighted avg']:
                    continue
                if isinstance(metrics, dict):
                    print(f"  {class_name}:")
                    print(f"    精确率: {metrics.get('precision', 0):.4f}")
                    print(f"    召回率: {metrics.get('recall', 0):.4f}")
                    print(f"    F1分数: {metrics.get('f1-score', 0):.4f}")
                    print(f"    样本数: {int(metrics.get('support', 0))}")
                    print()

            # 打印总体指标
            if 'macro avg' in report:
                macro_avg = report['macro avg']
                print(f"  宏平均:")
                print(f"    精确率: {macro_avg.get('precision', 0):.4f}")
                print(f"    召回率: {macro_avg.get('recall', 0):.4f}")
                print(f"    F1分数: {macro_avg.get('f1-score', 0):.4f}")
                print()

        # 混淆矩阵
        if 'val_confusion_matrix' in training_result:
            print(f"🔢 验证集混淆矩阵:")
            cm = training_result['val_confusion_matrix']
            if self.num_classes > 2:
                class_names = ['下跌', '上涨', '中性']
            else:
                class_names = ['下跌', '上涨']

            print(f"  实际\\预测  " + "  ".join(f"{name:>6}" for name in class_names))
            for i, row in enumerate(cm):
                print(f"  {class_names[i]:>8}  " + "  ".join(f"{val:>6}" for val in row))
            print()

        # 过拟合检查
        is_overfitting = training_result.get('is_overfitting', False)
        print(f"🔍 过拟合检查:")
        if is_overfitting:
            print(f"  ⚠️  检测到过拟合迹象!")
            print(f"     - 训练损失与验证损失差值: {loss_diff:.4f}")
            print(f"     - 训练准确率与验证准确率差值: {acc_diff:.4f}")
            print(f"  建议:")
            print(f"     - 增加Dropout比率")
            print(f"     - 减少模型复杂度")
            print(f"     - 增加训练数据")
            print(f"     - 使用更强的正则化")
        else:
            print(f"  ✅ 模型泛化良好，无明显过拟合")
        print()

        # 训练建议
        print(f"💡 训练建议:")
        if val_acc < 0.6:
            print(f"  📈 准确率较低 ({val_acc:.3f})，建议:")
            print(f"     - 增加模型复杂度（更多LSTM层或隐藏单元）")
            print(f"     - 调整学习率")
            print(f"     - 检查特征质量")
            print(f"     - 增加训练数据")
        elif val_acc > 0.8:
            print(f"  🎉 准确率优秀 ({val_acc:.3f})!")
            print(f"     - 模型性能良好")
            print(f"     - 可以考虑用于实际预测")
        else:
            print(f"  👍 准确率良好 ({val_acc:.3f})")
            print(f"     - 可以尝试进一步优化")

        print()
        print("="*80)
        print(f"✅ LSTM模型 {self.target_name} 训练报告完成")
        print("="*80)

    def predict(self, X_sequence: np.ndarray) -> Dict[str, Any]:
        """
        使用训练好的LSTM模型进行预测

        Args:
            X_sequence: 3D特征序列，形状为 (1, sequence_length, num_features)

        Returns:
            预测结果字典
        """
        if self.model is None:
            raise ValueError("模型未训练或未加载")

        try:
            logger.debug(f"LSTM预测输入形状: {X_sequence.shape}")

            # 确保输入数据类型正确
            X_sequence = X_sequence.astype(np.float32)

            # 进行预测
            predictions = self.model.predict(X_sequence, verbose=0)

            if self.num_classes > 2:
                # 多分类：获取概率和预测类别
                probabilities = predictions[0]  # 形状: (num_classes,)
                predicted_class = np.argmax(probabilities)

                result = {
                    'predicted_class': int(predicted_class),
                    'probabilities': {
                        'up': float(probabilities[1]) if len(probabilities) > 1 else 0.0,
                        'down': float(probabilities[0]) if len(probabilities) > 0 else 0.0,
                        'neutral': float(probabilities[2]) if len(probabilities) > 2 else 0.0
                    },
                    'confidence': float(np.max(probabilities))
                }
            else:
                # 二分类：获取概率
                probability = predictions[0][0] if predictions.ndim > 1 else predictions[0]
                predicted_class = 1 if probability > 0.5 else 0

                result = {
                    'predicted_class': int(predicted_class),
                    'probabilities': {
                        'positive': float(probability),
                        'negative': float(1 - probability)
                    },
                    'confidence': float(max(probability, 1 - probability))
                }

            logger.debug(f"LSTM预测完成: 类别={result['predicted_class']}, 置信度={result['confidence']:.4f}")
            return result

        except Exception as e:
            logger.error(f"LSTM预测失败: {e}")
            logger.debug(traceback.format_exc())
            raise

    def load_model(self, model_save_dir: str) -> bool:
        """
        加载训练好的LSTM模型

        Args:
            model_save_dir: 模型保存目录

        Returns:
            是否加载成功
        """
        try:
            logger.info(f"加载LSTM模型: {model_save_dir}")

            # 加载模型
            model_path = os.path.join(model_save_dir, 'lstm_model.h5')
            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return False

            self.model = load_model(model_path)
            logger.info("LSTM模型加载成功")

            # 加载特征列名
            features_path = os.path.join(model_save_dir, 'feature_columns.pkl')
            if os.path.exists(features_path):
                self.feature_columns = joblib.load(features_path)
                logger.info(f"特征列名加载成功，特征数: {len(self.feature_columns)}")
            else:
                logger.warning("特征列名文件不存在")

            # 加载配置
            config_path = os.path.join(model_save_dir, 'model_config.pkl')
            if os.path.exists(config_path):
                saved_config = joblib.load(config_path)
                # 更新配置（保留当前配置的优先级）
                for key, value in saved_config.items():
                    if key not in self.config:
                        self.config[key] = value
                logger.info("模型配置加载成功")

            return True

        except Exception as e:
            logger.error(f"加载LSTM模型失败: {e}")
            logger.debug(traceback.format_exc())
            return False

    def save_model_metadata(self, model_save_dir: str, training_result: Dict[str, Any]):
        """
        保存模型元数据

        Args:
            model_save_dir: 模型保存目录
            training_result: 训练结果
        """
        try:
            metadata = {
                'model_type': 'LSTM',
                'target_name': self.target_name,
                'sequence_length': self.sequence_length,
                'hidden_units': self.hidden_units,
                'num_classes': self.num_classes,
                'feature_count': len(self.feature_columns) if self.feature_columns else 0,
                'training_result': training_result,
                'config': self.config
            }

            metadata_path = os.path.join(model_save_dir, 'model_metadata.pkl')
            joblib.dump(metadata, metadata_path)
            logger.info(f"模型元数据已保存: {metadata_path}")

        except Exception as e:
            logger.error(f"保存模型元数据失败: {e}")


def create_lstm_model(config: Dict[str, Any]) -> LSTMModel:
    """
    创建LSTM模型实例的工厂函数

    Args:
        config: 模型配置字典

    Returns:
        LSTM模型实例
    """
    return LSTMModel(config)


def load_lstm_model(model_save_dir: str, config: Dict[str, Any]) -> Optional[LSTMModel]:
    """
    加载训练好的LSTM模型

    Args:
        model_save_dir: 模型保存目录
        config: 模型配置字典

    Returns:
        加载的LSTM模型实例，失败时返回None
    """
    try:
        model = LSTMModel(config)
        if model.load_model(model_save_dir):
            return model
        else:
            return None
    except Exception as e:
        logger.error(f"加载LSTM模型失败: {e}")
        return None
