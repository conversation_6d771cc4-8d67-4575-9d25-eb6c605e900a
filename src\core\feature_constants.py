#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征常量定义
集中管理所有硬编码的特征名字符串，避免在代码中分散定义
"""

from typing import Dict, List, Set

# =============================================================================
# 基础特征名常量
# =============================================================================

# 基础数据列
BASE_COLUMNS = {
    'open', 'high', 'low', 'close', 'volume', 'qav', 'n', 'tbbav', 'tbqav'
}

# 价格相关特征
PRICE_FEATURES = {
    # 价格变化特征（动态生成，这里列出常见的）
    'price_change_1p', 'price_change_2p', 'price_change_3p', 
    'price_change_5p', 'price_change_10p', 'price_change_15p', 'price_change_30p'
}

# K线形态特征
CANDLE_FEATURES = {
    # 基础K线特征
    'body_size', 'candle_range', 'upper_shadow', 'lower_shadow',
    'close_pos_in_candle', 'is_green_candle', 'is_doji',
    
    # 标准化K线特征
    'body_size_norm', 'upper_shadow_norm', 'lower_shadow_norm', 'candle_range_norm',
    
    # 平滑K线特征（动态生成）
    'upper_shadow_smooth3p', 'close_pos_in_candle_smooth3p', 'body_size_smooth3p',
    'upper_shadow_smooth5p', 'close_pos_in_candle_smooth5p', 'body_size_smooth5p',
    
    # K线形态识别
    'candlestick_pattern_name'
}

# 成交量特征
VOLUME_FEATURES = {
    'volume_vs_avg', 'volume_change_1p', 'volume_change_3p', 'volume_change_5p'
}

# 技术指标特征（固定参数）
TECHNICAL_INDICATORS_FIXED = {
    # MACD相关
    'MACD', 'MACD_histogram', 'MACD_signal',
    
    # 随机指标
    'STOCH_k', 'STOCH_d',
    
    # Keltner通道
    'KC_lower', 'KC_middle', 'KC_upper'
}

# 技术指标特征模板（动态生成）
TECHNICAL_INDICATORS_TEMPLATES = {
    'RSI': 'RSI_{period}',
    'HMA': 'HMA_{period}',
    'ATR': 'ATRr_{period}',
    'WILLR': 'WILLR_{period}',
    'CCI': 'CCI_{period}_{constant}',
    'EMA': 'EMA_{period}',
    'SMA': 'SMA_{period}'
}

# 资金流特征
FUND_FLOW_FEATURES = {
    'taker_buy_ratio_smooth5p', 'taker_buy_ratio_smooth10p', 'taker_buy_ratio_smooth15p'
}

# 衍生特征
DERIVED_FEATURES = {
    # EMA距离特征
    'ema_distance_abs', 'ema_distance_pct', 'ema_bullish_strength', 'ema_bearish_strength',
    
    # 布林带突破特征
    'bb_upper_breakout_strength', 'price_vs_bb_upper_pct',
    'bb_lower_breakout_strength', 'price_vs_bb_lower_pct',
    
    # 时间框架敏感度特征
    'rsi_15m_vs_4h_diff', 'rsi_15m_vs_4h_ratio',
    'close_pos_15m_vs_4h_diff', 'close_pos_15m_vs_4h_deviation',
    'macd_15m_vs_4h_diff', 'macd_15m_vs_4h_signal_agreement',
    'volume_ratio_15m_vs_4h_diff', 'volume_activity_15m_vs_4h_ratio'
}

# 时间特征
TIME_FEATURES = {
    'hour_sin', 'hour_cos', 'day_of_week_sin', 'day_of_week_cos',
    'day_of_month_sin', 'day_of_month_cos', 'month_sin', 'month_cos'
}

# 趋势特征
TREND_FEATURES = {
    'trend_slope_period_1', 'trend_slope_period_2', 'trend_slope_period_3',
    'adx_value', 'adx_pdi', 'adx_mdi', 'adx_trend_strength'
}

# =============================================================================
# 特征默认值常量
# =============================================================================

# 基础特征默认值
BASE_FEATURE_DEFAULTS = {
    # 价格变化特征
    'price_change_1p': 0.0, 'price_change_2p': 0.0, 'price_change_3p': 0.0,
    'price_change_5p': 0.0, 'price_change_10p': 0.0, 'price_change_15p': 0.0, 'price_change_30p': 0.0,
    
    # K线特征
    'body_size': 0.0, 'candle_range': 0.0, 'upper_shadow': 0.0, 'lower_shadow': 0.0,
    'close_pos_in_candle': 0.5, 'is_green_candle': 0, 'is_doji': 0,
    'body_size_norm': 0.0, 'upper_shadow_norm': 0.0, 'lower_shadow_norm': 0.0, 'candle_range_norm': 0.0,
    'upper_shadow_smooth3p': 0.0, 'close_pos_in_candle_smooth3p': 0.5, 'body_size_smooth3p': 0.0,
    'candlestick_pattern_name': 'Unknown',
    
    # 成交量特征
    'volume_vs_avg': 1.0, 'volume_change_1p': 0.0, 'volume_change_3p': 0.0, 'volume_change_5p': 0.0,
    
    # 技术指标特征
    'MACD': 0.0, 'MACD_histogram': 0.0, 'MACD_signal': 0.0,
    'STOCH_k': 50.0, 'STOCH_d': 50.0,
    'KC_lower': 0.0, 'KC_middle': 0.0, 'KC_upper': 0.0,
    
    # 资金流特征
    'taker_buy_ratio_smooth5p': 0.5, 'taker_buy_ratio_smooth10p': 0.5, 'taker_buy_ratio_smooth15p': 0.5,
    
    # 衍生特征
    'ema_distance_abs': 0.0, 'ema_distance_pct': 0.0, 'ema_bullish_strength': 0.0, 'ema_bearish_strength': 0.0,
    'bb_upper_breakout_strength': 0.0, 'price_vs_bb_upper_pct': 0.0,
    'bb_lower_breakout_strength': 0.0, 'price_vs_bb_lower_pct': 0.0,
    'rsi_15m_vs_4h_diff': 0.0, 'rsi_15m_vs_4h_ratio': 1.0,
    'close_pos_15m_vs_4h_diff': 0.0, 'close_pos_15m_vs_4h_deviation': 0.0,
    'macd_15m_vs_4h_diff': 0.0, 'macd_15m_vs_4h_signal_agreement': 1,
    'volume_ratio_15m_vs_4h_diff': 0.0, 'volume_activity_15m_vs_4h_ratio': 1.0,
    
    # 时间特征
    'hour_sin': 0.0, 'hour_cos': 1.0, 'day_of_week_sin': 0.0, 'day_of_week_cos': 1.0,
    'day_of_month_sin': 0.0, 'day_of_month_cos': 1.0, 'month_sin': 0.0, 'month_cos': 1.0,
    
    # 趋势特征
    'trend_slope_period_1': 0.0, 'trend_slope_period_2': 0.0, 'trend_slope_period_3': 0.0,
    'adx_value': 0.0, 'adx_pdi': 0.0, 'adx_mdi': 0.0, 'adx_trend_strength': 0.0
}

# 动态特征默认值模板
DYNAMIC_FEATURE_DEFAULTS = {
    'RSI': 50.0,
    'HMA': 0.0,
    'ATR': 0.0,
    'WILLR': -50.0,
    'CCI': 0.0,
    'EMA': 0.0,
    'SMA': 0.0
}

# =============================================================================
# 特征分组常量
# =============================================================================

# 按功能分组的特征集合
FEATURE_GROUPS = {
    'price': PRICE_FEATURES,
    'candle': CANDLE_FEATURES,
    'volume': VOLUME_FEATURES,
    'technical': TECHNICAL_INDICATORS_FIXED,
    'fund_flow': FUND_FLOW_FEATURES,
    'derived': DERIVED_FEATURES,
    'time': TIME_FEATURES,
    'trend': TREND_FEATURES
}

# 所有静态特征名（不依赖配置参数）
ALL_STATIC_FEATURES = (
    PRICE_FEATURES | CANDLE_FEATURES | VOLUME_FEATURES | 
    TECHNICAL_INDICATORS_FIXED | FUND_FLOW_FEATURES | 
    DERIVED_FEATURES | TIME_FEATURES | TREND_FEATURES
)

# 需要排除的列（不是特征）
EXCLUDED_COLUMNS = {
    # 基础数据列
    'open', 'high', 'low', 'close', 'volume', 'qav', 'n', 'tbbav', 'tbqav',
    
    # 目标变量列
    'target_up_1p', 'target_up_2p', 'target_up_3p', 'target_up_5p', 'target_up_10p',
    'target_down_1p', 'target_down_2p', 'target_down_3p', 'target_down_5p', 'target_down_10p',
    
    # 其他元数据列
    'timestamp', 'datetime', 'symbol'
}

# =============================================================================
# 特征验证常量
# =============================================================================

# 数值特征的有效范围
FEATURE_VALUE_RANGES = {
    'RSI': (0, 100),
    'WILLR': (-100, 0),
    'STOCH_k': (0, 100),
    'STOCH_d': (0, 100),
    'close_pos_in_candle': (0, 1),
    'volume_vs_avg': (0, float('inf')),
    'taker_buy_ratio': (0, 1)
}

# 特征数据类型映射
FEATURE_DATA_TYPES = {
    'float_features': {
        'price_change', 'body_size', 'candle_range', 'shadow', 'close_pos_in_candle',
        'volume_vs_avg', 'volume_change', 'RSI', 'HMA', 'ATR', 'WILLR', 'CCI',
        'MACD', 'STOCH', 'KC_', 'ema_', 'bb_', 'trend_slope', 'adx_'
    },
    'int_features': {
        'is_green_candle', 'is_doji', 'signal_agreement'
    },
    'string_features': {
        'candlestick_pattern_name'
    }
}

# =============================================================================
# 工具函数
# =============================================================================

def get_all_feature_names() -> Set[str]:
    """获取所有静态特征名"""
    return ALL_STATIC_FEATURES.copy()

def get_feature_group(group_name: str) -> Set[str]:
    """获取指定分组的特征名"""
    return FEATURE_GROUPS.get(group_name, set()).copy()

def is_dynamic_feature(feature_name: str) -> bool:
    """判断是否为动态特征（依赖配置参数）"""
    for template in TECHNICAL_INDICATORS_TEMPLATES.values():
        if '{' in template:  # 包含占位符的是动态特征
            base_name = template.split('_')[0]
            if feature_name.startswith(base_name):
                return True
    return False

def get_feature_default_value(feature_name: str) -> any:
    """获取特征的默认值"""
    # 首先检查基础默认值
    if feature_name in BASE_FEATURE_DEFAULTS:
        return BASE_FEATURE_DEFAULTS[feature_name]
    
    # 检查动态特征
    for indicator, default_val in DYNAMIC_FEATURE_DEFAULTS.items():
        if feature_name.startswith(indicator):
            return default_val
    
    # 根据特征名模式推断默认值
    if 'change' in feature_name or 'diff' in feature_name:
        return 0.0
    elif 'ratio' in feature_name or 'vs_avg' in feature_name:
        return 1.0
    elif 'pos_in' in feature_name:
        return 0.5
    elif 'RSI' in feature_name or 'STOCH' in feature_name:
        return 50.0
    elif 'WILLR' in feature_name:
        return -50.0
    elif feature_name.endswith('_name'):
        return 'Unknown'
    else:
        return 0.0

def validate_feature_name(feature_name: str) -> bool:
    """验证特征名是否符合命名规范"""
    if not feature_name:
        return False
    
    # 检查是否为排除的列
    if feature_name in EXCLUDED_COLUMNS:
        return False
    
    # 检查是否为已知的静态特征
    if feature_name in ALL_STATIC_FEATURES:
        return True
    
    # 检查是否为动态特征
    return is_dynamic_feature(feature_name)

def get_feature_data_type(feature_name: str) -> str:
    """获取特征的数据类型"""
    for data_type, patterns in FEATURE_DATA_TYPES.items():
        for pattern in patterns:
            if pattern in feature_name:
                if data_type == 'float_features':
                    return 'float64'
                elif data_type == 'int_features':
                    return 'int64'
                elif data_type == 'string_features':
                    return 'object'
    
    # 默认为浮点型
    return 'float64'
