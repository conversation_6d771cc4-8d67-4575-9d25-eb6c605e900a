#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版交易状态管理器
解决状态同步失效问题的根本性修复
"""

import os
import json
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
from abc import ABC, abstractmethod

# 设置日志
logger = logging.getLogger(__name__)

class TradeState(Enum):
    """交易状态枚举"""
    IDLE = "idle"
    OPENING = "opening"
    ACTIVE = "active"
    CLOSING = "closing"
    ERROR = "error"

class StateChangeEvent:
    """状态变更事件"""
    def __init__(self, old_state: TradeState, new_state: TradeState, 
                 reason: str, timestamp: datetime, trade_info: Dict[str, Any]):
        self.old_state = old_state
        self.new_state = new_state
        self.reason = reason
        self.timestamp = timestamp
        self.trade_info = trade_info.copy()

class StateObserver(ABC):
    """状态观察者接口"""
    
    @abstractmethod
    def on_state_changed(self, event: StateChangeEvent):
        """状态变更回调"""
        pass

class ConsistencyChecker:
    """状态一致性检查器"""
    
    def __init__(self, state_manager):
        self.state_manager = state_manager
        self.external_validators = []
    
    def add_validator(self, validator: Callable[[], bool]):
        """添加外部验证器"""
        self.external_validators.append(validator)
    
    def check_consistency(self) -> tuple[bool, List[str]]:
        """检查状态一致性"""
        issues = []
        
        # 检查超时
        if self.state_manager._is_state_timeout():
            issues.append("状态已超时但未自动重置")
        
        # 检查外部验证器
        for i, validator in enumerate(self.external_validators):
            try:
                if not validator():
                    issues.append(f"外部验证器{i}检查失败")
            except Exception as e:
                issues.append(f"外部验证器{i}异常: {e}")
        
        return len(issues) == 0, issues

class EnhancedTradeStateManager:
    """增强版交易状态管理器"""
    
    def __init__(self):
        self._initialized = False
        self._state_lock = threading.RLock()
        
        # 状态信息
        self._current_state = TradeState.IDLE
        self._trade_info = {}
        self._state_history = []
        self._filtered_signals = []
        
        # 观察者模式
        self._observers = []
        
        # 一致性检查器
        self._consistency_checker = ConsistencyChecker(self)
        
        # 配置参数
        self._load_config()
        
        # 加载持久化状态
        self._load_state()
        
        # 启动后台任务
        self._start_background_tasks()
        
        self._initialized = True
        logger.info(f"增强版交易状态管理器初始化完成 - 启用: {self.enabled}, 当前状态: {self._current_state.value}")
    
    def _load_config(self):
        """加载配置"""
        try:
            import config
            self.enabled = getattr(config, 'ENABLE_SINGLE_TRADE_LIMIT', True)
            self.timeout_minutes = getattr(config, 'SINGLE_TRADE_TIMEOUT_MINUTES', 35)
            self.force_reset_enabled = getattr(config, 'SINGLE_TRADE_FORCE_RESET_ENABLED', True)
            self.log_filtered_signals = getattr(config, 'SINGLE_TRADE_LOG_FILTERED_SIGNALS', True)
            self.state_file = getattr(config, 'SINGLE_TRADE_STATE_FILE', 'trade_state.json')
            self.backup_interval = getattr(config, 'SINGLE_TRADE_BACKUP_INTERVAL_MINUTES', 1)  # 更频繁的检查
            
            # 状态配置
            self.state_config = getattr(config, 'TRADE_STATE_CONFIG', {})
            self.allowed_transitions = self.state_config.get('allowed_transitions', {})
            self.blocking_states = [TradeState(s) for s in self.state_config.get('blocking_states', [])]
            self.timeout_reset_states = [TradeState(s) for s in self.state_config.get('timeout_reset_states', [])]
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            # 使用默认配置
            self.enabled = True
            self.timeout_minutes = 35
            self.force_reset_enabled = True
            self.log_filtered_signals = True
            self.state_file = 'trade_state.json'
            self.backup_interval = 1
            
            self.blocking_states = [TradeState.OPENING, TradeState.ACTIVE, TradeState.CLOSING]
            self.timeout_reset_states = [TradeState.OPENING, TradeState.CLOSING]
    
    def add_observer(self, observer: StateObserver):
        """添加状态观察者"""
        with self._state_lock:
            self._observers.append(observer)
    
    def remove_observer(self, observer: StateObserver):
        """移除状态观察者"""
        with self._state_lock:
            if observer in self._observers:
                self._observers.remove(observer)
    
    def _notify_observers(self, event: StateChangeEvent):
        """通知所有观察者"""
        for observer in self._observers:
            try:
                observer.on_state_changed(event)
            except Exception as e:
                logger.error(f"观察者通知失败: {e}")
    
    def add_external_validator(self, validator: Callable[[], bool]):
        """添加外部状态验证器"""
        self._consistency_checker.add_validator(validator)
    
    def _load_state(self):
        """加载持久化的交易状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复状态
                state_value = data.get('current_state', TradeState.IDLE.value)
                self._current_state = TradeState(state_value)
                self._trade_info = data.get('trade_info', {})
                
                # 🔧 修复：加载时立即检查超时
                if self._is_state_timeout():
                    logger.warning(f"检测到超时状态，自动重置: {self._current_state.value}")
                    self._reset_to_idle("状态超时自动重置")
                
                logger.info(f"交易状态已从文件恢复: {self._current_state.value}")
            else:
                logger.info("未找到状态文件，使用默认状态")
                
        except Exception as e:
            logger.error(f"加载交易状态失败: {e}")
            self._reset_to_idle("加载状态失败，重置为空闲")
    
    def _is_state_timeout(self) -> bool:
        """检查当前状态是否超时"""
        if self._current_state not in self.timeout_reset_states:
            return False
        
        state_time_str = self._trade_info.get('state_start_time')
        if not state_time_str:
            return False
        
        try:
            state_time = datetime.fromisoformat(state_time_str)
            timeout_threshold = datetime.now() - timedelta(minutes=self.timeout_minutes)
            is_timeout = state_time < timeout_threshold
            
            if is_timeout:
                duration = (datetime.now() - state_time).total_seconds() / 60
                logger.warning(f"状态超时检测: 状态={self._current_state.value}, 持续时间={duration:.1f}分钟, 超时阈值={self.timeout_minutes}分钟")
            
            return is_timeout
        except Exception as e:
            logger.error(f"超时检查异常: {e}")
            return True  # 如果时间解析失败，认为超时
    
    def _start_background_tasks(self):
        """启动后台任务"""
        def background_worker():
            while True:
                try:
                    # 🔧 修复：更频繁的状态检查
                    with self._state_lock:
                        # 检查超时
                        if self._is_state_timeout():
                            logger.warning(f"后台检查发现状态超时，自动重置: {self._current_state.value}")
                            self._reset_to_idle("后台检查发现状态超时")
                        
                        # 检查一致性
                        is_consistent, issues = self._consistency_checker.check_consistency()
                        if not is_consistent:
                            logger.warning(f"状态一致性检查失败: {issues}")
                            # 可以选择自动修复或仅记录
                    
                    # 定期保存状态
                    self._save_state()
                    
                    # 等待下次检查（更短的间隔）
                    time.sleep(self.backup_interval * 60)
                    
                except Exception as e:
                    logger.error(f"后台任务异常: {e}")
                    time.sleep(30)  # 出错时等待30秒
        
        # 启动后台线程
        background_thread = threading.Thread(target=background_worker, daemon=True)
        background_thread.start()
        logger.info("增强版交易状态管理器后台任务已启动")
    
    def can_start_new_trade(self) -> bool:
        """🔧 修复：检查是否可以开启新交易（包含超时检查）"""
        if not self.enabled:
            return True
        
        with self._state_lock:
            # 🔧 关键修复：在检查状态前先检查超时
            if self._is_state_timeout():
                logger.info(f"检测到状态超时，自动重置: {self._current_state.value}")
                self._reset_to_idle("can_start_new_trade检查时发现超时")
            
            return self._current_state not in self.blocking_states
    
    def start_trade(self, trade_info: Dict[str, Any]) -> bool:
        """开始新交易"""
        if not self.enabled:
            return True
        
        with self._state_lock:
            if not self.can_start_new_trade():
                logger.warning(f"无法开始新交易，当前状态: {self._current_state.value}")
                return False
            
            # 转换到开仓状态
            if self._transition_state(TradeState.OPENING, trade_info):
                logger.info(f"开始新交易: {trade_info.get('signal_type', 'Unknown')}")
                return True
            else:
                logger.error("状态转换失败，无法开始交易")
                return False
    
    def confirm_trade_opened(self, additional_info: Optional[Dict[str, Any]] = None):
        """确认交易已开仓"""
        if not self.enabled:
            return
        
        with self._state_lock:
            if additional_info:
                self._trade_info.update(additional_info)
            
            self._transition_state(TradeState.ACTIVE, {"confirmed_time": datetime.now().isoformat()})
            logger.info("交易开仓确认，状态转为活跃")
    
    def complete_trade(self, result_info: Optional[Dict[str, Any]] = None):
        """🔧 增强：完成交易（增加错误处理和日志）"""
        if not self.enabled:
            return
        
        with self._state_lock:
            try:
                if result_info:
                    self._trade_info.update(result_info)
                
                # 记录交易历史
                trade_record = {
                    **self._trade_info,
                    'completed_time': datetime.now().isoformat(),
                    'duration_minutes': self._get_trade_duration_minutes()
                }
                self._state_history.append(trade_record)
                
                # 重置为空闲状态
                self._reset_to_idle("交易正常完成")
                logger.info(f"交易完成，状态重置为空闲: {result_info}")
                
            except Exception as e:
                logger.error(f"完成交易时发生异常: {e}")
                # 即使出错也要重置状态，避免卡住
                self._reset_to_idle(f"交易完成时异常，强制重置: {e}")
    
    def _transition_state(self, new_state: TradeState, additional_info: Optional[Dict[str, Any]] = None) -> bool:
        """状态转换（增强版）"""
        old_state = self._current_state
        
        # 检查状态转换是否合法
        allowed_next_states = self.allowed_transitions.get(old_state.value, [])
        if new_state.value not in allowed_next_states:
            logger.error(f"非法状态转换: {old_state.value} -> {new_state.value}")
            logger.debug(f"允许的转换: {old_state.value} -> {allowed_next_states}")
            return False
        
        # 执行状态转换
        self._current_state = new_state
        
        # 更新交易信息
        self._trade_info['state_start_time'] = datetime.now().isoformat()
        self._trade_info['previous_state'] = old_state.value
        
        if additional_info:
            self._trade_info.update(additional_info)
        
        # 保存状态
        self._save_state()
        
        # 创建状态变更事件
        event = StateChangeEvent(
            old_state=old_state,
            new_state=new_state,
            reason=additional_info.get('reason', 'state_transition') if additional_info else 'state_transition',
            timestamp=datetime.now(),
            trade_info=self._trade_info
        )
        
        # 通知观察者
        self._notify_observers(event)
        
        logger.info(f"状态转换: {old_state.value} -> {new_state.value}")
        return True
    
    def _reset_to_idle(self, reason: str):
        """重置为空闲状态（增强版）"""
        old_state = self._current_state
        self._current_state = TradeState.IDLE
        self._trade_info = {'reset_reason': reason, 'reset_time': datetime.now().isoformat()}
        self._save_state()
        
        # 创建重置事件
        event = StateChangeEvent(
            old_state=old_state,
            new_state=TradeState.IDLE,
            reason=reason,
            timestamp=datetime.now(),
            trade_info=self._trade_info
        )
        
        # 通知观察者
        self._notify_observers(event)
        
        logger.info(f"状态重置为空闲: {old_state.value} -> {TradeState.IDLE.value}, 原因: {reason}")
    
    def _save_state(self):
        """保存状态到文件"""
        try:
            state_data = {
                'current_state': self._current_state.value,
                'trade_info': self._trade_info,
                'last_update': datetime.now().isoformat(),
                'version': '2.0'  # 增强版版本号
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存状态失败: {e}")
    
    def _get_trade_duration_minutes(self) -> float:
        """获取交易持续时间（分钟）"""
        start_time_str = self._trade_info.get('state_start_time')
        if not start_time_str:
            return 0.0
        
        try:
            start_time = datetime.fromisoformat(start_time_str)
            duration = datetime.now() - start_time
            return duration.total_seconds() / 60
        except Exception:
            return 0.0
    
    def get_status(self) -> Dict[str, Any]:
        """获取状态信息"""
        with self._state_lock:
            return {
                'enabled': self.enabled,
                'current_state': self._current_state.value,
                'trade_info': self._trade_info.copy(),
                'can_start_new_trade': self.can_start_new_trade(),
                'trade_duration_minutes': self._get_trade_duration_minutes(),
                'filtered_signals_count': len(self._filtered_signals),
                'state_history_count': len(self._state_history),
                'is_timeout': self._is_state_timeout(),
                'version': '2.0'
            }
    
    def force_reset(self, reason: str) -> bool:
        """强制重置状态"""
        try:
            with self._state_lock:
                logger.warning(f"强制重置交易状态: {self._current_state.value} -> idle, 原因: {reason}")
                self._reset_to_idle(reason)
                return True
        except Exception as e:
            logger.error(f"强制重置失败: {e}")
            return False

class SimulatorStateObserver(StateObserver):
    """模拟器状态观察者"""

    def __init__(self):
        self.last_check_time = datetime.now()
        self.simulator_check_interval = 30  # 30秒检查一次

    def on_state_changed(self, event: StateChangeEvent):
        """状态变更回调"""
        logger.info(f"状态变更通知: {event.old_state.value} -> {event.new_state.value}, 原因: {event.reason}")

        # 如果状态变为ACTIVE，开始监控模拟器
        if event.new_state == TradeState.ACTIVE:
            self._start_simulator_monitoring()

    def _start_simulator_monitoring(self):
        """开始监控模拟器状态"""
        def monitor_worker():
            while True:
                try:
                    # 检查模拟器是否还有活跃交易
                    has_active_trades = self._check_simulator_active_trades()

                    # 如果模拟器没有活跃交易但状态管理器显示ACTIVE，说明同步失效
                    current_state = enhanced_trade_state_manager._current_state
                    if current_state == TradeState.ACTIVE and not has_active_trades:
                        logger.warning("检测到状态同步失效：状态管理器显示ACTIVE但模拟器无活跃交易")
                        enhanced_trade_state_manager.force_reset("状态同步失效自动修复")
                        break

                    # 如果状态不是ACTIVE，停止监控
                    if current_state != TradeState.ACTIVE:
                        break

                    time.sleep(self.simulator_check_interval)

                except Exception as e:
                    logger.error(f"模拟器监控异常: {e}")
                    time.sleep(60)

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()

    def _check_simulator_active_trades(self) -> bool:
        """检查模拟器是否有活跃交易"""
        try:
            # 检查可能的模拟器状态文件
            sim_files = [
                "sim_trading_state.json",
                "simulator_state.json",
                "active_trades.json"
            ]

            for file in sim_files:
                if os.path.exists(file):
                    with open(file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 检查是否有活跃交易
                    active_trades = data.get('active_trades', [])
                    if active_trades:
                        return True

            # 如果没有找到任何状态文件或没有活跃交易，返回False
            return False

        except Exception as e:
            logger.error(f"检查模拟器状态失败: {e}")
            return False  # 出错时假设没有活跃交易

class StateConsistencyValidator:
    """状态一致性验证器"""

    @staticmethod
    def validate_simulator_consistency() -> bool:
        """验证与模拟器的状态一致性"""
        try:
            # 获取状态管理器状态
            status = enhanced_trade_state_manager.get_status()
            manager_has_active = status['current_state'] in ['opening', 'active', 'closing']

            # 检查模拟器状态
            simulator_has_active = StateConsistencyValidator._check_simulator_has_active_trades()

            # 如果状态管理器认为有活跃交易但模拟器没有，或者相反，则不一致
            if manager_has_active and not simulator_has_active:
                logger.warning(f"状态不一致：管理器={status['current_state']}, 模拟器=无活跃交易")
                return False

            return True

        except Exception as e:
            logger.error(f"状态一致性验证异常: {e}")
            return False

    @staticmethod
    def _check_simulator_has_active_trades() -> bool:
        """检查模拟器是否有活跃交易"""
        try:
            import glob

            # 检查所有可能的模拟器状态文件
            patterns = ["sim*.json", "*simulator*.json", "*trading*.json"]

            for pattern in patterns:
                files = glob.glob(pattern)
                for file in files:
                    try:
                        with open(file, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 检查各种可能的活跃交易字段
                        for key in ['active_trades', 'trades', 'open_trades']:
                            if key in data and data[key]:
                                return True
                    except:
                        continue

            return False

        except Exception:
            return False

# 创建全局实例
enhanced_trade_state_manager = EnhancedTradeStateManager()

# 添加观察者和验证器
simulator_observer = SimulatorStateObserver()
enhanced_trade_state_manager.add_observer(simulator_observer)
enhanced_trade_state_manager.add_external_validator(StateConsistencyValidator.validate_simulator_consistency)
