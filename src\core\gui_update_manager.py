#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI 更新管理器 - 增强的 GUI 更新机制

提供线程安全的 GUI 更新队列、批量更新、状态管理等功能，
提升界面响应性和稳定性。

主要功能：
1. 线程安全的 GUI 更新队列
2. 批量更新机制
3. 状态驱动的 GUI 更新
4. 性能优化和错误处理
"""

import logging
import threading
import tkinter as tk
import queue
import time
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

# 设置日志
logger = logging.getLogger(__name__)


class UpdatePriority(Enum):
    """GUI 更新优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class UpdateType(Enum):
    """GUI 更新类型"""
    STATUS = "status"
    PREDICTION = "prediction"
    PROGRESS = "progress"
    BUTTON_STATE = "button_state"
    PRICE = "price"
    TIME = "time"
    TRAINING_METRICS = "training_metrics"
    META_MODEL = "meta_model"
    BATCH = "batch"


@dataclass
class GUIUpdateRequest:
    """GUI 更新请求"""
    update_type: UpdateType
    update_func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: UpdatePriority = UpdatePriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    batch_key: Optional[str] = None  # 用于批量更新的键
    merge_with_previous: bool = False  # 是否可以与之前的更新合并

    def __lt__(self, other):
        """实现小于比较，用于优先级队列排序"""
        if not isinstance(other, GUIUpdateRequest):
            return NotImplemented
        # 优先级高的排在前面（值小的优先级高）
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        # 优先级相同时，按时间戳排序（早的排在前面）
        return self.timestamp < other.timestamp

    def __eq__(self, other):
        """实现等于比较"""
        if not isinstance(other, GUIUpdateRequest):
            return NotImplemented
        return (self.priority.value == other.priority.value and
                self.timestamp == other.timestamp and
                self.update_type == other.update_type)

    def __hash__(self):
        """实现哈希，使对象可以用作字典键"""
        return hash((self.update_type, self.priority.value, self.timestamp))


class GUIUpdateManager:
    """
    GUI 更新管理器
    
    提供线程安全的 GUI 更新队列和批量更新机制
    """
    
    def __init__(self, gui_module=None, update_interval_ms: int = 100):
        """
        初始化 GUI 更新管理器
        
        Args:
            gui_module: GUI 模块引用
            update_interval_ms: 更新间隔（毫秒）
        """
        self.gui_module = gui_module
        self.update_interval_ms = update_interval_ms
        
        # 更新队列（线程安全）
        self._update_queue = queue.PriorityQueue()
        self._batch_updates = {}  # 批量更新缓存
        self._batch_lock = threading.Lock()
        
        # 状态管理
        self._is_running = False
        self._gui_root = None
        self._update_job_id = None
        
        # 性能统计
        self._stats = {
            'total_updates': 0,
            'batched_updates': 0,
            'merged_updates': 0,
            'failed_updates': 0,
            'last_update_time': 0
        }
        
        # 更新类型映射（用于批量更新）
        self._batchable_types = {
            UpdateType.STATUS,
            UpdateType.PROGRESS,
            UpdateType.PRICE,
            UpdateType.TIME
        }
        
        logger.info(f"GUI更新管理器初始化完成，更新间隔: {update_interval_ms}ms")
    
    def set_gui_module(self, gui_module) -> None:
        """设置 GUI 模块引用"""
        self.gui_module = gui_module
        self._gui_root = getattr(gui_module, '_gui_root', None)
        logger.info("GUI模块已设置")
    
    def start(self) -> None:
        """启动 GUI 更新管理器"""
        if self._is_running:
            return
        
        if not self._gui_root:
            logger.warning("GUI根窗口未设置，无法启动更新管理器")
            return
        
        self._is_running = True
        self._schedule_next_update()
        logger.info("GUI更新管理器已启动")
    
    def stop(self) -> None:
        """停止 GUI 更新管理器"""
        self._is_running = False
        if self._update_job_id and self._gui_root:
            try:
                self._gui_root.after_cancel(self._update_job_id)
            except:
                pass
        logger.info("GUI更新管理器已停止")
    
    def queue_update(self, 
                    update_type: UpdateType,
                    update_func: Callable,
                    *args,
                    priority: UpdatePriority = UpdatePriority.NORMAL,
                    batch_key: Optional[str] = None,
                    merge_with_previous: bool = False,
                    **kwargs) -> None:
        """
        将 GUI 更新请求加入队列
        
        Args:
            update_type: 更新类型
            update_func: 更新函数
            *args: 位置参数
            priority: 优先级
            batch_key: 批量更新键
            merge_with_previous: 是否可以与之前的更新合并
            **kwargs: 关键字参数
        """
        try:
            # 创建更新请求
            request = GUIUpdateRequest(
                update_type=update_type,
                update_func=update_func,
                args=args,
                kwargs=kwargs,
                priority=priority,
                batch_key=batch_key,
                merge_with_previous=merge_with_previous
            )
            
            # 检查是否可以批量处理
            if self._should_batch_update(request):
                self._add_to_batch(request)
            else:
                # 直接加入队列（现在GUIUpdateRequest对象本身就可以比较）
                self._update_queue.put(request)
            
            self._stats['total_updates'] += 1
            
        except Exception as e:
            logger.error(f"加入GUI更新队列失败: {e}")
    
    def _should_batch_update(self, request: GUIUpdateRequest) -> bool:
        """判断是否应该批量处理更新"""
        return (
            request.update_type in self._batchable_types and
            request.batch_key is not None
        )
    
    def _add_to_batch(self, request: GUIUpdateRequest) -> None:
        """将更新请求添加到批量更新缓存"""
        with self._batch_lock:
            batch_key = f"{request.update_type.value}_{request.batch_key}"
            
            if request.merge_with_previous and batch_key in self._batch_updates:
                # 合并更新
                existing_request = self._batch_updates[batch_key]
                # 使用最新的参数
                existing_request.args = request.args
                existing_request.kwargs = request.kwargs
                existing_request.timestamp = request.timestamp
                self._stats['merged_updates'] += 1
            else:
                # 添加新的批量更新
                self._batch_updates[batch_key] = request
                self._stats['batched_updates'] += 1
    
    def _schedule_next_update(self) -> None:
        """调度下一次更新"""
        if not self._is_running or not self._gui_root:
            return
        
        try:
            self._update_job_id = self._gui_root.after(
                self.update_interval_ms, 
                self._process_updates
            )
        except Exception as e:
            logger.error(f"调度GUI更新失败: {e}")
    
    def _process_updates(self) -> None:
        """处理 GUI 更新队列"""
        try:
            start_time = time.time()
            updates_processed = 0
            max_updates_per_cycle = 50  # 限制每次处理的更新数量
            
            # 处理批量更新
            self._process_batch_updates()
            
            # 处理队列中的更新
            while (not self._update_queue.empty() and 
                   updates_processed < max_updates_per_cycle):
                
                try:
                    # 获取更新请求（非阻塞）
                    request = self._update_queue.get_nowait()

                    # 执行更新
                    self._execute_update(request)
                    updates_processed += 1
                    
                except queue.Empty:
                    break
                except Exception as e:
                    logger.error(f"处理GUI更新请求失败: {e}")
                    self._stats['failed_updates'] += 1
            
            # 更新统计信息
            self._stats['last_update_time'] = time.time() - start_time
            
            # 调度下一次更新
            self._schedule_next_update()
            
        except Exception as e:
            logger.error(f"处理GUI更新失败: {e}")
            # 确保继续调度
            self._schedule_next_update()
    
    def _process_batch_updates(self) -> None:
        """处理批量更新"""
        with self._batch_lock:
            if not self._batch_updates:
                return
            
            # 获取所有批量更新并清空缓存
            batch_updates = list(self._batch_updates.values())
            self._batch_updates.clear()
        
        # 按类型分组批量更新
        grouped_updates = {}
        for request in batch_updates:
            update_type = request.update_type
            if update_type not in grouped_updates:
                grouped_updates[update_type] = []
            grouped_updates[update_type].append(request)
        
        # 执行分组的批量更新
        for update_type, requests in grouped_updates.items():
            try:
                if update_type == UpdateType.STATUS:
                    self._execute_batch_status_updates(requests)
                elif update_type == UpdateType.PROGRESS:
                    self._execute_batch_progress_updates(requests)
                elif update_type == UpdateType.PRICE:
                    self._execute_batch_price_updates(requests)
                elif update_type == UpdateType.TIME:
                    self._execute_batch_time_updates(requests)
                else:
                    # 对于不支持批量的类型，逐个执行
                    for request in requests:
                        self._execute_update(request)
            except Exception as e:
                logger.error(f"执行批量更新失败 ({update_type}): {e}")
    
    def _execute_batch_status_updates(self, requests: List[GUIUpdateRequest]) -> None:
        """执行批量状态更新"""
        if not requests:
            return
        
        # 使用最新的状态更新
        latest_request = max(requests, key=lambda r: r.timestamp)
        self._execute_update(latest_request)
    
    def _execute_batch_progress_updates(self, requests: List[GUIUpdateRequest]) -> None:
        """执行批量进度更新"""
        if not requests:
            return
        
        # 使用最新的进度更新
        latest_request = max(requests, key=lambda r: r.timestamp)
        self._execute_update(latest_request)
    
    def _execute_batch_price_updates(self, requests: List[GUIUpdateRequest]) -> None:
        """执行批量价格更新"""
        if not requests:
            return
        
        # 使用最新的价格更新
        latest_request = max(requests, key=lambda r: r.timestamp)
        self._execute_update(latest_request)
    
    def _execute_batch_time_updates(self, requests: List[GUIUpdateRequest]) -> None:
        """执行批量时间更新"""
        if not requests:
            return
        
        # 使用最新的时间更新
        latest_request = max(requests, key=lambda r: r.timestamp)
        self._execute_update(latest_request)
    
    def _execute_update(self, request: GUIUpdateRequest) -> None:
        """执行单个 GUI 更新"""
        try:
            if not self._check_gui_availability():
                return
            
            # 执行更新函数
            request.update_func(*request.args, **request.kwargs)
            
        except tk.TclError as e:
            if "application has been destroyed" not in str(e):
                logger.error(f"GUI更新TclError: {e}")
        except Exception as e:
            logger.error(f"执行GUI更新失败: {e}")
            self._stats['failed_updates'] += 1
    
    def _check_gui_availability(self) -> bool:
        """检查 GUI 是否可用"""
        try:
            return (
                self._gui_root and 
                hasattr(self._gui_root, 'winfo_exists') and 
                self._gui_root.winfo_exists()
            )
        except:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self._stats.copy()

    def force_update(self) -> None:
        """
        🚀 强制立即处理所有待处理的GUI更新

        用于关键时刻（如阶段结束）确保GUI状态同步
        """
        if not self._is_running:
            return

        try:
            # 立即处理所有待处理的更新
            self._process_updates()

            # 确保批量更新也被处理
            self._process_batch_updates()

            logger.debug("强制GUI更新完成")

        except Exception as e:
            logger.error(f"强制GUI更新失败: {e}")

    def clear_queue(self) -> None:
        """清空更新队列"""
        while not self._update_queue.empty():
            try:
                self._update_queue.get_nowait()
            except queue.Empty:
                break
        
        with self._batch_lock:
            self._batch_updates.clear()
        
        logger.info("GUI更新队列已清空")


# 全局 GUI 更新管理器实例
_global_gui_update_manager = None
_gui_update_manager_lock = threading.Lock()


def get_gui_update_manager() -> GUIUpdateManager:
    """
    获取全局 GUI 更新管理器实例（单例模式）
    
    Returns:
        GUI 更新管理器实例
    """
    global _global_gui_update_manager
    
    if _global_gui_update_manager is None:
        with _gui_update_manager_lock:
            if _global_gui_update_manager is None:
                _global_gui_update_manager = GUIUpdateManager()
    
    return _global_gui_update_manager


def initialize_gui_update_manager(gui_module, update_interval_ms: int = 100) -> None:
    """
    初始化 GUI 更新管理器

    Args:
        gui_module: GUI 模块
        update_interval_ms: 更新间隔（毫秒）
    """
    manager = get_gui_update_manager()
    manager.update_interval_ms = update_interval_ms
    manager.set_gui_module(gui_module)
    manager.start()
    logger.info("GUI更新管理器已初始化并启动")


# 便捷的 GUI 更新函数

def queue_status_update(message: str, level: str = "neutral",
                       priority: UpdatePriority = UpdatePriority.NORMAL) -> None:
    """队列状态更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'update_status'):
        manager.queue_update(
            UpdateType.STATUS,
            manager.gui_module.update_status,
            message, level,
            priority=priority,
            batch_key="main_status",
            merge_with_previous=True
        )


def queue_prediction_update(target_name: str, text: str, color: Optional[str] = None,
                           priority: UpdatePriority = UpdatePriority.HIGH) -> None:
    """队列预测结果更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'update_prediction_display'):
        manager.queue_update(
            UpdateType.PREDICTION,
            manager.gui_module.update_prediction_display,
            target_name, text, color,
            priority=priority
        )


def queue_progress_update(progress: float,
                         priority: UpdatePriority = UpdatePriority.NORMAL) -> None:
    """队列进度更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'update_progress'):
        manager.queue_update(
            UpdateType.PROGRESS,
            manager.gui_module.update_progress,
            progress,
            priority=priority,
            batch_key="main_progress",
            merge_with_previous=True
        )


def queue_button_state_update(button_name: str, state: int,
                             priority: UpdatePriority = UpdatePriority.HIGH) -> None:
    """队列按钮状态更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'set_button_state'):
        manager.queue_update(
            UpdateType.BUTTON_STATE,
            manager.gui_module.set_button_state,
            button_name, state,
            priority=priority
        )


def queue_price_update(price_str: str, color: str,
                      priority: UpdatePriority = UpdatePriority.NORMAL) -> None:
    """队列价格更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'update_price_label'):
        manager.queue_update(
            UpdateType.PRICE,
            manager.gui_module.update_price_label,
            price_str, color,
            priority=priority,
            batch_key="btc_price",
            merge_with_previous=True
        )


def queue_time_update(time_str: str,
                     priority: UpdatePriority = UpdatePriority.LOW) -> None:
    """队列时间更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'update_time_label'):
        manager.queue_update(
            UpdateType.TIME,
            manager.gui_module.update_time_label,
            time_str,
            priority=priority,
            batch_key="current_time",
            merge_with_previous=True
        )


def queue_meta_model_update(text: str, color: Optional[str] = None,
                           priority: UpdatePriority = UpdatePriority.HIGH) -> None:
    """队列元模型显示更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, '_update_meta_model_traditional'):
        # 直接调用传统更新方法，避免循环调用
        manager.queue_update(
            UpdateType.META_MODEL,
            manager.gui_module._update_meta_model_traditional,
            text, color,
            priority=priority
        )


def queue_training_metrics_update(target_name: str, metrics_dict: Dict[str, Any],
                                 priority: UpdatePriority = UpdatePriority.NORMAL) -> None:
    """队列训练指标更新"""
    manager = get_gui_update_manager()
    if manager.gui_module and hasattr(manager.gui_module, 'append_evaluation_metrics'):
        manager.queue_update(
            UpdateType.TRAINING_METRICS,
            manager.gui_module.append_evaluation_metrics,
            target_name, metrics_dict,
            priority=priority
        )


def force_gui_update() -> None:
    """
    🚀 强制立即处理所有待处理的GUI更新

    便捷函数，用于关键时刻（如阶段结束）确保GUI状态同步
    """
    manager = get_gui_update_manager()
    manager.force_update()
