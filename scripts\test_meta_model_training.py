#!/usr/bin/env python3
"""
测试元模型训练修复
验证os模块导入问题是否已解决
"""

import os
import sys
import pandas as pd
import numpy as np
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_meta_model_training():
    """测试元模型训练"""
    logger.info("🧪 测试元模型训练修复...")
    
    try:
        # 检查CSV文件是否存在
        x_meta_file = "meta_model_data/X_meta_features_oof.csv"
        y_meta_file = "meta_model_data/y_meta_target.csv"
        
        if not os.path.exists(x_meta_file):
            logger.error(f"❌ 特征文件不存在: {x_meta_file}")
            return False
        
        if not os.path.exists(y_meta_file):
            logger.error(f"❌ 目标文件不存在: {y_meta_file}")
            return False
        
        # 加载数据
        logger.info("📊 加载元模型训练数据...")
        X_meta_df = pd.read_csv(x_meta_file, index_col=0)
        y_meta_series = pd.read_csv(y_meta_file, index_col=0).iloc[:, 0]
        
        logger.info(f"特征数据形状: {X_meta_df.shape}")
        logger.info(f"目标数据形状: {y_meta_series.shape}")
        
        # 检查数据质量
        logger.info("🔍 检查数据质量...")
        
        # 检查是否包含新的交互特征
        interaction_features = [col for col in X_meta_df.columns if '_IN_' in col]
        logger.info(f"发现交互特征数量: {len(interaction_features)}")
        
        if interaction_features:
            logger.info("✅ 发现新的交互特征:")
            for feature in interaction_features[:5]:  # 显示前5个
                logger.info(f"  📈 {feature}")
            if len(interaction_features) > 5:
                logger.info(f"  ... 还有 {len(interaction_features) - 5} 个")
        else:
            logger.warning("⚠️ 未发现交互特征，可能需要重新训练基础模型")
        
        # 检查目标变量分布
        target_dist = y_meta_series.value_counts()
        logger.info(f"目标变量分布: {dict(target_dist)}")
        
        # 尝试导入训练函数
        logger.info("🔧 测试训练函数导入...")
        from src.core.prediction import train_meta_model
        logger.info("✅ 训练函数导入成功")
        
        # 创建小样本测试
        logger.info("🧪 创建小样本测试...")
        sample_size = min(100, len(X_meta_df))
        X_sample = X_meta_df.head(sample_size)
        y_sample = y_meta_series.head(sample_size)
        
        logger.info(f"测试样本大小: {sample_size}")
        logger.info(f"测试特征数: {X_sample.shape[1]}")
        
        # 这里不实际调用训练函数，只是验证导入和数据准备
        logger.info("✅ 元模型训练准备就绪")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_training_readiness():
    """检查训练就绪状态"""
    logger.info("🔍 检查训练就绪状态...")
    
    checks = {
        "CSV文件存在": False,
        "数据格式正确": False,
        "交互特征存在": False,
        "训练函数可用": False,
        "配置正确": False
    }
    
    try:
        # 1. 检查CSV文件
        x_meta_file = "meta_model_data/X_meta_features_oof.csv"
        y_meta_file = "meta_model_data/y_meta_target.csv"
        
        if os.path.exists(x_meta_file) and os.path.exists(y_meta_file):
            checks["CSV文件存在"] = True
        
        # 2. 检查数据格式
        if checks["CSV文件存在"]:
            X_meta_df = pd.read_csv(x_meta_file, index_col=0)
            y_meta_series = pd.read_csv(y_meta_file, index_col=0).iloc[:, 0]
            
            if not X_meta_df.empty and not y_meta_series.empty:
                checks["数据格式正确"] = True
            
            # 3. 检查交互特征
            interaction_features = [col for col in X_meta_df.columns if '_IN_' in col]
            if len(interaction_features) > 0:
                checks["交互特征存在"] = True
        
        # 4. 检查训练函数
        try:
            from src.core.prediction import train_meta_model
            checks["训练函数可用"] = True
        except Exception:
            pass
        
        # 5. 检查配置
        try:
            import config
            meta_config = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', None)
            if isinstance(meta_config, dict) and meta_config.get(1, 1) >= 6:
                checks["配置正确"] = True
        except Exception:
            pass
        
    except Exception as e:
        logger.error(f"检查过程出错: {e}")
    
    # 报告结果
    logger.info("📋 训练就绪状态检查结果:")
    for check_name, status in checks.items():
        status_icon = "✅" if status else "❌"
        logger.info(f"  {status_icon} {check_name}")
    
    all_ready = all(checks.values())
    
    if all_ready:
        logger.info("🎉 所有检查通过，可以开始元模型训练！")
    else:
        failed_checks = [name for name, status in checks.items() if not status]
        logger.warning(f"⚠️ 以下检查未通过: {failed_checks}")
    
    return all_ready

def main():
    """主函数"""
    logger.info("🎯 开始测试元模型训练修复...")
    
    try:
        # 1. 测试元模型训练
        training_test_ok = test_meta_model_training()
        
        # 2. 检查训练就绪状态
        readiness_ok = check_training_readiness()
        
        if training_test_ok and readiness_ok:
            logger.info("🎉 元模型训练修复测试成功！")
            logger.info("🚀 现在可以重新运行元模型训练")
            return True
        else:
            logger.warning("⚠️ 部分测试失败")
            logger.info("测试结果:")
            logger.info(f"  训练测试: {'✅' if training_test_ok else '❌'}")
            logger.info(f"  就绪检查: {'✅' if readiness_ok else '❌'}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 元模型训练修复测试成功！")
        print("🚀 现在可以重新运行元模型训练")
    else:
        print("\n❌ 元模型训练修复测试失败！")
