"""
RTX 3070 GPU优化配置
"""

import os
import tensorflow as tf
import multiprocessing as mp

# 系统配置
CPU_COUNT = mp.cpu_count()

def setup_tensorflow_gpu():
    """配置TensorFlow GPU"""
    try:
        # 设置GPU内存增长
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                # 启用内存增长，避免占用全部显存
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                # 设置线程配置
                tf.config.threading.set_inter_op_parallelism_threads(4)
                tf.config.threading.set_intra_op_parallelism_threads(0)
                
                print(f"✅ TensorFlow GPU配置完成，检测到 {len(gpus)} 个GPU")
                return True
            except RuntimeError as e:
                print(f"GPU配置错误: {e}")
                return False
        else:
            print("未检测到GPU，使用CPU模式")
            return False
    except Exception as e:
        print(f"TensorFlow配置失败: {e}")
        return False

def setup_environment_variables():
    """设置环境变量"""
    # TensorFlow优化
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'  # 减少日志输出
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'  # 启用oneDNN优化
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'  # 异步GPU内存分配
    
    # CUDA优化
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第一个GPU
    
    # CPU优化
    os.environ['OMP_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['MKL_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['NUMBA_NUM_THREADS'] = str(CPU_COUNT)
    
    print(f"✅ 环境变量设置完成 - CPU核心: {CPU_COUNT}")

# LightGBM优化参数（CPU版本，针对AMD处理器优化）
LIGHTGBM_OPTIMIZED_PARAMS = {
    'device_type': 'cpu',  # 使用CPU，因为GPU版本安装困难
    'num_threads': CPU_COUNT,
    'force_row_wise': True,  # AMD处理器推荐
    'histogram_pool_size': -1,
    'max_bin': 255,
    'boost_from_average': True,
    'tree_learner': 'serial',
    'verbose': -1,
    # 性能优化参数
    'bagging_fraction': 0.8,
    'feature_fraction': 0.8,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'min_data_in_leaf': 20,
    'min_sum_hessian_in_leaf': 1e-3
}

# LSTM模型GPU配置
LSTM_GPU_CONFIG = {
    'use_gpu': True,
    'gpu_memory_growth': True,
    'mixed_precision': True,  # 使用混合精度训练
    'batch_size': 64,  # 适合RTX 3070的批次大小
    'sequence_length': 60,
    'hidden_units': 128,
    'dropout_rate': 0.2
}

def initialize_gpu_optimization():
    """初始化GPU优化"""
    print("🚀 初始化GPU优化...")
    
    # 设置环境变量
    setup_environment_variables()
    
    # 配置TensorFlow GPU
    gpu_success = setup_tensorflow_gpu()
    
    if gpu_success:
        print("✅ GPU优化初始化成功")
    else:
        print("⚠️  GPU不可用，使用CPU优化模式")
    
    return gpu_success

# 性能监控函数
def monitor_gpu_usage():
    """监控GPU使用情况"""
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            gpu = gpus[0]  # RTX 3070
            print(f"GPU使用率: {gpu.load*100:.1f}%")
            print(f"GPU内存: {gpu.memoryUsed}MB / {gpu.memoryTotal}MB")
            print(f"GPU温度: {gpu.temperature}°C")
    except ImportError:
        print("GPUtil未安装，无法监控GPU")
    except Exception as e:
        print(f"GPU监控失败: {e}")
