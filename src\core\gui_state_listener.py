#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI 状态监听器 - 监听 ApplicationState 变化并更新 GUI

实现状态驱动的 GUI 更新机制，GUI 监听 ApplicationState 的状态变化
来更新界面，而不是由后台线程直接调用 GUI 更新函数。

主要功能：
1. 监听 ApplicationState 的状态变化
2. 将状态变化转换为 GUI 更新操作
3. 与 GUI 更新管理器集成
4. 提供解耦的状态-GUI 映射
"""

import logging
import tkinter as tk
from typing import Dict, Any, Optional
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)


class GUIStateListener:
    """
    GUI 状态监听器
    
    监听 ApplicationState 的状态变化并更新 GUI
    """
    
    def __init__(self, gui_module=None):
        """
        初始化 GUI 状态监听器
        
        Args:
            gui_module: GUI 模块引用
        """
        self.gui_module = gui_module
        self._app_state = None
        self._is_active = False
        
        # 状态映射配置
        self._state_mappings = {
            'training_status': self._handle_training_status_change,
            'training_progress': self._handle_training_progress_change,
            'prediction_result': self._handle_prediction_result_change,
            'button_state': self._handle_button_state_change,
            'price_update': self._handle_price_update,
            'system_status': self._handle_system_status_change
        }
        
        logger.info("GUI状态监听器初始化完成")
    
    def set_gui_module(self, gui_module) -> None:
        """设置 GUI 模块引用"""
        self.gui_module = gui_module
        logger.info("GUI模块已设置")
    
    def start_listening(self, app_state) -> None:
        """
        开始监听 ApplicationState 的状态变化
        
        Args:
            app_state: ApplicationState 实例
        """
        if self._is_active:
            return
        
        self._app_state = app_state
        
        # 注册状态监听器
        for event_type, handler in self._state_mappings.items():
            app_state.add_state_listener(event_type, handler)
        
        self._is_active = True
        logger.info("GUI状态监听器已开始监听")
    
    def stop_listening(self) -> None:
        """停止监听状态变化"""
        if not self._is_active or not self._app_state:
            return
        
        # 移除状态监听器
        for event_type, handler in self._state_mappings.items():
            self._app_state.remove_state_listener(event_type, handler)
        
        self._is_active = False
        logger.info("GUI状态监听器已停止监听")
    
    def _handle_training_status_change(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理训练状态变化"""
        try:
            is_training = data.get('is_training', False)
            status = data.get('status')
            
            # 更新按钮状态
            if self.gui_module and hasattr(self.gui_module, 'set_button_state'):
                if is_training:
                    # 训练开始 - 禁用所有训练按钮
                    self.gui_module.set_button_state("train_any", tk.DISABLED)
                else:
                    # 训练结束 - 启用所有训练按钮
                    self.gui_module.set_button_state("train_any", tk.NORMAL)
            
            # 更新状态信息
            if self.gui_module and hasattr(self.gui_module, 'update_status'):
                if is_training:
                    current_target = status.current_target if status else "未知目标"
                    self.gui_module.update_status(f"正在训练: {current_target}", "neutral")
                else:
                    self.gui_module.update_status("训练完成，系统就绪", "success")
            
            logger.debug(f"处理训练状态变化: {'训练中' if is_training else '空闲'}")
            
        except Exception as e:
            logger.error(f"处理训练状态变化失败: {e}")
    
    def _handle_training_progress_change(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理训练进度变化"""
        try:
            status = data.get('status')
            updates = data.get('updates', {})
            
            if not status:
                return
            
            # 更新进度条
            if self.gui_module and hasattr(self.gui_module, 'update_progress'):
                progress_percent = status.progress * 100 if status.progress else 0
                self.gui_module.update_progress(progress_percent)
            
            # 更新状态信息
            if self.gui_module and hasattr(self.gui_module, 'update_status'):
                if status.current_target and status.status_message:
                    message = f"训练 {status.current_target}: {status.status_message}"
                    self.gui_module.update_status(message, "neutral")
            
            logger.debug(f"处理训练进度变化: {status.progress:.1%}")
            
        except Exception as e:
            logger.error(f"处理训练进度变化失败: {e}")
    
    def _handle_prediction_result_change(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理预测结果变化"""
        try:
            result = data.get('result')
            target_name = data.get('target_name')
            
            if not result or not target_name:
                return
            
            # 构建预测显示文本
            timestamp = result.timestamp.strftime('%H:%M:%S')
            prediction_text = (
                f"预测结果 ({timestamp})\n"
                f"类别: {result.prediction_class}\n"
                f"概率: {result.probabilities}\n"
            )
            
            if result.details:
                prediction_text += f"详情: {result.details}\n"
            
            # 更新预测显示
            if self.gui_module and hasattr(self.gui_module, 'update_prediction_display'):
                self.gui_module.update_prediction_display(target_name, prediction_text)
            
            logger.debug(f"处理预测结果变化: {target_name} -> {result.prediction_class}")
            
        except Exception as e:
            logger.error(f"处理预测结果变化失败: {e}")
    
    def _handle_button_state_change(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理按钮状态变化"""
        try:
            button_name = data.get('button_name')
            state = data.get('state')
            
            if button_name is None or state is None:
                return
            
            # 更新按钮状态
            if self.gui_module and hasattr(self.gui_module, 'set_button_state'):
                self.gui_module.set_button_state(button_name, state)
            
            logger.debug(f"处理按钮状态变化: {button_name} -> {state}")
            
        except Exception as e:
            logger.error(f"处理按钮状态变化失败: {e}")
    
    def _handle_price_update(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理价格更新"""
        try:
            price_str = data.get('price_str')
            color = data.get('color')
            
            if not price_str:
                return
            
            # 更新价格显示
            if self.gui_module and hasattr(self.gui_module, 'update_price_label'):
                self.gui_module.update_price_label(price_str, color or "#FFFFFF")
            
            logger.debug(f"处理价格更新: {price_str}")
            
        except Exception as e:
            logger.error(f"处理价格更新失败: {e}")
    
    def _handle_system_status_change(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理系统状态变化"""
        try:
            message = data.get('message')
            level = data.get('level', 'neutral')
            
            if not message:
                return
            
            # 更新系统状态
            if self.gui_module and hasattr(self.gui_module, 'update_status'):
                self.gui_module.update_status(message, level)
            
            logger.debug(f"处理系统状态变化: {message} ({level})")
            
        except Exception as e:
            logger.error(f"处理系统状态变化失败: {e}")


# 全局 GUI 状态监听器实例
_global_gui_state_listener = None


def get_gui_state_listener() -> GUIStateListener:
    """
    获取全局 GUI 状态监听器实例（单例模式）
    
    Returns:
        GUI 状态监听器实例
    """
    global _global_gui_state_listener
    
    if _global_gui_state_listener is None:
        _global_gui_state_listener = GUIStateListener()
    
    return _global_gui_state_listener


def initialize_gui_state_listener(gui_module, app_state) -> None:
    """
    初始化 GUI 状态监听器
    
    Args:
        gui_module: GUI 模块
        app_state: ApplicationState 实例
    """
    listener = get_gui_state_listener()
    listener.set_gui_module(gui_module)
    listener.start_listening(app_state)
    logger.info("GUI状态监听器已初始化并开始监听")


# 便捷的状态通知函数

def notify_button_state_change(app_state, button_name: str, state: int) -> None:
    """通知按钮状态变化"""
    app_state._notify_state_listeners('button_state', {
        'button_name': button_name,
        'state': state
    })


def notify_price_update(app_state, price_str: str, color: str) -> None:
    """通知价格更新"""
    app_state._notify_state_listeners('price_update', {
        'price_str': price_str,
        'color': color
    })


def notify_system_status_change(app_state, message: str, level: str = 'neutral') -> None:
    """通知系统状态变化"""
    app_state._notify_state_listeners('system_status', {
        'message': message,
        'level': level
    })
