# websocket_manager.py
"""
增强的WebSocket连接管理器
提供稳定的WebSocket连接、自动重连、健康检查和降级机制
"""

import threading
import time
import logging
import traceback
from typing import Optional, Callable, Dict, Any
from enum import Enum
try:
    from binance import ThreadedWebsocketManager, Client
except ImportError:
    try:
        from binance.websocket import ThreadedWebsocketManager
        from binance import Client
    except ImportError:
        print("⚠️ Binance WebSocket导入失败，将使用REST API模式")
        ThreadedWebsocketManager = None
        from binance import Client
import requests
import json

# 导入新的WebSocket初始化器
from .websocket_initializer import WebSocketInitializer, diagnose_websocket_environment

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"
    DEGRADED = "degraded"  # 降级到REST API模式

class WebSocketConnectionManager:
    """
    增强的WebSocket连接管理器
    
    功能：
    - 自动重连（指数退避）
    - 连接健康检查
    - 降级机制（WebSocket失败时使用REST API）
    - 连接状态监控
    - 智能错误处理
    """
    
    def __init__(self,
                 symbol: str = "BTCUSDT",
                 max_reconnect_attempts: int = 20,  # 增加重连次数
                 initial_retry_delay: float = 1.0,
                 max_retry_delay: float = 60.0,
                 health_check_interval: float = 30.0,
                 degraded_mode_poll_interval: float = 5.0,
                 proxy_url: str = 'http://127.0.0.1:7897'):
        
        self.symbol = symbol.upper()
        self.max_reconnect_attempts = max_reconnect_attempts
        self.initial_retry_delay = initial_retry_delay
        self.max_retry_delay = max_retry_delay
        self.health_check_interval = health_check_interval
        self.degraded_mode_poll_interval = degraded_mode_poll_interval
        self.proxy_url = proxy_url
        
        # 连接状态
        self.connection_state = ConnectionState.DISCONNECTED
        self.state_lock = threading.RLock()
        
        # 重连计数
        self.reconnect_attempts = 0
        self.consecutive_failures = 0
        self.last_successful_connection = 0
        
        # 数据存储
        self.latest_price = None
        self.last_price_update = 0
        self.price_lock = threading.RLock()
        
        # WebSocket相关
        self.twm: Optional[ThreadedWebsocketManager] = None
        self.stream_name: Optional[str] = None

        # 🔧 新增：WebSocket初始化器
        self.websocket_initializer = WebSocketInitializer(self.proxy_url)

        # REST API客户端（降级模式）
        self.rest_client: Optional[Client] = None
        
        # 线程控制
        self.stop_event = threading.Event()
        self.health_check_thread: Optional[threading.Thread] = None
        self.degraded_mode_thread: Optional[threading.Thread] = None
        
        # 回调函数
        self.price_callback: Optional[Callable] = None
        self.state_callback: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_reconnects': 0,
            'total_errors': 0,
            'uptime_start': time.time(),
            'last_error_time': 0,
            'degraded_mode_activations': 0
        }
        
        logger.info(f"WebSocket连接管理器初始化完成 - 交易对: {self.symbol}")
    
    def set_price_callback(self, callback: Callable[[float], None]):
        """设置价格更新回调函数"""
        self.price_callback = callback
    
    def set_state_callback(self, callback: Callable[[ConnectionState], None]):
        """设置状态变化回调函数"""
        self.state_callback = callback
    
    def _update_connection_state(self, new_state: ConnectionState):
        """更新连接状态"""
        with self.state_lock:
            if self.connection_state != new_state:
                old_state = self.connection_state
                self.connection_state = new_state
                logger.info(f"连接状态变化: {old_state.value} -> {new_state.value}")
                
                if self.state_callback:
                    try:
                        self.state_callback(new_state)
                    except Exception as e:
                        logger.error(f"状态回调函数执行失败: {e}")
    
    def _calculate_retry_delay(self) -> float:
        """计算重连延迟（指数退避）"""
        delay = self.initial_retry_delay * (2 ** min(self.reconnect_attempts, 6))
        return min(delay, self.max_retry_delay)
    
    def _handle_websocket_message(self, msg: Dict[str, Any]):
        """处理WebSocket消息"""
        try:
            if msg.get('e') == 'error':
                error_msg = msg.get('m', 'Unknown WebSocket error')
                logger.error(f"WebSocket错误: {error_msg}")
                self.stats['total_errors'] += 1
                self.stats['last_error_time'] = time.time()
                self._handle_connection_error(f"WebSocket error: {error_msg}")
                return
            
            # 处理价格数据
            if msg.get('e') == '24hrTicker' and msg.get('s') == self.symbol:
                try:
                    new_price = float(msg['c'])
                    self._update_price(new_price)
                except (KeyError, ValueError) as e:
                    logger.warning(f"价格数据解析失败: {e}")
            
            elif msg.get('e') == 'aggTrade' and msg.get('s') == self.symbol:
                try:
                    new_price = float(msg['p'])
                    self._update_price(new_price)
                except (KeyError, ValueError) as e:
                    logger.warning(f"交易数据解析失败: {e}")
                    
        except Exception as e:
            logger.error(f"消息处理异常: {e}")
            traceback.print_exc()
    
    def _update_price(self, price: float):
        """更新价格数据"""
        with self.price_lock:
            self.latest_price = price
            self.last_price_update = time.time()
            
        # 重置连续失败计数
        self.consecutive_failures = 0
        self.last_successful_connection = time.time()
        
        # 调用价格回调
        if self.price_callback:
            try:
                self.price_callback(price)
            except Exception as e:
                logger.error(f"价格回调函数执行失败: {e}")
    
    def _handle_connection_error(self, error_msg: str):
        """处理连接错误"""
        self.consecutive_failures += 1
        logger.warning(f"连接错误 (连续失败: {self.consecutive_failures}): {error_msg}")
        
        # 如果连续失败次数过多，考虑降级
        if self.consecutive_failures >= 3:
            logger.warning("连续失败次数过多，准备重连或降级")
            self._attempt_reconnect()
    
    def _initialize_twm(self) -> bool:
        """初始化ThreadedWebsocketManager - 使用专用初始化器避免事件循环冲突"""
        try:
            logger.info("使用WebSocketInitializer进行安全TWM初始化")

            # 诊断当前环境
            diagnosis = diagnose_websocket_environment()
            logger.info(f"WebSocket环境诊断: {diagnosis}")

            # 使用专用初始化器
            self.twm = self.websocket_initializer.initialize_twm_safe()

            if self.twm:
                logger.info("TWM初始化成功")
                return True
            else:
                logger.error("TWM初始化失败")
                return False

        except Exception as e:
            logger.error(f"TWM初始化流程失败: {e}")
            traceback.print_exc()
            return False
    
    def _start_websocket_stream(self) -> bool:
        """启动WebSocket数据流 - 改进的启动逻辑"""
        try:
            if not self.twm:
                if not self._initialize_twm():
                    logger.error("TWM初始化失败，无法启动WebSocket流")
                    return False

            # 🔧 改进的TWM启动逻辑 - 使用专用初始化器
            if not self.twm.is_alive():
                logger.info("启动ThreadedWebsocketManager...")

                # 使用专用初始化器启动TWM
                if not self.websocket_initializer.start_twm_safe(self.twm):
                    logger.error("TWM启动失败")
                    return False

                logger.info("TWM启动成功")

            # 启动ticker流
            logger.info(f"启动 {self.symbol} 的ticker流...")
            try:
                self.stream_name = self.twm.start_symbol_ticker_socket(
                    symbol=self.symbol,
                    callback=self._handle_websocket_message
                )
            except RuntimeError as re:
                if "failed to initialize after" in str(re):
                    logger.error(f"WebSocket初始化超时: {re}")
                    return False
                else:
                    raise re

            if self.stream_name:
                logger.info(f"WebSocket流启动成功: {self.symbol} (Stream ID: {self.stream_name})")
                return True
            else:
                logger.error("WebSocket流启动失败: 返回None")
                return False

        except Exception as e:
            logger.error(f"WebSocket流启动异常: {e}")
            traceback.print_exc()
            return False

    def _attempt_reconnect(self):
        """尝试重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"达到最大重连次数 ({self.max_reconnect_attempts})，切换到降级模式")
            self._activate_degraded_mode()
            return

        self._update_connection_state(ConnectionState.RECONNECTING)
        self.reconnect_attempts += 1
        self.stats['total_reconnects'] += 1

        retry_delay = self._calculate_retry_delay()
        logger.info(f"第 {self.reconnect_attempts} 次重连尝试，延迟 {retry_delay:.1f} 秒")

        # 清理现有连接
        self._cleanup_websocket()

        # 等待重连延迟
        time.sleep(retry_delay)

        # 尝试重新连接
        if self._start_websocket_stream():
            logger.info("重连成功")
            self._update_connection_state(ConnectionState.CONNECTED)
            self.reconnect_attempts = 0  # 重置重连计数
            self.consecutive_failures = 0
        else:
            logger.warning(f"第 {self.reconnect_attempts} 次重连失败")
            # 递归尝试下一次重连
            threading.Thread(target=self._attempt_reconnect, daemon=True).start()

    def _cleanup_websocket(self):
        """清理WebSocket连接"""
        try:
            if self.twm and self.twm.is_alive():
                if self.stream_name:
                    self.twm.stop_socket(self.stream_name)
                    self.stream_name = None
                self.twm.stop()
                self.twm = None
        except Exception as e:
            logger.warning(f"WebSocket清理异常: {e}")

    def _initialize_rest_client(self) -> bool:
        """初始化REST API客户端（降级模式）"""
        try:
            self.rest_client = Client()
            # 测试连接
            self.rest_client.get_server_time()
            logger.info("REST API客户端初始化成功")
            return True
        except Exception as e:
            logger.error(f"REST API客户端初始化失败: {e}")
            return False

    def _activate_degraded_mode(self):
        """激活降级模式（使用REST API轮询）"""
        logger.warning("激活降级模式 - 使用REST API轮询获取价格")
        self._update_connection_state(ConnectionState.DEGRADED)
        self.stats['degraded_mode_activations'] += 1

        if not self.rest_client:
            if not self._initialize_rest_client():
                logger.error("降级模式初始化失败")
                self._update_connection_state(ConnectionState.FAILED)
                return

        # 启动降级模式线程
        if not self.degraded_mode_thread or not self.degraded_mode_thread.is_alive():
            self.degraded_mode_thread = threading.Thread(
                target=self._degraded_mode_worker,
                daemon=True
            )
            self.degraded_mode_thread.start()

    def _degraded_mode_worker(self):
        """降级模式工作线程（REST API轮询）"""
        logger.info("降级模式工作线程启动")

        while not self.stop_event.is_set() and self.connection_state == ConnectionState.DEGRADED:
            try:
                # 使用REST API获取价格
                ticker = self.rest_client.get_symbol_ticker(symbol=self.symbol)
                price = float(ticker['price'])
                self._update_price(price)

                # 等待下次轮询
                time.sleep(self.degraded_mode_poll_interval)

            except Exception as e:
                logger.error(f"降级模式价格获取失败: {e}")
                time.sleep(self.degraded_mode_poll_interval * 2)  # 错误时延长等待时间

        logger.info("降级模式工作线程结束")

    def _health_check_worker(self):
        """健康检查工作线程"""
        logger.info("健康检查线程启动")

        while not self.stop_event.is_set():
            try:
                time.sleep(self.health_check_interval)

                if self.stop_event.is_set():
                    break

                current_time = time.time()

                # 检查价格更新时间
                with self.price_lock:
                    time_since_last_update = current_time - self.last_price_update

                # 如果超过2倍健康检查间隔没有价格更新，认为连接有问题
                if time_since_last_update > self.health_check_interval * 2:
                    logger.warning(f"价格数据超时 ({time_since_last_update:.1f}秒)，可能存在连接问题")

                    if self.connection_state == ConnectionState.CONNECTED:
                        self._handle_connection_error("价格数据超时")

                # 检查连接状态
                if self.connection_state == ConnectionState.CONNECTED:
                    if self.twm and not self.twm.is_alive():
                        logger.warning("TWM进程已停止，尝试重连")
                        self._handle_connection_error("TWM进程停止")

            except Exception as e:
                logger.error(f"健康检查异常: {e}")

        logger.info("健康检查线程结束")

    def start(self) -> bool:
        """启动连接管理器"""
        logger.info(f"启动WebSocket连接管理器 - {self.symbol}")

        self.stop_event.clear()
        self._update_connection_state(ConnectionState.CONNECTING)

        # 尝试启动WebSocket连接
        if self._start_websocket_stream():
            self._update_connection_state(ConnectionState.CONNECTED)

            # 启动健康检查线程
            self.health_check_thread = threading.Thread(
                target=self._health_check_worker,
                daemon=True
            )
            self.health_check_thread.start()

            logger.info("WebSocket连接管理器启动成功")
            return True
        else:
            logger.warning("WebSocket连接失败，尝试降级模式")
            self._activate_degraded_mode()
            return True  # 降级模式也算启动成功

    def stop(self):
        """停止连接管理器"""
        logger.info("停止WebSocket连接管理器")

        self.stop_event.set()
        self._update_connection_state(ConnectionState.DISCONNECTED)

        # 清理WebSocket连接
        self._cleanup_websocket()

        # 等待线程结束
        if self.health_check_thread and self.health_check_thread.is_alive():
            self.health_check_thread.join(timeout=5)

        if self.degraded_mode_thread and self.degraded_mode_thread.is_alive():
            self.degraded_mode_thread.join(timeout=5)

        logger.info("WebSocket连接管理器已停止")

    def get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        with self.price_lock:
            return self.latest_price

    def get_connection_state(self) -> ConnectionState:
        """获取连接状态"""
        with self.state_lock:
            return self.connection_state

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats['uptime_start']

        return {
            **self.stats,
            'uptime_seconds': uptime,
            'current_state': self.connection_state.value,
            'reconnect_attempts': self.reconnect_attempts,
            'consecutive_failures': self.consecutive_failures,
            'last_price_update': self.last_price_update,
            'time_since_last_update': current_time - self.last_price_update if self.last_price_update > 0 else 0
        }

    def force_reconnect(self):
        """强制重连"""
        logger.info("强制重连请求")
        self.reconnect_attempts = 0  # 重置重连计数
        self._attempt_reconnect()

    def reset_to_websocket(self):
        """尝试从降级模式恢复到WebSocket模式"""
        if self.connection_state == ConnectionState.DEGRADED:
            logger.info("尝试从降级模式恢复到WebSocket模式")
            self.reconnect_attempts = 0
            self.consecutive_failures = 0

            # 停止降级模式
            self._update_connection_state(ConnectionState.CONNECTING)

            # 尝试重新启动WebSocket
            if self._start_websocket_stream():
                logger.info("成功恢复到WebSocket模式")
                self._update_connection_state(ConnectionState.CONNECTED)
                return True
            else:
                logger.warning("恢复WebSocket模式失败，继续降级模式")
                self._activate_degraded_mode()
                return False
        return True
