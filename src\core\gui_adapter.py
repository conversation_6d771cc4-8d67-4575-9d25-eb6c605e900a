#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI适配器 - 事件驱动的GUI更新

监听事件系统的事件并更新GUI界面，实现核心逻辑与GUI的完全解耦。
GUI适配器作为事件系统和GUI模块之间的桥梁。

主要功能：
1. 监听各种事件类型
2. 将事件转换为GUI更新操作
3. 线程安全的GUI更新
4. 错误处理和日志记录
"""

import logging
import threading
import tkinter as tk
from typing import Dict, Any, Optional
from datetime import datetime

from .event_system import (
    EventHandler, Event, EventType,
    StatusUpdateEvent, PredictionResultEvent, TrainingProgressEvent,
    SystemErrorEvent, SignalSentEvent,
    subscribe_event, get_event_bus
)

# 设置日志
logger = logging.getLogger(__name__)


class GUIAdapter(EventHandler):
    """
    GUI适配器
    
    监听事件并更新GUI界面，实现事件驱动的GUI更新
    """
    
    def __init__(self, gui_module=None):
        """
        初始化GUI适配器
        
        Args:
            gui_module: GUI模块引用（可选，支持延迟绑定）
        """
        self.gui_module = gui_module
        self._gui_available = False
        self._update_queue = []
        self._queue_lock = threading.Lock()
        
        # 注册支持的事件类型
        self._supported_events = [
            EventType.STATUS_UPDATE,
            EventType.PREDICTION_RESULT,
            EventType.TRAINING_PROGRESS,
            EventType.SYSTEM_ERROR,
            EventType.SYSTEM_WARNING,
            EventType.SYSTEM_INFO,
            EventType.SIGNAL_SENT,
            EventType.BUTTON_STATE_CHANGE,
            EventType.PROGRESS_UPDATE
        ]
        
        # 自动订阅事件
        self._subscribe_to_events()
        
        logger.info("GUI适配器初始化完成")
    
    def set_gui_module(self, gui_module) -> None:
        """
        设置GUI模块引用
        
        Args:
            gui_module: GUI模块
        """
        self.gui_module = gui_module
        self._gui_available = self._check_gui_availability()
        logger.info(f"GUI模块已设置，可用性: {self._gui_available}")
    
    def _check_gui_availability(self) -> bool:
        """检查GUI是否可用"""
        if not self.gui_module:
            return False
        
        try:
            # 检查GUI根窗口是否存在且有效
            gui_root = getattr(self.gui_module, '_gui_root', None)
            if gui_root and hasattr(gui_root, 'winfo_exists'):
                return gui_root.winfo_exists()
            return False
        except Exception as e:
            logger.debug(f"GUI可用性检查失败: {e}")
            return False
    
    def _subscribe_to_events(self) -> None:
        """订阅支持的事件类型"""
        for event_type in self._supported_events:
            subscribe_event(event_type, self)
        logger.debug(f"已订阅 {len(self._supported_events)} 种事件类型")
    
    def get_supported_event_types(self) -> list:
        """获取支持的事件类型"""
        return self._supported_events.copy()
    
    def handle_event(self, event: Event) -> None:
        """
        处理事件
        
        Args:
            event: 事件对象
        """
        try:
            # 检查GUI可用性
            if not self._check_gui_availability():
                logger.debug(f"GUI不可用，跳过事件处理: {event.event_type.value}")
                return
            
            # 根据事件类型分发处理
            if event.event_type == EventType.STATUS_UPDATE:
                self._handle_status_update(event)
            elif event.event_type == EventType.PREDICTION_RESULT:
                self._handle_prediction_result(event)
            elif event.event_type == EventType.TRAINING_PROGRESS:
                self._handle_training_progress(event)
            elif event.event_type == EventType.SYSTEM_ERROR:
                self._handle_system_error(event)
            elif event.event_type == EventType.SIGNAL_SENT:
                self._handle_signal_sent(event)
            elif event.event_type == EventType.BUTTON_STATE_CHANGE:
                self._handle_button_state_change(event)
            elif event.event_type == EventType.PROGRESS_UPDATE:
                self._handle_progress_update(event)
            else:
                logger.debug(f"未处理的事件类型: {event.event_type.value}")
                
        except Exception as e:
            logger.error(f"处理事件时发生错误: {e}")
    
    def _handle_status_update(self, event: StatusUpdateEvent) -> None:
        """处理状态更新事件"""
        try:
            message = event.data.get('message', '')
            level = event.data.get('level', 'neutral')
            
            if hasattr(self.gui_module, 'update_status'):
                self._safe_gui_update(self.gui_module.update_status, message, level)
            
            logger.debug(f"状态更新: {message} ({level})")
            
        except Exception as e:
            logger.error(f"处理状态更新事件失败: {e}")
    
    def _handle_prediction_result(self, event: PredictionResultEvent) -> None:
        """处理预测结果事件"""
        try:
            target_name = event.data.get('target_name', '')
            prediction_label = event.data.get('prediction_label', '')
            probabilities = event.data.get('probabilities', [])
            confidence = event.data.get('confidence', 0.0)
            signal_sent = event.data.get('signal_sent')
            current_price = event.data.get('current_price')

            # 构建显示文本
            timestamp = event.timestamp.strftime('%H:%M:%S')
            display_text = self._build_prediction_display_text(
                target_name, prediction_label, probabilities,
                confidence, signal_sent, timestamp, current_price
            )

            if hasattr(self.gui_module, 'update_prediction_display'):
                self._safe_gui_update(
                    self.gui_module.update_prediction_display,
                    target_name, display_text
                )

            logger.debug(f"预测结果更新: {target_name} -> {prediction_label}")
            
        except Exception as e:
            logger.error(f"处理预测结果事件失败: {e}")
    
    def _handle_training_progress(self, event: TrainingProgressEvent) -> None:
        """处理训练进度事件"""
        try:
            progress = event.data.get('progress', 0.0)
            stage = event.data.get('stage', '')
            details = event.data.get('details', '')
            
            # 更新进度条
            if hasattr(self.gui_module, 'update_progress'):
                self._safe_gui_update(self.gui_module.update_progress, progress * 100)
            
            # 更新状态信息
            status_message = f"{stage}: {details}" if details else stage
            if status_message and hasattr(self.gui_module, 'update_status'):
                self._safe_gui_update(self.gui_module.update_status, status_message, "neutral")
            
            logger.debug(f"训练进度更新: {progress:.1%} - {stage}")
            
        except Exception as e:
            logger.error(f"处理训练进度事件失败: {e}")
    
    def _handle_system_error(self, event: SystemErrorEvent) -> None:
        """处理系统错误事件"""
        try:
            error_message = event.data.get('error_message', '')
            
            if hasattr(self.gui_module, 'update_status'):
                self._safe_gui_update(self.gui_module.update_status, error_message, "error")
            
            logger.error(f"系统错误: {error_message}")
            
        except Exception as e:
            logger.error(f"处理系统错误事件失败: {e}")
    
    def _handle_signal_sent(self, event: SignalSentEvent) -> None:
        """处理信号发送事件"""
        try:
            target_name = event.data.get('target_name', '')
            signal_type = event.data.get('signal_type', '')
            success = event.data.get('success', False)
            response_message = event.data.get('response_message', '')
            
            # 构建状态消息
            if success:
                message = f"信号发送成功: {target_name} -> {signal_type}"
                level = "success"
            else:
                message = f"信号发送失败: {target_name} -> {signal_type}"
                if response_message:
                    message += f" ({response_message})"
                level = "error"
            
            if hasattr(self.gui_module, 'update_status'):
                self._safe_gui_update(self.gui_module.update_status, message, level)
            
            logger.info(f"信号发送事件: {message}")
            
        except Exception as e:
            logger.error(f"处理信号发送事件失败: {e}")
    
    def _handle_button_state_change(self, event: Event) -> None:
        """处理按钮状态变更事件"""
        try:
            button_name = event.data.get('button_name', '')
            state = event.data.get('state', tk.NORMAL)
            
            if hasattr(self.gui_module, 'set_button_state'):
                self._safe_gui_update(self.gui_module.set_button_state, button_name, state)
            
            logger.debug(f"按钮状态更新: {button_name} -> {state}")
            
        except Exception as e:
            logger.error(f"处理按钮状态变更事件失败: {e}")
    
    def _handle_progress_update(self, event: Event) -> None:
        """处理进度更新事件"""
        try:
            progress = event.data.get('progress', 0.0)
            
            if hasattr(self.gui_module, 'update_progress'):
                self._safe_gui_update(self.gui_module.update_progress, progress)
            
            logger.debug(f"进度更新: {progress:.1f}%")
            
        except Exception as e:
            logger.error(f"处理进度更新事件失败: {e}")
    
    def _build_prediction_display_text(self, target_name: str, prediction_label: str,
                                     probabilities: list, confidence: float,
                                     signal_sent: Optional[str], timestamp: str,
                                     current_price: Optional[float] = None) -> str:
        """构建预测显示文本"""
        try:
            lines = [
                f"预测结果 ({timestamp})",
                "=" * 30,
                f"目标: {target_name}",
            ]

            # 添加价格信息
            if current_price is not None:
                lines.append(f"💰 当前价格: ${current_price:.2f}")

            lines.append(f"预测: {prediction_label}")

            if probabilities:
                if len(probabilities) == 3:
                    lines.append(f"概率: [跌:{probabilities[0]:.2%}, 涨:{probabilities[1]:.2%}, 中:{probabilities[2]:.2%}]")
                else:
                    prob_str = ", ".join([f"{p:.2%}" for p in probabilities])
                    lines.append(f"概率: [{prob_str}]")

            if confidence > 0:
                lines.append(f"置信度: {confidence:.2%}")

            if signal_sent:
                lines.append(f"信号: {signal_sent}")

            lines.append("=" * 30)

            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"构建预测显示文本失败: {e}")
            return f"预测结果显示错误: {e}"
    
    def _safe_gui_update(self, update_func: callable, *args, **kwargs) -> None:
        """
        线程安全的GUI更新
        
        Args:
            update_func: GUI更新函数
            *args: 位置参数
            **kwargs: 关键字参数
        """
        try:
            if not self._check_gui_availability():
                return
            
            gui_root = getattr(self.gui_module, '_gui_root', None)
            if gui_root and hasattr(gui_root, 'after'):
                # 使用tkinter的after方法确保在主线程中执行
                gui_root.after(0, lambda: self._execute_gui_update(update_func, *args, **kwargs))
            else:
                # 直接执行（如果在主线程中）
                self._execute_gui_update(update_func, *args, **kwargs)
                
        except Exception as e:
            logger.error(f"安全GUI更新失败: {e}")
    
    def _execute_gui_update(self, update_func: callable, *args, **kwargs) -> None:
        """
        执行GUI更新
        
        Args:
            update_func: GUI更新函数
            *args: 位置参数
            **kwargs: 关键字参数
        """
        try:
            update_func(*args, **kwargs)
        except tk.TclError as e:
            if "application has been destroyed" not in str(e):
                logger.error(f"GUI更新TclError: {e}")
        except Exception as e:
            logger.error(f"GUI更新执行失败: {e}")


# 全局GUI适配器实例
_global_gui_adapter = None
_gui_adapter_lock = threading.Lock()


def get_gui_adapter() -> GUIAdapter:
    """
    获取全局GUI适配器实例（单例模式）
    
    Returns:
        GUI适配器实例
    """
    global _global_gui_adapter
    
    if _global_gui_adapter is None:
        with _gui_adapter_lock:
            if _global_gui_adapter is None:
                _global_gui_adapter = GUIAdapter()
    
    return _global_gui_adapter


def initialize_gui_adapter(gui_module) -> None:
    """
    初始化GUI适配器
    
    Args:
        gui_module: GUI模块
    """
    adapter = get_gui_adapter()
    adapter.set_gui_module(gui_module)
    logger.info("GUI适配器已初始化")


# 便捷函数
def publish_button_state_change(button_name: str, state: int) -> None:
    """发布按钮状态变更事件"""
    from .event_system import publish_event, Event, EventType
    
    event = Event(
        event_type=EventType.BUTTON_STATE_CHANGE,
        source="gui_adapter",
        data={
            'button_name': button_name,
            'state': state
        }
    )
    publish_event(event)


def publish_progress_update(progress: float) -> None:
    """发布进度更新事件"""
    from .event_system import publish_event, Event, EventType
    
    event = Event(
        event_type=EventType.PROGRESS_UPDATE,
        source="gui_adapter",
        data={
            'progress': progress
        }
    )
    publish_event(event)
