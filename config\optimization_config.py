#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三道屏障和safe_fill_nans优化配置
"""

# 🚀 三道屏障优化配置
TRIPLE_BARRIER_OPTIMIZATION_CONFIG = {
    # 是否启用向量化版本（默认启用）
    'triple_barrier_vectorized': True,
    
    # 三道屏障基本参数
    'enable_triple_barrier': True,
    'enable_dynamic_thresholds': True,
    
    # ATR参数
    'atr_period': 14,
    'atr_multiplier_profit': 2.5,  # 止盈屏障的ATR倍数
    'atr_multiplier_loss': 1.5,    # 止损屏障的ATR倍数
    
    # 性能优化参数
    'vectorized_chunk_size': 10000,  # 大数据集分块处理大小
    'enable_parallel_processing': False,  # 是否启用并行处理（实验性）
}

# 🚀 safe_fill_nans优化配置
SAFE_FILL_NANS_OPTIMIZATION_CONFIG = {
    # 默认使用优化版本
    'use_historical_only': True,
    
    # 性能优化参数
    'chunk_size': 50000,  # 大数据集分块处理大小
    'enable_validation': True,  # 是否启用结果验证
    
    # 填充策略
    'default_fill_strategies': {
        'price_columns': 'ffill',      # 价格列使用前向填充
        'volume_columns': 'zero',      # 成交量列使用0填充
        'indicator_columns': 'ffill',  # 技术指标使用前向填充
        'ratio_columns': 'one',        # 比率列使用1填充
    }
}

# 🚀 性能监控配置
PERFORMANCE_MONITORING_CONFIG = {
    # 是否启用性能监控
    'enable_monitoring': True,
    
    # 性能阈值（秒）
    'performance_thresholds': {
        'safe_fill_nans': {
            'warning_threshold': 0.1,   # 超过0.1秒发出警告
            'error_threshold': 1.0,     # 超过1秒记录错误
        },
        'triple_barrier': {
            'warning_threshold': 1.0,   # 超过1秒发出警告
            'error_threshold': 10.0,    # 超过10秒记录错误
        }
    },
    
    # 是否记录详细的性能统计
    'detailed_stats': True,
    
    # 性能日志文件
    'performance_log_file': 'performance_optimization.log'
}

# 🚀 自动回退配置
AUTO_FALLBACK_CONFIG = {
    # 是否启用自动回退
    'enable_auto_fallback': True,
    
    # 回退条件
    'fallback_conditions': {
        'max_execution_time': 30.0,    # 最大执行时间（秒）
        'max_memory_usage': 2048,      # 最大内存使用（MB）
        'max_error_rate': 0.1,         # 最大错误率
    },
    
    # 回退策略
    'fallback_strategies': {
        'triple_barrier': 'legacy',    # 三道屏障回退到传统方法
        'safe_fill_nans': 'simple',    # safe_fill_nans回退到简单方法
    }
}

# 🚀 数据验证配置
DATA_VALIDATION_CONFIG = {
    # 是否启用数据验证
    'enable_validation': True,
    
    # 验证规则
    'validation_rules': {
        'check_nan_count': True,       # 检查NaN数量
        'check_inf_values': True,      # 检查无穷大值
        'check_data_types': True,      # 检查数据类型
        'check_index_integrity': True, # 检查索引完整性
        'check_value_ranges': True,    # 检查数值范围
    },
    
    # 验证阈值
    'validation_thresholds': {
        'max_nan_ratio': 0.05,         # 最大NaN比例
        'max_inf_count': 0,            # 最大无穷大值数量
        'min_valid_samples': 100,      # 最小有效样本数
    }
}

# 🚀 调试配置
DEBUG_CONFIG = {
    # 是否启用调试模式
    'enable_debug': False,
    
    # 调试选项
    'debug_options': {
        'log_execution_time': True,    # 记录执行时间
        'log_memory_usage': True,      # 记录内存使用
        'save_intermediate_results': False,  # 保存中间结果
        'compare_with_legacy': False,  # 与传统方法对比
    },
    
    # 调试输出目录
    'debug_output_dir': 'debug_optimization'
}

def get_optimization_config():
    """获取完整的优化配置"""
    return {
        'triple_barrier': TRIPLE_BARRIER_OPTIMIZATION_CONFIG,
        'safe_fill_nans': SAFE_FILL_NANS_OPTIMIZATION_CONFIG,
        'performance_monitoring': PERFORMANCE_MONITORING_CONFIG,
        'auto_fallback': AUTO_FALLBACK_CONFIG,
        'data_validation': DATA_VALIDATION_CONFIG,
        'debug': DEBUG_CONFIG
    }

def apply_optimization_to_target_config(target_config):
    """将优化配置应用到目标配置中"""
    opt_config = get_optimization_config()
    
    # 应用三道屏障优化
    target_config.update(opt_config['triple_barrier'])
    
    # 应用性能监控
    target_config['performance_monitoring'] = opt_config['performance_monitoring']
    
    # 应用自动回退
    target_config['auto_fallback'] = opt_config['auto_fallback']
    
    # 应用数据验证
    target_config['data_validation'] = opt_config['data_validation']
    
    return target_config

def get_recommended_settings_for_data_size(data_size):
    """根据数据大小推荐优化设置"""
    if data_size < 1000:
        # 小数据集：可以使用传统方法
        return {
            'triple_barrier_vectorized': False,
            'enable_parallel_processing': False,
            'chunk_size': data_size
        }
    elif data_size < 10000:
        # 中等数据集：使用向量化但不分块
        return {
            'triple_barrier_vectorized': True,
            'enable_parallel_processing': False,
            'chunk_size': data_size
        }
    else:
        # 大数据集：使用所有优化
        return {
            'triple_barrier_vectorized': True,
            'enable_parallel_processing': True,
            'chunk_size': min(10000, data_size // 4)
        }

# 🚀 使用示例
if __name__ == "__main__":
    # 获取优化配置
    config = get_optimization_config()
    print("优化配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 根据数据大小推荐设置
    for size in [500, 5000, 50000]:
        settings = get_recommended_settings_for_data_size(size)
        print(f"\n数据大小 {size} 的推荐设置: {settings}")
