#!/usr/bin/env python3
# data_processing_optimizer.py
"""
数据处理优化器 - 优化data_utils.py中的向量化操作和MTFA处理
"""

import pandas as pd
import numpy as np
import time
import os
import sys
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DataProcessingOptimizer:
    """数据处理优化器"""
    
    def __init__(self):
        self.optimization_results = {}
        
    def optimize_mtfa_features(self, 
                              main_data: pd.DataFrame,
                              target_config: Dict,
                              binance_client=None) -> pd.DataFrame:
        """
        优化MTFA特征生成 - 一次性获取和高效合并
        
        Args:
            main_data: 主时间框架数据
            target_config: 目标配置
            binance_client: Binance客户端
            
        Returns:
            包含MTFA特征的数据
        """
        print("🚀 开始优化MTFA特征生成...")
        
        if not target_config.get('enable_mtfa', False):
            return main_data
        
        start_time = time.time()
        
        try:
            # 1. 一次性获取所有MTFA数据
            mtfa_data = self._batch_fetch_mtfa_data(main_data, target_config, binance_client)
            
            # 2. 高效特征合并
            result_data = self._efficient_mtfa_merge(main_data, mtfa_data, target_config)
            
            end_time = time.time()
            
            print(f"✅ MTFA特征优化完成，耗时: {end_time - start_time:.2f}秒")
            print(f"   原始列数: {len(main_data.columns)}")
            print(f"   优化后列数: {len(result_data.columns)}")
            
            return result_data
            
        except Exception as e:
            print(f"❌ MTFA特征优化失败: {e}")
            return main_data
    
    def _batch_fetch_mtfa_data(self,
                              main_data: pd.DataFrame,
                              target_config: Dict,
                              binance_client) -> Dict[str, pd.DataFrame]:
        """批量获取MTFA数据"""
        
        mtfa_timeframes = target_config.get('mtfa_timeframes', ['30m', '1h', '4h'])
        main_timeframe = target_config.get('interval', '15m')
        symbol = target_config.get('symbol', 'BTCUSDT')
        
        mtfa_data = {}
        
        # 计算需要的数据范围
        data_start = main_data.index[0]
        data_end = main_data.index[-1]
        
        # 添加缓冲时间
        buffer_hours = 48  # 48小时缓冲
        extended_start = data_start - pd.Timedelta(hours=buffer_hours)
        extended_end = data_end + pd.Timedelta(hours=24)
        
        for timeframe in mtfa_timeframes:
            if timeframe == main_timeframe:
                continue
                
            print(f"  📡 获取 {timeframe} 数据...")
            
            try:
                # 尝试从主数据重采样（如果可能）
                if self._can_resample(main_timeframe, timeframe):
                    print(f"    🔄 从 {main_timeframe} 重采样生成 {timeframe}")
                    resampled_data = self._resample_timeframe(main_data, timeframe)
                    if resampled_data is not None and not resampled_data.empty:
                        mtfa_data[timeframe] = resampled_data
                        continue
                
                # 从API获取
                if binance_client:
                    import data_utils
                    
                    # 计算需要的K线数量
                    required_bars = self._calculate_required_bars(
                        extended_start, extended_end, timeframe
                    )
                    
                    # 🚀 检查分层数据策略开关
                    import config
                    enable_layered_strategy = getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True)

                    if enable_layered_strategy:
                        mtfa_limit = getattr(config, 'MTFA_DATA_LIMIT', 2000)
                        actual_limit = min(required_bars, mtfa_limit)
                    else:
                        # 使用传统限制
                        actual_limit = min(required_bars, 1000)

                    tf_data = data_utils.fetch_binance_history(
                        binance_client=binance_client,
                        symbol=symbol,
                        interval=timeframe,
                        limit=actual_limit
                    )
                    
                    if tf_data is not None and not tf_data.empty:
                        # 过滤到需要的时间范围
                        filtered_data = tf_data[
                            (tf_data.index >= extended_start) &
                            (tf_data.index <= extended_end)
                        ]
                        mtfa_data[timeframe] = filtered_data
                        print(f"    ✅ API获取成功: {len(filtered_data)} 条数据")
                
            except Exception as e:
                print(f"    ❌ {timeframe} 数据获取失败: {e}")
        
        return mtfa_data
    
    def _can_resample(self, from_tf: str, to_tf: str) -> bool:
        """判断是否可以重采样"""
        tf_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        from_min = tf_minutes.get(from_tf, 15)
        to_min = tf_minutes.get(to_tf, 60)
        
        return to_min > from_min and to_min % from_min == 0
    
    def _resample_timeframe(self, data: pd.DataFrame, target_tf: str) -> Optional[pd.DataFrame]:
        """重采样到目标时间框架"""
        
        resample_rules = {
            '30m': '30T', '1h': '1H', '4h': '4H', '1d': '1D'
        }
        
        rule = resample_rules.get(target_tf)
        if not rule:
            return None
        
        try:
            resampled = data.resample(rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'qav': 'sum' if 'qav' in data.columns else 'first',
                'n': 'sum' if 'n' in data.columns else 'first',
                'tbbav': 'sum' if 'tbbav' in data.columns else 'first',
                'tbqav': 'sum' if 'tbqav' in data.columns else 'first'
            })
            
            return resampled.dropna()
            
        except Exception as e:
            print(f"    ❌ 重采样失败: {e}")
            return None
    
    def _calculate_required_bars(self, start_time, end_time, timeframe: str) -> int:
        """计算需要的K线数量"""
        tf_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        interval_minutes = tf_minutes.get(timeframe, 60)
        total_minutes = (end_time - start_time).total_seconds() / 60
        
        return int(total_minutes / interval_minutes) + 100  # 额外缓冲
    
    def _efficient_mtfa_merge(self,
                             main_data: pd.DataFrame,
                             mtfa_data: Dict[str, pd.DataFrame],
                             target_config: Dict) -> pd.DataFrame:
        """高效MTFA特征合并"""
        
        result_data = main_data.copy()
        
        for timeframe, tf_data in mtfa_data.items():
            if tf_data.empty:
                continue
                
            print(f"    🔧 合并 {timeframe} 特征...")
            
            try:
                # 生成该时间框架的特征
                tf_config = target_config.copy()
                tf_config['interval'] = timeframe
                tf_config['enable_mtfa'] = False  # 避免递归
                
                import data_utils
                tf_features = data_utils.add_classification_features(tf_data.copy(), tf_config)
                
                if tf_features is not None and not tf_features.empty:
                    # 高效特征合并
                    merged_data = self._vectorized_feature_merge(
                        result_data, tf_features, timeframe
                    )
                    result_data = merged_data
                    print(f"    ✅ {timeframe}: 合并完成")
                
            except Exception as e:
                print(f"    ❌ {timeframe} 特征合并失败: {e}")
        
        return result_data
    
    def _vectorized_feature_merge(self,
                                 main_data: pd.DataFrame,
                                 tf_features: pd.DataFrame,
                                 timeframe: str) -> pd.DataFrame:
        """向量化特征合并"""
        
        # 排除基础OHLCV列
        exclude_cols = {'open', 'high', 'low', 'close', 'volume', 'qav', 'n', 'tbbav', 'tbqav'}
        feature_cols = [col for col in tf_features.columns if col not in exclude_cols]
        
        # 重命名特征列（使用后缀命名与data_utils.py保持一致）
        rename_dict = {col: f"{col}_{timeframe}" for col in feature_cols}
        tf_features_renamed = tf_features[feature_cols].rename(columns=rename_dict)
        
        # 使用merge_asof进行高效时间对齐
        merged = pd.merge_asof(
            main_data.sort_index(),
            tf_features_renamed.sort_index(),
            left_index=True,
            right_index=True,
            direction='backward'
        )
        
        return merged
    
    def optimize_feature_calculation(self, data: pd.DataFrame, target_config: Dict) -> pd.DataFrame:
        """优化特征计算的向量化操作"""
        
        print("🚀 开始优化特征计算...")
        start_time = time.time()
        
        try:
            # 1. 数据类型优化
            optimized_data = self._optimize_data_types(data)
            
            # 2. 向量化技术指标计算
            optimized_data = self._vectorized_technical_indicators(optimized_data, target_config)
            
            # 3. 向量化价格变动特征
            optimized_data = self._vectorized_price_features(optimized_data, target_config)
            
            # 4. 向量化时间特征
            optimized_data = self._vectorized_time_features(optimized_data, target_config)
            
            end_time = time.time()
            print(f"✅ 特征计算优化完成，耗时: {end_time - start_time:.2f}秒")
            
            return optimized_data
            
        except Exception as e:
            print(f"❌ 特征计算优化失败: {e}")
            return data
    
    def _optimize_data_types(self, data: pd.DataFrame) -> pd.DataFrame:
        """优化数据类型以节省内存和提升速度"""
        
        optimized_data = data.copy()
        
        # 将数值列转换为float32
        numeric_columns = optimized_data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if optimized_data[col].dtype == 'float64':
                optimized_data[col] = optimized_data[col].astype('float32')
            elif optimized_data[col].dtype == 'int64':
                # 检查是否可以转换为更小的整数类型
                col_min = optimized_data[col].min()
                col_max = optimized_data[col].max()
                
                if col_min >= -32768 and col_max <= 32767:
                    optimized_data[col] = optimized_data[col].astype('int16')
                elif col_min >= -2147483648 and col_max <= 2147483647:
                    optimized_data[col] = optimized_data[col].astype('int32')
        
        return optimized_data
    
    def _vectorized_technical_indicators(self, data: pd.DataFrame, config: Dict) -> pd.DataFrame:
        """向量化技术指标计算"""
        
        if not config.get('enable_ta', True):
            return data
        
        result_data = data.copy()
        
        try:
            # RSI - 向量化计算
            if config.get('rsi_period', 14):
                rsi_period = config['rsi_period']
                delta = result_data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
                rs = gain / loss
                result_data[f'RSI_{rsi_period}'] = 100 - (100 / (1 + rs))
            
            # 移动平均 - 向量化计算
            if config.get('hma_period', 23):
                hma_period = config['hma_period']
                wma1 = result_data['close'].rolling(window=hma_period//2).mean()
                wma2 = result_data['close'].rolling(window=hma_period).mean()
                result_data[f'HMA_{hma_period}'] = (2 * wma1 - wma2).rolling(window=int(np.sqrt(hma_period))).mean()
            
            # ATR - 向量化计算
            if config.get('atr_period', 16):
                atr_period = config['atr_period']
                high_low = result_data['high'] - result_data['low']
                high_close = np.abs(result_data['high'] - result_data['close'].shift())
                low_close = np.abs(result_data['low'] - result_data['close'].shift())
                true_range = np.maximum(high_low, np.maximum(high_close, low_close))
                result_data[f'ATR_{atr_period}'] = true_range.rolling(window=atr_period).mean()
            
        except Exception as e:
            print(f"⚠️  技术指标计算部分失败: {e}")
        
        return result_data
    
    def _vectorized_price_features(self, data: pd.DataFrame, config: Dict) -> pd.DataFrame:
        """向量化价格变动特征"""
        
        if not config.get('enable_price_change', True):
            return data
        
        result_data = data.copy()
        
        try:
            periods = config.get('price_change_periods', [1, 2, 3, 5, 10])
            
            for period in periods:
                # 价格变动率
                result_data[f'price_change_{period}'] = result_data['close'].pct_change(period)
                
                # 价格变动幅度
                result_data[f'price_range_{period}'] = (
                    result_data['high'].rolling(window=period).max() - 
                    result_data['low'].rolling(window=period).min()
                ) / result_data['close']
                
                # 成交量变动
                if 'volume' in result_data.columns:
                    result_data[f'volume_change_{period}'] = result_data['volume'].pct_change(period)
        
        except Exception as e:
            print(f"[WARN] 价格特征计算部分失败: {e}")
        
        return result_data
    
    def _vectorized_time_features(self, data: pd.DataFrame, config: Dict) -> pd.DataFrame:
        """向量化时间特征"""
        
        if not config.get('enable_time', True):
            return data
        
        result_data = data.copy()
        
        try:
            # 提取时间特征
            result_data['hour'] = result_data.index.hour
            result_data['day_of_week'] = result_data.index.dayofweek
            result_data['day_of_month'] = result_data.index.day
            result_data['month'] = result_data.index.month
            
            # 三角函数时间特征（如果启用）
            if config.get('enable_time_trigonometric', False):
                result_data['hour_sin'] = np.sin(2 * np.pi * result_data['hour'] / 24)
                result_data['hour_cos'] = np.cos(2 * np.pi * result_data['hour'] / 24)
                result_data['dow_sin'] = np.sin(2 * np.pi * result_data['day_of_week'] / 7)
                result_data['dow_cos'] = np.cos(2 * np.pi * result_data['day_of_week'] / 7)
        
        except Exception as e:
            print(f"⚠️  时间特征计算部分失败: {e}")
        
        return result_data
    
    def benchmark_optimization(self, data: pd.DataFrame, target_config: Dict) -> Dict:
        """基准测试优化效果"""
        
        print("📊 开始基准测试...")
        
        results = {
            'original_method': {},
            'optimized_method': {},
            'improvement': {}
        }
        
        # 测试原始方法（模拟）
        print("  测试原始方法...")
        start_time = time.time()
        
        # 这里应该调用原始的特征生成方法
        # original_result = original_feature_generation(data, target_config)
        
        original_time = time.time() - start_time
        results['original_method'] = {
            'time': original_time,
            'memory_mb': self._get_memory_usage()
        }
        
        # 测试优化方法
        print("  测试优化方法...")
        start_time = time.time()
        
        optimized_result = self.optimize_feature_calculation(data, target_config)
        
        optimized_time = time.time() - start_time
        results['optimized_method'] = {
            'time': optimized_time,
            'memory_mb': self._get_memory_usage(),
            'output_shape': optimized_result.shape
        }
        
        # 计算改进
        if original_time > 0:
            results['improvement'] = {
                'speed_improvement': original_time / optimized_time,
                'time_saved': original_time - optimized_time
            }
        
        return results
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0

def main():
    """主函数"""
    print("🚀 数据处理优化器")
    print("=" * 50)
    
    optimizer = DataProcessingOptimizer()
    
    # 创建测试数据
    print("📊 创建测试数据...")
    timestamps = pd.date_range('2024-01-01', periods=1000, freq='15T')
    
    np.random.seed(42)
    test_data = pd.DataFrame({
        'open': np.random.uniform(49000, 51000, 1000),
        'high': np.random.uniform(50000, 52000, 1000),
        'low': np.random.uniform(48000, 50000, 1000),
        'close': np.random.uniform(49000, 51000, 1000),
        'volume': np.random.uniform(100, 1000, 1000),
        'qav': np.random.uniform(1000000, 10000000, 1000),
        'n': np.random.randint(100, 1000, 1000),
        'tbbav': np.random.uniform(500000, 5000000, 1000),
        'tbqav': np.random.uniform(500000, 5000000, 1000)
    }, index=timestamps)
    
    # 测试配置
    test_config = {
        'enable_ta': True,
        'enable_price_change': True,
        'enable_time': True,
        'enable_time_trigonometric': True,
        'rsi_period': 14,
        'hma_period': 23,
        'atr_period': 16,
        'price_change_periods': [1, 2, 3, 5, 10]
    }
    
    # 运行基准测试
    benchmark_results = optimizer.benchmark_optimization(test_data, test_config)
    
    print("\n📊 基准测试结果:")
    print(f"  优化方法耗时: {benchmark_results['optimized_method']['time']:.2f}秒")
    print(f"  内存使用: {benchmark_results['optimized_method']['memory_mb']:.1f}MB")
    print(f"  输出形状: {benchmark_results['optimized_method']['output_shape']}")
    
    if 'improvement' in benchmark_results:
        print(f"  性能提升: {benchmark_results['improvement']['speed_improvement']:.1f}倍")

if __name__ == "__main__":
    main()
