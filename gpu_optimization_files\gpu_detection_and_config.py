#!/usr/bin/env python3
"""
GPU检测和配置脚本
检测RTX 3070并配置TensorFlow使用GPU
"""

import os
import sys
import subprocess

def setup_cuda_environment():
    """设置CUDA环境变量"""
    print("🔧 设置CUDA环境变量...")
    
    # 设置CUDA路径
    cuda_path = r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8"
    if os.path.exists(cuda_path):
        os.environ['CUDA_PATH'] = cuda_path
        os.environ['CUDA_HOME'] = cuda_path
        
        # 添加到PATH
        cuda_bin = os.path.join(cuda_path, 'bin')
        cuda_lib = os.path.join(cuda_path, 'lib', 'x64')
        
        current_path = os.environ.get('PATH', '')
        if cuda_bin not in current_path:
            os.environ['PATH'] = f"{cuda_bin};{current_path}"
        if cuda_lib not in current_path:
            os.environ['PATH'] = f"{cuda_lib};{os.environ['PATH']}"
        
        print(f"✅ CUDA环境设置完成: {cuda_path}")
    else:
        print("⚠️  CUDA路径未找到，可能影响GPU检测")

def test_tensorflow_gpu():
    """测试TensorFlow GPU支持"""
    print("\n🧪 测试TensorFlow GPU支持...")
    
    try:
        # 设置环境变量
        setup_cuda_environment()
        
        import tensorflow as tf
        
        print(f"TensorFlow版本: {tf.__version__}")
        
        # 检查构建信息
        build_info = tf.sysconfig.get_build_info()
        print(f"CUDA构建: {build_info.get('is_cuda_build', False)}")
        
        # 尝试检测GPU
        print("\n🔍 检测GPU设备...")
        
        # 方法1: 使用list_physical_devices
        try:
            gpus = tf.config.list_physical_devices('GPU')
            if gpus:
                print(f"✅ 方法1检测到 {len(gpus)} 个GPU:")
                for i, gpu in enumerate(gpus):
                    print(f"  GPU {i}: {gpu}")
                return True
            else:
                print("❌ 方法1未检测到GPU")
        except Exception as e:
            print(f"❌ 方法1检测失败: {e}")
        
        # 方法2: 使用experimental方法
        try:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                print(f"✅ 方法2检测到 {len(gpus)} 个GPU:")
                for i, gpu in enumerate(gpus):
                    print(f"  GPU {i}: {gpu}")
                return True
            else:
                print("❌ 方法2未检测到GPU")
        except Exception as e:
            print(f"❌ 方法2检测失败: {e}")
        
        # 方法3: 检查CUDA可用性
        try:
            cuda_available = tf.test.is_built_with_cuda()
            gpu_available = tf.test.is_gpu_available()
            print(f"CUDA构建支持: {cuda_available}")
            print(f"GPU可用: {gpu_available}")
            
            if gpu_available:
                return True
        except Exception as e:
            print(f"❌ 方法3检测失败: {e}")
        
        return False
        
    except ImportError as e:
        print(f"❌ TensorFlow导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False

def create_optimized_tensorflow_config():
    """创建优化的TensorFlow配置"""
    print("\n📝 创建TensorFlow优化配置...")
    
    config_content = '''"""
TensorFlow GPU优化配置
适用于RTX 3070 + AMD处理器
"""

import os
import tensorflow as tf

def setup_tensorflow_optimization():
    """设置TensorFlow优化配置"""
    print("🚀 初始化TensorFlow优化...")
    
    # 设置CUDA环境变量
    cuda_path = r"C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8"
    if os.path.exists(cuda_path):
        os.environ['CUDA_PATH'] = cuda_path
        os.environ['CUDA_HOME'] = cuda_path
    
    # TensorFlow优化环境变量
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'  # 减少日志
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'  # 启用oneDNN
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'  # 异步GPU内存分配
    
    try:
        # 检测GPU
        gpus = tf.config.list_physical_devices('GPU')
        
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备")
            
            try:
                # 配置GPU内存增长
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                # 设置线程配置
                tf.config.threading.set_inter_op_parallelism_threads(4)
                tf.config.threading.set_intra_op_parallelism_threads(0)  # 使用所有核心
                
                print("✅ GPU优化配置完成")
                return True
                
            except RuntimeError as e:
                print(f"⚠️  GPU配置警告: {e}")
                print("GPU可能已被其他进程使用，将使用CPU模式")
                return False
        else:
            print("⚠️  未检测到GPU，使用CPU优化模式")
            
            # CPU优化配置
            tf.config.threading.set_inter_op_parallelism_threads(4)
            tf.config.threading.set_intra_op_parallelism_threads(16)  # AMD 16核
            
            return False
            
    except Exception as e:
        print(f"❌ TensorFlow配置失败: {e}")
        return False

def create_lstm_model_with_gpu(input_shape, num_classes=2):
    """创建GPU优化的LSTM模型"""
    try:
        # 尝试使用GPU
        gpus = tf.config.list_physical_devices('GPU')
        device = '/GPU:0' if gpus else '/CPU:0'
        
        print(f"使用设备: {device}")
        
        with tf.device(device):
            model = tf.keras.Sequential([
                tf.keras.layers.LSTM(128, return_sequences=True, input_shape=input_shape),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.2),
                
                tf.keras.layers.LSTM(64, return_sequences=False),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.2),
                
                tf.keras.layers.Dense(32, activation='relu'),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(16, activation='relu'),
                
                tf.keras.layers.Dense(
                    num_classes,
                    activation='softmax' if num_classes > 2 else 'sigmoid'
                )
            ])
        
        # 编译模型
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy' if num_classes > 2 else 'binary_crossentropy',
            metrics=['accuracy']
        )
        
        return model
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return None

# 自动初始化
if __name__ != "__main__":
    setup_tensorflow_optimization()
'''
    
    with open('tensorflow_gpu_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ TensorFlow配置文件已创建: tensorflow_gpu_config.py")

def create_simple_gpu_test():
    """创建简单的GPU测试脚本"""
    print("\n📝 创建GPU测试脚本...")
    
    test_content = '''#!/usr/bin/env python3
"""
简单的GPU测试脚本
"""

from tensorflow_gpu_config import setup_tensorflow_optimization
import tensorflow as tf
import numpy as np
import time

def simple_gpu_test():
    """简单的GPU测试"""
    print("🧪 简单GPU测试")
    print("-" * 30)
    
    # 初始化优化配置
    gpu_available = setup_tensorflow_optimization()
    
    # 创建测试数据
    size = 1000
    a = tf.random.normal([size, size])
    b = tf.random.normal([size, size])
    
    # CPU测试
    with tf.device('/CPU:0'):
        start = time.time()
        cpu_result = tf.matmul(a, b)
        cpu_time = time.time() - start
        print(f"CPU计算时间: {cpu_time:.4f}秒")
    
    # GPU测试（如果可用）
    if gpu_available:
        try:
            with tf.device('/GPU:0'):
                start = time.time()
                gpu_result = tf.matmul(a, b)
                gpu_time = time.time() - start
                print(f"GPU计算时间: {gpu_time:.4f}秒")
                
                if cpu_time > 0 and gpu_time > 0:
                    speedup = cpu_time / gpu_time
                    print(f"🚀 GPU加速比: {speedup:.2f}x")
        except Exception as e:
            print(f"❌ GPU测试失败: {e}")
    else:
        print("⚠️  GPU不可用，跳过GPU测试")

if __name__ == "__main__":
    simple_gpu_test()
'''
    
    with open('simple_gpu_test.py', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ GPU测试脚本已创建: simple_gpu_test.py")

def main():
    """主函数"""
    print("🔍 GPU检测和配置")
    print("=" * 40)
    
    # 测试TensorFlow GPU支持
    gpu_detected = test_tensorflow_gpu()
    
    # 创建配置文件
    create_optimized_tensorflow_config()
    create_simple_gpu_test()
    
    print(f"\n📊 检测结果:")
    print(f"GPU支持: {'✅ 是' if gpu_detected else '❌ 否'}")
    
    print(f"\n📋 下一步:")
    print("1. 运行: python simple_gpu_test.py")
    print("2. 在你的代码中导入: from tensorflow_gpu_config import setup_tensorflow_optimization")
    print("3. 如果GPU不可用，TensorFlow会自动使用CPU优化")
    
    if not gpu_detected:
        print(f"\n💡 GPU不可用的可能原因:")
        print("- TensorFlow版本不支持CUDA 12.8")
        print("- 需要安装兼容的cuDNN版本")
        print("- 驱动版本不兼容")
        print("- 但不用担心，CPU优化也很强大！")

if __name__ == "__main__":
    main()
