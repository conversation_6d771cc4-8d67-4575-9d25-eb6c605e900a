#!/usr/bin/env python3
"""
增强的动态样本权重模块
实现细化的权重分配策略：时间衰减、波动率权重、成交量权重
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class EnhancedSampleWeighter:
    """增强的样本权重计算器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化权重计算器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
        # 默认权重策略配置
        self.default_config = {
            'enable_time_decay': True,
            'enable_volatility_weighting': True,
            'enable_volume_weighting': True,
            'enable_market_state_weighting': True,
            
            # 时间衰减配置
            'time_decay': {
                'decay_rate': 0.95,           # 衰减率
                'min_weight': 0.1,            # 最小权重
                'max_weight': 2.0,            # 最大权重
            },
            
            # 波动率权重配置
            'volatility_weighting': {
                'lookback_period': 20,        # 回望期
                'extreme_vol_threshold': 2.0, # 极端波动阈值（标准差倍数）
                'low_vol_multiplier': 1.5,    # 低波动权重倍数
                'high_vol_multiplier': 0.3,   # 高波动权重倍数
            },
            
            # 成交量权重配置
            'volume_weighting': {
                'lookback_period': 20,        # 回望期
                'volume_threshold': 1.5,      # 放量阈值（平均成交量倍数）
                'high_volume_multiplier': 2.0, # 放量权重倍数
                'low_volume_multiplier': 0.8,  # 缩量权重倍数
            },
            
            # 权重组合策略
            'combination_method': 'weighted_average',  # 'weighted_average', 'multiply', 'max'
            'strategy_weights': {
                'time_decay': 0.3,
                'volatility': 0.3,
                'volume': 0.2,
                'market_state': 0.2
            }
        }
        
        # 合并用户配置
        self.effective_config = self._merge_configs(self.default_config, self.config)
        
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """递归合并配置字典"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def calculate_enhanced_sample_weights(self, 
                                        df: pd.DataFrame, 
                                        target_config: Dict[str, Any],
                                        target_name: str) -> np.ndarray:
        """
        计算增强的样本权重
        
        Args:
            df: 历史数据DataFrame，包含OHLCV数据
            target_config: 目标配置
            target_name: 目标名称
            
        Returns:
            样本权重数组
        """
        logger.info(f"开始计算 {target_name} 的增强样本权重...")
        
        if df.empty:
            logger.warning(f"数据为空，返回均匀权重")
            return np.ones(len(df))
        
        # 确保必要的列存在
        required_columns = ['close', 'high', 'low', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.warning(f"缺少必要列 {missing_columns}，返回均匀权重")
            return np.ones(len(df))
        
        weight_components = {}
        
        # 1. 时间衰减权重
        if self.effective_config['enable_time_decay']:
            weight_components['time_decay'] = self._calculate_time_decay_weights(df, target_name)
        
        # 2. 波动率权重
        if self.effective_config['enable_volatility_weighting']:
            weight_components['volatility'] = self._calculate_volatility_weights(df, target_name)
        
        # 3. 成交量权重
        if self.effective_config['enable_volume_weighting']:
            weight_components['volume'] = self._calculate_volume_weights(df, target_name)
        
        # 4. 市场状态权重（使用现有的市场状态识别）
        if self.effective_config['enable_market_state_weighting']:
            weight_components['market_state'] = self._calculate_market_state_weights(df, target_config, target_name)
        
        # 组合权重
        if weight_components:
            combined_weights = self._combine_weight_components(weight_components, target_name)
        else:
            logger.warning(f"没有启用任何权重策略，返回均匀权重")
            combined_weights = np.ones(len(df))
        
        # 标准化权重
        final_weights = self._normalize_weights(combined_weights)
        
        logger.info(f"{target_name} 权重计算完成，范围: [{final_weights.min():.4f}, {final_weights.max():.4f}]，均值: {final_weights.mean():.4f}")
        
        return final_weights
    
    def _calculate_time_decay_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算时间衰减权重
        越近的数据权重越高
        """
        config = self.effective_config['time_decay']
        decay_rate = config['decay_rate']
        min_weight = config['min_weight']
        max_weight = config['max_weight']
        
        n_samples = len(df)
        
        # 计算衰减权重：最新的数据权重最高
        decay_weights = np.array([decay_rate ** (n_samples - i - 1) for i in range(n_samples)])
        
        # 标准化到指定范围
        decay_weights = decay_weights / decay_weights.max()  # 归一化到[0,1]
        decay_weights = min_weight + (max_weight - min_weight) * decay_weights
        
        logger.debug(f"[{target_name}] 时间衰减权重计算完成，范围: [{decay_weights.min():.4f}, {decay_weights.max():.4f}]")
        
        return decay_weights
    
    def _calculate_volatility_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算波动率权重
        极端高波动或低波动时期的样本权重调整
        """
        config = self.effective_config['volatility_weighting']
        lookback_period = config['lookback_period']
        extreme_vol_threshold = config['extreme_vol_threshold']
        low_vol_multiplier = config['low_vol_multiplier']
        high_vol_multiplier = config['high_vol_multiplier']
        
        # 计算滚动波动率（使用收盘价的对数收益率）
        returns = np.log(df['close'] / df['close'].shift(1)).dropna()
        rolling_vol = returns.rolling(window=lookback_period, min_periods=5).std()
        
        # 计算波动率的Z-score
        vol_mean = rolling_vol.mean()
        vol_std = rolling_vol.std()
        
        if vol_std == 0:
            logger.warning(f"[{target_name}] 波动率标准差为0，返回均匀权重")
            return np.ones(len(df))
        
        vol_zscore = (rolling_vol - vol_mean) / vol_std
        
        # 初始化权重
        vol_weights = np.ones(len(df))
        
        # 对齐索引（因为计算收益率会丢失第一个数据点）
        if len(vol_zscore) < len(df):
            vol_zscore = pd.concat([pd.Series([0]), vol_zscore]).reset_index(drop=True)
        
        # 应用波动率权重策略
        for i in range(len(vol_weights)):
            if i < len(vol_zscore):
                z_score = vol_zscore.iloc[i]
                if z_score < -extreme_vol_threshold:  # 低波动
                    vol_weights[i] = low_vol_multiplier
                elif z_score > extreme_vol_threshold:  # 高波动
                    vol_weights[i] = high_vol_multiplier
                # 正常波动保持1.0
        
        logger.debug(f"[{target_name}] 波动率权重计算完成，低波动样本: {np.sum(vol_weights == low_vol_multiplier)}, "
                    f"高波动样本: {np.sum(vol_weights == high_vol_multiplier)}")
        
        return vol_weights
    
    def _calculate_volume_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算成交量权重
        放量突破时的样本权重更高
        """
        config = self.effective_config['volume_weighting']
        lookback_period = config['lookback_period']
        volume_threshold = config['volume_threshold']
        high_volume_multiplier = config['high_volume_multiplier']
        low_volume_multiplier = config['low_volume_multiplier']
        
        # 计算滚动平均成交量
        rolling_avg_volume = df['volume'].rolling(window=lookback_period, min_periods=5).mean()
        
        # 计算成交量比率
        volume_ratio = df['volume'] / rolling_avg_volume
        
        # 初始化权重
        volume_weights = np.ones(len(df))
        
        # 应用成交量权重策略
        high_volume_mask = volume_ratio > volume_threshold
        low_volume_mask = volume_ratio < (1 / volume_threshold)
        
        volume_weights[high_volume_mask] = high_volume_multiplier
        volume_weights[low_volume_mask] = low_volume_multiplier
        
        # 处理NaN值（前几个数据点可能没有足够的历史数据）
        volume_weights = np.nan_to_num(volume_weights, nan=1.0)
        
        logger.debug(f"[{target_name}] 成交量权重计算完成，放量样本: {np.sum(high_volume_mask)}, "
                    f"缩量样本: {np.sum(low_volume_mask)}")
        
        return volume_weights

    def _calculate_market_state_weights(self, df: pd.DataFrame, target_config: Dict[str, Any], target_name: str) -> np.ndarray:
        """
        计算市场状态权重
        使用现有的市场状态识别逻辑
        """
        try:
            # 尝试使用现有的市场状态识别函数
            from src.core.data_utils import _identify_market_regimes

            C = df['close']
            H = df['high']
            L = df['low']

            market_states = _identify_market_regimes(df, target_config, C, H, L, f"{target_name}_weight")

            # 🎯 V9.0优化市场状态权重映射 - 策略二：精准调控
            state_weight_map = {
                # === 高价值状态（权重提升）===
                'strong_trend_up': 3.5,           # 强趋势：提升权重
                'strong_trend_down': 3.5,         # 强趋势：提升权重
                'strong_uptrend': 3.5,            # 强趋势：提升权重
                'strong_downtrend': 3.5,          # 强趋势：提升权重
                'low_vol_sideways': 2.5,          # 🚀 低波动盘整：大幅提升权重（突破前兆）

                # === 中等价值状态 ===
                'moderate_uptrend': 2.2,          # 中等趋势：略微提升
                'moderate_downtrend': 2.2,        # 中等趋势：略微提升
                'normal_trend': 1.0,              # 正常趋势：保持基准
                'sideways_normal': 0.8,           # 正常横盘：略微降低

                # === 低价值状态（权重降低）===
                'high_vol_sideways': 0.05,       # 🔻 高波动盘整：大幅降低权重（噪音状态）
                'extreme_volatility': 0.02,      # 🔻 极端波动：最低权重（趋势末期）
                'panic_selling': 0.03,           # 🔻 恐慌性抛售：极低权重（情绪化噪音）
                'bubble_state': 0.03,            # 🔻 泡沫状态：极低权重（非理性状态）
            }

            # 应用权重映射
            market_weights = np.array([state_weight_map.get(state, 1.0) for state in market_states])

            # 🚀 策略二增强：引入成交量作为乘数因子
            try:
                if 'volume' in df.columns and len(df) > 20:
                    # 计算成交量相对于平均值的比率
                    volume_avg_period = 20
                    vol_avg = df['volume'].rolling(window=volume_avg_period, min_periods=10).mean()
                    volume_vs_avg = (df['volume'] / vol_avg.replace(0, 1e-9)).fillna(1.0)

                    # 成交量乘数因子计算
                    volume_multipliers = np.ones(len(market_weights))  # 确保长度匹配

                    # 确保volume_vs_avg长度与market_weights匹配
                    min_len = min(len(volume_vs_avg), len(market_weights))

                    for i in range(min_len):
                        vol_ratio = volume_vs_avg.iloc[i]
                        if vol_ratio > 2.0:  # 放量突破
                            volume_multipliers[i] = 1.5  # 放量突破给予1.5倍权重
                        elif vol_ratio > 1.5:  # 适度放量
                            volume_multipliers[i] = 1.2  # 适度放量给予1.2倍权重
                        elif vol_ratio < 0.5:  # 缩量
                            volume_multipliers[i] = 0.7  # 缩量降低权重
                        # 正常成交量保持1.0

                    # 应用成交量乘数
                    market_weights = market_weights * volume_multipliers

                    logger.debug(f"[{target_name}] 成交量乘数应用完成，"
                               f"放量样本: {np.sum(volume_multipliers > 1.0)}, "
                               f"缩量样本: {np.sum(volume_multipliers < 1.0)}")

            except Exception as e_vol:
                logger.warning(f"[{target_name}] 成交量乘数计算失败: {e_vol}，使用基础权重")

            logger.debug(f"[{target_name}] 市场状态权重计算完成，状态分布: {pd.Series(market_states).value_counts().to_dict()}")

            return market_weights

        except Exception as e:
            logger.warning(f"[{target_name}] 市场状态权重计算失败: {e}，返回均匀权重")
            return np.ones(len(df))

    def _combine_weight_components(self, weight_components: Dict[str, np.ndarray], target_name: str) -> np.ndarray:
        """
        组合不同的权重组件
        """
        method = self.effective_config['combination_method']
        strategy_weights = self.effective_config['strategy_weights']

        if method == 'weighted_average':
            # 加权平均组合
            combined_weights = np.zeros(len(list(weight_components.values())[0]))
            total_weight = 0

            for component_name, weights in weight_components.items():
                component_weight = strategy_weights.get(component_name, 1.0)
                combined_weights += component_weight * weights
                total_weight += component_weight

            if total_weight > 0:
                combined_weights /= total_weight

        elif method == 'multiply':
            # 乘积组合
            combined_weights = np.ones(len(list(weight_components.values())[0]))
            for weights in weight_components.values():
                combined_weights *= weights

        elif method == 'max':
            # 最大值组合
            weight_matrix = np.column_stack(list(weight_components.values()))
            combined_weights = np.max(weight_matrix, axis=1)

        else:
            logger.warning(f"未知的权重组合方法: {method}，使用加权平均")
            # 默认使用加权平均
            combined_weights = np.mean(np.column_stack(list(weight_components.values())), axis=1)

        logger.debug(f"[{target_name}] 权重组合完成，方法: {method}，组件数: {len(weight_components)}")

        return combined_weights

    def _normalize_weights(self, weights: np.ndarray) -> np.ndarray:
        """
        标准化权重
        确保权重为正数且总和合理
        """
        # 确保权重为正数
        weights = np.maximum(weights, 0.01)

        # 标准化权重，使其均值为1
        mean_weight = np.mean(weights)
        if mean_weight > 0:
            weights = weights / mean_weight

        # 限制权重范围，避免极端值
        weights = np.clip(weights, 0.01, 10.0)

        return weights


def calculate_enhanced_sample_weights(df: pd.DataFrame,
                                    target_config: Dict[str, Any],
                                    target_name: str,
                                    weighting_config: Optional[Dict[str, Any]] = None) -> np.ndarray:
    """
    便捷函数：计算增强的样本权重

    Args:
        df: 历史数据DataFrame
        target_config: 目标配置
        target_name: 目标名称
        weighting_config: 权重配置

    Returns:
        样本权重数组
    """
    weighter = EnhancedSampleWeighter(weighting_config)
    return weighter.calculate_enhanced_sample_weights(df, target_config, target_name)
