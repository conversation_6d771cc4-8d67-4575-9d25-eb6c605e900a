#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 配置管理迁移示例

展示如何从直接字典访问迁移到类型安全的配置包装器。
"""

import sys
import os
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from config import get_target_config_wrapper, get_target_config
from src.core.config_validator import ConfigurationError


# =============================================================================
# 示例 1: 数据处理函数的迁移
# =============================================================================

def process_data_old_way(target_config: Dict[str, Any]) -> Dict[str, Any]:
    """❌ 旧方式：直接字典访问（不推荐）"""
    
    # 直接字典访问，容易出错
    symbol = target_config['symbol']  # 可能抛出 KeyError
    interval = target_config['interval']  # 可能抛出 KeyError
    
    # 使用 get 方法，但无类型安全
    enable_ta = target_config.get('enable_ta', True)  # 无类型验证
    data_limit = target_config.get('data_fetch_limit', 1000)  # 无类型验证
    periods = target_config.get('prediction_periods', [1])  # 无类型验证
    
    # 处理逻辑...
    result = {
        'symbol': symbol,
        'interval': interval,
        'enable_ta': enable_ta,
        'data_limit': data_limit,
        'periods': periods
    }
    
    return result


def process_data_new_way(target_name: str) -> Dict[str, Any]:
    """✅ 新方式：类型安全的配置包装器（推荐）"""
    
    try:
        # 获取类型安全的配置包装器
        wrapper = get_target_config_wrapper(target_name)
        
        # 类型安全的配置访问
        symbol = wrapper.get_str('symbol', required=True)
        interval = wrapper.get_str('interval', required=True)
        enable_ta = wrapper.get_bool('enable_ta', default=True)
        data_limit = wrapper.get_int('data_fetch_limit', default=1000)
        periods = wrapper.get_list('prediction_periods', default=[1])
        
        # 处理逻辑...
        result = {
            'symbol': symbol,
            'interval': interval,
            'enable_ta': enable_ta,
            'data_limit': data_limit,
            'periods': periods
        }
        
        return result
        
    except ConfigurationError as e:
        print(f"配置错误: {e}")
        raise
    except ValueError as e:
        print(f"目标未找到: {e}")
        raise


# =============================================================================
# 示例 2: 模型训练函数的迁移
# =============================================================================

def train_model_old_way(target_config: Dict[str, Any]) -> Dict[str, Any]:
    """❌ 旧方式：直接字典访问"""
    
    # 直接访问，可能出错
    n_estimators = target_config.get('n_estimators', 100)
    learning_rate = target_config.get('learning_rate', 0.1)
    max_depth = target_config.get('max_depth', -1)
    
    # 布尔值处理不够严格
    early_stopping = target_config.get('early_stopping_rounds', 50)
    
    return {
        'n_estimators': n_estimators,
        'learning_rate': learning_rate,
        'max_depth': max_depth,
        'early_stopping': early_stopping
    }


def train_model_new_way(target_name: str) -> Dict[str, Any]:
    """✅ 新方式：类型安全的配置包装器"""
    
    wrapper = get_target_config_wrapper(target_name)
    
    # 类型安全的参数获取
    n_estimators = wrapper.get_int('n_estimators', default=100)
    learning_rate = wrapper.get_float('learning_rate', default=0.1)
    max_depth = wrapper.get_int('max_depth', default=-1)
    early_stopping = wrapper.get_int('early_stopping_rounds', default=50)
    
    # 验证参数合理性
    if n_estimators <= 0:
        raise ValueError(f"n_estimators 必须大于 0，当前值: {n_estimators}")
    if learning_rate <= 0 or learning_rate > 1:
        raise ValueError(f"learning_rate 必须在 (0, 1] 范围内，当前值: {learning_rate}")
    
    return {
        'n_estimators': n_estimators,
        'learning_rate': learning_rate,
        'max_depth': max_depth,
        'early_stopping': early_stopping
    }


# =============================================================================
# 示例 3: 特征工程函数的迁移
# =============================================================================

def create_features_old_way(data, target_config: Dict[str, Any]) -> pd.DataFrame:
    """❌ 旧方式：直接字典访问"""
    
    # 特征开关
    enable_ta = target_config.get('enable_ta', True)
    enable_volume = target_config.get('enable_volume', True)
    enable_price_change = target_config.get('enable_price_change', True)
    
    # 参数配置
    ta_periods = target_config.get('ta_periods', [14, 21])
    price_change_periods = target_config.get('price_change_periods', [1, 2, 3])
    
    # 特征工程逻辑...
    features = data.copy()
    
    if enable_ta:
        # 添加技术指标
        for period in ta_periods:
            features[f'rsi_{period}'] = data['close'].rolling(period).mean()
    
    if enable_volume:
        # 添加成交量特征
        features['volume_ma'] = data['volume'].rolling(20).mean()
    
    if enable_price_change:
        # 添加价格变化特征
        for period in price_change_periods:
            features[f'price_change_{period}'] = data['close'].pct_change(period)
    
    return features


def create_features_new_way(data, target_name: str) -> pd.DataFrame:
    """✅ 新方式：类型安全的配置包装器"""
    
    wrapper = get_target_config_wrapper(target_name)
    
    # 类型安全的特征开关
    enable_ta = wrapper.get_bool('enable_ta', default=True)
    enable_volume = wrapper.get_bool('enable_volume', default=True)
    enable_price_change = wrapper.get_bool('enable_price_change', default=True)
    
    # 类型安全的参数配置
    ta_periods = wrapper.get_list('ta_periods', default=[14, 21])
    price_change_periods = wrapper.get_list('price_change_periods', default=[1, 2, 3])
    
    # 参数验证
    if not all(isinstance(p, int) and p > 0 for p in ta_periods):
        raise ValueError(f"ta_periods 必须是正整数列表，当前值: {ta_periods}")
    if not all(isinstance(p, int) and p > 0 for p in price_change_periods):
        raise ValueError(f"price_change_periods 必须是正整数列表，当前值: {price_change_periods}")
    
    # 特征工程逻辑...
    features = data.copy()
    
    if enable_ta:
        # 添加技术指标
        for period in ta_periods:
            features[f'rsi_{period}'] = data['close'].rolling(period).mean()
    
    if enable_volume:
        # 添加成交量特征
        features['volume_ma'] = data['volume'].rolling(20).mean()
    
    if enable_price_change:
        # 添加价格变化特征
        for period in price_change_periods:
            features[f'price_change_{period}'] = data['close'].pct_change(period)
    
    return features


# =============================================================================
# 示例 4: 批量配置处理的迁移
# =============================================================================

def process_multiple_targets_old_way(target_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """❌ 旧方式：批量处理直接字典访问"""
    
    results = []
    for target_config in target_configs:
        try:
            result = {
                'name': target_config['name'],
                'symbol': target_config['symbol'],
                'threshold': target_config.get('target_threshold', 0.001),
                'enable_features': target_config.get('enable_ta', True)
            }
            results.append(result)
        except KeyError as e:
            print(f"配置缺少必需字段: {e}")
            continue
    
    return results


def process_multiple_targets_new_way(target_names: List[str]) -> List[Dict[str, Any]]:
    """✅ 新方式：批量处理类型安全配置"""
    
    results = []
    for target_name in target_names:
        try:
            wrapper = get_target_config_wrapper(target_name)
            
            result = {
                'name': wrapper.get_str('name', required=True),
                'symbol': wrapper.get_str('symbol', required=True),
                'threshold': wrapper.get_float('target_threshold', default=0.001),
                'enable_features': wrapper.get_bool('enable_ta', default=True)
            }
            results.append(result)
            
        except (ConfigurationError, ValueError) as e:
            print(f"处理目标 '{target_name}' 时出错: {e}")
            continue
    
    return results


# =============================================================================
# 示例 5: 错误处理和回退机制
# =============================================================================

def robust_config_access(target_name: str) -> Dict[str, Any]:
    """🛡️ 强健的配置访问，支持回退机制"""
    
    try:
        # 尝试使用新的类型安全方式
        wrapper = get_target_config_wrapper(target_name)
        
        return {
            'name': wrapper.get_str('name', required=True),
            'symbol': wrapper.get_str('symbol', required=True),
            'interval': wrapper.get_str('interval', required=True),
            'threshold': wrapper.get_float('target_threshold', default=0.001),
            'periods': wrapper.get_list('prediction_periods', default=[1]),
            'enable_ta': wrapper.get_bool('enable_ta', default=True)
        }
        
    except Exception as e:
        print(f"类型安全配置访问失败，回退到传统方式: {e}")
        
        # 回退到传统字典访问
        try:
            config = get_target_config(target_name)
            return {
                'name': config.get('name', target_name),
                'symbol': config.get('symbol', 'BTCUSDT'),
                'interval': config.get('interval', '15m'),
                'threshold': config.get('target_threshold', 0.001),
                'periods': config.get('prediction_periods', [1]),
                'enable_ta': config.get('enable_ta', True)
            }
        except Exception as fallback_error:
            print(f"回退方式也失败: {fallback_error}")
            raise


def main():
    """主函数：运行所有示例"""
    print("🚀 配置管理迁移示例")
    print("=" * 50)
    
    # 测试目标
    test_target = "BTC_15m_UP"
    
    try:
        print("1. 数据处理函数迁移示例:")
        old_result = process_data_old_way(get_target_config(test_target))
        new_result = process_data_new_way(test_target)
        print(f"   旧方式结果: {old_result}")
        print(f"   新方式结果: {new_result}")
        
        print("\n2. 模型训练函数迁移示例:")
        old_train = train_model_old_way(get_target_config(test_target))
        new_train = train_model_new_way(test_target)
        print(f"   旧方式结果: {old_train}")
        print(f"   新方式结果: {new_train}")
        
        print("\n3. 强健配置访问示例:")
        robust_result = robust_config_access(test_target)
        print(f"   强健访问结果: {robust_result}")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
    
    print("\n" + "=" * 50)
    print("✅ 示例运行完成")


if __name__ == "__main__":
    main()
