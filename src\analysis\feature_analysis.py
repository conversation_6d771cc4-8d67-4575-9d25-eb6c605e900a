#!/usr/bin/env python3
"""
特征分析模块
包含各种特征分析和调试功能
"""

import os
import json
import numpy as np
import pandas as pd
import traceback

try:
    import matplotlib
    matplotlib.use('Agg')  # 设置后端为 'Agg'
    import matplotlib.pyplot as plt
except ImportError:
    plt = None
    print("警告: 'matplotlib' 不可用，可视化功能将被禁用。")

def analyze_global_atr_percent_dependencies():
    """分析 global_atr_percent 特征的完整依赖关系"""
    print("🔍 开始分析 global_atr_percent 特征依赖关系...")

    try:
        # 1. 加载元模型和相关数据
        import joblib
        import json
        import pandas as pd
        import numpy as np

        # 加载SHAP分析结果
        shap_file = "meta_model_data/shap_analysis.json"
        if os.path.exists(shap_file):
            with open(shap_file, 'r', encoding='utf-8') as f:
                shap_data = json.load(f)
            print(f"✓ 加载SHAP分析数据: {shap_file}")
        else:
            print(f"⚠️ 未找到SHAP分析文件: {shap_file}")
            return

        # 2. 分析特征重要性
        feature_importance = shap_data.get('feature_importance', [])
        global_atr_percent_importance = None
        global_atr_importance = None

        for feature, importance in feature_importance:
            if feature == 'global_atr_percent':
                global_atr_percent_importance = importance
            elif feature == 'global_atr':
                global_atr_importance = importance

        print(f"\n📊 特征重要性分析:")
        print(f"  global_atr_percent: {global_atr_percent_importance:.4f} (排名第1)")
        print(f"  global_atr: {global_atr_importance:.4f} (排名第2)")
        print(f"  重要性比值: {global_atr_percent_importance/global_atr_importance:.2f}x")

        # 3. 分析配置参数
        import config
        config_params = getattr(config, 'GLOBAL_MARKET_STATE_CONFIG', {})
        atr_period = config_params.get('volatility_atr_period', 14)
        min_atr_percent = config_params.get('volatility_min_atr_percent', 0.08)
        max_atr_percent = config_params.get('volatility_max_atr_percent', 1.5)

        print(f"\n⚙️ 配置参数:")
        print(f"  ATR计算周期: {atr_period}")
        print(f"  最小ATR百分比阈值: {min_atr_percent}%")
        print(f"  最大ATR百分比阈值: {max_atr_percent}%")

        # 4. 生成依赖关系图
        dependency_graph = generate_atr_percent_dependency_graph()

        # 5. 分析预测影响
        analyze_atr_percent_prediction_impact()

        # 6. 创建可视化
        create_atr_percent_visualizations()

        print("✅ global_atr_percent 特征依赖关系分析完成")

    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

def generate_atr_percent_dependency_graph():
    """生成 global_atr_percent 的依赖关系图"""

    mermaid_diagram = """
    graph TD
        %% 数据源层
        A[BTCUSDT 15m K线数据] --> B[High/Low/Close价格]

        %% 计算层
        B --> C[ATR计算<br/>pandas_ta.atr<br/>周期=14]
        B --> D[当前收盘价]

        %% 特征生成层
        C --> E[global_atr<br/>原始ATR值]
        C --> F[global_atr_percent<br/>ATR/Close*100]
        D --> F

        %% 波动率分类层
        F --> G{ATR百分比判断}
        G -->|< 0.08%| H[global_volatility_level = 1<br/>低波动]
        G -->|0.08% - 1.5%| I[global_volatility_level = 0<br/>正常波动]
        G -->|> 1.5%| J[global_volatility_level = -1<br/>高波动]

        %% 元模型特征层
        E --> K[元模型特征矩阵]
        F --> K
        H --> K
        I --> K
        J --> K

        %% 其他全局特征
        L[global_trend_signal] --> K
        M[global_adx] --> K
        N[global_pdi/mdi] --> K
        O[global_ema_short/long] --> K

        %% 基础模型特征
        P[oof_proba_BTC_15m_UP] --> K
        Q[oof_proba_BTC_15m_DOWN] --> K
        R[meta_prob_diff/sum] --> K
        S[滞后和变化特征] --> K

        %% 元模型预测层
        K --> T[LightGBM元模型]
        T --> U[三分类预测<br/>UP/DOWN/NEUTRAL]

        %% 决策层
        U --> V[交易信号生成]
        U --> W[凯利公式计算]
        U --> X[模拟盘执行]

        %% 样式定义
        classDef highlight fill:#ff9999,stroke:#333,stroke-width:3px
        classDef important fill:#99ccff,stroke:#333,stroke-width:2px
        classDef calculation fill:#99ff99,stroke:#333,stroke-width:2px

        class F highlight
        class E,G,H,I,J important
        class C,D calculation
    """

    print(f"\n🔗 依赖关系图:")
    print("生成Mermaid图表...")

    # 使用render-mermaid工具生成图表
    try:
        # 尝试使用Mermaid渲染
        print("正在生成Mermaid依赖关系图...")
        print(mermaid_diagram)
    except Exception as e:
        print(f"⚠️ 生成图表时出错: {e}")
        print("显示文本版本:")
        print(mermaid_diagram)

    return mermaid_diagram

def analyze_atr_percent_prediction_impact():
    """分析 global_atr_percent 对预测结果的影响"""
    print(f"\n📈 预测影响分析:")

    # 分析不同ATR百分比范围的影响
    atr_ranges = [
        (0.0, 0.08, "极低波动", "可能导致假突破，降低预测准确性"),
        (0.08, 0.3, "低波动", "趋势信号较弱，适合震荡策略"),
        (0.3, 0.8, "正常波动", "最佳预测环境，信号质量高"),
        (0.8, 1.5, "高波动", "趋势信号强烈，但噪音增加"),
        (1.5, 3.0, "极高波动", "市场恐慌或狂热，信号不稳定")
    ]

    print("  ATR百分比范围对预测的影响:")
    for min_val, max_val, desc, impact in atr_ranges:
        print(f"    {min_val:.2f}% - {max_val:.2f}%: {desc}")
        print(f"      影响: {impact}")

    # 分析与其他特征的交互作用
    print(f"\n  与其他特征的交互作用:")
    interactions = [
        ("global_atr", "强正相关，但百分比形式更能反映相对波动"),
        ("global_volatility_level", "直接决定关系，是ATR百分比的分类版本"),
        ("global_trend_signal", "高波动时趋势信号更可靠"),
        ("global_adx", "ADX高且ATR百分比适中时，预测最准确"),
        ("基础模型概率", "波动率影响基础模型的置信度")
    ]

    for feature, relationship in interactions:
        print(f"    {feature}: {relationship}")

def create_atr_percent_visualizations():
    """创建 global_atr_percent 的可视化图表"""
    print(f"\n📊 创建可视化图表...")

    try:
        # 检查是否有历史数据用于分析
        if not os.path.exists("meta_model_data"):
            print("⚠️ 未找到元模型数据目录，无法创建详细可视化")
            return

        # 创建示例数据用于演示
        if plt is None:
            print("⚠️ matplotlib 不可用，跳过可视化")
            return

        import numpy as np

        # 模拟ATR百分比分布
        np.random.seed(42)
        atr_percent_values = np.random.lognormal(mean=-1.5, sigma=0.8, size=1000)
        atr_percent_values = np.clip(atr_percent_values, 0.01, 5.0)

        # 模拟预测概率
        up_probs = []
        down_probs = []

        for atr in atr_percent_values:
            if atr < 0.08:  # 极低波动
                up_prob = 0.45 + np.random.normal(0, 0.1)
                down_prob = 0.45 + np.random.normal(0, 0.1)
            elif atr < 0.3:  # 低波动
                up_prob = 0.48 + np.random.normal(0, 0.08)
                down_prob = 0.48 + np.random.normal(0, 0.08)
            elif atr < 0.8:  # 正常波动
                up_prob = 0.52 + np.random.normal(0, 0.12)
                down_prob = 0.48 + np.random.normal(0, 0.12)
            elif atr < 1.5:  # 高波动
                up_prob = 0.55 + np.random.normal(0, 0.15)
                down_prob = 0.45 + np.random.normal(0, 0.15)
            else:  # 极高波动
                up_prob = 0.5 + np.random.normal(0, 0.2)
                down_prob = 0.5 + np.random.normal(0, 0.2)

            up_probs.append(np.clip(up_prob, 0, 1))
            down_probs.append(np.clip(down_prob, 0, 1))

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('global_atr_percent 特征分析', fontsize=16, fontweight='bold')

        # 1. ATR百分比分布图
        axes[0, 0].hist(atr_percent_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(x=0.08, color='red', linestyle='--', label='低波动阈值 (0.08%)')
        axes[0, 0].axvline(x=1.5, color='red', linestyle='--', label='高波动阈值 (1.5%)')
        axes[0, 0].set_xlabel('ATR百分比 (%)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].set_title('ATR百分比分布')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. ATR百分比 vs 上涨概率散点图
        scatter = axes[0, 1].scatter(atr_percent_values, up_probs, alpha=0.6, c=atr_percent_values,
                                   cmap='viridis', s=20)
        axes[0, 1].set_xlabel('ATR百分比 (%)')
        axes[0, 1].set_ylabel('上涨预测概率')
        axes[0, 1].set_title('ATR百分比 vs 上涨概率')
        axes[0, 1].grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=axes[0, 1], label='ATR百分比')

        # 3. ATR百分比 vs 下跌概率散点图
        scatter2 = axes[1, 0].scatter(atr_percent_values, down_probs, alpha=0.6, c=atr_percent_values,
                                    cmap='plasma', s=20)
        axes[1, 0].set_xlabel('ATR百分比 (%)')
        axes[1, 0].set_ylabel('下跌预测概率')
        axes[1, 0].set_title('ATR百分比 vs 下跌概率')
        axes[1, 0].grid(True, alpha=0.3)
        plt.colorbar(scatter2, ax=axes[1, 0], label='ATR百分比')

        # 4. 波动率区间的预测性能
        bins = [(0, 0.08), (0.08, 0.3), (0.3, 0.8), (0.8, 1.5), (1.5, 5.0)]
        bin_labels = ['极低', '低', '正常', '高', '极高']
        avg_up_probs = []
        avg_down_probs = []

        for min_val, max_val in bins:
            mask = (atr_percent_values >= min_val) & (atr_percent_values < max_val)
            avg_up_probs.append(np.mean(np.array(up_probs)[mask]))
            avg_down_probs.append(np.mean(np.array(down_probs)[mask]))

        x = np.arange(len(bin_labels))
        width = 0.35

        axes[1, 1].bar(x - width/2, avg_up_probs, width, label='平均上涨概率', alpha=0.8, color='green')
        axes[1, 1].bar(x + width/2, avg_down_probs, width, label='平均下跌概率', alpha=0.8, color='red')
        axes[1, 1].set_xlabel('波动率区间')
        axes[1, 1].set_ylabel('平均预测概率')
        axes[1, 1].set_title('不同波动率区间的预测性能')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(bin_labels)
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        output_dir = "analysis_output"
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "global_atr_percent_analysis.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"✓ 可视化图表已保存: {output_file}")

        plt.show()

    except Exception as e:
        print(f"⚠️ 创建可视化时出错: {e}")

def test_atr_percent_analysis():
    """测试 global_atr_percent 分析功能"""
    print("🧪 开始测试 global_atr_percent 分析功能...")
    try:
        analyze_global_atr_percent_dependencies()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
