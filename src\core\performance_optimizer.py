#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ 性能优化器 V1.0
为统一综合日志系统提供性能优化功能

核心功能：
- 内存使用优化
- 数据处理加速
- 缓存管理
- 并发处理优化
- 资源监控
"""

import gc
import os
import sys
import psutil
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import pandas as pd
import numpy as np
from functools import lru_cache, wraps
import pickle
import hashlib


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MemoryOptimizer")
        self.process = psutil.Process()
        
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
                'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
                'percent': memory_percent,
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
        except Exception as e:
            self.logger.error(f"获取内存使用情况失败: {e}")
            return {}
    
    def optimize_dataframe_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame内存使用"""
        try:
            original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
            
            # 优化数值类型
            for col in df.select_dtypes(include=['int64']).columns:
                col_min = df[col].min()
                col_max = df[col].max()
                
                if col_min >= -128 and col_max <= 127:
                    df[col] = df[col].astype('int8')
                elif col_min >= -32768 and col_max <= 32767:
                    df[col] = df[col].astype('int16')
                elif col_min >= -2147483648 and col_max <= 2147483647:
                    df[col] = df[col].astype('int32')
            
            for col in df.select_dtypes(include=['float64']).columns:
                df[col] = pd.to_numeric(df[col], downcast='float')
            
            # 优化字符串类型
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].dtype == 'object':
                    try:
                        df[col] = df[col].astype('category')
                    except:
                        pass
            
            optimized_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
            reduction = (original_memory - optimized_memory) / original_memory * 100
            
            self.logger.debug(f"DataFrame内存优化: {original_memory:.2f}MB -> {optimized_memory:.2f}MB ({reduction:.1f}% 减少)")
            
            return df
            
        except Exception as e:
            self.logger.error(f"优化DataFrame内存失败: {e}")
            return df
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            before_memory = self.get_memory_usage()
            
            # 执行垃圾回收
            collected = gc.collect()
            
            after_memory = self.get_memory_usage()
            
            if before_memory and after_memory:
                memory_freed = before_memory['rss_mb'] - after_memory['rss_mb']
                self.logger.debug(f"垃圾回收: 回收了 {collected} 个对象, 释放 {memory_freed:.2f}MB 内存")
            
        except Exception as e:
            self.logger.error(f"强制垃圾回收失败: {e}")
    
    def monitor_memory_usage(self, threshold_mb: float = 500.0) -> bool:
        """监控内存使用，超过阈值时返回True"""
        try:
            memory_usage = self.get_memory_usage()
            current_mb = memory_usage.get('rss_mb', 0)
            
            if current_mb > threshold_mb:
                self.logger.warning(f"内存使用超过阈值: {current_mb:.2f}MB > {threshold_mb}MB")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"监控内存使用失败: {e}")
            return False


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache", max_size_mb: int = 100):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size_mb = max_size_mb
        self.logger = logging.getLogger(f"{__name__}.CacheManager")
        
        # 内存缓存
        self.memory_cache = {}
        self.cache_stats = {'hits': 0, 'misses': 0}
    
    def _get_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        key_data = f"{func_name}_{args}_{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_from_cache(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        try:
            # 先检查内存缓存
            if key in self.memory_cache:
                self.cache_stats['hits'] += 1
                return self.memory_cache[key]
            
            # 检查磁盘缓存
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                    # 加载到内存缓存
                    self.memory_cache[key] = data
                    self.cache_stats['hits'] += 1
                    return data
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            self.logger.error(f"从缓存获取数据失败: {e}")
            self.cache_stats['misses'] += 1
            return None
    
    def put_to_cache(self, key: str, data: Any):
        """存储数据到缓存"""
        try:
            # 存储到内存缓存
            self.memory_cache[key] = data
            
            # 存储到磁盘缓存
            cache_file = self.cache_dir / f"{key}.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
            
            # 检查缓存大小
            self._cleanup_cache_if_needed()
            
        except Exception as e:
            self.logger.error(f"存储数据到缓存失败: {e}")
    
    def _cleanup_cache_if_needed(self):
        """如果需要，清理缓存"""
        try:
            total_size = sum(f.stat().st_size for f in self.cache_dir.glob("*.pkl"))
            total_size_mb = total_size / 1024 / 1024
            
            if total_size_mb > self.max_size_mb:
                # 按修改时间排序，删除最旧的文件
                cache_files = list(self.cache_dir.glob("*.pkl"))
                cache_files.sort(key=lambda x: x.stat().st_mtime)
                
                # 删除一半的缓存文件
                files_to_delete = cache_files[:len(cache_files) // 2]
                for file_path in files_to_delete:
                    file_path.unlink()
                    # 同时从内存缓存中删除
                    key = file_path.stem
                    self.memory_cache.pop(key, None)
                
                self.logger.info(f"清理了 {len(files_to_delete)} 个缓存文件")
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
    
    def clear_cache(self):
        """清空所有缓存"""
        try:
            # 清空内存缓存
            self.memory_cache.clear()
            
            # 清空磁盘缓存
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_file.unlink()
            
            self.logger.info("所有缓存已清空")
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            cache_files = list(self.cache_dir.glob("*.pkl"))
            total_size = sum(f.stat().st_size for f in cache_files)
            
            return {
                'hits': self.cache_stats['hits'],
                'misses': self.cache_stats['misses'],
                'hit_rate': hit_rate,
                'memory_cache_size': len(self.memory_cache),
                'disk_cache_files': len(cache_files),
                'disk_cache_size_mb': total_size / 1024 / 1024
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}


def cached_function(cache_manager: CacheManager, ttl_seconds: int = 3600):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache_manager._get_cache_key(func.__name__, args, kwargs)
            
            # 尝试从缓存获取
            cached_result = cache_manager.get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            cache_manager.put_to_cache(cache_key, result)
            
            return result
        
        return wrapper
    return decorator


class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.logger = logging.getLogger(f"{__name__}.ParallelProcessor")
    
    def process_dataframes_parallel(self, dataframes: List[pd.DataFrame], 
                                  process_func: Callable, use_processes: bool = False) -> List[Any]:
        """并行处理多个DataFrame"""
        try:
            if use_processes:
                executor_class = ProcessPoolExecutor
            else:
                executor_class = ThreadPoolExecutor
            
            with executor_class(max_workers=self.max_workers) as executor:
                futures = [executor.submit(process_func, df) for df in dataframes]
                results = [future.result() for future in futures]
            
            return results
            
        except Exception as e:
            self.logger.error(f"并行处理DataFrame失败: {e}")
            return []
    
    def process_files_parallel(self, file_paths: List[Path], 
                             process_func: Callable, use_processes: bool = False) -> List[Any]:
        """并行处理多个文件"""
        try:
            if use_processes:
                executor_class = ProcessPoolExecutor
            else:
                executor_class = ThreadPoolExecutor
            
            with executor_class(max_workers=self.max_workers) as executor:
                futures = [executor.submit(process_func, file_path) for file_path in file_paths]
                results = [future.result() for future in futures]
            
            return results
            
        except Exception as e:
            self.logger.error(f"并行处理文件失败: {e}")
            return []


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, cache_size_mb: int = 64, max_workers: int = None):
        self.memory_optimizer = MemoryOptimizer()
        self.cache_manager = CacheManager(max_size_mb=cache_size_mb)
        self.parallel_processor = ParallelProcessor(max_workers=max_workers)
        
        self.logger = logging.getLogger(f"{__name__}.PerformanceOptimizer")
        
        # 性能监控
        self.performance_stats = {
            'start_time': datetime.now(),
            'operations_count': 0,
            'total_processing_time': 0.0,
            'memory_optimizations': 0,
            'cache_operations': 0
        }
        
        self.logger.info("性能优化器初始化完成")
    
    def optimize_dataframe_processing(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame处理"""
        try:
            start_time = time.time()
            
            # 内存优化
            optimized_df = self.memory_optimizer.optimize_dataframe_memory(df)
            
            # 更新统计
            self.performance_stats['operations_count'] += 1
            self.performance_stats['total_processing_time'] += time.time() - start_time
            self.performance_stats['memory_optimizations'] += 1
            
            return optimized_df
            
        except Exception as e:
            self.logger.error(f"优化DataFrame处理失败: {e}")
            return df
    
    def get_cached_result(self, func: Callable, *args, **kwargs) -> Any:
        """获取缓存结果"""
        try:
            cache_key = self.cache_manager._get_cache_key(func.__name__, args, kwargs)
            
            # 尝试从缓存获取
            result = self.cache_manager.get_from_cache(cache_key)
            if result is not None:
                self.performance_stats['cache_operations'] += 1
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            self.cache_manager.put_to_cache(cache_key, result)
            self.performance_stats['cache_operations'] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取缓存结果失败: {e}")
            return func(*args, **kwargs)
    
    def monitor_and_optimize(self, memory_threshold_mb: float = 500.0):
        """监控并优化性能"""
        try:
            # 检查内存使用
            if self.memory_optimizer.monitor_memory_usage(memory_threshold_mb):
                self.memory_optimizer.force_garbage_collection()
            
            # 获取性能统计
            memory_usage = self.memory_optimizer.get_memory_usage()
            cache_stats = self.cache_manager.get_cache_stats()
            
            self.logger.debug(f"性能监控 - 内存: {memory_usage.get('rss_mb', 0):.2f}MB, "
                            f"缓存命中率: {cache_stats.get('hit_rate', 0):.1f}%")
            
        except Exception as e:
            self.logger.error(f"性能监控失败: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            uptime = (datetime.now() - self.performance_stats['start_time']).total_seconds()
            avg_processing_time = (
                self.performance_stats['total_processing_time'] / 
                max(self.performance_stats['operations_count'], 1)
            )
            
            return {
                'uptime_seconds': uptime,
                'operations_count': self.performance_stats['operations_count'],
                'avg_processing_time': avg_processing_time,
                'memory_optimizations': self.performance_stats['memory_optimizations'],
                'cache_operations': self.performance_stats['cache_operations'],
                'memory_usage': self.memory_optimizer.get_memory_usage(),
                'cache_stats': self.cache_manager.get_cache_stats()
            }
            
        except Exception as e:
            self.logger.error(f"获取性能报告失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            self.cache_manager.clear_cache()
            self.memory_optimizer.force_garbage_collection()
            self.logger.info("性能优化器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")


# 全局性能优化器实例
_global_performance_optimizer = None
_optimizer_lock = threading.Lock()


def get_performance_optimizer(cache_size_mb: int = 64, max_workers: int = None) -> PerformanceOptimizer:
    """获取全局性能优化器实例（单例模式）"""
    global _global_performance_optimizer
    
    with _optimizer_lock:
        if _global_performance_optimizer is None:
            _global_performance_optimizer = PerformanceOptimizer(
                cache_size_mb=cache_size_mb,
                max_workers=max_workers
            )
        
        return _global_performance_optimizer


def reset_performance_optimizer():
    """重置全局性能优化器"""
    global _global_performance_optimizer
    
    with _optimizer_lock:
        if _global_performance_optimizer:
            _global_performance_optimizer.cleanup()
        _global_performance_optimizer = None
