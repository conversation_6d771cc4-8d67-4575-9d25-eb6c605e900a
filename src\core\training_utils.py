#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练工具模块
提供训练过程中的辅助功能，包括动态样本加权集成
"""

import logging
import numpy as np
import pandas as pd
import traceback
from typing import Dict, List, Tuple, Optional, Any, Union

logger = logging.getLogger(__name__)

def prepare_training_data_with_weights(X_df: pd.DataFrame,
                                     y_array: np.ndarray,
                                     target_config: Dict[str, Any],
                                     target_name: str,
                                     apply_smote: bool = True,
                                     df_with_target: Optional[pd.DataFrame] = None,
                                     target_col: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray]]:
    """
    准备训练数据，包括SMOTE和动态样本加权（含质量权重）

    Args:
        X_df: 特征DataFrame
        y_array: 目标变量数组
        target_config: 目标配置
        target_name: 目标名称
        apply_smote: 是否应用SMOTE
        df_with_target: 包含目标变量和价格数据的完整DataFrame（用于质量权重计算）
        target_col: 目标变量列名（用于质量权重计算）

    Returns:
        tuple: (X_resampled, y_resampled, sample_weights)
            - X_resampled: 重采样后的特征数组
            - y_resampled: 重采样后的目标数组
            - sample_weights: 样本权重数组（如果启用）
    """
    try:
        logger.info(f"prepare_training_data_with_weights [{target_name}]: 开始准备训练数据")
        
        # 1. 首先计算原始数据的样本权重（包括质量权重）
        original_sample_weights = None
        enable_dynamic = target_config.get('enable_dynamic_sample_weighting', False)
        enable_quality = target_config.get('enable_meta_label_quality_weighting', False)

        if enable_dynamic or enable_quality:
            try:
                from src.core.data_utils import calculate_training_sample_weights
                original_sample_weights = calculate_training_sample_weights(
                    X_df, target_config, target_name, df_with_target, target_col
                )

                if original_sample_weights is not None:
                    logger.info(f"prepare_training_data_with_weights [{target_name}]: "
                               f"样本权重计算完成，范围: [{original_sample_weights.min():.4f}, {original_sample_weights.max():.4f}]")
                    if enable_quality:
                        logger.info(f"prepare_training_data_with_weights [{target_name}]: 已启用质量权重")

            except Exception as e:
                logger.warning(f"prepare_training_data_with_weights [{target_name}]: 样本权重计算失败: {e}")
                original_sample_weights = None
        
        # 2. 应用SMOTE（如果启用）
        X_resampled = X_df.values
        y_resampled = y_array
        final_sample_weights = original_sample_weights

        if apply_smote and target_config.get('smote_enable', False):
            try:
                from imblearn.over_sampling import SMOTE

                logger.info(f"prepare_training_data_with_weights [{target_name}]: 开始SMOTE过采样处理...")

                # SMOTE配置
                smote_k_neighbors = target_config.get('smote_k_neighbors', 5)
                smote_random_state = target_config.get('smote_random_state', 42)
                smote_min_threshold = target_config.get('smote_min_samples_threshold', 5)

                # 分析类别分布
                unique_labels, counts = np.unique(y_array, return_counts=True)
                min_samples = counts.min()

                logger.info(f"prepare_training_data_with_weights [{target_name}]: "
                           f"原始类别分布: {dict(zip(unique_labels, counts))}, "
                           f"最小类别样本数: {min_samples}")

                if min_samples >= smote_min_threshold:
                    # 确定SMOTE策略
                    target_variable_type = target_config.get('target_variable_type', 'BOTH').upper()

                    if target_variable_type in ["UP_ONLY", "DOWN_ONLY"]:
                        # 对于UP/DOWN模型，总是对标签1进行SMOTE
                        target_class_for_smote = 1
                        target_class_name = "明确上涨" if target_variable_type == "UP_ONLY" else "明确下跌"
                        logger.info(f"prepare_training_data_with_weights [{target_name}]: "
                                   f"使用{target_variable_type}策略，对{target_class_name}(标签{target_class_for_smote})进行SMOTE")
                    else:
                        # 对于其他情况，使用传统的少数类逻辑
                        minority_class_idx = np.argmin(counts)
                        target_class_for_smote = unique_labels[minority_class_idx]
                        target_class_name = f"少数类(标签{target_class_for_smote})"
                        logger.info(f"prepare_training_data_with_weights [{target_name}]: "
                                   f"使用传统策略，对{target_class_name}进行SMOTE")

                    # 检查目标类别是否存在且样本数足够
                    if target_class_for_smote in unique_labels:
                        target_class_idx = np.where(unique_labels == target_class_for_smote)[0][0]
                        target_class_count = counts[target_class_idx]

                        if target_class_count >= smote_min_threshold:
                            # 应用SMOTE
                            effective_k_neighbors = min(smote_k_neighbors, target_class_count - 1)
                            effective_k_neighbors = max(1, effective_k_neighbors)  # 确保至少为1

                            logger.info(f"prepare_training_data_with_weights [{target_name}]: "
                                       f"应用SMOTE，k_neighbors={effective_k_neighbors}")

                            smote = SMOTE(
                                random_state=smote_random_state,
                                k_neighbors=effective_k_neighbors
                            )
                            X_resampled, y_resampled = smote.fit_resample(X_df.values, y_array)

                            # 处理SMOTE后的样本权重
                            if original_sample_weights is not None:
                                final_sample_weights = _handle_smote_sample_weights(
                                    original_sample_weights, X_df.values, X_resampled, y_array, y_resampled, target_name
                                )

                            # 记录SMOTE后的分布
                            unique_labels_after, counts_after = np.unique(y_resampled, return_counts=True)
                            logger.info(f"prepare_training_data_with_weights [{target_name}]: "
                                       f"SMOTE后类别分布: {dict(zip(unique_labels_after, counts_after))}")
                        else:
                            logger.warning(f"prepare_training_data_with_weights [{target_name}]: "
                                         f"目标类别{target_class_for_smote}样本数({target_class_count})不足，跳过SMOTE")
                    else:
                        logger.warning(f"prepare_training_data_with_weights [{target_name}]: "
                                     f"目标类别{target_class_for_smote}不存在，跳过SMOTE")
                else:
                    logger.warning(f"prepare_training_data_with_weights [{target_name}]: "
                                 f"最小类别样本数({min_samples})不足阈值({smote_min_threshold})，跳过SMOTE")

            except Exception as e:
                logger.error(f"prepare_training_data_with_weights [{target_name}]: SMOTE应用失败: {e}")
                logger.debug(traceback.format_exc())
        
        # 3. 验证最终数据
        if len(X_resampled) != len(y_resampled):
            raise ValueError(f"特征和目标数量不匹配: {len(X_resampled)} vs {len(y_resampled)}")
        
        if final_sample_weights is not None and len(final_sample_weights) != len(y_resampled):
            logger.warning(f"prepare_training_data_with_weights [{target_name}]: "
                          f"样本权重数量不匹配，禁用权重: {len(final_sample_weights)} vs {len(y_resampled)}")
            final_sample_weights = None
        
        logger.info(f"prepare_training_data_with_weights [{target_name}]: 训练数据准备完成，"
                   f"样本数: {len(y_resampled)}, 特征数: {X_resampled.shape[1]}, "
                   f"权重: {'启用' if final_sample_weights is not None else '禁用'}")
        
        return X_resampled, y_resampled, final_sample_weights
        
    except Exception as e:
        logger.error(f"prepare_training_data_with_weights [{target_name}]: 训练数据准备失败: {e}")
        logger.debug(traceback.format_exc())
        return X_df.values, y_array, None

def _handle_smote_sample_weights(original_weights: np.ndarray,
                               X_original: np.ndarray,
                               X_resampled: np.ndarray,
                               y_original: np.ndarray,
                               y_resampled: np.ndarray,
                               target_name: str) -> np.ndarray:
    """
    处理SMOTE后的样本权重

    重要改进：
    1. 支持多种权重分配策略
    2. 为新生成样本提供更智能的权重分配
    3. 保持权重分布的合理性

    Args:
        original_weights: 原始样本权重
        X_original: 原始特征数组
        X_resampled: SMOTE后特征数组
        y_original: 原始目标数组
        y_resampled: SMOTE后目标数组
        target_name: 目标名称

    Returns:
        SMOTE后的样本权重数组
    """
    try:
        original_count = len(y_original)
        resampled_count = len(y_resampled)

        logger.info(f"_handle_smote_sample_weights [{target_name}]: 处理SMOTE权重映射，"
                   f"原始样本: {original_count}, SMOTE后样本: {resampled_count}")

        if resampled_count <= original_count:
            # 没有新增样本，直接返回原始权重
            logger.info(f"_handle_smote_sample_weights [{target_name}]: 无新增样本，使用原始权重")
            return original_weights[:resampled_count]

        # 分析原始权重分布
        unique_labels = np.unique(y_original)
        class_weight_stats = {}

        for label in unique_labels:
            mask = y_original == label
            class_weights = original_weights[mask]

            if len(class_weights) > 0:
                class_weight_stats[label] = {
                    'mean': class_weights.mean(),
                    'std': class_weights.std(),
                    'median': np.median(class_weights),
                    'count': len(class_weights)
                }
            else:
                class_weight_stats[label] = {
                    'mean': 1.0, 'std': 0.0, 'median': 1.0, 'count': 0
                }

        # 初始化SMOTE后的权重数组
        smote_weights = np.zeros(resampled_count)

        # 前original_count个样本保持原始权重
        smote_weights[:original_count] = original_weights

        # 为新生成的样本分配权重
        new_samples_count = resampled_count - original_count

        # 策略：使用类别权重的中位数（更稳定）+ 小幅随机扰动
        for i in range(original_count, resampled_count):
            label = y_resampled[i]

            if label in class_weight_stats:
                base_weight = class_weight_stats[label]['median']

                # 添加小幅随机扰动以避免权重过于均匀
                if class_weight_stats[label]['std'] > 0:
                    noise_scale = min(0.1, class_weight_stats[label]['std'] * 0.2)
                    noise = np.random.normal(0, noise_scale)
                    final_weight = max(0.1, base_weight + noise)  # 确保权重不会太小
                else:
                    final_weight = base_weight

                smote_weights[i] = final_weight
            else:
                # 未知标签，使用全局平均权重
                smote_weights[i] = original_weights.mean()

        # 权重标准化：确保总权重合理
        original_total_weight = original_weights.sum()
        smote_total_weight = smote_weights.sum()

        # 如果总权重变化过大，进行调整
        if smote_total_weight > 0:
            weight_ratio = original_total_weight / smote_total_weight
            # 只对新生成的样本进行调整
            smote_weights[original_count:] *= weight_ratio

        # 记录权重分配统计
        new_sample_weights = smote_weights[original_count:]
        logger.info(f"_handle_smote_sample_weights [{target_name}]: 新样本权重分配完成，"
                   f"新增样本: {new_samples_count}, "
                   f"新样本权重范围: [{new_sample_weights.min():.4f}, {new_sample_weights.max():.4f}], "
                   f"新样本权重均值: {new_sample_weights.mean():.4f}")

        return smote_weights

    except Exception as e:
        logger.error(f"_handle_smote_sample_weights [{target_name}]: SMOTE权重处理失败: {e}")
        logger.debug(traceback.format_exc())
        # 返回均匀权重作为后备方案
        return np.ones(len(y_resampled))

def train_model_with_sample_weights(model, 
                                  X_train: np.ndarray, 
                                  y_train: np.ndarray, 
                                  sample_weights: Optional[np.ndarray] = None,
                                  eval_set: Optional[List[Tuple]] = None,
                                  **fit_kwargs) -> Any:
    """
    使用样本权重训练模型
    
    Args:
        model: 模型对象
        X_train: 训练特征
        y_train: 训练目标
        sample_weights: 样本权重（可选）
        eval_set: 验证集（可选）
        **fit_kwargs: 其他fit参数
        
    Returns:
        训练后的模型
    """
    try:
        # 准备fit参数
        fit_params = fit_kwargs.copy()
        
        # 添加样本权重
        if sample_weights is not None:
            fit_params['sample_weight'] = sample_weights
            logger.info(f"train_model_with_sample_weights: 使用样本权重训练，"
                       f"权重范围: [{sample_weights.min():.4f}, {sample_weights.max():.4f}]")
        else:
            logger.info(f"train_model_with_sample_weights: 使用均匀权重训练")
        
        # 添加验证集
        if eval_set is not None:
            fit_params['eval_set'] = eval_set
        
        # 训练模型
        model.fit(X_train, y_train, **fit_params)
        
        return model
        
    except Exception as e:
        logger.error(f"train_model_with_sample_weights: 模型训练失败: {e}")
        logger.debug(traceback.format_exc())
        raise


def get_sample_weight_statistics(sample_weights: Optional[np.ndarray],
                                y_array: np.ndarray,
                                target_name: str) -> Dict[str, Any]:
    """
    获取样本权重统计信息

    Args:
        sample_weights: 样本权重数组（可选）
        y_array: 目标变量数组
        target_name: 目标名称

    Returns:
        权重统计信息字典
    """
    try:
        if sample_weights is None:
            return {
                'enabled': False,
                'target_name': target_name,
                'total_samples': len(y_array),
                'message': '样本权重未启用'
            }

        # 基本统计
        stats = {
            'enabled': True,
            'target_name': target_name,
            'total_samples': len(y_array),
            'min_weight': float(sample_weights.min()),
            'max_weight': float(sample_weights.max()),
            'mean_weight': float(sample_weights.mean()),
            'std_weight': float(sample_weights.std()),
            'weight_sum': float(sample_weights.sum())
        }

        # 按类别统计
        unique_labels = np.unique(y_array)
        class_statistics = {}

        for label in unique_labels:
            mask = y_array == label
            class_weights = sample_weights[mask]

            class_statistics[str(label)] = {
                'count': int(mask.sum()),
                'min_weight': float(class_weights.min()),
                'max_weight': float(class_weights.max()),
                'mean_weight': float(class_weights.mean()),
                'std_weight': float(class_weights.std()),
                'weight_sum': float(class_weights.sum())
            }

        stats['class_statistics'] = class_statistics

        # 权重分布分析
        weight_percentiles = np.percentile(sample_weights, [10, 25, 50, 75, 90])
        stats['weight_percentiles'] = {
            'p10': float(weight_percentiles[0]),
            'p25': float(weight_percentiles[1]),
            'p50': float(weight_percentiles[2]),
            'p75': float(weight_percentiles[3]),
            'p90': float(weight_percentiles[4])
        }

        return stats

    except Exception as e:
        logger.error(f"get_sample_weight_statistics [{target_name}]: 统计计算失败: {e}")
        return {
            'enabled': sample_weights is not None,
            'target_name': target_name,
            'error': str(e)
        }

def get_sample_weight_statistics(sample_weights: Optional[np.ndarray], 
                                y_array: np.ndarray, 
                                target_name: str) -> Dict[str, Any]:
    """
    获取样本权重统计信息
    
    Args:
        sample_weights: 样本权重数组
        y_array: 目标变量数组
        target_name: 目标名称
        
    Returns:
        权重统计信息字典
    """
    if sample_weights is None:
        return {
            'enabled': False,
            'message': '样本权重未启用'
        }
    
    try:
        stats = {
            'enabled': True,
            'total_samples': len(sample_weights),
            'min_weight': float(sample_weights.min()),
            'max_weight': float(sample_weights.max()),
            'mean_weight': float(sample_weights.mean()),
            'std_weight': float(sample_weights.std()),
            'weight_sum': float(sample_weights.sum())
        }
        
        # 按类别统计权重
        unique_labels = np.unique(y_array)
        class_weight_stats = {}
        
        for label in unique_labels:
            mask = y_array == label
            class_weights = sample_weights[mask]
            
            class_weight_stats[f'class_{label}'] = {
                'count': int(mask.sum()),
                'mean_weight': float(class_weights.mean()),
                'std_weight': float(class_weights.std()),
                'min_weight': float(class_weights.min()),
                'max_weight': float(class_weights.max())
            }
        
        stats['class_statistics'] = class_weight_stats
        
        logger.info(f"get_sample_weight_statistics [{target_name}]: 权重统计完成，"
                   f"总权重: {stats['weight_sum']:.2f}, 平均权重: {stats['mean_weight']:.4f}")
        
        return stats
        
    except Exception as e:
        logger.error(f"get_sample_weight_statistics [{target_name}]: 权重统计失败: {e}")
        return {
            'enabled': True,
            'error': str(e)
        }

def validate_sample_weights_compatibility(sample_weights: Optional[np.ndarray],
                                        X_array: np.ndarray,
                                        y_array: np.ndarray,
                                        target_name: str) -> bool:
    """
    验证样本权重与数据的兼容性
    
    Args:
        sample_weights: 样本权重数组
        X_array: 特征数组
        y_array: 目标数组
        target_name: 目标名称
        
    Returns:
        是否兼容
    """
    if sample_weights is None:
        return True
    
    try:
        # 检查长度匹配
        if len(sample_weights) != len(X_array) or len(sample_weights) != len(y_array):
            logger.error(f"validate_sample_weights_compatibility [{target_name}]: "
                        f"权重长度不匹配 - 权重: {len(sample_weights)}, 特征: {len(X_array)}, 目标: {len(y_array)}")
            return False
        
        # 检查权重值有效性
        if np.any(sample_weights < 0):
            logger.error(f"validate_sample_weights_compatibility [{target_name}]: 存在负权重值")
            return False
        
        if np.any(np.isnan(sample_weights)) or np.any(np.isinf(sample_weights)):
            logger.error(f"validate_sample_weights_compatibility [{target_name}]: 存在NaN或Inf权重值")
            return False
        
        if sample_weights.sum() <= 0:
            logger.error(f"validate_sample_weights_compatibility [{target_name}]: 权重总和为0或负数")
            return False
        
        logger.info(f"validate_sample_weights_compatibility [{target_name}]: 样本权重验证通过")
        return True
        
    except Exception as e:
        logger.error(f"validate_sample_weights_compatibility [{target_name}]: 权重验证失败: {e}")
        return False
