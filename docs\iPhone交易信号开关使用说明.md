# iPhone交易信号开关使用说明

## 📱 功能概述

为预测程序发送交易信号到iPhone添加了一个独立的开关控制，允许用户灵活控制是否启用iPhone自动化交易功能。

## 🔧 配置说明

### 主要配置项

在 `config.py` 文件中新增了以下配置：

```python
# --- iPhone SSH交易执行配置 ---
IPHONE_SSH_ENABLED = True                 # SSH连接功能开关
IPHONE_TRADING_SIGNAL_ENABLED = True      # iPhone交易信号发送开关
```

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `IPHONE_SSH_ENABLED` | 布尔值 | `True` | 控制iPhone SSH连接功能 |
| `IPHONE_TRADING_SIGNAL_ENABLED` | 布尔值 | `True` | 控制预测程序向iPhone发送交易信号 |

## 🎯 使用方法

### 启用iPhone交易信号 (推荐生产环境)

```python
IPHONE_TRADING_SIGNAL_ENABLED = True
```

**效果：**
- ✅ 当模拟盘接收到元模型信号或测试信号时
- ✅ 会自动调用iPhone自动化交易功能
- ✅ 通过SSH连接到iPhone执行真实交易
- ✅ 适用于正式交易环境

### 禁用iPhone交易信号 (推荐测试环境)

```python
IPHONE_TRADING_SIGNAL_ENABLED = False
```

**效果：**
- 🔒 模拟盘只在内部处理交易信号
- 🔒 不会触发iPhone自动化交易
- 🔒 适用于纯模拟测试或调试场景
- 🔒 避免测试时误触发真实交易

## 📋 工作流程

### 信号处理流程

```
预测系统 → 模拟盘接收信号 → 检查开关状态 → 决定是否执行iPhone交易
```

### 详细步骤

1. **信号接收**
   - 模拟盘接收到元模型信号或测试信号
   - 识别信号类型（UP/DOWN）和交易金额

2. **开关检查**
   - 检查 `IPHONE_TRADING_SIGNAL_ENABLED` 配置
   - 如果为 `False`，跳过iPhone交易执行

3. **交易执行**
   - 如果开关启用，调用iPhone自动化交易
   - 通过SSH连接到iPhone执行ZXTouch脚本

## 🧪 测试方法

### 运行测试脚本

```bash
python test_iphone_switch.py
```

### 测试内容

- ✅ 配置导入测试
- ✅ 开关状态检查
- ✅ 信号处理模拟
- ✅ 功能说明展示

### 测试输出示例

```
📱 iPhone交易信号开关测试
✅ 成功导入配置
📱 IPHONE_TRADING_SIGNAL_ENABLED: True
🔗 IPHONE_SSH_ENABLED: True

🧪 测试开关状态
✅ iPhone交易信号开关: 已启用
   - 预测程序会向iPhone发送交易信号
   - iPhone自动化交易将被执行
```

## 🔄 开关切换

### 启用交易信号

1. 编辑 `config.py` 文件
2. 设置 `IPHONE_TRADING_SIGNAL_ENABLED = True`
3. 重启预测程序或模拟盘

### 禁用交易信号

1. 编辑 `config.py` 文件
2. 设置 `IPHONE_TRADING_SIGNAL_ENABLED = False`
3. 重启预测程序或模拟盘

## ⚠️ 注意事项

### 安全建议

1. **测试环境**
   - 建议在测试时先禁用开关
   - 确认系统运行正常后再启用

2. **生产环境**
   - 确保iPhone网络连接稳定
   - 确认SSH配置正确
   - 监控交易执行状态

### 故障排除

1. **开关不生效**
   - 检查配置文件语法
   - 确认重启了程序
   - 查看控制台日志

2. **iPhone连接失败**
   - 检查 `IPHONE_SSH_ENABLED` 配置
   - 验证iPhone网络连接
   - 确认SSH服务运行正常

## 📊 日志输出

### 开关启用时

```
📱 SimMain [BTCUSDT]: 开始执行iPhone自动化交易...
   信号类型: UP
   交易金额: 25 USDT
   信号来源: MetaModel_BTC_15m
```

### 开关禁用时

```
📱 SimMain [BTCUSDT]: iPhone交易信号已禁用 (IPHONE_TRADING_SIGNAL_ENABLED=False)
   信号类型: UP
   交易金额: 25 USDT
   信号来源: MetaModel_BTC_15m
🔒 跳过iPhone自动化交易执行
```

## 🎉 总结

通过添加 `IPHONE_TRADING_SIGNAL_ENABLED` 开关，用户可以：

- ✅ 灵活控制iPhone交易功能
- ✅ 安全地进行测试和调试
- ✅ 避免误触发真实交易
- ✅ 保持系统的可控性和安全性

这个开关为iPhone自动化交易提供了更好的控制粒度，提升了系统的安全性和可用性。
