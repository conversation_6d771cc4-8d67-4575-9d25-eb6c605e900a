#!/usr/bin/env python3
"""
LightGBM GPU加速配置脚本
为RTX 3070配置LightGBM GPU支持
"""

import subprocess
import sys
import os
import platform

def check_gpu_requirements():
    """检查GPU要求"""
    print("🔍 检查GPU要求...")
    
    # 检查NVIDIA GPU
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA GPU检测成功")
            # 提取GPU信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'RTX 3070' in line:
                    print(f"  发现: {line.strip()}")
                    return True
        else:
            print("❌ NVIDIA GPU检测失败")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ nvidia-smi命令不可用")
        return False

def check_opencl_support():
    """检查OpenCL支持"""
    print("\n🔍 检查OpenCL支持...")
    
    try:
        import pyopencl as cl
        
        # 获取平台信息
        platforms = cl.get_platforms()
        print(f"✅ 检测到 {len(platforms)} 个OpenCL平台:")
        
        gpu_found = False
        for i, platform in enumerate(platforms):
            print(f"  平台 {i}: {platform.name}")
            
            # 获取设备信息
            try:
                devices = platform.get_devices(cl.device_type.GPU)
                for j, device in enumerate(devices):
                    print(f"    GPU设备 {j}: {device.name}")
                    if 'RTX 3070' in device.name or 'NVIDIA' in device.name:
                        gpu_found = True
            except:
                pass
        
        return gpu_found
        
    except ImportError:
        print("❌ PyOpenCL未安装")
        return False
    except Exception as e:
        print(f"❌ OpenCL检查失败: {e}")
        return False

def install_pyopencl():
    """安装PyOpenCL"""
    print("\n🚀 安装PyOpenCL...")
    
    try:
        # 方法1: 直接pip安装
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'pyopencl'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PyOpenCL安装成功")
            return True
        else:
            print(f"❌ pip安装失败: {result.stderr}")
            
        # 方法2: conda安装
        print("尝试使用conda安装...")
        result = subprocess.run([
            'conda', 'install', 'pyopencl', '-c', 'conda-forge', '-y'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 通过conda安装成功")
            return True
        else:
            print(f"❌ conda安装失败: {result.stderr}")
            
        return False
        
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def install_lightgbm_gpu():
    """安装LightGBM GPU版本"""
    print("\n🚀 安装LightGBM GPU版本...")
    
    try:
        # 卸载现有版本
        print("卸载现有LightGBM...")
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'lightgbm', '-y'], 
                      capture_output=True)
        
        # 方法1: 使用预编译的GPU版本
        print("尝试安装预编译GPU版本...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'lightgbm', '--config-settings=--build-option=--gpu'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 预编译GPU版本安装成功")
            return True
        else:
            print(f"❌ 预编译版本安装失败: {result.stderr}")
        
        # 方法2: 从源码编译
        print("尝试从源码编译GPU版本...")
        
        # 设置编译环境变量
        env = os.environ.copy()
        env['CMAKE_ARGS'] = '-DUSE_GPU=1'
        
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'lightgbm', '--no-binary=lightgbm'
        ], env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 源码编译GPU版本安装成功")
            return True
        else:
            print(f"❌ 源码编译失败: {result.stderr}")
        
        # 方法3: conda安装
        print("尝试使用conda安装...")
        result = subprocess.run([
            'conda', 'install', 'lightgbm', '-c', 'conda-forge', '-y'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ conda安装成功")
            return True
        else:
            print(f"❌ conda安装失败")
        
        # 方法4: 安装CPU版本作为备选
        print("安装CPU版本作为备选...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'lightgbm'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ CPU版本安装成功（备选方案）")
            return False  # 返回False表示不是GPU版本
        
        return False
        
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def test_lightgbm_gpu():
    """测试LightGBM GPU功能"""
    print("\n🧪 测试LightGBM GPU功能...")
    
    try:
        import lightgbm as lgb
        import numpy as np
        
        # 创建测试数据
        X = np.random.random((5000, 20)).astype(np.float32)
        y = np.random.randint(0, 2, 5000)
        
        print("测试GPU训练...")
        
        # GPU配置
        gpu_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'device_type': 'gpu',
            'gpu_platform_id': 0,
            'gpu_device_id': 0,
            'num_leaves': 31,
            'learning_rate': 0.05,
            'n_estimators': 100,
            'verbose': -1
        }
        
        # 尝试GPU训练
        try:
            model = lgb.LGBMClassifier(**gpu_params)
            model.fit(X, y)
            
            # 预测测试
            predictions = model.predict_proba(X[:100])
            
            print("✅ LightGBM GPU训练成功！")
            print(f"  预测形状: {predictions.shape}")
            return True
            
        except Exception as e:
            print(f"❌ GPU训练失败: {e}")
            
            # 尝试CPU训练作为对比
            print("尝试CPU训练作为对比...")
            cpu_params = gpu_params.copy()
            cpu_params['device_type'] = 'cpu'
            cpu_params.pop('gpu_platform_id', None)
            cpu_params.pop('gpu_device_id', None)
            
            model = lgb.LGBMClassifier(**cpu_params)
            model.fit(X, y)
            print("✅ CPU训练正常，GPU配置可能有问题")
            return False
        
    except ImportError as e:
        print(f"❌ LightGBM导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_lightgbm_gpu_config():
    """创建LightGBM GPU配置文件"""
    print("\n📝 创建LightGBM GPU配置...")
    
    config_content = '''"""
LightGBM GPU优化配置
适用于RTX 3070
"""

import lightgbm as lgb
import numpy as np
import time

# RTX 3070优化的LightGBM GPU参数
LIGHTGBM_GPU_PARAMS = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'device_type': 'gpu',
    'gpu_platform_id': 0,
    'gpu_device_id': 0,
    'gpu_use_dp': False,  # 使用单精度，RTX 3070更适合
    'num_leaves': 255,    # RTX 3070可以处理更多叶子
    'learning_rate': 0.05,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'verbose': -1,
    'random_state': 42,
    'n_estimators': 1000,
    'early_stopping_rounds': 100
}

# CPU备用参数
LIGHTGBM_CPU_PARAMS = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'device_type': 'cpu',
    'num_threads': 16,  # AMD 16核
    'force_row_wise': True,
    'histogram_pool_size': -1,
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'verbose': -1,
    'random_state': 42,
    'n_estimators': 1000,
    'early_stopping_rounds': 100
}

def create_optimized_lgb_model(use_gpu=True):
    """创建优化的LightGBM模型"""
    try:
        if use_gpu:
            print("🚀 尝试创建GPU模型...")
            params = LIGHTGBM_GPU_PARAMS.copy()
            model = lgb.LGBMClassifier(**params)
            
            # 测试GPU是否可用
            test_X = np.random.random((100, 10)).astype(np.float32)
            test_y = np.random.randint(0, 2, 100)
            model.fit(test_X, test_y)
            
            print("✅ GPU模型创建成功")
            return model, True
            
    except Exception as e:
        print(f"⚠️  GPU模型创建失败: {e}")
        print("🔄 切换到CPU模式...")
    
    # 使用CPU模式
    params = LIGHTGBM_CPU_PARAMS.copy()
    model = lgb.LGBMClassifier(**params)
    print("✅ CPU模型创建成功")
    return model, False

def benchmark_gpu_vs_cpu():
    """GPU vs CPU性能对比"""
    print("\\n🏁 GPU vs CPU性能对比")
    print("-" * 40)
    
    # 生成测试数据
    n_samples = 10000
    n_features = 50
    X = np.random.random((n_samples, n_features)).astype(np.float32)
    y = np.random.randint(0, 2, n_samples)
    
    results = {}
    
    # GPU测试
    try:
        print("测试GPU性能...")
        gpu_model = lgb.LGBMClassifier(**LIGHTGBM_GPU_PARAMS)
        
        start_time = time.time()
        gpu_model.fit(X, y)
        gpu_time = time.time() - start_time
        
        results['GPU'] = gpu_time
        print(f"  GPU训练时间: {gpu_time:.2f}秒")
        
    except Exception as e:
        print(f"  GPU测试失败: {e}")
    
    # CPU测试
    print("测试CPU性能...")
    cpu_model = lgb.LGBMClassifier(**LIGHTGBM_CPU_PARAMS)
    
    start_time = time.time()
    cpu_model.fit(X, y)
    cpu_time = time.time() - start_time
    
    results['CPU'] = cpu_time
    print(f"  CPU训练时间: {cpu_time:.2f}秒")
    
    # 计算加速比
    if 'GPU' in results and 'CPU' in results:
        speedup = results['CPU'] / results['GPU']
        print(f"\\n🚀 GPU加速比: {speedup:.2f}x")
    
    return results

if __name__ == "__main__":
    # 测试GPU功能
    gpu_model, is_gpu = create_optimized_lgb_model(use_gpu=True)
    
    if is_gpu:
        print("\\n🎉 LightGBM GPU配置成功！")
        benchmark_gpu_vs_cpu()
    else:
        print("\\n⚠️  使用CPU模式，但已优化")
'''
    
    with open('lightgbm_gpu_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ LightGBM GPU配置文件已创建: lightgbm_gpu_config.py")

def main():
    """主函数"""
    print("🚀 LightGBM GPU加速配置")
    print("=" * 50)
    
    # 检查GPU要求
    gpu_available = check_gpu_requirements()
    
    if not gpu_available:
        print("❌ GPU不可用，将配置CPU优化版本")
    
    # 安装PyOpenCL（LightGBM GPU需要）
    opencl_installed = check_opencl_support()
    if not opencl_installed:
        opencl_installed = install_pyopencl()
    
    # 安装LightGBM GPU版本
    lgb_gpu_success = install_lightgbm_gpu()
    
    # 测试GPU功能
    if lgb_gpu_success:
        gpu_works = test_lightgbm_gpu()
    else:
        gpu_works = False
    
    # 创建配置文件
    create_lightgbm_gpu_config()
    
    print(f"\n📊 配置结果:")
    print(f"GPU硬件: {'✅' if gpu_available else '❌'}")
    print(f"OpenCL支持: {'✅' if opencl_installed else '❌'}")
    print(f"LightGBM GPU: {'✅' if gpu_works else '❌'}")
    
    print(f"\n📋 下一步:")
    print("1. 运行: python lightgbm_gpu_config.py")
    print("2. 在你的代码中使用: from lightgbm_gpu_config import create_optimized_lgb_model")
    
    if gpu_works:
        print("3. 🎉 享受GPU加速的LightGBM训练！")
    else:
        print("3. ⚠️  使用CPU优化版本（仍然很快）")

if __name__ == "__main__":
    main()
