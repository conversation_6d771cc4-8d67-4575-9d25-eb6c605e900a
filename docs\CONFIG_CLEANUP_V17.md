# V17.0 配置清理文档

## 🧹 清理目标

清理config.py中所有与旧的三分类逻辑相关的配置参数，确保配置文件与新的V17.0二分类元模型决策体系保持一致。

## 🗑️ 已清理的配置项

### 1. 元模型阈值优化相关参数
**清理前**：
```python
# --- 阈值搜索范围配置（基于0交易结果的紧急优化）---
META_MODEL_THRESHOLD_MIN = 0.15
META_MODEL_THRESHOLD_MAX = 0.6
META_MODEL_CONFIDENCE_GAP_MIN = 0.0                      # ❌ 三分类相关
META_MODEL_CONFIDENCE_GAP_MAX = 0.15                     # ❌ 三分类相关
```

**清理后**：
```python
# --- 🎯 V17.0 二分类元模型：简化的阈值配置 ---
# 注意：旧的三分类相关参数（CONFIDENCE_GAP等）已移除，因为二分类模型不需要这些参数
```

### 2. V15.1 多阶段决策配置
**清理前**：
```python
# --- V15.1 元模型实战决策配置 - 基于训练结果优化 ---
# --- 阶段一：宏观环境过滤（基于global_atr_percent重要性19.3%）---
# --- 阶段二：模型共识过滤（基于models_consensus_score重要性10.4%）---
# --- 阶段三：概率阈值确认（基于训练结果0交易的反思）---
# --- 阶段四：方向优势确认 ---
META_FILTER_DIRECTION_ADVANTAGE = 0.6  # ❌ 二分类中不需要
```

**清理后**：
```python
# --- 🎯 V17.0 二分类元模型决策配置 ---
# 注意：旧的多阶段决策逻辑（V15.1）已移除，现在使用简化的二分类决策体系
# 旧的参数如 META_FILTER_DIRECTION_ADVANTAGE 等已不再需要，因为二分类模型使用更简单的阈值判断
```

### 3. 旧的UP/DOWN阈值参数
**清理前**：
```python
# --- 旧的简单阈值参数（基于二分类修复后优化）---
META_SIGNAL_UP_THRESHOLD = 0.6          # ❌ 二分类中不需要
META_SIGNAL_DOWN_THRESHOLD = 0.6        # ❌ 二分类中不需要
```

**清理后**：
```python
# --- 🎯 V17.0 注意：旧的UP/DOWN阈值参数已移除 ---
# META_SIGNAL_UP_THRESHOLD 和 META_SIGNAL_DOWN_THRESHOLD 在二分类模型中不再需要
# 现在只使用 META_SIGNAL_MIN_PROBABILITY 作为统一的决策阈值
```

### 4. 三分类类别权重策略
**清理前**：
```python
# 🔄 类别权重调整策略:
# 阶段1: {0: 1, 1: 1.5, 2: 1}     - 之前使用，侧重Class 1
# 阶段2: {0: 1.5, 1: 1.2, 2: 1}   - 🎯 当前使用，重点提升Class 0，保持Class 1关注
# 阶段3: {0: 1.8, 1: 1.2, 2: 1}   - 如果需要进一步提升Class 0
# 阶段4: {0: 1.5, 1: 1.5, 2: 1}   - 平衡提升Class 0和1
# 阶段5: {0: 2.0, 1: 1.5, 2: 1}   - 强化Class 0，适度保持Class 1
```

**清理后**：
```python
# 🎯 V17.0 二分类模型：类别权重策略已简化
# 注意：旧的三分类权重策略（阶段1-5）已移除，现在使用二分类的 'balanced' 策略
```

### 5. LSTM三分类配置注释
**清理前**：
```python
#     "target_variable_type": "BOTH",                     # [字符串] LSTM支持三分类：上涨、下跌、中性
```

**清理后**：
```python
#     "target_variable_type": "BOTH",                     # [字符串] 🎯 V17.0注意：三分类已废弃，如重新启用LSTM请使用二分类
```

## ✅ 保留的有效配置

### 1. 核心二分类决策参数
```python
# --- V16.0 元模型最终决策体系：Argmax + 最小概率 ---
META_SIGNAL_MIN_PROBABILITY = 0.6       # 🎯 二分类的统一决策阈值
```

### 2. 元模型阈值优化配置
```python
# --- 元模型决策阈值优化配置 ---
META_MODEL_THRESHOLD_OPTIMIZATION_ENABLE = True
META_MODEL_THRESHOLD_OPTIMIZATION_METHOD = 'optuna'
META_MODEL_THRESHOLD_OPTIMIZATION_N_TRIALS = 1000
META_MODEL_THRESHOLD_OPTIMIZATION_TIMEOUT = 2400
META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY = 'composite_score'
```

### 3. 元模型特征工程配置
```python
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    'enable_prob_diff': True,           # 概率差异特征 (SHAP最重要)
    'enable_prob_sum': True,            # 概率总和特征
    'enable_lag_features': True,        # 滞后特征
    'enable_change_features': False,    # 变化特征已禁用
    # ... 其他特征配置
}
```

## 🎯 清理效果

### 1. 配置一致性
- ✅ 移除了所有三分类相关的过时参数
- ✅ 保留了二分类模型需要的核心配置
- ✅ 添加了清晰的V17.0标识和说明

### 2. 代码维护性
- ✅ 减少了配置文件的复杂性
- ✅ 避免了新旧逻辑混合导致的混淆
- ✅ 为未来的配置管理提供了清晰的基础

### 3. 功能正确性
- ✅ 确保配置与实际使用的二分类逻辑匹配
- ✅ 防止了旧参数被误用的可能性
- ✅ 简化了参数调优的复杂度

## 📋 后续建议

### 1. 验证配置
- 重新训练元模型，确保清理后的配置正常工作
- 检查是否有其他代码引用了被删除的配置参数

### 2. 文档更新
- 更新相关的配置文档和使用指南
- 确保团队成员了解配置变更

### 3. 监控运行
- 观察清理后的系统运行状况
- 确认二分类决策逻辑工作正常

这次配置清理确保了config.py与V17.0二分类元模型决策体系的完全一致，为系统的稳定运行和未来维护奠定了良好基础。
