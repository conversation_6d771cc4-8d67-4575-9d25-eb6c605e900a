# 元标签质量权重系统使用指南

## 概述

元标签质量权重系统是一个革命性的标签"提纯"解决方案，它不再将所有"正确答案"一视同仁，而是通过分析交易路径质量来区分"教科书级别的完美答案"和"勉强及格的答案"。

## 核心理念

### 问题背景
传统的机器学习训练中，所有标记为"上涨"或"下跌"的样本都被赋予相同的重要性。但在实际交易中，有些信号虽然方向正确，但路径质量很差（如大幅回撤后才达到目标），这样的样本可能会误导模型学习。

### 解决方案
质量权重系统通过两个维度评估每个标签的质量：
1. **盈利/亏损效率**：越快达到目标价格，质量越高
2. **路径平滑度**：价格路径的回撤越小，质量越高

## 系统架构

### 核心组件

1. **`_calculate_meta_label_weights`** - 质量权重计算函数
2. **`meta_label_quality_config.py`** - 配置管理模块
3. **增强的训练工具** - 集成质量权重的训练流程

### 权重计算逻辑

```python
# 伪代码示例
for each_sample:
    if label in [0, 1]:  # 只处理非中性标签
        # 1. 计算效率权重
        if target_reached_quickly:
            efficiency_weight = 1.0 + (speed_bonus * multiplier)
        else:
            efficiency_weight = direction_penalty
        
        # 2. 计算平滑度权重
        if path_smooth:
            smoothness_weight = 1.0 + smoothness_bonus
        else:
            smoothness_weight = drawdown_penalty
        
        # 3. 最终质量权重
        final_weight = efficiency_weight * smoothness_weight
```

## 配置系统

### 配置模板

系统提供三种预定义模板：

#### Conservative（保守型）
- 较低的权重倍数
- 更严格的质量要求
- 适合稳定性优先的策略

#### Balanced（平衡型）
- 中等的权重倍数
- 平衡的质量要求
- 适合大多数应用场景

#### Aggressive（激进型）
- 较高的权重倍数
- 更宽松的质量要求
- 适合追求高收益的策略

### 配置参数详解

```python
quality_config = {
    'efficiency_weight_config': {
        'max_efficiency_multiplier': 1.5,  # 最大效率权重倍数
        'direction_penalty': 0.5,          # 方向错误惩罚
        'final_price_weight_multiplier': 2.0,  # 最终价格权重倍数
    },
    'smoothness_weight_config': {
        'drawdown_threshold': 0.1,         # 回撤阈值
        'smoothness_multiplier': 10,       # 平滑度倍数
    },
    'final_weight_limits': {
        'min_final_weight': 0.1,           # 最小权重
        'max_final_weight': 5.0,           # 最大权重
        'high_quality_threshold': 2.0,     # 高质量阈值
    }
}
```

## 使用方法

### 1. 基本启用

```python
# 在目标配置中启用质量权重
target_config = {
    'name': 'BTC_15m_UP',
    'prediction_periods': [2],
    'enable_meta_label_quality_weighting': True,  # 启用质量权重
    # ... 其他配置
}
```

### 2. 使用配置模板

```python
from src.core.meta_label_quality_config import (
    apply_quality_config_to_target,
    get_quality_config_template
)

# 应用激进型模板
target_config = apply_quality_config_to_target(
    target_config=base_config,
    enable_quality_weighting=True,
    custom_config=get_quality_config_template('aggressive')
)
```

### 3. 训练流程集成

```python
from src.core.training_utils import prepare_training_data_with_weights

# 准备训练数据（自动包含质量权重）
X_train, y_train, sample_weights = prepare_training_data_with_weights(
    X_df=features_df,
    y_array=labels_array,
    target_config=target_config,
    target_name='your_target',
    df_with_target=full_data_with_prices,  # 包含价格数据的完整DataFrame
    target_col='target_column_name'        # 目标变量列名
)

# 训练模型（权重会自动应用）
model.fit(X_train, y_train, sample_weight=sample_weights)
```

## 与现有系统的集成

### 与动态样本权重结合

质量权重可以与现有的动态样本权重系统结合使用：

```python
target_config = {
    'enable_dynamic_sample_weighting': True,      # 启用动态权重
    'enable_meta_label_quality_weighting': True,  # 启用质量权重
    # 两种权重会自动组合
}
```

### 权重组合策略

```python
# 最终权重 = 动态权重 × 质量权重
final_weights = dynamic_weights * quality_weights
```

## 性能监控

### 关键指标

1. **高质量样本比例**：权重 > 阈值的样本占比
2. **权重分布**：权重的统计分布
3. **类别权重差异**：不同类别的平均权重

### 日志输出示例

```
_calculate_meta_label_weights (BTC_15m_UP): 质量权重计算完成
  - 处理样本数: 8500
  - 高质量样本数: 1275 (15.0%)
  - 权重范围: [0.100, 4.850]
  - 权重均值: 1.000
  - 权重标准差: 0.845
```

## 最佳实践

### 1. 配置选择

- **新手用户**：使用 `balanced` 模板
- **保守策略**：使用 `conservative` 模板
- **激进策略**：使用 `aggressive` 模板

### 2. 参数调优

- 监控高质量样本比例（建议10-20%）
- 根据回测结果调整阈值参数
- 注意权重分布的合理性

### 3. 集成建议

- 先单独测试质量权重效果
- 再与动态权重结合使用
- 定期评估权重对模型性能的影响

## 故障排除

### 常见问题

1. **权重全为1.0**
   - 检查是否正确启用：`enable_meta_label_quality_weighting=True`
   - 确认传递了正确的价格数据

2. **高质量样本过少**
   - 降低 `high_quality_threshold`
   - 调整 `drawdown_threshold`
   - 检查数据质量

3. **权重分布异常**
   - 检查配置参数的合理性
   - 验证价格数据的完整性
   - 调整权重限制范围

### 调试技巧

```python
# 启用详细日志
target_config['meta_label_quality_config'] = {
    'logging_config': {
        'enable_detailed_logging': True,
        'log_sample_statistics': True,
        'log_weight_distribution': True,
    }
}
```

## 示例代码

完整的使用示例请参考：`examples/meta_label_quality_weighting_example.py`

## 总结

元标签质量权重系统通过智能评估每个训练样本的质量，帮助模型专注学习高质量的交易信号，从而提升预测准确性和交易策略的稳定性。

正确使用此系统可以显著改善模型的训练效果，特别是在处理噪声较多的金融数据时。
