@echo off
title Quick Start - Trading System

echo ========================================
echo      Quick Start - Trading System
echo ========================================
echo.

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not installed or not in PATH
    pause
    exit /b 1
)

echo [INFO] Quick start mode
echo [INFO] Starting prediction program and simulator together
echo.

REM Start prediction program
echo [INFO] Starting prediction program...
start "Prediction Program" cmd /k "title Prediction Program && python main.py"

REM Wait 2 seconds
timeout /t 2 /nobreak >nul

REM Start simulator
echo [INFO] Starting simulator...
start "Simulator" cmd /k "title Simulator && python SimMain.py"

echo.
echo [INFO] All services started successfully!
echo [INFO] Prediction program and simulator are running in separate windows
echo.
echo Press any key to close this window...
pause >nul
