#!/usr/bin/env python3
"""
增强日志记录配置文件

这个文件包含了所有日志记录相关的配置选项，
用户可以根据自己的需求调整日志级别、输出格式和目标。
"""

import os
import logging
import logging.handlers
from typing import Dict, Any

# ===== 基础日志配置 =====

# 日志级别配置
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# 默认日志级别
DEFAULT_LOG_LEVEL = 'INFO'

# 控制台日志级别（通常比文件日志级别高一些）
CONSOLE_LOG_LEVEL = 'INFO'

# 文件日志级别（可以记录更详细的信息）
FILE_LOG_LEVEL = 'DEBUG'

# ===== 日志格式配置 =====

# 详细格式（用于文件日志）
DETAILED_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'

# 简洁格式（用于控制台日志）
SIMPLE_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# 时间格式
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# ===== 日志文件配置 =====

# 日志文件目录
LOG_DIR = 'logs'

# 确保日志目录存在
os.makedirs(LOG_DIR, exist_ok=True)

# 日志文件配置
LOG_FILES = {
    'main': {
        'filename': os.path.join(LOG_DIR, 'main.log'),
        'level': FILE_LOG_LEVEL,
        'format': DETAILED_FORMAT,
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    },
    'training': {
        'filename': os.path.join(LOG_DIR, 'training.log'),
        'level': FILE_LOG_LEVEL,
        'format': DETAILED_FORMAT,
        'max_bytes': 20 * 1024 * 1024,  # 20MB
        'backup_count': 10
    },
    'data': {
        'filename': os.path.join(LOG_DIR, 'data.log'),
        'level': FILE_LOG_LEVEL,
        'format': DETAILED_FORMAT,
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    },
    'model': {
        'filename': os.path.join(LOG_DIR, 'model.log'),
        'level': FILE_LOG_LEVEL,
        'format': DETAILED_FORMAT,
        'max_bytes': 15 * 1024 * 1024,  # 15MB
        'backup_count': 7
    },
    'performance': {
        'filename': os.path.join(LOG_DIR, 'performance.log'),
        'level': 'INFO',
        'format': DETAILED_FORMAT,
        'max_bytes': 5 * 1024 * 1024,  # 5MB
        'backup_count': 3
    },
    'error': {
        'filename': os.path.join(LOG_DIR, 'error.log'),
        'level': 'ERROR',
        'format': DETAILED_FORMAT,
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 10
    }
}

# ===== 控制台输出配置 =====

# 是否启用控制台输出
ENABLE_CONSOLE_OUTPUT = True

# 控制台输出格式
CONSOLE_FORMAT = SIMPLE_FORMAT

# 控制台颜色配置（如果支持）
ENABLE_CONSOLE_COLORS = True

# ===== 高级日志配置 =====

# 是否启用异步日志（提高性能）
ENABLE_ASYNC_LOGGING = False

# 日志缓冲区大小（异步日志使用）
LOG_BUFFER_SIZE = 1000

# 是否启用日志压缩（轮转时）
ENABLE_LOG_COMPRESSION = True

# 日志保留天数
LOG_RETENTION_DAYS = 30

# ===== 特定模块日志配置 =====

# 第三方库日志级别控制
THIRD_PARTY_LOG_LEVELS = {
    'urllib3': 'WARNING',
    'requests': 'WARNING',
    'matplotlib': 'WARNING',
    'PIL': 'WARNING',
    'binance': 'INFO',
    'websocket': 'WARNING',
    'lightgbm': 'WARNING',
    'optuna': 'INFO'
}

# ===== 调试和开发配置 =====

# 开发模式（启用更详细的日志）
DEVELOPMENT_MODE = False

# 调试模式日志配置
DEBUG_CONFIG = {
    'log_level': 'DEBUG',
    'console_level': 'DEBUG',
    'enable_trace': True,
    'log_sql_queries': True,
    'log_api_calls': True
}

# ===== 生产环境配置 =====

# 生产模式日志配置
PRODUCTION_CONFIG = {
    'log_level': 'INFO',
    'console_level': 'WARNING',
    'enable_trace': False,
    'log_sql_queries': False,
    'log_api_calls': False,
    'enable_syslog': False,  # 是否启用系统日志
    'syslog_address': ('localhost', 514)
}

# ===== 日志过滤配置 =====

# 敏感信息过滤（防止密码、API密钥等泄露）
SENSITIVE_PATTERNS = [
    r'password["\s]*[:=]["\s]*[^"\s]+',
    r'api[_-]?key["\s]*[:=]["\s]*[^"\s]+',
    r'secret["\s]*[:=]["\s]*[^"\s]+',
    r'token["\s]*[:=]["\s]*[^"\s]+',
    r'auth["\s]*[:=]["\s]*[^"\s]+'
]

# 是否启用敏感信息过滤
ENABLE_SENSITIVE_FILTER = True

# ===== 性能监控日志配置 =====

# 是否记录性能指标
ENABLE_PERFORMANCE_LOGGING = True

# 性能日志阈值（秒）
PERFORMANCE_THRESHOLD = {
    'slow_query': 1.0,      # 慢查询阈值
    'slow_function': 5.0,   # 慢函数阈值
    'memory_warning': 80,   # 内存使用警告阈值（百分比）
    'cpu_warning': 90       # CPU使用警告阈值（百分比）
}

# ===== 日志聚合和分析配置 =====

# 是否启用日志聚合
ENABLE_LOG_AGGREGATION = False

# 日志聚合配置
LOG_AGGREGATION_CONFIG = {
    'endpoint': 'http://localhost:9200',  # Elasticsearch端点
    'index_pattern': 'trading-logs-%Y.%m.%d',
    'batch_size': 100,
    'flush_interval': 30  # 秒
}

# ===== 配置验证和工具函数 =====

def validate_logging_config():
    """验证日志配置的合理性"""
    warnings = []
    
    # 检查日志目录权限
    if not os.access(LOG_DIR, os.W_OK):
        warnings.append(f"日志目录 {LOG_DIR} 没有写权限")
    
    # 检查日志文件大小配置
    for name, config in LOG_FILES.items():
        if config['max_bytes'] < 1024 * 1024:  # 小于1MB
            warnings.append(f"日志文件 {name} 的最大大小可能过小")
        
        if config['backup_count'] < 1:
            warnings.append(f"日志文件 {name} 的备份数量应至少为1")
    
    # 检查日志级别配置
    if DEFAULT_LOG_LEVEL not in LOG_LEVELS:
        warnings.append(f"默认日志级别 {DEFAULT_LOG_LEVEL} 无效")
    
    return warnings

def get_logger_config(logger_name: str) -> Dict[str, Any]:
    """获取指定日志记录器的配置"""
    if logger_name in LOG_FILES:
        return LOG_FILES[logger_name]
    else:
        # 返回默认配置
        return {
            'filename': os.path.join(LOG_DIR, f'{logger_name}.log'),
            'level': FILE_LOG_LEVEL,
            'format': DETAILED_FORMAT,
            'max_bytes': 10 * 1024 * 1024,
            'backup_count': 5
        }

def setup_third_party_loggers():
    """设置第三方库的日志级别"""
    for logger_name, level in THIRD_PARTY_LOG_LEVELS.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, level))

def get_environment_config():
    """根据环境获取日志配置"""
    if DEVELOPMENT_MODE:
        return DEBUG_CONFIG
    else:
        return PRODUCTION_CONFIG

def print_logging_config_summary():
    """打印日志配置摘要"""
    print("=== 增强日志记录配置摘要 ===")
    print(f"默认日志级别: {DEFAULT_LOG_LEVEL}")
    print(f"控制台输出: {'启用' if ENABLE_CONSOLE_OUTPUT else '禁用'}")
    print(f"日志文件数量: {len(LOG_FILES)}")
    print(f"日志目录: {LOG_DIR}")
    print(f"开发模式: {'启用' if DEVELOPMENT_MODE else '禁用'}")
    print(f"性能监控: {'启用' if ENABLE_PERFORMANCE_LOGGING else '禁用'}")
    print(f"敏感信息过滤: {'启用' if ENABLE_SENSITIVE_FILTER else '禁用'}")
    
    # 检查配置警告
    warnings = validate_logging_config()
    if warnings:
        print("\n⚠️  配置警告:")
        for warning in warnings:
            print(f"  - {warning}")
    else:
        print("\n✅ 配置验证通过")

def create_log_directories():
    """创建所有必要的日志目录"""
    directories = set()
    directories.add(LOG_DIR)
    
    for config in LOG_FILES.values():
        log_dir = os.path.dirname(config['filename'])
        directories.add(log_dir)
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# ===== 日志轮转和清理配置 =====

def cleanup_old_logs():
    """清理过期的日志文件"""
    import time
    import glob
    
    current_time = time.time()
    cutoff_time = current_time - (LOG_RETENTION_DAYS * 24 * 3600)
    
    for config in LOG_FILES.values():
        log_pattern = config['filename'] + '*'
        for log_file in glob.glob(log_pattern):
            try:
                if os.path.getmtime(log_file) < cutoff_time:
                    os.remove(log_file)
                    print(f"已删除过期日志文件: {log_file}")
            except OSError as e:
                print(f"删除日志文件失败 {log_file}: {e}")

if __name__ == "__main__":
    print_logging_config_summary()
    create_log_directories()
    
    # 显示环境配置
    env_config = get_environment_config()
    print(f"\n💡 当前环境配置:")
    for key, value in env_config.items():
        print(f"  {key}: {value}")
