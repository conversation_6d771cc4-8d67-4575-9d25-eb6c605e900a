"""
RTX 3070 + AMD处理器优化配置
自动生成的优化参数
"""

import os
import multiprocessing as mp

# 系统信息
CPU_COUNT = mp.cpu_count()
GPU_AVAILABLE = True  # RTX 3070

# 环境变量设置
def setup_environment():
    """设置优化环境变量"""
    # CPU优化
    os.environ['OMP_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['MKL_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['NUMBA_NUM_THREADS'] = str(CPU_COUNT)
    os.environ['OPENBLAS_NUM_THREADS'] = str(CPU_COUNT)
    
    # TensorFlow优化
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
    os.environ['TF_NUM_INTEROP_THREADS'] = '4'
    os.environ['TF_NUM_INTRAOP_THREADS'] = str(CPU_COUNT)
    
    # CUDA优化
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第一个GPU
    
    print(f"✅ 环境优化设置完成 - CPU核心: {CPU_COUNT}, GPU: RTX 3070")

# LightGBM GPU优化参数
LIGHTGBM_GPU_PARAMS = {
    'device_type': 'gpu',
    'gpu_platform_id': 0,
    'gpu_device_id': 0,
    'num_threads': CPU_COUNT,
    'force_row_wise': False,  # GPU模式下使用列优先
    'histogram_pool_size': -1,
    'max_bin': 255,
    'boost_from_average': True,
    'tree_learner': 'serial',
    'verbose': -1
}

# LightGBM CPU备用参数（如果GPU不可用）
LIGHTGBM_CPU_PARAMS = {
    'device_type': 'cpu',
    'num_threads': CPU_COUNT,
    'force_row_wise': True,  # AMD处理器推荐
    'histogram_pool_size': -1,
    'max_bin': 255,
    'boost_from_average': True,
    'tree_learner': 'serial',
    'verbose': -1
}

# TensorFlow GPU优化配置
def setup_tensorflow_gpu():
    """配置TensorFlow GPU优化"""
    try:
        import tensorflow as tf
        
        # GPU内存配置
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                # 启用内存增长
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                
                # 线程配置
                tf.config.threading.set_inter_op_parallelism_threads(4)
                tf.config.threading.set_intra_op_parallelism_threads(0)
                
                print("✅ TensorFlow GPU优化配置完成")
                return True
            except RuntimeError as e:
                print(f"❌ TensorFlow GPU配置失败: {e}")
                return False
        else:
            print("⚠️  未检测到GPU，使用CPU模式")
            return False
    except ImportError:
        print("⚠️  TensorFlow未安装")
        return False

# 内存优化配置
MEMORY_OPTIMIZATION = {
    'max_depth': -1,
    'min_data_in_leaf': 20,
    'min_sum_hessian_in_leaf': 1e-3,
    'bagging_fraction': 0.8,
    'feature_fraction': 0.8,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1
}

# 性能监控配置
PERFORMANCE_CONFIG = {
    'enable_gpu_monitoring': True,
    'enable_memory_monitoring': True,
    'log_performance_metrics': True,
    'benchmark_mode': False
}
