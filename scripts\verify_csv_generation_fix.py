#!/usr/bin/env python3
"""
验证CSV生成修复是否成功
检查元模型训练后是否能正确生成CSV文件
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_csv_generation_fix():
    """检查CSV生成修复"""
    logger.info("🔍 检查CSV生成修复...")
    
    # 检查prediction.py是否包含CSV保存代码
    try:
        with open("src/core/prediction.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键代码片段
        csv_indicators = [
            "X_meta_features_oof.csv",
            "y_meta_target.csv", 
            "meta_data_summary.json",
            "保存元模型训练数据到CSV文件"
        ]
        
        found_indicators = []
        for indicator in csv_indicators:
            if indicator in content:
                found_indicators.append(indicator)
        
        logger.info(f"发现CSV相关代码片段: {len(found_indicators)}/{len(csv_indicators)}")
        
        if len(found_indicators) == len(csv_indicators):
            logger.info("✅ prediction.py已包含完整的CSV保存功能")
            return True
        else:
            logger.warning(f"⚠️ prediction.py缺少部分CSV保存功能")
            missing = set(csv_indicators) - set(found_indicators)
            logger.warning(f"缺少: {missing}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查prediction.py失败: {e}")
        return False

def check_meta_model_directory():
    """检查元模型目录配置"""
    logger.info("📁 检查元模型目录配置...")
    
    meta_save_dir = getattr(config, 'META_MODEL_SAVE_DIR', 'meta_model_data')
    logger.info(f"配置的保存目录: {meta_save_dir}")
    
    # 直接使用配置的路径
    output_dir = meta_save_dir
    
    logger.info(f"实际保存路径: {output_dir}")
    
    # 检查目录是否存在
    if os.path.exists(output_dir):
        logger.info("✅ 目录已存在")
    else:
        logger.info("📁 目录不存在，将在训练时创建")
    
    return output_dir

def simulate_csv_generation():
    """模拟CSV生成过程"""
    logger.info("🧪 模拟CSV生成过程...")
    
    try:
        # 获取保存目录
        meta_save_dir = getattr(config, 'META_MODEL_SAVE_DIR', 'meta_model_data')
        output_dir = meta_save_dir  # 直接使用配置的目录
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"✅ 目录创建成功: {output_dir}")
        
        # 创建模拟数据
        n_samples = 100
        n_features = 10
        
        # 模拟特征数据
        feature_names = [f"feature_{i}" for i in range(n_features)]
        X_meta_df = pd.DataFrame(
            np.random.randn(n_samples, n_features),
            columns=feature_names,
            index=pd.date_range('2024-01-01', periods=n_samples, freq='15min')
        )
        
        # 模拟目标变量
        y_meta_series = pd.Series(
            np.random.choice([0, 1], size=n_samples),
            index=X_meta_df.index,
            name='target'
        )
        
        # 保存特征数据
        x_meta_csv_path = os.path.join(output_dir, "X_meta_features_oof_test.csv")
        X_meta_df.to_csv(x_meta_csv_path, index=True)
        logger.info(f"✅ 测试特征数据已保存: {x_meta_csv_path}")
        
        # 保存目标变量
        y_meta_csv_path = os.path.join(output_dir, "y_meta_target_test.csv")
        y_meta_series.to_csv(y_meta_csv_path, index=True, header=True)
        logger.info(f"✅ 测试目标变量已保存: {y_meta_csv_path}")
        
        # 保存数据摘要
        summary_path = os.path.join(output_dir, "meta_data_summary_test.json")
        summary = {
            "timestamp": pd.Timestamp.now().isoformat(),
            "samples_count": len(X_meta_df),
            "features_count": len(X_meta_df.columns),
            "feature_names": list(X_meta_df.columns),
            "target_distribution": y_meta_series.value_counts().to_dict(),
            "data_time_range": {
                "start": str(X_meta_df.index.min()),
                "end": str(X_meta_df.index.max())
            }
        }
        
        import json
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"✅ 测试数据摘要已保存: {summary_path}")
        
        # 验证文件
        for file_path in [x_meta_csv_path, y_meta_csv_path, summary_path]:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"✅ {os.path.basename(file_path)}: {file_size} bytes")
            else:
                logger.error(f"❌ 文件不存在: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模拟CSV生成失败: {e}")
        return False

def provide_usage_instructions():
    """提供使用说明"""
    logger.info("📋 使用说明:")
    
    instructions = [
        "1. 修复已完成：prediction.py中的train_meta_model函数现在会自动保存CSV文件",
        "2. 文件位置：CSV文件将保存在meta_model_data/目录下",
        "3. 生成的文件：",
        "   - X_meta_features_oof.csv: 元模型特征数据",
        "   - y_meta_target.csv: 元模型目标变量",
        "   - meta_data_summary.json: 数据摘要信息",
        "4. 下次训练：重新运行元模型训练时，这些文件会自动生成",
        "5. 验证方法：训练完成后检查meta_model_data/目录"
    ]
    
    for instruction in instructions:
        logger.info(f"  {instruction}")

def main():
    """主函数"""
    logger.info("🎯 开始验证CSV生成修复...")
    
    try:
        # 1. 检查修复是否正确应用
        fix_applied = check_csv_generation_fix()
        
        # 2. 检查目录配置
        output_dir = check_meta_model_directory()
        
        # 3. 模拟CSV生成
        simulation_success = simulate_csv_generation()
        
        # 4. 提供使用说明
        provide_usage_instructions()
        
        if fix_applied and simulation_success:
            logger.info("🎉 CSV生成修复验证成功！")
            logger.info("📋 下次元模型训练时将自动生成CSV文件")
            return True
        else:
            logger.warning("⚠️ 修复验证部分失败，请检查上述错误")
            return False
        
    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 CSV生成修复验证成功！")
    else:
        print("\n❌ CSV生成修复验证失败！")
