# dynamic_config_manager.py
import json
import os
import threading
import time
from datetime import datetime
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    print("⚠️ watchdog导入失败，配置文件监控功能将不可用")
    Observer = None
    FileSystemEventHandler = None
    WATCHDOG_AVAILABLE = False
import traceback  # 仅用于格式化异常信息
from ..core.error_handler import get_enhanced_logger


class DynamicConfigManager:
    def __init__(self, filepath, default_config_provider_func=None):
        self.filepath = filepath
        self.config_data = {}
        self.lock = threading.RLock() # 使用 RLock
        self.default_config_provider_func = default_config_provider_func
        self.enhanced_logger = get_enhanced_logger(__name__)
        self._load_config()

        if WATCHDOG_AVAILABLE and Observer is not None:
            self.observer = Observer()
            event_handler = ConfigFileChangeHandler(self)
            self.observer.schedule(event_handler, os.path.dirname(self.filepath) or '.', recursive=False)
            self.observer.start()
        else:
            self.observer = None
            print("⚠️ 配置文件监控功能不可用（watchdog未安装）")
        self.enhanced_logger.info(
            "动态配置管理器已启动",
            filepath=self.filepath,
            monitoring_enabled=True
        )

    def _generate_default_dynamic_config(self):
        """如果提供了默认配置生成函数，则调用它"""
        if self.default_config_provider_func:
            try:
                return self.default_config_provider_func()
            except Exception as e:
                self.enhanced_logger.error(
                    "调用默认配置生成函数失败",
                    error_type=type(e).__name__,
                    error_message=str(e),
                    traceback=traceback.format_exc()
                )
        # 如果没有提供函数，或者函数失败，返回一个最小结构
        default_config = {"targets": {}, "global_settings": {"master_signal_sending_enabled": True}}
        self.enhanced_logger.info(
            "使用默认动态配置",
            config_structure=list(default_config.keys())
        )
        return default_config

    def _load_config(self):
        with self.lock:
            try:
                if os.path.exists(self.filepath):
                    with open(self.filepath, 'r', encoding='utf-8') as f:
                        self.config_data = json.load(f)
                    self.enhanced_logger.info(
                        "动态配置加载成功",
                        filepath=self.filepath,
                        config_keys=list(self.config_data.keys()),
                        targets_count=len(self.config_data.get('targets', {}))
                    )
                else:
                    self.enhanced_logger.warning(
                        "动态配置文件未找到，使用默认配置",
                        filepath=self.filepath
                    )
                    self.config_data = self._generate_default_dynamic_config()
                    self.save_config() # 保存一次生成的默认配置
            except json.JSONDecodeError as e:
                self.enhanced_logger.error(
                    "动态配置JSON解析失败",
                    filepath=self.filepath,
                    error_message=str(e),
                    fallback_action="使用上次成功配置或默认配置"
                )
                if not self.config_data: # 如果之前没有成功加载过
                    self.config_data = self._generate_default_dynamic_config()
            except Exception as e:
                self.enhanced_logger.error(
                    "动态配置加载异常",
                    filepath=self.filepath,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    traceback=traceback.format_exc(),
                    fallback_action="使用上次成功配置或默认配置"
                )
                if not self.config_data:
                    self.config_data = self._generate_default_dynamic_config()

    def reload_config(self):
        self.enhanced_logger.info(
            "检测到配置文件变化，重新加载中",
            filepath=self.filepath
        )
        old_config = self.config_data.copy()
        self._load_config()

        # 详细的配置变更审计
        if old_config != self.config_data:
            self._audit_config_changes(old_config, self.config_data)
            self.enhanced_logger.info(
                "配置已更新",
                filepath=self.filepath,
                old_targets_count=len(old_config.get('targets', {})),
                new_targets_count=len(self.config_data.get('targets', {}))
            )

    def get_target_params(self, target_name, static_target_config=None):
        with self.lock:
            dynamic_target_data = self.config_data.get("targets", {}).get(target_name, {})
        
        static_kelly_params = {}
        if static_target_config and isinstance(static_target_config.get('kelly_config_params'), dict):
            static_kelly_params = static_target_config['kelly_config_params']

        # 定义获取参数值的辅助函数（动态配置优先，然后是静态配置，最后是默认值）
        def get_param_value(param_name, default_value):
            return dynamic_target_data.get(param_name, static_kelly_params.get(param_name, default_value))

        params_to_return = {
            # 基础凯利参数
            "virtual_total_capital_for_kelly": get_param_value("virtual_total_capital_for_kelly", 50.0),
            "win_rate_p_estimate": get_param_value("win_rate_p_estimate", 0.56),
            "max_kelly_fraction_f": get_param_value("max_kelly_fraction_f", 0.07),
            "payout_ratio_b": get_param_value("payout_ratio_b", 0.85),

            # 下注金额限制参数
            "min_bet_kelly": get_param_value("min_bet_kelly", 5.0),
            "max_bet_kelly": get_param_value("max_bet_kelly", 250.0),

            # 保守下注期参数
            "enable_initial_conservative_betting": get_param_value("enable_initial_conservative_betting", True),
            "initial_conservative_trades_count": get_param_value("initial_conservative_trades_count", 25),
            "initial_conservative_bet_amount": get_param_value("initial_conservative_bet_amount", 5.0),

            # 负凯利分数处理参数
            "min_bet_if_kelly_negative": get_param_value("min_bet_if_kelly_negative", 5.0),

            # 启用状态
            "enabled": get_param_value("enabled", True)
        }
        return params_to_return

    def get_global_param(self, param_name, default_value=None):
        with self.lock:
            return self.config_data.get("global_settings", {}).get(param_name, default_value)

    def save_config(self):
        """手动触发保存当前配置到文件 (例如，在SimMain更新后调用)"""
        with self.lock:
            try:
                with open(self.filepath, 'w', encoding='utf-8') as f:
                    json.dump(self.config_data, f, indent=4)
                self.enhanced_logger.info(
                    "动态配置保存成功",
                    filepath=self.filepath,
                    config_size_bytes=os.path.getsize(self.filepath) if os.path.exists(self.filepath) else 0
                )
            except Exception as e:
                self.enhanced_logger.error(
                    "动态配置保存失败",
                    filepath=self.filepath,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    traceback=traceback.format_exc()
                )
    
    def update_target_dynamic_params(self, target_name, params_to_update):
        """
        外部调用此方法来更新特定目标的动态参数并保存
        支持扩展的凯利公式参数
        """
        with self.lock:
            if "targets" not in self.config_data:
                self.config_data["targets"] = {}
            if target_name not in self.config_data["targets"]:
                self.config_data["targets"][target_name] = {}

            # 定义所有支持的凯利公式浮点数参数
            kelly_float_params = [
                "virtual_total_capital_for_kelly", "win_rate_p_estimate", "max_kelly_fraction_f",
                "payout_ratio_b", "min_bet_kelly", "max_bet_kelly",
                "initial_conservative_bet_amount", "min_bet_if_kelly_negative"
            ]

            # 定义所有支持的凯利公式整数参数
            kelly_int_params = [
                "initial_conservative_trades_count"
            ]

            # 定义所有支持的凯利公式布尔参数
            kelly_bool_params = [
                "enabled", "enable_initial_conservative_betting"
            ]

            for key, value in params_to_update.items():
                # 处理浮点数参数
                if key in kelly_float_params:
                    try:
                        old_value = self.config_data["targets"][target_name].get(key, "未设置")
                        self.config_data["targets"][target_name][key] = float(value)
                        self.enhanced_logger.info(
                            "动态参数更新 - 浮点数",
                            target_name=target_name,
                            param_name=key,
                            old_value=old_value,
                            new_value=float(value)
                        )
                    except ValueError:
                        self.enhanced_logger.warning(
                            "动态参数更新失败 - 无效浮点数",
                            target_name=target_name,
                            param_name=key,
                            invalid_value=value
                        )

                # 处理整数参数
                elif key in kelly_int_params:
                    try:
                        old_value = self.config_data["targets"][target_name].get(key, "未设置")
                        self.config_data["targets"][target_name][key] = int(value)
                        self.enhanced_logger.info(
                            "动态参数更新 - 整数",
                            target_name=target_name,
                            param_name=key,
                            old_value=old_value,
                            new_value=int(value)
                        )
                    except ValueError:
                        self.enhanced_logger.warning(
                            "动态参数更新失败 - 无效整数",
                            target_name=target_name,
                            param_name=key,
                            invalid_value=value
                        )

                # 处理布尔参数
                elif key in kelly_bool_params:
                    old_value = self.config_data["targets"][target_name].get(key, "未设置")
                    self.config_data["targets"][target_name][key] = bool(value)
                    self.enhanced_logger.info(
                        "动态参数更新 - 布尔值",
                        target_name=target_name,
                        param_name=key,
                        old_value=old_value,
                        new_value=bool(value)
                    )

                # 处理其他参数
                else:
                    old_value = self.config_data["targets"][target_name].get(key, "未设置")
                    self.config_data["targets"][target_name][key] = value
                    self.enhanced_logger.info(
                        "动态参数更新 - 其他类型",
                        target_name=target_name,
                        param_name=key,
                        old_value=old_value,
                        new_value=value
                    )

        self.save_config()

    def _audit_config_changes(self, old_config: dict, new_config: dict, path_prefix: str = ""):
        """
        详细审计配置变更

        Args:
            old_config: 旧配置
            new_config: 新配置
            path_prefix: 配置路径前缀
        """
        # 检查删除的配置项
        for key in old_config:
            if key not in new_config:
                self.enhanced_logger.info(
                    "配置项删除",
                    config_path=f"{path_prefix}.{key}" if path_prefix else key,
                    old_value=old_config[key],
                    change_type="removed"
                )

        # 检查新增的配置项
        for key in new_config:
            if key not in old_config:
                self.enhanced_logger.info(
                    "配置项新增",
                    config_path=f"{path_prefix}.{key}" if path_prefix else key,
                    new_value=new_config[key],
                    change_type="added"
                )

        # 检查修改的配置项
        for key in new_config:
            if key in old_config:
                old_value = old_config[key]
                new_value = new_config[key]

                if isinstance(old_value, dict) and isinstance(new_value, dict):
                    # 递归检查嵌套字典
                    self._audit_config_changes(
                        old_value, new_value,
                        f"{path_prefix}.{key}" if path_prefix else key
                    )
                elif old_value != new_value:
                    self.enhanced_logger.info(
                        "配置项修改",
                        config_path=f"{path_prefix}.{key}" if path_prefix else key,
                        old_value=old_value,
                        new_value=new_value,
                        change_type="modified"
                    )

    def create_config_snapshot(self) -> dict:
        """
        创建当前配置的完整快照

        Returns:
            dict: 配置快照信息
        """
        snapshot = {
            'timestamp': time.time(),
            'timestamp_iso': datetime.now().isoformat(),
            'filepath': self.filepath,
            'config_data': self.config_data.copy(),
            'targets_count': len(self.config_data.get('targets', {})),
            'global_settings': self.config_data.get('global_settings', {}),
            'file_size_bytes': os.path.getsize(self.filepath) if os.path.exists(self.filepath) else 0
        }

        # 记录快照创建
        self.enhanced_logger.info(
            "配置快照创建",
            snapshot_timestamp=snapshot['timestamp_iso'],
            targets_count=snapshot['targets_count'],
            file_size_bytes=snapshot['file_size_bytes'],
            global_settings_keys=list(snapshot['global_settings'].keys())
        )

        return snapshot

    def stop_observer(self):
        if self.observer is not None and self.observer.is_alive():
            self.observer.stop()
            self.observer.join()
            self.enhanced_logger.info(
                "动态配置文件监控已停止",
                filepath=self.filepath
            )


if WATCHDOG_AVAILABLE:
    class ConfigFileChangeHandler(FileSystemEventHandler):
        def __init__(self, manager_instance):
            self.manager = manager_instance
            self.last_modified_time = 0
            self.debounce_interval = 1.0

        def on_modified(self, event):
            if not event.is_directory and event.src_path == self.manager.filepath:
                current_time = time.time()
                if current_time - self.last_modified_time > self.debounce_interval:
                    self.manager.reload_config()
                    self.last_modified_time = current_time
else:
    class ConfigFileChangeHandler:
        def __init__(self, manager_instance):
            self.manager = manager_instance
            print("⚠️ 配置文件监控功能不可用（watchdog未安装）")

