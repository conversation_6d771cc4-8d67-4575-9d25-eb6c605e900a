﻿# prediction.py

import warnings
# 屏蔽LightGBM的警告信息
warnings.filterwarnings('ignore', category=UserWarning, module='lightgbm')
warnings.filterwarnings('ignore', message='.*No further splits with positive gain.*')
warnings.filterwarnings('ignore', message='.*best gain.*')

import requests
import numpy as np
import os
import joblib
import pandas as pd
from datetime import datetime, timedelta, timezone
import time
import traceback
import threading
import logging
import pandas_ta as pta  # 临时注释，解决numpy兼容性问题
import json
import math
import atexit # 用于注册退出处理函数
from collections import defaultdict # 导入 defaultdict
import sys
import inspect
try:
    import shap
    import matplotlib.pyplot as plt
    import seaborn as sns
    SHAP_AVAILABLE = True
except ImportError:
    shap = None
    plt = None
    sns = None
    SHAP_AVAILABLE = False

# --- 1. 在文件顶部，确保导入了动态交易过滤器 (带异常处理) ---
try:
    from .dynamic_trading_filter import DynamicTradingFilter
except ImportError:
    print("!!! 警告: 未能导入 DynamicTradingFilter。交易过滤器将不可用。")
    # 创建一个虚拟类，以防止程序在调用时崩溃
    class DynamicTradingFilter:
        def apply_filter(self, **kwargs):
            print("警告: DynamicTradingFilter 未加载，所有信号将直接通过。")
            # 返回一个表示“允许通过”的默认字典
            return {
                'should_trade': True,
                'confidence_adjustment': 1.0,
                'filtered_signal': kwargs.get('signal'),
                'filter_action': 'allow',
                'filter_reasons': ['Filter not available']
            }

# --- 2. 导入集中式预测过滤器 ---
try:
    from .prediction_filter import (
        PredictionFilter, FilterInput, FilterResult,
        create_filter_input_from_prediction_data
    )
except ImportError:
    print("!!! 警告: 未能导入 PredictionFilter。将使用传统过滤逻辑。")
    PredictionFilter = None
    FilterInput = None
    FilterResult = None
    create_filter_input_from_prediction_data = None

# --- LSTM模型导入 ---
try:
    from .models import LSTMModel, create_lstm_model, load_lstm_model, LSTM_AVAILABLE
except ImportError as e:
    print(f"警告: LSTM模型导入失败: {e}")
    LSTMModel = None
    create_lstm_model = None
    load_lstm_model = None
    LSTM_AVAILABLE = False

from .dynamic_win_rate_tracker import get_dynamic_win_rate_tracker

from .application_state import ApplicationState, get_app_state
from . import data_utils
import config
from .data_utils import fetch_binance_history, prepare_features_for_prediction, interval_to_timedelta
from .event_system import (
    publish_status_update, publish_prediction_result, publish_training_progress,
    publish_system_error, publish_signal_sent, EventType
)
from lightgbm import LGBMClassifier
from binance.exceptions import BinanceAPIException, BinanceRequestException
import requests.exceptions
from sklearn.calibration import CalibratedClassifierCV
# from sklearn.metrics import brier_score_loss # 主要在训练后评估，预测时可选
import pygame
import multiprocessing
from lightgbm import early_stopping # 或者如果您已经导入了整个 lightgbm (import lightgbm as lgb)，则使用 lgb.early_stopping
from sklearn.metrics import accuracy_score, log_loss, classification_report
from ..utils.DataValidator import DataValidator
from . import realtime_data_manager
from ..utils.dynamic_config_manager import DynamicConfigManager # 导入新的管理器
from ..analysis.analysis_logger import log_prediction_context, initialize_loggers
import optuna
from sklearn.model_selection import TimeSeriesSplit # 用于Optuna内部的CV
from sklearn.metrics import f1_score, make_scorer # 用于计算F1分数

# --- 自定义异常类 ---
class PredictionBaseError(Exception):
    """预测系统基础异常类"""
    def __init__(self, message, context=None, original_exception=None):
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()

class ModelLoadError(PredictionBaseError):
    """模型加载错误"""
    pass

class DataValidationError(PredictionBaseError):
    """数据验证错误"""
    pass

class FeatureEngineeringError(PredictionBaseError):
    """特征工程错误"""
    pass

class PredictionExecutionError(PredictionBaseError):
    """预测执行错误"""
    pass

class ConfigurationError(PredictionBaseError):
    """配置错误"""
    pass

class MetaModelError(PredictionBaseError):
    """元模型错误"""
    pass

# --- 标准错误结果类 ---
class PredictionResult:
    """标准预测结果类"""
    def __init__(self, success=True, data=None, error=None):
        self.success = success
        self.data = data or {}
        self.error = error
        self.timestamp = datetime.now()

    def to_dict(self):
        result = {
            'success': self.success,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data
        }
        if self.error:
            result['error'] = {
                'type': type(self.error).__name__,
                'message': str(self.error),
                'context': getattr(self.error, 'context', {}),
                'original_exception': str(getattr(self.error, 'original_exception', '')) if getattr(self.error, 'original_exception', None) else None
            }
        return result

# --- 增强的错误日志记录函数 ---
def log_prediction_error(logger, error, function_name, target_name=None, additional_context=None):
    """
    记录预测错误的详细信息

    Args:
        logger: 日志记录器
        error: 异常对象
        function_name: 函数名
        target_name: 目标名称（可选）
        additional_context: 额外上下文信息（可选）
    """
    # 获取完整异常信息
    exc_type, exc_value, exc_traceback = sys.exc_info()

    # 获取调用栈信息
    frame = inspect.currentframe()
    if frame and frame.f_back:
        caller_frame = frame.f_back
        line_number = caller_frame.f_lineno
        filename = caller_frame.f_code.co_filename
    else:
        line_number = None
        filename = None

    # 构建错误上下文
    error_context = {
        'function': function_name,
        'line_number': line_number,
        'filename': filename,
        'target_name': target_name,
        'timestamp': datetime.now().isoformat()
    }

    # 添加异常特有的上下文
    if hasattr(error, 'context'):
        error_context.update(error.context)

    # 添加额外上下文
    if additional_context:
        error_context.update(additional_context)

    # 记录错误信息
    logger.error(f"❌ Error in {function_name}" + (f" at line {line_number}" if line_number else ""))
    logger.error(f"   Error type: {type(error).__name__}")
    logger.error(f"   Message: {str(error)}")
    logger.error(f"   Context: {error_context}")

    if target_name:
        logger.error(f"   Target: {target_name}")

    # 记录原始异常信息（如果存在）
    if hasattr(error, 'original_exception') and error.original_exception:
        logger.error(f"   Original exception: {type(error.original_exception).__name__}: {error.original_exception}")

    # 记录系统异常信息（如果存在）
    if exc_type and exc_value:
        logger.error(f"   System exception: {exc_type.__name__}: {exc_value}")
        logger.error("   Full traceback:")
        logger.error(traceback.format_exc())

def create_error_result(error_type, message, context=None, original_exception=None):
    """
    创建标准错误结果

    Args:
        error_type: 错误类型类
        message: 错误消息
        context: 上下文信息
        original_exception: 原始异常

    Returns:
        PredictionResult: 包含错误信息的结果对象
    """
    error = error_type(message, context, original_exception)
    return PredictionResult(success=False, error=error)

def create_success_result(data=None):
    """
    创建成功结果

    Args:
        data: 结果数据

    Returns:
        PredictionResult: 包含成功数据的结果对象
    """
    return PredictionResult(success=True, data=data or {})

# --- 元模型智能决策函数 ---
def _make_intelligent_meta_decision(meta_probas, original_class):
    """
    基于元模型概率输出进行智能决策，与calculate_simulated_profit_meta保持完全一致的决策逻辑

    Args:
        meta_probas: 元模型输出的概率数组 [P(下跌), P(上涨), P(中性)]
        original_class: 原始的argmax类别预测

    Returns:
        tuple: (final_signal, prediction_label, prediction_color)
    """
    # 🎯 统一决策逻辑：使用与calculate_simulated_profit_meta完全相同的参数和逻辑
    # 获取Optuna优化的参数，确保与优化过程一致
    up_threshold = getattr(config, 'META_SIGNAL_UP_THRESHOLD', 0.4)
    down_threshold = getattr(config, 'META_SIGNAL_DOWN_THRESHOLD', 0.4)
    confidence_gap_up = getattr(config, 'META_SIGNAL_CONFIDENCE_GAP_UP', 0.1)
    confidence_gap_down = getattr(config, 'META_SIGNAL_CONFIDENCE_GAP_DOWN', 0.1)

    # 解析概率（假设：0=DOWN, 1=UP, 2=NEUTRAL）
    p_down = meta_probas[0] if len(meta_probas) > 0 else 0.33
    p_up = meta_probas[1] if len(meta_probas) > 1 else 0.33
    p_neutral = meta_probas[2] if len(meta_probas) > 2 else 0.33

    print(f"    智能决策分析: P(下跌)={p_down:.3f}, P(上涨)={p_up:.3f}, P(中性)={p_neutral:.3f}")
    print(f"    🎯 统一决策策略: UP阈值={up_threshold}, DOWN阈值={down_threshold}")
    print(f"    置信度差值要求: UP_gap={confidence_gap_up}, DOWN_gap={confidence_gap_down}")

    # 🎯 统一决策逻辑：与calculate_simulated_profit_meta完全一致
    decision_reasons = []

    # 检查做多条件：P(上涨) >= threshold_up AND P(上涨) - P(中性) >= confidence_gap_up
    if (p_up >= up_threshold and p_up - p_neutral >= confidence_gap_up):
        decision_reasons.append(f"P(上涨)={p_up:.3f}>={up_threshold}")
        decision_reasons.append(f"置信度差值={p_up - p_neutral:.3f}>={confidence_gap_up}")
        final_signal = "UP_Meta"
        prediction_label = f"上涨 (P:{p_up:.2%}, 差值+{p_up - p_neutral:.2%})"
        prediction_color = config.UP_COLOR
        print(f"    ✅ 决策结果: 做多 - {', '.join(decision_reasons)}")
        return final_signal, prediction_label, prediction_color

    # 检查做空条件：P(下跌) >= threshold_down AND P(下跌) - P(中性) >= confidence_gap_down
    elif (p_down >= down_threshold and p_down - p_neutral >= confidence_gap_down):
        decision_reasons.append(f"P(下跌)={p_down:.3f}>={down_threshold}")
        decision_reasons.append(f"置信度差值={p_down - p_neutral:.3f}>={confidence_gap_down}")
        final_signal = "DOWN_Meta"
        prediction_label = f"下跌 (P:{p_down:.2%}, 差值+{p_down - p_neutral:.2%})"
        prediction_color = config.DOWN_COLOR
        print(f"    ✅ 决策结果: 做空 - {', '.join(decision_reasons)}")
        return final_signal, prediction_label, prediction_color

    # 分析为什么没有产生信号
    print(f"    🔍 信号过滤分析:")
    if p_up < up_threshold:
        print(f"      - 上涨概率不足: {p_up:.3f} < {up_threshold}")
    elif p_up - p_neutral < confidence_gap_up:
        print(f"      - 上涨置信度差值不足: {p_up - p_neutral:.3f} < {confidence_gap_up}")

    if p_down < down_threshold:
        print(f"      - 下跌概率不足: {p_down:.3f} < {down_threshold}")
    elif p_down - p_neutral < confidence_gap_down:
        print(f"      - 下跌置信度差值不足: {p_down - p_neutral:.3f} < {confidence_gap_down}")

    # 默认为中性
    neutral_reasons = []
    if p_up < up_threshold and p_down < down_threshold:
        neutral_reasons.append("两个方向概率都不足")
    elif p_up >= up_threshold and p_down >= down_threshold:
        neutral_reasons.append("两个方向概率都足够但置信度差值不足")
    elif p_up >= up_threshold:
        neutral_reasons.append(f"上涨置信度差值不足: {p_up - p_neutral:.3f} < {confidence_gap_up}")
    elif p_down >= down_threshold:
        neutral_reasons.append(f"下跌置信度差值不足: {p_down - p_neutral:.3f} < {confidence_gap_down}")

    final_signal = "Neutral_Meta"
    prediction_label = f"中性 (上涨:{p_up:.2%}, 下跌:{p_down:.2%}, 置信度不足)"
    prediction_color = config.NEUTRAL_COLOR

    print(f"    🔄 决策结果: 中性 - {', '.join(neutral_reasons) if neutral_reasons else '无明确方向'}")

    return final_signal, prediction_label, prediction_color


# --- 状态管理类 ---
class PredictionStateManager:
    def __init__(self):
        self._lock = threading.Lock()
        # 初始化 last_signal_state，确保 config 和 PREDICTION_TARGETS 可访问
        # 如果在类定义时 config 不可用，可能需要在外部初始化后传入或稍后设置
        try:
            self._last_signal_state = {target['name']: "Neutral" for target in config.PREDICTION_TARGETS if isinstance(target, dict) and 'name' in target}
        except (NameError, AttributeError):
             # Fallback if config or PREDICTION_TARGETS isn't available at this exact point
            print("Warning: config.PREDICTION_TARGETS not available at PredictionStateManager init for _last_signal_state. Initializing as empty dict.")
            self._last_signal_state = {}
        self._last_signal_alert_time = 0
        self._last_pre_alarm_play_time = defaultdict(float) # 使用defaultdict简化时间戳处理
        self._last_signal_sent_time_per_target = defaultdict(float)
        self._last_signal_type_sent_per_target = defaultdict(str)
        self._strategy_execution_counters = defaultdict(int)
        self._music_fadeout_timer = None
        # prediction_context_logger 通常是日志记录器实例，其自身的并发由logging模块处理
        # 如果需要动态更改记录器实例，则需要锁
        self._prediction_context_logger = None 

        # 元模型相关状态
        self._meta_model_instance = None
        self._meta_model_feature_names = None
        self._meta_model_loaded_successfully = False
        self._last_meta_predictions = {}  # 存储上一次的元模型基础预测概率

    # --- Getters and Setters for thread-safe access ---
    # Example for last_signal_alert_time
    def get_last_signal_alert_time(self):
        with self._lock:
            return self._last_signal_alert_time

    def set_last_signal_alert_time(self, value):
        with self._lock:
            self._last_signal_alert_time = value

    # Example for last_pre_alarm_play_time (dictionary)
    def get_last_pre_alarm_play_time(self, key):
        with self._lock:
            return self._last_pre_alarm_play_time[key]

    def set_last_pre_alarm_play_time(self, key, value):
        with self._lock:
            self._last_pre_alarm_play_time[key] = value
            
    def get_all_last_pre_alarm_play_time(self):
        with self._lock:
            return self._last_pre_alarm_play_time.copy() # 返回副本以防外部修改

    # last_signal_state
    def get_last_signal_state(self, target_name):
        with self._lock:
            return self._last_signal_state.get(target_name, "Neutral")

    def set_last_signal_state(self, target_name, state):
        with self._lock:
            self._last_signal_state[target_name] = state
            
    def get_all_last_signal_states(self):
        with self._lock:
            return self._last_signal_state.copy()
    
    def initialize_last_signal_state(self, targets_config):
        with self._lock:
            self._last_signal_state = {target['name']: "Neutral" for target in targets_config if isinstance(target, dict) and 'name' in target}

    # last_signal_sent_time_per_target
    def get_last_signal_sent_time(self, target_name):
        with self._lock:
            return self._last_signal_sent_time_per_target[target_name]

    def set_last_signal_sent_time(self, target_name, timestamp):
        with self._lock:
            self._last_signal_sent_time_per_target[target_name] = timestamp
            
    def get_all_last_signal_sent_times(self):
        with self._lock:
            return self._last_signal_sent_time_per_target.copy()

    # last_signal_type_sent_per_target
    def get_last_signal_type_sent(self, target_name):
        with self._lock:
            return self._last_signal_type_sent_per_target[target_name]

    def set_last_signal_type_sent(self, target_name, signal_type):
        with self._lock:
            self._last_signal_type_sent_per_target[target_name] = signal_type
            
    def get_all_last_signal_types_sent(self):
        with self._lock:
            return self._last_signal_type_sent_per_target.copy()

    # strategy_execution_counters
    def get_strategy_execution_counter(self, strategy_name):
        with self._lock:
            return self._strategy_execution_counters[strategy_name]

    def increment_strategy_execution_counter(self, strategy_name):
        with self._lock:
            self._strategy_execution_counters[strategy_name] += 1
            return self._strategy_execution_counters[strategy_name]

    def reset_strategy_execution_counter(self, strategy_name):
        with self._lock:
            self._strategy_execution_counters[strategy_name] = 0
            
    def get_all_strategy_execution_counters(self):
        with self._lock:
            return self._strategy_execution_counters.copy()

    # music_fadeout_timer
    def get_music_fadeout_timer(self):
        with self._lock:
            return self._music_fadeout_timer

    def set_music_fadeout_timer(self, timer_instance):
        # threading.Timer objects are typically not modified after creation,
        # but the reference to it is. If it can be cancelled/recreated, lock is good.
        with self._lock:
            if self._music_fadeout_timer and hasattr(self._music_fadeout_timer, 'cancel'):
                try:
                    self._music_fadeout_timer.cancel()
                except Exception:
                    pass # Ignore if already run or cancelled
            self._music_fadeout_timer = timer_instance

    # prediction_context_logger (assuming it might be reassigned)
    def get_prediction_context_logger(self):
        # Logging instances are typically thread-safe for writing.
        # Lock is only needed if the logger instance itself is being swapped out.
        with self._lock: 
            return self._prediction_context_logger

    def set_prediction_context_logger(self, logger_instance):
        with self._lock:
            self._prediction_context_logger = logger_instance

    # Meta Model State
    def get_meta_model_instance(self):
        with self._lock:
            return self._meta_model_instance

    def set_meta_model_instance(self, instance):
        with self._lock:
            self._meta_model_instance = instance

    def get_meta_model_feature_names(self):
        with self._lock:
            return self._meta_model_feature_names

    def set_meta_model_feature_names(self, names):
        with self._lock:
            self._meta_model_feature_names = names

    def is_meta_model_loaded_successfully(self):
        with self._lock:
            return self._meta_model_loaded_successfully

    def set_meta_model_loaded_successfully(self, status):
        with self._lock:
            self._meta_model_loaded_successfully = status

    def get_last_meta_predictions(self):
        with self._lock:
            return self._last_meta_predictions.copy()

    def set_last_meta_predictions(self, predictions_dict):
        with self._lock:
            self._last_meta_predictions = predictions_dict.copy() if predictions_dict else {}

    # --- Method Aliases ---
    update_last_signal_alert_time = set_last_signal_alert_time
    update_last_pre_alarm_play_time = set_last_pre_alarm_play_time
    update_last_signal_state = set_last_signal_state
    update_last_signal_sent_time = set_last_signal_sent_time
    update_last_signal_type_sent = set_last_signal_type_sent
    update_music_fadeout_timer = set_music_fadeout_timer
    update_prediction_context_logger = set_prediction_context_logger
    update_meta_model_instance = set_meta_model_instance
    update_meta_model_feature_names = set_meta_model_feature_names
    update_meta_model_loaded_successfully = set_meta_model_loaded_successfully


# --- 全局状态管理器实例 ---
# prediction_lock 仍然可以作为全局锁用于控制整个预测流程的入口，如果需要的话
prediction_lock = threading.Lock()
global_prediction_state_manager = PredictionStateManager()

# --- 元模型相关文件名 (从config获取，确保config已导入) ---
# 这些可以硬编码，或者更好的是，也从 config.py 中读取，如果它们是固定的
META_MODEL_FILENAME = "meta_model_lgbm.joblib"
META_FEATURES_FILENAME = "meta_model_features.json"

# --- 全局配置读取 ---
SEND_SIGNALS_TO_SIMULATOR = getattr(config, 'SIMULATOR_INTEGRATION_ENABLED', True)
# --------------------
SIGNAL_SEND_COOLDOWN_SECONDS = getattr(config, 'SIGNAL_SEND_COOLDOWN_SECONDS', 120)


# --- Pygame Mixer 初始化 ---
_PYGAME_MIXER_INIT_ATTEMPTED_IN_THIS_PROCESS = False
PYGAME_MIXER_AVAILABLE = False
if not _PYGAME_MIXER_INIT_ATTEMPTED_IN_THIS_PROCESS:
    _PYGAME_MIXER_INIT_ATTEMPTED_IN_THIS_PROCESS = True
    is_main_process_for_init = (multiprocessing.current_process().name == 'MainProcess')
    if is_main_process_for_init:
        if 'PYGAME_HIDE_SUPPORT_PROMPT' not in os.environ:
            os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = "1"
        if 'SDL_VIDEODRIVER' not in os.environ and not os.name == 'nt':
             try: pass # os.environ['SDL_VIDEODRIVER'] = 'dummy'
             except Exception: pass
    try:
        pygame.mixer.pre_init(44100, -16, 2, 512)
        pygame.mixer.init()
        PYGAME_MIXER_AVAILABLE = pygame.mixer.get_init() is not None
        if not PYGAME_MIXER_AVAILABLE: pygame.init(); PYGAME_MIXER_AVAILABLE = pygame.mixer.get_init() is not None
        if PYGAME_MIXER_AVAILABLE and is_main_process_for_init: print("[Pygame Init] Pygame mixer 初始化成功。")
        elif not PYGAME_MIXER_AVAILABLE and is_main_process_for_init: print("!!! [Pygame Init] Pygame mixer 初始化失败。")
    except ImportError:
        if is_main_process_for_init: print("!!! 警告: 'pygame' 库未找到。")
    except Exception as e_pygame_init:
        if is_main_process_for_init: print(f"!!! 警告: 初始化 Pygame 时发生错误: {e_pygame_init}。")

# --- 模拟盘与冷却配置 ---
SIMULATOR_API_URL = getattr(config, 'SIMULATOR_API_URL', "http://127.0.0.1:5005/signal") # 从config读取，提供默认
SEND_SIGNALS_TO_SIMULATOR = getattr(config, 'SIMULATOR_INTEGRATION_ENABLED', True)
SIGNAL_SEND_COOLDOWN_SECONDS = getattr(config, 'SIGNAL_SEND_COOLDOWN_SECONDS', 120)
# DEFAULT_AMOUNT_FOR_SIMULATOR_SIGNAL 似乎不再直接使用，因为金额由策略决定

VALID_LGBM_PARAM_KEYS_PRED_PY = [
    'boosting_type', 'num_leaves', 'max_depth', 'learning_rate', 'n_estimators',
    'subsample_for_bin', 'objective', 'class_weight', 'min_split_gain',
    'min_child_weight', 'min_child_samples', 'subsample', 'subsample_freq',
    'colsample_bytree', 'reg_alpha', 'reg_lambda', 'random_state', 'n_jobs',
    'importance_type', 'metric', 'device_type', 'verbose', 'verbosity', 'seed', 
    'callbacks', 'eval_set', 'eval_names', 'eval_metric', 'feature_name', 
    'categorical_feature', 'early_stopping_rounds', 'first_metric_only', 'num_class'
]





# --- 用于Optuna内部评估的辅助函数 (可以放在 train_meta_model 外部或作为其内部函数) ---
def apply_realtime_meta_feature_engineering(meta_input_data_dict, trained_feature_names):
    """
    在实时预测时应用元模型特征工程，确保与训练时的特征一致

    参数:
    - meta_input_data_dict: 包含基础模型概率的字典
    - trained_feature_names: 训练时使用的特征名列表

    返回:
    - 添加了特征工程的字典
    """
    print("  应用实时元模型特征工程...")

    # 创建副本以避免修改原始数据
    enhanced_data = meta_input_data_dict.copy()

    # 🎯 修复：识别精英模型特征 - 支持精英格式和传统格式
    up_prob_features = [key for key in enhanced_data.keys()
                       if ('UP' in key.upper() and
                           (key.startswith('oof_proba_') or key.startswith('prob_') or
                            key.startswith('oof_BTC_15m_UP_elite_fold')))]
    down_prob_features = [key for key in enhanced_data.keys()
                         if ('DOWN' in key.upper() and
                             (key.startswith('oof_proba_') or key.startswith('prob_') or
                              key.startswith('oof_BTC_15m_DOWN_elite_fold')))]

    print(f"    识别到UP概率特征: {up_prob_features}")
    print(f"    识别到DOWN概率特征: {down_prob_features}")

    # 1. 基础模型概率差异 (Probability Difference)
    if len(up_prob_features) >= 1 and len(down_prob_features) >= 1:
        up_prob = enhanced_data[up_prob_features[0]] if len(up_prob_features) == 1 else np.mean([enhanced_data[key] for key in up_prob_features])
        down_prob = enhanced_data[down_prob_features[0]] if len(down_prob_features) == 1 else np.mean([enhanced_data[key] for key in down_prob_features])

        enhanced_data['meta_prob_diff_up_vs_down'] = up_prob - down_prob
        print(f"    ✓ 添加特征: meta_prob_diff_up_vs_down = {enhanced_data['meta_prob_diff_up_vs_down']:.3f}")

        # 2. 基础模型概率总和 (Combined Conviction)
        enhanced_data['meta_prob_sum_up_down'] = up_prob + down_prob
        print(f"    ✓ 添加特征: meta_prob_sum_up_down = {enhanced_data['meta_prob_sum_up_down']:.3f}")
    else:
        print("    ! 警告: 未找到足够的UP/DOWN概率特征，跳过概率差异和总和特征")

    # 3. 滞后特征和变化特征 - 实时预测时需要从历史状态获取
    # 获取上一次的预测概率（如果有的话）
    last_meta_predictions = global_prediction_state_manager.get_last_meta_predictions()

    # 🎯 修复：为每个基础概率特征添加滞后和变化特征 - 支持精英格式
    for key in list(enhanced_data.keys()):
        # 处理精英模型特征和传统概率特征
        if (key.startswith('oof_proba_') or key.startswith('prob_') or
            (key.startswith('meta_') and ('prob' in key)) or
            key.startswith('oof_BTC_15m_UP_elite_fold') or
            key.startswith('oof_BTC_15m_DOWN_elite_fold')):
            current_prob = enhanced_data[key]

            # 滞后特征
            lag_feature_name = f"meta_lag1_{key}"
            if last_meta_predictions and key in last_meta_predictions:
                lag_value = last_meta_predictions[key]
            else:
                # 如果没有历史数据，使用当前值的中位数估计（这里用当前值本身）
                lag_value = current_prob
            enhanced_data[lag_feature_name] = lag_value
            print(f"    ✓ 添加滞后特征: {lag_feature_name} = {lag_value:.3f}")

            # 变化特征
            change_feature_name = f"meta_change1_{key}"
            if last_meta_predictions and key in last_meta_predictions:
                change_value = current_prob - last_meta_predictions[key]
            else:
                # 如果没有历史数据，变化为0
                change_value = 0.0
            enhanced_data[change_feature_name] = change_value
            print(f"    ✓ 添加变化特征: {change_feature_name} = {change_value:.3f}")

    # 保存当前预测概率供下次使用 - 只保存真正的概率特征
    current_predictions = {key: val for key, val in enhanced_data.items()
                          if (key.startswith('oof_proba_') or key.startswith('prob_') or
                              (key.startswith('meta_') and ('prob' in key)))}
    global_prediction_state_manager.set_last_meta_predictions(current_predictions)

    # 🎯 核心优化建议2.4：增强的上下文感知特征工程
    feature_engineering_config = getattr(config, 'META_MODEL_FEATURE_ENGINEERING_CONFIG', {})

    # 1. 基础模型间的分歧度特征
    if feature_engineering_config.get('enable_model_divergence', True):
        if len(up_prob_features) >= 2:
            up_probs = [enhanced_data[feat] for feat in up_prob_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if len(up_probs) >= 2:
                enhanced_data['up_models_divergence'] = np.std(up_probs)
                enhanced_data['up_models_max_diff'] = max(up_probs) - min(up_probs)
                enhanced_data['up_models_range'] = max(up_probs) - min(up_probs)
                enhanced_data['up_models_mean'] = np.mean(up_probs)
            else:
                enhanced_data['up_models_divergence'] = 0.0
                enhanced_data['up_models_max_diff'] = 0.0
                enhanced_data['up_models_range'] = 0.0
                enhanced_data['up_models_mean'] = 0.5
        else:
            enhanced_data['up_models_divergence'] = 0.0
            enhanced_data['up_models_max_diff'] = 0.0
            enhanced_data['up_models_range'] = 0.0
            enhanced_data['up_models_mean'] = 0.5

        if len(down_prob_features) >= 2:
            down_probs = [enhanced_data[feat] for feat in down_prob_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if len(down_probs) >= 2:
                enhanced_data['down_models_divergence'] = np.std(down_probs)
                enhanced_data['down_models_max_diff'] = max(down_probs) - min(down_probs)
                enhanced_data['down_models_range'] = max(down_probs) - min(down_probs)
                enhanced_data['down_models_mean'] = np.mean(down_probs)
            else:
                enhanced_data['down_models_divergence'] = 0.0
                enhanced_data['down_models_max_diff'] = 0.0
                enhanced_data['down_models_range'] = 0.0
                enhanced_data['down_models_mean'] = 0.5
        else:
            enhanced_data['down_models_divergence'] = 0.0
            enhanced_data['down_models_max_diff'] = 0.0
            enhanced_data['down_models_range'] = 0.0
            enhanced_data['down_models_mean'] = 0.5

    # 2. 模型置信度聚合特征
    if feature_engineering_config.get('enable_confidence_features', True):
        confidence_features = [key for key in enhanced_data.keys() if 'confidence' in key.lower()]
        if confidence_features:
            confidence_values = [enhanced_data[feat] for feat in confidence_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if confidence_values:
                enhanced_data['meta_avg_confidence'] = np.mean(confidence_values)
                enhanced_data['meta_max_confidence'] = np.max(confidence_values)
                enhanced_data['meta_min_confidence'] = np.min(confidence_values)
                enhanced_data['meta_confidence_spread'] = np.max(confidence_values) - np.min(confidence_values)
            else:
                enhanced_data['meta_avg_confidence'] = 0.0
                enhanced_data['meta_max_confidence'] = 0.0
                enhanced_data['meta_min_confidence'] = 0.0
                enhanced_data['meta_confidence_spread'] = 0.0

    # 3. 市场状态聚合特征
    if feature_engineering_config.get('enable_market_state_features', True):
        atr_features = [key for key in enhanced_data.keys() if 'atr' in key.lower()]
        adx_features = [key for key in enhanced_data.keys() if 'adx' in key.lower()]
        rsi_features = [key for key in enhanced_data.keys() if 'rsi' in key.lower()]

        if atr_features:
            atr_values = [enhanced_data[feat] for feat in atr_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if atr_values:
                enhanced_data['meta_avg_atr'] = np.mean(atr_values)
                enhanced_data['meta_volatility_consistency'] = 1.0 / (1.0 + np.std(atr_values))  # 波动率一致性

        if adx_features:
            adx_values = [enhanced_data[feat] for feat in adx_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if adx_values:
                enhanced_data['meta_avg_adx'] = np.mean(adx_values)
                enhanced_data['meta_trend_consistency'] = 1.0 / (1.0 + np.std(adx_values))  # 趋势一致性

        if rsi_features:
            rsi_values = [enhanced_data[feat] for feat in rsi_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if rsi_values:
                enhanced_data['meta_avg_rsi'] = np.mean(rsi_values)
                enhanced_data['meta_rsi_extremeness'] = abs(np.mean(rsi_values) - 50.0) / 50.0  # RSI极端程度

    # 4. 交互特征
    if feature_engineering_config.get('enable_interaction_features', True):
        # 概率×置信度交互
        for prob_feat in up_prob_features + down_prob_features:
            if prob_feat in enhanced_data:
                prob_val = enhanced_data[prob_feat]
                # 寻找对应的置信度特征
                for conf_feat in confidence_features:
                    if conf_feat in enhanced_data:
                        conf_val = enhanced_data[conf_feat]
                        interaction_name = f'interaction_{prob_feat}_x_{conf_feat}'
                        enhanced_data[interaction_name] = prob_val * conf_val

        # 概率差异特征
        if len(up_prob_features) >= 1 and len(down_prob_features) >= 1:
            up_prob = enhanced_data.get(up_prob_features[0], 0.5)
            down_prob = enhanced_data.get(down_prob_features[0], 0.5)
            enhanced_data['meta_prob_diff_up_vs_down'] = up_prob - down_prob
            enhanced_data['meta_prob_sum_up_down'] = up_prob + down_prob
            enhanced_data['meta_prob_ratio_up_vs_down'] = up_prob / (down_prob + 1e-8)

    # 确保所有训练时的特征都存在，缺失的用默认值填充
    for feature_name in trained_feature_names:
        if feature_name not in enhanced_data:
            if 'prob' in feature_name.lower():
                default_value = 0.5
            else:
                default_value = 0.0
            enhanced_data[feature_name] = default_value
            print(f"    ! 填充缺失特征: {feature_name} = {default_value}")

    print(f"  ✓ 实时特征工程完成，特征数量: {len(enhanced_data)}")
    return enhanced_data


def perform_shap_analysis(trained_model, X_val, y_val, feature_names, save_dir):
    """
    执行SHAP可解释性分析

    Args:
        trained_model: 训练好的LightGBM模型
        X_val: 验证集特征数据 (numpy array)
        y_val: 验证集标签数据 (numpy array)
        feature_names: 特征名称列表
        save_dir: 保存目录

    Returns:
        dict: SHAP分析结果
    """
    if not SHAP_AVAILABLE:
        print("  ⚠️ SHAP库不可用，跳过SHAP分析")
        return None

    if X_val is None or len(X_val) == 0:
        print("  ⚠️ 验证集数据为空，跳过SHAP分析")
        return None

    try:
        import numpy as np  # 确保numpy在函数开始时就被导入
        print(f"  开始SHAP分析，验证集样本数: {len(X_val)}, 特征数: {len(feature_names)}")

        # 创建SHAP图表保存目录
        shap_plots_dir = os.path.join(save_dir, "shap_plots")
        os.makedirs(shap_plots_dir, exist_ok=True)

        # 限制样本数量以提高计算效率（最多使用1000个样本）
        max_samples = min(1000, len(X_val))
        if len(X_val) > max_samples:
            print(f"  为提高计算效率，从 {len(X_val)} 个样本中随机选择 {max_samples} 个进行SHAP分析")
            indices = np.random.choice(len(X_val), max_samples, replace=False)
            X_val_sample = X_val[indices]
            y_val_sample = y_val[indices] if y_val is not None else None
        else:
            X_val_sample = X_val
            y_val_sample = y_val

        # 创建DataFrame用于SHAP分析
        X_val_df = pd.DataFrame(X_val_sample, columns=feature_names)

        # 1. 创建SHAP TreeExplainer
        print("  创建SHAP TreeExplainer...")
        explainer = shap.TreeExplainer(trained_model)

        # 2. 计算SHAP值
        print("  计算SHAP值...")
        shap_values = explainer.shap_values(X_val_df)

        # 对于多分类问题，shap_values是一个列表，每个类别一个数组
        # 但有时LightGBM多分类也可能返回3D数组而不是列表
        if isinstance(shap_values, list):
            print(f"  多分类SHAP值计算完成，类别数: {len(shap_values)}")
            # 计算平均绝对SHAP值（所有类别的平均）
            mean_abs_shap = np.mean([np.abs(sv).mean(axis=0) for sv in shap_values], axis=0)
        elif hasattr(shap_values, 'shape') and len(shap_values.shape) == 3:
            print(f"  多分类SHAP值计算完成 (3D数组)，类别数: {shap_values.shape[2]}")
            # 转换为列表格式以保持一致性
            shap_values = [shap_values[:, :, i] for i in range(shap_values.shape[2])]
            mean_abs_shap = np.mean([np.abs(sv).mean(axis=0) for sv in shap_values], axis=0)
        else:
            print("  二分类SHAP值计算完成")
            mean_abs_shap = np.abs(shap_values).mean(axis=0)

        # 确保mean_abs_shap是一维数组
        if mean_abs_shap.ndim > 1:
            mean_abs_shap = mean_abs_shap.flatten()

        # 3. 特征重要性排序
        feature_importance = [(feature_names[i], float(mean_abs_shap[i])) for i in range(len(feature_names))]
        feature_importance.sort(key=lambda x: x[1], reverse=True)

        print("  前10个最重要特征:")
        for i, (feat_name, importance) in enumerate(feature_importance[:10]):
            print(f"    {i+1:2d}. {feat_name:<35} : {importance:.6f}")

        # 4. 生成SHAP图表
        analysis_results = {
            'feature_importance': feature_importance,
            'top_10_features': feature_importance[:10],
            'shap_plots_saved': [],
            'analysis_summary': {
                'total_features': len(feature_names),
                'samples_analyzed': len(X_val_sample),
                'model_type': 'LightGBM',
                'classes': len(shap_values) if isinstance(shap_values, list) else 2
            }
        }

        # 设置matplotlib后端和样式
        plt.style.use('default')
        plt.rcParams['font.size'] = 10
        plt.rcParams['figure.dpi'] = 100

        # 5. 生成并保存SHAP图表
        try:
            print("  生成SHAP图表...")

            # 设置中文字体支持
            try:
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass  # 如果字体设置失败，继续使用默认字体

            # 5.1 特征重要性条形图
            print("    生成特征重要性图...")
            plt.figure(figsize=(12, 8))
            top_features = feature_importance[:15]  # 显示前15个特征
            feat_names = [f[0] for f in top_features]
            feat_values = [f[1] for f in top_features]

            # 创建水平条形图
            y_pos = range(len(feat_names))
            bars = plt.barh(y_pos, feat_values, color='skyblue', alpha=0.7)

            # 设置标签和标题
            plt.yticks(y_pos, feat_names)
            plt.xlabel('平均绝对SHAP值')
            plt.title('元模型特征重要性 (基于SHAP值)', fontsize=14, fontweight='bold')
            plt.gca().invert_yaxis()  # 最重要的特征在顶部

            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars, feat_values)):
                plt.text(value + max(feat_values) * 0.01, bar.get_y() + bar.get_height()/2,
                        f'{value:.4f}', va='center', fontsize=9)

            plt.tight_layout()
            importance_plot_path = os.path.join(shap_plots_dir, "feature_importance.png")
            plt.savefig(importance_plot_path, dpi=150, bbox_inches='tight')
            plt.close()
            analysis_results['shap_plots_saved'].append(importance_plot_path)
            print(f"    ✓ 特征重要性图已保存: {importance_plot_path}")

            # 5.2 SHAP Summary Plots (散点/小提琴图) for each class
            if len(X_val_sample) <= 500:  # 限制样本数以避免图表过于复杂
                print("    生成SHAP summary plots...")

                if isinstance(shap_values, list):
                    # 多分类：为每个类别生成Summary Plot
                    class_names = ["明确下跌_元", "明确上涨_元", "中性_元"] if len(shap_values) >= 3 else [f"Class {i}" for i in range(len(shap_values))]

                    # Class 1 ("明确上涨_元") Summary Plot
                    if len(shap_values) > 1:
                        print("      生成Class 1 (明确上涨_元) Summary Plot...")
                        plt.figure(figsize=(12, 8))
                        shap.summary_plot(shap_values[1], X_val_df, show=False, max_display=15)
                        plt.title('SHAP Summary Plot - 明确上涨_元 (Class 1)', fontsize=14, fontweight='bold')
                        summary_plot_class1_path = os.path.join(shap_plots_dir, "shap_summary_plot_class1_up.png")
                        plt.savefig(summary_plot_class1_path, dpi=150, bbox_inches='tight')
                        plt.close()
                        analysis_results['shap_plots_saved'].append(summary_plot_class1_path)
                        print(f"      ✓ Class 1 Summary Plot已保存: {summary_plot_class1_path}")

                    # Class 0 ("明确下跌_元") Summary Plot
                    if len(shap_values) > 0:
                        print("      生成Class 0 (明确下跌_元) Summary Plot...")
                        plt.figure(figsize=(12, 8))
                        shap.summary_plot(shap_values[0], X_val_df, show=False, max_display=15)
                        plt.title('SHAP Summary Plot - 明确下跌_元 (Class 0)', fontsize=14, fontweight='bold')
                        summary_plot_class0_path = os.path.join(shap_plots_dir, "shap_summary_plot_class0_down.png")
                        plt.savefig(summary_plot_class0_path, dpi=150, bbox_inches='tight')
                        plt.close()
                        analysis_results['shap_plots_saved'].append(summary_plot_class0_path)
                        print(f"      ✓ Class 0 Summary Plot已保存: {summary_plot_class0_path}")

                else:
                    # 二分类
                    plt.figure(figsize=(12, 8))
                    shap.summary_plot(shap_values, X_val_df, show=False, max_display=15)
                    plt.title('SHAP Summary Plot', fontsize=14, fontweight='bold')
                    summary_plot_path = os.path.join(shap_plots_dir, "shap_summary_plot.png")
                    plt.savefig(summary_plot_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    analysis_results['shap_plots_saved'].append(summary_plot_path)
                    print(f"    ✓ SHAP summary plot已保存: {summary_plot_path}")

            # 5.3 单个样本的Waterfall Plot（选择一个有趣的样本）
            print("    生成SHAP waterfall plot...")
            sample_idx = 0  # 选择第一个样本

            try:
                if isinstance(shap_values, list):
                    # 多分类：为上涨类别（类别1）生成waterfall plot
                    class_idx = 1  # 上涨类别
                    expected_value = explainer.expected_value[class_idx]
                    shap_vals_sample = shap_values[class_idx][sample_idx]
                    class_name = "上涨类别"
                else:
                    expected_value = explainer.expected_value
                    shap_vals_sample = shap_values[sample_idx]
                    class_name = "预测"

                plt.figure(figsize=(12, 8))

                # 创建SHAP Explanation对象
                explanation = shap.Explanation(
                    values=shap_vals_sample,
                    base_values=expected_value,
                    data=X_val_sample[sample_idx],
                    feature_names=feature_names
                )

                # 生成waterfall plot
                shap.plots.waterfall(explanation, max_display=15, show=False)
                plt.title(f'SHAP Waterfall Plot - 样本 {sample_idx+1} ({class_name})', fontsize=14, fontweight='bold')

                waterfall_plot_path = os.path.join(shap_plots_dir, "shap_waterfall_plot.png")
                plt.savefig(waterfall_plot_path, dpi=150, bbox_inches='tight')
                plt.close()
                analysis_results['shap_plots_saved'].append(waterfall_plot_path)
                print(f"    ✓ SHAP waterfall plot已保存: {waterfall_plot_path}")

            except Exception as e_waterfall:
                print(f"    ⚠️ 生成waterfall plot时出现错误: {e_waterfall}")
                # 尝试生成简化版本的waterfall plot
                try:
                    plt.figure(figsize=(12, 8))
                    if isinstance(shap_values, list):
                        # 使用第一个类别的SHAP值
                        shap_vals_to_plot = shap_values[1][sample_idx]  # 上涨类别
                        title_suffix = " (上涨类别)"
                    else:
                        shap_vals_to_plot = shap_values[sample_idx]
                        title_suffix = ""

                    # 创建简化的条形图显示特征贡献
                    # 确保shap_vals_to_plot是一维数组
                    if hasattr(shap_vals_to_plot, 'shape') and len(shap_vals_to_plot.shape) > 1:
                        shap_vals_to_plot = shap_vals_to_plot.flatten()

                    feature_contributions = list(zip(feature_names, shap_vals_to_plot))
                    feature_contributions.sort(key=lambda x: abs(float(x[1])), reverse=True)

                    # 只显示前10个最重要的特征
                    top_features = feature_contributions[:10]
                    feat_names = [f[0] for f in top_features]
                    feat_values = [f[1] for f in top_features]

                    colors = ['red' if v < 0 else 'blue' for v in feat_values]
                    y_pos = range(len(feat_names))

                    plt.barh(y_pos, feat_values, color=colors, alpha=0.7)
                    plt.yticks(y_pos, feat_names)
                    plt.xlabel('SHAP值 (特征贡献)')
                    plt.title(f'特征贡献分析 - 样本 {sample_idx+1}{title_suffix}', fontsize=14, fontweight='bold')
                    plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

                    waterfall_plot_path = os.path.join(shap_plots_dir, "shap_feature_contributions.png")
                    plt.savefig(waterfall_plot_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    analysis_results['shap_plots_saved'].append(waterfall_plot_path)
                    print(f"    ✓ 特征贡献图已保存: {waterfall_plot_path}")

                except Exception as e_backup:
                    print(f"    ⚠️ 生成备用图表也失败: {e_backup}")
                    # 继续执行，不影响整体分析

            # 5.4 生成详细的Dependence Plots
            print("    生成SHAP dependence plots...")
            try:
                if isinstance(shap_values, list) and len(shap_values) >= 2:
                    # 定义要分析的关键特征
                    key_features_to_analyze = [
                        'meta_prob_sum_up_down',
                        'meta_change1_meta_prob_sum_up_down',
                        'meta_change1_meta_prob_diff_up_vs_down',
                        'meta_lag1_oof_proba_BTC_15m_DOWN_pfavorable',
                        'oof_proba_BTC_15m_UP_pfavorable',
                        'oof_proba_BTC_15m_DOWN_pfavorable',
                        'global_pdi',
                        'global_mdi',
                        'global_trend_signal'
                    ]

                    # 找到实际存在的特征
                    available_features = []
                    for feat in key_features_to_analyze:
                        if feat in feature_names:
                            available_features.append(feat)

                    print(f"      将为 {len(available_features)} 个关键特征生成dependence plots")

                    # 为每个关键特征生成Class 0和Class 1的dependence plots
                    for feat_name in available_features:
                        try:
                            feat_idx = feature_names.index(feat_name)

                            # Class 1 ("明确上涨_元") Dependence Plot
                            plt.figure(figsize=(10, 6))
                            interaction_feature = None
                            if feat_name == 'meta_prob_sum_up_down':
                                # 使用meta_prob_diff_up_vs_down作为交互特征
                                if 'meta_prob_diff_up_vs_down' in feature_names:
                                    interaction_feature = feature_names.index('meta_prob_diff_up_vs_down')
                            elif feat_name == 'meta_lag1_oof_proba_BTC_15m_DOWN_pfavorable':
                                # 使用当前DOWN概率作为交互特征
                                if 'oof_proba_BTC_15m_DOWN_pfavorable' in feature_names:
                                    interaction_feature = feature_names.index('oof_proba_BTC_15m_DOWN_pfavorable')

                            shap.dependence_plot(
                                feat_idx,
                                shap_values[1],
                                X_val_df,
                                feature_names=feature_names,
                                interaction_index=interaction_feature,
                                show=False
                            )
                            plt.title(f'Dependence Plot: {feat_name} on 明确上涨_元 (Class 1)', fontsize=12, fontweight='bold')
                            dep_plot_class1_path = os.path.join(shap_plots_dir, f"dependence_{feat_name}_class1_up.png")
                            plt.savefig(dep_plot_class1_path, dpi=150, bbox_inches='tight')
                            plt.close()
                            analysis_results['shap_plots_saved'].append(dep_plot_class1_path)
                            print(f"        ✓ {feat_name} Class 1 dependence plot已保存")

                            # Class 0 ("明确下跌_元") Dependence Plot
                            plt.figure(figsize=(10, 6))
                            shap.dependence_plot(
                                feat_idx,
                                shap_values[0],
                                X_val_df,
                                feature_names=feature_names,
                                interaction_index=interaction_feature,
                                show=False
                            )
                            plt.title(f'Dependence Plot: {feat_name} on 明确下跌_元 (Class 0)', fontsize=12, fontweight='bold')
                            dep_plot_class0_path = os.path.join(shap_plots_dir, f"dependence_{feat_name}_class0_down.png")
                            plt.savefig(dep_plot_class0_path, dpi=150, bbox_inches='tight')
                            plt.close()
                            analysis_results['shap_plots_saved'].append(dep_plot_class0_path)
                            print(f"        ✓ {feat_name} Class 0 dependence plot已保存")

                        except Exception as e_dep_feat:
                            print(f"        ⚠️ 生成 {feat_name} dependence plots时出错: {e_dep_feat}")
                            continue

                else:
                    print("      跳过dependence plots生成 (非多分类模型或类别数不足)")

            except Exception as e_dep_plots:
                print(f"    ⚠️ 生成dependence plots时出现错误: {e_dep_plots}")

        except Exception as e_plot:
            print(f"    ⚠️ 生成SHAP图表时出现错误: {e_plot}")
            # 图表生成失败不影响整体分析

        # 6. 保存分析结果到JSON文件
        try:
            # 转换numpy类型为Python原生类型以便JSON序列化
            json_results = {
                'feature_importance': [(name, float(importance)) for name, importance in feature_importance],
                'top_10_features': [(name, float(importance)) for name, importance in feature_importance[:10]],
                'analysis_summary': analysis_results['analysis_summary'],
                'shap_plots_saved': analysis_results['shap_plots_saved'],
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            }

            shap_json_path = os.path.join(save_dir, "shap_analysis.json")
            with open(shap_json_path, 'w', encoding='utf-8') as f:
                json.dump(json_results, f, indent=2, ensure_ascii=False)

            print(f"  ✅ SHAP分析结果已保存: {shap_json_path}")
            analysis_results['json_saved'] = shap_json_path

        except Exception as e_json:
            print(f"  ⚠️ 保存SHAP分析JSON时出现错误: {e_json}")

        return analysis_results

    except Exception as e:
        print(f"  ❌ SHAP分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc(limit=3)
        return None

def calculate_simulated_profit_meta(y_true_meta_val, y_proba_meta_val,
                                   threshold_up=0.4, threshold_down=0.4,
                                   confidence_gap_up=0.1, confidence_gap_down=0.1,
                                   payout_ratio=0.85, verbose=False):
    """
    计算元模型在给定决策阈值下的模拟交易利润

    参数:
    - y_true_meta_val: 验证集真实标签 (0=下跌, 1=上涨, 2=中性)
    - y_proba_meta_val: 验证集概率预测 [P(下跌), P(上涨), P(中性)]
    - threshold_up: 做多决策阈值
    - threshold_down: 做空决策阈值
    - confidence_gap_up: 做多置信度差值要求
    - confidence_gap_down: 做空置信度差值要求
    - payout_ratio: 盈亏比 (胜利时的收益率)
    - verbose: 是否输出详细信息

    返回:
    - dict: 包含期望收益、交易次数、胜率等指标
    """
    if len(y_true_meta_val) == 0 or len(y_proba_meta_val) == 0:
        return {
            'expected_profit_per_trade': 0.0,
            'total_trades': 0,
            'win_rate': 0.0,
            'up_trades': 0,
            'down_trades': 0,
            'up_wins': 0,
            'down_wins': 0,
            'total_profit': 0.0,
            'risk_adjusted_return': 0.0
        }

    total_trades = 0
    total_wins = 0
    total_profit = 0.0
    up_trades = 0
    down_trades = 0
    up_wins = 0
    down_wins = 0

    for i in range(len(y_true_meta_val)):
        true_label = y_true_meta_val[i]
        probas = y_proba_meta_val[i]

        p_down = probas[0]   # P(明确下跌)
        p_up = probas[1]     # P(明确上涨)
        p_neutral = probas[2] # P(中性)

        # 决策逻辑
        signal = None

        # 检查做多条件
        if (p_up >= threshold_up and
            p_up - p_neutral >= confidence_gap_up):
            signal = "UP"
            up_trades += 1

        # 检查做空条件
        elif (p_down >= threshold_down and
              p_down - p_neutral >= confidence_gap_down):
            signal = "DOWN"
            down_trades += 1

        # 如果有信号，计算盈亏
        if signal:
            total_trades += 1

            # 判断交易结果
            is_win = False
            if signal == "UP" and true_label == 1:  # 预测上涨且实际上涨
                is_win = True
                up_wins += 1
            elif signal == "DOWN" and true_label == 0:  # 预测下跌且实际下跌
                is_win = True
                down_wins += 1

            # 计算盈亏 (假设每次下注1单位)
            if is_win:
                total_profit += payout_ratio  # 盈利
                total_wins += 1
            else:
                total_profit -= 1.0  # 亏损全部本金

    # 计算指标
    win_rate = total_wins / total_trades if total_trades > 0 else 0.0
    expected_profit_per_trade = total_profit / total_trades if total_trades > 0 else 0.0

    # 风险调整收益 (考虑交易频率)
    risk_adjusted_return = expected_profit_per_trade * np.sqrt(total_trades) if total_trades > 0 else 0.0

    if verbose:
        print(f"    模拟交易结果: 总交易={total_trades}, 胜率={win_rate:.3f}, "
              f"期望收益/交易={expected_profit_per_trade:.4f}")
        print(f"    UP交易: {up_trades}次, 胜{up_wins}次 | DOWN交易: {down_trades}次, 胜{down_wins}次")

    # 计算更详细的指标
    up_win_rate = up_wins / up_trades if up_trades > 0 else 0.0
    down_win_rate = down_wins / down_trades if down_trades > 0 else 0.0

    # 计算各方向的精确率、召回率、F1分数
    from sklearn.metrics import precision_recall_fscore_support

    # 为了计算P/R/F1，我们需要构造预测和真实标签
    y_pred_signals = []
    y_true_signals = []

    for i in range(len(y_true_meta_val)):
        true_label = y_true_meta_val[i]
        probas = y_proba_meta_val[i]

        p_down = probas[0]
        p_up = probas[1]
        p_neutral = probas[2]

        # 决策逻辑
        signal = None
        if (p_up >= threshold_up and p_up - p_neutral >= confidence_gap_up):
            signal = 1  # UP
        elif (p_down >= threshold_down and p_down - p_neutral >= confidence_gap_down):
            signal = 0  # DOWN
        else:
            signal = 2  # NO_SIGNAL

        if signal != 2:  # 只记录有信号的情况
            y_pred_signals.append(signal)
            y_true_signals.append(true_label)

    # 计算各类别的P/R/F1
    up_precision = up_recall = up_f1 = 0.0
    down_precision = down_recall = down_f1 = 0.0

    if len(y_pred_signals) > 0:
        try:
            precision, recall, f1, _ = precision_recall_fscore_support(
                y_true_signals, y_pred_signals, labels=[0, 1], average=None, zero_division=0
            )
            if len(precision) >= 2:
                down_precision, up_precision = precision[0], precision[1]
                down_recall, up_recall = recall[0], recall[1]
                down_f1, up_f1 = f1[0], f1[1]
        except:
            pass

    # 计算更多关键性能指标
    total_samples = len(y_true_meta_val)
    trade_frequency = total_trades / total_samples if total_samples > 0 else 0.0

    # 计算夏普比率近似值 (期望收益 / 收益波动率的近似)
    sharpe_ratio = 0.0
    if total_trades > 1 and win_rate > 0 and win_rate < 1:
        # 使用胜率的标准差作为波动率的近似
        volatility_proxy = np.sqrt(win_rate * (1 - win_rate))
        if volatility_proxy > 0:
            sharpe_ratio = expected_profit_per_trade / volatility_proxy

    # 计算最大连续亏损 (模拟)
    max_consecutive_losses = 0
    current_consecutive_losses = 0
    for i in range(len(y_true_meta_val)):
        true_label = y_true_meta_val[i]
        probas = y_proba_meta_val[i]

        p_down = probas[0]
        p_up = probas[1]
        p_neutral = probas[2]

        signal = None
        if (p_up >= threshold_up and p_up - p_neutral >= confidence_gap_up):
            signal = "UP"
        elif (p_down >= threshold_down and p_down - p_neutral >= confidence_gap_down):
            signal = "DOWN"

        if signal:
            is_win = (signal == "UP" and true_label == 1) or (signal == "DOWN" and true_label == 0)
            if is_win:
                current_consecutive_losses = 0
            else:
                current_consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)

    # 计算盈亏比 (平均盈利/平均亏损)
    profit_loss_ratio = payout_ratio if win_rate > 0 else 0.0  # 简化计算，实际盈利是payout_ratio，亏损是1

    # 计算期望收益的置信区间 (简化版)
    if total_trades > 0:
        profit_std = np.sqrt(win_rate * (payout_ratio ** 2) + (1 - win_rate) * (1 ** 2))
        profit_confidence_interval = 1.96 * profit_std / np.sqrt(total_trades)  # 95%置信区间
    else:
        profit_confidence_interval = 0.0

    # 计算交易平衡度 (UP/DOWN交易的平衡程度)
    trade_balance_score = 0.0
    if up_trades > 0 and down_trades > 0:
        trade_balance_score = min(up_trades, down_trades) / max(up_trades, down_trades)

    return {
        # === 核心盈利指标 ===
        'expected_profit_per_trade': expected_profit_per_trade,  # 平均每笔交易期望收益
        'total_profit': total_profit,                            # 总利润
        'average_profit_per_trade': expected_profit_per_trade,   # 别名，便于Optuna使用

        # === 交易统计 ===
        'total_trades': total_trades,                            # 总交易次数
        'num_trades': total_trades,                              # 别名，便于Optuna使用
        'trade_frequency': trade_frequency,                      # 交易频率 (交易次数/总样本数)
        'up_trades': up_trades,                                  # 做多交易次数
        'down_trades': down_trades,                              # 做空交易次数
        'trade_balance_score': trade_balance_score,              # 交易平衡度 [0,1]

        # === 胜率指标 ===
        'win_rate': win_rate,                                    # 总胜率
        'overall_win_rate': win_rate,                            # 总胜率 (别名，便于Optuna使用)
        'up_wins': up_wins,                                      # 做多胜利次数
        'down_wins': down_wins,                                  # 做空胜利次数
        'up_win_rate': up_win_rate,                              # 做多胜率
        'down_win_rate': down_win_rate,                          # 做空胜率

        # === 风险指标 ===
        'risk_adjusted_return': risk_adjusted_return,            # 风险调整收益
        'sharpe_ratio': sharpe_ratio,                            # 夏普比率近似值
        'max_consecutive_losses': max_consecutive_losses,        # 最大连续亏损次数
        'profit_loss_ratio': profit_loss_ratio,                  # 盈亏比
        'profit_confidence_interval': profit_confidence_interval, # 收益置信区间

        # === 精确率/召回率/F1 ===
        'up_precision': up_precision,                            # 做多精确率
        'up_recall': up_recall,                                  # 做多召回率
        'up_f1': up_f1,                                          # 做多F1分数
        'down_precision': down_precision,                        # 做空精确率
        'down_recall': down_recall,                              # 做空召回率
        'down_f1': down_f1,                                      # 做空F1分数

        # === 综合评分 ===
        'overall_f1': (up_f1 + down_f1) / 2.0,                  # 平均F1分数
        'balanced_accuracy': (up_win_rate + down_win_rate) / 2.0 if up_trades > 0 and down_trades > 0 else win_rate
    }


def _calculate_optimization_objective(result, total_samples=None):
    """
    计算优化目标分数，支持多种优化策略

    参数:
    - result: calculate_simulated_profit_meta 的返回结果
    - total_samples: 总样本数，用于计算交易频率相关指标

    返回:
    - float: 优化目标分数
    """
    # 获取配置的优化策略
    optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'risk_adjusted_return')

    if optimization_strategy == 'expected_profit':
        # 策略1: 纯期望收益最大化
        return result['expected_profit_per_trade']

    elif optimization_strategy == 'risk_adjusted_return':
        # 策略2: 风险调整收益 (期望收益 × √交易次数)
        return result['risk_adjusted_return']

    elif optimization_strategy == 'balanced_profit_frequency':
        # 策略3: 平衡收益和交易频率 (期望收益 × log(1 + 交易次数))
        if result['total_trades'] > 0:
            return result['expected_profit_per_trade'] * np.log(1 + result['total_trades'])
        else:
            return 0.0

    elif optimization_strategy == 'win_rate_weighted':
        # 策略4: 胜率加权的期望收益
        return result['expected_profit_per_trade'] * result['win_rate']

    elif optimization_strategy == 'f1_weighted':
        # 策略5: F1分数加权的期望收益
        avg_f1 = (result['up_f1'] + result['down_f1']) / 2.0
        return result['expected_profit_per_trade'] * avg_f1

    elif optimization_strategy == 'sharpe_like':
        # 策略6: 类似夏普比率 (期望收益 / 胜率标准差的近似)
        if result['win_rate'] > 0 and result['win_rate'] < 1:
            volatility_proxy = np.sqrt(result['win_rate'] * (1 - result['win_rate']))
            return result['expected_profit_per_trade'] / volatility_proxy if volatility_proxy > 0 else 0.0
        else:
            return result['expected_profit_per_trade']

    elif optimization_strategy == 'frequency_weighted_profit':
        # 策略7: 期望收益 × (交易次数/总样本数)^0.3 (新增)
        if total_samples and total_samples > 0 and result['total_trades'] > 0:
            frequency_factor = (result['total_trades'] / total_samples) ** 0.3
            return result['expected_profit_per_trade'] * frequency_factor
        else:
            return result['expected_profit_per_trade']

    elif optimization_strategy == 'trade_efficiency':
        # 策略8: (期望收益 × 交易次数) / 总样本数 (新增)
        if total_samples and total_samples > 0:
            return (result['expected_profit_per_trade'] * result['total_trades']) / total_samples
        else:
            return result['expected_profit_per_trade']

    elif optimization_strategy == 'composite_score':
        # 策略9: 综合评分，平衡收益、频率、胜率 (新增，推荐)
        base_profit = result['expected_profit_per_trade']
        win_rate_bonus = result['win_rate'] * 0.5  # 胜率奖励

        # 交易频率奖励 (适中的交易频率最佳)
        if total_samples and total_samples > 0:
            trade_frequency = result['total_trades'] / total_samples
            # 使用倒U型函数，最优频率在5-15%之间
            optimal_frequency = 0.1
            frequency_penalty = -abs(trade_frequency - optimal_frequency) * 2
        else:
            frequency_penalty = 0

        # 平衡UP/DOWN交易奖励
        if result['up_trades'] > 0 and result['down_trades'] > 0:
            balance_ratio = min(result['up_trades'], result['down_trades']) / max(result['up_trades'], result['down_trades'])
            balance_bonus = balance_ratio * 0.2
        else:
            balance_bonus = 0

        return base_profit + win_rate_bonus + frequency_penalty + balance_bonus

    else:
        # 默认: 风险调整收益
        return result['risk_adjusted_return']


def optimize_meta_decision_thresholds(y_true_meta_val, y_proba_meta_val,
                                    optimization_method='grid_search',
                                    n_trials=100, verbose=True,
                                    model_dir=None, model_name="meta_model"):
    """
    🎯 统一决策阈值优化：基于盈利能力的优化目标

    核心改进：
    - 统一使用 calculate_simulated_profit_meta 函数计算盈利指标
    - 支持多种优化策略（风险调整收益、复合评分等）
    - 网格搜索和Optuna都使用相同的优化目标函数
    - 优化目标直接关联实际交易盈利能力

    参数:
    - y_true_meta_val: 验证集真实标签
    - y_proba_meta_val: 验证集概率预测
    - optimization_method: 优化方法 ('grid_search' 或 'optuna')
    - n_trials: Optuna试验次数 (仅当method='optuna'时使用)
    - verbose: 是否输出详细信息

    返回:
    - dict: 最优阈值组合和性能指标（包含期望收益、胜率、夏普比率等）

    优化策略配置 (META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY):
    - 'risk_adjusted_return': 风险调整收益 (默认)
    - 'composite_score': 综合评分 (推荐)
    - 'expected_profit': 纯期望收益最大化
    - 'balanced_profit_frequency': 平衡收益和交易频率
    """
    if verbose:
        print(f"\n🎯 开始元模型决策阈值优化 (方法: {optimization_method})")
        print(f"   验证集大小: {len(y_true_meta_val)} 样本")
        print(f"   类别分布: {np.bincount(y_true_meta_val)}")

    best_result = {
        'threshold_up': 0.4,
        'threshold_down': 0.4,
        'confidence_gap_up': 0.1,
        'confidence_gap_down': 0.1,
        'expected_profit_per_trade': 0.0,
        'total_trades': 0,
        'win_rate': 0.0,
        'optimization_method': optimization_method
    }

    if optimization_method == 'grid_search':
        best_result = _grid_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, verbose, model_dir, model_name)
    elif optimization_method == 'optuna':
        best_result = _optuna_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, n_trials, verbose, model_dir, model_name)
    else:
        print(f"❌ 未知的优化方法: {optimization_method}")
        return best_result

    if verbose:
        print(f"\n✅ 阈值优化完成!")
        print(f"   最优阈值: UP={best_result['threshold_up']:.3f}, DOWN={best_result['threshold_down']:.3f}")
        print(f"   置信度差值: UP={best_result['confidence_gap_up']:.3f}, DOWN={best_result['confidence_gap_down']:.3f}")
        print(f"   期望收益/交易: {best_result['expected_profit_per_trade']:.4f}")
        print(f"   总交易次数: {best_result['total_trades']}")
        print(f"   胜率: {best_result['win_rate']:.3f}")

    return best_result


def _grid_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, verbose=True, model_dir=None, model_name="meta_model"):
    """
    网格搜索优化元模型决策阈值 - 基于盈利能力的统一优化版

    🎯 核心改进：
    - 统一使用 calculate_simulated_profit_meta 计算盈利指标
    - 通过 _calculate_optimization_objective 应用配置的优化策略
    - 确保与Optuna搜索使用相同的优化目标
    """

    # 从配置获取搜索范围
    threshold_min = getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.1)
    threshold_max = getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
    confidence_gap_min = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0)
    confidence_gap_max = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.5)

    # 约束配置
    min_trades = getattr(config, 'META_MODEL_MIN_TRADES_CONSTRAINT', 5)
    min_win_rate = getattr(config, 'META_MODEL_MIN_WIN_RATE_CONSTRAINT', 0.4)

    # 定义大幅扩展的搜索空间 (更细的步长以充分探索)
    threshold_range = np.arange(threshold_min, threshold_max + 0.01, 0.02)  # 步长0.02
    confidence_gap_range = np.arange(confidence_gap_min, confidence_gap_max + 0.01, 0.02)  # 步长0.02

    best_profit = -float('inf')
    best_params = None
    total_combinations = len(threshold_range) ** 2 * len(confidence_gap_range) ** 2
    total_samples = len(y_true_meta_val)

    if verbose:
        print(f"   🔍 增强网格搜索空间: {total_combinations:,} 种组合")
        print(f"   📊 扩展搜索范围:")
        print(f"      - 阈值: [{threshold_min:.1f}, {threshold_max:.1f}] (步长: 0.02)")
        print(f"      - 置信度差值: [{confidence_gap_min:.1f}, {confidence_gap_max:.1f}] (步长: 0.02)")
        print(f"   🔒 约束: 最小交易数≥{min_trades}, 最小胜率≥{min_win_rate:.1f}")

    tested_combinations = 0

    for threshold_up in threshold_range:
        for threshold_down in threshold_range:
            for confidence_gap_up in confidence_gap_range:
                for confidence_gap_down in confidence_gap_range:

                    result = calculate_simulated_profit_meta(
                        y_true_meta_val, y_proba_meta_val,
                        threshold_up=threshold_up,
                        threshold_down=threshold_down,
                        confidence_gap_up=confidence_gap_up,
                        confidence_gap_down=confidence_gap_down,
                        verbose=False
                    )

                    # 强化约束检查
                    if result['total_trades'] < min_trades or result['win_rate'] < min_win_rate:
                        tested_combinations += 1
                        continue  # 跳过不满足约束的组合

                    # 使用增强的优化目标选择 (传入总样本数)
                    objective_score = _calculate_optimization_objective(result, total_samples)

                    # 更新最佳结果
                    if objective_score > best_profit:
                        best_profit = objective_score
                        best_params = {
                            'threshold_up': threshold_up,
                            'threshold_down': threshold_down,
                            'confidence_gap_up': confidence_gap_up,
                            'confidence_gap_down': confidence_gap_down,
                            **result,
                            'optimization_method': 'grid_search',
                            'objective_score': objective_score
                        }

                    tested_combinations += 1

                    if verbose and tested_combinations % 500 == 0:
                        print(f"   已测试 {tested_combinations}/{total_combinations} 组合...")

    if best_params is None:
        # 如果没有找到满足最小交易次数的组合，降低约束
        if verbose:
            print("   ⚠️ 未找到满足最小交易次数(5)的组合，降低约束重新搜索...")

        best_profit = -float('inf')
        for threshold_up in threshold_range:
            for threshold_down in threshold_range:
                for confidence_gap_up in confidence_gap_range:
                    for confidence_gap_down in confidence_gap_range:

                        result = calculate_simulated_profit_meta(
                            y_true_meta_val, y_proba_meta_val,
                            threshold_up=threshold_up,
                            threshold_down=threshold_down,
                            confidence_gap_up=confidence_gap_up,
                            confidence_gap_down=confidence_gap_down,
                            verbose=False
                        )

                        # 🎯 统一使用基于盈利能力的优化目标 (即使在降级搜索中)
                        objective_score = _calculate_optimization_objective(result, total_samples)

                        if result['total_trades'] >= 1 and objective_score > best_profit:
                            best_profit = objective_score
                            best_params = {
                                'threshold_up': threshold_up,
                                'threshold_down': threshold_down,
                                'confidence_gap_up': confidence_gap_up,
                                'confidence_gap_down': confidence_gap_down,
                                **result,
                                'optimization_method': 'grid_search',
                                'objective_score': objective_score
                            }

    return best_params if best_params else {
        'threshold_up': 0.4, 'threshold_down': 0.4,
        'confidence_gap_up': 0.1, 'confidence_gap_down': 0.1,
        'expected_profit_per_trade': 0.0, 'total_trades': 0, 'win_rate': 0.0,
        'optimization_method': 'grid_search'
    }


def _optuna_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, n_trials=100, verbose=True, model_dir=None, model_name="meta_model"):
    """
    使用Optuna优化元模型决策阈值 - 基于盈利能力的统一优化版

    🎯 核心改进：
    - 目标函数直接调用 calculate_simulated_profit_meta 计算盈利指标
    - 支持多种优化策略（通过配置选择）
    - 强制约束优化，确保交易质量
    - 与网格搜索使用相同的优化目标逻辑
    """

    # 从配置获取搜索范围
    threshold_min = getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.1)
    threshold_max = getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
    confidence_gap_min = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0)
    confidence_gap_max = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.5)

    # 约束配置
    min_trades = getattr(config, 'META_MODEL_MIN_TRADES_CONSTRAINT', 5)
    min_win_rate = getattr(config, 'META_MODEL_MIN_WIN_RATE_CONSTRAINT', 0.4)
    enable_early_stopping = getattr(config, 'META_MODEL_ENABLE_EARLY_STOPPING', True)
    timeout = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_TIMEOUT', 3600)
    early_stop_patience = getattr(config, 'META_MODEL_OPTUNA_EARLY_STOP_PATIENCE', 30)
    n_startup_trials = getattr(config, 'META_MODEL_OPTUNA_SAMPLER_N_STARTUP_TRIALS', 50)

    # 获取优化策略
    optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')

    # 计算验证集大小用于约束计算
    total_samples = len(y_true_meta_val)

    if verbose:
        print(f"   🎯 Optuna增强优化版: {n_trials} 次试验 (超时: {timeout//60}分钟)")
        print(f"   📊 优化搜索空间:")
        print(f"      - 阈值范围: [{threshold_min:.1f}, {threshold_max:.1f}]")
        print(f"      - 置信度差值: [{confidence_gap_min:.1f}, {confidence_gap_max:.1f}] (降低最大值以探索更小GAP)")
        print(f"   🔒 动态约束优化:")
        print(f"      - 验证集大小: {total_samples}")
        print(f"      - 最小交易数: 基于验证集长度动态计算 (至少{max(20, int(total_samples * 0.01))}次)")
        print(f"      - 最小胜率: 动态范围 [0.20, 0.50]")
        print(f"   🎯 优化策略: {optimization_strategy}")
        print(f"   ⚡ 早停设置: {early_stop_patience}次无改进停止, 前{n_startup_trials}次随机探索")

    total_samples = len(y_true_meta_val)
    best_score_so_far = -float('inf')
    trials_without_improvement = 0

    def objective(trial):
        """
        🎯 优化的Optuna目标函数 - 聚焦核心参数优化

        核心优化：
        1. 聚焦优化参数：只搜索threshold_up, threshold_down, confidence_gap_up, confidence_gap_down
        2. 后置约束检查：计算指标后检查硬性约束，不满足返回惩罚值
        3. 清晰的优化目标：从config中明确指定一种优化策略
        4. 使用Pruner：支持中间结果剪枝提高效率
        """
        nonlocal best_score_so_far, trials_without_improvement

        # === 🎯 聚焦优化参数：只搜索核心决策参数 ===
        confidence_gap_up = trial.suggest_float('confidence_gap_up', confidence_gap_min, confidence_gap_max, step=0.01)
        confidence_gap_down = trial.suggest_float('confidence_gap_down', confidence_gap_min, confidence_gap_max, step=0.01)
        threshold_up = trial.suggest_float('threshold_up', threshold_min, threshold_max, step=0.02)
        threshold_down = trial.suggest_float('threshold_down', threshold_min, threshold_max, step=0.02)

        # === 计算所有性能指标 ===
        metrics = calculate_simulated_profit_meta(
            y_true_meta_val, y_proba_meta_val,
            threshold_up=threshold_up,
            threshold_down=threshold_down,
            confidence_gap_up=confidence_gap_up,
            confidence_gap_down=confidence_gap_down,
            verbose=False
        )

        # === 🎯 后置约束检查：使用配置文件中的硬性约束 ===
        # 从配置文件获取约束值
        MIN_TRADES_REQUIRED = getattr(config, 'META_MODEL_MIN_TRADES_CONSTRAINT', 20)
        MIN_WIN_RATE_REQUIRED = getattr(config, 'META_MODEL_MIN_WIN_RATE_CONSTRAINT', 0.4)
        MAX_CONSECUTIVE_LOSSES_ALLOWED = getattr(config, 'META_MODEL_MAX_CONSECUTIVE_LOSSES', 10)

        # 硬约束1: 最小交易次数
        if metrics['num_trades'] < MIN_TRADES_REQUIRED:
            return -float('inf') if optuna_direction == 'maximize' else float('inf')

        # 硬约束2: 最小胜率
        if metrics['win_rate'] < MIN_WIN_RATE_REQUIRED:
            return -float('inf') if optuna_direction == 'maximize' else float('inf')

        # 硬约束3: 最大连续亏损限制
        if metrics['max_consecutive_losses'] > MAX_CONSECUTIVE_LOSSES_ALLOWED:
            return -float('inf') if optuna_direction == 'maximize' else float('inf')

        # 硬约束4: 交易平衡性 (避免过度偏向单一方向)
        if metrics['num_trades'] >= 10:
            if metrics['up_trades'] == 0 or metrics['down_trades'] == 0:
                return -float('inf') if optuna_direction == 'maximize' else float('inf')

        # === 🎯 选择清晰的优化目标：从config中明确指定一种优化策略 ===
        optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')

        # 基础指标
        average_profit_per_trade = metrics['average_profit_per_trade']
        num_trades = metrics['num_trades']
        win_rate = metrics['win_rate']
        total_profit = metrics.get('total_profit', average_profit_per_trade * num_trades)
        sharpe_ratio = metrics.get('sharpe_ratio', 0.0)

        # 增强负数惩罚机制：如果平均收益为负，直接返回惩罚值
        if average_profit_per_trade < 0:
            return -float('inf') if optuna_direction == 'maximize' else float('inf')

        # === 🎯 根据配置策略计算对应的优化目标 ===
        if optimization_strategy == 'profit_first':
            # 策略1: 优先最大化总收益
            final_objective = total_profit

        elif optimization_strategy == 'balanced_profit_frequency':
            # 策略2: 平衡收益和频率 (风险调整收益)
            final_objective = average_profit_per_trade * np.sqrt(num_trades)

        elif optimization_strategy == 'frequency_under_winrate_constraint':
            # 策略3: 胜率约束下最大化交易频率
            if win_rate > 0.55:
                final_objective = num_trades + average_profit_per_trade * 10
            else:
                final_objective = average_profit_per_trade

        elif optimization_strategy == 'composite_score':
            # 策略4: 综合评分 (推荐) - 复杂效用函数
            if average_profit_per_trade > 0:
                # 正收益时：收益与交易次数正相关
                final_objective = average_profit_per_trade * (1 + np.log(max(1, num_trades)) * 0.1)
            else:
                # 负收益时：重惩罚，惩罚程度与交易次数成正比
                final_objective = average_profit_per_trade * (1 + num_trades * 0.1)

        elif optimization_strategy == 'risk_adjusted':
            # 策略5: 风险调整收益 (考虑夏普比率)
            final_objective = average_profit_per_trade * (1 + sharpe_ratio * 0.1)

        elif optimization_strategy == 'total_expected_profit':
            # 策略6: 综合指标 (总收益+风险调整+胜率)
            final_objective = (
                total_profit * 0.4 +                       # 40% 总收益权重
                average_profit_per_trade * np.sqrt(num_trades) * 0.4 +  # 40% 风险调整收益
                win_rate * num_trades * 0.2                 # 20% 胜率×频率权重
            )

        else:
            # 默认: 总收益优先
            final_objective = total_profit

        # === 🎯 使用Pruner：支持中间结果剪枝 ===
        # 报告中间结果给Pruner，用于提前终止表现不佳的试验
        trial.report(final_objective, step=0)
        if trial.should_prune():
            raise optuna.TrialPruned()

        # === 计算最终分数 ===
        final_score = final_objective

        # === 早停机制 ===
        if enable_early_stopping:
            if final_score > best_score_so_far:
                best_score_so_far = final_score
                trials_without_improvement = 0
            else:
                trials_without_improvement += 1

            if trials_without_improvement >= early_stop_patience:
                trial.study.stop()

        # === 记录关键指标到trial ===
        trial.set_user_attr('profit_per_trade', average_profit_per_trade)
        trial.set_user_attr('total_trades', num_trades)
        trial.set_user_attr('win_rate', win_rate)
        trial.set_user_attr('total_profit', total_profit)
        trial.set_user_attr('trade_frequency', metrics.get('trade_frequency', 0.0))
        trial.set_user_attr('max_consecutive_losses', metrics['max_consecutive_losses'])
        trial.set_user_attr('sharpe_ratio', sharpe_ratio)
        trial.set_user_attr('min_trades_constraint', MIN_TRADES_REQUIRED)
        trial.set_user_attr('min_win_rate_constraint', MIN_WIN_RATE_REQUIRED)
        trial.set_user_attr('max_consecutive_losses_constraint', MAX_CONSECUTIVE_LOSSES_ALLOWED)
        trial.set_user_attr('optimization_strategy', optimization_strategy)
        trial.set_user_attr('final_score', final_score)

        return final_score

    try:
        # 🎯 创建优化的Optuna study，使用Pruner提高效率
        optuna_direction = getattr(config, 'META_MODEL_OPTUNA_DIRECTION', 'maximize')
        study = optuna.create_study(
            direction=optuna_direction,
            sampler=optuna.samplers.TPESampler(
                seed=42,
                n_startup_trials=min(n_startup_trials, n_trials // 4),  # 前25%试验用于随机探索
                n_ei_candidates=24,  # 减少候选点数量，提高效率
                multivariate=True,   # 启用多变量优化
                warn_independent_sampling=False  # 减少警告信息
            ),
            pruner=optuna.pruners.MedianPruner(
                n_startup_trials=min(10, n_trials // 10),  # 前10%试验不剪枝
                n_warmup_steps=5,    # 减少预热步数
                interval_steps=1
            )
        )

        # 运行优化 (增加超时保护)
        study.optimize(
            objective,
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=verbose
        )

        # 获取最佳参数
        best_params = study.best_params

        # 计算最佳参数的详细结果
        best_result = calculate_simulated_profit_meta(
            y_true_meta_val, y_proba_meta_val,
            threshold_up=best_params['threshold_up'],
            threshold_down=best_params['threshold_down'],
            confidence_gap_up=best_params['confidence_gap_up'],
            confidence_gap_down=best_params['confidence_gap_down'],
            verbose=False
        )

        # 合并参数和结果
        best_result.update(best_params)
        best_result['optimization_method'] = 'optuna'
        best_result['best_value'] = study.best_value
        best_result['n_trials'] = len(study.trials)

        if verbose:
            print(f"   Optuna最佳值: {study.best_value:.4f}")
            print(f"   完成试验: {len(study.trials)}/{n_trials}")

        # 如果提供了model_dir，保存Optuna结果
        if model_dir is not None:
            try:
                from src.core.optuna_integration import save_optuna_study_results
                save_path = save_optuna_study_results(
                    study=study,
                    model_dir=model_dir,
                    model_name=model_name,
                    optimization_type='meta_thresholds'
                )
                if verbose and save_path:
                    print(f"   Optuna优化结果已保存: {save_path}")
                best_result['optuna_results_path'] = save_path
            except Exception as e_save:
                if verbose:
                    print(f"   ⚠️ 保存Optuna结果失败: {e_save}")

        return best_result

    except Exception as e:
        if verbose:
            print(f"   ⚠️ Optuna优化失败: {e}")

        # 返回默认值
        return {
            'threshold_up': 0.4, 'threshold_down': 0.4,
            'confidence_gap_up': 0.1, 'confidence_gap_down': 0.1,
            'expected_profit_per_trade': 0.0, 'total_trades': 0, 'win_rate': 0.0,
            'optimization_method': 'optuna_failed'
        }


def load_optimal_meta_thresholds():
    """
    加载优化后的元模型决策阈值

    返回:
    - dict: 包含最优阈值的字典，如果加载失败则返回默认值
    """
    default_thresholds = {
        'threshold_up': getattr(config, 'META_SIGNAL_UP_THRESHOLD', 0.4),
        'threshold_down': getattr(config, 'META_SIGNAL_DOWN_THRESHOLD', 0.4),
        'confidence_gap_up': getattr(config, 'META_SIGNAL_CONFIDENCE_GAP_UP', 0.1),
        'confidence_gap_down': getattr(config, 'META_SIGNAL_CONFIDENCE_GAP_DOWN', 0.1)
    }

    # 尝试从保存的文件加载
    meta_model_dir = getattr(config, 'META_MODEL_SAVE_DIR', "meta_model_data")
    threshold_file_path = os.path.join(meta_model_dir, "optimal_thresholds.json")

    if os.path.exists(threshold_file_path):
        try:
            with open(threshold_file_path, 'r', encoding='utf-8') as f:
                optimal_thresholds = json.load(f)

            # 验证加载的阈值是否有效
            required_keys = ['threshold_up', 'threshold_down', 'confidence_gap_up', 'confidence_gap_down']
            if all(key in optimal_thresholds for key in required_keys):
                print(f"  [MetaThresholds] 成功加载优化阈值: {threshold_file_path}")
                print(f"    UP阈值: {optimal_thresholds['threshold_up']:.3f}, DOWN阈值: {optimal_thresholds['threshold_down']:.3f}")
                print(f"    UP置信度差值: {optimal_thresholds['confidence_gap_up']:.3f}, DOWN置信度差值: {optimal_thresholds['confidence_gap_down']:.3f}")
                return optimal_thresholds
            else:
                print(f"  ⚠️ [MetaThresholds] 优化阈值文件格式不完整，使用默认值")

        except Exception as e:
            print(f"  ⚠️ [MetaThresholds] 加载优化阈值失败: {e}，使用默认值")
    else:
        print(f"  [MetaThresholds] 优化阈值文件不存在，使用默认值: {threshold_file_path}")

    return default_thresholds


def calculate_optuna_meta_metric(metric_name, y_true, y_pred_proba, num_classes, verbose_metric_calc=False):
    """
    为元模型的Optuna计算指定的评估指标。
    🎯 新增：支持基于模拟交易盈利能力的优化目标

    参数:
    - metric_name: 指标名称
    - y_true: 真实标签
    - y_pred_proba: 预测概率 (predict_proba的输出)
    - num_classes: 类别数量
    - verbose_metric_calc: 是否输出详细计算信息
    """
    # 确保导入必要的函数
    from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score, log_loss

    y_pred_labels = np.argmax(y_pred_proba, axis=1) # 从概率中获取预测类别

    # 🎯 新增：基于盈利能力的优化指标
    if metric_name == 'simulated_profit_expected':
        """期望收益优化 - 直接优化模拟交易的期望收益"""
        try:
            # 使用默认的决策阈值进行模拟交易
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )
            return profit_result['expected_profit_per_trade']
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算模拟交易期望收益时出错: {e}")
            return -1.0  # 返回负值表示失败

    elif metric_name == 'simulated_profit_risk_adjusted':
        """风险调整收益优化 - 考虑交易频率的风险调整收益"""
        try:
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )
            return profit_result['risk_adjusted_return']
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算风险调整收益时出错: {e}")
            return -1.0

    elif metric_name == 'simulated_profit_composite':
        """复合盈利指标 - 综合考虑收益、胜率和交易频率"""
        try:
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )

            # 复合评分：期望收益 × 胜率 × sqrt(交易频率)
            expected_profit = profit_result['expected_profit_per_trade']
            win_rate = profit_result['win_rate']
            trade_frequency = profit_result['trade_frequency']

            # 添加约束：最小交易次数和胜率
            if profit_result['num_trades'] < 10 or win_rate < 0.3:
                return -1.0  # 不满足基本约束

            composite_score = expected_profit * win_rate * np.sqrt(trade_frequency)
            return composite_score
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算复合盈利指标时出错: {e}")
            return -1.0

    elif metric_name == 'simulated_profit_sharpe':
        """夏普比率优化 - 基于模拟交易的夏普比率"""
        try:
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )

            # 使用夏普比率，但添加最小交易次数约束
            if profit_result['num_trades'] < 10:
                return -1.0

            return profit_result.get('sharpe_ratio', -1.0)
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算夏普比率时出错: {e}")
            return -1.0

    # 🎯 保留原有的技术指标（向后兼容）
    elif metric_name == 'macro_f1_score':
        return f1_score(y_true, y_pred_labels, average='macro', zero_division=0)
    elif metric_name == 'weighted_f1_score':
        return f1_score(y_true, y_pred_labels, average='weighted', zero_division=0)
    elif metric_name == 'val_accuracy': # 之前元模型用的
        return accuracy_score(y_true, y_pred_labels)
    elif metric_name == 'multi_logloss':
        try:
            return -log_loss(y_true, y_pred_proba)  # 负号使其变为最大化目标
        except Exception:
            return -10.0  # 返回较大的负值表示失败

    elif metric_name == 'custom_f1_class01_avg':
        """自定义F1指标：Class 0和1的F1分数平均值"""
        try:
            # 计算Class 0和1的F1分数
            f1_scores = f1_score(y_true, y_pred_labels, average=None, zero_division=0)
            if len(f1_scores) >= 2:
                # 只取Class 0和1的F1分数
                f1_class_0 = f1_scores[0]
                f1_class_1 = f1_scores[1]
                avg_f1_class01 = (f1_class_0 + f1_class_1) / 2.0
                return avg_f1_class01
            else:
                return 0.0
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算custom_f1_class01_avg时出错: {e}")
            return 0.0

    elif metric_name == 'custom_precision_class01_avg':
        """自定义精确率指标：Class 0和1的精确率平均值"""
        try:
            precision_scores = precision_score(y_true, y_pred_labels, average=None, zero_division=0)
            if len(precision_scores) >= 2:
                precision_class_0 = precision_scores[0]
                precision_class_1 = precision_scores[1]
                avg_precision_class01 = (precision_class_0 + precision_class_1) / 2.0
                return avg_precision_class01
            else:
                return 0.0
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算custom_precision_class01_avg时出错: {e}")
            return 0.0

    elif metric_name == 'custom_recall_class01_avg':
        """自定义召回率指标：Class 0和1的召回率平均值"""
        try:
            recall_scores = recall_score(y_true, y_pred_labels, average=None, zero_division=0)
            if len(recall_scores) >= 2:
                recall_class_0 = recall_scores[0]
                recall_class_1 = recall_scores[1]
                avg_recall_class01 = (recall_class_0 + recall_class_1) / 2.0
                return avg_recall_class01
            else:
                return 0.0
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算custom_recall_class01_avg时出错: {e}")
            return 0.0

    else:
        # 未知指标，返回默认值
        if verbose_metric_calc:
            print(f"未知的优化指标: {metric_name}")
        return 0.0




def train_meta_model(X_meta_df_input, y_meta_series_input, force_include_features=None):
    """
    训练元模型的主函数，使用改进的错误处理

    Args:
        X_meta_df_input: 输入特征DataFrame
        y_meta_series_input: 输入标签Series

    Returns:
        tuple: (success, model, X_val, y_val, evaluation_results)
    """
    logger = logging.getLogger(__name__)
    logger.info(">>> train_meta_model 函数开始执行...")

    try:
        # 获取保存目录配置并创建目录
        meta_model_save_dir_final = getattr(config, 'META_MODEL_SAVE_DIR', "meta_model_data")
        logger.info(f"配置的保存目录: {meta_model_save_dir_final}")

        # 确保使用绝对路径
        if not os.path.isabs(meta_model_save_dir_final):
            meta_model_save_dir_final = os.path.abspath(meta_model_save_dir_final)
        logger.info(f"绝对保存路径: {meta_model_save_dir_final}")

        # 创建保存目录
        try:
            os.makedirs(meta_model_save_dir_final, exist_ok=True)
            logger.info(f"保存目录创建成功: {meta_model_save_dir_final}")
        except OSError as e:
            error_context = {
                'save_dir': meta_model_save_dir_final,
                'operation': 'create_directory'
            }
            raise ConfigurationError(
                f"创建保存目录失败: {meta_model_save_dir_final}",
                context=error_context,
                original_exception=e
            )

        # 输入验证
        if not isinstance(X_meta_df_input, pd.DataFrame):
            raise DataValidationError(
                "X_meta_df_input 必须是 pandas DataFrame",
                context={'input_type': type(X_meta_df_input).__name__}
            )

        if not isinstance(y_meta_series_input, pd.Series):
            raise DataValidationError(
                "y_meta_series_input 必须是 pandas Series",
                context={'input_type': type(y_meta_series_input).__name__}
            )

        if X_meta_df_input.empty:
            raise DataValidationError(
                "X_meta_df_input 不能为空",
                context={'shape': X_meta_df_input.shape}
            )

        if y_meta_series_input.empty:
            raise DataValidationError(
                "y_meta_series_input 不能为空",
                context={'shape': y_meta_series_input.shape}
            )

        # 索引对齐检查
        common_idx_train_meta_internal = X_meta_df_input.index.intersection(y_meta_series_input.index)
        if len(common_idx_train_meta_internal) < len(X_meta_df_input) * 0.95:
            error_context = {
                'X_length': len(X_meta_df_input),
                'y_length': len(y_meta_series_input),
                'common_length': len(common_idx_train_meta_internal),
                'alignment_ratio': len(common_idx_train_meta_internal) / len(X_meta_df_input)
            }
            raise DataValidationError(
                "X_meta和y_meta索引差异过大或不匹配",
                context=error_context
            )

    except (ConfigurationError, DataValidationError) as e:
        log_prediction_error(logger, e, 'train_meta_model', additional_context={'step': 'input_validation'})
        error_result = create_error_result(type(e), str(e), e.context, e.original_exception)
        return False, None, None, None, error_result.to_dict()

    except Exception as e:
        error_context = {'step': 'initialization', 'unexpected_error': True}
        error = MetaModelError("元模型训练初始化时发生未预期错误", context=error_context, original_exception=e)
        log_prediction_error(logger, error, 'train_meta_model', additional_context=error_context)
        error_result = create_error_result(MetaModelError, str(error), error.context, e)
        return False, None, None, None, error_result.to_dict()

    try:
        # 数据对齐处理
        logger.info("开始数据对齐处理...")
        X_meta_df = X_meta_df_input.loc[common_idx_train_meta_internal]
        y_meta_series = y_meta_series_input.loc[common_idx_train_meta_internal]

        # 对齐后数据验证
        if X_meta_df.empty or y_meta_series.empty:
            error_context = {
                'X_shape_after_align': X_meta_df.shape,
                'y_shape_after_align': y_meta_series.shape,
                'common_indices_count': len(common_idx_train_meta_internal)
            }
            raise DataValidationError(
                "对齐后X_meta或y_meta为空",
                context=error_context
            )

        logger.info(f"元模型训练数据形状 (对齐后): X_meta={X_meta_df.shape}, y_meta={y_meta_series.shape}")

        # 目标变量分布检查
        y_value_counts = y_meta_series.value_counts(normalize=True).sort_index()
        logger.info(f"元模型目标变量分布 (y_meta):\n{y_value_counts}")

        # 检查类别数量是否足够
        unique_classes = y_meta_series.nunique()
        if unique_classes < 2:
            error_context = {
                'unique_classes': unique_classes,
                'class_distribution': y_value_counts.to_dict()
            }
            raise DataValidationError(
                f"目标变量类别数量不足，需要至少2个类别，当前只有{unique_classes}个",
                context=error_context
            )

        # --- Optuna 相关配置 ---
        optuna_enabled_meta = getattr(config, 'META_MODEL_OPTUNA_ENABLE', False)
        best_params_from_optuna = {}
        logger.info(f"Optuna超参数优化状态: {'启用' if optuna_enabled_meta else '禁用'}")

    except DataValidationError as e:
        log_prediction_error(logger, e, 'train_meta_model', additional_context={'step': 'data_alignment'})
        error_result = create_error_result(DataValidationError, str(e), e.context, e.original_exception)
        return False, None, None, None, error_result.to_dict()

    except Exception as e:
        error_context = {'step': 'data_alignment', 'unexpected_error': True}
        error = MetaModelError("数据对齐过程中发生未预期错误", context=error_context, original_exception=e)
        log_prediction_error(logger, error, 'train_meta_model', additional_context=error_context)
        error_result = create_error_result(MetaModelError, str(error), error.context, e)
        return False, None, None, None, error_result.to_dict()

    if optuna_enabled_meta:
        print("  prediction.train_meta_model: Optuna超参数优化已为元模型启用。")
        
        def objective_meta(trial):
            # 定义参数搜索空间
            param_grid_meta = getattr(config, 'META_MODEL_OPTUNA_PARAM_GRID', {})
            params = {}
            for name, (param_type, low, high, *args) in param_grid_meta.items():
                if param_type == 'int':
                    params[name] = trial.suggest_int(name, low, high)
                elif param_type == 'float':
                    log_scale = args[0] if args else False
                    params[name] = trial.suggest_float(name, low, high, log=log_scale)
                elif param_type == 'categorical':
                    params[name] = trial.suggest_categorical(name, high) # 'high' here is the list of categories

            # 固定参数
            params['objective'] = getattr(config, 'META_MODEL_LGBM_OBJECTIVE', 'multiclass')
            params['num_class'] = getattr(config, 'META_MODEL_LGBM_NUM_CLASS', 3)
            params['metric'] = getattr(config, 'META_MODEL_OPTUNA_LGBM_EVAL_METRIC', 'multi_logloss') # 早停用此指标
            params['n_estimators'] = getattr(config, 'META_MODEL_OPTUNA_LGBM_N_ESTIMATORS_MAX', 300)
            params['random_state'] = getattr(config, 'META_MODEL_LGBM_RANDOM_STATE', 2024) + trial.number # 确保每次试验种子不同
            params['n_jobs'] = -1
            params['device_type'] = getattr(config, 'META_MODEL_LGBM_DEVICE', 'cpu').lower()
            params['class_weight'] = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', 'balanced') # 通常固定
            params['verbosity'] = -1 # Optuna试验时保持静默

            # 清理参数，只保留LGBM认识的
            lgbm_trial_params_clean = {k: v for k, v in params.items() if k in VALID_LGBM_PARAM_KEYS_PRED_PY}

            cv_scores = []
            # 使用 TimeSeriesSplit 进行交叉验证
            n_splits_optuna_cv = getattr(config, 'META_MODEL_OPTUNA_CV_FOLDS', 3)
            if n_splits_optuna_cv <= 1: # 如果CV折数不合理，则不进行CV，直接在全数据上评估（或划分一次验证集）
                print(f"  Optuna Trial {trial.number}: CV folds ({n_splits_optuna_cv}) <= 1, will use single validation split for this trial.")
                 # 可以选择划分一个固定的验证集来评估，或者如果数据量小就直接在训练集上评估（不推荐）
                # 这里我们简化，如果CV折数不合理，就返回一个很差的值，促使配置被修正
                return -float('inf') if getattr(config, 'META_MODEL_OPTUNA_DIRECTION', "maximize") == "maximize" else float('inf')


            tscv_optuna = TimeSeriesSplit(n_splits=n_splits_optuna_cv)
            
            # 获取早停回调
            early_stopping_rounds_optuna = getattr(config, 'META_MODEL_OPTUNA_LGBM_EARLY_STOPPING_ROUNDS', 30)
            callbacks_optuna_trial = [early_stopping(early_stopping_rounds_optuna, verbose=False, first_metric_only=False)] if early_stopping_rounds_optuna > 0 else None

            for fold_idx, (train_indices, val_indices) in enumerate(tscv_optuna.split(X_meta_df.values, y_meta_series.values)):
                X_train_fold_np, X_val_fold_np = X_meta_df.values[train_indices], X_meta_df.values[val_indices]
                y_train_fold_np, y_val_fold_np = y_meta_series.values[train_indices], y_meta_series.values[val_indices]

                if len(np.unique(y_val_fold_np)) < params['num_class']: # 验证集类别不足
                    print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: Validation set has only {len(np.unique(y_val_fold_np))} classes. Skipping fold.")
                    cv_scores.append(-1.0) # 或者一个非常差的值
                    continue

                # 应用SMOTE过采样 (仅在训练数据上)
                smote_enabled_optuna = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and getattr(config, 'META_MODEL_SMOTE_ENABLE', True)
                if smote_enabled_optuna:
                    unique_labels_optuna, counts_optuna = np.unique(y_train_fold_np, return_counts=True)
                    minority_class_count_optuna = counts_optuna.min()
                    smote_min_threshold_optuna = getattr(config, 'META_MODEL_SMOTE_MIN_SAMPLES_THRESHOLD', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

                    if minority_class_count_optuna < smote_min_threshold_optuna:
                        print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: 少数类样本 ({minority_class_count_optuna}) 过少，跳过SMOTE。")
                        X_train_fold_resampled_np = X_train_fold_np
                        y_train_fold_resampled_np = y_train_fold_np
                    else:
                        try:
                            print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: 应用SMOTE前类别分布: {dict(zip(unique_labels_optuna, counts_optuna))}")
                            # k_neighbors 的值不能超过少数类样本数 - 1
                            smote_k_neighbors_config_optuna = getattr(config, 'META_MODEL_SMOTE_K_NEIGHBORS', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                            smote_k_neighbors_optuna = min(smote_k_neighbors_config_optuna, minority_class_count_optuna - 1) if minority_class_count_optuna > 1 else 1
                            smote_random_state_optuna = getattr(config, 'META_MODEL_SMOTE_RANDOM_STATE', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                            from imblearn.over_sampling import SMOTE  # 确保导入SMOTE
                            sm_optuna = SMOTE(random_state=smote_random_state_optuna, k_neighbors=smote_k_neighbors_optuna)
                            X_train_fold_resampled_np, y_train_fold_resampled_np = sm_optuna.fit_resample(X_train_fold_np, y_train_fold_np)
                            unique_labels_after_optuna, counts_after_optuna = np.unique(y_train_fold_resampled_np, return_counts=True)
                            print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: 应用SMOTE后类别分布: {dict(zip(unique_labels_after_optuna, counts_after_optuna))}")
                        except ValueError as e_smote_optuna:
                            print(f"    !!! Optuna Trial {trial.number}, Fold {fold_idx}: SMOTE执行失败: {e_smote_optuna}。将使用原始训练数据。")
                            X_train_fold_resampled_np = X_train_fold_np
                            y_train_fold_resampled_np = y_train_fold_np
                else:
                    print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: SMOTE已禁用，使用原始训练数据。")
                    X_train_fold_resampled_np = X_train_fold_np
                    y_train_fold_resampled_np = y_train_fold_np

                model_trial = LGBMClassifier(**lgbm_trial_params_clean)
                try:
                    model_trial.fit(X_train_fold_resampled_np, y_train_fold_resampled_np,
                                    eval_set=[(X_val_fold_np, y_val_fold_np)],
                                    eval_metric=params['metric'], # 使用LGBM早停的指标
                                    callbacks=callbacks_optuna_trial,
                                    feature_name=list(X_meta_df.columns))
                except Exception as e_fit_trial:
                    print(f"    !!! Optuna Trial {trial.number}, Fold {fold_idx} fit error: {e_fit_trial}")
                    cv_scores.append(-1.0) # 或者一个非常差的值
                    continue

                y_pred_proba_val_fold = model_trial.predict_proba(X_val_fold_np)
                
                # 使用我们定义的 calculate_optuna_meta_metric 和 config 中配置的优化目标指标
                optuna_target_metric_name = getattr(config, 'META_MODEL_OPTUNA_METRIC', 'macro_f1_score')
                score_fold = calculate_optuna_meta_metric(optuna_target_metric_name, y_val_fold_np, y_pred_proba_val_fold, params['num_class'])
                cv_scores.append(score_fold)
                
                # Optuna剪枝（可选）
                trial.report(np.mean(cv_scores) if cv_scores else 0.0, fold_idx)
                if trial.should_prune():
                    raise optuna.exceptions.TrialPruned()
            
            return np.mean(cv_scores) if cv_scores else 0.0 # 返回平均CV分数

        # --- 运行 Optuna study ---
        optuna_direction = getattr(config, 'META_MODEL_OPTUNA_DIRECTION', "maximize")
        study = optuna.create_study(direction=optuna_direction)
        try:
            study.optimize(objective_meta,
                           n_trials=getattr(config, 'META_MODEL_OPTUNA_N_TRIALS', 50),
                           timeout=getattr(config, 'META_MODEL_OPTUNA_TIMEOUT', None),
                           n_jobs=1, # LightGBM自身可以多线程，Optuna的n_jobs通常设为1
                           show_progress_bar=True) # 显示进度条
            best_params_from_optuna = study.best_params
            # 🚨 修复：暂存Optuna结果，稍后输出
            optuna_best_value = study.best_value
            optuna_best_params = best_params_from_optuna
        except optuna.exceptions.TrialPruned as e_pruned:
            print(f"  prediction.train_meta_model: Optuna试验被剪枝: {e_pruned}")
        except Exception as e_optuna_run:
            print(f"  !!! prediction.train_meta_model: Optuna优化过程中发生错误: {e_optuna_run}")
            traceback.print_exc(limit=1)
            best_params_from_optuna = {} # 出错则不使用优化参数
    
    # --- 准备最终的LGBM参数 ---
    if optuna_enabled_meta and best_params_from_optuna:
        # 🚨 修复：暂存消息，稍后输出
        optuna_success_message = "将使用Optuna找到的最佳参数进行元模型最终训练。"
        # 从Optuna的最佳参数开始，并补充固定的和必要的参数
        final_lgbm_params = best_params_from_optuna.copy()
        final_lgbm_params['objective'] = getattr(config, 'META_MODEL_LGBM_OBJECTIVE', 'multiclass')
        final_lgbm_params['num_class'] = getattr(config, 'META_MODEL_LGBM_NUM_CLASS', 3)
        final_lgbm_params['metric'] = getattr(config, 'META_MODEL_LGBM_METRIC', 'multi_logloss') # 最终训练和评估用此指标
        final_lgbm_params['n_estimators'] = getattr(config, 'META_MODEL_LGBM_N_ESTIMATORS', 100) # 可以用config中的值或Optuna也优化它
        final_lgbm_params['random_state'] = getattr(config, 'META_MODEL_LGBM_RANDOM_STATE', 2024)
        final_lgbm_params['n_jobs'] = -1
        final_lgbm_params['device_type'] = getattr(config, 'META_MODEL_LGBM_DEVICE', 'cpu').lower()
        final_lgbm_params['class_weight'] = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', 'balanced')
        final_lgbm_params['verbosity'] = getattr(config, 'META_MODEL_LGBM_VERBOSE', -1) # 最终训练时的verbose
    else:
        if optuna_enabled_meta: # Optuna启用了但没有找到最佳参数（例如出错或所有试验都剪枝了）
            print("  prediction.train_meta_model: Optuna未找到有效参数，将使用config.py中的固定参数进行元模型训练。")
        else:
            print("  prediction.train_meta_model: Optuna未启用，将使用config.py中的固定参数进行元模型训练。")
        # 使用 config.py 中的固定参数
        fixed_params = {
            'objective': getattr(config, 'META_MODEL_LGBM_OBJECTIVE', 'multiclass'),
            'num_class': getattr(config, 'META_MODEL_LGBM_NUM_CLASS', 3),
            'metric': getattr(config, 'META_MODEL_LGBM_METRIC', 'multi_logloss'),
            'n_estimators': getattr(config, 'META_MODEL_LGBM_N_ESTIMATORS', 100),
            'learning_rate': getattr(config, 'META_MODEL_LGBM_LEARNING_RATE', 0.02),
            'num_leaves': getattr(config, 'META_MODEL_LGBM_NUM_LEAVES', 10),
            'max_depth': getattr(config, 'META_MODEL_LGBM_MAX_DEPTH', 4),
            'reg_alpha': getattr(config, 'META_MODEL_LGBM_REG_ALPHA', 2.0),
            'reg_lambda': getattr(config, 'META_MODEL_LGBM_REG_LAMBDA', 2.0),
            'colsample_bytree': getattr(config, 'META_MODEL_LGBM_COLSAMPLE_BYTREE', 0.7),
            'subsample': getattr(config, 'META_MODEL_LGBM_SUBSAMPLE', 0.7),
            'min_child_samples': getattr(config, 'META_MODEL_LGBM_MIN_CHILD_SAMPLES', 30),
            'random_state': getattr(config, 'META_MODEL_LGBM_RANDOM_STATE', 2024),
            'verbose': getattr(config, 'META_MODEL_LGBM_VERBOSE', -1),
            'n_jobs': -1,
            'device_type': getattr(config, 'META_MODEL_LGBM_DEVICE', 'cpu').lower(),
            'class_weight': getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', 'balanced')
        }
        final_lgbm_params = fixed_params

    # 清理最终参数并设置verbosity
    meta_lgbm_final_params_to_use = {k: v for k, v in final_lgbm_params.items() if k in VALID_LGBM_PARAM_KEYS_PRED_PY}
    if 'verbose' in meta_lgbm_final_params_to_use:
        lgbm_verbosity_level = meta_lgbm_final_params_to_use.pop('verbose')
        if lgbm_verbosity_level == 0: meta_lgbm_final_params_to_use['verbosity'] = -1
        elif lgbm_verbosity_level == 1: meta_lgbm_final_params_to_use['verbosity'] = 0
        elif lgbm_verbosity_level > 1: meta_lgbm_final_params_to_use['verbosity'] = 1
        else: meta_lgbm_final_params_to_use['verbosity'] = lgbm_verbosity_level

    # 🚨 修复：暂存参数信息，稍后输出
    final_params_info = json.dumps(meta_lgbm_final_params_to_use, indent=2)

    # --- 后续的模型训练、评估、保存逻辑与之前类似，使用 meta_lgbm_final_params_to_use ---
    trained_model_object_to_return = None
    X_val_data_to_return_np = None 
    y_val_data_to_return_np = None 
    evaluation_results_to_return_dict = {"status": "评估未开始或无验证数据"}

    X_meta_np_for_split = X_meta_df.values
    y_meta_np_for_split = y_meta_series.values
    
    X_train_meta_for_fit, y_train_meta_for_fit = X_meta_np_for_split, y_meta_np_for_split
    eval_set_for_meta_fit = None
    callbacks_for_meta_fit = None
    
    eval_ratio_meta_config = getattr(config, 'META_MODEL_TRAIN_TEST_SPLIT_FOR_EVAL_RATIO', 0.2)
    if 0 < eval_ratio_meta_config < 1:
        # ... (之前的数据划分逻辑，用于最终训练和评估) ...
        num_samples_meta_total = len(X_meta_np_for_split)
        num_val_samples_meta_calc = int(num_samples_meta_total * eval_ratio_meta_config)
        min_val_samples_meta_config = getattr(config, 'META_MODEL_OOF_MIN_SAMPLES_FOR_EARLY_STOP_VALID', 30) # 复用这个配置
        if num_val_samples_meta_calc >= min_val_samples_meta_config and (num_samples_meta_total - num_val_samples_meta_calc) >= min_val_samples_meta_config:
            train_size_meta_split = num_samples_meta_total - num_val_samples_meta_calc
            X_train_meta_for_fit, y_train_meta_for_fit = X_meta_np_for_split[:train_size_meta_split], y_meta_np_for_split[:train_size_meta_split]
            X_val_data_to_return_np, y_val_data_to_return_np = X_meta_np_for_split[train_size_meta_split:], y_meta_np_for_split[train_size_meta_split:]
            if len(np.unique(y_val_data_to_return_np)) < meta_lgbm_final_params_to_use.get('num_class', 3):
                 print(f"  prediction.train_meta_model: 警告 - 元模型验证集类别数不足。不使用早停，使用全部数据训练。")
                 X_train_meta_for_fit, y_train_meta_for_fit = X_meta_np_for_split, y_meta_np_for_split
                 X_val_data_to_return_np, y_val_data_to_return_np = None, None
            else:
                eval_set_for_meta_fit = [(X_val_data_to_return_np, y_val_data_to_return_np)]
                final_esr_meta = getattr(config, 'META_MODEL_LGBM_EARLY_STOPPING_ROUNDS_FINAL', 20)
                if final_esr_meta > 0 :
                    callbacks_for_meta_fit = [
                        early_stopping(
                            stopping_rounds=final_esr_meta,
                            verbose=meta_lgbm_final_params_to_use.get('verbosity',0) > 0,
                            first_metric_only=False
                        )
                    ]
            print(f"  prediction.train_meta_model: 元模型数据划分: 训练集 {len(X_train_meta_for_fit)} 条, 验证集 {len(X_val_data_to_return_np) if X_val_data_to_return_np is not None else 0} 条。")
        else:
            print(f"  prediction.train_meta_model: 警告 - 验证/训练集样本不足，不划分，使用全部数据训练。")
            X_val_data_to_return_np, y_val_data_to_return_np = None, None
    else:
        print(f"  prediction.train_meta_model: 验证集划分比例无效，使用全部数据训练。")
        X_val_data_to_return_np, y_val_data_to_return_np = None, None

    # 应用SMOTE过采样 (仅在训练数据上)
    smote_enabled_meta = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and getattr(config, 'META_MODEL_SMOTE_ENABLE', True)
    if smote_enabled_meta:
        unique_labels_meta, counts_meta = np.unique(y_train_meta_for_fit, return_counts=True)
        minority_class_count_meta = counts_meta.min()
        smote_min_threshold_meta = getattr(config, 'META_MODEL_SMOTE_MIN_SAMPLES_THRESHOLD', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

        if minority_class_count_meta < smote_min_threshold_meta:
            print(f"  prediction.train_meta_model: 少数类样本 ({minority_class_count_meta}) 过少，跳过SMOTE。")
            X_train_meta_resampled = X_train_meta_for_fit
            y_train_meta_resampled = y_train_meta_for_fit
        else:
            try:
                print(f"  prediction.train_meta_model: 应用SMOTE前类别分布: {dict(zip(unique_labels_meta, counts_meta))}")
                # k_neighbors 的值不能超过少数类样本数 - 1
                smote_k_neighbors_config_meta = getattr(config, 'META_MODEL_SMOTE_K_NEIGHBORS', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                smote_k_neighbors_meta = min(smote_k_neighbors_config_meta, minority_class_count_meta - 1) if minority_class_count_meta > 1 else 1
                smote_random_state_meta = getattr(config, 'META_MODEL_SMOTE_RANDOM_STATE', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                from imblearn.over_sampling import SMOTE  # 确保导入SMOTE
                sm_meta = SMOTE(random_state=smote_random_state_meta, k_neighbors=smote_k_neighbors_meta)
                X_train_meta_resampled, y_train_meta_resampled = sm_meta.fit_resample(X_train_meta_for_fit, y_train_meta_for_fit)
                unique_labels_after_meta, counts_after_meta = np.unique(y_train_meta_resampled, return_counts=True)
                print(f"  prediction.train_meta_model: 应用SMOTE后类别分布: {dict(zip(unique_labels_after_meta, counts_after_meta))}")
            except ValueError as e_smote_meta:
                print(f"  !!! prediction.train_meta_model: SMOTE执行失败: {e_smote_meta}。将使用原始训练数据。")
                X_train_meta_resampled = X_train_meta_for_fit
                y_train_meta_resampled = y_train_meta_for_fit
    else:
        print(f"  prediction.train_meta_model: SMOTE已禁用，使用原始训练数据。")
        X_train_meta_resampled = X_train_meta_for_fit
        y_train_meta_resampled = y_train_meta_for_fit

    trained_model_object_to_return = LGBMClassifier(**meta_lgbm_final_params_to_use)
    try:
        print("  prediction.train_meta_model: 开始拟合元模型...")
        trained_model_object_to_return.fit(
            X_train_meta_resampled, y_train_meta_resampled,
            eval_set=eval_set_for_meta_fit,
            eval_metric=meta_lgbm_final_params_to_use.get('metric'), # 使用最终训练的评估指标
            callbacks=callbacks_for_meta_fit,
            feature_name=list(X_meta_df.columns)
        )
        print("  prediction.train_meta_model: 元模型拟合完成。")
        if eval_set_for_meta_fit and hasattr(trained_model_object_to_return, 'best_iteration_') and trained_model_object_to_return.best_iteration_ is not None:
            print(f"  prediction.train_meta_model: 元模型早停于第 {trained_model_object_to_return.best_iteration_} 轮。")
            evaluation_results_to_return_dict['best_iteration'] = trained_model_object_to_return.best_iteration_
            # ... (获取 best_score_ 的逻辑)
    except Exception as e_fit_meta_in_func_final:
        print(f"!!! prediction.train_meta_model: 元模型训练失败: {e_fit_meta_in_func_final}")
        traceback.print_exc(limit=2)
        evaluation_results_to_return_dict['status'] = f'Error_Fit: {str(e_fit_meta_in_func_final)}'
        return False, None, X_val_data_to_return_np, y_val_data_to_return_np, evaluation_results_to_return_dict

    # --- 评估部分 ---
    if X_val_data_to_return_np is not None and y_val_data_to_return_np is not None and len(y_val_data_to_return_np) > 0:
        print("\n  prediction.train_meta_model: --- 元模型在内部验证集上的评估 ---")
        try:
            # 直接使用NumPy数组进行预测，避免创建不必要的DataFrame
            # 这样可以减少内存消耗，因为LightGBM可以直接处理NumPy数组
            y_pred_meta_val_final_eval = trained_model_object_to_return.predict(X_val_data_to_return_np)
            
            # 如果后续代码需要使用DataFrame，再创建一次
            X_meta_val_df_for_pred_final_eval = pd.DataFrame(X_val_data_to_return_np, columns=list(X_meta_df.columns))
            # 直接使用NumPy数组进行概率预测，避免使用DataFrame
            y_proba_meta_val_final_eval = trained_model_object_to_return.predict_proba(X_val_data_to_return_np)

            evaluation_results_to_return_dict['val_accuracy'] = accuracy_score(y_val_data_to_return_np, y_pred_meta_val_final_eval)
            evaluation_results_to_return_dict['val_logloss'] = log_loss(y_val_data_to_return_np, y_proba_meta_val_final_eval, labels=trained_model_object_to_return.classes_)
            
            target_names_for_report_final = [f"Class_{c}" for c in trained_model_object_to_return.classes_] if hasattr(trained_model_object_to_return, 'classes_') else ['Down_Meta(0)', 'Up_Meta(1)', 'Neutral_Meta(2)']
            labels_for_report_final = trained_model_object_to_return.classes_ if hasattr(trained_model_object_to_return, 'classes_') else [0,1,2]

            evaluation_results_to_return_dict['val_classification_report_dict'] = classification_report(
                y_val_data_to_return_np, y_pred_meta_val_final_eval, output_dict=True, zero_division=0,
                labels=labels_for_report_final, target_names=target_names_for_report_final
            )
            evaluation_results_to_return_dict['status'] = "评估完成"
            # 🚨 修复：暂存评估结果，稍后输出
            evaluation_accuracy = evaluation_results_to_return_dict['val_accuracy']
            evaluation_logloss = evaluation_results_to_return_dict['val_logloss']
            evaluation_report = classification_report(y_val_data_to_return_np, y_pred_meta_val_final_eval, digits=3, zero_division=0,
                                        labels=labels_for_report_final, target_names=target_names_for_report_final)

            # --- 元模型决策阈值优化 (重新启用) ---
            # 🎯 重新启用阈值优化，在已优化的概率质量基础上进一步提升交易性能
            print("  📋 元模型阈值优化已重新启用")
            print("  🎯 目标：在优化的概率质量基础上找到更好的决策边界")

            # 计算并显示详细的三分类性能指标
            try:
                from sklearn.metrics import precision_recall_fscore_support, confusion_matrix

                # 计算每个类别的精确率、召回率、F1分数
                precision, recall, f1, support = precision_recall_fscore_support(
                    y_val_data_to_return_np, y_pred_meta_val_final_eval,
                    labels=[0, 1, 2], zero_division=0
                )

                # 计算混淆矩阵
                cm = confusion_matrix(y_val_data_to_return_np, y_pred_meta_val_final_eval, labels=[0, 1, 2])

                print("\n  📊 详细三分类性能分析 (使用argmax决策):")
                print("  " + "="*50)
                for i, class_name in enumerate(['Class 0 (下跌)', 'Class 1 (上涨)', 'Class 2 (中性)']):
                    print(f"  {class_name}:")
                    print(f"    精确率 (Precision): {precision[i]:.4f}")
                    print(f"    召回率 (Recall):    {recall[i]:.4f}")
                    print(f"    F1分数:            {f1[i]:.4f}")
                    print(f"    支持样本数:        {support[i]}")
                    print()

                # 重点关注Class 0和1的性能
                class01_precision_avg = (precision[0] + precision[1]) / 2
                class01_recall_avg = (recall[0] + recall[1]) / 2
                class01_f1_avg = (f1[0] + f1[1]) / 2

                print(f"  🎯 Class 0&1 平均性能 (交易相关类别):")
                print(f"    平均精确率: {class01_precision_avg:.4f}")
                print(f"    平均召回率: {class01_recall_avg:.4f}")
                print(f"    平均F1分数: {class01_f1_avg:.4f}")
                print()

                print(f"  📈 混淆矩阵:")
                print(f"    实际\\预测  Class0  Class1  Class2")
                for i, row in enumerate(cm):
                    print(f"    Class{i}     {row[0]:6d}  {row[1]:6d}  {row[2]:6d}")
                print("  " + "="*50)

                # 保存详细性能到评估结果
                evaluation_results_to_return_dict['detailed_class_performance'] = {
                    'precision': precision.tolist(),
                    'recall': recall.tolist(),
                    'f1': f1.tolist(),
                    'support': support.tolist(),
                    'class01_avg_precision': class01_precision_avg,
                    'class01_avg_recall': class01_recall_avg,
                    'class01_avg_f1': class01_f1_avg,
                    'confusion_matrix': cm.tolist()
                }

            except Exception as e_detailed_eval:
                print(f"  ⚠️ 详细性能分析出错: {e_detailed_eval}")

            try:
                # 🚨 修复：暂存阈值优化开始消息，稍后输出
                threshold_optimization_start_message = "--- 开始元模型决策阈值优化 ---"

                # 检查是否启用阈值优化
                enable_threshold_optimization = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_ENABLE', True)
                optimization_method = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_METHOD', 'optuna')
                n_trials = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_N_TRIALS', 500)
                optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')

                if enable_threshold_optimization and len(y_val_data_to_return_np) >= 10:
                    # 🚨 修复：暂存阈值优化方法消息，稍后输出
                    threshold_optimization_method_message = f"元模型阈值优化已启用 (方法: {optimization_method}, 策略: {optimization_strategy}, 试验数: {n_trials})"

                    optimal_thresholds = optimize_meta_decision_thresholds(
                        y_val_data_to_return_np,
                        y_proba_meta_val_final_eval,
                        optimization_method=optimization_method,
                        n_trials=n_trials,
                        verbose=True,
                        model_dir=meta_model_save_dir_final,
                        model_name="meta_model"
                    )

                    # 保存优化结果到评估字典
                    evaluation_results_to_return_dict['optimal_thresholds'] = optimal_thresholds

                    # 保存到配置文件或JSON
                    threshold_save_path = os.path.join(meta_model_save_dir_final, "optimal_thresholds.json")
                    try:
                        with open(threshold_save_path, 'w', encoding='utf-8') as f_thresh:
                            json.dump(optimal_thresholds, f_thresh, indent=2, ensure_ascii=False)
                        print(f"  ✅ 最优阈值已保存到: {threshold_save_path}")
                    except Exception as e_save_thresh:
                        print(f"  ⚠️ 保存最优阈值失败: {e_save_thresh}")

                    # 更新config.py中的阈值 (可选)
                    try:
                        print(f"  💡 建议更新config.py中的元模型决策阈值:")
                        print(f"     META_SIGNAL_UP_THRESHOLD = {optimal_thresholds['threshold_up']:.3f}")
                        print(f"     META_SIGNAL_DOWN_THRESHOLD = {optimal_thresholds['threshold_down']:.3f}")
                        if 'confidence_gap_up' in optimal_thresholds:
                            print(f"     META_SIGNAL_CONFIDENCE_GAP_UP = {optimal_thresholds['confidence_gap_up']:.3f}")
                        if 'confidence_gap_down' in optimal_thresholds:
                            print(f"     META_SIGNAL_CONFIDENCE_GAP_DOWN = {optimal_thresholds['confidence_gap_down']:.3f}")
                        if 'confidence_gap' in optimal_thresholds:
                            print(f"     META_SIGNAL_CONFIDENCE_GAP = {optimal_thresholds['confidence_gap']:.3f}")
                    except:
                        pass

                else:
                    if not enable_threshold_optimization:
                        print(f"  元模型阈值优化已禁用")
                    else:
                        print(f"  验证集样本不足 ({len(y_val_data_to_return_np)} < 10)，跳过阈值优化")

            except Exception as e_threshold_opt:
                print(f"  ⚠️ 元模型阈值优化过程中出现错误: {e_threshold_opt}")
                print("  阈值优化失败不会影响元模型训练的成功")
                import traceback
                traceback.print_exc(limit=2)
        except Exception as e_eval_meta_in_func_final:
            print(f"  !!! prediction.train_meta_model: 评估元模型时出错: {e_eval_meta_in_func_final}")
            traceback.print_exc(limit=1)
            evaluation_results_to_return_dict['status'] = f'Error_Eval: {str(e_eval_meta_in_func_final)}'
    else:
        print("  prediction.train_meta_model: 未划分验证集或验证集为空，跳过元模型内部评估。")
        evaluation_results_to_return_dict['status'] = 'NoValData_For_InternalEval'
        evaluation_results_to_return_dict['val_accuracy'] = None
        evaluation_results_to_return_dict['val_logloss'] = None
        evaluation_results_to_return_dict['val_classification_report_dict'] = None

    # 构建文件路径
    meta_model_filename_to_save = META_MODEL_FILENAME
    meta_model_filepath_to_save = os.path.join(meta_model_save_dir_final, meta_model_filename_to_save)
    meta_features_filename_to_save = META_FEATURES_FILENAME
    meta_features_filepath_to_save = os.path.join(meta_model_save_dir_final, meta_features_filename_to_save)
    meta_params_filename_to_save = "meta_model_params.json"
    meta_params_filepath_to_save = os.path.join(meta_model_save_dir_final, meta_params_filename_to_save)

    print(f"  prediction.train_meta_model: 准备保存文件:")
    print(f"    模型文件: {meta_model_filepath_to_save}")
    print(f"    特征文件: {meta_features_filepath_to_save}")
    print(f"    参数文件: {meta_params_filepath_to_save}")

    try:
        # 保存模型文件
        print(f"  prediction.train_meta_model: 开始保存模型文件...")
        joblib.dump(trained_model_object_to_return, meta_model_filepath_to_save, compress=3)
        if os.path.exists(meta_model_filepath_to_save):
            file_size = os.path.getsize(meta_model_filepath_to_save)
            print(f"  ✅ 元模型已成功保存到: {meta_model_filepath_to_save} (大小: {file_size} 字节)")
        else:
            print(f"  ❌ 元模型保存失败，文件不存在: {meta_model_filepath_to_save}")

        # 保存特征列表
        print(f"  prediction.train_meta_model: 开始保存特征列表...")
        with open(meta_features_filepath_to_save, 'w', encoding='utf-8') as f_json_meta_feat_write:
            json.dump(list(X_meta_df.columns), f_json_meta_feat_write, indent=2, ensure_ascii=False)
        if os.path.exists(meta_features_filepath_to_save):
            print(f"  ✅ 特征列表已成功保存到: {meta_features_filepath_to_save}")
        else:
            print(f"  ❌ 特征列表保存失败，文件不存在: {meta_features_filepath_to_save}")

        # 保存模型参数
        print(f"  prediction.train_meta_model: 开始保存模型参数...")
        with open(meta_params_filepath_to_save, 'w', encoding='utf-8') as f_params_meta_write:
            json.dump(trained_model_object_to_return.get_params(), f_params_meta_write, indent=4, ensure_ascii=False)
        if os.path.exists(meta_params_filepath_to_save):
            print(f"  ✅ 模型参数已成功保存到: {meta_params_filepath_to_save}")
        else:
            print(f"  ❌ 模型参数保存失败，文件不存在: {meta_params_filepath_to_save}")

    except Exception as e_save_meta_model_in_func_final:
        print(f"!!! prediction.train_meta_model: 保存元模型或其元数据失败: {e_save_meta_model_in_func_final}")
        import traceback
        traceback.print_exc()
        evaluation_results_to_return_dict['status'] = f'Error_Save: {str(e_save_meta_model_in_func_final)}'
        return False, trained_model_object_to_return, X_val_data_to_return_np, y_val_data_to_return_np, evaluation_results_to_return_dict

    # --- SHAP可解释性分析 ---
    try:
        print("\n  prediction.train_meta_model: --- 开始SHAP可解释性分析 ---")
        shap_analysis_results = perform_shap_analysis(
            trained_model_object_to_return,
            X_val_data_to_return_np,
            y_val_data_to_return_np,
            list(X_meta_df.columns),
            meta_model_save_dir_final
        )
        if shap_analysis_results:
            evaluation_results_to_return_dict['shap_analysis'] = shap_analysis_results
            print("  ✅ SHAP可解释性分析完成")
        else:
            print("  ⚠️ SHAP分析未成功完成，但不影响模型训练")
    except Exception as e_shap:
        print(f"  ⚠️ SHAP分析过程中出现错误: {e_shap}")
        print("  SHAP分析失败不会影响元模型训练的成功")
        import traceback
        traceback.print_exc(limit=2)

    # 🚨 修复：在最后输出所有暂存的详细信息
    print("\n" + "="*60)
    print("📊 元模型训练详细信息")
    print("="*60)

    # 输出Optuna优化结果
    if 'optuna_best_value' in locals() and 'optuna_best_params' in locals():
        print(f"prediction.train_meta_model: Optuna优化完成。最佳试验值 ({getattr(config, 'META_MODEL_OPTUNA_METRIC', 'macro_f1_score')}): {optuna_best_value:.4f}")
        print(f"prediction.train_meta_model: Optuna找到的最佳参数: {optuna_best_params}")

    # 输出参数使用信息
    if 'optuna_success_message' in locals():
        print(f"prediction.train_meta_model: {optuna_success_message}")

    # 输出最终参数
    if 'final_params_info' in locals():
        print(f"prediction.train_meta_model: 元模型最终LightGBM参数: {final_params_info}")

    # 输出评估结果
    if 'evaluation_accuracy' in locals() and 'evaluation_logloss' in locals() and 'evaluation_report' in locals():
        print(f"验证集准确率 (Accuracy): {evaluation_accuracy:.4f}")
        print(f"验证集LogLoss: {evaluation_logloss:.4f}")
        print(f"验证集分类报告:")
        print(evaluation_report)

    # 输出阈值优化信息 (重新启用)
    if 'threshold_optimization_start_message' in locals():
        print(f"\nprediction.train_meta_model: {threshold_optimization_start_message}")
    if 'threshold_optimization_method_message' in locals():
        print(f"{threshold_optimization_method_message}")

    # 输出阈值优化结果
    if 'optimal_thresholds' in evaluation_results_to_return_dict:
        optimal_thresholds = evaluation_results_to_return_dict['optimal_thresholds']
        print(f"\n🎯 元模型阈值优化结果:")
        print(f"   最优上涨阈值: {optimal_thresholds.get('threshold_up', 'N/A')}")
        print(f"   最优下跌阈值: {optimal_thresholds.get('threshold_down', 'N/A')}")
        if 'confidence_gap' in optimal_thresholds:
            print(f"   置信度差值: {optimal_thresholds['confidence_gap']}")
        if 'expected_profit' in optimal_thresholds:
            print(f"   预期收益: {optimal_thresholds['expected_profit']:.4f}")
        if 'num_trades' in optimal_thresholds:
            print(f"   交易次数: {optimal_thresholds['num_trades']}")
        if 'win_rate' in optimal_thresholds:
            print(f"   胜率: {optimal_thresholds['win_rate']:.2%}")
    else:
        print(f"\nprediction.train_meta_model: 阈值优化未执行或失败")

    print("="*60)
    print("<---<<< prediction.train_meta_model 函数执行结束。")
    return True, trained_model_object_to_return, X_val_data_to_return_np, y_val_data_to_return_np, evaluation_results_to_return_dict


# --- DynamicConfigManager 初始化 ---
DYNAMIC_CONFIG_FILEPATH = "dynamic_params.json"

def get_global_dynamic_config_manager():
    return dynamic_config_manager

def load_meta_model_if_needed():
    """智能加载元模型和其特征名列表 (优先加载V11.0精英超级元模型)。"""
    # global meta_model_instance, meta_model_feature_names, meta_model_loaded_successfully

    if global_prediction_state_manager.is_meta_model_loaded_successfully():
        return True

    if not getattr(config, 'ENABLE_META_MODEL_TRAINING', False) and not getattr(config, 'ENABLE_META_MODEL_PREDICTION', True): # 新增一个开关控制是否使用元模型进行预测
        print("  [MetaModel Load] 元模型预测未启用 (config.ENABLE_META_MODEL_PREDICTION = False)。")
        return False

    # 🎯 关键修复：智能元模型检测和加载
    print("  [MetaModel Load] 开始智能元模型检测...")

    # 1. 优先尝试加载V11.0精英超级元模型
    elite_model_dir = "models/elite_meta_model"
    elite_model_path = os.path.join(elite_model_dir, "elite_super_meta_model.joblib")
    elite_features_path = os.path.join(elite_model_dir, "elite_meta_features.json")

    if os.path.exists(elite_model_path) and os.path.exists(elite_features_path):
        print("  [MetaModel Load] 🌟 发现V11.0精英超级元模型，优先加载...")

        try:
            loaded_model_instance = joblib.load(elite_model_path)
            with open(elite_features_path, 'r') as f:
                loaded_feature_names = json.load(f)

            if loaded_model_instance and loaded_feature_names:
                global_prediction_state_manager.set_meta_model_instance(loaded_model_instance)
                global_prediction_state_manager.set_meta_model_feature_names(loaded_feature_names)
                global_prediction_state_manager.set_meta_model_loaded_successfully(True)
                print(f"  [MetaModel Load] ✅ V11.0精英超级元模型加载成功！")
                print(f"    模型类型: {type(loaded_model_instance).__name__}")
                print(f"    精英特征数: {len(loaded_feature_names)}")
                print(f"    特征列表: {loaded_feature_names}")
                return True
            else:
                print(f"  [MetaModel Load] ⚠️  V11.0精英超级元模型加载后为空，尝试传统元模型...")
        except Exception as e:
            print(f"  [MetaModel Load] ⚠️  V11.0精英超级元模型加载失败: {e}，尝试传统元模型...")
    else:
        print("  [MetaModel Load] 📊 未找到V11.0精英超级元模型，尝试传统元模型...")

    # 2. 回退到传统元模型
    meta_model_dir = getattr(config, 'META_MODEL_SAVE_DIR', "meta_model_data") # 从config获取

    meta_model_path = os.path.join(meta_model_dir, META_MODEL_FILENAME)
    meta_features_path = os.path.join(meta_model_dir, META_FEATURES_FILENAME)

    if not os.path.exists(meta_model_path):
        print(f"!!! [MetaModel Load] 传统元模型文件未找到: {meta_model_path}")
        global_prediction_state_manager.set_meta_model_loaded_successfully(False)
        return False
    if not os.path.exists(meta_features_path):
        print(f"!!! [MetaModel Load] 传统元模型特征列表文件未找到: {meta_features_path}")
        global_prediction_state_manager.set_meta_model_loaded_successfully(False)
        return False

    try:
        loaded_model_instance = joblib.load(meta_model_path)
        with open(meta_features_path, 'r') as f:
            loaded_feature_names = json.load(f)

        if loaded_model_instance and loaded_feature_names:
            global_prediction_state_manager.set_meta_model_instance(loaded_model_instance)
            global_prediction_state_manager.set_meta_model_feature_names(loaded_feature_names)
            global_prediction_state_manager.set_meta_model_loaded_successfully(True)
            print(f"  [MetaModel Load] ✅ 传统元模型 '{META_MODEL_FILENAME}' 加载成功")
            print(f"    特征数: {len(loaded_feature_names)}")
            return True
        else:
            print(f"!!! [MetaModel Load] 传统元模型或特征列表加载后为空。")
            global_prediction_state_manager.set_meta_model_instance(None)
            global_prediction_state_manager.set_meta_model_feature_names(None)
            global_prediction_state_manager.set_meta_model_loaded_successfully(False)
            return False
    except Exception as e:
        print(f"!!! [MetaModel Load] 加载传统元模型时发生错误: {e}")
        global_prediction_state_manager.set_meta_model_instance(None)
        global_prediction_state_manager.set_meta_model_feature_names(None)
        global_prediction_state_manager.set_meta_model_loaded_successfully(False)
        return False


def _get_default_dynamic_config_from_static():
    initial_dynamic_config = {"targets": {}, "global_settings": {"master_signal_sending_enabled": True}}
    if hasattr(config, 'PREDICTION_TARGETS') and isinstance(config.PREDICTION_TARGETS, list):
        for static_cfg_item in config.PREDICTION_TARGETS:
            if isinstance(static_cfg_item, dict) and 'name' in static_cfg_item:
                target_name = static_cfg_item['name']
                static_kelly_params = static_cfg_item.get('kelly_config_params', {})
                initial_dynamic_config["targets"][target_name] = {
                    "virtual_total_capital_for_kelly": static_kelly_params.get("virtual_total_capital_for_kelly", 1000.0),
                    "win_rate_p_estimate": static_kelly_params.get("win_rate_p_estimate", 0.55),
                    "max_kelly_fraction_f": static_kelly_params.get("max_kelly_fraction_f", 0.1),
                    "enabled": True 
                }
    return initial_dynamic_config

try:
    dynamic_config_manager = DynamicConfigManager(DYNAMIC_CONFIG_FILEPATH, _get_default_dynamic_config_from_static)
except Exception as e_dcm_init:
    print(f"!!! CRITICAL ERROR during DynamicConfigManager instantiation in prediction.py: {e_dcm_init}")
    traceback.print_exc()

def stop_dynamic_config_observer_on_exit(): # 重命名以避免与atexit.register冲突
    if 'dynamic_config_manager' in globals() and dynamic_config_manager:
        dynamic_config_manager.stop_observer()
atexit.register(stop_dynamic_config_observer_on_exit)


def get_prediction_lock():
    return prediction_lock

# --- 通知与声音函数 (保持不变) ---
def _notify_simulator(signal_type, target_name_origin, amount=None, symbol_for_sim=None, sim_url_param=None, context_data=None):
    if not SEND_SIGNALS_TO_SIMULATOR: # 使用模块级的 SEND_SIGNALS_TO_SIMULATOR
        # print(f"[{target_name_origin}] 模拟盘信号发送已禁用 (全局配置)。")
        return False
    
    actual_sim_url = sim_url_param
    if not actual_sim_url: 
        actual_sim_url = SIMULATOR_API_URL # 使用模块级的默认 URL
        if not actual_sim_url:
            print(f"!!! 预测系统: 未能确定模拟盘URL，不发送信号 for {target_name_origin}。")
            return False
        # print(f"警告: _notify_simulator 未收到特定 sim_url, 将使用模块级配置的 URL: {actual_sim_url}")

    payload = {
        "signal_type": signal_type.upper(),
        "target_name": str(target_name_origin)
    }
    if amount is not None:
        payload["amount"] = float(amount)
    if symbol_for_sim:
        payload["symbol"] = str(symbol_for_sim).upper()

    # 🚀 V12.0: 传递完整的上下文数据（信号快照）
    if context_data is not None:
        payload["context_data"] = context_data
    
    # print(f"DEBUG prediction.py/_notify_simulator: Sending payload: {payload} to URL: {actual_sim_url}") 
    
    try:
        response = requests.post(actual_sim_url, json=payload, timeout=3)
        response.raise_for_status()
        log_symbol = symbol_for_sim if symbol_for_sim else "UnknownSymbol"
        print(f"预测系统 [{log_symbol} via {target_name_origin}]: 信号成功发送至模拟盘 ({actual_sim_url})...")
        return True
    except Exception as e: 
        log_symbol = symbol_for_sim if symbol_for_sim else "UnknownSymbol"
        print(f"!!! 预测系统 [{log_symbol} via {target_name_origin}]: 发送信号至模拟盘 ({actual_sim_url}) 出错: {e}"); 
        return False

def _stop_music_after_duration():
    """辅助函数，用于在定时器触发时停止音乐（或开始淡出）"""
    # music_fadeout_timer is now managed by PredictionStateManager
    if pygame.mixer.music.get_busy():
        fadeout_duration = 1000 # 例如，用1秒时间淡出
        print(f"  [Pygame Sound] 开始淡出音乐 ({fadeout_duration}ms)...")
        pygame.mixer.music.fadeout(fadeout_duration)
    
    current_timer = global_prediction_state_manager.get_music_fadeout_timer()
    if current_timer:
        current_timer.cancel() # 取消定时器，以防万一
        global_prediction_state_manager.set_music_fadeout_timer(None)

def _play_sound_with_pygame_non_blocking(sound_path, sound_type="未知", duration_ms=None):
    global PYGAME_MIXER_AVAILABLE # music_fadeout_timer is now managed by PredictionStateManager
    
    if not PYGAME_MIXER_AVAILABLE or not pygame.mixer.get_init():
        print(f"!!! [{sound_type} Sound] Pygame mixer 不可用或未初始化。")
        return False
    
    try:
        if pygame.mixer.music.get_busy():
            print(f"  [Pygame Sound] 检测到音乐正忙，先停止当前播放...")
            pygame.mixer.music.stop()
            pygame.mixer.music.unload() 
        
        pygame.mixer.music.load(sound_path)
        print(f"  [Pygame Sound] 已加载: {sound_path}")
        
        pygame.mixer.music.play() # 开始播放
        print(f"  [Pygame Sound] 开始播放 '{sound_type}'。")

        # 如果之前有定时器，取消它
        existing_timer = global_prediction_state_manager.get_music_fadeout_timer()
        if existing_timer and existing_timer.is_alive():
            existing_timer.cancel()
            global_prediction_state_manager.set_music_fadeout_timer(None)
            print(f"  [Pygame Sound] 取消了之前的淡出定时器。")

        if duration_ms and duration_ms > 0:
            # 创建一个新的定时器，在 duration_ms 毫秒后调用 _stop_music_after_duration
            # 注意：实际淡出时间是在 _stop_music_after_duration 中定义的 fadeout_duration
            # duration_ms 是指从播放开始到 *开始淡出* 的时间（或如果不想淡出，则是到停止的时间）
            
            # 如果 duration_ms 很短，可能淡出效果不明显或不需要
            actual_play_duration_seconds = duration_ms / 1000.0
            print(f"  [Pygame Sound] 计划在 {actual_play_duration_seconds:.1f} 秒后开始淡出/停止。")
            
            new_timer = threading.Timer(actual_play_duration_seconds, _stop_music_after_duration)
            new_timer.daemon = True # 确保主程序退出时此线程也退出
            new_timer.start()
            global_prediction_state_manager.set_music_fadeout_timer(new_timer)
        else:
            # 如果没有提供 duration_ms，则完整播放音频
            print(f"  [Pygame Sound] 未指定播放时长，将完整播放。")
            global_prediction_state_manager.set_music_fadeout_timer(None) # 确保没有活动的定时器

        return True
        
    except pygame.error as pg_err: # 更具体地捕获pygame的错误
        print(f"!!! [{sound_type} Sound] Pygame 播放 '{sound_path}' 时发生 Pygame 错误: {pg_err}")
        traceback.print_exc(limit=1)
        return False
    except Exception as e_pg:
        print(f"!!! [{sound_type} Sound] Pygame 播放 '{sound_path}' 时发生未知错误: {e_pg}")
        traceback.print_exc(limit=1)
        return False


def play_signal_alert_sound(signal_type: str):
    current_time = time.time()
    last_alert_time = global_prediction_state_manager.get_last_signal_alert_time()
    if not (getattr(config, 'ALERT_SOUND_ENABLED', False) and (current_time - last_alert_time > 3)): return
    sound_played, custom_sound_path, sound_desc = False, None, "未知"
    if signal_type == "UP": custom_sound_path, sound_desc = getattr(config, 'CUSTOM_UP_SIGNAL_SOUND_PATH', None), "做多"
    elif signal_type == "DOWN": custom_sound_path, sound_desc = getattr(config, 'CUSTOM_DOWN_SIGNAL_SOUND_PATH', None), "做空"
    else: return
    if PYGAME_MIXER_AVAILABLE and custom_sound_path and os.path.exists(custom_sound_path):
        if _play_sound_with_pygame_non_blocking(custom_sound_path, sound_desc + "信号", getattr(config, 'CUSTOM_SIGNAL_SOUND_DURATION_MS', None)):
            sound_played = True
    if not sound_played:
        # 使用系统铃声作为后备
        print('\a', end='', flush=True)
        sound_played = True
    if sound_played:
        global_prediction_state_manager.update_last_signal_alert_time(current_time)

def play_pre_prediction_alarm(target_name_for_alarm):
    current_time = time.time()
    last_alarm_time = global_prediction_state_manager.get_last_pre_alarm_play_time(target_name_for_alarm)
    if not (getattr(config, 'PRE_PREDICTION_ALARM_ENABLED', False) and \
            (current_time - last_alarm_time > getattr(config, 'PRE_ALARM_COOLDOWN_SECONDS', 70))): return
    sound_played, custom_alarm_path = False, getattr(config, 'CUSTOM_PRE_ALARM_SOUND_PATH', None)
    if PYGAME_MIXER_AVAILABLE and custom_alarm_path and os.path.exists(custom_alarm_path):
        if _play_sound_with_pygame_non_blocking(custom_alarm_path, f"预告({target_name_for_alarm})", getattr(config, 'CUSTOM_PRE_ALARM_SOUND_DURATION_MS', None)): sound_played = True
    if sound_played: global_prediction_state_manager.update_last_pre_alarm_play_time(target_name_for_alarm, current_time)

# --- 获取实时价格 (保持不变) ---
def get_real_time_price(binance_client_unused, symbol_override=None):
    # ... (与你之前版本一致) ...
    symbol_to_get = symbol_override if symbol_override else getattr(config, 'SYMBOL', 'BTCUSDT')
    ws_price = realtime_data_manager.get_latest_price(symbol_to_get)
    if ws_price is not None: return float(ws_price)
    url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol_to_get}"
    headers = {'User-Agent': 'Mozilla/5.0'}
    for _ in range(2):
        try:
            response = requests.get(url, timeout=1.5, headers=headers); response.raise_for_status()
            data = response.json();
            if data and 'price' in data: return float(data['price'])
        except: time.sleep(0.3)
    print(f"!!! 严重警告: 多次尝试后仍未能获取 {symbol_to_get} 的价格。")
    return None




# --- 金额计算策略函数 (核心修改处) ---
def _calculate_dynamic_kelly_fraction(target_config, probability_value, signal_direction, target_name):
    """
    🎯 核心优化建议2.3：动态风险敞口模型

    根据市场确定性和模型置信度动态调整max_kelly_fraction_f，实现智能风险管理。

    Args:
        target_config (dict): 目标的动态参数字典。
        probability_value (float): 模型对有利方向的预测概率。
        signal_direction (str): "UP" 或 "DOWN"。
        target_name (str): 目标名称，用于日志记录。

    Returns:
        float: 经过动态调整后的max_kelly_fraction_f。
    """
    try:
        # 1. 从配置中获取基础的 max_kelly_fraction_f 作为基准
        base_max_kelly = float(target_config.get('max_kelly_fraction_f', 0.2))

        # 2. 计算模型置信度评分 (0.0 - 1.0)
        #    概率越接近1，置信度越高
        model_confidence_score = abs(probability_value - 0.5) * 2
        model_confidence_score = min(model_confidence_score, 1.0) # 确保不超过1.0

        # 3. 计算市场确定性评分 (0.0 - 1.0)
        #    依赖于全局市场状态数据
        app_state = get_app_state()
        global_market_data = app_state.get_global_market_data()
        market_certainty_score = 0.5  # 默认中等确定性

        if global_market_data:
            atr_percent = global_market_data.get('global_atr_percent', 1.5)
            adx_value = global_market_data.get('global_adx', 25.0)

            # a. 波动率评分：适中波动最好 (1.0)，过高或过低都降低评分
            #    使用一个倒U型函数来评分
            optimal_atr = 1.0 # 假设1.0%是最佳波动率
            volatility_score = max(0, 1 - abs(atr_percent - optimal_atr) / (optimal_atr * 2.0))

            # b. 趋势强度评分：ADX越高，趋势越明确
            trend_score = min(1.0, max(0.0, (adx_value - 15) / 30.0))  # 将ADX从15-45映射到0-1

            # 综合市场确定性
            market_certainty_score = 0.6 * trend_score + 0.4 * volatility_score
        else:
            print(f"  警告 [{target_name} Dynamic Kelly]: 无法获取全局市场数据，市场确定性评分使用默认值 0.5")


        # 4. 计算综合确定性，并动态调整凯利比例
        #    权重可以根据策略调整，这里赋予模型置信度更高权重
        composite_certainty = 0.7 * model_confidence_score + 0.3 * market_certainty_score

        # 5. 定义凯利乘数：根据综合确定性调整风险敞口
        #    确定性高时放大风险 (最高1.5倍)，低时缩小风险 (最低0.3倍)
        kelly_multiplier = 0.3 + (1.2 * composite_certainty) # 将[0,1]的确定性映射到[0.3, 1.5]的乘数
        dynamic_kelly_fraction = base_max_kelly * kelly_multiplier

        # 6. 应用安全约束，防止极端值
        min_f = base_max_kelly * 0.1 # 最小不低于基准的10%
        max_f = base_max_kelly * 2.0 # 最大不超过基准的200%
        final_dynamic_f = max(min_f, min(dynamic_kelly_fraction, max_f))

        print(f"    调试 [{target_name} Dynamic Kelly]:")
        print(f"      - 输入: 概率={probability_value:.3f}, 基础max_f={base_max_kelly:.3f}")
        print(f"      - 评分: 模型置信度={model_confidence_score:.3f}, 市场确定性={market_certainty_score:.3f}")
        print(f"      - 决策: 综合确定性={composite_certainty:.3f} -> 凯利乘数={kelly_multiplier:.2f}x")
        print(f"      - 输出: 最终动态max_f = {final_dynamic_f:.4f}")

        return final_dynamic_f

    except Exception as e:
        print(f"  !!! 警告 [{target_name} Dynamic Kelly]: 动态凯利分数计算失败: {e}，将回退到静态基础值。")
        traceback.print_exc(limit=1)
        return float(target_config.get('max_kelly_fraction_f', 0.2))


def calculate_trade_amount_from_strategy(target_config_static, signal_direction, probability_value):
    """
    根据配置的策略计算交易金额，已集成动态胜率和动态风险敞口调整。

    Args:
        target_config_static (dict): 来自 config.py 的静态配置字典。
        signal_direction (str): "UP" 或 "DOWN"。
        probability_value (float): 模型对有利方向的预测概率。

    Returns:
        float: 计算出的交易金额。
    """
    calculated_amount_float = 0.0
    target_name = target_config_static.get('name', 'unknown_target_for_amount_calc')
    strategy = target_config_static.get('trade_amount_strategy', 'fixed')

    # 从动态配置管理器获取最新的参数，如果缺失则回退到静态配置
    dynamic_params_for_target = dynamic_config_manager.get_target_params(target_name, target_config_static)

    if strategy == "fixed":
        calculated_amount_float = float(dynamic_params_for_target.get('fixed_trade_amount', 5.0))

    elif strategy == "simple_prob_tiered":
        # ... (此部分逻辑不变，因为它不依赖于动态胜率或凯利) ...
        params_tiered = target_config_static.get('simple_prob_tiered_params')
        if params_tiered and isinstance(params_tiered.get('tiers'), list):
            # ...
            pass # 您的原有逻辑
        else:
            calculated_amount_float = float(dynamic_params_for_target.get('fixed_trade_amount', 5.0))

    elif strategy == "kelly_config":
        # --- 1. 保守下注期检查 ---
        min_bet_kelly_cfg = float(dynamic_params_for_target.get('min_bet_kelly', 5.0))
        max_bet_kelly_cfg = float(dynamic_params_for_target.get('max_bet_kelly', 250.0))
        enable_initial_conservative = dynamic_params_for_target.get('enable_initial_conservative_betting', True)

        if enable_initial_conservative:
            conservative_trades_threshold = int(dynamic_params_for_target.get('initial_conservative_trades_count', 25))
            current_execution_count = global_prediction_state_manager.get_strategy_execution_counter(target_name)
            if current_execution_count < conservative_trades_threshold:
                conservative_amount = float(dynamic_params_for_target.get('initial_conservative_bet_amount', min_bet_kelly_cfg))
                calculated_amount_float = max(min_bet_kelly_cfg, min(conservative_amount, max_bet_kelly_cfg))
                print(f"    调试 [{target_name} Kelly]: 处于保守期 ({current_execution_count}/{conservative_trades_threshold})，使用固定金额: ${calculated_amount_float:.2f}")
                return calculated_amount_float

        # --- 2. 动态胜率 (p) 计算与平滑过渡 ---
        win_rate_tracker = get_dynamic_win_rate_tracker()
        static_win_rate = float(dynamic_params_for_target.get('win_rate_p_estimate', 0.55))
        
        # 获取动态胜率和已结算交易数
        dynamic_win_rate, settled_trades_count = win_rate_tracker.get_dynamic_win_rate(
            target_name=target_name,
            min_trades=20, # 至少需要20笔交易才开始考虑动态胜率
            default_rate=static_win_rate
        )

        # 平滑过渡逻辑
        warmup_period = 20
        full_trust_period = 100
        if settled_trades_count < warmup_period:
            p_estimate = static_win_rate
            win_rate_source = f"静态预热期 (交易数 {settled_trades_count} < {warmup_period})"
        elif settled_trades_count < full_trust_period:
            blending_weight = (settled_trades_count - warmup_period) / (full_trust_period - warmup_period)
            p_estimate = (1 - blending_weight) * static_win_rate + blending_weight * dynamic_win_rate
            win_rate_source = f"平滑过渡期 (静态{(1-blending_weight)*100:.0f}% + 动态{blending_weight*100:.0f}%)"
        else:
            p_estimate = dynamic_win_rate
            win_rate_source = f"完全动态期 (交易数 > {full_trust_period})"
        
        # --- 3. 动态风险敞口 (max_f) 计算 ---
        max_f_config = _calculate_dynamic_kelly_fraction(
            target_config=dynamic_params_for_target,
            probability_value=probability_value,
            signal_direction=signal_direction,
            target_name=target_name
        )

        # --- 4. 凯利公式核心计算 ---
        b_payout = float(dynamic_params_for_target.get('payout_ratio_b', 0.85))
        reference_total_capital = float(dynamic_params_for_target.get('virtual_total_capital_for_kelly', 1000.0))
        
        if reference_total_capital <= 0:
            print(f"    警告 [{target_name} Kelly]: 参考总资本无效 (${reference_total_capital})，使用最小下注。")
            calculated_amount_float = min_bet_kelly_cfg
        else:
            q_loss_rate = 1.0 - p_estimate
            if b_payout == 0:
                kelly_fraction_raw = -1.0
            else:
                kelly_fraction_raw = p_estimate - (q_loss_rate / b_payout)
            
            print(f"    调试 [{target_name} Kelly] 输入:")
            print(f"      - 胜率(p): {p_estimate:.4f} ({win_rate_source})")
            print(f"      - 赔率(b): {b_payout:.2f} (静态)")
            print(f"      - 总资本  : ${reference_total_capital:.2f} (动态)")
            print(f"      - 最大比例(max_f): {max_f_config:.4f} (动态)")
            print(f"      - 原始凯利分数: {kelly_fraction_raw:.4f}")

            if kelly_fraction_raw > 0:
                f_applied_to_capital = min(kelly_fraction_raw, max_f_config)
                base_kelly_amount_float = reference_total_capital * f_applied_to_capital
                calculated_amount_float = max(min_bet_kelly_cfg, min(base_kelly_amount_float, max_bet_kelly_cfg))
                
                # 确保下注金额不超过虚拟总资本
                calculated_amount_float = min(calculated_amount_float, reference_total_capital)
                
                print(f"    调试 [{target_name} Kelly] 输出: 应用凯利分数={f_applied_to_capital:.4f}, 计算金额=${base_kelly_amount_float:.2f} -> 最终金额=${calculated_amount_float:.2f}")
            else:
                calculated_amount_float = float(dynamic_params_for_target.get('min_bet_if_kelly_negative', min_bet_kelly_cfg))
                print(f"    调试 [{target_name} Kelly]: 凯利分数为负，使用最小安全金额: ${calculated_amount_float:.2f}")
    else:
        print(f"警告 [{target_name}]: 未知的 trade_amount_strategy '{strategy}'，使用固定金额。")
        calculated_amount_float = float(dynamic_params_for_target.get('fixed_trade_amount', 5.0))

    # 最终安全检查，确保金额大于0
    if calculated_amount_float <= 0:
        calculated_amount_float = 1.0

    return calculated_amount_float




def _load_lstm_model_artifacts(target_config_static, target_name):
    """辅助函数：加载LSTM模型和相关工件"""
    model_dir = target_config_static.get('model_save_dir')
    if not model_dir:
        return None, None, "Error: model_save_dir not found", False, None, None

    try:
        # 检查LSTM模型文件是否存在
        lstm_model_path = os.path.join(model_dir, 'lstm_model.h5')
        if not os.path.exists(lstm_model_path):
            return None, None, f"Error: LSTM model file not found: {lstm_model_path}", False, None, None

        # 加载LSTM模型
        lstm_model = load_lstm_model(model_dir, target_config_static)
        if lstm_model is None:
            return None, None, f"Error: Failed to load LSTM model from {model_dir}", False, None, None

        # 加载缩放器
        scaler_path = os.path.join(model_dir, 'scaler.pkl')
        if not os.path.exists(scaler_path):
            return None, None, f"Error: Scaler file not found: {scaler_path}", False, None, None

        scaler = joblib.load(scaler_path)

        # 加载LSTM参数
        params_path = os.path.join(model_dir, 'lstm_params.pkl')
        lstm_params = {}
        if os.path.exists(params_path):
            lstm_params = joblib.load(params_path)

        # 创建模拟的model_meta用于兼容性
        model_meta = {
            'model_type': 'LSTM',
            'lstm_params': lstm_params,
            'feature_columns': lstm_params.get('feature_columns', []),
            'sequence_length': lstm_params.get('sequence_length', 60)
        }

        print(f"  Info ({target_name}): LSTM模型加载成功")
        return [lstm_model], scaler, "LSTM Model", True, model_meta, [0.5], None  # 默认阈值0.5, 无精英索引

    except Exception as e:
        return None, None, f"Error loading LSTM model: {e}", False, None, None, None


def _identify_elite_models(model_meta, top_n=3):
    """
    从模型元数据中识别精英模型（基于test_f1_score）

    Args:
        model_meta: 模型元数据字典
        top_n: 选择前N个精英模型

    Returns:
        list: 精英模型的fold索引列表，按F1分数降序排列
    """
    fold_artifacts = model_meta.get("fold_model_artifacts", [])
    fold_performance_metrics = model_meta.get("fold_performance_metrics", [])

    if not fold_performance_metrics:
        print("  Warning: No fold_performance_metrics found, using all folds as elite")
        return list(range(len(fold_artifacts)))

    # 提取每个fold的F1分数
    fold_scores = []
    for i, metrics in enumerate(fold_performance_metrics):
        f1_score = metrics.get("test_f1_score", 0.0)
        fold_scores.append((i, f1_score))

    # 按F1分数降序排序
    fold_scores.sort(key=lambda x: x[1], reverse=True)

    # 选择前top_n个
    elite_fold_indices = [fold_idx for fold_idx, _ in fold_scores[:top_n]]

    elite_scores = [(fold_idx, score) for fold_idx, score in fold_scores[:top_n]]
    print(f"  Elite models selected: {elite_scores}")

    return elite_fold_indices


def _load_model_artifacts(target_config_static, target_name, elite_only=False):
    """
    辅助函数：加载模型、Scaler和相关元数据，包括每个fold的最优阈值。
    使用改进的错误处理机制。

    Args:
        target_config_static: 目标配置字典
        target_name: 目标名称
        elite_only: 是否只加载精英模型

    Returns:
        tuple: (loaded_models, scaler, model_type_str, success, model_meta, fold_thresholds, elite_fold_indices)
    """
    logger = logging.getLogger(__name__)

    try:
        # 检查模型类型
        model_type = target_config_static.get('model_type', 'LGBM')
        logger.info(f"加载模型工件，目标: {target_name}, 模型类型: {model_type}")

        if model_type == 'LSTM':
            return _load_lstm_model_artifacts(target_config_static, target_name)

        # 验证模型保存目录
        model_dir = target_config_static.get('model_save_dir')
        if not model_dir:
            raise ConfigurationError(
                "model_save_dir 配置未找到",
                context={'target_name': target_name, 'config_keys': list(target_config_static.keys())}
            )

        if not os.path.exists(model_dir):
            raise ConfigurationError(
                f"模型保存目录不存在: {model_dir}",
                context={'target_name': target_name, 'model_dir': model_dir}
            )

        # 构建文件名
        target_name_for_file = target_name.replace('/', '_').replace(':', '_')
        minutes_display_val_str = str(target_config_static.get('prediction_minutes_display', '?'))
        if minutes_display_val_str == '?':
            raise ConfigurationError(
                f"prediction_minutes_display 配置未设置",
                context={'target_name': target_name}
            )

        # 检查元数据文件
        meta_filename = f"model_meta_{target_name_for_file}_{minutes_display_val_str}m.json"
        meta_file_path = os.path.join(model_dir, meta_filename)
        if not os.path.exists(meta_file_path):
            raise ModelLoadError(
                f"元数据文件缺失: {meta_filename}",
                context={
                    'target_name': target_name,
                    'meta_filename': meta_filename,
                    'meta_file_path': meta_file_path,
                    'model_dir': model_dir
                }
            )

        # 加载元数据文件
        try:
            with open(meta_file_path, 'r', encoding='utf-8') as f_meta:
                model_meta = json.load(f_meta)
            logger.info(f"成功加载元数据文件: {meta_filename}")
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            raise ModelLoadError(
                f"元数据文件格式错误: {meta_filename}",
                context={
                    'target_name': target_name,
                    'meta_filename': meta_filename,
                    'file_path': meta_file_path
                },
                original_exception=e
            )
        except IOError as e:
            raise ModelLoadError(
                f"无法读取元数据文件: {meta_filename}",
                context={
                    'target_name': target_name,
                    'meta_filename': meta_filename,
                    'file_path': meta_file_path
                },
                original_exception=e
            )

    except (ConfigurationError, ModelLoadError) as e:
        log_prediction_error(logger, e, '_load_model_artifacts', target_name,
                           {'step': 'initialization_and_metadata'})
        return None, None, str(e), False, None, None, None

    except Exception as e:
        error_context = {
            'target_name': target_name,
            'step': 'initialization_and_metadata',
            'unexpected_error': True
        }
        wrapped_error = ModelLoadError(
            f"加载模型工件时发生未预期错误: {str(e)}",
            context=error_context,
            original_exception=e
        )
        log_prediction_error(logger, wrapped_error, '_load_model_artifacts', target_name, error_context)
        return None, None, str(wrapped_error), False, None, None, None

    try:
        # 加载 Scaler
        scaler_filename = model_meta.get("scaler_filename")
        if not scaler_filename:
            raise ModelLoadError(
                "Scaler 文件名在元数据中未找到",
                context={
                    'target_name': target_name,
                    'meta_keys': list(model_meta.keys()) if model_meta else []
                }
            )

        scaler_path = os.path.join(model_dir, scaler_filename)
        if not os.path.exists(scaler_path):
            raise ModelLoadError(
                f"Scaler 文件缺失: {scaler_filename}",
                context={
                    'target_name': target_name,
                    'scaler_filename': scaler_filename,
                    'scaler_path': scaler_path,
                    'model_dir': model_dir
                }
            )

        try:
            scaler = joblib.load(scaler_path)
            if scaler is None:
                raise ValueError("Scaler 加载后为 None")
            logger.info(f"成功加载 Scaler: {scaler_filename}")
        except Exception as e:
            raise ModelLoadError(
                f"无法加载 Scaler 文件: {scaler_filename}",
                context={
                    'target_name': target_name,
                    'scaler_filename': scaler_filename,
                    'scaler_path': scaler_path
                },
                original_exception=e
            )

    except ModelLoadError as e:
        log_prediction_error(logger, e, '_load_model_artifacts', target_name,
                           {'step': 'scaler_loading'})
        return None, None, str(e), False, model_meta, None, None

    except Exception as e:
        error_context = {
            'target_name': target_name,
            'step': 'scaler_loading',
            'unexpected_error': True
        }
        wrapped_error = ModelLoadError(
            f"加载 Scaler 时发生未预期错误: {str(e)}",
            context=error_context,
            original_exception=e
        )
        log_prediction_error(logger, wrapped_error, '_load_model_artifacts', target_name, error_context)
        return None, None, str(wrapped_error), False, model_meta, None, None

    loaded_models = []
    fold_thresholds = []  # 新增：存储每个fold的最优阈值
    elite_fold_indices = []  # 新增：存储精英模型的fold索引
    model_type_str = "Unknown"
    fold_artifacts = model_meta.get("fold_model_artifacts", [])

    if not fold_artifacts: # Legacy: try loading single model
        # print(f"  Info ({target_name}): No 'fold_model_artifacts' in meta, attempting single model load...")
        single_model_base_fn = model_meta.get("model_filename_base")
        single_model_calib_fn = model_meta.get("calibrated_model_filename")
        model_to_load_path = None
        use_calib = target_config_static.get('enable_probability_calibration', True)

        if use_calib and single_model_calib_fn and os.path.exists(os.path.join(model_dir, single_model_calib_fn)):
            model_to_load_path = os.path.join(model_dir, single_model_calib_fn)
            model_type_str = f"Calibrated (Single): {os.path.basename(single_model_calib_fn)}"
        elif single_model_base_fn and os.path.exists(os.path.join(model_dir, single_model_base_fn)):
            model_to_load_path = os.path.join(model_dir, single_model_base_fn)
            model_type_str = f"Original (Single): {os.path.basename(single_model_base_fn)}"

        if model_to_load_path:
            try:
                loaded_models.append(joblib.load(model_to_load_path))
                # 为单模型加载阈值
                threshold = data_utils.load_threshold_from_model_metadata(
                    model_to_load_path, target_name=target_name, default_threshold=0.5
                )
                fold_thresholds.append(threshold)
                elite_fold_indices = [0]  # 单模型被视为精英模型
            except Exception as e:
                return None, scaler, f"Error loading single model '{os.path.basename(model_to_load_path)}': {e}", False, model_meta, None, None
        else:
            return None, scaler, f"Error: No fold_model_artifacts and no valid single model path for {target_name}", False, model_meta, None, None
    else: # Ensemble model with folds
        # 识别精英模型
        if elite_only:
            elite_fold_indices = _identify_elite_models(model_meta, top_n=3)
            print(f"  ({target_name}): Elite-only mode, loading folds: {elite_fold_indices}")
            folds_to_load = elite_fold_indices
        else:
            folds_to_load = list(range(len(fold_artifacts)))
            elite_fold_indices = list(range(len(fold_artifacts)))  # 所有fold都被视为精英
            print(f"  ({target_name}): Loading all folds: {folds_to_load}")

        for idx in folds_to_load:
            if idx >= len(fold_artifacts):
                print(f"  Warning ({target_name}): Fold {idx} out of range, skipping.")
                continue

            fold_info = fold_artifacts[idx]
            model_fn_key = "model_filename"
            use_calib_fold = target_config_static.get('enable_probability_calibration', True)
            if use_calib_fold and fold_info.get("calibrated_model_filename"):
                model_fn_key = "calibrated_model_filename"

            model_filename = fold_info.get(model_fn_key)
            if not model_filename:
                print(f"  Warning ({target_name}): Fold {idx} missing valid model filename in meta, skipping.")
                continue

            model_path = os.path.join(model_dir, model_filename)
            if os.path.exists(model_path):
                try:
                    loaded_models.append(joblib.load(model_path))
                    # 为每个fold加载其最优阈值
                    threshold = data_utils.load_threshold_from_model_metadata(
                        model_path, target_name=target_name, default_threshold=0.5
                    )
                    fold_thresholds.append(threshold)
                    if elite_only:
                        print(f"  Info ({target_name}): Elite fold {idx} loaded, 阈值: {threshold:.4f}")
                    else:
                        print(f"  Info ({target_name}): Fold {idx} 阈值: {threshold:.4f}")
                except Exception as e:
                    print(f"  Warning ({target_name}): Failed to load Fold {idx} model '{model_filename}': {e}, skipping.")
                    # 从elite_fold_indices中移除失败的fold
                    if idx in elite_fold_indices:
                        elite_fold_indices.remove(idx)
            else:
                print(f"  Warning ({target_name}): Fold {idx} model file '{model_filename}' not found, skipping.")
                # 从elite_fold_indices中移除缺失的fold
                if idx in elite_fold_indices:
                    elite_fold_indices.remove(idx)

        if loaded_models:
            if elite_only:
                model_type_str = f"Elite Ensemble ({len(loaded_models)} elite folds)"
            else:
                model_type_str = f"Ensemble ({len(loaded_models)} folds)"
        else:
            return None, scaler, f"Error: Failed to load any models from 'fold_model_artifacts' for {target_name}", False, model_meta, None, None

    if not loaded_models:
        return None, scaler, f"Error: No models were successfully loaded for {target_name}", False, model_meta, None, None

    # 确保fold_thresholds和loaded_models数量一致
    if len(fold_thresholds) != len(loaded_models):
        print(f"  Warning ({target_name}): 阈值数量({len(fold_thresholds)})与模型数量({len(loaded_models)})不匹配，用默认阈值填充")
        while len(fold_thresholds) < len(loaded_models):
            fold_thresholds.append(0.5)

    # 确保elite_fold_indices与loaded_models数量一致
    if len(elite_fold_indices) != len(loaded_models):
        print(f"  Warning ({target_name}): 精英fold索引数量({len(elite_fold_indices)})与加载模型数量({len(loaded_models)})不匹配")
        elite_fold_indices = elite_fold_indices[:len(loaded_models)]

    return loaded_models, scaler, model_type_str, True, model_meta, fold_thresholds, elite_fold_indices


# --- 核心预测周期函数 ---
def run_prediction_cycle_for_target(target_config_static, binance_client, app_timezone_param,
                                    return_core_prediction_only=False,
                                    simulator_actual_url=None):
    # --- 初始变量定义 ---
    target_name = target_config_static.get('name', 'unknown_target')
    interval_main = target_config_static.get('interval', 'unknown_interval')
    symbol_to_use = target_config_static.get('symbol', getattr(config, 'SYMBOL', 'BTCUSDT'))

    # 🎯 初始化logger
    logger = logging.getLogger(__name__)
    
    prediction_label = "等待中..."
    prediction_color = config.NEUTRAL_COLOR
    up_prob_display = "N/A"
    down_prob_display = "N/A"
    details = ""
    status_msg_for_gui = f"正在为 '{target_name}' 执行预测..." # 初始状态消息
    status_level_final_gui = "info" # 初始状态级别
    
    avg_up_prob_value = np.nan
    avg_down_prob_value = np.nan
    model_positive_class_prob = np.nan
    final_signal_for_internal_state = "Error_Initialization"
    any_successful_model_prediction = False

    actual_signal_for_sound_and_simulator = None
    last_kline_close_main = None # 会在数据获取后赋值
    model_type_loaded_str = "未知模型类型" # 会在模型加载后赋值
    filter_reason_details_for_gui = ""
    # 初始化阈值，将在模型加载后更新为最优阈值
    sig_thresh_use = float(target_config_static.get('signal_threshold', 0.6))
    # 初始化趋势和波动率相关变量，以防过滤器未执行或出错
    current_trend_status_text = "趋势: 未检测/未启用"
    current_volatility_status_text = "波动率: 未检测/未启用"
    higher_tf_trend_signal = 0
    higher_tf_trend_strength = 0
    current_volatility_level = 0
    # 🔧 修复：初始化技术指标变量
    latest_adx = 0.0
    latest_pdi = 0.0
    latest_mdi = 0.0
    atr_percent = 0.0
    atr_now = 0.0
    model_meta = None # 初始化 model_meta

    if not return_core_prediction_only:
        # 发布状态更新事件
        publish_status_update(status_msg_for_gui, status_level_final_gui)

    try: # <--- 这是最外层的 try 块，包裹所有核心逻辑
        # === 步骤 1: 加载模型、Scaler和阈值 (使用辅助函数) ===
        # 根据return_core_prediction_only参数决定是否只加载精英模型
        elite_only = return_core_prediction_only
        loaded_ensemble_models, scaler, model_type_loaded_str, load_success, model_meta_from_helper, fold_thresholds, elite_fold_indices = \
            _load_model_artifacts(target_config_static, target_name, elite_only=elite_only)

        if not load_success:
            # model_type_loaded_str from _load_model_artifacts already contains the error message
            raise ValueError(f"'{target_name}': Model/Scaler loading failed. Reason: {model_type_loaded_str}")

        # 如果加载成功，model_meta 应该由辅助函数返回，这里我们将其赋值给函数作用域内的 model_meta
        # 注意：原来的 model_meta 初始化是在 try 块之外，现在它依赖于 _load_model_artifacts 的成功返回
        model_meta = model_meta_from_helper
        if model_meta is None: # 确保 model_meta 在加载成功后不是 None
            raise ValueError(f"'{target_name}': model_meta is None even after successful artifact loading.")

        # 检查是否启用了独立阈值投票机制
        use_individual_thresholds = target_config_static.get('use_individual_fold_thresholds', getattr(config, 'USE_INDIVIDUAL_FOLD_THRESHOLDS', False))
        if use_individual_thresholds and fold_thresholds and len(fold_thresholds) == len(loaded_ensemble_models):
            print(f"  ({target_name}): 启用独立阈值投票机制，每个fold使用自己的最优阈值")
        else:
            print(f"  ({target_name}): 使用传统平均概率机制")

        # === 加载最优决策阈值 ===
        optimal_threshold_loaded = None
        try:
            # 🎯 优先尝试从主模型元数据文件中加载集成模型最优阈值
            model_dir = target_config_static.get('model_save_dir')
            if model_dir:
                # 构建主模型元数据文件路径
                pred_periods = target_config_static.get('prediction_periods', [1])
                pred_period = pred_periods[0] if isinstance(pred_periods, list) and pred_periods else 1
                interval_str = target_config_static.get('interval', 'Xm').rstrip('mhd')
                try:
                    minutes_display = int(interval_str) * pred_period
                except ValueError:
                    minutes_display = target_config_static.get('prediction_minutes_display', '?')

                target_name_for_file = target_name.replace('/', '_').replace(':', '_')
                main_meta_filename = f"model_meta_{target_name_for_file}_{minutes_display}m.json"
                main_meta_path = os.path.join(model_dir, main_meta_filename)

                # 尝试加载集成模型最优阈值
                ensemble_threshold = data_utils.load_ensemble_threshold_from_main_metadata(
                    main_meta_path,
                    target_name=target_name,
                    default_threshold=None  # 使用None表示未找到
                )

                if ensemble_threshold is not None:
                    optimal_threshold_loaded = ensemble_threshold
                    print(f"  ({target_name}): 成功加载集成模型最优阈值 {optimal_threshold_loaded:.4f}")
                else:
                    print(f"  ({target_name}): 未找到集成模型阈值，尝试加载单模型阈值")

                    # 回退到从第一个模型的元数据中加载最优阈值
                    if model_meta and 'model_files' in model_meta and model_meta['model_files']:
                        first_model_path = model_meta['model_files'][0]
                        if os.path.exists(first_model_path):
                            optimal_threshold_loaded = data_utils.load_threshold_from_model_metadata(
                                first_model_path,
                                target_name=target_name,
                                default_threshold=target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                            )
                            print(f"  ({target_name}): 成功加载单模型最优阈值 {optimal_threshold_loaded:.4f}")
                        else:
                            print(f"  ({target_name}): 模型文件不存在，使用默认阈值")
                            optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                    else:
                        print(f"  ({target_name}): 无模型元数据，使用默认阈值")
                        optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
            else:
                print(f"  ({target_name}): 无模型目录，使用默认阈值")
                optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))

        except Exception as e_threshold_load:
            print(f"  !!! ({target_name}): 加载最优阈值失败: {e_threshold_load}，使用默认阈值")
            optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))

        # 更新决策阈值为加载的最优阈值或集成模型预测阈值
        ensemble_threshold_override = target_config_static.get('ensemble_prediction_threshold_override', False)
        ensemble_prediction_threshold = target_config_static.get('ensemble_prediction_threshold')

        if ensemble_threshold_override and ensemble_prediction_threshold is not None:
            # 使用集成模型预测阈值覆盖最优阈值
            sig_thresh_use = float(ensemble_prediction_threshold)
            print(f"  ({target_name}): 决策阈值已设置为集成模型预测阈值 {sig_thresh_use:.4f} (覆盖最优阈值)")
        elif optimal_threshold_loaded is not None:
            sig_thresh_use = float(optimal_threshold_loaded)
            print(f"  ({target_name}): 决策阈值已更新为最优阈值 {sig_thresh_use:.4f}")
        else:
            print(f"  ({target_name}): 使用配置中的默认阈值 {sig_thresh_use:.4f}")

        # === 步骤 2: 获取K线数据 ===
        df_klines_main_raw = fetch_binance_history(binance_client, symbol_to_use, target_config_static.get('interval'), limit=target_config_static.get('prediction_fetch_limit', 350))
        if df_klines_main_raw is None or df_klines_main_raw.empty: raise ValueError(f"未能获取 {symbol_to_use}@{target_config_static.get('interval')} K线 for '{target_name}'")
        # +++ 数据验证 +++
        # 现在使用 df_klines_main_raw 进行验证
        validation_result_pred = DataValidator.validate_ohlcv_data(df_klines_main_raw.copy())
        
        if not validation_result_pred['is_valid']:
            error_msg_pred_val = f"预测用K线数据 for '{target_name}' 未通过验证: {validation_result_pred['errors']}"
            print(f"!!! {error_msg_pred_val}")
            if return_core_prediction_only:
                return {"p_favorable": np.nan, "p_up": np.nan, "p_down": np.nan,
                        "internal_signal": f"Error_DataValidation_{target_name}", "error": True}
            raise ValueError(error_msg_pred_val)
            
        if validation_result_pred['warnings']:
            print(f"  警告 ({target_name} 预测): 数据验证发现问题（已尝试清理）: {validation_result_pred['warnings']}")
        
        # 使用验证和可能清理过的数据赋值给 df_klines_main
        df_klines_main = validation_result_pred['cleaned_data']
        # +++ 验证结束 +++


        min_hist_bars_pred = target_config_static.get('min_historical_bars_for_prediction', 100)
        if len(df_klines_main) < min_hist_bars_pred: raise ValueError(f"K线数据过少 ({len(df_klines_main)}条) for '{target_name}'")
        last_kline_close_main = df_klines_main['close'].iloc[-1]





        # === 步骤 3: 特征准备和模型预测 ===
        model_type = target_config_static.get('model_type', 'LGBM')

        # 🎯 V9.0统一大市场：废除专家委员会物理分割，回归统一模型预测
        # 模型内部已通过市场状态特征和样本权重实现智能调度

        # 初始化专家委员会标志
        enable_expert_committee = False

        if False:  # 禁用V8.0专家委员会逻辑
            # V8.0专家委员会预测逻辑
            print(f"  Info ({target_name}): 🎯 使用V8.0专家委员会系统进行预测")

            try:
                from src.core.expert_committee_system import create_expert_committee_system

                # 创建专家委员会预测器
                _, predictor = create_expert_committee_system(
                    config=target_config_static,
                    model_base_dir=target_config_static.get('model_save_dir', 'models')
                )

                # 生成特征数据
                df_with_features = data_utils.add_classification_features(df_klines_main.copy(), target_config_static)
                if df_with_features is None:
                    raise ValueError(f"专家委员会特征生成失败 for '{target_name}'")

                # 添加MTFA特征（如果启用）
                if target_config_static.get('enable_mtfa', False):
                    df_mtfa = data_utils.add_mtfa_features_to_df(df_with_features.copy(), target_config_static, binance_client)
                    if df_mtfa is not None and not df_mtfa.empty:
                        df_with_features = df_mtfa

                # 使用专家委员会进行预测
                expert_prediction_result = predictor.predict_with_expert_committee(
                    df_current=df_with_features,
                    target_name=target_name
                )

                if expert_prediction_result.get('success', False):
                    # 处理专家委员会预测结果
                    expert_prediction = expert_prediction_result.get('prediction')
                    expert_confidence = expert_prediction_result.get('confidence', 0.5)
                    expert_market_state = expert_prediction_result.get('market_state', 'unknown')
                    expert_used = expert_prediction_result.get('expert_used', 'unknown')

                    if expert_prediction == 'FILTER':
                        # 高波动盘整状态，过滤信号
                        print(f"  🔴 专家委员会决策: 过滤信号 (市场状态: {expert_market_state})")
                        prediction_label = f"过滤 ({expert_market_state})"
                        prediction_color = config.NEUTRAL_COLOR
                        final_signal_for_internal_state = "Filter_HighVolSideways"
                        filter_reason_details_for_gui = f"专家委员会: {expert_prediction_result.get('reason', '高风险市场状态')}"

                        # 设置概率显示
                        up_prob_display = "过滤"
                        down_prob_display = "过滤"
                        model_positive_class_prob = 0.5

                    else:
                        # 正常预测结果
                        print(f"  🎯 专家委员会预测: {expert_prediction} (置信度: {expert_confidence:.3f}, 专家: {expert_used})")

                        # 转换预测结果
                        if expert_prediction == 1:  # 上涨
                            prediction_label = f"上涨 ({expert_used})"
                            prediction_color = config.UP_COLOR
                            final_signal_for_internal_state = "UP"
                            model_positive_class_prob = expert_confidence
                            up_prob_display = f"{expert_confidence:.1%}"
                            down_prob_display = f"{1-expert_confidence:.1%}"
                        else:  # 下跌
                            prediction_label = f"下跌 ({expert_used})"
                            prediction_color = config.DOWN_COLOR
                            final_signal_for_internal_state = "DOWN"
                            model_positive_class_prob = 1 - expert_confidence
                            up_prob_display = f"{1-expert_confidence:.1%}"
                            down_prob_display = f"{expert_confidence:.1%}"

                        any_successful_model_prediction = True

                        # 设置详细信息
                        details = f"专家: {expert_used}, 市场状态: {expert_market_state}, 置信度: {expert_confidence:.3f}"

                        # 检查信号阈值
                        if expert_confidence >= sig_thresh_use:
                            actual_signal_for_sound_and_simulator = final_signal_for_internal_state
                            print(f"  ✅ 专家委员会信号通过阈值检查: {actual_signal_for_sound_and_simulator}")
                        else:
                            print(f"  ⚠️ 专家委员会置信度 ({expert_confidence:.3f}) 低于阈值 ({sig_thresh_use})")
                            actual_signal_for_sound_and_simulator = None

                else:
                    # 专家委员会预测失败，回退到传统预测
                    error_msg = expert_prediction_result.get('error', 'Unknown error')
                    print(f"  ⚠️ 专家委员会预测失败: {error_msg}")
                    print(f"  🔄 回退到传统预测模式...")

                    # 设置标志以继续传统预测流程
                    enable_expert_committee = False

            except Exception as e:
                print(f"  ❌ 专家委员会系统错误: {e}")
                print(f"  🔄 回退到传统预测模式...")
                import traceback
                traceback.print_exc(limit=2)

                # 设置标志以继续传统预测流程
                enable_expert_committee = False

        # 如果不是专家委员会模式或专家委员会失败，继续传统预测
        print(f"  Debug ({target_name}): enable_expert_committee = {enable_expert_committee}")
        print(f"  Debug ({target_name}): model_type = {model_type}")

        if not enable_expert_committee:
            # 初始化预测结果变量
            all_probas_from_ensemble = []
            individual_decisions = []
            print(f"  Debug ({target_name}): 初始化预测变量完成，开始预测流程...")

            if model_type == 'LSTM':
                # LSTM模型预测逻辑
                print(f"  Info ({target_name}): 进入LSTM分支，使用LSTM模型进行预测")

                # 为LSTM准备序列数据
                lstm_params = model_meta.get('lstm_params', {})
                feature_columns = lstm_params.get('feature_columns', [])
                sequence_length = lstm_params.get('sequence_length', 60)

                # 🎯 使用LSTM专用的精简特征生成函数
                print(f"  Info ({target_name}): 使用LSTM专用特征生成函数，避免复杂技术指标")
                df_with_features = data_utils.add_lstm_features(df_klines_main.copy(), target_config_static)
                if df_with_features is None:
                    raise ValueError(f"LSTM特征生成失败 for '{target_name}'")

                # 准备LSTM预测序列
                X_sequence, valid_index = data_utils.prepare_lstm_prediction_data(
                    df_with_features, feature_columns, sequence_length, scaler, target_name
                )

                if X_sequence is None:
                    raise ValueError(f"LSTM序列数据准备失败 for '{target_name}'")

                # LSTM模型预测
                lstm_model = loaded_ensemble_models[0]  # LSTM只有一个模型
                prediction_result = lstm_model.predict(X_sequence)

                # 转换LSTM预测结果为标准格式
                if target_config_static.get('target_variable_type', 'BOTH') == 'BOTH':
                    # 三分类
                    probabilities = prediction_result['probabilities']
                    # 转换为二分类格式 (down_prob, up_prob)
                    down_prob = probabilities.get('down', 0.0)
                    up_prob = probabilities.get('up', 0.0)
                    neutral_prob = probabilities.get('neutral', 0.0)

                    # 重新归一化为二分类
                    total_non_neutral = down_prob + up_prob
                    if total_non_neutral > 0:
                        down_prob_norm = down_prob / total_non_neutral
                        up_prob_norm = up_prob / total_non_neutral
                    else:
                        down_prob_norm = up_prob_norm = 0.5

                    proba_ensemble_avg = np.array([down_prob_norm, up_prob_norm])
                else:
                    # 二分类
                    probabilities = prediction_result['probabilities']
                    negative_prob = probabilities.get('negative', 0.5)
                    positive_prob = probabilities.get('positive', 0.5)
                    proba_ensemble_avg = np.array([negative_prob, positive_prob])

                all_probas_from_ensemble = [proba_ensemble_avg]
                individual_decisions = ["LSTM_Decision"]

                # 🎯 初始化elite_individual_predictions用于LSTM模型
                elite_individual_predictions = {}
                # 为LSTM模型创建elite特征格式
                elite_feature_name = f"oof_{target_name}_lstm_prediction"
                elite_individual_predictions[elite_feature_name] = float(proba_ensemble_avg[1])  # UP概率

            else:
                # 传统LGBM模型预测逻辑
                print(f"  Info ({target_name}): 进入LGBM分支，开始准备LGBM预测特征...")
                try:
                    scaled_X_for_model = prepare_features_for_prediction(df_klines_main.copy(), client=binance_client, scaler=scaler, target_config=target_config_static, model_meta=model_meta)
                    print(f"  Info ({target_name}): prepare_features_for_prediction 返回结果: {type(scaled_X_for_model)}")
                    if scaled_X_for_model is not None:
                        print(f"  Info ({target_name}): 特征矩阵形状: {scaled_X_for_model.shape}")
                    else:
                        print(f"  Error ({target_name}): prepare_features_for_prediction 返回 None")
                except Exception as e:
                    print(f"  Error ({target_name}): prepare_features_for_prediction 抛出异常: {e}")
                    import traceback
                    traceback.print_exc()
                    raise ValueError(f"准备预测特征时发生异常 for '{target_name}': {e}")

                if scaled_X_for_model is None or scaled_X_for_model.shape[0] != 1:
                    raise ValueError(f"准备预测特征失败 for '{target_name}': 返回值为 {scaled_X_for_model}")

                # === 获取每个模型的概率和决策 ===
                elite_individual_predictions = {}  # 存储精英模型的个别预测

                for i, ensemble_member_model in enumerate(loaded_ensemble_models):
                    try:
                        proba_single_member = ensemble_member_model.predict_proba(scaled_X_for_model)[0]
                        if not (isinstance(proba_single_member, np.ndarray) and len(proba_single_member) == 2 and not np.isnan(proba_single_member).any()):
                            print(f"  !!! 警告 ({target_name}): 集成成员 {i} 返回了无效的概率值: {proba_single_member}，将使用中性概率 [0.5, 0.5]。")
                            proba_single_member = np.array([0.5, 0.5])

                        all_probas_from_ensemble.append(proba_single_member)

                        # 存储精英模型的个别预测（用于精英模型OOF特征）
                        if elite_only and i < len(elite_fold_indices):
                            fold_idx = elite_fold_indices[i]
                            elite_feature_name = f"oof_{target_name}_elite_fold{fold_idx}"
                            # 🎯 关键修复：根据目标变量类型统一概率语义为"看涨概率"
                            target_variable_type = target_config_static.get('target_variable_type', 'BOTH').upper()
                            if target_variable_type == "UP_ONLY":
                                elite_individual_predictions[elite_feature_name] = float(proba_single_member[1])  # 直接使用看涨概率
                            elif target_variable_type == "DOWN_ONLY":
                                elite_individual_predictions[elite_feature_name] = float(1.0 - proba_single_member[1])  # 转换下跌概率为看涨概率
                            else:  # BOTH
                                elite_individual_predictions[elite_feature_name] = float(proba_single_member[1])  # 使用UP概率

                        # 如果启用独立阈值投票，使用该fold的最优阈值进行决策
                        if use_individual_thresholds and fold_thresholds and i < len(fold_thresholds):
                            fold_threshold = fold_thresholds[i]
                            # 根据模型类型判断使用哪个概率
                            target_variable_type = target_config_static.get('target_variable_type', 'BOTH').upper()
                            if target_variable_type == "UP_ONLY":
                                fold_decision = "UP" if proba_single_member[1] > fold_threshold else "Neutral"
                            elif target_variable_type == "DOWN_ONLY":
                                fold_decision = "DOWN" if proba_single_member[1] > fold_threshold else "Neutral"
                            elif target_variable_type == "BOTH":
                                if proba_single_member[1] > fold_threshold:
                                    fold_decision = "UP"
                                elif proba_single_member[0] > fold_threshold:
                                    fold_decision = "DOWN"
                                else:
                                    fold_decision = "Neutral"
                            else:
                                fold_decision = "Neutral"

                            individual_decisions.append(fold_decision)
                            print(f"    Fold {i}: 概率={proba_single_member}, 阈值={fold_threshold:.4f}, 决策={fold_decision}")

                    except Exception as e_pred_member:
                        print(f"  !!! 警告 ({target_name}): 集成成员 {i} 预测时出错: {e_pred_member}，将使用中性概率 [0.5, 0.5]。")
                        all_probas_from_ensemble.append(np.array([0.5, 0.5]))
                        if use_individual_thresholds:
                            individual_decisions.append("Neutral")



        print(f"  Debug ({target_name}): 预测完成，检查结果...")
        print(f"  Debug ({target_name}): all_probas_from_ensemble 长度: {len(all_probas_from_ensemble)}")
        print(f"  Debug ({target_name}): all_probas_from_ensemble 内容: {all_probas_from_ensemble}")

        if not all_probas_from_ensemble:
            raise ValueError(f"所有集成模型预测均失败 (无有效概率输出) for target {target_name}")

        # 计算平均概率（用于显示和向后兼容）
        proba_from_model_raw = np.mean(all_probas_from_ensemble, axis=0)
        if not (isinstance(proba_from_model_raw, np.ndarray) and len(proba_from_model_raw) == 2 and not np.isnan(proba_from_model_raw).any()):
            raise ValueError(f"平均集成概率无效 for '{target_name}': {proba_from_model_raw}")

        # === 决策逻辑：独立阈值投票 vs 传统平均概率 ===
        target_variable_type = target_config_static.get('target_variable_type', 'BOTH').upper()

        if use_individual_thresholds and individual_decisions and len(individual_decisions) == len(loaded_ensemble_models):
            # 使用独立阈值投票机制
            up_votes = individual_decisions.count("UP")
            down_votes = individual_decisions.count("DOWN")
            neutral_votes = individual_decisions.count("Neutral")
            total_votes = len(individual_decisions)

            # 投票阈值：需要超过一半的模型同意
            vote_threshold = total_votes // 2 + 1

            if up_votes >= vote_threshold:
                final_signal_for_internal_state = "UP"
                raw_lgbm_signal_internal = "UP"
                model_positive_class_prob = proba_from_model_raw[1]
            elif down_votes >= vote_threshold:
                final_signal_for_internal_state = "DOWN"
                raw_lgbm_signal_internal = "DOWN"
                model_positive_class_prob = proba_from_model_raw[0] if target_variable_type == "DOWN_ONLY" else proba_from_model_raw[1]
            else:
                final_signal_for_internal_state = "Neutral"
                raw_lgbm_signal_internal = "Neutral"
                model_positive_class_prob = max(proba_from_model_raw)

            print(f"  ({target_name}): 投票结果 - UP:{up_votes}, DOWN:{down_votes}, Neutral:{neutral_votes}, 最终决策:{final_signal_for_internal_state}")

        else:
            # 使用传统的平均概率机制
            if target_variable_type == "UP_ONLY":
                model_positive_class_prob = proba_from_model_raw[1]
                if model_positive_class_prob > sig_thresh_use:
                    final_signal_for_internal_state = "UP"
                    raw_lgbm_signal_internal = "UP"
                else:
                    final_signal_for_internal_state = "Neutral"
                    raw_lgbm_signal_internal = "Neutral"
            elif target_variable_type == "DOWN_ONLY":
                model_positive_class_prob = proba_from_model_raw[1]
                if model_positive_class_prob > sig_thresh_use:
                    final_signal_for_internal_state = "DOWN"
                    raw_lgbm_signal_internal = "DOWN"
                else:
                    final_signal_for_internal_state = "Neutral"
                    raw_lgbm_signal_internal = "Neutral"
            elif target_variable_type == "BOTH":
                if proba_from_model_raw[1] > sig_thresh_use:
                    final_signal_for_internal_state = "UP"
                    raw_lgbm_signal_internal = "UP"
                    model_positive_class_prob = proba_from_model_raw[1]
                elif proba_from_model_raw[0] > sig_thresh_use:
                    final_signal_for_internal_state = "DOWN"
                    raw_lgbm_signal_internal = "DOWN"
                    model_positive_class_prob = proba_from_model_raw[0]
                else:
                    final_signal_for_internal_state = "Neutral"
                    raw_lgbm_signal_internal = "Neutral"
                    model_positive_class_prob = max(proba_from_model_raw)
            else:
                raise ValueError(f"未知的 target_variable_type: {target_variable_type} for target {target_name}")

        # 设置用于显示的概率值
        avg_down_prob_value = proba_from_model_raw[0]
        avg_up_prob_value = proba_from_model_raw[1]
        



        any_successful_model_prediction = True # 标记核心预测成功

        # === 初始化趋势和波动率相关变量 (在return_core_prediction_only检查之前) ===
        # 这些变量在核心模式返回时也需要使用
        higher_tf_trend_signal = 0  # -1: 下降趋势, 0: 无明确趋势, 1: 上升趋势
        higher_tf_trend_strength = 0  # 0: 弱趋势, 1: 强趋势
        latest_adx = 0.0
        latest_pdi = 0.0
        latest_mdi = 0.0
        latest_ema_short = 0.0
        latest_ema_long = 0.0
        current_trend_status_text = "趋势: 未计算"
        current_volatility_level = 0  # 0: 正常, 1: 高波动, -1: 低波动
        atr_now = 0.0
        atr_percent = 0.0
        current_volatility_status_text = "波动率: 未计算"

        # === 始终计算趋势和波动率指标 (用于元模型特征工程) ===
        # 即使在核心模式下也要计算，因为元模型需要这些特征
        enable_trend_detection_cfg = target_config_static.get('enable_trend_detection', False)
        enable_volatility_filter_cfg = target_config_static.get('enable_volatility_filter', False)

        # 趋势指标计算
        trend_tf_str_cfg = target_config_static.get('trend_detection_timeframe', interval_main)
        df_for_trend = df_klines_main if trend_tf_str_cfg == interval_main else \
                       fetch_binance_history(binance_client, symbol_to_use, trend_tf_str_cfg, limit=200)
        if df_for_trend is not None and not df_for_trend.empty and len(df_for_trend) >= 50:
            trend_indicator_type_cfg = target_config_static.get('trend_indicator_type', 'adx')
            trend_desc_brief_gui = f"{trend_indicator_type_cfg.upper()}@{trend_tf_str_cfg}"

            if trend_indicator_type_cfg == 'adx':
                adx_p_cfg = target_config_static.get('trend_adx_period',14)
                adx_strength_th_cfg = target_config_static.get('trend_adx_strength_threshold',23)
                adx_general_th_cfg = target_config_static.get('trend_adx_threshold',20)
                adx_df_calc = pta.adx(df_for_trend['high'], df_for_trend['low'], df_for_trend['close'], length=adx_p_cfg, lensig=adx_p_cfg)

                if adx_df_calc is not None and not adx_df_calc.empty:
                    adx_col, pdi_col, mdi_col = f'ADX_{adx_p_cfg}', f'DMP_{adx_p_cfg}', f'DMN_{adx_p_cfg}'
                    if all(c in adx_df_calc.columns for c in [adx_col, pdi_col, mdi_col]):
                        try:
                            adx_val = adx_df_calc[adx_col].iloc[-1]
                            pdi_val = adx_df_calc[pdi_col].iloc[-1]
                            mdi_val = adx_df_calc[mdi_col].iloc[-1]

                            if not any(pd.isna(v) for v in [adx_val, pdi_val, mdi_val]):
                                latest_adx, latest_pdi, latest_mdi = adx_val, pdi_val, mdi_val

                                if latest_adx > adx_general_th_cfg:
                                    higher_tf_trend_signal = 1 if latest_pdi > latest_mdi else (-1 if latest_mdi > latest_pdi else 0)
                                if latest_adx > adx_strength_th_cfg and higher_tf_trend_signal != 0:
                                    higher_tf_trend_strength = 1
                                current_trend_status_text = f"趋势: ADX({adx_p_cfg})={latest_adx:.1f}"
                        except (IndexError, KeyError):
                            pass  # 保持默认值

            elif trend_indicator_type_cfg == 'ema_cross':
                ema_short_p_cfg = target_config_static.get('trend_ema_short_period',20)
                ema_long_p_cfg = target_config_static.get('trend_ema_long_period',50)
                ema_short_calc = pta.ema(df_for_trend['close'], length=ema_short_p_cfg)
                ema_long_calc = pta.ema(df_for_trend['close'], length=ema_long_p_cfg)

                if ema_short_calc is not None and ema_long_calc is not None and not ema_short_calc.empty and not ema_long_calc.empty:
                    try:
                        latest_ema_short, latest_ema_long = ema_short_calc.iloc[-1], ema_long_calc.iloc[-1]
                        if latest_ema_short > latest_ema_long:
                            higher_tf_trend_signal = 1
                        elif latest_ema_short < latest_ema_long:
                            higher_tf_trend_signal = -1
                        current_trend_status_text = f"趋势: EMA({ema_short_p_cfg},{ema_long_p_cfg})"
                    except (IndexError, KeyError):
                        pass  # 保持默认值

        # 波动率指标计算
        vol_tf_cfg = target_config_static.get('volatility_filter_timeframe', interval_main)
        df_for_vol = df_klines_main if vol_tf_cfg == interval_main else \
                     fetch_binance_history(binance_client, symbol_to_use, vol_tf_cfg, limit=target_config_static.get('volatility_atr_period', 14) + 50)
        if df_for_vol is not None and not df_for_vol.empty and len(df_for_vol) >= target_config_static.get('volatility_atr_period', 14) + 1:
            atr_p_vol = target_config_static.get('volatility_atr_period', 14)
            atr_series = pta.atr(df_for_vol['high'], df_for_vol['low'], df_for_vol['close'], length=atr_p_vol)

            if atr_series is not None and not atr_series.empty and not pd.isna(atr_series.iloc[-1]):
                try:
                    atr_now = atr_series.iloc[-1]
                    close_for_atr = df_for_vol['close'].iloc[-1]
                    atr_percent = (atr_now / close_for_atr) * 100 if close_for_atr > 0 else 0.0
                    min_atr_pct = target_config_static.get('volatility_min_atr_percent',0.05)
                    max_atr_pct = target_config_static.get('volatility_max_atr_percent',1.5)

                    if atr_percent < min_atr_pct:
                        current_volatility_level = 1
                    elif atr_percent > max_atr_pct:
                        current_volatility_level = -1
                    else:
                        current_volatility_level = 0
                    current_volatility_status_text = f"波动率: ATR({atr_p_vol})={atr_now:.2f} ({atr_percent:.2f}%)"
                except (IndexError, KeyError):
                    pass  # 保持默认值

        # --- 核心模式的返回逻辑 ---
        if return_core_prediction_only: # 假设这一行是正确的缩进级别 (例如，在 try 块内)
            # 🎯 V11.0 "三军协同" - 第一步：基础模型静默模式
            # 定义基础模型列表（这些模型只提供信息，不发送信号）
            BASE_MODEL_NAMES = ['BTC_15m_UP', 'BTC_15m_DOWN', 'BTC_LSTM', 'BTC_30m_UP', 'BTC_30m_DOWN']
            is_base_model = any(base_name in target_name for base_name in BASE_MODEL_NAMES)

            if is_base_model:
                print(f"🔇 [V11.0 静默模式] 基础模型 '{target_name}' 完成预测，进入静默模式（不发送信号）")

            if not any_successful_model_prediction: # 相对于上面的 if 增加一级缩进
                # 错误情况：返回传统格式或精英模型格式
                if elite_only:
                    # 精英模型模式：返回精英模型OOF特征格式
                    elite_predictions = {}
                    for i, fold_idx in enumerate(elite_fold_indices):
                        elite_feature_name = f"oof_{target_name}_elite_fold{fold_idx}"
                        elite_predictions[elite_feature_name] = np.nan
                    elite_predictions["error"] = True
                    elite_predictions["internal_signal"] = final_signal_for_internal_state
                    return elite_predictions
                else:
                    # 传统模式：返回平均概率格式
                    core_info = { # 相对于 if not ... 增加一级缩进
                        "p_favorable": np.nan,
                        "p_up": np.nan,
                        "p_down": np.nan,
                        "internal_signal": final_signal_for_internal_state,
                        "error": True,
                        # 新增：上下文特征（错误情况下使用默认值）
                        "context_features": {
                            "atr_percent": np.nan,
                            "adx_value": np.nan,
                            "prediction_confidence": np.nan,
                            "rsi_value": np.nan,
                            "volume_ratio": np.nan,
                            "price_change_1p": np.nan
                        }
                    }
            else: # 这个 else 与 if not ... 对齐
                # 计算上下文特征
                context_features = {}

                # 1. 波动率特征：ATR百分比
                if not pd.isna(atr_percent):
                    context_features["atr_percent"] = float(atr_percent)
                else:
                    context_features["atr_percent"] = 0.0

                # 2. 趋势强度特征：ADX值
                if not pd.isna(latest_adx):
                    context_features["adx_value"] = float(latest_adx)
                else:
                    context_features["adx_value"] = 0.0

                # 3. 预测置信度：abs(p_favorable - 0.5)
                if not pd.isna(model_positive_class_prob):
                    context_features["prediction_confidence"] = abs(float(model_positive_class_prob) - 0.5)
                else:
                    context_features["prediction_confidence"] = 0.0

                # 4. 从最新特征中提取RSI值
                try:
                    if scaled_X_for_model is not None and hasattr(scaler, 'feature_names_in_'):
                        feature_names = list(scaler.feature_names_in_)
                        feature_values = scaled_X_for_model[0]

                        # 查找RSI特征
                        rsi_features = [i for i, name in enumerate(feature_names) if 'RSI' in name.upper()]
                        if rsi_features:
                            # 使用第一个RSI特征，需要反向缩放获得原始值
                            rsi_scaled = feature_values[rsi_features[0]]
                            # 简化处理：假设RSI在0-100范围内，缩放后的值需要反向处理
                            # 这里使用一个近似值，实际应该用scaler.inverse_transform
                            context_features["rsi_value"] = float(rsi_scaled * 50 + 50)  # 简化的反缩放
                        else:
                            context_features["rsi_value"] = 50.0  # RSI中性值
                    else:
                        context_features["rsi_value"] = 50.0
                except Exception as e_rsi:
                    context_features["rsi_value"] = 50.0

                # 5. 成交量比率（从最新K线数据中获取）
                try:
                    if df_klines_main is not None and not df_klines_main.empty and 'volume' in df_klines_main.columns:
                        recent_volumes = df_klines_main['volume'].tail(20)
                        current_volume = df_klines_main['volume'].iloc[-1]
                        avg_volume = recent_volumes.mean()
                        if avg_volume > 0:
                            context_features["volume_ratio"] = float(current_volume / avg_volume)
                        else:
                            context_features["volume_ratio"] = 1.0
                    else:
                        context_features["volume_ratio"] = 1.0
                except Exception as e_vol:
                    context_features["volume_ratio"] = 1.0

                # 6. 价格变化（1期）
                try:
                    if df_klines_main is not None and not df_klines_main.empty and 'close' in df_klines_main.columns:
                        if len(df_klines_main) >= 2:
                            current_close = df_klines_main['close'].iloc[-1]
                            prev_close = df_klines_main['close'].iloc[-2]
                            context_features["price_change_1p"] = float((current_close - prev_close) / prev_close)
                        else:
                            context_features["price_change_1p"] = 0.0
                    else:
                        context_features["price_change_1p"] = 0.0
                except Exception as e_price:
                    context_features["price_change_1p"] = 0.0

                # 🚀 新增：丰富化上下文特征（高阶特征、交互特征、聪明钱特征等）
                try:
                    from src.core.meta_model_input_enrichment import enrich_meta_model_context_features

                    enriched_context_features = enrich_meta_model_context_features(
                        df_klines_main, context_features, target_config_static, target_name
                    )

                    # 更新上下文特征
                    context_features = enriched_context_features

                    logger.info(f"run_prediction_cycle_for_target [{target_name}]: "
                               f"上下文特征丰富化完成，特征数: {len(context_features)}")

                except ImportError:
                    logger.warning(f"run_prediction_cycle_for_target [{target_name}]: 元模型输入丰富化模块不可用")
                except Exception as e:
                    logger.error(f"run_prediction_cycle_for_target [{target_name}]: 上下文特征丰富化失败: {e}")
                    logger.debug(traceback.format_exc())

                # 根据模式返回不同格式的数据
                if elite_only:
                    # 精英模型模式：返回精英模型OOF特征格式
                    elite_individual_predictions["error"] = False
                    elite_individual_predictions["internal_signal"] = final_signal_for_internal_state
                    # 添加上下文特征（用于调试和监控）
                    elite_individual_predictions["context_features"] = context_features
                    print(f"  ({target_name}): Elite predictions: {elite_individual_predictions}")
                    return elite_individual_predictions
                else:
                    # 传统模式：返回平均概率格式
                    core_info = { # 相对于 else 增加一级缩进
                        "p_favorable": model_positive_class_prob,
                        "p_up": avg_up_prob_value,
                        "p_down": avg_down_prob_value,
                        "internal_signal": final_signal_for_internal_state,
                        "error": False,
                        # 新增：丰富的上下文特征
                        "context_features": context_features,
                        # 保留原有的趋势和波动率信息（向后兼容）
                        "trend_signal": higher_tf_trend_signal,
                        "trend_strength": higher_tf_trend_strength,
                        "adx_value": latest_adx,
                        "pdi_value": latest_pdi,
                        "mdi_value": latest_mdi,
                        "ema_short": latest_ema_short,
                        "ema_long": latest_ema_long,
                        "volatility_level": current_volatility_level,
                        "atr_value": atr_now,
                        "atr_percent": atr_percent
                    }
                    return core_info

        # --- 完整模式的后续逻辑 (仍然在 try 块内) ---
        up_prob_display = f"{avg_up_prob_value:.2%}" if not pd.isna(avg_up_prob_value) else "N/A"
        down_prob_display = f"{avg_down_prob_value:.2%}" if not pd.isna(avg_down_prob_value) else "N/A"

        # === 步骤 4, 5, 6: 趋势、波动率、信号决策、过滤 (与您之前版本一致) ===
        # 动态配置获取 (应在过滤器和金额计算前，确保使用最新参数)
        dynamic_params_current_target = dynamic_config_manager.get_target_params(target_name, target_config_static)
        is_target_signal_enabled_dynamically = dynamic_params_current_target.get("enabled", True)
        # is_master_signal_sending_enabled 将在元模型层面或更高层级检查，这里暂时不使用

        # === 🎯 集中式过滤逻辑 ===
        # 使用新的 PredictionFilter 类来处理所有过滤逻辑
        if PredictionFilter is not None and create_filter_input_from_prediction_data is not None:
            try:
                # 使用辅助函数创建过滤器输入
                filter_input = create_filter_input_from_prediction_data(
                    raw_signal=raw_lgbm_signal_internal,
                    up_probability=avg_up_prob_value if not pd.isna(avg_up_prob_value) else 0.0,
                    down_probability=avg_down_prob_value if not pd.isna(avg_down_prob_value) else 0.0,
                    target_variable_type=target_variable_type,
                    trend_signal=higher_tf_trend_signal,
                    trend_strength=higher_tf_trend_strength,
                    adx_value=latest_adx,
                    pdi_value=latest_pdi,
                    mdi_value=latest_mdi,
                    ema_short=latest_ema_short,
                    ema_long=latest_ema_long,
                    volatility_level=current_volatility_level,
                    atr_value=atr_now,
                    atr_percent=atr_percent,
                    signal_threshold=sig_thresh_use,
                    target_config=target_config_static,
                    target_name=target_name
                )

                # 应用过滤器
                prediction_filter = PredictionFilter(logger)
                filter_result = prediction_filter.apply_filters(filter_input)

                # 使用过滤结果
                final_signal_for_internal_state = filter_result.final_signal
                filter_reason_details_for_gui = filter_result.filter_description
                sig_thresh_use = filter_result.adjusted_threshold

                # 如果过滤器成功，还可以使用其生成的详细信息
                if filter_result.success and not return_core_prediction_only:
                    # 将过滤器的详细信息添加到 details 中
                    if filter_result.threshold_adjustments:
                        # 使用正确的配置键名，避免与目标变量创建的dynamic_threshold_base冲突
                        base_threshold = target_config_static.get('signal_dynamic_threshold_base', filter_input.signal_threshold)
                        details += f"\n动态阈P: {filter_result.adjusted_threshold:.3f} (基:{base_threshold:.3f},调:{','.join(filter_result.threshold_adjustments)})"
                    else:
                        details += f"\n固定阈P: {filter_result.adjusted_threshold:.3f}"

                logger.info(f"集中式过滤器应用完成: {raw_lgbm_signal_internal} -> {final_signal_for_internal_state}")

            except Exception as e:
                logger.error(f"集中式过滤器执行失败: {e}，回退到传统过滤逻辑")
                # 回退到传统过滤逻辑
                final_signal_for_internal_state = raw_lgbm_signal_internal
                filter_reason_details_for_gui = ""
        else:
            # 传统过滤逻辑（作为备用）
            logger.warning("集中式过滤器不可用，使用传统过滤逻辑")
            final_signal_for_internal_state = raw_lgbm_signal_internal
            filter_reason_details_for_gui = ""

        # === 详细的趋势和波动率分析 (仅在非核心模式下) ===
        # 在核心模式下，基本的趋势和波动率指标已经在前面计算过了
        if not return_core_prediction_only:
            # 添加详细的状态描述和日志
            trend_desc_brief_gui = f"ADX@{trend_tf_str_cfg}" if enable_trend_detection_cfg else "趋势检测已禁用"

            # 更新趋势状态文本
            if latest_adx > 0:
                strength_label = "强劲" if higher_tf_trend_strength == 1 else "一般"
                if higher_tf_trend_signal == 1:
                    current_trend_status_text = f"趋势: {trend_desc_brief_gui} {strength_label}上升"
                elif higher_tf_trend_signal == -1:
                    current_trend_status_text = f"趋势: {trend_desc_brief_gui} {strength_label}下降"
                else:
                    current_trend_status_text = f"趋势: {trend_desc_brief_gui} 盘整/不明"

                # 添加详细信息到details
                details += f"\nADX@{trend_tf_str_cfg}: {latest_adx:.1f} (PDI:{latest_pdi:.1f}, MDI:{latest_mdi:.1f})"
            elif latest_ema_short > 0 and latest_ema_long > 0:
                details += f"\nEMA@{trend_tf_str_cfg}: {latest_ema_short:.2f} vs {latest_ema_long:.2f}"

            # 添加波动率详细信息
            if atr_now > 0:
                details += f"\nATR@{vol_tf_cfg}: {atr_now:.2f} ({atr_percent:.2f}%)"


        # 动态阈值调整 (如果启用)
        enable_dyn_thresh_cfg = target_config_static.get('enable_dynamic_threshold', False)
        if enable_dyn_thresh_cfg:
            # 使用正确的配置键名，避免与目标变量创建的dynamic_threshold_base冲突
            base_thresh_dyn = target_config_static.get('signal_dynamic_threshold_base', sig_thresh_use)
            trend_adj_dyn = target_config_static.get('dynamic_threshold_trend_adjust',0.03)
            vol_adj_dyn = target_config_static.get('dynamic_threshold_volatility_adjust',0.02)
            adj_thresh_calc = base_thresh_dyn; adj_reasons_gui = []
            # 动态调整阈值是针对“预测为正类”的概率，所以对于DOWN_ONLY模型，如果它预测的是“下跌”，
            # 那么趋势和波动率对这个“下跌”的确认/否认也应该相应调整。
            # 这里的逻辑假设 sig_thresh_use 总是用于“主要方向”的概率。
            # 如果是UP信号(来自UP_ONLY或BOTH)，且趋势是强劲上升，则降低阈值。
            # 如果是DOWN信号(来自DOWN_ONLY或BOTH)，且趋势是强劲下降，则降低阈值。
            # 反之，如果信号与强趋势相反，则增加阈值。
            # 这是一个简化的例子，你可能需要更细致地处理：

            current_signal_direction_for_dyn_thresh = "NEUTRAL"
            if "UP" in final_signal_for_internal_state: current_signal_direction_for_dyn_thresh = "UP"
            elif "DOWN" in final_signal_for_internal_state: current_signal_direction_for_dyn_thresh = "DOWN"

            if higher_tf_trend_strength == 1: # 强趋势存在
                if higher_tf_trend_signal == 1 and current_signal_direction_for_dyn_thresh == "UP": # 强升，且当前信号是UP
                    adj_thresh_calc -= trend_adj_dyn; adj_reasons_gui.append(f"强升确认-{trend_adj_dyn:.3f}")
                elif higher_tf_trend_signal == -1 and current_signal_direction_for_dyn_thresh == "DOWN": # 强降，且当前信号是DOWN
                    adj_thresh_calc -= trend_adj_dyn; adj_reasons_gui.append(f"强降确认-{trend_adj_dyn:.3f}")
                elif higher_tf_trend_signal == 1 and current_signal_direction_for_dyn_thresh == "DOWN": # 强升，但当前信号是DOWN (逆势)
                    adj_thresh_calc += trend_adj_dyn; adj_reasons_gui.append(f"强升过滤-{trend_adj_dyn:.3f}")
                elif higher_tf_trend_signal == -1 and current_signal_direction_for_dyn_thresh == "UP": # 强降，但当前信号是UP (逆势)
                    adj_thresh_calc += trend_adj_dyn; adj_reasons_gui.append(f"强降过滤-{trend_adj_dyn:.3f}")

            if current_volatility_level != 0 : # 波动率异常（过高或过低）
                adj_thresh_calc += vol_adj_dyn # 增加阈值，更谨慎
                adj_reasons_gui.append(f"{('低' if current_volatility_level==1 else '高')}波+{vol_adj_dyn:.3f}")

            sig_thresh_use = np.clip(adj_thresh_calc, 0.51, target_config_static.get('dynamic_threshold_max_clip', 0.95))
            if not return_core_prediction_only: details += f"\n动态阈P: {sig_thresh_use:.3f} (基:{base_thresh_dyn:.3f},调:{','.join(adj_reasons_gui) if adj_reasons_gui else '无'})"
        elif not return_core_prediction_only: details += f"\n固定阈P: {sig_thresh_use:.3f}" # 使用静态配置的阈值

        # 根据调整后的 sig_thresh_use 重新判断初步信号 (如果启用了动态阈值)
        # 注意：这一步很重要，因为 sig_thresh_use 可能已经变了
        if enable_dyn_thresh_cfg:
            if target_variable_type == "UP_ONLY" and avg_up_prob_value > sig_thresh_use: final_signal_for_internal_state = "UP"
            elif target_variable_type == "DOWN_ONLY" and avg_down_prob_value > sig_thresh_use: final_signal_for_internal_state = "DOWN"
            elif target_variable_type == "BOTH":
                if avg_up_prob_value > sig_thresh_use: final_signal_for_internal_state = "UP"
                elif avg_down_prob_value > sig_thresh_use: final_signal_for_internal_state = "DOWN"
                else: final_signal_for_internal_state = "Neutral" # 如果动态调整后都不满足了
            else: # 如果之前是UP/DOWN但动态调整后不满足了
                final_signal_for_internal_state = "Neutral"


        # 应用波动率过滤器 (如果启用且初步信号不是Neutral)
        if enable_volatility_filter_cfg and current_volatility_level != 0 and final_signal_for_internal_state not in ["Neutral", "Error_Initialization"]:
            filter_reason_details_for_gui += f"\n过滤({('低' if current_volatility_level==1 else '高')}波)!"
            final_signal_for_internal_state = "Neutral_Filtered_Volatility"

        # 应用趋势过滤器 (如果启用，且未被波动率过滤，且有初步信号)
        current_signal_after_vol = final_signal_for_internal_state # 保存波动率过滤后的状态
        if enable_trend_detection_cfg and target_config_static.get('trend_filter_strategy','none') != 'none' and \
           not ("Filtered_Volatility" in current_signal_after_vol or "Error" in current_signal_after_vol or "Neutral" == current_signal_after_vol) :
            trend_action_desc_gui = ""; is_strong_trend = (higher_tf_trend_strength == 1)
            trend_strategy = target_config_static.get('trend_filter_strategy','filter_only')
            # sig_dir_for_trend 现在基于 current_signal_after_vol
            sig_dir_for_trend = "UP" if "UP" in current_signal_after_vol else ("DOWN" if "DOWN" in current_signal_after_vol else "Neutral")

            if higher_tf_trend_signal != 0 and sig_dir_for_trend != "Neutral": # 有趋势且有信号
                if is_strong_trend and trend_strategy in ['filter_only', 'chase_trend']: # 强趋势下的过滤或追逐逻辑
                    if (sig_dir_for_trend == "UP" and higher_tf_trend_signal == -1) or \
                       (sig_dir_for_trend == "DOWN" and higher_tf_trend_signal == 1): # 信号与强趋势相反
                        final_signal_for_internal_state = "Neutral_Filtered_Trend"
                        trend_action_desc_gui = f"趋势:强{('降' if higher_tf_trend_signal == -1 else '升')}过滤{sig_dir_for_trend}"
                    # else: 信号与强趋势同向，不改变信号
                    #    trend_action_desc_gui = f"趋势:强{('升' if higher_tf_trend_signal == 1 else '降')}确认{sig_dir_for_trend}"
            # 追逐趋势的逻辑 (当原始信号是 Neutral 时)
            elif sig_dir_for_trend == "Neutral" and trend_strategy == 'chase_trend' and is_strong_trend:
                chase_boost = target_config_static.get('trend_chase_confidence_boost', 0.05)
                # 对于UP_ONLY模型，我们关心avg_up_prob_value
                # 对于DOWN_ONLY模型，我们关心avg_down_prob_value
                if target_variable_type == "UP_ONLY" or target_variable_type == "BOTH":
                    if higher_tf_trend_signal == 1 and avg_up_prob_value > (0.5 + chase_boost): # 强升，且UP概率尚可
                        final_signal_for_internal_state = "UP_Chasing"; trend_action_desc_gui = "趋势:强升追多"
                if target_variable_type == "DOWN_ONLY" or target_variable_type == "BOTH": # 注意这里是 if 不是 elif，因为BOTH模型可能两个都看
                    if higher_tf_trend_signal == -1 and avg_down_prob_value > (0.5 + chase_boost): # 强降，且DOWN概率尚可
                        final_signal_for_internal_state = "DOWN_Chasing"; trend_action_desc_gui = "趋势:强降追空"
            if trend_action_desc_gui: filter_reason_details_for_gui += f"\n{trend_action_desc_gui}"


        # --- END OF FILTERING LOGIC PLACEHOLDER ---





        # 根据过滤后的 final_signal_for_internal_state，重新确定 actual_signal_for_sound_and_simulator, prediction_label, prediction_color
        # (您提供的这部分逻辑，确保它在过滤器之后)
        if "UP" in final_signal_for_internal_state and not final_signal_for_internal_state.startswith("Error_"):
            actual_signal_for_sound_and_simulator = "UP"; prediction_label = "上涨 看多" + (" (追涨)" if "_Chasing" in final_signal_for_internal_state else ""); prediction_color = config.UP_COLOR
        elif "DOWN" in final_signal_for_internal_state and not final_signal_for_internal_state.startswith("Error_"):
            actual_signal_for_sound_and_simulator = "DOWN"; prediction_label = "下跌 看空" + (" (追跌)" if "_Chasing" in final_signal_for_internal_state else ""); prediction_color = config.DOWN_COLOR
        elif "Filtered" in final_signal_for_internal_state:
            actual_signal_for_sound_and_simulator = None; prediction_label = f"中性 ({filter_reason_details_for_gui.strip() if filter_reason_details_for_gui else '已过滤'})"; prediction_color = config.NEUTRAL_COLOR
        else:
            actual_signal_for_sound_and_simulator = None; prediction_label = "中性 观望" if final_signal_for_internal_state == "Neutral" else f"错误 ({final_signal_for_internal_state})"; prediction_color = config.NEUTRAL_COLOR if final_signal_for_internal_state == "Neutral" else config.ERROR_COLOR
        
        # --- 构建 details 字符串 ---
        details_model_gui = f"(模型: {model_type_loaded_str})"
        if model_meta: # 确保 model_meta 已加载
            config_suffix_parts_meta = model_meta.get("model_suffix_parts_used_for_run0_artifacts", [])
            suffix_display_gui = ""
            if isinstance(config_suffix_parts_meta, list) and config_suffix_parts_meta: 
                actual_parts_to_join = [s for s in config_suffix_parts_meta if s and isinstance(s, str)] 
                if actual_parts_to_join: suffix_display_gui = f" (Train: {', '.join(actual_parts_to_join)})"
            elif isinstance(config_suffix_parts_meta, str) and config_suffix_parts_meta.strip():
                 suffix_display_gui = f" (Train: {config_suffix_parts_meta.strip()})"
            if suffix_display_gui: details_model_gui += suffix_display_gui
        # 🎯 修复：安全地获取过滤原因详情
        filter_details = filter_reason_details_for_gui if 'filter_reason_details_for_gui' in locals() and filter_reason_details_for_gui else ""
        details = details_model_gui + filter_details

        # --- 根据预测的最终结果，更新 status_msg_for_gui 和 status_level_final_gui ---
        # (这里的逻辑应该在 try 块的末尾，但在 except 之前，并且在 !return_core_prediction_only 条件内)
        # (因为 return_core_prediction_only 为 True 时，这些详细的 status_msg 不会直接用于该基础模型的GUI状态栏)
        # (但为了简化，我们先在这里确定，如果函数继续执行到信号发送和GUI更新，它们会被使用)
        if final_signal_for_internal_state.startswith("Error_"):
            status_msg_for_gui = f"预测 '{target_name}' 失败 ({final_signal_for_internal_state})"
            status_level_final_gui = "error"
        elif any_successful_model_prediction:
            status_msg_for_gui = f"预测 '{target_name}' 完成"
            if actual_signal_for_sound_and_simulator == "UP": status_msg_for_gui += " (看多)"; status_level_final_gui = config.UP_COLOR
            elif actual_signal_for_sound_and_simulator == "DOWN": status_msg_for_gui += " (看空)"; status_level_final_gui = config.DOWN_COLOR
            elif "Filtered" in final_signal_for_internal_state: status_msg_for_gui += " (已过滤)"; status_level_final_gui = "warning"
            elif final_signal_for_internal_state == "Neutral": status_msg_for_gui += " (中性)"; status_level_final_gui = "success" 
            else: status_level_final_gui = "info"
        else:
            status_msg_for_gui = f"预测 '{target_name}' 未完成/结果未知"
            status_level_final_gui = "warning"
        
    # --- try 块到此结束，下面是改进的 except 块 ---
    except (ModelLoadError, DataValidationError, FeatureEngineeringError) as e_pred_specific:
        # 处理特定的预测相关错误
        logger = logging.getLogger(__name__)
        any_successful_model_prediction = False
        final_signal_for_internal_state = f"Error_Core_{type(e_pred_specific).__name__}"
        model_positive_class_prob = np.nan; avg_up_prob_value = np.nan; avg_down_prob_value = np.nan
        actual_signal_for_sound_and_simulator = None

        # 记录详细错误信息
        error_context = {
            'target_name': target_name,
            'interval': interval_main,
            'step': 'core_prediction_flow',
            'return_core_only': return_core_prediction_only
        }

        # 添加异常特有的上下文
        if hasattr(e_pred_specific, 'context'):
            error_context.update(e_pred_specific.context)

        log_prediction_error(logger, e_pred_specific, 'run_prediction_cycle_for_target',
                           target_name, error_context)

        prediction_label = f"错误 ({type(e_pred_specific).__name__})"
        details = str(e_pred_specific)[:150]
        prediction_color = config.ERROR_COLOR

        if not return_core_prediction_only:
            status_msg_for_gui = f"预测 '{target_name}' 核心流程错误: {type(e_pred_specific).__name__}"
            status_level_final_gui = "error"
            # 发布错误事件
            error_display_text_main = (f"预测 '{target_name}' 发生错误:\n"
                                       f"时间: {datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime('%H:%M:%S %Z')}\n"
                                       f"错误类型: {type(e_pred_specific).__name__}\n"
                                       f"信息: {details}\n"
                                       f"上下文: {error_context}")
            publish_prediction_result(target_name, error_display_text_main, [], 0.0, None)
            publish_status_update(status_msg_for_gui, status_level_final_gui)

        if return_core_prediction_only:
            error_result = create_error_result(type(e_pred_specific), str(e_pred_specific),
                                             error_context, e_pred_specific.original_exception)
            return {
                "p_favorable": np.nan,
                "p_up": np.nan,
                "p_down": np.nan,
                "internal_signal": final_signal_for_internal_state,
                "error": True,
                "error_details": error_result.to_dict()
            }

    except Exception as e_pred_core:
        # 处理未预期的错误
        logger = logging.getLogger(__name__)
        any_successful_model_prediction = False
        final_signal_for_internal_state = f"Error_Core_{type(e_pred_core).__name__}"
        model_positive_class_prob = np.nan; avg_up_prob_value = np.nan; avg_down_prob_value = np.nan
        actual_signal_for_sound_and_simulator = None

        # 创建未预期错误的上下文
        error_context = {
            'target_name': target_name,
            'interval': interval_main,
            'step': 'core_prediction_flow',
            'return_core_only': return_core_prediction_only,
            'unexpected_error': True
        }

        # 创建包装错误
        wrapped_error = PredictionExecutionError(
            f"核心预测流程中发生未预期错误: {str(e_pred_core)}",
            context=error_context,
            original_exception=e_pred_core
        )

        log_prediction_error(logger, wrapped_error, 'run_prediction_cycle_for_target',
                           target_name, error_context)

        prediction_label = f"错误 ({type(e_pred_core).__name__})"
        details = str(e_pred_core)[:150]
        prediction_color = config.ERROR_COLOR

        if not return_core_prediction_only:
            status_msg_for_gui = f"预测 '{target_name}' 核心流程错误: {type(e_pred_core).__name__}"
            status_level_final_gui = "error"
            # 发布错误事件
            error_display_text_main = (f"预测 '{target_name}' 发生未预期错误:\n"
                                       f"时间: {datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime('%H:%M:%S %Z')}\n"
                                       f"错误类型: {type(e_pred_core).__name__}\n"
                                       f"信息: {details}\n"
                                       f"上下文: {error_context}")
            publish_prediction_result(target_name, error_display_text_main, [], 0.0, None)
            publish_status_update(status_msg_for_gui, status_level_final_gui)

        if return_core_prediction_only:
            error_result = create_error_result(PredictionExecutionError, str(wrapped_error),
                                             error_context, e_pred_core)
            return {
                "p_favorable": np.nan,
                "p_up": np.nan,
                "p_down": np.nan,
                "internal_signal": final_signal_for_internal_state,
                "error": True,
                "error_details": error_result.to_dict()
            }
    
    # --- 信号发送决策逻辑 (仅在 not return_core_prediction_only 且核心预测成功时执行) ---
    # 注意：any_successful_model_prediction 的值取决于 try 块是否成功执行到最后或中途因异常退出
    if not return_core_prediction_only and any_successful_model_prediction:
        # 🎯 V11.0 "三军协同" - 第一步：基础模型信号阻止
        # 定义基础模型列表（这些模型绝对不能发送信号）
        BASE_MODEL_NAMES = ['BTC_15m_UP', 'BTC_15m_DOWN', 'BTC_LSTM', 'BTC_30m_UP', 'BTC_30m_DOWN']
        is_base_model = any(base_name in target_name for base_name in BASE_MODEL_NAMES)

        if is_base_model:
            print(f"🚫 [V11.0 信号阻止] 基础模型 '{target_name}' 被阻止发送信号！只有元模型才能发送信号。")
            print(f"🎯 [V11.0 架构] 基础模型仅作为信息提供者，决策权归元模型所有。")
            # 直接返回，不执行任何信号发送逻辑
            return any_successful_model_prediction, final_signal_for_internal_state
        # 初始化此块内作用域的变量
        should_send_signal_this_time = False 
        trade_amount_to_send_float = 0.0   

        # 获取最新的动态配置
        dynamic_params_current_target_final = dynamic_config_manager.get_target_params(target_name, target_config_static)
        is_target_signal_enabled_dynamically_final = dynamic_params_current_target_final.get("enabled", True)
        is_master_signal_sending_enabled_final = dynamic_config_manager.get_global_param("master_signal_sending_enabled", True)

        if actual_signal_for_sound_and_simulator and \
           is_target_signal_enabled_dynamically_final and \
           is_master_signal_sending_enabled_final:

            prob_for_amount_calc = 0.0
            # avg_up_prob_value 和 avg_down_prob_value 应在 try 块内被正确计算和赋值
            if actual_signal_for_sound_and_simulator == "UP" and not pd.isna(avg_up_prob_value):
                prob_for_amount_calc = avg_up_prob_value
            elif actual_signal_for_sound_and_simulator == "DOWN" and not pd.isna(avg_down_prob_value):
                prob_for_amount_calc = avg_down_prob_value
            
            if prob_for_amount_calc > 0 or target_config_static.get('trade_amount_strategy') == "fixed":
                trade_amount_to_send_float = calculate_trade_amount_from_strategy(
                    target_config_static,
                    actual_signal_for_sound_and_simulator,
                    prob_for_amount_calc
                )

                # 🎯 记录交易开仓到动态胜率跟踪器
                try:
                    from .dynamic_win_rate_tracker import get_dynamic_win_rate_tracker
                    win_rate_tracker = get_dynamic_win_rate_tracker()

                    # 生成交易ID
                    trade_id = f"{target_name}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

                    # 记录交易开仓
                    win_rate_tracker.record_trade_entry(
                        trade_id=trade_id,
                        direction=actual_signal_for_sound_and_simulator,
                        amount=trade_amount_to_send_float,
                        target_name=target_name
                    )

                    print(f"[{target_name}] 已记录交易开仓: {trade_id} ({actual_signal_for_sound_and_simulator}, ${trade_amount_to_send_float:.2f})")

                except Exception as e_trade_record:
                    print(f"[{target_name}] 记录交易开仓失败: {e_trade_record}")
                    # 不影响主流程，继续执行
            
            if trade_amount_to_send_float > 0:
                last_sent_t = global_prediction_state_manager.get_last_signal_sent_time(target_name)
                last_sent_sig_type = global_prediction_state_manager.get_last_signal_type_sent(target_name)
                cooldown_seconds_cfg = getattr(config, 'SIGNAL_SEND_COOLDOWN_SECONDS', SIGNAL_SEND_COOLDOWN_SECONDS)

                if last_sent_sig_type != actual_signal_for_sound_and_simulator or \
                   (time.time() - last_sent_t) >= cooldown_seconds_cfg :
                    should_send_signal_this_time = True
            
            # 🚨 V11.0 关键修复：基础模型在静默模式下不播放声音
            if should_send_signal_this_time and not return_core_prediction_only:
                # play_signal_alert_sound will internally use global_prediction_state_manager
                play_signal_alert_sound(actual_signal_for_sound_and_simulator)
        
        # --- 记录预测上下文用于失败案例分析 ---
        if should_send_signal_this_time and actual_signal_for_sound_and_simulator:
            # 获取当前特征名称和值
            feature_names_current = []
            feature_values_current = []
            
            if scaled_X_for_model is not None and hasattr(scaler, 'feature_names_in_'):
                feature_names_current = list(scaler.feature_names_in_)
                feature_values_current = scaled_X_for_model[0].tolist()
            
            # 构建信号数据
            signal_data = {
                'signal_type': actual_signal_for_sound_and_simulator,
                'signal_strength': model_positive_class_prob,
                'avg_up_prob': avg_up_prob_value,
                'avg_down_prob': avg_down_prob_value,
                'model_positive_class_prob': model_positive_class_prob,
                'signal_threshold_used': sig_thresh_use,
                'planned_amount': trade_amount_to_send_float,
                'amount_calculation_method': target_config_static.get('trade_amount_strategy', 'unknown')
            }
            
            # 构建市场数据
            market_data = {
                'current_price': get_real_time_price(binance_client, symbol_override=symbol_to_use),
                'last_kline_close': last_kline_close_main
            }
            
            # 构建模型数据
            model_data = {
                'feature_names': feature_names_current,
                'feature_values': feature_values_current,
                'model_type': model_type_loaded_str,
                'ensemble_size': len(loaded_ensemble_models)
            }
            
            # 构建过滤器数据
            filter_data = {
                'trend_signal': higher_tf_trend_signal,
                'trend_strength': higher_tf_trend_strength,
                'trend_status_text': current_trend_status_text,
                'adx_value': getattr(locals(), 'latest_adx', None),
                'pdi_value': getattr(locals(), 'latest_pdi', None),
                'mdi_value': getattr(locals(), 'latest_mdi', None),
                'volatility_level': current_volatility_level,
                'volatility_status_text': current_volatility_status_text,
                'atr_value': getattr(locals(), 'atr_now', None),
                'atr_percent': getattr(locals(), 'atr_pct', None),
                'dynamic_threshold_adjustments': getattr(locals(), 'adj_reasons_gui', []),
                'filter_reasons': filter_reason_details_for_gui
            }
            
            # 记录预测上下文
            log_prediction_context(
                target_name=target_name,
                symbol=symbol_to_use,
                signal_data=signal_data,
                market_data=market_data,
                model_data=model_data,
                filter_data=filter_data
            )

        # --- 实际的信号发送操作 ---
        if should_send_signal_this_time: # 这个 if 块需要和上面的 if 块在同一缩进级别
            # 🔒 交易状态检查 - 基础模型信号发送前的最终检查
            try:
                from .trade_state_manager import enhanced_trade_state_manager as trade_state_manager

                # 构建信号信息用于过滤检查
                signal_info = {
                    'signal_type': actual_signal_for_sound_and_simulator,
                    'amount': trade_amount_to_send_float,
                    'symbol': symbol_to_use,
                    'source': 'base_model',
                    'target_name': target_name,
                    'trigger_time': datetime.now().isoformat()
                }

                # 检查是否应该过滤此信号
                if trade_state_manager.filter_signal(signal_info):
                    print(f"🔒 [交易限制] 基础模型信号被过滤: {actual_signal_for_sound_and_simulator}")
                    should_send_signal_this_time = False

            except Exception as e:
                print(f"⚠️ [交易状态检查] 异常: {e}")
                # 如果交易状态管理器出错，为了安全起见，阻止信号发送
                should_send_signal_this_time = False

        # 重新检查发送条件（可能被交易状态管理器修改）
        if should_send_signal_this_time:
            target_name_for_comm = f"{target_name}_{symbol_to_use}" # symbol_to_use 需要在函数顶部定义
            print(f"预测系统 [{target_name_for_comm}]: 准备发送信号. 类型: {actual_signal_for_sound_and_simulator}, 金额: {trade_amount_to_send_float:.2f}")

            # === V2.0日志系统：构建完整决策上下文 ===
            try:
                import json
                from ..core.unified_trade_logger import get_unified_trade_logger

                # 获取当前价格
                current_price = get_real_time_price(binance_client, symbol_override=symbol_to_use)

                # 构建决策上下文数据
                context_data = {}

                # 1. 捕获模型概率信息（核心！）
                context_data['entry_signal_probability'] = model_positive_class_prob
                context_data['entry_neutral_probability'] = 1.0 - avg_up_prob_value - avg_down_prob_value if (avg_up_prob_value + avg_down_prob_value) <= 1.0 else 0.0
                context_data['entry_opposite_probability'] = avg_down_prob_value if actual_signal_for_sound_and_simulator == "UP" else avg_up_prob_value

                # 2. V2.0核心：记录4个具体模型概率（用户特别要求！）
                # 上涨模型、下跌模型、LSTM模型、元模型
                four_models_probabilities = {}

                # 当前模型的概率（可能是UP模型、DOWN模型或LSTM模型）
                current_model_type = target_config_static.get('target_variable_type', 'UNKNOWN')
                model_name = target_config_static.get('name', target_name)

                if model_type == 'LSTM':
                    # LSTM模型概率
                    four_models_probabilities['LSTM_model_up_prob'] = float(avg_up_prob_value)
                    four_models_probabilities['LSTM_model_down_prob'] = float(avg_down_prob_value)
                elif 'UP' in model_name.upper():
                    # 上涨模型概率
                    four_models_probabilities['UP_model_up_prob'] = float(avg_up_prob_value)
                    four_models_probabilities['UP_model_down_prob'] = float(avg_down_prob_value)
                elif 'DOWN' in model_name.upper():
                    # 下跌模型概率
                    four_models_probabilities['DOWN_model_up_prob'] = float(avg_up_prob_value)
                    four_models_probabilities['DOWN_model_down_prob'] = float(avg_down_prob_value)
                else:
                    # 其他基础模型
                    four_models_probabilities[f'{model_name}_up_prob'] = float(avg_up_prob_value)
                    four_models_probabilities[f'{model_name}_down_prob'] = float(avg_down_prob_value)

                context_data['individual_model_probabilities'] = json.dumps(four_models_probabilities)

                # 3. 捕获市场状态信息
                context_data['entry_market_regime'] = "normal"  # 可以后续集成MarketStateAnalyzer
                context_data['entry_atr_percent'] = atr_percent
                context_data['entry_adx_value'] = latest_adx

                # 4. 捕获关键特征值快照
                top_features_values = {}
                if 'feature_names_current' in locals() and 'feature_values_current' in locals() and feature_names_current:
                    # 取前10个最重要的特征
                    for i, (name, value) in enumerate(zip(feature_names_current[:10], feature_values_current[:10])):
                        top_features_values[name] = float(value)
                context_data['entry_top_features'] = json.dumps(top_features_values)

                # 5. 捕获元模型输入（如果有）
                meta_model_inputs = {
                    'avg_up_prob': float(avg_up_prob_value),
                    'avg_down_prob': float(avg_down_prob_value),
                    'signal_threshold': float(sig_thresh_use),
                    'trend_signal': int(higher_tf_trend_signal),
                    'volatility_level': int(current_volatility_level)
                }
                context_data['meta_model_inputs'] = json.dumps(meta_model_inputs)

                # === V2.0日志系统：记录交易开仓 ===
                trade_logger = get_unified_trade_logger(base_log_dir="trading_logs_unified", auto_start=True)

                trade_id = trade_logger.record_trade_entry(
                    target_name=target_name,
                    symbol=symbol_to_use,
                    direction=actual_signal_for_sound_and_simulator,
                    entry_price=current_price,
                    amount=trade_amount_to_send_float,
                    payout_ratio=0.85,  # 可以从配置中获取
                    context_data=context_data  # <--- 传递完整的决策上下文！
                )

                print(f"[V2.0日志系统] 交易开仓已记录: {trade_id}")
                print(f"[V2.0日志系统] 记录了 {len(individual_model_probas)} 个模型的具体概率")

            except Exception as e_trade_logger:
                print(f"!!! [V2.0日志系统] 记录交易开仓失败: {e_trade_logger}")
                # 不影响主流程，继续执行

            # 🚨 V11.0 关键修复：基础模型在静默模式下不发送信号
            if SEND_SIGNALS_TO_SIMULATOR and not return_core_prediction_only:
                _notify_simulator(
                    signal_type=actual_signal_for_sound_and_simulator,
                    target_name_origin=f"{target_name_for_comm}_ToSim",
                    amount=trade_amount_to_send_float,
                    symbol_for_sim=symbol_to_use,
                    sim_url_param=simulator_actual_url
                )

            # 🚨 V11.0 关键修复：基础模型在静默模式下不发送命令服务器信号
            SEND_TO_COMMAND_SERVER_ENABLED = getattr(config, 'SEND_TO_COMMAND_SERVER_ENABLED', True)
            if SEND_TO_COMMAND_SERVER_ENABLED and not return_core_prediction_only:
                payload_cs = {
                    "signal_type": actual_signal_for_sound_and_simulator,
                    "amount": trade_amount_to_send_float,
                    "symbol": symbol_to_use,
                    "target_name": f"{target_name_for_comm}_ToReal"
                }
                try:
                    COMMAND_SERVER_URL_GLOBAL = getattr(config, 'COMMAND_SERVER_URL', "http://127.0.0.1:8080/internal_signal")
                    requests.post(COMMAND_SERVER_URL_GLOBAL, json=payload_cs, timeout=3).raise_for_status()
                    print(f"预测系统 [{symbol_to_use} via {target_name_for_comm}]: 信号成功发送至 CommandServer ({COMMAND_SERVER_URL_GLOBAL})...")
                except Exception as e_cs_send:
                    print(f"!!! 预测系统: 发送信号至 CommandServer ({COMMAND_SERVER_URL_GLOBAL}) 失败: {e_cs_send}")

            global_prediction_state_manager.update_last_signal_sent_time(target_name, time.time())
            global_prediction_state_manager.update_last_signal_type_sent(target_name, actual_signal_for_sound_and_simulator)
            # dynamic_params_current_target_final 变量名可能与上面获取动态配置的变量冲突，确保使用正确的那个
            if dynamic_params_current_target_final.get('enable_initial_conservative_betting', False) and \
               target_config_static.get('trade_amount_strategy') == "kelly_config":
                global_prediction_state_manager.increment_strategy_execution_counter(target_name)

        # --- GUI 更新逻辑 (在信号发送之后，并且仍然在 if not return_core_prediction_only and any_successful_model_prediction 条件内) ---
        # 再次调整 status_msg_for_gui 以反映是否因动态配置而未发送 (如果信号发送决策逻辑没有发送)
        if any_successful_model_prediction and \
           actual_signal_for_sound_and_simulator is not None and \
           not (is_target_signal_enabled_dynamically_final and is_master_signal_sending_enabled_final) and \
           not should_send_signal_this_time: # 添加这个条件，确保是由于开关导致没发送，而不是冷却等
            status_msg_for_gui += " (信号发送被动态禁用)" 
            if status_level_final_gui in ["success", config.UP_COLOR, config.DOWN_COLOR]:
                status_level_final_gui = "info" 
        
        # 构建最终的详细文本显示在 ScrolledText 中
        pred_time_gui_str = datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime("%H:%M:%S %Z")
        current_price_val_gui = get_real_time_price(binance_client, symbol_override=symbol_to_use) # symbol_to_use
        price_str_gui = f"${current_price_val_gui:.2f}" if current_price_val_gui is not None else (f"${last_kline_close_main:.2f}(K)" if last_kline_close_main is not None else "$----.--") # last_kline_close_main
        
        result_txt_parts_for_gui = [
            f"预测时间: {pred_time_gui_str}",
            f"当前 {symbol_to_use} 价格: {price_str_gui}",
            current_trend_status_text, 
            current_volatility_status_text 
        ]

        if not (up_prob_display == "N/A" and down_prob_display == "N/A"): # up_prob_display, down_prob_display
            result_txt_parts_for_gui.extend([
                f"\n预测信号: {prediction_label}", 
                f" 模型 P(涨): {up_prob_display}",
                f" 模型 P(跌): {down_prob_display}"
            ])
            if should_send_signal_this_time and trade_amount_to_send_float > 0:
                result_txt_parts_for_gui.append(f" 发送金额: {trade_amount_to_send_float:.2f}")
        else:
            result_txt_parts_for_gui.append(f"\n预测信号: {prediction_label}")

        if details and details.strip(): # details
            result_txt_parts_for_gui.append("\n" + details.strip())
        
        result_text_final_for_gui = "\n".join(filter(None, result_txt_parts_for_gui))

        # 发布预测结果和状态更新事件
        publish_prediction_result(
            target_name=target_name,
            prediction_label=prediction_label,
            probabilities=[up_prob_float, down_prob_float] if up_prob_float != "N/A" and down_prob_float != "N/A" else [],
            confidence=0.0,  # 可以根据需要计算置信度
            signal_sent=actual_signal_for_sound_and_simulator
        )
        publish_status_update(status_msg_for_gui, status_level_final_gui)

    # 函数末尾的返回
    if not return_core_prediction_only:
        return any_successful_model_prediction, final_signal_for_internal_state
    else:
        # 这条路径理论上不应该在 try 块成功执行后到达，因为核心模式应该在 try 块内返回。
        # 但如果 any_successful_model_prediction 为 False（例如初始化就是False且try中未成功），则会到这里。
        if any_successful_model_prediction: # 这种情况不应该发生
             print(f"!!! 警告: 意外的执行流 (核心模式但 try 块未返回)。")
             return {"p_favorable": model_positive_class_prob, "p_up": avg_up_prob_value, "p_down": avg_down_prob_value, 
                     "internal_signal": final_signal_for_internal_state, "error": False} # 假设有值
        else: # 如果核心预测本身就失败了（例如初始化后未成功）
             return {"p_favorable": np.nan, "p_up": np.nan, "p_down": np.nan, 
                     "internal_signal": final_signal_for_internal_state if final_signal_for_internal_state else "Error_Unknown_Core_Init", 
                     "error": True}



def calculate_global_market_state(binance_client):
    """
    计算全局市场状态指标，用于元模型特征工程
    返回包含全局趋势和波动率指标的字典
    使用改进的错误处理机制

    Args:
        binance_client: Binance客户端对象

    Returns:
        dict: 包含全局市场状态指标的字典
    """
    logger = logging.getLogger(__name__)

    # 默认返回值结构
    default_result = {
        'global_trend_signal': 0,
        'global_trend_strength': 0,
        'global_adx': 0.0,
        'global_pdi': 0.0,
        'global_mdi': 0.0,
        'global_ema_short': 0.0,
        'global_ema_long': 0.0,
        'global_ema_diff': 0.0,
        'global_ema_diff_pct': 0.0,
        'global_price_ema_distance_pct': 0.0,
        'global_ema_slope_short': 0.0,
        'global_ema_slope_long': 0.0,
        'global_ema_cross_signal': 0,
        'global_ema_divergence': 0.0,
        'global_volatility_level': 0,
        'global_atr': 0.0,
        'global_atr_percent': 0.0,
        'global_status': 'error'
    }

    try:
        logger.info("开始计算全局市场状态...")

        # 验证输入参数
        if binance_client is None:
            raise DataValidationError(
                "Binance客户端为空",
                context={'function': 'calculate_global_market_state'}
            )

        # 获取配置
        global_config = getattr(config, 'GLOBAL_MARKET_STATE_CONFIG', {})
        symbol = global_config.get('symbol', 'BTCUSDT')
        timeframe = global_config.get('timeframe', '1h')
        data_limit = global_config.get('data_fetch_limit', 200)

        logger.info(f"配置参数: Symbol={symbol}, Timeframe={timeframe}, DataLimit={data_limit}")

        # 获取全局市场数据
        logger.info(f"正在获取 {symbol}@{timeframe} 数据...")
        try:
            df_global = fetch_binance_history(binance_client, symbol, timeframe, limit=data_limit)
        except Exception as e:
            raise DataValidationError(
                f"获取市场数据失败: {symbol}@{timeframe}",
                context={
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'data_limit': data_limit
                },
                original_exception=e
            )

        # 数据验证
        if df_global is None:
            raise DataValidationError(
                "获取的市场数据为 None",
                context={'symbol': symbol, 'timeframe': timeframe}
            )

        if df_global.empty:
            raise DataValidationError(
                "获取的市场数据为空",
                context={'symbol': symbol, 'timeframe': timeframe, 'data_shape': df_global.shape}
            )

        # 检查数据质量
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df_global.columns]
        if missing_columns:
            raise DataValidationError(
                f"市场数据缺少必要列: {missing_columns}",
                context={
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'available_columns': list(df_global.columns),
                    'missing_columns': missing_columns
                }
            )

        # 检查数据量是否足够
        min_data_required = 50
        if len(df_global) < min_data_required:
            error_context = {
                'symbol': symbol,
                'timeframe': timeframe,
                'actual_length': len(df_global),
                'required_length': min_data_required
            }
            logger.warning(f"数据不足，返回默认值 (需要至少{min_data_required}条数据，实际: {len(df_global)})")
            result = default_result.copy()
            result['global_status'] = 'data_insufficient'
            return result

        logger.info(f"数据获取成功: 形状={df_global.shape}, 时间范围={df_global.index[0]} 到 {df_global.index[-1]}")
        logger.info(f"最新价格: {df_global['close'].iloc[-1]:.2f}")

    except (DataValidationError, FeatureEngineeringError) as e:
        log_prediction_error(logger, e, 'calculate_global_market_state',
                           additional_context={'step': 'data_validation'})
        result = default_result.copy()
        result['global_status'] = f'error_{type(e).__name__.lower()}'
        return result

    except Exception as e:
        error_context = {
            'step': 'data_validation',
            'unexpected_error': True
        }
        wrapped_error = FeatureEngineeringError(
            f"全局市场状态计算时发生未预期错误: {str(e)}",
            context=error_context,
            original_exception=e
        )
        log_prediction_error(logger, wrapped_error, 'calculate_global_market_state',
                           additional_context=error_context)
        result = default_result.copy()
        result['global_status'] = 'error_unexpected'
        return result

    # 如果数据验证通过，继续计算市场状态指标
    try:
        # 初始化返回值 (优化EMA特征)
        result = {
            'global_trend_signal': 0,  # -1: 下降, 0: 无明确趋势, 1: 上升
            'global_trend_strength': 0,  # 0: 弱趋势, 1: 强趋势
            'global_adx': 0.0,
            'global_pdi': 0.0,
            'global_mdi': 0.0,
            # 🎯 优化：保留原始EMA特征但降低权重
            'global_ema_short': 0.0,
            'global_ema_long': 0.0,
            # 🎯 新增：高信息量EMA衍生特征
            'global_ema_diff': 0.0,                    # EMA差值 (short - long)
            'global_ema_diff_pct': 0.0,                # EMA差值百分比 ((short-long)/long*100)
            'global_price_ema_distance_pct': 0.0,      # 价格与EMA距离百分比
            'global_ema_slope_short': 0.0,             # 短期EMA斜率
            'global_ema_slope_long': 0.0,              # 长期EMA斜率
            'global_ema_cross_signal': 0,              # EMA交叉信号 (1=金叉, -1=死叉, 0=无交叉)
            'global_ema_divergence': 0.0,              # 价格与EMA背离程度
            'global_volatility_level': 0,  # -1: 高波动, 0: 正常, 1: 低波动
            'global_atr': 0.0,
            'global_atr_percent': 0.0,
            'global_status': 'calculated'
        }

        # 🎯 优化：计算增强的EMA特征集
        ema_short_period = global_config.get('trend_ema_short_period', 21)
        ema_long_period = global_config.get('trend_ema_long_period', 50)
        ema_slope_period = global_config.get('ema_slope_period', 5)  # EMA斜率计算周期

        # 🎯 调试日志：EMA配置
        print(f"🔍 [GlobalMarketState] EMA配置:")
        print(f"    短期EMA周期: {ema_short_period}")
        print(f"    长期EMA周期: {ema_long_period}")
        print(f"    斜率计算周期: {ema_slope_period}")

        # 验证数据长度是否足够计算EMA和衍生特征
        min_required_length = max(ema_short_period, ema_long_period) * 2 + ema_slope_period
        print(f"🔍 [GlobalMarketState] 数据长度验证:")
        print(f"    实际数据长度: {len(df_global)}")
        print(f"    最小需要长度: {min_required_length}")

        if len(df_global) >= min_required_length:
            try:
                print(f"✓ [GlobalMarketState] 开始计算EMA指标...")
                close_prices = df_global['close']
                current_price = float(close_prices.iloc[-1])
                print(f"    当前价格: {current_price:.2f}")

                # 计算基础EMA
                print(f"🔍 [GlobalMarketState] 计算EMA指标...")
                ema_short = pta.ema(close_prices, length=ema_short_period)
                ema_long = pta.ema(close_prices, length=ema_long_period)

                # 🎯 调试日志：EMA计算结果
                print(f"🔍 [GlobalMarketState] EMA计算结果:")
                if ema_short is not None and not ema_short.empty:
                    print(f"    短期EMA: 成功计算，长度={len(ema_short)}")
                    print(f"    短期EMA最新3个值: {ema_short.tail(3).tolist()}")
                else:
                    print(f"    ❌ 短期EMA计算失败或为空")

                if ema_long is not None and not ema_long.empty:
                    print(f"    长期EMA: 成功计算，长度={len(ema_long)}")
                    print(f"    长期EMA最新3个值: {ema_long.tail(3).tolist()}")
                else:
                    print(f"    ❌ 长期EMA计算失败或为空")

                if ema_short is not None and ema_long is not None and not ema_short.empty and not ema_long.empty:
                    # 获取最新的非NaN EMA值
                    latest_ema_short = ema_short.dropna().iloc[-1] if not ema_short.dropna().empty else 0.0
                    latest_ema_long = ema_long.dropna().iloc[-1] if not ema_long.dropna().empty else 0.0

                    # 基础EMA特征
                    result['global_ema_short'] = float(latest_ema_short)
                    result['global_ema_long'] = float(latest_ema_long)

                    # 🎯 新增：高信息量EMA衍生特征
                    if latest_ema_long > 0:
                        # 1. EMA差值和百分比
                        ema_diff = latest_ema_short - latest_ema_long
                        result['global_ema_diff'] = float(ema_diff)
                        result['global_ema_diff_pct'] = float((ema_diff / latest_ema_long) * 100)

                        # 2. 价格与EMA距离百分比 (使用短期EMA作为参考)
                        result['global_price_ema_distance_pct'] = float((current_price - latest_ema_short) / latest_ema_short * 100)

                        # 3. EMA斜率计算 (变化率)
                        if len(ema_short.dropna()) >= ema_slope_period and len(ema_long.dropna()) >= ema_slope_period:
                            ema_short_clean = ema_short.dropna()
                            ema_long_clean = ema_long.dropna()

                            # 计算斜率 (最近N期的平均变化率)
                            short_slope = (ema_short_clean.iloc[-1] - ema_short_clean.iloc[-ema_slope_period]) / ema_slope_period
                            long_slope = (ema_long_clean.iloc[-1] - ema_long_clean.iloc[-ema_slope_period]) / ema_slope_period

                            result['global_ema_slope_short'] = float(short_slope / latest_ema_short * 100)  # 标准化为百分比
                            result['global_ema_slope_long'] = float(long_slope / latest_ema_long * 100)

                        # 4. EMA交叉信号检测
                        if len(ema_short.dropna()) >= 2 and len(ema_long.dropna()) >= 2:
                            ema_short_clean = ema_short.dropna()
                            ema_long_clean = ema_long.dropna()

                            # 当前和前一期的EMA关系
                            current_above = ema_short_clean.iloc[-1] > ema_long_clean.iloc[-1]
                            previous_above = ema_short_clean.iloc[-2] > ema_long_clean.iloc[-2]

                            if current_above and not previous_above:
                                result['global_ema_cross_signal'] = 1  # 金叉
                            elif not current_above and previous_above:
                                result['global_ema_cross_signal'] = -1  # 死叉
                            else:
                                result['global_ema_cross_signal'] = 0  # 无交叉

                        # 5. 价格与EMA背离程度
                        # 计算价格趋势与EMA趋势的背离
                        if len(close_prices) >= ema_slope_period:
                            price_slope = (close_prices.iloc[-1] - close_prices.iloc[-ema_slope_period]) / ema_slope_period
                            price_slope_pct = price_slope / close_prices.iloc[-ema_slope_period] * 100
                            ema_slope_pct = result['global_ema_slope_short']

                            # 背离程度 = 价格斜率 - EMA斜率
                            result['global_ema_divergence'] = float(price_slope_pct - ema_slope_pct)

                    # 调试输出 (仅在启用时)
                    if getattr(config, 'EMA_DEBUG_VERBOSE', False):
                        print(f"    [GlobalEMA] 数据长度: {len(df_global)}")
                        print(f"    [GlobalEMA] EMA短期: {latest_ema_short:.2f}, EMA长期: {latest_ema_long:.2f}")
                        print(f"    [GlobalEMA] EMA差值: {result['global_ema_diff']:.2f}, 差值%: {result['global_ema_diff_pct']:.2f}%")
                        print(f"    [GlobalEMA] 价格距离%: {result['global_price_ema_distance_pct']:.2f}%")
                        print(f"    [GlobalEMA] 交叉信号: {result['global_ema_cross_signal']}, 背离: {result['global_ema_divergence']:.2f}")
                else:
                    print(f"    ⚠️ [GlobalEMA] EMA计算失败，使用默认值")
            except Exception as e:
                print(f"    ❌ [GlobalEMA] EMA计算异常: {e}")
        else:
            print(f"    ⚠️ [GlobalEMA] 数据不足: {len(df_global)} < {min_required_length}")

        # 🎯 调试日志：开始计算全局趋势指标
        print(f"🔍 [GlobalMarketState] 开始计算全局趋势指标...")
        trend_indicator_type = global_config.get('trend_indicator_type', 'ema_cross')
        print(f"    趋势指标类型: {trend_indicator_type}")

        # 🎯 修复：支持 'both' 模式，同时计算ADX和EMA指标
        if trend_indicator_type in ['adx', 'both']:
            adx_period = global_config.get('trend_adx_period', 14)
            adx_strength_threshold = global_config.get('trend_adx_strength_threshold', 25)
            adx_threshold = global_config.get('trend_adx_threshold', 20)

            # 🎯 调试日志：ADX配置
            print(f"🔍 [GlobalMarketState] ADX配置:")
            print(f"    ADX周期: {adx_period}")
            print(f"    ADX阈值: {adx_threshold}")
            print(f"    ADX强度阈值: {adx_strength_threshold}")

            print(f"🔍 [GlobalMarketState] 计算ADX指标...")
            adx_df = pta.adx(df_global['high'], df_global['low'], df_global['close'],
                           length=adx_period, lensig=adx_period)

            # 🎯 调试日志：ADX计算结果
            if adx_df is None:
                print(f"    ❌ ADX计算失败: adx_df 为 None")
            elif adx_df.empty:
                print(f"    ❌ ADX计算失败: adx_df 为空")
            else:
                print(f"    ✓ ADX计算成功:")
                print(f"      ADX DataFrame形状: {adx_df.shape}")
                print(f"      ADX DataFrame列名: {list(adx_df.columns)}")

                adx_col, pdi_col, mdi_col = f'ADX_{adx_period}', f'DMP_{adx_period}', f'DMN_{adx_period}'
                print(f"      期望的列名: {[adx_col, pdi_col, mdi_col]}")

                if all(c in adx_df.columns for c in [adx_col, pdi_col, mdi_col]):
                    print(f"    ✓ 所有ADX列都存在")

                    # 获取最新值
                    latest_adx = adx_df[adx_col].iloc[-1]
                    latest_pdi = adx_df[pdi_col].iloc[-1]
                    latest_mdi = adx_df[mdi_col].iloc[-1]

                    print(f"    ADX最新3个值: {adx_df[adx_col].tail(3).tolist()}")
                    print(f"    PDI最新3个值: {adx_df[pdi_col].tail(3).tolist()}")
                    print(f"    MDI最新3个值: {adx_df[mdi_col].tail(3).tolist()}")

                    result['global_adx'] = latest_adx
                    result['global_pdi'] = latest_pdi
                    result['global_mdi'] = latest_mdi

                    if not any(pd.isna(v) for v in [result['global_adx'], result['global_pdi'], result['global_mdi']]):
                        print(f"    ✓ ADX值有效，计算趋势信号...")
                        print(f"      ADX: {latest_adx:.2f}, PDI: {latest_pdi:.2f}, MDI: {latest_mdi:.2f}")

                        # 🎯 修复：在both模式下，ADX用于趋势强度，EMA用于趋势方向
                        if trend_indicator_type == 'adx':
                            # 纯ADX模式：ADX决定趋势方向和强度
                            if result['global_adx'] > adx_threshold:
                                result['global_trend_signal'] = 1 if result['global_pdi'] > result['global_mdi'] else -1
                                print(f"      趋势信号(ADX): {result['global_trend_signal']} ({'上升' if result['global_trend_signal'] == 1 else '下降'})")
                            else:
                                print(f"      ADX低于阈值，无明确趋势")

                            if result['global_adx'] > adx_strength_threshold and result['global_trend_signal'] != 0:
                                result['global_trend_strength'] = 1
                                print(f"      趋势强度(ADX): 强")
                            else:
                                print(f"      趋势强度(ADX): 弱")
                        else:
                            # both模式：ADX仅用于趋势强度判断，趋势方向由EMA决定
                            # 🎯 修复：实现1-5级趋势强度分级系统
                            adx_val = result['global_adx']
                            if adx_val < 15:
                                result['global_trend_strength'] = 1  # 极弱趋势
                                trend_desc = "极弱"
                            elif adx_val < 25:
                                result['global_trend_strength'] = 2  # 弱趋势
                                trend_desc = "弱"
                            elif adx_val < 40:
                                result['global_trend_strength'] = 3  # 中等趋势
                                trend_desc = "中等"
                            elif adx_val < 60:
                                result['global_trend_strength'] = 4  # 强趋势
                                trend_desc = "强"
                            else:
                                result['global_trend_strength'] = 5  # 极强趋势
                                trend_desc = "极强"

                            print(f"      趋势强度(ADX): {trend_desc} (等级{result['global_trend_strength']}, ADX={adx_val:.2f})")
                    else:
                        print(f"    ❌ ADX值包含NaN: ADX={latest_adx}, PDI={latest_pdi}, MDI={latest_mdi}")
                else:
                    print(f"    ❌ 缺少必要的ADX列")
                    missing_cols = [c for c in [adx_col, pdi_col, mdi_col] if c not in adx_df.columns]
                    print(f"      缺少的列: {missing_cols}")

        if trend_indicator_type in ['ema_cross', 'both']:
            # 使用已计算的EMA值来确定趋势信号
            print(f"🔍 [GlobalMarketState] 使用EMA交叉判断趋势...")
            print(f"    短期EMA: {result['global_ema_short']:.2f}")
            print(f"    长期EMA: {result['global_ema_long']:.2f}")

            if result['global_ema_short'] > 0 and result['global_ema_long'] > 0:
                if result['global_ema_short'] > result['global_ema_long']:
                    result['global_trend_signal'] = 1
                    print(f"    ✓ 趋势信号: 上升 (短期EMA > 长期EMA)")
                elif result['global_ema_short'] < result['global_ema_long']:
                    result['global_trend_signal'] = -1
                    print(f"    ✓ 趋势信号: 下降 (短期EMA < 长期EMA)")
                else:
                    print(f"    ✓ 趋势信号: 中性 (EMA相等)")

                # 🎯 修复：基于EMA差值的1-5级趋势强度分级
                ema_diff_percent = abs(result['global_ema_short'] - result['global_ema_long']) / result['global_ema_long'] * 100
                print(f"    EMA差值百分比: {ema_diff_percent:.2f}%")

                # 只有在both模式下才需要计算EMA趋势强度，否则使用ADX的结果
                if trend_indicator_type == 'ema_cross':
                    if ema_diff_percent < 0.1:
                        result['global_trend_strength'] = 1  # 极弱趋势
                        trend_desc = "极弱"
                    elif ema_diff_percent < 0.5:
                        result['global_trend_strength'] = 2  # 弱趋势
                        trend_desc = "弱"
                    elif ema_diff_percent < 1.5:
                        result['global_trend_strength'] = 3  # 中等趋势
                        trend_desc = "中等"
                    elif ema_diff_percent < 3.0:
                        result['global_trend_strength'] = 4  # 强趋势
                        trend_desc = "强"
                    else:
                        result['global_trend_strength'] = 5  # 极强趋势
                        trend_desc = "极强"

                    print(f"    ✓ 趋势强度(EMA): {trend_desc} (等级{result['global_trend_strength']}, 差值={ema_diff_percent:.2f}%)")
                else:
                    print(f"    ✓ EMA差值: {ema_diff_percent:.2f}% (趋势强度由ADX决定)")
            else:
                print(f"    ❌ EMA值无效，无法判断趋势")

        # 🎯 调试日志：开始计算全局波动率指标
        print(f"🔍 [GlobalMarketState] 开始计算全局波动率指标...")
        atr_period = global_config.get('volatility_atr_period', 14)
        min_atr_percent = global_config.get('volatility_min_atr_percent', 0.08)
        max_atr_percent = global_config.get('volatility_max_atr_percent', 1.5)

        # 🎯 调试日志：ATR配置
        print(f"🔍 [GlobalMarketState] ATR配置:")
        print(f"    ATR周期: {atr_period}")
        print(f"    最小ATR百分比: {min_atr_percent}%")
        print(f"    最大ATR百分比: {max_atr_percent}%")

        print(f"🔍 [GlobalMarketState] 计算ATR指标...")
        atr_series = pta.atr(df_global['high'], df_global['low'], df_global['close'], length=atr_period)

        # 🎯 调试日志：ATR计算结果
        if atr_series is None:
            print(f"    ❌ ATR计算失败: atr_series 为 None")
        elif atr_series.empty:
            print(f"    ❌ ATR计算失败: atr_series 为空")
        elif pd.isna(atr_series.iloc[-1]):
            print(f"    ❌ ATR最新值为NaN")
        else:
            print(f"    ✓ ATR计算成功:")
            print(f"      ATR序列长度: {len(atr_series)}")
            print(f"      ATR最新3个值: {atr_series.tail(3).tolist()}")

            result['global_atr'] = atr_series.iloc[-1]
            close_price = df_global['close'].iloc[-1]
            result['global_atr_percent'] = (result['global_atr'] / close_price) * 100 if close_price > 0 else 0.0

            print(f"      ATR绝对值: {result['global_atr']:.4f}")
            print(f"      ATR百分比: {result['global_atr_percent']:.4f}%")
            print(f"      当前价格: {close_price:.2f}")

            # 🎯 修复：实现1-5级波动率分级系统
            atr_pct = result['global_atr_percent']
            if atr_pct < 0.05:
                result['global_volatility_level'] = 1  # 极低波动
                vol_desc = "极低"
            elif atr_pct < min_atr_percent:
                result['global_volatility_level'] = 2  # 低波动
                vol_desc = "低"
            elif atr_pct < max_atr_percent:
                result['global_volatility_level'] = 3  # 正常波动
                vol_desc = "正常"
            elif atr_pct < 3.0:
                result['global_volatility_level'] = 4  # 高波动
                vol_desc = "高"
            else:
                result['global_volatility_level'] = 5  # 极高波动
                vol_desc = "极高"

            print(f"      ✓ 波动率水平: {vol_desc} (等级{result['global_volatility_level']}, ATR={atr_pct:.4f}%)")

        # 🎯 调试日志：最终结果总结
        print(f"\n📊 [GlobalMarketState] 计算完成，最终结果:")
        print(f"=" * 60)
        print(f"  趋势信号: {result['global_trend_signal']} ({'上升' if result['global_trend_signal'] == 1 else '下降' if result['global_trend_signal'] == -1 else '中性'})")
        print(f"  趋势强度: {result['global_trend_strength']} ({'强' if result['global_trend_strength'] == 1 else '弱'})")
        print(f"  ADX: {result['global_adx']:.2f}")
        print(f"  PDI: {result['global_pdi']:.2f}")
        print(f"  MDI: {result['global_mdi']:.2f}")
        print(f"  短期EMA: {result['global_ema_short']:.2f}")
        print(f"  长期EMA: {result['global_ema_long']:.2f}")
        print(f"  EMA差值: {result['global_ema_diff']:.2f}")
        print(f"  EMA差值%: {result['global_ema_diff_pct']:.2f}%")
        print(f"  价格EMA距离%: {result['global_price_ema_distance_pct']:.2f}%")
        print(f"  EMA斜率(短): {result['global_ema_slope_short']:.4f}")
        print(f"  EMA斜率(长): {result['global_ema_slope_long']:.4f}")
        print(f"  EMA交叉信号: {result['global_ema_cross_signal']}")
        print(f"  EMA背离: {result['global_ema_divergence']:.2f}")
        print(f"  波动率水平: {result['global_volatility_level']} ({'低' if result['global_volatility_level'] == 1 else '高' if result['global_volatility_level'] == -1 else '正常'})")
        print(f"  ATR: {result['global_atr']:.4f}")
        print(f"  ATR%: {result['global_atr_percent']:.4f}%")
        print(f"  状态: {result['global_status']}")
        print(f"=" * 60)

        # 检查是否有异常值（全为0或NaN）
        non_zero_count = sum(1 for k, v in result.items()
                           if k != 'global_status' and isinstance(v, (int, float)) and v != 0)
        total_numeric_fields = sum(1 for k, v in result.items()
                                 if k != 'global_status' and isinstance(v, (int, float)))

        print(f"🔍 [GlobalMarketState] 数据质量检查:")
        print(f"  非零字段数: {non_zero_count}/{total_numeric_fields}")

        if non_zero_count == 0:
            print(f"  ⚠️ 警告: 所有数值字段都为0，可能存在计算问题")
        elif non_zero_count < total_numeric_fields * 0.5:
            print(f"  ⚠️ 警告: 超过50%的字段为0，数据质量可能有问题")
        else:
            print(f"  ✓ 数据质量正常")

        return result

    except (FeatureEngineeringError, DataValidationError) as e:
        # 处理特定的特征工程错误
        log_prediction_error(logger, e, 'calculate_global_market_state',
                           additional_context={'step': 'feature_calculation'})
        result = default_result.copy()
        result['global_status'] = f'error_{type(e).__name__.lower()}'
        return result

    except Exception as e:
        # 处理未预期的错误
        error_context = {
            'step': 'feature_calculation',
            'unexpected_error': True
        }
        wrapped_error = FeatureEngineeringError(
            f"全局市场状态特征计算时发生未预期错误: {str(e)}",
            context=error_context,
            original_exception=e
        )
        log_prediction_error(logger, wrapped_error, 'calculate_global_market_state',
                           additional_context=error_context)
        result = default_result.copy()
        result['global_status'] = 'error_unexpected'
        return result


def _extract_probabilities_from_core_info(core_info, base_name):
    """
    🎯 从基础模型核心信息中智能提取上涨和下跌概率

    支持三种数据格式：
    1. 传统格式：包含 p_up, p_down 字段
    2. 精英格式：包含 oof_{target_name}_elite_fold{idx} 字段
    3. LSTM格式：包含 oof_{target_name}_lstm_prediction 字段

    Args:
        core_info (dict): 基础模型返回的核心信息
        base_name (str): 基础模型名称

    Returns:
        tuple: (p_up, p_down) 概率值
    """
    try:
        # 方法1：尝试传统格式
        if 'p_up' in core_info and 'p_down' in core_info:
            return core_info.get('p_up', 0), core_info.get('p_down', 0)

        # 方法2：尝试精英格式
        elite_predictions = []
        for key, value in core_info.items():
            if key.startswith(f'oof_{base_name}_elite_fold') and isinstance(value, (int, float)):
                elite_predictions.append(float(value))

        if elite_predictions:
            # 计算精英模型预测的平均值
            avg_prediction = sum(elite_predictions) / len(elite_predictions)

            # 根据基础模型类型解释概率
            if base_name.endswith('_UP'):
                # UP模型：预测值表示上涨概率
                p_up = avg_prediction
                p_down = 1.0 - avg_prediction
            elif base_name.endswith('_DOWN'):
                # DOWN模型：预测值表示下跌概率
                p_down = avg_prediction
                p_up = 1.0 - avg_prediction
            else:
                # 其他模型：假设预测值表示正类概率，根据目标类型判断
                # 这里需要获取模型配置来确定目标类型
                try:
                    import config
                    model_config = config.get_target_config(base_name)
                    target_type = model_config.get('target_variable_type', 'BOTH').upper()

                    if target_type == 'UP_ONLY':
                        p_up = avg_prediction
                        p_down = 1.0 - avg_prediction
                    elif target_type == 'DOWN_ONLY':
                        p_down = avg_prediction
                        p_up = 1.0 - avg_prediction
                    else:  # BOTH 或其他
                        # 对于三分类模型，使用预测值作为上涨概率
                        p_up = avg_prediction
                        p_down = 1.0 - avg_prediction
                except:
                    # 配置获取失败时的默认处理
                    p_up = avg_prediction
                    p_down = 1.0 - avg_prediction

            return p_up, p_down

        # 方法3：尝试LSTM格式
        lstm_prediction_key = f'oof_{base_name}_lstm_prediction'
        if lstm_prediction_key in core_info:
            lstm_up_prob = float(core_info[lstm_prediction_key])
            lstm_down_prob = 1.0 - lstm_up_prob
            print(f"  ✓ 提取LSTM概率: {base_name} -> UP:{lstm_up_prob:.3f}, DOWN:{lstm_down_prob:.3f}")
            return lstm_up_prob, lstm_down_prob

        # 方法4：回退到默认值
        print(f"⚠️ 无法从 {base_name} 的核心信息中提取概率，使用默认值")
        return 0.0, 0.0

    except Exception as e:
        print(f"❌ 提取 {base_name} 概率时出错: {e}")
        return 0.0, 0.0


def run_meta_prediction_for_current_trigger(binance_client, app_timezone_param, simulator_actual_url=None):
    """
    🎯 V11.0 "三军协同" - 元模型预测流程：唯一的决策中枢

    架构说明：
    1. 静默调用基础模型（return_core_prediction_only=True）
    2. 元模型综合决策
    3. 动态交易过滤器最终裁决
    4. 只有通过过滤器的信号才能发送
    """
    print("\n" + "="*25 + " 🎯 V11.0 元模型决策中枢启动 " + "="*25)
    print("🏛️ [V11.0 架构] 元模型作为唯一决策出口，基础模型仅提供信息")

    # --- 步骤 0: 前置检查 ---
    # 🔒 交易状态检查 - 严格的单笔交易限制
    try:
        from .trade_state_manager import enhanced_trade_state_manager as trade_state_manager

        if not trade_state_manager.can_start_new_trade():
            trade_status = trade_state_manager.get_status()
            print(f"🔒 [交易限制] 当前有活跃交易，跳过预测")
            print(f"    当前状态: {trade_status['current_state']}")
            print(f"    交易持续时间: {trade_status['trade_duration_minutes']:.1f} 分钟")

            # 记录被过滤的信号
            signal_info = {
                'signal_type': 'meta_model_prediction',
                'trigger_time': datetime.now().isoformat(),
                'reason': 'active_trade_blocking'
            }
            trade_state_manager.filter_signal(signal_info)

            publish_status_update("有活跃交易，跳过预测", "info")
            return False, "Active_Trade_Blocking"

    except Exception as e:
        print(f"⚠️ [交易状态检查] 异常: {e}")
        # 如果交易状态管理器出错，继续执行但记录警告
        publish_status_update("交易状态检查异常", "warning")

    if not global_prediction_state_manager.is_meta_model_loaded_successfully():
        if not load_meta_model_if_needed():
            print("!!! [Meta-Check] 元模型加载失败，预测中止。")
            publish_status_update("元模型加载失败", "error")
            return False, "Error_Meta_Load"

    # --- 步骤 1: 静默收集基础模型数据 ---
    print("--- 🔇 步骤 1: 静默收集基础模型的核心预测数据 ---")
    print("🎯 [V11.0 静默调用] 基础模型只提供信息，不发送任何信号")
    all_core_infos_from_bases = {}
    all_base_models_succeeded = True
    unique_base_model_names = list(set(item['base_model_name'] for item in getattr(config, 'META_MODEL_INPUT_FEATURES_CONFIG', [])))

    for base_model_name in unique_base_model_names:
        try:
            print(f"  🔇 静默调用基础模型: {base_model_name}")
            base_cfg = config.get_target_config(base_model_name)
            # 🎯 V11.0 关键：return_core_prediction_only=True 确保基础模型静默运行
            core_pred_info = run_prediction_cycle_for_target(base_cfg, binance_client, app_timezone_param, return_core_prediction_only=True)
            all_core_infos_from_bases[base_model_name] = core_pred_info
            if core_pred_info.get("error", True):
                all_base_models_succeeded = False
                print(f"  ❌ {base_model_name}: 预测失败")
            else:
                print(f"  ✅ {base_model_name}: 预测成功，信息已收集")
        except Exception as e:
            all_base_models_succeeded = False
            all_core_infos_from_bases[base_model_name] = {"error": True, "internal_signal": str(e)}
            print(f"!!! 获取 '{base_model_name}' 核心预测时出错: {e}")
            traceback.print_exc(limit=1)
            
    if not all_base_models_succeeded:
        print("!!! 元模型中止: 一个或多个基础模型预测失败。")
        publish_status_update("基础模型预测失败，元模型中止", "error")
        return False, "Error_Base_Pred_Fail"

    # --- 步骤 2 & 3: 构建特征并执行元模型预测 ---
    print("--- 步骤 2 & 3: 构建特征并执行元模型预测 ---")
    try:
        trained_meta_feature_names = global_prediction_state_manager.get_meta_model_feature_names()
        meta_input_data = {}
        
        # 2a. 添加基础模型和上下文特征
        # 🎯 修复：直接从精英格式数据中提取特征，而不是使用传统配置映射
        for base_name, core_info in all_core_infos_from_bases.items():
            if core_info.get("error", False):
                continue

            # 提取精英模型特征（直接使用训练时的特征名格式）
            for key, value in core_info.items():
                if key.startswith(f'oof_{base_name}_elite_fold') and isinstance(value, (int, float)):
                    meta_input_data[key] = float(value)
                    print(f"    ✓ 添加精英特征: {key} = {value:.3f}")

            # 提取上下文特征
            context_features = core_info.get('context_features', {})
            for context_key, context_value in context_features.items():
                if isinstance(context_value, (int, float)):
                    feature_name = f"feat_{context_key}_{base_name.lower()}_model"
                    meta_input_data[feature_name] = float(context_value)
                    print(f"    ✓ 添加上下文特征: {feature_name} = {context_value:.3f}")

        # 2b. 添加全局市场状态特征
        global_market_state = calculate_global_market_state(binance_client)
        for key, value in global_market_state.items():
            if key != 'global_status': meta_input_data[key] = value

        # 2c. 应用特征工程
        meta_input_data = apply_realtime_meta_feature_engineering(meta_input_data, trained_meta_feature_names)
        
        # 2d. 构建最终DataFrame
        final_meta_input_list = [meta_input_data.get(name, 0.5) for name in trained_meta_feature_names]
        meta_input_X = pd.DataFrame([final_meta_input_list], columns=trained_meta_feature_names)

        # 3. 元模型预测
        current_meta_model = global_prediction_state_manager.get_meta_model_instance()

        # 🔧 强化修复：全面的模型类型检测和预测方法
        print(f"  🔍 元模型类型检测: {type(current_meta_model)}")

        try:
            # 方法1: 优先尝试predict_proba（适用于LGBMClassifier等sklearn兼容模型）
            if hasattr(current_meta_model, 'predict_proba'):
                print("  📊 使用predict_proba方法")
                meta_pred_class = current_meta_model.predict(meta_input_X)[0]
                meta_model_probas = current_meta_model.predict_proba(meta_input_X)[0]
                print(f"  ✅ predict_proba成功: 类别={meta_pred_class}, 概率={meta_model_probas}")

            # 方法2: 如果没有predict_proba，检查是否是Booster对象
            elif hasattr(current_meta_model, 'predict'):
                import lightgbm as lgb
                if isinstance(current_meta_model, lgb.Booster):
                    print("  📊 使用Booster.predict方法")
                    # 对于Booster，predict返回的是概率值
                    raw_predictions = current_meta_model.predict(meta_input_X.values, num_iteration=current_meta_model.best_iteration)
                    print(f"  🔍 Booster原始预测: {raw_predictions}")

                    # 处理二分类情况
                    if len(raw_predictions) == 1:
                        prob_positive = raw_predictions[0]
                        prob_negative = 1.0 - prob_positive
                        meta_model_probas = np.array([prob_negative, prob_positive])
                        meta_pred_class = 1 if prob_positive > 0.5 else 0
                    else:
                        # 多分类情况
                        meta_model_probas = raw_predictions
                        meta_pred_class = np.argmax(meta_model_probas)

                    print(f"  ✅ Booster预测成功: 类别={meta_pred_class}, 概率={meta_model_probas}")

                else:
                    print("  📊 使用通用predict方法")
                    # 其他类型的模型，尝试标准预测
                    meta_pred_class = current_meta_model.predict(meta_input_X)[0]
                    # 如果没有predict_proba，使用基于预测类别的默认概率
                    if meta_pred_class == 1:
                        meta_model_probas = np.array([0.3, 0.7])  # UP信号
                    else:
                        meta_model_probas = np.array([0.7, 0.3])  # DOWN信号

                    print(f"  ✅ 通用预测成功: 类别={meta_pred_class}, 概率={meta_model_probas}")

            else:
                raise ValueError(f"元模型对象不支持predict或predict_proba方法: {type(current_meta_model)}")

        except Exception as predict_error:
            print(f"  ❌ 元模型预测过程中出错: {predict_error}")
            # 提供默认的安全预测
            meta_pred_class = 2  # NEUTRAL
            meta_model_probas = np.array([0.4, 0.4, 0.2])  # 默认为中性
            print(f"  🛡️ 使用安全默认预测: 类别={meta_pred_class}, 概率={meta_model_probas}")

        print(f"  📊 最终元模型预测结果: 类别={meta_pred_class}, 概率={meta_model_probas}")
    except Exception as e:
        print(f"!!! 元模型特征构建或预测失败: {e}")
        publish_status_update("元模型特征构建或预测失败", "error")
        return False, "Error_Meta_Feature_Build"

    # --- 步骤 4: 智能决策 ---
    print("--- 步骤 4: 初步智能决策 ---")
    initial_signal, prediction_label_meta, prediction_color_meta = _make_intelligent_meta_decision(
        meta_model_probas, meta_pred_class
    )
    print(f"  初步决策: {initial_signal}, 标签: {prediction_label_meta}")

    # 确保prediction_color_meta有默认值
    if 'prediction_color_meta' not in locals():
        prediction_color_meta = config.NEUTRAL_COLOR

    # --- 步骤 5: 动态交易过滤器 - 最终守门员 ---
    print("--- 🛡️ 步骤 5: 动态交易过滤器 - 最终守门员 ---")
    print("🎯 [V11.0 过滤器] 元模型决策必须通过过滤器验证才能发送信号")
    try:
        # 获取过滤器配置
        filter_config = getattr(config, 'DYNAMIC_TRADING_FILTER_CONFIG', None)
        trading_filter = DynamicTradingFilter(filter_config)

        print(f"  📊 元模型初步决策: {initial_signal}")
        print(f"  📊 元模型概率: {meta_model_probas}")
        print(f"  📊 全局市场状态: {global_market_state.get('market_regime', 'unknown')}")

        # 🎯 V11.0 关键：调用最终过滤器
        filter_result = trading_filter.apply_filter(
            signal=initial_signal.replace("_Meta", ""), # 传递 "UP", "DOWN", "Neutral"
            signal_probabilities=list(meta_model_probas),
            global_market_data=global_market_state
        )

        final_meta_signal = initial_signal
        # 根据过滤器结果更新信号和标签
        if not filter_result.get('should_trade', True):
            final_meta_signal = "Neutral_Filtered"
            prediction_label_meta = f"中性 (过滤: {', '.join(filter_result.get('filter_reasons', []))})"
            prediction_color_meta = config.NEUTRAL_COLOR
            print(f"  🚨 [V11.0 过滤器] 信号被动态过滤器阻止！原因: {filter_result.get('filter_reasons')}")
            print(f"  🛡️ [V11.0 守门员] 过滤器保护系统免受不良市场条件影响")
        elif filter_result.get('confidence_adjustment', 1.0) < 1.0:
            print(f"  🎯 [V11.0 过滤器] 过滤器调整了信号置信度: {filter_result.get('confidence_adjustment', 1.0):.2f}")
            prediction_label_meta += f" (置信度调整)"
        else:
            print(f"  ✅ [V11.0 过滤器] 信号通过过滤器验证，准备发送")

    except Exception as e:
        print(f"!!! [V11.0 过滤器] 动态交易过滤器执行失败: {e}。将使用原始信号。")
        final_meta_signal = initial_signal # 出错时使用原始信号

    # --- 步骤 6 & 7: 信号发送与GUI更新 ---
    print("--- 步骤 6 & 7: 最终决策与通讯 ---")
    actual_signal_to_send = "UP" if "UP" in final_meta_signal else ("DOWN" if "DOWN" in final_meta_signal else None)
    
    if actual_signal_to_send:
        # ... (您原有的信号发送逻辑，包括金额计算、冷却检查等)
        prob_for_sizing = meta_model_probas[1] if actual_signal_to_send == "UP" else meta_model_probas[0]
        trade_amount_to_send = calculate_trade_amount_from_strategy(
            getattr(config, 'META_MODEL_STATIC_KELLY_CONFIG', {}), actual_signal_to_send, prob_for_sizing
        )

        # 检查是否满足发送条件并发送信号
        should_send_signal = True

        # 检查冷却时间
        try:
            # 🎯 修复：使用全局的dynamic_config_manager实例
            try:
                # 使用全局的dynamic_config_manager实例
                if 'dynamic_config_manager' in globals() and dynamic_config_manager:
                    cooldown_seconds = dynamic_config_manager.get_global_param('signal_cooldown_seconds', 300)  # 默认5分钟
                else:
                    cooldown_seconds = 300  # 默认5分钟
            except ImportError:
                # 如果导入失败，使用默认值
                cooldown_seconds = 300  # 默认5分钟

            # 获取上次发送时间
            last_sent_time = getattr(global_prediction_state_manager, '_last_signal_sent_time', 0)
            current_time = time.time()

            if (current_time - last_sent_time) < cooldown_seconds:
                should_send_signal = False
                print(f"  ⏰ 信号发送冷却中，剩余时间: {cooldown_seconds - (current_time - last_sent_time):.1f}秒")
        except Exception as e:
            print(f"  ⚠️ 冷却检查失败: {e}")

        # 发送信号
        if should_send_signal and trade_amount_to_send > 0:
            # 🔒 交易状态管理 - 开始新交易前的最终检查
            try:
                from .trade_state_manager import enhanced_trade_state_manager as trade_state_manager

                # 构建交易信息
                trade_info = {
                    'signal_type': actual_signal_to_send,
                    'amount': trade_amount_to_send,
                    'symbol': 'BTCUSDT',
                    'source': 'meta_model',
                    'probabilities': meta_model_probas.tolist() if hasattr(meta_model_probas, 'tolist') else list(meta_model_probas),
                    'start_time': datetime.now().isoformat()
                }

                # 尝试开始新交易
                if not trade_state_manager.start_trade(trade_info):
                    print(f"🔒 [交易限制] 无法开始新交易，当前状态阻止")
                    should_send_signal = False
                else:
                    print(f"🔒 [交易管理] 新交易已开始，状态转为开仓中")

            except Exception as e:
                print(f"⚠️ [交易状态管理] 异常: {e}")
                # 如果交易状态管理器出错，为了安全起见，阻止信号发送
                should_send_signal = False

        # 重新检查发送条件（可能被交易状态管理器修改）
        if should_send_signal and trade_amount_to_send > 0:
            # === V2.0日志系统：元模型决策上下文记录 ===
            try:
                import json
                from ..core.unified_trade_logger import get_unified_trade_logger

                # 获取当前价格
                current_price = get_real_time_price(binance_client, symbol_override="BTCUSDT")

                # 构建元模型决策上下文
                meta_context_data = {}

                # 1. 元模型概率信息
                meta_context_data['entry_signal_probability'] = float(meta_model_probas[1]) if actual_signal_to_send == "UP" else float(meta_model_probas[0])
                if len(meta_model_probas) == 3:
                    meta_context_data['entry_neutral_probability'] = float(meta_model_probas[2])
                    meta_context_data['entry_opposite_probability'] = float(meta_model_probas[0]) if actual_signal_to_send == "UP" else float(meta_model_probas[1])
                else:
                    meta_context_data['entry_neutral_probability'] = 1.0 - float(meta_model_probas[0]) - float(meta_model_probas[1])
                    meta_context_data['entry_opposite_probability'] = float(meta_model_probas[0]) if actual_signal_to_send == "UP" else float(meta_model_probas[1])

                # 2. V2.0核心：记录4个具体模型概率（用户特别要求！）
                # 上涨模型、下跌模型、LSTM模型、元模型
                four_models_probabilities = {}

                # 元模型自身的概率
                four_models_probabilities['Meta_model_up_prob'] = float(meta_model_probas[1]) if len(meta_model_probas) > 1 else 0.5
                four_models_probabilities['Meta_model_down_prob'] = float(meta_model_probas[0]) if len(meta_model_probas) > 0 else 0.5

                # 基础模型概率（UP模型、DOWN模型、LSTM模型）
                for base_model_name, core_info in all_core_infos_from_bases.items():
                    if not core_info.get("error", True):
                        if 'UP' in base_model_name.upper():
                            four_models_probabilities['UP_model_up_prob'] = float(core_info.get('p_up', 0.0))
                            four_models_probabilities['UP_model_down_prob'] = float(core_info.get('p_down', 0.0))
                        elif 'DOWN' in base_model_name.upper():
                            four_models_probabilities['DOWN_model_up_prob'] = float(core_info.get('p_up', 0.0))
                            four_models_probabilities['DOWN_model_down_prob'] = float(core_info.get('p_down', 0.0))
                        elif 'LSTM' in base_model_name.upper():
                            four_models_probabilities['LSTM_model_up_prob'] = float(core_info.get('p_up', 0.0))
                            four_models_probabilities['LSTM_model_down_prob'] = float(core_info.get('p_down', 0.0))

                meta_context_data['individual_model_probabilities'] = json.dumps(four_models_probabilities)

                # 3. 基础模型预测信息（元模型的输入特征）
                base_model_predictions = {}
                for base_model_name, core_info in all_core_infos_from_bases.items():
                    if not core_info.get("error", True):
                        base_model_predictions[f'{base_model_name}_up_prob'] = float(core_info.get('p_up', 0.0))
                        base_model_predictions[f'{base_model_name}_down_prob'] = float(core_info.get('p_down', 0.0))
                        base_model_predictions[f'{base_model_name}_signal'] = core_info.get('internal_signal', 'Neutral')
                meta_context_data['base_model_predictions'] = json.dumps(base_model_predictions)

                # 4. 市场状态信息
                meta_context_data['entry_market_regime'] = global_market_state.get('market_regime', 'normal')
                # 🔧 修复：正确映射全局市场状态字段名
                meta_context_data['entry_atr_percent'] = global_market_state.get('global_atr_percent', 0.0)
                meta_context_data['entry_adx_value'] = global_market_state.get('global_adx', 0.0)

                # 5. 元模型输入特征
                meta_input_features = {}
                if 'meta_input_data' in locals():
                    for feature_name, feature_value in meta_input_data.items():
                        if isinstance(feature_value, (int, float)):
                            meta_input_features[feature_name] = float(feature_value)
                meta_context_data['meta_model_inputs'] = json.dumps(meta_input_features)

                # 5. 🎯 V11.0 过滤器决策信息（关键！）
                filter_info = {
                    'filter_applied': 'filter_result' in locals(),
                    'should_trade': filter_result.get('should_trade', True) if 'filter_result' in locals() else True,
                    'filter_reasons': filter_result.get('filter_reasons', []) if 'filter_result' in locals() else [],
                    'confidence_adjustment': filter_result.get('confidence_adjustment', 1.0) if 'filter_result' in locals() else 1.0,
                    'filter_action': filter_result.get('filter_action', 'allow') if 'filter_result' in locals() else 'allow',
                    'original_signal': initial_signal,
                    'final_signal': final_meta_signal
                }
                meta_context_data['entry_top_features'] = json.dumps(filter_info)

                # === V2.0日志系统：记录元模型交易开仓 ===
                trade_logger = get_unified_trade_logger(base_log_dir="trading_logs_unified", auto_start=True)

                trade_id = trade_logger.record_trade_entry(
                    target_name="MetaModel_BTC_15m",
                    symbol="BTCUSDT",
                    direction=actual_signal_to_send,
                    entry_price=current_price,
                    amount=trade_amount_to_send,
                    payout_ratio=0.85,
                    context_data=meta_context_data
                )

                print(f"[V2.0日志系统] 元模型交易开仓已记录: {trade_id}")
                print(f"[V2.0日志系统] 记录了 {len(base_model_predictions)} 个基础模型的预测信息")

            except Exception as e_meta_logger:
                print(f"!!! [V2.0日志系统] 记录元模型交易开仓失败: {e_meta_logger}")
                # 不影响主流程，继续执行

            try:
                # 🚀 V12.0: 调用信号发送函数，传递完整的上下文数据
                success = _notify_simulator(
                    signal_type=actual_signal_to_send,
                    target_name_origin="MetaModel_BTC_15m",
                    amount=trade_amount_to_send,
                    symbol_for_sim="BTCUSDT",
                    sim_url_param=simulator_actual_url,
                    context_data=meta_context_data  # 🚀 V12.0: 传递信号快照
                )
                if success:
                    # 更新最后发送时间
                    global_prediction_state_manager._last_signal_sent_time = time.time()
                    print(f"  ✅ 信号成功发送至模拟盘: {actual_signal_to_send} (金额: ${trade_amount_to_send:.2f})")

                    # 🔒 交易状态管理 - 确认交易开仓
                    try:
                        from .trade_state_manager import enhanced_trade_state_manager as trade_state_manager
                        trade_state_manager.confirm_trade_opened({
                            'simulator_response': 'success',
                            'actual_amount_sent': trade_amount_to_send,
                            'signal_sent_time': datetime.now().isoformat()
                        })
                        print(f"🔒 [交易管理] 交易开仓已确认，状态转为活跃")
                    except Exception as e:
                        print(f"⚠️ [交易状态确认] 异常: {e}")

                else:
                    print(f"  ❌ 信号发送失败: {actual_signal_to_send} (金额: ${trade_amount_to_send:.2f})")

                    # 🔒 交易状态管理 - 信号发送失败，重置状态
                    try:
                        from .trade_state_manager import enhanced_trade_state_manager as trade_state_manager
                        trade_state_manager.force_reset("信号发送失败")
                        print(f"🔒 [交易管理] 信号发送失败，交易状态已重置")
                    except Exception as e:
                        print(f"⚠️ [交易状态重置] 异常: {e}")
            except Exception as e:
                print(f"  ❌ 信号发送异常: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"  ✅ 最终信号: {actual_signal_to_send} (金额: ${trade_amount_to_send:.2f}) - 未发送 (冷却中或金额为0)")
    else:
        print(f"  ℹ️ 最终决策: 观望 (信号: {final_meta_signal})")

    # 构建并更新GUI
    meta_gui_target_name = getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', "MetaSignal_BTC")
    # 🎯 修复：处理二分类和三分类概率格式
    if len(meta_model_probas) == 2:
        # 二分类：[下跌概率, 上涨概率]
        prob_text = f"[跌:{meta_model_probas[0]:.2%}, 涨:{meta_model_probas[1]:.2%}]"
    elif len(meta_model_probas) == 3:
        # 三分类：[下跌概率, 上涨概率, 中性概率]
        prob_text = f"[跌:{meta_model_probas[0]:.2%}, 涨:{meta_model_probas[1]:.2%}, 中:{meta_model_probas[2]:.2%}]"
    else:
        # 其他情况
        prob_text = f"{meta_model_probas}"

    # 🎯 增强：构建更详细的GUI显示文本
    from pytz import timezone
    tz = timezone(app_timezone_param) if isinstance(app_timezone_param, str) else app_timezone_param
    final_gui_text = (
        f"🤖 元模型综合决策 ({datetime.now(tz).strftime('%H:%M:%S')})\n"
        f"{'='*50}\n"
        f"📊 元模型输出: {prediction_label_meta}\n"
        f"📈 概率分布: {prob_text}\n"
        f"\n🌍 全局市场状态:\n"
        f"  • ADX趋势强度: {global_market_state.get('global_adx', 0):.1f}\n"
        f"  • ATR波动率: {global_market_state.get('global_atr_percent', 0):.2f}%\n"
        f"  • EMA趋势差: {global_market_state.get('global_ema_diff_pct', 0):.2f}%\n"
        f"  • 趋势强度: {global_market_state.get('global_trend_strength', 0)}\n"
        f"  • 波动等级: {global_market_state.get('global_volatility_level', 0)}\n"
        f"\n⚡ 基础模型贡献:\n"
    )

    # 添加基础模型信息
    for base_name, core_info in all_core_infos_from_bases.items():
        if isinstance(core_info, dict) and not core_info.get('error', False):
            # 🎯 修复：智能检测数据格式并提取概率信息
            p_up, p_down = _extract_probabilities_from_core_info(core_info, base_name)
            signal = core_info.get('internal_signal', 'Unknown')
            final_gui_text += f"  • {base_name}: ↑{p_up:.2%} ↓{p_down:.2%} ({signal})\n"

    final_gui_text += (
        f"\n{'='*50}\n"
        f"🎯 最终决策: {'🚀 发送 ' + actual_signal_to_send + ' 信号' if actual_signal_to_send else '⏸️ 观望等待'}"
    )

    # 🎯 修复：如果有过滤信息，添加到显示中
    if 'filter_result' in locals() and filter_result:
        filter_reasons = filter_result.get('filter_reasons', [])
        if filter_reasons:
            final_gui_text += f"\n🔍 过滤原因: {', '.join(filter_reasons)}"

    # 🎯 修复：直接更新元模型GUI显示
    try:
        from . import gui
        if hasattr(gui, 'update_meta_model_display'):
            # 确定显示颜色
            display_color = None
            if actual_signal_to_send == "UP":
                display_color = getattr(config, 'UP_COLOR', '#26A69A')
            elif actual_signal_to_send == "DOWN":
                display_color = getattr(config, 'DOWN_COLOR', '#EF5350')
            else:
                display_color = getattr(config, 'COLOR_SCHEME', {}).get('text_primary', '#FFFFFF')

            gui.update_meta_model_display(final_gui_text, display_color)
            print(f"✅ 元模型GUI显示已更新")
        else:
            print("⚠️ GUI模块中未找到update_meta_model_display函数")
    except Exception as gui_error:
        print(f"⚠️ 更新元模型GUI显示失败: {gui_error}")

    # 发布元模型预测结果事件
    publish_prediction_result(
        target_name=meta_gui_target_name,
        prediction_label=prediction_label_meta,
        probabilities=meta_model_probas.tolist() if hasattr(meta_model_probas, 'tolist') else list(meta_model_probas),
        confidence=0.0,  # 可以根据需要计算置信度
        signal_sent=actual_signal_to_send
    )
    publish_status_update(f"元模型: {prediction_label_meta.split('(')[0].strip()}", "success" if actual_signal_to_send else "info")

    print("="*27 + " 元模型预测结束 " + "="*28 + "\n")
    return True, final_meta_signal




# --- 计划预测任务 (与你原始版本结构一致) ---
