@echo off
title Simulator - Trading System

echo ========================================
echo    Starting Simulator - Trading System
echo ========================================
echo.

echo [INFO] Starting simulator program...
echo [INFO] Current directory: %CD%
echo.

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not installed or not in PATH
    echo [ERROR] Please install Python and add to system PATH
    pause
    exit /b 1
)

REM Check if SimMain.py exists
if not exist "SimMain.py" (
    echo [ERROR] SimMain.py file not found
    echo [ERROR] Please ensure running in correct project directory
    pause
    exit /b 1
)

echo [INFO] Python environment check passed
echo [INFO] Starting simulator program...
echo.

REM Start simulator program
python SimMain.py

REM Check program exit status
if errorlevel 1 (
    echo.
    echo [ERROR] Simulator program exited abnormally
    echo [ERROR] Please check log files for detailed error information
) else (
    echo.
    echo [INFO] Simulator program exited normally
)

echo.
echo Press any key to close window...
pause >nul
