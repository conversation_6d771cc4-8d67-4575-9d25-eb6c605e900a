# CommandServer 改进功能使用指南

## 概述

CommandServer 已经进行了全面的改进，包括 UUID 生成、改进的错误处理、日志记录、认证机制和生产部署支持。

## 主要改进功能

### 1. UUID 生成指令 ID
- **改进前**: 使用时间戳和计数器生成 ID
- **改进后**: 使用 UUID4 生成全局唯一标识符

```python
# 示例：生成的命令ID
command_id = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
```

### 2. 改进的错误处理和日志记录
- **改进前**: 使用 print 语句输出信息
- **改进后**: 使用 Python logging 模块，支持不同日志级别

```python
# 创建带日志配置的服务器
server = CommandServer(
    host='0.0.0.0',
    port=8080,
    log_level=logging.INFO
)
```

### 3. 明确的金额处理策略
- **改进前**: 简单的类型转换，可能导致错误
- **改进后**: 严格的金额验证和四舍五入处理

```python
# 金额验证示例
valid_amounts = [10, 10.5, "15", None]  # 都会被正确处理
invalid_amounts = [-10, "abc", []]      # 会抛出 ValueError
```

### 4. 认证/授权机制
- **改进前**: 无认证保护
- **改进后**: 支持 API 密钥认证，多种认证方式

```python
# 创建带认证的服务器
server = CommandServer(
    host='0.0.0.0',
    port=8080,
    api_key="your-secret-api-key-here"
)
```

### 5. 生产部署准备
- **改进前**: 仅支持 Flask 开发服务器
- **改进后**: 支持 Gunicorn 生产服务器

```python
# 使用 Gunicorn 运行（生产环境推荐）
server.run_with_gunicorn(workers=4, bind="0.0.0.0:8080")
```

## 使用示例

### 基本使用（开发环境）

```python
from simulation.CommandServer import CommandServer
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

# 创建服务器
server = CommandServer(
    host='0.0.0.0',
    port=8080,
    cleanup_interval_seconds=300,  # 5分钟清理一次
    max_command_age_seconds=3600,  # 命令最多保留1小时
    max_executed_age_seconds=1800  # 已执行命令保留30分钟
)

# 启动服务器
server.run_server()
```

### 生产环境使用

```python
from simulation.CommandServer import CommandServer
import logging

# 配置生产日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('command_server.log', encoding='utf-8')
    ]
)

# 创建带认证的服务器
server = CommandServer(
    host='0.0.0.0',
    port=8080,
    api_key="your-production-api-key",
    cleanup_interval_seconds=300,
    max_command_age_seconds=7200,    # 生产环境保留更长时间
    max_executed_age_seconds=3600,
    log_level=logging.INFO
)

# 使用 Gunicorn 启动（推荐）
try:
    server.run_with_gunicorn(workers=4)
except ImportError:
    # 回退到 Flask 开发服务器
    server.run_server()
```

## API 认证方式

### 1. Authorization 头认证

```bash
curl -X POST http://localhost:8080/internal_signal \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"signal_type": "UP", "amount": 10}'
```

### 2. 查询参数认证

```bash
curl -X POST "http://localhost:8080/internal_signal?api_key=your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"signal_type": "UP", "amount": 10}'
```

### 3. JSON 数据认证

```bash
curl -X POST http://localhost:8080/internal_signal \
  -H "Content-Type: application/json" \
  -d '{"signal_type": "UP", "amount": 10, "api_key": "your-api-key"}'
```

## 错误处理示例

### 金额验证错误

```json
// 请求
{
  "signal_type": "UP",
  "amount": -10
}

// 响应 (400 Bad Request)
{
  "status": "error",
  "message": "金额不能为负数"
}
```

### 认证错误

```json
// 响应 (401 Unauthorized)
{
  "status": "error",
  "message": "未授权访问"
}
```

### 参数验证错误

```json
// 请求
{
  "amount": 10
  // 缺少 signal_type
}

// 响应 (400 Bad Request)
{
  "status": "error",
  "message": "缺少必需参数: signal_type"
}
```

## 监控和统计

### 获取清理统计信息

```bash
curl -X GET "http://localhost:8080/cleanup_stats?api_key=your-api-key"
```

```json
// 响应示例
{
  "status": "success",
  "stats": {
    "total_commands": 15,
    "status_distribution": {
      "queued": 3,
      "sent_to_hamibot": 2,
      "executed_by_hamibot": 10
    },
    "cleanup_config": {
      "cleanup_interval_seconds": 300,
      "max_command_age_seconds": 3600,
      "max_executed_age_seconds": 1800
    },
    "cleanup_thread_alive": true
  }
}
```

## 日志配置

### 自定义日志配置

```python
import logging

# 创建自定义日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 控制台输出
        logging.FileHandler('command_server.log', encoding='utf-8'),  # 文件输出
        logging.handlers.RotatingFileHandler(  # 轮转日志
            'command_server_rotating.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
    ]
)
```

## 生产部署建议

### 1. 使用 Gunicorn

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动命令
gunicorn --workers 4 --bind 0.0.0.0:8080 --timeout 30 \
  --access-logfile access.log --error-logfile error.log \
  "simulation.CommandServer:create_app()"
```

### 2. 使用 Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 使用 systemd 服务

```ini
# /etc/systemd/system/command-server.service
[Unit]
Description=Command Server
After=network.target

[Service]
Type=exec
User=your-user
WorkingDirectory=/path/to/your/project
ExecStart=/path/to/venv/bin/python -m simulation.CommandServer
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

## 安全建议

1. **使用强 API 密钥**: 至少 32 个字符的随机字符串
2. **启用 HTTPS**: 在生产环境中使用 SSL/TLS
3. **限制访问**: 使用防火墙限制访问来源
4. **监控日志**: 定期检查访问日志和错误日志
5. **定期轮换密钥**: 定期更换 API 密钥

## 故障排除

### 常见问题

1. **路由 404 错误**: 确保 `_setup_routes()` 在 `__init__` 中被正确调用
2. **认证失败**: 检查 API 密钥格式和传递方式
3. **金额验证错误**: 确保金额为有效的数字格式
4. **日志文件权限**: 确保应用有写入日志文件的权限
5. **端口占用**: 检查端口是否被其他进程占用

### 调试模式

```python
# 启用详细日志
server = CommandServer(
    host='127.0.0.1',
    port=8080,
    log_level=logging.DEBUG  # 启用调试日志
)
```

这些改进使 CommandServer 更加健壮、安全和适合生产环境使用。
