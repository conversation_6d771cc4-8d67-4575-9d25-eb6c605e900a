<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一综合日志系统 - 实时监控仪表板</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background-color: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .alert-item {
            border-left: 4px solid;
            margin-bottom: 0.5rem;
        }
        
        .alert-high {
            border-left-color: #dc3545;
        }
        
        .alert-medium {
            border-left-color: #ffc107;
        }
        
        .alert-low {
            border-left-color: #17a2b8;
        }
        
        .chart-container {
            height: 400px;
            margin-bottom: 2rem;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-chart-line"></i> 统一综合日志系统</h1>
                    <p class="mb-0">实时监控仪表板</p>
                </div>
                <div class="col-md-6 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <span class="me-3">
                            <span id="connection-status" class="status-indicator status-offline"></span>
                            <span id="connection-text">连接中...</span>
                        </span>
                        <button id="refresh-btn" class="btn btn-light btn-sm">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 概览指标 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value text-primary" id="total-trades">-</div>
                    <div class="metric-label">总交易数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value text-success" id="win-rate">-</div>
                    <div class="metric-label">胜率 (%)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value text-info" id="daily-pnl">-</div>
                    <div class="metric-label">今日盈亏</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value text-warning" id="consecutive-losses">-</div>
                    <div class="metric-label">连续亏损</div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧：图表区域 -->
            <div class="col-md-8">
                <!-- 累积盈亏图表 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-chart-area"></i> 累积盈亏曲线</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" data-days="7">7天</button>
                            <button type="button" class="btn btn-outline-primary" data-days="30">30天</button>
                            <button type="button" class="btn btn-outline-primary" data-days="90">90天</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="pnl-chart" class="chart-container"></div>
                    </div>
                </div>

                <!-- 策略表现 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-trophy"></i> 策略表现排行</h5>
                    </div>
                    <div class="card-body">
                        <div id="strategy-performance" class="loading">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：监控和告警 -->
            <div class="col-md-4">
                <!-- 实时监控状态 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-eye"></i> 实时监控</h5>
                        <div>
                            <button id="start-monitoring" class="btn btn-success btn-sm">
                                <i class="fas fa-play"></i> 启动
                            </button>
                            <button id="stop-monitoring" class="btn btn-danger btn-sm">
                                <i class="fas fa-stop"></i> 停止
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="monitoring-status">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> 加载监控状态...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时告警 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle"></i> 实时告警</h5>
                    </div>
                    <div class="card-body">
                        <div id="alerts-container">
                            <div class="text-muted text-center">
                                <i class="fas fa-shield-alt"></i><br>
                                暂无告警
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> 系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-info">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> 加载系统信息...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> 最近交易记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="trades-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>策略</th>
                                        <th>方向</th>
                                        <th>开仓价</th>
                                        <th>平仓价</th>
                                        <th>金额</th>
                                        <th>结果</th>
                                        <th>盈亏</th>
                                    </tr>
                                </thead>
                                <tbody id="trades-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <i class="fas fa-spinner fa-spin"></i> 加载交易数据...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let socket;
        let currentDays = 7;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadInitialData();
            setupEventListeners();
        });
        
        // 初始化Socket连接
        function initializeSocket() {
            socket = io();
            
            socket.on('connect', function() {
                updateConnectionStatus(true);
                console.log('WebSocket连接成功');
            });
            
            socket.on('disconnect', function() {
                updateConnectionStatus(false);
                console.log('WebSocket连接断开');
            });
            
            socket.on('data_update', function(data) {
                if (data.type === 'overview') {
                    updateOverviewMetrics(data.data);
                }
            });
            
            socket.on('alert', function(alert) {
                addAlert(alert);
            });
            
            socket.on('error', function(error) {
                console.error('WebSocket错误:', error);
                showNotification('连接错误: ' + error.message, 'danger');
            });
        }
        
        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusIndicator = document.getElementById('connection-status');
            const statusText = document.getElementById('connection-text');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-online';
                statusText.textContent = '已连接';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = '连接断开';
            }
        }
        
        // 加载初始数据
        function loadInitialData() {
            loadOverviewData();
            loadPnlChart(currentDays);
            loadTradesData();
            loadStrategyPerformance();
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 刷新按钮
            document.getElementById('refresh-btn').addEventListener('click', function() {
                loadInitialData();
                socket.emit('request_update');
            });
            
            // 天数选择按钮
            document.querySelectorAll('[data-days]').forEach(button => {
                button.addEventListener('click', function() {
                    // 更新按钮状态
                    document.querySelectorAll('[data-days]').forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新图表
                    currentDays = parseInt(this.dataset.days);
                    loadPnlChart(currentDays);
                });
            });
            
            // 监控控制按钮
            document.getElementById('start-monitoring').addEventListener('click', function() {
                fetch('/api/monitoring/start', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('监控已启动', 'success');
                        } else {
                            showNotification('启动监控失败: ' + data.error, 'danger');
                        }
                    });
            });
            
            document.getElementById('stop-monitoring').addEventListener('click', function() {
                fetch('/api/monitoring/stop', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('监控已停止', 'warning');
                        } else {
                            showNotification('停止监控失败: ' + data.error, 'danger');
                        }
                    });
            });
        }
        
        // 加载概览数据
        function loadOverviewData() {
            fetch('/api/overview')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateOverviewMetrics(data.data);
                    }
                })
                .catch(error => console.error('加载概览数据失败:', error));
        }
        
        // 更新概览指标
        function updateOverviewMetrics(data) {
            const basicStats = data.basic_stats || {};
            const monitoringData = data.monitoring_data || {};
            
            document.getElementById('total-trades').textContent = basicStats.total_entries || 0;
            document.getElementById('win-rate').textContent = (monitoringData.current_win_rate || 0).toFixed(1);
            document.getElementById('daily-pnl').textContent = (monitoringData.daily_pnl || 0).toFixed(2);
            document.getElementById('consecutive-losses').textContent = monitoringData.consecutive_losses || 0;
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            // 这里可以实现通知显示逻辑
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 添加告警
        function addAlert(alert) {
            const alertsContainer = document.getElementById('alerts-container');
            
            // 如果是第一个告警，清空"暂无告警"提示
            if (alertsContainer.querySelector('.text-muted')) {
                alertsContainer.innerHTML = '';
            }
            
            const alertElement = document.createElement('div');
            alertElement.className = `alert alert-${alert.severity === 'high' ? 'danger' : alert.severity === 'medium' ? 'warning' : 'info'} alert-item`;
            alertElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${alert.type}</strong><br>
                        <small>${alert.message}</small>
                    </div>
                    <small class="text-muted">${new Date(alert.timestamp).toLocaleTimeString()}</small>
                </div>
            `;
            
            // 插入到顶部
            alertsContainer.insertBefore(alertElement, alertsContainer.firstChild);
            
            // 限制显示的告警数量
            const alerts = alertsContainer.querySelectorAll('.alert-item');
            if (alerts.length > 5) {
                alerts[alerts.length - 1].remove();
            }
        }
        
        // 加载盈亏图表
        function loadPnlChart(days) {
            fetch(`/api/charts/pnl_chart/${days}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Plotly.newPlot('pnl-chart', data.data.data, data.data.layout, {responsive: true});
                    } else {
                        document.getElementById('pnl-chart').innerHTML = `
                            <div class="text-center text-muted">
                                <i class="fas fa-chart-line fa-3x mb-3"></i><br>
                                暂无数据
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载盈亏图表失败:', error);
                    document.getElementById('pnl-chart').innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i><br>
                            加载失败
                        </div>
                    `;
                });
        }
        
        // 加载交易数据
        function loadTradesData() {
            fetch(`/api/trades?days=${currentDays}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateTradesTable(data.data);
                    }
                })
                .catch(error => console.error('加载交易数据失败:', error));
        }
        
        // 更新交易表格
        function updateTradesTable(trades) {
            const tbody = document.getElementById('trades-tbody');
            
            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无交易数据</td></tr>';
                return;
            }
            
            tbody.innerHTML = trades.slice(0, 20).map(trade => `
                <tr>
                    <td>${new Date(trade.entry_timestamp).toLocaleString()}</td>
                    <td>${trade.target_name || '-'}</td>
                    <td><span class="badge bg-${trade.direction === 'LONG' ? 'success' : 'danger'}">${trade.direction}</span></td>
                    <td>${(trade.entry_price || 0).toFixed(2)}</td>
                    <td>${(trade.exit_price || 0).toFixed(2)}</td>
                    <td>${(trade.amount || 0).toFixed(2)}</td>
                    <td><span class="badge bg-${trade.result === 'WIN' ? 'success' : 'danger'}">${trade.result}</span></td>
                    <td class="text-${(trade.profit_loss || 0) >= 0 ? 'success' : 'danger'}">
                        ${(trade.profit_loss || 0).toFixed(2)}
                    </td>
                </tr>
            `).join('');
        }
        
        // 加载策略表现
        function loadStrategyPerformance() {
            fetch(`/api/performance/${currentDays}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.strategy_performance) {
                        updateStrategyPerformance(data.data.strategy_performance);
                    }
                })
                .catch(error => console.error('加载策略表现失败:', error));
        }
        
        // 更新策略表现
        function updateStrategyPerformance(strategyData) {
            const container = document.getElementById('strategy-performance');
            const rankings = strategyData.strategy_rankings || {};
            
            if (Object.keys(rankings).length === 0) {
                container.innerHTML = '<div class="text-center text-muted">暂无策略数据</div>';
                return;
            }
            
            const html = Object.entries(rankings).slice(0, 5).map(([strategy, stats], index) => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                    <div>
                        <span class="badge bg-primary me-2">${index + 1}</span>
                        <strong>${strategy}</strong>
                    </div>
                    <div class="text-end">
                        <div class="text-${stats.total_pnl >= 0 ? 'success' : 'danger'}">
                            ${stats.total_pnl.toFixed(2)}
                        </div>
                        <small class="text-muted">${stats.win_rate.toFixed(1)}%</small>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 定期更新数据
        setInterval(() => {
            if (socket && socket.connected) {
                socket.emit('request_update');
            }
        }, 30000); // 每30秒更新一次
    </script>
</body>
</html>
