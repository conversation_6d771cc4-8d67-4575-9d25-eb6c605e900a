#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8.0专家委员会系统配置示例

这个文件展示了如何配置V8.0专家委员会系统。
您可以将这些配置添加到您的目标配置中以启用专家委员会模式。
"""

# V8.0专家委员会系统配置示例
EXPERT_COMMITTEE_CONFIG_EXAMPLE = {
    # ===== 核心开关 =====
    'enable_expert_committee': True,  # 启用专家委员会模式
    
    # ===== 专家系统技术指标参数 =====
    'expert_system_atr_period': 14,        # ATR周期（波动率计算）
    'expert_system_adx_period': 14,        # ADX周期（趋势强度计算）
    'expert_system_ema_fast': 12,          # 快速EMA周期
    'expert_system_ema_slow': 26,          # 慢速EMA周期
    
    # ===== 市场状态识别阈值 =====
    'expert_system_high_vol_threshold': 2.0,      # 高波动阈值（ATR%）
    'expert_system_low_trend_threshold': 25,      # 低趋势强度阈值（ADX）
    'expert_system_strong_trend_threshold': 35,   # 强趋势阈值（ADX）
    'expert_system_strong_up_threshold': 0.8,     # 强上升趋势阈值（EMA差值%）
    'expert_system_strong_down_threshold': -0.8,  # 强下降趋势阈值（EMA差值%）
    
    # ===== 标准训练配置 =====
    'cv_folds': 3,                          # 交叉验证折数
    'model_save_dir': 'models/expert_committee',  # 模型保存目录
    'objective': 'binary',                  # 目标函数
    'metric': 'binary_logloss',            # 评估指标
    'n_estimators': 1000,                  # 树的数量
    'random_state': 42,                    # 随机种子
    
    # ===== 其他配置 =====
    'signal_threshold': 0.6,               # 信号阈值
    'enable_mtfa': True,                   # 启用多时间框架分析
    'rfe_enable': False,                   # 禁用RFE（专家模型使用简化特征选择）
    'importance_thresholding_enable': False,  # 禁用重要性筛选
    'optuna_enable': False,                # 禁用Optuna（专家模型使用预设参数）
}

# 完整的目标配置示例（集成专家委员会）
BTCUSDT_UP_EXPERT_CONFIG = {
    'name': 'BTC_UP_15m_Expert',
    'symbol': 'BTCUSDT',
    'interval': '15m',
    'target_variable_type': 'UP_ONLY',
    'prediction_periods': [1],
    'prediction_minutes_display': 15,
    
    # 🎯 启用V8.0专家委员会系统
    **EXPERT_COMMITTEE_CONFIG_EXAMPLE,
    
    # 目标变量配置
    'threshold_up': 0.3,
    'threshold_down': -0.3,
    'enable_dynamic_thresholds': True,
    
    # 特征工程配置
    'enable_mtfa': True,
    'mtfa_timeframes': ['1h', '4h'],
    'enable_market_state_adaptive': True,
    
    # 数据配置
    'train_ratio': 0.7,
    'validation_ratio': 0.15,
    'min_train_samples': 100,
    'min_val_test_samples': 50,
}

# 使用说明
USAGE_INSTRUCTIONS = """
V8.0专家委员会系统使用说明：

1. 基本启用：
   在您的目标配置中添加：
   'enable_expert_committee': True

2. 自定义参数（可选）：
   添加以上任何 'expert_system_*' 参数来自定义系统行为

3. 训练流程：
   - 系统会自动识别市场状态
   - 为每种状态训练专门的专家模型
   - 保存专家模型到指定目录

4. 预测流程：
   - 系统会实时判断当前市场状态
   - 自动加载对应的专家模型
   - 在高波动盘整状态下过滤所有信号

5. 专家状态说明：
   - strong_trend_up: 强上升趋势专家
   - strong_trend_down: 强下降趋势专家
   - low_vol_sideways: 低波动盘整专家
   - high_vol_sideways: 危险状态，过滤信号

6. 文件结构：
   models/
   ├── expert_[target]_[state]_fold_[n].joblib  # 专家模型
   ├── expert_scaler_[target]_[state].joblib    # 专家缩放器
   ├── expert_features_[target]_[state].json    # 专家特征列表
   └── expert_metadata_[target]_[state].json    # 专家元数据

7. 监控和调试：
   - 查看日志中的市场状态识别信息
   - 检查专家模型训练结果
   - 观察预测时的专家调度情况
"""

if __name__ == "__main__":
    import json
    
    print("🎯 V8.0专家委员会系统配置示例")
    print("=" * 60)
    
    print("\n📋 基础配置：")
    print(json.dumps(EXPERT_COMMITTEE_CONFIG_EXAMPLE, indent=2, ensure_ascii=False))
    
    print("\n📋 完整目标配置示例：")
    print(json.dumps(BTCUSDT_UP_EXPERT_CONFIG, indent=2, ensure_ascii=False))
    
    print("\n📖 使用说明：")
    print(USAGE_INSTRUCTIONS)
