#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📡 实时日志监控系统 V1.0
为统一综合日志系统提供实时监控和告警功能

核心功能：
- 实时监控交易表现
- 自动风险告警
- 性能指标跟踪
- 异常检测
- 邮件/消息通知
"""

import time
import threading
import queue
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
import logging
from pathlib import Path
import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart


class RealtimeLogMonitor:
    """实时日志监控器"""
    
    def __init__(self, comprehensive_logs_dir: str = "comprehensive_logs"):
        self.logs_dir = Path(comprehensive_logs_dir)
        self.logger = logging.getLogger(f"{__name__}.RealtimeLogMonitor")
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # 监控配置
        self.config = {
            'check_interval': 30,  # 检查间隔（秒）
            'max_drawdown_threshold': -100.0,  # 最大回撤告警阈值
            'consecutive_losses_threshold': 5,  # 连续亏损告警阈值
            'win_rate_threshold': 30.0,  # 胜率告警阈值（%）
            'daily_loss_threshold': -200.0,  # 日损失告警阈值
            'enable_email_alerts': False,  # 是否启用邮件告警
            'email_config': {
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'recipients': []
            }
        }
        
        # 监控数据
        self.monitoring_data = {
            'last_check_time': None,
            'total_trades': 0,
            'daily_pnl': 0.0,
            'current_drawdown': 0.0,
            'consecutive_losses': 0,
            'current_win_rate': 0.0,
            'alerts_sent': []
        }
        
        # 告警回调函数
        self.alert_callbacks: List[Callable] = []
        
        self.logger.info("实时日志监控器初始化完成")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("监控已在运行中")
            return
        
        self.is_monitoring = True
        self.stop_event.clear()
        
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            name="LogMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("实时监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("实时监控已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        self.logger.info("监控循环开始")
        
        while not self.stop_event.is_set():
            try:
                # 执行监控检查
                self._perform_monitoring_check()
                
                # 等待下次检查
                self.stop_event.wait(self.config['check_interval'])
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(10)  # 异常后等待10秒再继续
        
        self.logger.info("监控循环结束")
    
    def _perform_monitoring_check(self):
        """执行监控检查"""
        try:
            # 加载最新数据
            current_data = self._load_current_data()
            
            if not current_data:
                return
            
            # 更新监控数据
            self._update_monitoring_data(current_data)
            
            # 检查告警条件
            alerts = self._check_alert_conditions()
            
            # 处理告警
            for alert in alerts:
                self._handle_alert(alert)
            
            # 记录监控状态
            self._log_monitoring_status()
            
        except Exception as e:
            self.logger.error(f"监控检查失败: {e}")
    
    def _load_current_data(self) -> Optional[Dict[str, Any]]:
        """加载当前数据"""
        try:
            # 加载今日交易数据
            today = datetime.now().date()
            trades_data = self._load_trades_for_date(today)
            
            if not trades_data:
                return None
            
            # 计算关键指标
            total_trades = len(trades_data)
            winning_trades = len([t for t in trades_data if t.get('result') == 'WIN'])
            losing_trades = len([t for t in trades_data if t.get('result') == 'LOSS'])
            
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            daily_pnl = sum(t.get('profit_loss', 0) for t in trades_data)
            
            # 计算连续亏损
            consecutive_losses = self._calculate_consecutive_losses(trades_data)
            
            # 计算当前回撤
            current_drawdown = self._calculate_current_drawdown(trades_data)
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'daily_pnl': daily_pnl,
                'consecutive_losses': consecutive_losses,
                'current_drawdown': current_drawdown,
                'last_trade_time': trades_data[-1].get('entry_timestamp') if trades_data else None
            }
            
        except Exception as e:
            self.logger.error(f"加载当前数据失败: {e}")
            return None
    
    def _load_trades_for_date(self, date) -> List[Dict[str, Any]]:
        """加载指定日期的交易数据"""
        trades_data = []
        
        try:
            year = str(date.year)
            month = f"{date.month:02d}"
            filename = f"trades_{date.strftime('%Y-%m-%d')}.csv"
            
            file_path = self.logs_dir / "trades" / year / month / filename
            
            if file_path.exists():
                import pandas as pd
                df = pd.read_csv(file_path)
                trades_data = df.to_dict('records')
            
        except Exception as e:
            self.logger.error(f"加载日期 {date} 的交易数据失败: {e}")
        
        return trades_data
    
    def _calculate_consecutive_losses(self, trades_data: List[Dict[str, Any]]) -> int:
        """计算连续亏损次数"""
        if not trades_data:
            return 0
        
        # 按时间排序
        sorted_trades = sorted(trades_data, key=lambda x: x.get('entry_timestamp', ''))
        
        consecutive = 0
        for trade in reversed(sorted_trades):  # 从最新的开始
            if trade.get('result') == 'LOSS':
                consecutive += 1
            else:
                break
        
        return consecutive
    
    def _calculate_current_drawdown(self, trades_data: List[Dict[str, Any]]) -> float:
        """计算当前回撤"""
        if not trades_data:
            return 0.0
        
        # 按时间排序
        sorted_trades = sorted(trades_data, key=lambda x: x.get('entry_timestamp', ''))
        
        cumulative_pnl = 0.0
        max_pnl = 0.0
        current_drawdown = 0.0
        
        for trade in sorted_trades:
            cumulative_pnl += trade.get('profit_loss', 0)
            max_pnl = max(max_pnl, cumulative_pnl)
            current_drawdown = min(current_drawdown, cumulative_pnl - max_pnl)
        
        return current_drawdown
    
    def _update_monitoring_data(self, current_data: Dict[str, Any]):
        """更新监控数据"""
        self.monitoring_data.update({
            'last_check_time': datetime.now(),
            'total_trades': current_data['total_trades'],
            'daily_pnl': current_data['daily_pnl'],
            'current_drawdown': current_data['current_drawdown'],
            'consecutive_losses': current_data['consecutive_losses'],
            'current_win_rate': current_data['win_rate']
        })
    
    def _check_alert_conditions(self) -> List[Dict[str, Any]]:
        """检查告警条件"""
        alerts = []
        
        # 检查最大回撤
        if self.monitoring_data['current_drawdown'] <= self.config['max_drawdown_threshold']:
            alerts.append({
                'type': 'max_drawdown',
                'severity': 'high',
                'message': f"最大回撤超过阈值: {self.monitoring_data['current_drawdown']:.2f}",
                'value': self.monitoring_data['current_drawdown'],
                'threshold': self.config['max_drawdown_threshold']
            })
        
        # 检查连续亏损
        if self.monitoring_data['consecutive_losses'] >= self.config['consecutive_losses_threshold']:
            alerts.append({
                'type': 'consecutive_losses',
                'severity': 'medium',
                'message': f"连续亏损次数超过阈值: {self.monitoring_data['consecutive_losses']}",
                'value': self.monitoring_data['consecutive_losses'],
                'threshold': self.config['consecutive_losses_threshold']
            })
        
        # 检查胜率
        if (self.monitoring_data['total_trades'] >= 10 and 
            self.monitoring_data['current_win_rate'] <= self.config['win_rate_threshold']):
            alerts.append({
                'type': 'low_win_rate',
                'severity': 'medium',
                'message': f"胜率过低: {self.monitoring_data['current_win_rate']:.2f}%",
                'value': self.monitoring_data['current_win_rate'],
                'threshold': self.config['win_rate_threshold']
            })
        
        # 检查日损失
        if self.monitoring_data['daily_pnl'] <= self.config['daily_loss_threshold']:
            alerts.append({
                'type': 'daily_loss',
                'severity': 'high',
                'message': f"日损失超过阈值: {self.monitoring_data['daily_pnl']:.2f}",
                'value': self.monitoring_data['daily_pnl'],
                'threshold': self.config['daily_loss_threshold']
            })
        
        return alerts
    
    def _handle_alert(self, alert: Dict[str, Any]):
        """处理告警"""
        alert_id = f"{alert['type']}_{datetime.now().strftime('%Y%m%d_%H%M')}"
        
        # 避免重复告警
        if alert_id in self.monitoring_data['alerts_sent']:
            return
        
        self.monitoring_data['alerts_sent'].append(alert_id)
        
        # 记录告警
        self.logger.warning(f"告警触发: {alert['message']}")
        
        # 调用回调函数
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调函数执行失败: {e}")
        
        # 发送邮件告警
        if self.config['enable_email_alerts']:
            self._send_email_alert(alert)
        
        # 保存告警记录
        self._save_alert_record(alert)
    
    def _send_email_alert(self, alert: Dict[str, Any]):
        """发送邮件告警"""
        try:
            email_config = self.config['email_config']
            
            if not email_config['username'] or not email_config['recipients']:
                return
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = f"交易系统告警 - {alert['type']}"
            
            # 邮件内容
            body = f"""
交易系统告警通知

告警类型: {alert['type']}
严重程度: {alert['severity']}
告警信息: {alert['message']}
当前值: {alert['value']}
阈值: {alert['threshold']}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

请及时检查交易系统状态。
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"邮件告警已发送: {alert['type']}")
            
        except Exception as e:
            self.logger.error(f"发送邮件告警失败: {e}")
    
    def _save_alert_record(self, alert: Dict[str, Any]):
        """保存告警记录"""
        try:
            alerts_dir = self.logs_dir / "alerts"
            alerts_dir.mkdir(exist_ok=True)
            
            alert_record = {
                'timestamp': datetime.now().isoformat(),
                'alert': alert,
                'monitoring_data': self.monitoring_data.copy()
            }
            
            today = datetime.now().strftime('%Y-%m-%d')
            alert_file = alerts_dir / f"alerts_{today}.json"
            
            # 追加到文件
            alerts = []
            if alert_file.exists():
                with open(alert_file, 'r', encoding='utf-8') as f:
                    alerts = json.load(f)
            
            alerts.append(alert_record)
            
            with open(alert_file, 'w', encoding='utf-8') as f:
                json.dump(alerts, f, ensure_ascii=False, indent=2, default=str)
            
        except Exception as e:
            self.logger.error(f"保存告警记录失败: {e}")
    
    def _log_monitoring_status(self):
        """记录监控状态"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'monitoring_data': self.monitoring_data.copy()
        }
        
        self.logger.debug(f"监控状态: {json.dumps(status, default=str, ensure_ascii=False)}")
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def update_config(self, config_updates: Dict[str, Any]):
        """更新监控配置"""
        self.config.update(config_updates)
        self.logger.info(f"监控配置已更新: {config_updates}")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'is_monitoring': self.is_monitoring,
            'config': self.config.copy(),
            'monitoring_data': self.monitoring_data.copy(),
            'last_check_time': self.monitoring_data.get('last_check_time')
        }
    
    def force_check(self) -> Dict[str, Any]:
        """强制执行一次检查"""
        try:
            self._perform_monitoring_check()
            return {'success': True, 'message': '检查完成'}
        except Exception as e:
            return {'success': False, 'error': str(e)}


# 全局监控器实例
_global_monitor = None
_monitor_lock = threading.Lock()


def get_realtime_monitor(comprehensive_logs_dir: str = "comprehensive_logs") -> RealtimeLogMonitor:
    """获取全局实时监控器实例（单例模式）"""
    global _global_monitor
    
    with _monitor_lock:
        if _global_monitor is None:
            _global_monitor = RealtimeLogMonitor(comprehensive_logs_dir)
        
        return _global_monitor


def reset_realtime_monitor():
    """重置全局监控器"""
    global _global_monitor
    
    with _monitor_lock:
        if _global_monitor:
            _global_monitor.stop_monitoring()
        _global_monitor = None
