#!/usr/bin/env python3
"""
性能监控器 - 监控训练过程中的性能指标
记录执行时间、内存使用、瓶颈检测等
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, enable_memory_monitoring: bool = True):
        """
        初始化性能监控器
        
        Args:
            enable_memory_monitoring: 是否启用内存监控
        """
        self.enable_memory_monitoring = enable_memory_monitoring
        self._lock = threading.RLock()
        
        # 性能记录
        self._performance_records = {}
        self._stage_timings = {}
        self._memory_snapshots = {}
        self._bottlenecks = []
        
        # 当前监控状态
        self._current_stage = None
        self._stage_start_time = None
        self._session_start_time = time.time()
        
        # 统计信息
        self._stats = {
            'total_stages': 0,
            'total_time': 0,
            'peak_memory_mb': 0,
            'avg_memory_mb': 0,
            'bottleneck_count': 0
        }
        
        logger.info("性能监控器初始化完成")
    
    @contextmanager
    def monitor_stage(self, stage_name: str, target_name: Optional[str] = None):
        """
        监控特定阶段的性能
        
        Args:
            stage_name: 阶段名称
            target_name: 目标名称（可选）
        """
        full_stage_name = f"{target_name}_{stage_name}" if target_name else stage_name
        
        with self._lock:
            self._current_stage = full_stage_name
            self._stage_start_time = time.time()
            
            # 记录开始时的内存状态
            if self.enable_memory_monitoring:
                start_memory = self._get_memory_usage()
                self._memory_snapshots[f"{full_stage_name}_start"] = start_memory
            
            logger.debug(f"开始监控阶段: {full_stage_name}")
        
        try:
            yield self
        finally:
            with self._lock:
                # 计算阶段耗时
                stage_duration = time.time() - self._stage_start_time
                
                # 记录结束时的内存状态
                if self.enable_memory_monitoring:
                    end_memory = self._get_memory_usage()
                    self._memory_snapshots[f"{full_stage_name}_end"] = end_memory
                    memory_delta = end_memory['rss_mb'] - self._memory_snapshots[f"{full_stage_name}_start"]['rss_mb']
                else:
                    memory_delta = 0
                
                # 存储性能记录
                self._performance_records[full_stage_name] = {
                    'duration': stage_duration,
                    'memory_delta_mb': memory_delta,
                    'timestamp': datetime.now(),
                    'target_name': target_name,
                    'stage_name': stage_name
                }
                
                # 更新统计信息
                self._stats['total_stages'] += 1
                self._stats['total_time'] += stage_duration
                
                if self.enable_memory_monitoring:
                    current_memory = end_memory['rss_mb']
                    if current_memory > self._stats['peak_memory_mb']:
                        self._stats['peak_memory_mb'] = current_memory
                
                # 检测瓶颈
                self._detect_bottleneck(full_stage_name, stage_duration, memory_delta)
                
                logger.debug(f"完成监控阶段: {full_stage_name}, 耗时: {stage_duration:.2f}秒, "
                           f"内存变化: {memory_delta:+.1f}MB")
                
                self._current_stage = None
                self._stage_start_time = None
    
    def _get_memory_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss_mb': memory_info.rss / (1024 * 1024),
                'vms_mb': memory_info.vms / (1024 * 1024),
                'percent': process.memory_percent()
            }
        except Exception as e:
            logger.warning(f"获取内存使用情况失败: {e}")
            return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0}
    
    def _detect_bottleneck(self, stage_name: str, duration: float, memory_delta: float):
        """检测性能瓶颈"""
        # 时间瓶颈检测
        if duration > 30:  # 超过30秒
            bottleneck = {
                'type': 'time',
                'stage': stage_name,
                'duration': duration,
                'severity': 'high' if duration > 60 else 'medium',
                'timestamp': datetime.now()
            }
            self._bottlenecks.append(bottleneck)
            self._stats['bottleneck_count'] += 1
            logger.warning(f"检测到时间瓶颈: {stage_name}, 耗时: {duration:.2f}秒")
        
        # 内存瓶颈检测
        if abs(memory_delta) > 500:  # 内存变化超过500MB
            bottleneck = {
                'type': 'memory',
                'stage': stage_name,
                'memory_delta_mb': memory_delta,
                'severity': 'high' if abs(memory_delta) > 1000 else 'medium',
                'timestamp': datetime.now()
            }
            self._bottlenecks.append(bottleneck)
            self._stats['bottleneck_count'] += 1
            logger.warning(f"检测到内存瓶颈: {stage_name}, 内存变化: {memory_delta:+.1f}MB")
    
    def record_custom_metric(self, metric_name: str, value: float, target_name: Optional[str] = None):
        """
        记录自定义性能指标
        
        Args:
            metric_name: 指标名称
            value: 指标值
            target_name: 目标名称（可选）
        """
        with self._lock:
            full_metric_name = f"{target_name}_{metric_name}" if target_name else metric_name
            
            if 'custom_metrics' not in self._performance_records:
                self._performance_records['custom_metrics'] = {}
            
            self._performance_records['custom_metrics'][full_metric_name] = {
                'value': value,
                'timestamp': datetime.now(),
                'target_name': target_name,
                'metric_name': metric_name
            }
            
            logger.debug(f"记录自定义指标: {full_metric_name} = {value}")
    
    def get_stage_performance(self, stage_name: str) -> Optional[Dict[str, Any]]:
        """获取特定阶段的性能数据"""
        with self._lock:
            return self._performance_records.get(stage_name)
    
    def get_all_performance_data(self) -> Dict[str, Any]:
        """获取所有性能数据"""
        with self._lock:
            return {
                'records': self._performance_records.copy(),
                'stats': self._stats.copy(),
                'bottlenecks': self._bottlenecks.copy(),
                'session_duration': time.time() - self._session_start_time
            }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            # 计算平均内存使用
            if self.enable_memory_monitoring and self._memory_snapshots:
                memory_values = [snapshot['rss_mb'] for snapshot in self._memory_snapshots.values()]
                avg_memory = sum(memory_values) / len(memory_values)
                self._stats['avg_memory_mb'] = avg_memory
            
            # 找出最耗时的阶段
            slowest_stages = sorted(
                [(name, record['duration']) for name, record in self._performance_records.items() 
                 if isinstance(record, dict) and 'duration' in record],
                key=lambda x: x[1], reverse=True
            )[:5]
            
            # 找出内存使用最多的阶段
            memory_intensive_stages = []
            if self.enable_memory_monitoring:
                memory_intensive_stages = sorted(
                    [(name, record['memory_delta_mb']) for name, record in self._performance_records.items() 
                     if isinstance(record, dict) and 'memory_delta_mb' in record],
                    key=lambda x: abs(x[1]), reverse=True
                )[:5]
            
            return {
                'total_stages': self._stats['total_stages'],
                'total_time': self._stats['total_time'],
                'avg_stage_time': self._stats['total_time'] / max(1, self._stats['total_stages']),
                'peak_memory_mb': self._stats['peak_memory_mb'],
                'avg_memory_mb': self._stats['avg_memory_mb'],
                'bottleneck_count': self._stats['bottleneck_count'],
                'slowest_stages': slowest_stages,
                'memory_intensive_stages': memory_intensive_stages,
                'session_duration': time.time() - self._session_start_time
            }
    
    def print_performance_report(self):
        """打印性能报告"""
        summary = self.get_performance_summary()
        
        print(f"\n📊 性能监控报告")
        print(f"=" * 50)
        print(f"会话总时长: {summary['session_duration']:.2f}秒")
        print(f"监控阶段数: {summary['total_stages']}")
        print(f"总执行时间: {summary['total_time']:.2f}秒")
        print(f"平均阶段时间: {summary['avg_stage_time']:.2f}秒")
        
        if self.enable_memory_monitoring:
            print(f"峰值内存使用: {summary['peak_memory_mb']:.1f}MB")
            print(f"平均内存使用: {summary['avg_memory_mb']:.1f}MB")
        
        print(f"检测到瓶颈: {summary['bottleneck_count']}个")
        
        if summary['slowest_stages']:
            print(f"\n🐌 最耗时的阶段:")
            for i, (stage, duration) in enumerate(summary['slowest_stages'], 1):
                print(f"  {i}. {stage}: {duration:.2f}秒")
        
        if summary['memory_intensive_stages']:
            print(f"\n💾 内存使用最多的阶段:")
            for i, (stage, memory_delta) in enumerate(summary['memory_intensive_stages'], 1):
                print(f"  {i}. {stage}: {memory_delta:+.1f}MB")
        
        if self._bottlenecks:
            print(f"\n⚠️  检测到的瓶颈:")
            for bottleneck in self._bottlenecks[-5:]:  # 显示最近5个瓶颈
                if bottleneck['type'] == 'time':
                    print(f"  时间瓶颈: {bottleneck['stage']} ({bottleneck['duration']:.2f}秒)")
                elif bottleneck['type'] == 'memory':
                    print(f"  内存瓶颈: {bottleneck['stage']} ({bottleneck['memory_delta_mb']:+.1f}MB)")
    
    def reset(self):
        """重置监控器"""
        with self._lock:
            self._performance_records.clear()
            self._stage_timings.clear()
            self._memory_snapshots.clear()
            self._bottlenecks.clear()
            
            self._stats = {
                'total_stages': 0,
                'total_time': 0,
                'peak_memory_mb': 0,
                'avg_memory_mb': 0,
                'bottleneck_count': 0
            }
            
            self._session_start_time = time.time()
            logger.info("性能监控器已重置")


# 全局性能监控器实例
_global_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _global_performance_monitor
    if _global_performance_monitor is None:
        _global_performance_monitor = PerformanceMonitor()
    return _global_performance_monitor
