# -*- coding: utf-8 -*-
"""
Optuna 集成模块
将优化的 Optuna 目标函数集成到现有预测系统中
"""

import logging
import numpy as np
import pandas as pd
import json
import os
from datetime import datetime, timezone
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
import optuna

# 导入优化的 Optuna 模块
from .optuna_optimizer import (
    OptimizedOptunaObjective,
    OptimizationConstraints,
    create_optimized_study,
    analyze_study_results
)

# 导入配置
try:
    import config
except ImportError:
    config = None


def create_threshold_optimization_constraints() -> OptimizationConstraints:
    """
    从配置文件创建阈值优化约束
    
    Returns:
        OptimizationConstraints: 约束配置对象
    """
    if config is None:
        # 使用默认约束
        return OptimizationConstraints()
    
    return OptimizationConstraints(
        min_trades=getattr(config, 'META_MODEL_MIN_TRADES_CONSTRAINT', 20),
        min_win_rate=getattr(config, 'META_MODEL_MIN_WIN_RATE_CONSTRAINT', 0.4),
        max_consecutive_losses=getattr(config, 'META_MODEL_MAX_CONSECUTIVE_LOSSES', 10),
        min_profit_per_trade=0.0,
        require_trade_balance=True,
        trade_balance_min_ratio=0.1,
        # 🎯 关键改进：使用有限惩罚值而不是 -inf
        constraint_penalty_base=getattr(config, 'OPTUNA_CONSTRAINT_PENALTY_BASE', -1000.0),
        constraint_penalty_multiplier=getattr(config, 'OPTUNA_CONSTRAINT_PENALTY_MULTIPLIER', 10.0)
    )


def optimize_meta_model_thresholds(y_true_meta_val: np.ndarray,
                                 y_proba_meta_val: np.ndarray,
                                 calculate_metrics_func: Callable,
                                 n_trials: int = 100,
                                 timeout: Optional[int] = None,
                                 verbose: bool = True,
                                 logger: Optional[logging.Logger] = None,
                                 model_dir: Optional[str] = None,
                                 model_name: str = "meta_model",
                                 **kwargs) -> Dict[str, Any]:
    """
    使用优化的 Optuna 目标函数进行元模型阈值优化

    Args:
        y_true_meta_val: 验证集真实标签
        y_proba_meta_val: 验证集预测概率
        calculate_metrics_func: 计算指标的函数
        n_trials: 试验次数
        timeout: 超时时间（秒）
        verbose: 是否显示详细信息
        logger: 日志记录器
        model_dir: 模型目录路径，用于保存Optuna结果
        model_name: 模型名称
        **kwargs: 其他参数

    Returns:
        Dict[str, Any]: 优化结果
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    logger.info("开始使用优化的 Optuna 目标函数进行阈值优化")

    # 输入验证
    if len(y_true_meta_val) == 0 or len(y_proba_meta_val) == 0:
        logger.error("输入数据为空")
        return {
            'optimization_method': 'optuna_optimized',
            'success': False,
            'error': '输入数据为空',
            'best_value': float('-inf'),
            'n_trials': 0
        }

    if len(y_true_meta_val) != len(y_proba_meta_val):
        logger.error(f"输入数据维度不匹配: y_true={len(y_true_meta_val)}, y_proba={len(y_proba_meta_val)}")
        return {
            'optimization_method': 'optuna_optimized',
            'success': False,
            'error': '输入数据维度不匹配',
            'best_value': float('-inf'),
            'n_trials': 0
        }

    if calculate_metrics_func is None:
        logger.error("calculate_metrics_func 不能为 None")
        return {
            'optimization_method': 'optuna_optimized',
            'success': False,
            'error': 'calculate_metrics_func 不能为 None',
            'best_value': float('-inf'),
            'n_trials': 0
        }

    try:
        # 1. 创建约束配置
        constraints = create_threshold_optimization_constraints()
        
        # 2. 获取优化策略
        optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')
        direction = getattr(config, 'META_MODEL_OPTUNA_DIRECTION', 'maximize')
        
        # 3. 创建优化器
        optimizer = OptimizedOptunaObjective(
            constraints=constraints,
            optimization_strategy=optimization_strategy,
            direction=direction,
            logger=logger
        )
        
        # 4. 定义参数范围
        param_ranges = {
            'threshold_up': (
                getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.5),
                getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
            ),
            'threshold_down': (
                getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.5),
                getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
            ),
            'confidence_gap_up': (
                getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0),
                getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.3)
            ),
            'confidence_gap_down': (
                getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0),
                getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.3)
            )
        }
        
        # 5. 创建目标函数
        objective = optimizer.create_threshold_objective(
            y_true=y_true_meta_val,
            y_proba=y_proba_meta_val,
            calculate_metrics_func=calculate_metrics_func,
            param_ranges=param_ranges
        )
        
        # 6. 创建和配置 Study
        sampler_config = {
            'n_startup_trials': min(20, n_trials // 4),
            'n_ei_candidates': 24,
            'multivariate': True
        }
        
        pruner_config = {
            'n_startup_trials': min(10, n_trials // 10),
            'n_warmup_steps': 5,
            'interval_steps': 1
        }
        
        study = create_optimized_study(
            direction=direction,
            sampler_config=sampler_config,
            pruner_config=pruner_config
        )
        
        # 7. 运行优化
        if verbose:
            logger.info(f"开始 Optuna 优化: {n_trials} 试验, 策略: {optimization_strategy}")
        
        study.optimize(
            objective,
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=verbose
        )
        
        # 8. 获取最佳结果
        best_params = study.best_params
        best_value = study.best_value

        # 检查是否有有效的优化结果
        if best_value == float('-inf') or best_value == float('inf') or np.isnan(best_value):
            logger.warning(f"所有试验都未能满足约束条件，最佳值: {best_value}")
            logger.warning("建议检查约束配置或增加验证数据量")

            # 分析约束失败的原因
            constraint_failures = {}
            valid_trials_count = 0
            for trial in study.trials:
                if trial.value is not None and not (trial.value == float('-inf') or trial.value == float('inf') or np.isnan(trial.value)):
                    valid_trials_count += 1
                if hasattr(trial, 'user_attrs'):
                    for key, value in trial.user_attrs.items():
                        if 'constraint_' in key:
                            constraint_failures[key] = constraint_failures.get(key, 0) + 1

            logger.info(f"试验统计: 总试验数={len(study.trials)}, 有效试验数={valid_trials_count}")
            if constraint_failures:
                logger.info("约束失败统计:")
                for constraint, count in constraint_failures.items():
                    logger.info(f"  {constraint}: {count} 次失败")

            # 如果没有任何有效试验，返回错误结果
            if valid_trials_count == 0:
                logger.error("没有任何有效的试验结果，优化失败")
                return {
                    'optimization_method': 'optuna_optimized',
                    'success': False,
                    'error': '没有任何有效的试验结果',
                    'best_value': best_value,
                    'n_trials': len(study.trials),
                    'constraint_failures': constraint_failures
                }

        # 9. 计算最佳参数的详细指标
        best_metrics = calculate_metrics_func(**best_params, verbose=False)

        # 10. 分析结果
        analysis = analyze_study_results(study, top_n=5, logger=logger)
        
        # 11. 构建返回结果
        result = {
            'optimization_method': 'optuna_optimized',
            'best_value': best_value,
            'n_trials': len(study.trials),
            'optimization_strategy': optimization_strategy,
            'constraints_used': {
                'min_trades': constraints.min_trades,
                'min_win_rate': constraints.min_win_rate,
                'max_consecutive_losses': constraints.max_consecutive_losses
            },
            'constraint_satisfaction_rate': analysis['constraint_analysis']['constraint_satisfaction_rate'],
            'parameter_importance': analysis.get('parameter_importance', {}),
            **best_params,
            **best_metrics
        }
        
        if verbose:
            logger.info(f"优化完成:")
            logger.info(f"  最佳值: {best_value:.4f}")
            logger.info(f"  完成试验: {len(study.trials)}")
            logger.info(f"  约束满足率: {result['constraint_satisfaction_rate']:.2%}")
            logger.info(f"  最佳参数: {best_params}")

        # 如果提供了model_dir参数，保存Optuna结果
        if model_dir:
            save_path = save_optuna_study_results(
                study=study,
                model_dir=model_dir,
                model_name=model_name,
                optimization_type='thresholds',
                logger=logger
            )
            result['optuna_results_path'] = save_path

        return result
        
    except Exception as e:
        logger.error(f"Optuna 阈值优化失败: {e}")
        # 返回错误结果
        return {
            'optimization_method': 'optuna_optimized',
            'success': False,
            'error': str(e),
            'best_value': float('-inf') if direction == 'maximize' else float('inf'),
            'n_trials': 0
        }


def optimize_model_hyperparameters(X_train: np.ndarray,
                                 y_train: np.ndarray,
                                 X_val: np.ndarray,
                                 y_val: np.ndarray,
                                 model_type: str = 'lgbm',
                                 n_trials: int = 100,
                                 timeout: Optional[int] = None,
                                 cv_folds: int = 3,
                                 verbose: bool = True,
                                 logger: Optional[logging.Logger] = None,
                                 model_dir: Optional[str] = None,
                                 model_name: Optional[str] = None,
                                 **kwargs) -> Dict[str, Any]:
    """
    使用优化的 Optuna 目标函数进行模型超参数优化

    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_val: 验证特征
        y_val: 验证标签
        model_type: 模型类型
        n_trials: 试验次数
        timeout: 超时时间（秒）
        cv_folds: 交叉验证折数
        verbose: 是否显示详细信息
        logger: 日志记录器
        model_dir: 模型目录路径，用于保存Optuna结果
        model_name: 模型名称，默认为model_type
        **kwargs: 其他参数

    Returns:
        Dict[str, Any]: 优化结果
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # 如果没有提供model_name，使用model_type
    if model_name is None:
        model_name = model_type
    
    logger.info(f"开始使用优化的 Optuna 目标函数进行 {model_type} 超参数优化")
    
    try:
        # 1. 获取参数网格
        if model_type.lower() == 'lgbm':
            param_grid = getattr(config, 'META_MODEL_OPTUNA_PARAM_GRID', {
                'num_leaves': ('int', 10, 100),
                'learning_rate': ('float', 0.01, 0.3, False),
                'feature_fraction': ('float', 0.4, 1.0),
                'bagging_fraction': ('float', 0.4, 1.0),
                'bagging_freq': ('int', 1, 7),
                'min_child_samples': ('int', 5, 100),
                'reg_alpha': ('float', 0.001, 10.0, True),  # 修复：最小值不能为0
                'reg_lambda': ('float', 0.001, 10.0, True)  # 修复：最小值不能为0
            })
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 2. 创建约束（模型优化通常约束较少）
        constraints = OptimizationConstraints(
            min_trades=0,  # 模型优化不需要交易约束
            min_win_rate=0.0,
            max_consecutive_losses=999,
            constraint_penalty_base=-100.0,
            constraint_penalty_multiplier=1.0
        )
        
        # 3. 创建优化器
        optimizer = OptimizedOptunaObjective(
            constraints=constraints,
            optimization_strategy='composite_score',
            direction='maximize',
            logger=logger
        )
        
        # 4. 创建目标函数
        objective = optimizer.create_model_objective(
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val,
            param_grid=param_grid,
            model_type=model_type,
            cv_folds=cv_folds
        )
        
        # 5. 创建 Study
        study = create_optimized_study(direction='maximize')
        
        # 6. 运行优化
        if verbose:
            logger.info(f"开始 {model_type} 超参数优化: {n_trials} 试验")
        
        study.optimize(
            objective,
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=verbose
        )
        
        # 7. 分析结果
        analysis = analyze_study_results(study, top_n=3, logger=logger)
        
        # 8. 构建返回结果
        result = {
            'optimization_method': f'optuna_optimized_{model_type}',
            'best_score': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials),
            'model_type': model_type,
            'cv_folds': cv_folds,
            'parameter_importance': analysis.get('parameter_importance', {}),
            'study_stats': analysis['study_stats']
        }
        
        if verbose:
            logger.info(f"{model_type} 超参数优化完成:")
            logger.info(f"  最佳分数: {study.best_value:.4f}")
            logger.info(f"  完成试验: {len(study.trials)}")
            logger.info(f"  最佳参数: {study.best_params}")

        # 如果提供了model_dir参数，保存Optuna结果
        if model_dir:
            save_path = save_optuna_study_results(
                study=study,
                model_dir=model_dir,
                model_name=model_name or f'{model_type}_model',
                optimization_type='hyperparameters',
                logger=logger
            )
            result['optuna_results_path'] = save_path

        return result
        
    except Exception as e:
        logger.error(f"{model_type} 超参数优化失败: {e}")
        return {
            'optimization_method': f'optuna_optimized_{model_type}',
            'success': False,
            'error': str(e),
            'best_score': 0.0,
            'n_trials': 0
        }


def replace_original_optuna_optimization(original_function: Callable) -> Callable:
    """
    装饰器：替换原始的 Optuna 优化函数
    
    Args:
        original_function: 原始的优化函数
        
    Returns:
        Callable: 包装后的优化函数
    """
    def wrapper(*args, **kwargs):
        # 检查是否启用了优化的 Optuna
        use_optimized = getattr(config, 'USE_OPTIMIZED_OPTUNA', True)
        
        if use_optimized:
            # 使用优化的 Optuna 实现
            logger = kwargs.get('logger') or logging.getLogger(__name__)
            logger.info("使用优化的 Optuna 目标函数")
            
            # 根据函数类型选择相应的优化方法
            if 'threshold' in original_function.__name__.lower():
                return optimize_meta_model_thresholds(*args, **kwargs)
            elif 'model' in original_function.__name__.lower():
                return optimize_model_hyperparameters(*args, **kwargs)
            else:
                logger.warning("未知的优化函数类型，使用原始实现")
                return original_function(*args, **kwargs)
        else:
            # 使用原始实现
            return original_function(*args, **kwargs)
    
    return wrapper


def save_optuna_study_results(study: optuna.Study,
                             model_dir: str,
                             model_name: str,
                             optimization_type: str = 'hyperparameters',
                             logger: Optional[logging.Logger] = None) -> str:
    """
    保存Optuna优化结果到JSON文件

    Args:
        study: Optuna Study对象
        model_dir: 模型目录路径
        model_name: 模型名称
        optimization_type: 优化类型 ('hyperparameters' 或 'thresholds')
        logger: 日志记录器

    Returns:
        str: 保存的文件路径
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # 确保目录存在
    os.makedirs(model_dir, exist_ok=True)

    # 创建文件名
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    filename = f"optuna_{optimization_type}_{model_name}_{timestamp}.json"
    file_path = os.path.join(model_dir, filename)

    # 获取最佳参数
    best_params = study.best_params
    best_value = study.best_value

    # 获取所有试验的信息
    trials_data = []
    for trial in study.trials:
        trial_info = {
            'number': trial.number,
            'value': trial.value if trial.value is not None else None,
            'params': trial.params,
            'state': str(trial.state),
            'datetime_start': trial.datetime_start.isoformat() if trial.datetime_start else None,
            'datetime_complete': trial.datetime_complete.isoformat() if trial.datetime_complete else None,
            'user_attrs': trial.user_attrs
        }
        trials_data.append(trial_info)

    # 获取参数重要性
    try:
        # 检查是否有有效的试验结果
        valid_trials = [t for t in study.trials if t.value is not None and not (
            t.value == float('-inf') or t.value == float('inf') or np.isnan(t.value)
        )]

        if len(valid_trials) < 2:
            logger.warning(f"有效试验数量不足 ({len(valid_trials)})，无法计算参数重要性")
            param_importance_dict = {}
        else:
            # 检查是否有足够的方差来计算重要性
            values = [t.value for t in valid_trials]
            if len(set(values)) < 2:
                logger.warning("所有有效试验的值相同，无法计算参数重要性")
                param_importance_dict = {}
            else:
                param_importance = optuna.importance.get_param_importances(study)
                param_importance_dict = {k: float(v) for k, v in param_importance.items()}
    except Exception as e:
        logger.warning(f"计算参数重要性时出错: {e}")
        param_importance_dict = {}

    # 构建结果字典
    result = {
        'optimization_type': optimization_type,
        'model_name': model_name,
        'timestamp': timestamp,
        'best_value': float(best_value) if best_value is not None else None,
        'best_params': best_params,
        'direction': str(study.direction),
        'n_trials': len(study.trials),
        'param_importance': param_importance_dict,
        'trials': trials_data
    }

    # 保存到文件
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        logger.info(f"Optuna优化结果已保存: {file_path}")
        return file_path
    except Exception as e:
        logger.error(f"保存Optuna结果时出错: {e}")
        return ""
