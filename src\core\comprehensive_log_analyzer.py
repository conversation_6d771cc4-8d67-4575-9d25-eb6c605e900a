#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 综合日志分析器 V1.0
为统一综合日志系统提供高级分析功能

核心功能：
- 实时性能监控
- 交易策略效果评估
- 市场条件影响分析
- 模型预测准确性分析
- 自动报告生成
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import logging


class ComprehensiveLogAnalyzer:
    """综合日志分析器"""
    
    def __init__(self, comprehensive_logs_dir: str = "comprehensive_logs"):
        self.logs_dir = Path(comprehensive_logs_dir)
        self.logger = logging.getLogger(f"{__name__}.ComprehensiveLogAnalyzer")
        
        # 设置中文字体（如果需要）
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.logger.info(f"综合日志分析器初始化完成: {comprehensive_logs_dir}")
    
    def generate_performance_dashboard(self, days_back: int = 7) -> Dict[str, Any]:
        """
        生成性能仪表板
        
        Args:
            days_back: 分析天数
        
        Returns:
            仪表板数据
        """
        try:
            # 加载数据
            trades_df = self._load_trades_data(days_back)
            contexts_df = self._load_contexts_data(days_back)
            
            if trades_df is None or trades_df.empty:
                return {'error': '没有找到交易数据'}
            
            dashboard = {
                'period': {
                    'start_date': (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d'),
                    'end_date': datetime.now().strftime('%Y-%m-%d'),
                    'days_analyzed': days_back
                },
                'overview': self._analyze_overview(trades_df),
                'strategy_performance': self._analyze_strategy_performance(trades_df),
                'market_conditions': self._analyze_market_impact(trades_df),
                'time_patterns': self._analyze_time_patterns(trades_df),
                'prediction_accuracy': self._analyze_prediction_accuracy(trades_df, contexts_df),
                'risk_metrics': self._calculate_risk_metrics(trades_df)
            }
            
            # 生成图表
            self._generate_charts(dashboard, trades_df)
            
            return dashboard
            
        except Exception as e:
            self.logger.error(f"生成性能仪表板失败: {e}")
            return {'error': str(e)}
    
    def _analyze_overview(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析总体概况"""
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['result'] == 'WIN'])
        losing_trades = len(trades_df[trades_df['result'] == 'LOSS'])
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # 计算盈亏
        total_pnl = trades_df['profit_loss'].sum()
        avg_win = trades_df[trades_df['result'] == 'WIN']['profit_loss'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['result'] == 'LOSS']['profit_loss'].mean() if losing_trades > 0 else 0
        
        # 计算最大连续盈亏
        max_consecutive_wins, max_consecutive_losses = self._calculate_consecutive_streaks(trades_df)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': round(win_rate, 2),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'profit_factor': round(abs(avg_win / avg_loss), 2) if avg_loss != 0 else float('inf'),
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses
        }
    
    def _analyze_strategy_performance(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析策略表现"""
        if 'target_name' not in trades_df.columns:
            return {}
        
        strategy_stats = {}
        
        for strategy in trades_df['target_name'].unique():
            strategy_trades = trades_df[trades_df['target_name'] == strategy]
            
            total = len(strategy_trades)
            wins = len(strategy_trades[strategy_trades['result'] == 'WIN'])
            win_rate = (wins / total * 100) if total > 0 else 0
            pnl = strategy_trades['profit_loss'].sum()
            
            strategy_stats[strategy] = {
                'total_trades': total,
                'win_rate': round(win_rate, 2),
                'total_pnl': round(pnl, 2),
                'avg_pnl_per_trade': round(pnl / total, 2) if total > 0 else 0
            }
        
        # 按总盈亏排序
        sorted_strategies = sorted(
            strategy_stats.items(),
            key=lambda x: x[1]['total_pnl'],
            reverse=True
        )
        
        return {
            'strategy_rankings': dict(sorted_strategies),
            'best_strategy': sorted_strategies[0][0] if sorted_strategies else None,
            'worst_strategy': sorted_strategies[-1][0] if sorted_strategies else None
        }
    
    def _analyze_market_impact(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析市场条件影响"""
        market_analysis = {}
        
        # 波动率影响分析
        if 'entry_atr_percent' in trades_df.columns:
            atr_analysis = self._analyze_by_atr(trades_df)
            market_analysis['volatility_impact'] = atr_analysis
        
        # 趋势强度影响分析
        if 'entry_adx_value' in trades_df.columns:
            adx_analysis = self._analyze_by_adx(trades_df)
            market_analysis['trend_impact'] = adx_analysis
        
        # 市场状态影响分析
        if 'entry_market_regime' in trades_df.columns:
            regime_analysis = self._analyze_by_regime(trades_df)
            market_analysis['regime_impact'] = regime_analysis
        
        return market_analysis
    
    def _analyze_by_atr(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """按ATR波动率分析"""
        atr_data = trades_df.dropna(subset=['entry_atr_percent'])
        
        # 定义波动率区间
        low_vol = atr_data[atr_data['entry_atr_percent'] < 1.5]
        med_vol = atr_data[(atr_data['entry_atr_percent'] >= 1.5) & (atr_data['entry_atr_percent'] < 3.0)]
        high_vol = atr_data[atr_data['entry_atr_percent'] >= 3.0]
        
        def calc_stats(df):
            if len(df) == 0:
                return {'trades': 0, 'win_rate': 0, 'avg_pnl': 0}
            
            wins = len(df[df['result'] == 'WIN'])
            return {
                'trades': len(df),
                'win_rate': round(wins / len(df) * 100, 2),
                'avg_pnl': round(df['profit_loss'].mean(), 2)
            }
        
        return {
            'low_volatility': calc_stats(low_vol),
            'medium_volatility': calc_stats(med_vol),
            'high_volatility': calc_stats(high_vol)
        }
    
    def _analyze_by_adx(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """按ADX趋势强度分析"""
        adx_data = trades_df.dropna(subset=['entry_adx_value'])
        
        # 定义趋势强度区间
        weak_trend = adx_data[adx_data['entry_adx_value'] < 20]
        medium_trend = adx_data[(adx_data['entry_adx_value'] >= 20) & (adx_data['entry_adx_value'] < 40)]
        strong_trend = adx_data[adx_data['entry_adx_value'] >= 40]
        
        def calc_stats(df):
            if len(df) == 0:
                return {'trades': 0, 'win_rate': 0, 'avg_pnl': 0}
            
            wins = len(df[df['result'] == 'WIN'])
            return {
                'trades': len(df),
                'win_rate': round(wins / len(df) * 100, 2),
                'avg_pnl': round(df['profit_loss'].mean(), 2)
            }
        
        return {
            'weak_trend': calc_stats(weak_trend),
            'medium_trend': calc_stats(medium_trend),
            'strong_trend': calc_stats(strong_trend)
        }
    
    def _analyze_by_regime(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """按市场状态分析"""
        regime_data = trades_df.dropna(subset=['entry_market_regime'])
        
        regime_stats = {}
        for regime in regime_data['entry_market_regime'].unique():
            regime_trades = regime_data[regime_data['entry_market_regime'] == regime]
            
            wins = len(regime_trades[regime_trades['result'] == 'WIN'])
            total = len(regime_trades)
            
            regime_stats[regime] = {
                'trades': total,
                'win_rate': round(wins / total * 100, 2) if total > 0 else 0,
                'avg_pnl': round(regime_trades['profit_loss'].mean(), 2)
            }
        
        return regime_stats
    
    def _analyze_time_patterns(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析时间模式"""
        if 'entry_timestamp' not in trades_df.columns:
            return {}
        
        # 转换时间戳
        trades_df['entry_timestamp'] = pd.to_datetime(trades_df['entry_timestamp'])
        trades_df['hour'] = trades_df['entry_timestamp'].dt.hour
        trades_df['day_of_week'] = trades_df['entry_timestamp'].dt.dayofweek
        
        # 按小时分析
        hourly_stats = {}
        for hour in range(24):
            hour_trades = trades_df[trades_df['hour'] == hour]
            if len(hour_trades) > 0:
                wins = len(hour_trades[hour_trades['result'] == 'WIN'])
                hourly_stats[hour] = {
                    'trades': len(hour_trades),
                    'win_rate': round(wins / len(hour_trades) * 100, 2),
                    'avg_pnl': round(hour_trades['profit_loss'].mean(), 2)
                }
        
        # 按星期分析
        daily_stats = {}
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day in range(7):
            day_trades = trades_df[trades_df['day_of_week'] == day]
            if len(day_trades) > 0:
                wins = len(day_trades[day_trades['result'] == 'WIN'])
                daily_stats[day_names[day]] = {
                    'trades': len(day_trades),
                    'win_rate': round(wins / len(day_trades) * 100, 2),
                    'avg_pnl': round(day_trades['profit_loss'].mean(), 2)
                }
        
        return {
            'hourly_patterns': hourly_stats,
            'daily_patterns': daily_stats
        }
    
    def _analyze_prediction_accuracy(self, trades_df: pd.DataFrame, contexts_df: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """分析预测准确性"""
        if contexts_df is None or contexts_df.empty:
            return {'error': '没有预测上下文数据'}
        
        accuracy_analysis = {}
        
        # 信号强度与结果关系
        if 'entry_signal_probability' in trades_df.columns:
            prob_analysis = self._analyze_probability_accuracy(trades_df)
            accuracy_analysis['probability_accuracy'] = prob_analysis
        
        # 方向优势与结果关系
        if 'direction_advantage' in trades_df.columns:
            advantage_analysis = self._analyze_direction_advantage(trades_df)
            accuracy_analysis['direction_advantage_accuracy'] = advantage_analysis
        
        return accuracy_analysis
    
    def _analyze_probability_accuracy(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析概率预测准确性"""
        prob_data = trades_df.dropna(subset=['entry_signal_probability'])
        
        # 按概率区间分析
        high_prob = prob_data[prob_data['entry_signal_probability'] >= 0.8]
        med_prob = prob_data[(prob_data['entry_signal_probability'] >= 0.6) & (prob_data['entry_signal_probability'] < 0.8)]
        low_prob = prob_data[prob_data['entry_signal_probability'] < 0.6]
        
        def calc_accuracy(df):
            if len(df) == 0:
                return {'trades': 0, 'accuracy': 0, 'avg_prob': 0}
            
            wins = len(df[df['result'] == 'WIN'])
            return {
                'trades': len(df),
                'accuracy': round(wins / len(df) * 100, 2),
                'avg_prob': round(df['entry_signal_probability'].mean(), 3)
            }
        
        return {
            'high_probability': calc_accuracy(high_prob),
            'medium_probability': calc_accuracy(med_prob),
            'low_probability': calc_accuracy(low_prob)
        }
    
    def _analyze_direction_advantage(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析方向优势准确性"""
        adv_data = trades_df.dropna(subset=['direction_advantage'])
        
        # 按优势区间分析
        strong_adv = adv_data[adv_data['direction_advantage'] >= 0.2]
        weak_adv = adv_data[adv_data['direction_advantage'] < 0.2]
        
        def calc_accuracy(df):
            if len(df) == 0:
                return {'trades': 0, 'accuracy': 0, 'avg_advantage': 0}
            
            wins = len(df[df['result'] == 'WIN'])
            return {
                'trades': len(df),
                'accuracy': round(wins / len(df) * 100, 2),
                'avg_advantage': round(df['direction_advantage'].mean(), 3)
            }
        
        return {
            'strong_advantage': calc_accuracy(strong_adv),
            'weak_advantage': calc_accuracy(weak_adv)
        }
    
    def _calculate_risk_metrics(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """计算风险指标"""
        if trades_df.empty:
            return {}
        
        pnl_series = trades_df['profit_loss']
        
        # 基础风险指标
        max_drawdown = self._calculate_max_drawdown(pnl_series)
        sharpe_ratio = self._calculate_sharpe_ratio(pnl_series)
        var_95 = np.percentile(pnl_series, 5) if len(pnl_series) > 0 else 0
        
        return {
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'value_at_risk_95': round(var_95, 2),
            'volatility': round(pnl_series.std(), 2),
            'skewness': round(pnl_series.skew(), 3),
            'kurtosis': round(pnl_series.kurtosis(), 3)
        }
    
    def _calculate_max_drawdown(self, pnl_series: pd.Series) -> float:
        """计算最大回撤"""
        cumulative = pnl_series.cumsum()
        running_max = cumulative.expanding().max()
        drawdown = cumulative - running_max
        return drawdown.min()
    
    def _calculate_sharpe_ratio(self, pnl_series: pd.Series, risk_free_rate: float = 0.02) -> float:
        """计算夏普比率"""
        if pnl_series.std() == 0:
            return 0
        
        excess_return = pnl_series.mean() - risk_free_rate / 252  # 假设年化无风险利率
        return excess_return / pnl_series.std() * np.sqrt(252)  # 年化
    
    def _calculate_consecutive_streaks(self, trades_df: pd.DataFrame) -> Tuple[int, int]:
        """计算最大连续盈亏"""
        if trades_df.empty:
            return 0, 0
        
        results = trades_df['result'].tolist()
        
        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0
        
        for result in results:
            if result == 'WIN':
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
        
        return max_wins, max_losses

    def _generate_charts(self, dashboard: Dict[str, Any], trades_df: pd.DataFrame):
        """生成分析图表"""
        try:
            charts_dir = self.logs_dir / "charts"
            charts_dir.mkdir(exist_ok=True)

            # 1. 盈亏分布图
            self._plot_pnl_distribution(trades_df, charts_dir)

            # 2. 累积盈亏曲线
            self._plot_cumulative_pnl(trades_df, charts_dir)

            # 3. 策略表现对比
            if 'strategy_performance' in dashboard:
                self._plot_strategy_comparison(dashboard['strategy_performance'], charts_dir)

            # 4. 时间模式热力图
            if 'time_patterns' in dashboard:
                self._plot_time_heatmap(dashboard['time_patterns'], charts_dir)

            # 5. 风险指标雷达图
            if 'risk_metrics' in dashboard:
                self._plot_risk_radar(dashboard['risk_metrics'], charts_dir)

            self.logger.info(f"图表已生成到: {charts_dir}")

        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")

    def _plot_pnl_distribution(self, trades_df: pd.DataFrame, charts_dir: Path):
        """绘制盈亏分布图"""
        plt.figure(figsize=(10, 6))

        wins = trades_df[trades_df['result'] == 'WIN']['profit_loss']
        losses = trades_df[trades_df['result'] == 'LOSS']['profit_loss']

        plt.hist([wins, losses], bins=20, alpha=0.7, label=['盈利交易', '亏损交易'], color=['green', 'red'])
        plt.xlabel('盈亏金额')
        plt.ylabel('交易数量')
        plt.title('盈亏分布图')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(charts_dir / 'pnl_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_cumulative_pnl(self, trades_df: pd.DataFrame, charts_dir: Path):
        """绘制累积盈亏曲线"""
        if 'entry_timestamp' not in trades_df.columns:
            return

        plt.figure(figsize=(12, 6))

        # 按时间排序
        trades_sorted = trades_df.sort_values('entry_timestamp')
        cumulative_pnl = trades_sorted['profit_loss'].cumsum()

        plt.plot(trades_sorted['entry_timestamp'], cumulative_pnl, linewidth=2, color='blue')
        plt.fill_between(trades_sorted['entry_timestamp'], cumulative_pnl, alpha=0.3, color='blue')

        plt.xlabel('时间')
        plt.ylabel('累积盈亏')
        plt.title('累积盈亏曲线')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)

        plt.tight_layout()
        plt.savefig(charts_dir / 'cumulative_pnl.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_strategy_comparison(self, strategy_data: Dict[str, Any], charts_dir: Path):
        """绘制策略表现对比图"""
        if 'strategy_rankings' not in strategy_data:
            return

        strategies = list(strategy_data['strategy_rankings'].keys())[:10]  # 取前10个策略
        win_rates = [strategy_data['strategy_rankings'][s]['win_rate'] for s in strategies]
        total_pnls = [strategy_data['strategy_rankings'][s]['total_pnl'] for s in strategies]

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 胜率对比
        ax1.bar(range(len(strategies)), win_rates, color='skyblue')
        ax1.set_xlabel('策略')
        ax1.set_ylabel('胜率 (%)')
        ax1.set_title('策略胜率对比')
        ax1.set_xticks(range(len(strategies)))
        ax1.set_xticklabels(strategies, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)

        # 总盈亏对比
        colors = ['green' if pnl >= 0 else 'red' for pnl in total_pnls]
        ax2.bar(range(len(strategies)), total_pnls, color=colors)
        ax2.set_xlabel('策略')
        ax2.set_ylabel('总盈亏')
        ax2.set_title('策略盈亏对比')
        ax2.set_xticks(range(len(strategies)))
        ax2.set_xticklabels(strategies, rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(charts_dir / 'strategy_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_time_heatmap(self, time_data: Dict[str, Any], charts_dir: Path):
        """绘制时间模式热力图"""
        if 'hourly_patterns' not in time_data:
            return

        # 创建24小时x7天的热力图数据
        hourly_win_rates = []
        for hour in range(24):
            if hour in time_data['hourly_patterns']:
                hourly_win_rates.append(time_data['hourly_patterns'][hour]['win_rate'])
            else:
                hourly_win_rates.append(0)

        plt.figure(figsize=(12, 4))

        # 重塑数据为热力图格式
        heatmap_data = np.array(hourly_win_rates).reshape(1, -1)

        sns.heatmap(heatmap_data,
                   xticklabels=[f'{h:02d}:00' for h in range(24)],
                   yticklabels=['胜率'],
                   annot=True,
                   fmt='.1f',
                   cmap='RdYlGn',
                   center=50)

        plt.title('24小时胜率热力图')
        plt.xlabel('小时')
        plt.tight_layout()
        plt.savefig(charts_dir / 'time_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_risk_radar(self, risk_data: Dict[str, Any], charts_dir: Path):
        """绘制风险指标雷达图"""
        # 标准化风险指标到0-100范围
        metrics = ['sharpe_ratio', 'volatility', 'max_drawdown', 'skewness', 'kurtosis']
        values = []
        labels = ['夏普比率', '波动率', '最大回撤', '偏度', '峰度']

        for metric in metrics:
            if metric in risk_data:
                value = risk_data[metric]
                # 简单的标准化处理
                if metric == 'sharpe_ratio':
                    normalized = min(max(value * 20 + 50, 0), 100)
                elif metric == 'volatility':
                    normalized = min(max(100 - value * 10, 0), 100)
                elif metric == 'max_drawdown':
                    normalized = min(max(100 + value * 2, 0), 100)  # 回撤为负值
                else:
                    normalized = min(max(abs(value) * 20, 0), 100)
                values.append(normalized)
            else:
                values.append(0)

        # 创建雷达图
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]

        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        ax.plot(angles, values, 'o-', linewidth=2, color='blue')
        ax.fill(angles, values, alpha=0.25, color='blue')
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels)
        ax.set_ylim(0, 100)
        ax.set_title('风险指标雷达图', pad=20)

        plt.tight_layout()
        plt.savefig(charts_dir / 'risk_radar.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _load_trades_data(self, days_back: int) -> Optional[pd.DataFrame]:
        """加载交易数据"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            all_trades = []

            trades_dir = self.logs_dir / "trades"
            if not trades_dir.exists():
                return None

            for year_dir in trades_dir.glob("*"):
                if not year_dir.is_dir():
                    continue

                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue

                    for csv_file in month_dir.glob("trades_*.csv"):
                        try:
                            df = pd.read_csv(csv_file)
                            if not df.empty:
                                df['entry_timestamp'] = pd.to_datetime(df['entry_timestamp'])
                                df = df[df['entry_timestamp'] >= start_date]
                                if not df.empty:
                                    all_trades.append(df)
                        except Exception as e:
                            self.logger.error(f"读取交易文件失败 {csv_file}: {e}")

            if not all_trades:
                return None

            combined_df = pd.concat(all_trades, ignore_index=True)
            return combined_df

        except Exception as e:
            self.logger.error(f"加载交易数据失败: {e}")
            return None

    def _load_contexts_data(self, days_back: int) -> Optional[pd.DataFrame]:
        """加载预测上下文数据"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            all_contexts = []

            contexts_dir = self.logs_dir / "contexts"
            if not contexts_dir.exists():
                return None

            for year_dir in contexts_dir.glob("*"):
                if not year_dir.is_dir():
                    continue

                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue

                    for csv_file in month_dir.glob("contexts_*.csv"):
                        try:
                            df = pd.read_csv(csv_file)
                            if not df.empty:
                                df['timestamp'] = pd.to_datetime(df['timestamp'])
                                df = df[df['timestamp'] >= start_date]
                                if not df.empty:
                                    all_contexts.append(df)
                        except Exception as e:
                            self.logger.error(f"读取上下文文件失败 {csv_file}: {e}")

            if not all_contexts:
                return None

            combined_df = pd.concat(all_contexts, ignore_index=True)
            return combined_df

        except Exception as e:
            self.logger.error(f"加载上下文数据失败: {e}")
            return None

    def export_dashboard_report(self, dashboard: Dict[str, Any], output_file: str = None) -> str:
        """导出仪表板报告"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"performance_dashboard_{timestamp}.json"

        output_path = self.logs_dir / output_file

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(dashboard, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"仪表板报告已导出: {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"导出仪表板报告失败: {e}")
            return ""

    def print_dashboard_summary(self, dashboard: Dict[str, Any]):
        """打印仪表板摘要"""
        print("=" * 80)
        print("📊 交易性能仪表板摘要")
        print("=" * 80)

        # 总体概况
        if 'overview' in dashboard:
            overview = dashboard['overview']
            print(f"\n📈 总体概况:")
            print(f"  总交易数: {overview.get('total_trades', 0)}")
            print(f"  胜率: {overview.get('win_rate', 0):.2f}%")
            print(f"  总盈亏: {overview.get('total_pnl', 0):.2f}")
            print(f"  盈亏比: {overview.get('profit_factor', 0):.2f}")
            print(f"  最大连胜: {overview.get('max_consecutive_wins', 0)}")
            print(f"  最大连亏: {overview.get('max_consecutive_losses', 0)}")

        # 最佳策略
        if 'strategy_performance' in dashboard:
            strategy_perf = dashboard['strategy_performance']
            best_strategy = strategy_perf.get('best_strategy')
            worst_strategy = strategy_perf.get('worst_strategy')

            print(f"\n🏆 策略表现:")
            print(f"  最佳策略: {best_strategy}")
            print(f"  最差策略: {worst_strategy}")

        # 风险指标
        if 'risk_metrics' in dashboard:
            risk = dashboard['risk_metrics']
            print(f"\n⚠️ 风险指标:")
            print(f"  最大回撤: {risk.get('max_drawdown', 0):.2f}")
            print(f"  夏普比率: {risk.get('sharpe_ratio', 0):.3f}")
            print(f"  波动率: {risk.get('volatility', 0):.2f}")
            print(f"  VaR(95%): {risk.get('value_at_risk_95', 0):.2f}")

        print("=" * 80)
