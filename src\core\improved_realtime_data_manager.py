# improved_realtime_data_manager.py
"""
改进的实时数据管理器，专门为预测程序GUI价格更新问题提供解决方案
基于原有的enhanced_realtime_data_manager，添加健康检查和自动重连机制
"""

import time
import threading
import logging
from typing import Dict, Optional, Callable, List
from enum import Enum

# 导入改进的价格获取器
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from simulation.ImprovedPriceFetcher import ImprovedPriceFetcher, ConnectionState

logger = logging.getLogger(__name__)

class ImprovedRealtimeDataManager:
    """
    改进的实时数据管理器
    
    功能：
    - 管理多个交易对的实时价格数据
    - 使用ImprovedPriceFetcher确保连接稳定性
    - 提供健康检查和自动重连机制
    - 向后兼容原有接口
    """
    
    def __init__(self, proxy_url: str = 'http://127.0.0.1:7897'):
        self.proxy_url = proxy_url
        
        # 数据存储
        self.data_lock = threading.RLock()
        self.latest_prices: Dict[str, float] = {}  # {'BTCUSDT': 30000.0}
        self.last_update_times: Dict[str, float] = {}  # {'BTCUSDT': timestamp}
        
        # 价格获取器管理
        self.price_fetchers: Dict[str, ImprovedPriceFetcher] = {}
        
        # 回调函数
        self.price_callbacks: List[Callable[[str, float], None]] = []
        self.state_callbacks: List[Callable[[str, ConnectionState], None]] = []
        
        # 状态
        self.is_running = False
        self.managed_symbols: List[str] = []
        
        # 健康检查
        self.health_check_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.health_check_interval = 60.0  # 每分钟检查一次
        
        logger.info("改进实时数据管理器初始化完成")
    
    def add_price_callback(self, callback: Callable[[str, float], None]):
        """添加价格更新回调函数"""
        self.price_callbacks.append(callback)
    
    def add_state_callback(self, callback: Callable[[str, ConnectionState], None]):
        """添加状态变化回调函数"""
        self.state_callbacks.append(callback)
    
    def _on_price_update(self, symbol: str, price: float):
        """价格更新回调"""
        with self.data_lock:
            self.latest_prices[symbol] = price
            self.last_update_times[symbol] = time.time()
        
        # 调用外部回调
        for callback in self.price_callbacks:
            try:
                callback(symbol, price)
            except Exception as e:
                logger.error(f"价格回调执行失败: {e}")
    
    def _on_state_change(self, symbol: str, state: ConnectionState):
        """连接状态变化回调"""
        logger.info(f"交易对 {symbol} 连接状态变化: {state.value}")
        
        # 调用外部回调
        for callback in self.state_callbacks:
            try:
                callback(symbol, state)
            except Exception as e:
                logger.error(f"状态回调执行失败: {e}")
    
    def add_symbol(self, symbol: str) -> bool:
        """添加交易对监控"""
        symbol = symbol.upper()
        
        if symbol in self.price_fetchers:
            logger.warning(f"交易对 {symbol} 已在监控中")
            return True
        
        logger.info(f"添加交易对监控: {symbol}")
        
        try:
            # 创建改进的价格获取器
            fetcher = ImprovedPriceFetcher(
                symbol=symbol,
                health_check_interval=30.0,  # 30秒检查一次
                price_stale_threshold=120.0,  # 2分钟过期阈值
                price_callback=lambda price, sym=symbol: self._on_price_update(sym, price),
                state_callback=lambda state, sym=symbol: self._on_state_change(sym, state)
            )
            
            # 启动价格流
            if fetcher.start_stream():
                self.price_fetchers[symbol] = fetcher
                self.managed_symbols.append(symbol)
                logger.info(f"交易对 {symbol} 监控启动成功")
                return True
            else:
                logger.error(f"交易对 {symbol} 监控启动失败")
                return False
                
        except Exception as e:
            logger.error(f"添加交易对 {symbol} 监控失败: {e}")
            return False
    
    def remove_symbol(self, symbol: str):
        """移除交易对监控"""
        symbol = symbol.upper()
        
        if symbol in self.price_fetchers:
            try:
                self.price_fetchers[symbol].stop_stream()
                del self.price_fetchers[symbol]
                
                if symbol in self.managed_symbols:
                    self.managed_symbols.remove(symbol)
                
                with self.data_lock:
                    self.latest_prices.pop(symbol, None)
                    self.last_update_times.pop(symbol, None)
                
                logger.info(f"交易对 {symbol} 监控已移除")
            except Exception as e:
                logger.error(f"移除交易对 {symbol} 监控失败: {e}")
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        symbol = symbol.upper()
        
        with self.data_lock:
            return self.latest_prices.get(symbol)
    
    def get_all_prices(self) -> Dict[str, float]:
        """获取所有价格"""
        with self.data_lock:
            return self.latest_prices.copy()
    
    def is_price_fresh(self, symbol: str, max_age_seconds: float = 60.0) -> bool:
        """检查价格数据是否新鲜"""
        symbol = symbol.upper()
        
        if symbol not in self.latest_prices:
            return False
        
        current_time = time.time()
        with self.data_lock:
            last_update = self.last_update_times.get(symbol, 0)
        
        age = current_time - last_update
        return age <= max_age_seconds
    
    def force_reconnect(self, symbol: str = None):
        """强制重连指定交易对或所有交易对"""
        if symbol:
            symbol = symbol.upper()
            if symbol in self.price_fetchers:
                logger.info(f"强制重连交易对: {symbol}")
                self.price_fetchers[symbol].force_reconnect()
        else:
            logger.info("强制重连所有交易对")
            for sym, fetcher in self.price_fetchers.items():
                try:
                    fetcher.force_reconnect()
                except Exception as e:
                    logger.error(f"重连交易对 {sym} 失败: {e}")
    
    def _health_check_worker(self):
        """健康检查工作线程"""
        logger.info("数据管理器健康检查线程启动")
        
        while not self.stop_event.is_set():
            try:
                time.sleep(self.health_check_interval)
                
                if self.stop_event.is_set():
                    break
                
                # 检查所有交易对的价格数据新鲜度
                current_time = time.time()
                stale_symbols = []
                
                for symbol in self.managed_symbols:
                    if not self.is_price_fresh(symbol, max_age_seconds=180):  # 3分钟
                        stale_symbols.append(symbol)
                
                # 对过期的交易对进行重连
                for symbol in stale_symbols:
                    last_update = self.last_update_times.get(symbol, 0)
                    age = current_time - last_update
                    logger.warning(f"交易对 {symbol} 价格数据过期 {age:.0f} 秒，尝试重连")
                    self.force_reconnect(symbol)
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
        
        logger.info("数据管理器健康检查线程结束")
    
    def start(self) -> bool:
        """启动数据管理器"""
        if self.is_running:
            logger.warning("数据管理器已在运行中")
            return True
        
        logger.info("启动改进实时数据管理器")
        
        self.is_running = True
        self.stop_event.clear()
        
        # 启动健康检查线程
        self.health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True
        )
        self.health_check_thread.start()
        
        logger.info("改进实时数据管理器启动成功")
        return True
    
    def stop(self):
        """停止数据管理器"""
        if not self.is_running:
            return
        
        logger.info("停止改进实时数据管理器")
        
        self.is_running = False
        self.stop_event.set()
        
        # 停止所有价格获取器
        for symbol, fetcher in self.price_fetchers.items():
            try:
                fetcher.stop_stream()
            except Exception as e:
                logger.error(f"停止交易对 {symbol} 获取器失败: {e}")
        
        self.price_fetchers.clear()
        self.managed_symbols.clear()
        
        with self.data_lock:
            self.latest_prices.clear()
            self.last_update_times.clear()
        
        logger.info("改进实时数据管理器已停止")
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = {
            'is_running': self.is_running,
            'managed_symbols': self.managed_symbols.copy(),
            'total_symbols': len(self.managed_symbols),
            'price_count': len(self.latest_prices),
            'fetcher_stats': {}
        }
        
        # 获取每个获取器的统计信息
        for symbol, fetcher in self.price_fetchers.items():
            try:
                stats['fetcher_stats'][symbol] = fetcher.get_stats()
            except Exception as e:
                stats['fetcher_stats'][symbol] = {'error': str(e)}
        
        return stats


# 全局实例（向后兼容）
improved_data_manager = ImprovedRealtimeDataManager()

# 向后兼容的函数
def get_latest_price(symbol: str) -> Optional[float]:
    """获取最新价格（向后兼容）"""
    return improved_data_manager.get_current_price(symbol)

def get_all_latest_prices() -> Dict[str, float]:
    """获取所有最新价格（向后兼容）"""
    return improved_data_manager.get_all_prices()

def add_symbol_monitoring(symbol: str) -> bool:
    """添加交易对监控（向后兼容）"""
    return improved_data_manager.add_symbol(symbol)

def remove_symbol_monitoring(symbol: str):
    """移除交易对监控（向后兼容）"""
    improved_data_manager.remove_symbol(symbol)

def start_data_manager() -> bool:
    """启动数据管理器（向后兼容）"""
    return improved_data_manager.start()

def stop_data_manager():
    """停止数据管理器（向后兼容）"""
    improved_data_manager.stop()

def force_reconnect_all():
    """强制重连所有交易对（新增功能）"""
    improved_data_manager.force_reconnect()

def is_price_fresh(symbol: str, max_age_seconds: float = 60.0) -> bool:
    """检查价格数据是否新鲜（新增功能）"""
    return improved_data_manager.is_price_fresh(symbol, max_age_seconds)
