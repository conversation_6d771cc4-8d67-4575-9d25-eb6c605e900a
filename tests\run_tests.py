#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器
运行所有单元测试并生成报告
"""

import unittest
import sys
import os
import time
from io import StringIO
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 忽略警告以保持测试输出清洁
warnings.filterwarnings('ignore')

class ColoredTextTestResult(unittest.TextTestResult):
    """带颜色的测试结果输出"""

    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.success_count = 0
        self.start_time = None
        self._verbosity = verbosity  # 保存verbosity

    def startTest(self, test):
        super().startTest(test)
        self.start_time = time.time()
        if self._verbosity > 1:
            self.stream.write(f"  {test._testMethodName} ... ")
            self.stream.flush()

    def addSuccess(self, test):
        super().addSuccess(test)
        self.success_count += 1
        if self._verbosity > 1:
            elapsed = time.time() - self.start_time
            self.stream.write(f"✅ ({elapsed:.3f}s)\n")

    def addError(self, test, err):
        super().addError(test, err)
        if self._verbosity > 1:
            elapsed = time.time() - self.start_time
            self.stream.write(f"❌ ERROR ({elapsed:.3f}s)\n")

    def addFailure(self, test, err):
        super().addFailure(test, err)
        if self._verbosity > 1:
            elapsed = time.time() - self.start_time
            self.stream.write(f"❌ FAIL ({elapsed:.3f}s)\n")

    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        if self._verbosity > 1:
            elapsed = time.time() - self.start_time
            self.stream.write(f"⏭️ SKIP ({elapsed:.3f}s) - {reason}\n")

class ColoredTextTestRunner(unittest.TextTestRunner):
    """带颜色的测试运行器"""
    
    resultclass = ColoredTextTestResult
    
    def run(self, test):
        result = super().run(test)
        
        # 打印总结
        print("\n" + "="*70)
        print("测试总结")
        print("="*70)
        
        total_tests = result.testsRun
        successes = result.success_count if hasattr(result, 'success_count') else (total_tests - len(result.failures) - len(result.errors) - len(result.skipped))
        failures = len(result.failures)
        errors = len(result.errors)
        skipped = len(result.skipped)
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 成功: {successes}")
        print(f"❌ 失败: {failures}")
        print(f"❌ 错误: {errors}")
        print(f"⏭️ 跳过: {skipped}")
        
        if failures > 0 or errors > 0:
            print(f"\n成功率: {successes/total_tests*100:.1f}%")
        else:
            print(f"\n🎉 所有测试通过! 成功率: 100%")
        
        return result

def discover_and_run_tests(test_dir=None, pattern='test_*.py', verbosity=2):
    """发现并运行测试"""
    if test_dir is None:
        test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 发现测试
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern=pattern)
    
    # 运行测试
    runner = ColoredTextTestRunner(verbosity=verbosity)
    return runner.run(suite)

def run_specific_test_file(test_file, verbosity=2):
    """运行特定的测试文件"""
    print(f"\n{'='*70}")
    print(f"运行测试文件: {test_file}")
    print(f"{'='*70}")
    
    # 导入测试模块
    test_dir = os.path.dirname(os.path.abspath(__file__))
    test_path = os.path.join(test_dir, test_file)
    
    if not os.path.exists(test_path):
        print(f"❌ 测试文件不存在: {test_path}")
        return None
    
    # 加载测试
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName(test_file.replace('.py', ''))
    
    # 运行测试
    runner = ColoredTextTestRunner(verbosity=verbosity)
    return runner.run(suite)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='运行单元测试')
    parser.add_argument('--file', '-f', help='运行特定的测试文件')
    parser.add_argument('--pattern', '-p', default='test_*.py', help='测试文件模式')
    parser.add_argument('--verbosity', '-v', type=int, default=2, help='详细程度 (0-2)')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有测试文件')
    
    args = parser.parse_args()
    
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    if args.list:
        # 列出所有测试文件
        print("可用的测试文件:")
        for file in os.listdir(test_dir):
            if file.startswith('test_') and file.endswith('.py'):
                print(f"  - {file}")
        return
    
    print("🧪 开始运行单元测试...")
    print(f"测试目录: {test_dir}")
    
    start_time = time.time()
    
    if args.file:
        # 运行特定文件
        result = run_specific_test_file(args.file, args.verbosity)
    else:
        # 运行所有测试
        result = discover_and_run_tests(test_dir, args.pattern, args.verbosity)
    
    end_time = time.time()
    
    if result:
        print(f"\n⏱️ 总耗时: {end_time - start_time:.2f}秒")
        
        # 返回适当的退出码
        if result.failures or result.errors:
            sys.exit(1)
        else:
            sys.exit(0)
    else:
        print("❌ 测试运行失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
