# analysis_logger.py
import csv
import os
import threading
from datetime import datetime, timezone
import json

class AnalysisLogger:
    def __init__(self, filename, fieldnames, log_directory="analysis_logs"):
        self.log_directory = log_directory
        os.makedirs(self.log_directory, exist_ok=True)
        self.filepath = os.path.join(self.log_directory, filename)
        self.fieldnames = fieldnames
        self._lock = threading.Lock()
        self._initialize_file()

    def _initialize_file(self):
        with self._lock:
            write_header = not os.path.exists(self.filepath)
            try:
                with open(self.filepath, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=self.fieldnames)
                    if write_header:
                        writer.writeheader()
            except IOError as e:
                print(f"!!! 错误：无法初始化日志文件 {self.filepath}: {e}")

    def log(self, data_dict):
        with self._lock:
            try:
                with open(self.filepath, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=self.fieldnames, extrasaction='ignore')
                    writer.writerow(data_dict)
            except IOError as e:
                print(f"!!! 错误：写入日志到 {self.filepath} 失败: {e}")
            except Exception as e_gen:
                print(f"!!! 错误：记录日志时发生未知错误: {e_gen}")

# 全局日志记录器实例
# prediction_context_logger = None # 将由 PredictionStateManager 管理
trade_settlement_logger = None
failure_analysis_logger = None

# 全局状态管理器的延迟导入和初始化
global_prediction_state_manager = None

def _get_global_prediction_state_manager():
    """
    延迟导入和获取全局预测状态管理器
    这种方法避免了模块初始化顺序问题
    """
    global global_prediction_state_manager

    if global_prediction_state_manager is not None:
        return global_prediction_state_manager

    # 尝试多种导入路径
    import_attempts = [
        ('src.core.prediction', 'global_prediction_state_manager'),
        ('prediction', 'global_prediction_state_manager'),
        ('src.prediction', 'global_prediction_state_manager'),
    ]

    for module_path, attr_name in import_attempts:
        try:
            module = __import__(module_path, fromlist=[attr_name])
            global_prediction_state_manager = getattr(module, attr_name, None)
            if global_prediction_state_manager is not None:
                print(f"[AnalysisLogger] 成功从 '{module_path}' 导入 global_prediction_state_manager")
                return global_prediction_state_manager
        except ImportError:
            continue
        except AttributeError:
            continue
        except Exception as e:
            print(f"[AnalysisLogger] 从 '{module_path}' 导入时发生异常: {e}")
            continue

    # 如果所有导入尝试都失败，打印警告但不阻止程序运行
    print("!!! [AnalysisLogger] 未能导入 global_prediction_state_manager。预测上下文日志可能无法正确设置。")
    print("    这可能是由于模块初始化顺序问题导致的，将在需要时重试导入。")
    return None

# 预测上下文日志字段
PREDICTION_CONTEXT_FIELDS = [
    'timestamp', 'target_name', 'symbol', 'signal_type', 'signal_strength',
    'avg_up_prob', 'avg_down_prob', 'model_positive_class_prob', 'signal_threshold_used',
    'feature_names_json', 'feature_values_json', 'current_price', 'last_kline_close',
    'trend_signal', 'trend_strength', 'trend_status_text', 'adx_value', 'pdi_value', 'mdi_value',
    'volatility_level', 'volatility_status_text', 'atr_value', 'atr_percent',
    'planned_amount', 'amount_calculation_method', 'dynamic_threshold_adjustments',
    'filter_reasons', 'model_type', 'ensemble_size'
]

# 交易结算日志字段
TRADE_SETTLEMENT_FIELDS = [
    'timestamp', 'trade_id', 'direction', 'entry_price', 'exit_price', 'amount_staked',
    'entry_time', 'exit_time', 'contract_duration_minutes', 'status', 'profit_loss',
    'entry_market_snapshot_json', 'exit_market_snapshot_json', 'related_prediction_id',
    'symbol', 'payout_ratio'
]

# 失败案例分析汇总字段
FAILURE_ANALYSIS_FIELDS = [
    'analysis_timestamp', 'period_start', 'period_end', 'total_trades', 'losing_trades',
    'loss_rate', 'total_loss_amount', 'avg_loss_per_trade', 'most_common_failure_patterns',
    'trend_analysis_json', 'volatility_analysis_json', 'feature_importance_analysis_json',
    'recommendations_json'
]

def initialize_loggers():
    """
    初始化所有分析日志记录器

    ⚠️ 注意：此函数已被标记为废弃，建议使用新的统一综合日志系统
    现在会自动重定向到新系统的兼容层
    """
    import warnings
    warnings.warn(
        "analysis_logger.initialize_loggers 已废弃，请使用统一综合日志系统",
        DeprecationWarning,
        stacklevel=2
    )

    # 使用兼容层初始化
    from ..core.logger_compatibility_layer import initialize_loggers as compat_initialize_loggers
    return compat_initialize_loggers()

    # 保留原始代码以防需要回退
    global trade_settlement_logger, failure_analysis_logger # prediction_context_logger 不再是这里的全局变量

    # 初始化 prediction_context_logger 并设置到 PredictionStateManager
    local_prediction_context_logger = AnalysisLogger(
        "prediction_context.csv",
        PREDICTION_CONTEXT_FIELDS
    )

    # 使用延迟导入获取状态管理器
    state_manager = _get_global_prediction_state_manager()
    if state_manager:
        state_manager.set_prediction_context_logger(local_prediction_context_logger)
        print("[AnalysisLogger] 预测上下文日志记录器已初始化并设置到状态管理器。")
    else:
        print("!!! [AnalysisLogger] global_prediction_state_manager 未可用，预测上下文日志记录器未设置。")
        print("    将在后续需要时重试获取状态管理器。")

    trade_settlement_logger = AnalysisLogger(
        "trade_settlement.csv",
        TRADE_SETTLEMENT_FIELDS
    )
    print("[AnalysisLogger] 交易结算日志记录器已初始化。") # 分开打印，更清晰

    failure_analysis_logger = AnalysisLogger(
        "failure_analysis_summary.csv",
        FAILURE_ANALYSIS_FIELDS
    )
    print("[AnalysisLogger] 失败案例分析日志记录器已初始化。")

def log_prediction_context(target_name, symbol, signal_data, market_data, model_data, filter_data):
    """
    🚫 已废弃：重定向到简化的UnifiedTradeLogger

    为了确保只使用简化的28字段日志格式，此函数现在重定向到UnifiedTradeLogger
    """
    import warnings
    warnings.warn(
        "analysis_logger.log_prediction_context 已废弃，系统自动重定向到简化的UnifiedTradeLogger",
        DeprecationWarning,
        stacklevel=2
    )

    # 🎯 重定向到简化的UnifiedTradeLogger - 不记录预测上下文，只在实际交易时记录
    print(f"[废弃警告] log_prediction_context调用被忽略，只在实际交易时记录到简化格式")
    return True  # 返回成功以保持兼容性

    # 保留原始代码以防需要回退
    current_prediction_context_logger = None

    # 使用延迟导入获取状态管理器
    state_manager = _get_global_prediction_state_manager()
    if state_manager:
        current_prediction_context_logger = state_manager.get_prediction_context_logger()

    if current_prediction_context_logger is None:
        # print("!!! log_prediction_context: 预测上下文日志记录器未初始化或未从状态管理器获取。") # 可以选择性打印
        return

    try:
        context_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'target_name': target_name,
            'symbol': symbol,
            'signal_type': signal_data.get('signal_type'),
            'signal_strength': signal_data.get('signal_strength'),
            'avg_up_prob': signal_data.get('avg_up_prob'),
            'avg_down_prob': signal_data.get('avg_down_prob'),
            'model_positive_class_prob': signal_data.get('model_positive_class_prob'),
            'signal_threshold_used': signal_data.get('signal_threshold_used'),
            'feature_names_json': json.dumps(model_data.get('feature_names', [])),
            'feature_values_json': json.dumps(model_data.get('feature_values', [])),
            'current_price': market_data.get('current_price'),
            'last_kline_close': market_data.get('last_kline_close'),
            'trend_signal': filter_data.get('trend_signal'),
            'trend_strength': filter_data.get('trend_strength'),
            'trend_status_text': filter_data.get('trend_status_text'),
            'adx_value': filter_data.get('adx_value'),
            'pdi_value': filter_data.get('pdi_value'),
            'mdi_value': filter_data.get('mdi_value'),
            'volatility_level': filter_data.get('volatility_level'),
            'volatility_status_text': filter_data.get('volatility_status_text'),
            'atr_value': filter_data.get('atr_value'),
            'atr_percent': filter_data.get('atr_percent'),
            'planned_amount': signal_data.get('planned_amount'),
            'amount_calculation_method': signal_data.get('amount_calculation_method'),
            'dynamic_threshold_adjustments': json.dumps(filter_data.get('dynamic_threshold_adjustments', {})),
            'filter_reasons': filter_data.get('filter_reasons'),
            'model_type': model_data.get('model_type'),
            'ensemble_size': model_data.get('ensemble_size')
        }

        current_prediction_context_logger.log(context_data)
    except (TypeError, ValueError) as e:
        print(f"!!! 数据序列化或格式错误: {e}")
    except json.JSONEncodeError as e:
        print(f"!!! JSON序列化失败: {e}")
    except AttributeError as e:
        print(f"!!! 日志记录器错误或数据访问错误: {e}")
    except Exception as e:
        print(f"!!! 未知错误记录预测上下文: {e}")

def log_trade_settlement(trade_obj, market_snapshot_entry=None, market_snapshot_exit=None, related_prediction_id=None):
    """
    记录交易结算信息

    ⚠️ 注意：此函数已被标记为废弃，建议使用新的统一综合日志系统
    现在会自动重定向到新系统的兼容层
    """
    import warnings
    warnings.warn(
        "analysis_logger.log_trade_settlement 已废弃，请使用统一综合日志系统",
        DeprecationWarning,
        stacklevel=2
    )

    # 使用兼容层
    from ..core.logger_compatibility_layer import log_trade_settlement as compat_log_trade_settlement
    return compat_log_trade_settlement(trade_obj, market_snapshot_entry, market_snapshot_exit, related_prediction_id)

    # 保留原始代码以防需要回退
    if trade_settlement_logger is None:
        return

    try:
        settlement_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'trade_id': trade_obj.trade_id,
            'direction': trade_obj.direction,
            'entry_price': trade_obj.entry_price,
            'exit_price': trade_obj.exit_price,
            'amount_staked': trade_obj.amount_staked,
            'entry_time': trade_obj.entry_time.isoformat(),
            'exit_time': datetime.now().isoformat(),
            'contract_duration_minutes': trade_obj.CONTRACT_DURATION_MINUTES,
            'status': trade_obj.status,
            'profit_loss': trade_obj.profit_loss,
            'entry_market_snapshot_json': json.dumps(market_snapshot_entry or {}),
            'exit_market_snapshot_json': json.dumps(market_snapshot_exit or {}),
            'related_prediction_id': related_prediction_id,
            'symbol': getattr(trade_obj, 'symbol', 'BTCUSDT'),
            'payout_ratio': trade_obj.PAYOUT_RATIO
        }

        trade_settlement_logger.log(settlement_data)

        # 如果是亏损交易，立即触发失败案例分析
        if trade_obj.status == "LOST":
            print(f"[FailureAnalysis] 检测到亏损交易 {trade_obj.trade_id}，将纳入失败案例分析")

    except Exception as e:
        print(f"!!! 记录交易结算失败: {e}")
