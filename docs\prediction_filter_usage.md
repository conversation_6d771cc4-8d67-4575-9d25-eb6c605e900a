# 集中式预测过滤器使用指南

## 概述

集中式预测过滤器 (`PredictionFilter`) 是一个统一的过滤系统，整合了所有预测信号过滤逻辑，包括：

- 动态阈值调整
- 波动率过滤
- 趋势过滤
- 趋势追逐

## 主要优势

1. **集中管理**: 所有过滤规则在一个地方管理，易于维护和修改
2. **一致性**: 确保过滤逻辑的一致性和可预测性
3. **可扩展性**: 易于添加新的过滤规则
4. **错误处理**: 完善的错误处理和日志记录
5. **测试友好**: 独立的过滤器类便于单元测试

## 核心组件

### 1. FilterInput 数据结构
```python
@dataclass
class FilterInput:
    # 基础信号信息
    raw_signal: str                    # 原始信号 ("UP", "DOWN", "Neutral")
    up_probability: float              # 上涨概率
    down_probability: float            # 下跌概率
    target_variable_type: str          # 目标变量类型 ("UP_ONLY", "DOWN_ONLY", "BOTH")
    
    # 市场状态信息
    trend_signal: int                  # 趋势信号 (-1, 0, 1)
    trend_strength: int                # 趋势强度 (0, 1)
    volatility_level: int              # 波动率水平 (-1, 0, 1)
    
    # 配置信息
    signal_threshold: float            # 信号阈值
    target_config: Dict[str, Any]      # 目标配置
    target_name: str                   # 目标名称
```

### 2. FilterResult 数据结构
```python
@dataclass
class FilterResult:
    final_signal: str                  # 最终信号
    action: FilterAction               # 过滤动作
    reasons: List[str]                 # 过滤原因列表
    adjusted_threshold: float          # 调整后的阈值
    prediction_label: str              # 预测标签
    success: bool                      # 是否成功
```

## 使用方法

### 1. 基本使用
```python
from src.core.prediction_filter import PredictionFilter, create_filter_input_from_prediction_data

# 创建过滤器
prediction_filter = PredictionFilter(logger)

# 创建输入数据
filter_input = create_filter_input_from_prediction_data(
    raw_signal="UP",
    up_probability=0.75,
    down_probability=0.25,
    target_variable_type="UP_ONLY",
    trend_signal=1,
    trend_strength=1,
    volatility_level=0,
    signal_threshold=0.6,
    target_config=target_config,
    target_name="BTC_15m_UP",
    # ... 其他参数
)

# 应用过滤器
result = prediction_filter.apply_filters(filter_input)

# 使用结果
final_signal = result.final_signal
adjusted_threshold = result.adjusted_threshold
```

### 2. 在预测流程中集成
过滤器已自动集成到 `run_prediction_cycle_for_target` 函数中，替代了原有的分散过滤逻辑。

## 配置选项

### 动态阈值调整
```python
target_config = {
    'enable_dynamic_threshold': True,
    'dynamic_threshold_base': 0.6,
    'dynamic_threshold_trend_adjust': 0.03,
    'dynamic_threshold_volatility_adjust': 0.02,
    'dynamic_threshold_max_clip': 0.95
}
```

### 波动率过滤
```python
target_config = {
    'enable_volatility_filter': True
}
```

### 趋势过滤
```python
target_config = {
    'enable_trend_detection': True,
    'trend_filter_strategy': 'filter_only',  # 或 'chase_trend'
    'trend_chase_confidence_boost': 0.05
}
```

## 过滤逻辑说明

### 1. 动态阈值调整
- **趋势确认**: 信号与强趋势同向时降低阈值
- **趋势过滤**: 信号与强趋势相反时提高阈值
- **波动率调整**: 异常波动率时提高阈值

### 2. 波动率过滤
- 高波动率或低波动率时过滤信号
- 返回 `Neutral_Filtered_Volatility` 信号

### 3. 趋势过滤
- **过滤模式**: 阻止与强趋势相反的信号
- **追逐模式**: 在强趋势时生成追逐信号

## 错误处理

过滤器包含完善的错误处理机制：

1. **输入验证**: 检查所有输入参数的有效性
2. **异常捕获**: 捕获并处理各种异常情况
3. **错误日志**: 详细记录错误信息和上下文
4. **优雅降级**: 出错时返回安全的默认值

## 扩展指南

### 添加新的过滤规则
1. 在 `PredictionFilter` 类中添加新的过滤方法
2. 在 `apply_filters` 方法中调用新的过滤方法
3. 更新 `FilterReason` 枚举添加新的过滤原因
4. 编写相应的测试用例

### 修改现有过滤逻辑
1. 直接修改 `PredictionFilter` 类中的相应方法
2. 更新配置选项（如需要）
3. 更新测试用例

## 注意事项

1. **配置一致性**: 确保目标配置中的过滤选项与过滤器期望的格式一致
2. **性能考虑**: 过滤器在每次预测时都会执行，避免过于复杂的计算
3. **日志记录**: 过滤器会生成详细的日志，注意日志级别设置
4. **向后兼容**: 如果过滤器不可用，系统会自动回退到传统过滤逻辑

## 故障排除

### 常见问题
1. **导入错误**: 检查 `prediction_filter.py` 文件是否存在
2. **配置错误**: 检查目标配置中的过滤选项
3. **数据验证失败**: 检查输入数据的格式和范围

### 调试技巧
1. 启用详细日志记录
2. 使用独立的测试脚本验证过滤器功能
3. 检查过滤结果的 `success` 字段和 `error_message`
