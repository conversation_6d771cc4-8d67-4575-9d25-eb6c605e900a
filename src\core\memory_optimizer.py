#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 内存优化系统

提供全面的内存优化功能，包括智能拷贝管理、原地操作优化、
分批处理、内存监控和垃圾回收等，显著降低内存使用量。
"""

import pandas as pd
import numpy as np
import logging
import gc
import psutil
import warnings
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from functools import wraps
import time
from contextlib import contextmanager

# 导入配置管理器
try:
    from .config_manager import get_data_processing_param, get_constant
    CONFIG_MANAGER_AVAILABLE = True
except ImportError:
    CONFIG_MANAGER_AVAILABLE = False

logger = logging.getLogger(__name__)


class MemoryOptimizer:
    """
    🚀 内存优化器
    
    提供全面的内存优化功能，包括智能拷贝管理、原地操作、
    分批处理和内存监控等。
    """
    
    def __init__(self, target_config: Optional[Dict[str, Any]] = None):
        """
        初始化内存优化器
        
        Args:
            target_config: 目标配置字典
        """
        self.target_config = target_config or {}
        
        # 🚀 从配置获取参数
        if CONFIG_MANAGER_AVAILABLE:
            self.optimization_level = get_data_processing_param('memory_optimization_level', target_config, 2, int)
            self.enable_auto_gc = get_data_processing_param('enable_auto_garbage_collection', target_config, True, bool)
            self.chunk_size = get_data_processing_param('memory_chunk_size', target_config, 10000, int)
            self.memory_threshold_mb = get_data_processing_param('memory_warning_threshold_mb', target_config, 4096, int)
        else:
            self.optimization_level = 2  # 1=保守, 2=平衡, 3=激进
            self.enable_auto_gc = True
            self.chunk_size = 10000
            self.memory_threshold_mb = 4096
        
        # 内存使用统计
        self.memory_stats = {
            'peak_usage_mb': 0.0,
            'current_usage_mb': 0.0,
            'copies_avoided': 0,
            'inplace_operations': 0,
            'gc_collections': 0,
            'chunks_processed': 0
        }
        
        # 优化策略配置
        self.optimization_strategies = {
            1: {  # 保守模式
                'avoid_unnecessary_copies': True,
                'use_inplace_operations': False,
                'enable_chunking': False,
                'aggressive_gc': False,
                'dtype_optimization': False
            },
            2: {  # 平衡模式
                'avoid_unnecessary_copies': True,
                'use_inplace_operations': True,
                'enable_chunking': True,
                'aggressive_gc': True,
                'dtype_optimization': True
            },
            3: {  # 激进模式
                'avoid_unnecessary_copies': True,
                'use_inplace_operations': True,
                'enable_chunking': True,
                'aggressive_gc': True,
                'dtype_optimization': True,
                'use_views_when_possible': True,
                'minimize_intermediate_results': True
            }
        }
        
        self.current_strategy = self.optimization_strategies.get(self.optimization_level, self.optimization_strategies[2])
    
    def optimize_dataframe_copy(
        self,
        df: pd.DataFrame,
        operation_type: str = 'general',
        force_copy: bool = False,
        verbose: bool = False
    ) -> pd.DataFrame:
        """
        🚀 智能DataFrame拷贝优化
        
        Args:
            df: 输入DataFrame
            operation_type: 操作类型 ('read_only', 'modify', 'general')
            force_copy: 是否强制拷贝
            verbose: 是否显示详细信息
            
        Returns:
            优化后的DataFrame
        """
        if force_copy or not self.current_strategy['avoid_unnecessary_copies']:
            if verbose:
                logger.debug(f"optimize_dataframe_copy: 执行强制拷贝 ({operation_type})")
            return df.copy()
        
        # 根据操作类型决定是否需要拷贝
        if operation_type == 'read_only':
            # 只读操作，不需要拷贝
            self.memory_stats['copies_avoided'] += 1
            if verbose:
                logger.debug(f"optimize_dataframe_copy: 避免只读拷贝，节省内存")
            return df
        
        elif operation_type == 'modify':
            # 修改操作，需要拷贝以保护原数据
            if verbose:
                logger.debug(f"optimize_dataframe_copy: 修改操作需要拷贝")
            return df.copy()
        
        else:
            # 一般操作，根据优化级别决定
            if self.optimization_level >= 2:
                # 尝试避免拷贝
                self.memory_stats['copies_avoided'] += 1
                if verbose:
                    logger.debug(f"optimize_dataframe_copy: 优化级别{self.optimization_level}，避免拷贝")
                return df
            else:
                if verbose:
                    logger.debug(f"optimize_dataframe_copy: 保守模式，执行拷贝")
                return df.copy()
    
    def optimize_inplace_operation(
        self,
        df: pd.DataFrame,
        operation: Callable,
        *args,
        use_inplace: Optional[bool] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        🚀 优化原地操作
        
        Args:
            df: 输入DataFrame
            operation: 要执行的操作函数
            use_inplace: 是否使用原地操作（None为自动决定）
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作后的DataFrame
        """
        if use_inplace is None:
            use_inplace = self.current_strategy['use_inplace_operations']
        
        if use_inplace:
            try:
                # 尝试原地操作
                if 'inplace' in kwargs:
                    kwargs['inplace'] = True
                else:
                    # 检查操作是否支持inplace参数
                    import inspect
                    sig = inspect.signature(operation)
                    if 'inplace' in sig.parameters:
                        kwargs['inplace'] = True
                
                result = operation(df, *args, **kwargs)
                
                if result is None:  # 原地操作通常返回None
                    result = df
                
                self.memory_stats['inplace_operations'] += 1
                return result
                
            except Exception as e:
                logger.warning(f"optimize_inplace_operation: 原地操作失败，回退到普通操作: {e}")
                # 回退到普通操作
                kwargs.pop('inplace', None)
                return operation(df, *args, **kwargs)
        else:
            # 普通操作
            kwargs.pop('inplace', None)
            return operation(df, *args, **kwargs)
    
    def process_in_chunks(
        self,
        df: pd.DataFrame,
        operation: Callable,
        chunk_size: Optional[int] = None,
        verbose: bool = False,
        **kwargs
    ) -> pd.DataFrame:
        """
        🚀 分块处理大数据集
        
        Args:
            df: 输入DataFrame
            operation: 处理函数
            chunk_size: 块大小
            verbose: 是否显示详细信息
            **kwargs: 操作函数的参数
            
        Returns:
            处理后的DataFrame
        """
        if chunk_size is None:
            chunk_size = self.chunk_size
        
        # 检查是否需要分块处理
        if len(df) <= chunk_size or not self.current_strategy['enable_chunking']:
            return operation(df, **kwargs)
        
        if verbose:
            logger.info(f"process_in_chunks: 分块处理 {len(df)} 行数据，块大小: {chunk_size}")
        
        results = []
        total_chunks = (len(df) + chunk_size - 1) // chunk_size
        
        for i in range(0, len(df), chunk_size):
            chunk_start = i
            chunk_end = min(i + chunk_size, len(df))
            chunk = df.iloc[chunk_start:chunk_end]
            
            if verbose:
                logger.debug(f"process_in_chunks: 处理块 {i//chunk_size + 1}/{total_chunks}")
            
            # 处理当前块
            try:
                chunk_result = operation(chunk, **kwargs)
                if chunk_result is not None:
                    results.append(chunk_result)
                
                self.memory_stats['chunks_processed'] += 1
                
                # 定期垃圾回收
                if self.enable_auto_gc and (i // chunk_size) % 5 == 0:
                    self._perform_garbage_collection()
                
            except Exception as e:
                logger.error(f"process_in_chunks: 处理块 {i//chunk_size + 1} 失败: {e}")
                continue
        
        if not results:
            logger.warning("process_in_chunks: 没有成功处理的块")
            return df
        
        # 合并结果
        try:
            final_result = pd.concat(results, ignore_index=True)
            if verbose:
                logger.info(f"process_in_chunks: 分块处理完成，最终形状: {final_result.shape}")
            return final_result
        except Exception as e:
            logger.error(f"process_in_chunks: 合并结果失败: {e}")
            return df
    
    def optimize_dtype(
        self,
        df: pd.DataFrame,
        aggressive: bool = False,
        verbose: bool = False
    ) -> pd.DataFrame:
        """
        🚀 优化数据类型以节省内存
        
        Args:
            df: 输入DataFrame
            aggressive: 是否使用激进优化
            verbose: 是否显示详细信息
            
        Returns:
            优化后的DataFrame
        """
        if not self.current_strategy['dtype_optimization']:
            return df
        
        if verbose:
            original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
            logger.info(f"optimize_dtype: 开始数据类型优化，原始内存: {original_memory:.2f} MB")
        
        df_optimized = df.copy() if not aggressive else df
        
        for col in df_optimized.columns:
            col_type = df_optimized[col].dtype
            
            try:
                # 数值类型优化
                if col_type in ['int64', 'int32']:
                    # 尝试降级整数类型
                    col_min = df_optimized[col].min()
                    col_max = df_optimized[col].max()
                    
                    if col_min >= -128 and col_max <= 127:
                        df_optimized[col] = df_optimized[col].astype('int8')
                    elif col_min >= -32768 and col_max <= 32767:
                        df_optimized[col] = df_optimized[col].astype('int16')
                    elif col_min >= -2147483648 and col_max <= 2147483647:
                        df_optimized[col] = df_optimized[col].astype('int32')
                
                elif col_type == 'float64':
                    # 尝试降级浮点类型
                    if aggressive:
                        # 激进模式：尝试转换为float32
                        df_optimized[col] = pd.to_numeric(df_optimized[col], downcast='float')
                    else:
                        # 保守模式：只在精度损失可接受时转换
                        original_values = df_optimized[col].values
                        float32_values = original_values.astype('float32')
                        
                        # 检查精度损失
                        if np.allclose(original_values, float32_values, rtol=1e-6, equal_nan=True):
                            df_optimized[col] = float32_values
                
                elif col_type == 'object':
                    # 尝试转换为分类类型
                    unique_count = df_optimized[col].nunique()
                    total_count = len(df_optimized[col])
                    
                    if unique_count / total_count < 0.5:  # 重复率高于50%
                        df_optimized[col] = df_optimized[col].astype('category')
                
            except Exception as e:
                if verbose:
                    logger.warning(f"optimize_dtype: 优化列 '{col}' 失败: {e}")
                continue
        
        if verbose:
            optimized_memory = df_optimized.memory_usage(deep=True).sum() / 1024 / 1024
            memory_saved = original_memory - optimized_memory
            logger.info(f"optimize_dtype: 优化完成，优化后内存: {optimized_memory:.2f} MB，节省: {memory_saved:.2f} MB")
        
        return df_optimized
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            memory_usage = {
                'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
                'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
                'percent': process.memory_percent(),       # 内存使用百分比
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
            
            # 更新统计
            self.memory_stats['current_usage_mb'] = memory_usage['rss_mb']
            if memory_usage['rss_mb'] > self.memory_stats['peak_usage_mb']:
                self.memory_stats['peak_usage_mb'] = memory_usage['rss_mb']
            
            return memory_usage
            
        except Exception as e:
            logger.warning(f"get_memory_usage: 获取内存使用情况失败: {e}")
            return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0, 'available_mb': 0}
    
    def check_memory_pressure(self, verbose: bool = False) -> bool:
        """
        检查内存压力
        
        Returns:
            True if memory pressure is high
        """
        memory_usage = self.get_memory_usage()
        
        # 检查内存使用是否超过阈值
        memory_pressure = (
            memory_usage['rss_mb'] > self.memory_threshold_mb or
            memory_usage['percent'] > 80
        )
        
        if memory_pressure and verbose:
            logger.warning(f"check_memory_pressure: 检测到内存压力 - "
                          f"使用: {memory_usage['rss_mb']:.1f} MB ({memory_usage['percent']:.1f}%)")
        
        return memory_pressure
    
    def _perform_garbage_collection(self, verbose: bool = False):
        """执行垃圾回收"""
        if not self.enable_auto_gc:
            return

        if verbose:
            memory_before = self.get_memory_usage()['rss_mb']

        # 执行垃圾回收
        collected = gc.collect()
        self.memory_stats['gc_collections'] += 1

        if verbose:
            memory_after = self.get_memory_usage()['rss_mb']
            memory_freed = memory_before - memory_after
            logger.debug(f"_perform_garbage_collection: 回收 {collected} 个对象，"
                        f"释放 {memory_freed:.1f} MB 内存")

    def force_garbage_collection(self, verbose: bool = False):
        """
        强制执行垃圾回收（公共接口）

        Args:
            verbose: 是否输出详细信息

        Returns:
            int: 回收的对象数量
        """
        if verbose:
            memory_before = self.get_memory_usage()['rss_mb']

        collected = gc.collect()
        self.memory_stats['gc_collections'] += 1

        if verbose:
            memory_after = self.get_memory_usage()['rss_mb']
            memory_freed = memory_before - memory_after
            logger.info(f"force_garbage_collection: 回收 {collected} 个对象，"
                       f"释放 {memory_freed:.1f} MB 内存")

        return collected
    
    @contextmanager
    def memory_monitor(self, operation_name: str = "operation", verbose: bool = True):
        """
        内存监控上下文管理器
        
        Args:
            operation_name: 操作名称
            verbose: 是否显示详细信息
        """
        # 记录开始状态
        start_memory = self.get_memory_usage()
        start_time = time.time()
        
        if verbose:
            logger.info(f"memory_monitor: 开始 {operation_name} - "
                       f"内存使用: {start_memory['rss_mb']:.1f} MB")
        
        try:
            yield self
        finally:
            # 记录结束状态
            end_memory = self.get_memory_usage()
            end_time = time.time()
            
            memory_delta = end_memory['rss_mb'] - start_memory['rss_mb']
            time_elapsed = end_time - start_time
            
            if verbose:
                logger.info(f"memory_monitor: 完成 {operation_name} - "
                           f"内存变化: {memory_delta:+.1f} MB, "
                           f"耗时: {time_elapsed:.2f}秒")
            
            # 如果内存使用过高，执行垃圾回收
            if self.check_memory_pressure():
                self._perform_garbage_collection(verbose)
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        return {
            'memory_stats': self.memory_stats.copy(),
            'optimization_level': self.optimization_level,
            'current_strategy': self.current_strategy.copy(),
            'current_memory_usage': self.get_memory_usage()
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.memory_stats = {
            'peak_usage_mb': 0.0,
            'current_usage_mb': 0.0,
            'copies_avoided': 0,
            'inplace_operations': 0,
            'gc_collections': 0,
            'chunks_processed': 0
        }


# 全局内存优化器实例
_memory_optimizer = None

def get_memory_optimizer(target_config: Optional[Dict[str, Any]] = None) -> MemoryOptimizer:
    """获取全局内存优化器实例（单例模式）"""
    global _memory_optimizer
    if _memory_optimizer is None:
        _memory_optimizer = MemoryOptimizer(target_config)
    return _memory_optimizer


# 装饰器函数
def memory_optimized(
    operation_type: str = 'general',
    enable_chunking: bool = False,
    chunk_size: Optional[int] = None,
    monitor_memory: bool = True
):
    """
    🚀 内存优化装饰器
    
    Args:
        operation_type: 操作类型 ('read_only', 'modify', 'general')
        enable_chunking: 是否启用分块处理
        chunk_size: 块大小
        monitor_memory: 是否监控内存
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            optimizer = get_memory_optimizer()
            
            # 内存监控
            if monitor_memory:
                with optimizer.memory_monitor(func.__name__):
                    # 检查第一个参数是否为DataFrame
                    if len(args) > 0 and isinstance(args[0], pd.DataFrame):
                        df = args[0]
                        
                        # 优化DataFrame拷贝
                        optimized_df = optimizer.optimize_dataframe_copy(
                            df, operation_type, verbose=False
                        )
                        
                        # 替换第一个参数
                        args = (optimized_df,) + args[1:]
                        
                        # 分块处理（如果启用）
                        if enable_chunking and len(df) > (chunk_size or optimizer.chunk_size):
                            return optimizer.process_in_chunks(
                                optimized_df, func, chunk_size, **kwargs
                            )
                    
                    return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


# 便捷函数
def optimize_dataframe_memory(
    df: pd.DataFrame,
    target_config: Optional[Dict[str, Any]] = None,
    aggressive: bool = False,
    verbose: bool = True
) -> pd.DataFrame:
    """
    🚀 便捷的DataFrame内存优化函数
    
    Args:
        df: 输入DataFrame
        target_config: 目标配置字典
        aggressive: 是否使用激进优化
        verbose: 是否显示详细信息
        
    Returns:
        优化后的DataFrame
    """
    optimizer = MemoryOptimizer(target_config)
    
    with optimizer.memory_monitor("DataFrame内存优化", verbose):
        # 数据类型优化
        df_optimized = optimizer.optimize_dtype(df, aggressive, verbose)
        
        return df_optimized


def monitor_memory_usage(operation_name: str = "operation", verbose: bool = True):
    """
    🚀 便捷的内存监控上下文管理器
    
    Args:
        operation_name: 操作名称
        verbose: 是否显示详细信息
    """
    optimizer = get_memory_optimizer()
    return optimizer.memory_monitor(operation_name, verbose)


def get_memory_stats() -> Dict[str, Any]:
    """获取内存使用统计"""
    optimizer = get_memory_optimizer()
    return optimizer.get_optimization_stats()


class MTFAMemoryOptimizer:
    """
    🚀 MTFA专用内存优化器

    专门针对多时间框架分析(MTFA)的内存优化，
    解决MTFA处理中的内存压力问题。
    """

    def __init__(self, target_config: Optional[Dict[str, Any]] = None):
        """初始化MTFA内存优化器"""
        self.target_config = target_config or {}
        self.base_optimizer = MemoryOptimizer(target_config)

        # MTFA特定配置
        if CONFIG_MANAGER_AVAILABLE:
            self.mtfa_chunk_size = get_data_processing_param('mtfa_chunk_size', target_config, 5000, int)
            self.enable_mtfa_caching = get_data_processing_param('enable_mtfa_caching', target_config, True, bool)
            self.max_concurrent_timeframes = get_data_processing_param('max_concurrent_timeframes', target_config, 3, int)
        else:
            self.mtfa_chunk_size = 5000
            self.enable_mtfa_caching = True
            self.max_concurrent_timeframes = 3

        # MTFA缓存
        self.mtfa_cache = {}
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0}

    def optimize_mtfa_processing(
        self,
        primary_df: pd.DataFrame,
        mtfa_timeframes: List[str],
        target_config: Dict[str, Any],
        verbose: bool = True
    ) -> pd.DataFrame:
        """
        🚀 优化MTFA处理流程

        Args:
            primary_df: 主时间框架数据
            mtfa_timeframes: MTFA时间框架列表
            target_config: 目标配置
            verbose: 是否显示详细信息

        Returns:
            包含MTFA特征的DataFrame
        """
        with self.base_optimizer.memory_monitor("MTFA处理优化", verbose):
            # 检查内存压力
            if self.base_optimizer.check_memory_pressure():
                logger.warning("optimize_mtfa_processing: 检测到内存压力，启用激进优化")
                return self._process_mtfa_memory_constrained(
                    primary_df, mtfa_timeframes, target_config, verbose
                )
            else:
                return self._process_mtfa_normal(
                    primary_df, mtfa_timeframes, target_config, verbose
                )

    def _process_mtfa_normal(
        self,
        primary_df: pd.DataFrame,
        mtfa_timeframes: List[str],
        target_config: Dict[str, Any],
        verbose: bool
    ) -> pd.DataFrame:
        """正常内存条件下的MTFA处理"""
        # 智能拷贝主数据
        df_result = self.base_optimizer.optimize_dataframe_copy(primary_df, 'modify', verbose=False)

        # 批量处理时间框架
        batch_size = min(self.max_concurrent_timeframes, len(mtfa_timeframes))

        for i in range(0, len(mtfa_timeframes), batch_size):
            batch_timeframes = mtfa_timeframes[i:i + batch_size]

            if verbose:
                logger.info(f"_process_mtfa_normal: 处理时间框架批次 {i//batch_size + 1}: {batch_timeframes}")

            # 处理当前批次
            for tf in batch_timeframes:
                try:
                    # 检查缓存
                    cache_key = f"{tf}_{len(primary_df)}_{hash(str(target_config))}"

                    if self.enable_mtfa_caching and cache_key in self.mtfa_cache:
                        tf_features = self.mtfa_cache[cache_key]
                        self.cache_stats['hits'] += 1
                        if verbose:
                            logger.debug(f"_process_mtfa_normal: 使用缓存的 {tf} 特征")
                    else:
                        # 计算新特征
                        tf_features = self._calculate_timeframe_features(
                            primary_df, tf, target_config, verbose
                        )

                        # 缓存结果
                        if self.enable_mtfa_caching and tf_features is not None:
                            self._cache_timeframe_features(cache_key, tf_features)

                        self.cache_stats['misses'] += 1

                    # 合并特征
                    if tf_features is not None:
                        df_result = self._merge_timeframe_features(df_result, tf_features, tf)

                except Exception as e:
                    logger.error(f"_process_mtfa_normal: 处理时间框架 {tf} 失败: {e}")
                    continue

            # 批次间垃圾回收
            if self.base_optimizer.enable_auto_gc:
                self.base_optimizer._perform_garbage_collection(verbose=False)

        return df_result

    def _process_mtfa_memory_constrained(
        self,
        primary_df: pd.DataFrame,
        mtfa_timeframes: List[str],
        target_config: Dict[str, Any],
        verbose: bool
    ) -> pd.DataFrame:
        """内存受限条件下的MTFA处理"""
        if verbose:
            logger.info("_process_mtfa_memory_constrained: 启用内存受限模式")

        # 使用分块处理
        if len(primary_df) > self.mtfa_chunk_size:
            return self._process_mtfa_chunked(primary_df, mtfa_timeframes, target_config, verbose)

        # 逐个处理时间框架，最小化内存使用
        df_result = self.base_optimizer.optimize_dataframe_copy(primary_df, 'read_only', verbose=False)

        for tf in mtfa_timeframes:
            try:
                if verbose:
                    logger.debug(f"_process_mtfa_memory_constrained: 处理时间框架 {tf}")

                # 立即处理和合并，不保留中间结果
                tf_features = self._calculate_timeframe_features(
                    primary_df, tf, target_config, verbose=False
                )

                if tf_features is not None:
                    df_result = self._merge_timeframe_features(df_result, tf_features, tf)

                    # 立即删除中间结果
                    del tf_features

                # 强制垃圾回收
                gc.collect()

            except Exception as e:
                logger.error(f"_process_mtfa_memory_constrained: 处理时间框架 {tf} 失败: {e}")
                continue

        return df_result

    def _process_mtfa_chunked(
        self,
        primary_df: pd.DataFrame,
        mtfa_timeframes: List[str],
        target_config: Dict[str, Any],
        verbose: bool
    ) -> pd.DataFrame:
        """分块处理MTFA"""
        if verbose:
            logger.info(f"_process_mtfa_chunked: 分块处理 {len(primary_df)} 行数据，块大小: {self.mtfa_chunk_size}")

        chunk_results = []
        total_chunks = (len(primary_df) + self.mtfa_chunk_size - 1) // self.mtfa_chunk_size

        for i in range(0, len(primary_df), self.mtfa_chunk_size):
            chunk_start = i
            chunk_end = min(i + self.mtfa_chunk_size, len(primary_df))
            chunk = primary_df.iloc[chunk_start:chunk_end]

            if verbose:
                logger.debug(f"_process_mtfa_chunked: 处理块 {i//self.mtfa_chunk_size + 1}/{total_chunks}")

            try:
                # 处理当前块的MTFA特征
                chunk_result = self._process_mtfa_normal(chunk, mtfa_timeframes, target_config, verbose=False)
                chunk_results.append(chunk_result)

                # 定期垃圾回收
                if (i // self.mtfa_chunk_size) % 3 == 0:
                    gc.collect()

            except Exception as e:
                logger.error(f"_process_mtfa_chunked: 处理块 {i//self.mtfa_chunk_size + 1} 失败: {e}")
                continue

        # 合并所有块的结果
        if chunk_results:
            final_result = pd.concat(chunk_results, ignore_index=True)
            if verbose:
                logger.info(f"_process_mtfa_chunked: 分块处理完成，最终形状: {final_result.shape}")
            return final_result
        else:
            logger.warning("_process_mtfa_chunked: 没有成功处理的块")
            return primary_df

    def _calculate_timeframe_features(
        self,
        df: pd.DataFrame,
        timeframe: str,
        target_config: Dict[str, Any],
        verbose: bool
    ) -> Optional[pd.DataFrame]:
        """计算单个时间框架的特征"""
        try:
            # 这里应该调用实际的特征计算函数
            # 为了避免循环导入，这里只是一个占位符
            logger.debug(f"_calculate_timeframe_features: 计算 {timeframe} 特征")

            # 实际实现中，这里会调用相应的特征计算函数
            # 例如：add_classification_features(df, modified_config)

            return None  # 占位符返回

        except Exception as e:
            logger.error(f"_calculate_timeframe_features: 计算 {timeframe} 特征失败: {e}")
            return None

    def _merge_timeframe_features(
        self,
        main_df: pd.DataFrame,
        tf_features: pd.DataFrame,
        timeframe: str
    ) -> pd.DataFrame:
        """合并时间框架特征"""
        try:
            # 使用内存优化的合并策略
            if self.base_optimizer.current_strategy['use_inplace_operations']:
                # 尝试原地合并
                return pd.merge(main_df, tf_features, left_index=True, right_index=True, how='left')
            else:
                # 普通合并
                return pd.merge(main_df, tf_features, left_index=True, right_index=True, how='left')

        except Exception as e:
            logger.error(f"_merge_timeframe_features: 合并 {timeframe} 特征失败: {e}")
            return main_df

    def _cache_timeframe_features(self, cache_key: str, features: pd.DataFrame):
        """缓存时间框架特征"""
        if not self.enable_mtfa_caching:
            return

        # 简单的LRU缓存实现
        max_cache_size = 10

        if len(self.mtfa_cache) >= max_cache_size:
            # 删除最旧的缓存项
            oldest_key = next(iter(self.mtfa_cache))
            del self.mtfa_cache[oldest_key]
            self.cache_stats['evictions'] += 1

        self.mtfa_cache[cache_key] = features.copy()

    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计"""
        return self.cache_stats.copy()

    def clear_cache(self):
        """清空缓存"""
        self.mtfa_cache.clear()
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0}


# MTFA内存优化器便捷函数
def get_mtfa_memory_optimizer(target_config: Optional[Dict[str, Any]] = None) -> MTFAMemoryOptimizer:
    """获取MTFA内存优化器实例"""
    return MTFAMemoryOptimizer(target_config)
