"""
K线数据缓存系统
提供智能缓存机制，避免重复下载相同的K线数据，提升训练性能
"""

import os
import pickle
import json
import pandas as pd
from datetime import datetime, timedelta, timezone
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class KlineDataCache:
    """K线数据智能缓存系统"""
    
    def __init__(self, cache_dir="cache/kline_data"):
        """
        初始化缓存系统
        
        Args:
            cache_dir: 缓存目录路径
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.metadata_file = self.cache_dir.parent / "cache_metadata.json"
        self.metadata = self._load_metadata()
        
        # 缓存有效期配置
        self.cache_expire_hours = 2  # 缓存2小时后过期
        self.max_cache_days = 3      # 最多保留3天的缓存文件
        
        logger.info(f"[KlineCache] 缓存系统初始化完成，缓存目录: {self.cache_dir}")
    
    def get_cache_key(self, symbol, timeframe, limit):
        """
        生成缓存键

        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            limit: 数据条数

        Returns:
            str: 缓存键
        """
        today = datetime.now().strftime("%Y%m%d")
        # 🎯 优化：为不同时间框架使用不同的缓存键，支持合理的数据量差异
        return f"{symbol}_{timeframe}_{limit}_{today}"
    
    def get_cache_file_path(self, cache_key):
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.pkl"
    
    def is_cache_valid(self, cache_key, required_limit):
        """
        检查缓存是否有效
        
        Args:
            cache_key: 缓存键
            required_limit: 需要的数据条数
            
        Returns:
            bool: 缓存是否有效
        """
        cache_file = self.get_cache_file_path(cache_key)
        
        # 检查文件是否存在
        if not cache_file.exists():
            return False
        
        # 检查文件修改时间
        file_mtime = datetime.fromtimestamp(cache_file.stat().st_mtime)
        if datetime.now() - file_mtime > timedelta(hours=self.cache_expire_hours):
            logger.info(f"[KlineCache] 缓存已过期: {cache_key}")
            return False
        
        # 检查元数据
        if cache_key in self.metadata:
            cache_info = self.metadata[cache_key]
            
            # 检查数据条数是否足够
            if cache_info.get('data_count', 0) < required_limit:
                logger.info(f"[KlineCache] 缓存数据量不足: {cache_key} ({cache_info.get('data_count', 0)} < {required_limit})")
                return False
            
            # 检查最新数据时间戳
            latest_time_str = cache_info.get('latest_timestamp')
            if latest_time_str:
                try:
                    latest_time = pd.to_datetime(latest_time_str)
                    # 确保两个datetime对象都是timezone-aware的
                    current_time = datetime.now(timezone.utc)
                    latest_time_dt = latest_time.to_pydatetime()

                    # 如果latest_time是timezone-naive，则假设为UTC
                    if latest_time_dt.tzinfo is None:
                        latest_time_dt = latest_time_dt.replace(tzinfo=timezone.utc)

                    time_diff = current_time - latest_time_dt
                    if time_diff > timedelta(hours=1):  # 最新数据超过1小时
                        logger.info(f"[KlineCache] 缓存数据太旧: {cache_key} (最新数据: {latest_time_str})")
                        return False
                except Exception as e:
                    logger.warning(f"[KlineCache] 解析时间戳失败: {e}")
                    return False
        
        return True
    
    def get_cached_data(self, symbol, timeframe, limit):
        """
        获取缓存的K线数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            limit: 数据条数
            
        Returns:
            pd.DataFrame or None: 缓存的数据，如果无效则返回None
        """
        cache_key = self.get_cache_key(symbol, timeframe, limit)
        
        if not self.is_cache_valid(cache_key, limit):
            return None
        
        cache_file = self.get_cache_file_path(cache_key)
        
        try:
            logger.info(f"[KlineCache] 从缓存加载数据: {cache_key}")
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
            
            # 确保返回足够的数据
            if len(data) >= limit:
                return data.tail(limit)  # 返回最新的limit条数据
            else:
                logger.warning(f"[KlineCache] 缓存数据量不足，重新获取: {cache_key}")
                return None
                
        except Exception as e:
            logger.error(f"[KlineCache] 加载缓存失败: {cache_key}, 错误: {e}")
            # 删除损坏的缓存文件
            try:
                cache_file.unlink()
                if cache_key in self.metadata:
                    del self.metadata[cache_key]
                    self._save_metadata()
            except:
                pass
            return None
    
    def save_to_cache(self, symbol, timeframe, limit, data):
        """
        保存数据到缓存
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            limit: 数据条数
            data: K线数据DataFrame
        """
        if data is None or data.empty:
            return
        
        cache_key = self.get_cache_key(symbol, timeframe, limit)
        cache_file = self.get_cache_file_path(cache_key)
        
        try:
            # 保存数据到pkl文件
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            # 更新元数据
            self.metadata[cache_key] = {
                'symbol': symbol,
                'timeframe': timeframe,
                'limit': limit,
                'data_count': len(data),
                'latest_timestamp': str(data.index[-1]) if not data.empty else None,
                'cache_time': datetime.now().isoformat(),
                'file_size': cache_file.stat().st_size
            }
            
            self._save_metadata()
            
            logger.info(f"[KlineCache] 数据已缓存: {cache_key} ({len(data)} 条数据)")
            
        except Exception as e:
            logger.error(f"[KlineCache] 保存缓存失败: {cache_key}, 错误: {e}")
    
    def cleanup_old_cache(self):
        """清理过期的缓存文件"""
        logger.info("[KlineCache] 开始清理过期缓存...")
        
        cutoff_time = datetime.now() - timedelta(days=self.max_cache_days)
        cleaned_count = 0
        
        # 清理过期文件
        for cache_file in self.cache_dir.glob("*.pkl"):
            try:
                file_mtime = datetime.fromtimestamp(cache_file.stat().st_mtime)
                if file_mtime < cutoff_time:
                    cache_file.unlink()
                    cleaned_count += 1
                    
                    # 从元数据中移除
                    cache_key = cache_file.stem
                    if cache_key in self.metadata:
                        del self.metadata[cache_key]
                        
            except Exception as e:
                logger.warning(f"[KlineCache] 清理文件失败: {cache_file}, 错误: {e}")
        
        # 清理元数据中的无效条目
        invalid_keys = []
        for cache_key in self.metadata:
            cache_file = self.get_cache_file_path(cache_key)
            if not cache_file.exists():
                invalid_keys.append(cache_key)
        
        for key in invalid_keys:
            del self.metadata[key]
            cleaned_count += 1
        
        if cleaned_count > 0:
            self._save_metadata()
            logger.info(f"[KlineCache] 清理完成，删除了 {cleaned_count} 个过期缓存")
        else:
            logger.info("[KlineCache] 没有需要清理的缓存")
    
    def get_cache_stats(self):
        """获取缓存统计信息"""
        total_files = len(list(self.cache_dir.glob("*.pkl")))
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob("*.pkl"))
        
        return {
            'total_files': total_files,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'cache_dir': str(self.cache_dir),
            'metadata_entries': len(self.metadata)
        }
    
    def _load_metadata(self):
        """加载缓存元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"[KlineCache] 加载元数据失败: {e}")
        return {}
    
    def _save_metadata(self):
        """保存缓存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"[KlineCache] 保存元数据失败: {e}")


# 全局缓存实例
_global_cache = None

def get_kline_cache():
    """获取全局K线缓存实例"""
    global _global_cache
    if _global_cache is None:
        _global_cache = KlineDataCache()
    return _global_cache
