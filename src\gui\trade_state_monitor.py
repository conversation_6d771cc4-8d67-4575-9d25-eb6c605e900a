# trade_state_monitor.py
"""
交易状态监控GUI组件
提供交易状态的可视化监控和管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional
import json

class TradeStateMonitorFrame:
    """交易状态监控框架"""
    
    def __init__(self, parent_frame):
        self.parent = parent_frame
        self.trade_state_manager = None
        self.update_thread = None
        self.is_running = False
        
        # 创建GUI组件
        self._create_widgets()
        
        # 启动监控
        self._start_monitoring()
    
    def _create_widgets(self):
        """创建GUI组件"""
        # 主框架
        self.main_frame = ttk.LabelFrame(self.parent, text="🔒 交易状态监控", padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 状态显示区域
        self._create_status_section()
        
        # 控制按钮区域
        self._create_control_section()
        
        # 历史记录区域
        self._create_history_section()
    
    def _create_status_section(self):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(self.main_frame, text="当前状态", padding="5")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 状态信息显示
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X)
        
        # 左侧状态信息
        left_frame = ttk.Frame(info_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 当前状态
        ttk.Label(left_frame, text="交易状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.status_label = ttk.Label(left_frame, text="未知", foreground="gray")
        self.status_label.grid(row=0, column=1, sticky=tk.W)
        
        # 持续时间
        ttk.Label(left_frame, text="持续时间:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.duration_label = ttk.Label(left_frame, text="--", foreground="gray")
        self.duration_label.grid(row=1, column=1, sticky=tk.W)
        
        # 交易信息
        ttk.Label(left_frame, text="交易信息:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.trade_info_label = ttk.Label(left_frame, text="--", foreground="gray")
        self.trade_info_label.grid(row=2, column=1, sticky=tk.W)
        
        # 右侧统计信息
        right_frame = ttk.Frame(info_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH)
        
        # 过滤信号数量
        ttk.Label(right_frame, text="过滤信号:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.filtered_count_label = ttk.Label(right_frame, text="0", foreground="orange")
        self.filtered_count_label.grid(row=0, column=1, sticky=tk.W)
        
        # 交易历史数量
        ttk.Label(right_frame, text="历史交易:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.history_count_label = ttk.Label(right_frame, text="0", foreground="blue")
        self.history_count_label.grid(row=1, column=1, sticky=tk.W)
        
        # 功能状态
        ttk.Label(right_frame, text="功能状态:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.enabled_label = ttk.Label(right_frame, text="未知", foreground="gray")
        self.enabled_label.grid(row=2, column=1, sticky=tk.W)
    
    def _create_control_section(self):
        """创建控制按钮区域"""
        control_frame = ttk.LabelFrame(self.main_frame, text="控制操作", padding="5")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        # 强制重置按钮
        self.reset_button = ttk.Button(
            button_frame, 
            text="🔄 强制重置", 
            command=self._force_reset,
            style="Accent.TButton"
        )
        self.reset_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        self.refresh_button = ttk.Button(
            button_frame, 
            text="🔄 刷新状态", 
            command=self._refresh_status
        )
        self.refresh_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看详情按钮
        self.details_button = ttk.Button(
            button_frame, 
            text="📊 查看详情", 
            command=self._show_details
        )
        self.details_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清理历史按钮
        self.clear_button = ttk.Button(
            button_frame, 
            text="🗑️ 清理历史", 
            command=self._clear_history
        )
        self.clear_button.pack(side=tk.LEFT)
    
    def _create_history_section(self):
        """创建历史记录区域"""
        history_frame = ttk.LabelFrame(self.main_frame, text="最近活动", padding="5")
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview显示历史记录
        columns = ("时间", "类型", "状态", "信息")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=8)
        
        # 设置列标题
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=120)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _start_monitoring(self):
        """启动监控线程"""
        self.is_running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
    
    def _update_loop(self):
        """更新循环"""
        while self.is_running:
            try:
                self._update_status()
                time.sleep(2)  # 每2秒更新一次
            except Exception as e:
                print(f"交易状态监控更新异常: {e}")
                time.sleep(5)  # 出错时等待更长时间
    
    def _update_status(self):
        """更新状态显示"""
        try:
            # 导入交易状态管理器
            if self.trade_state_manager is None:
                try:
                    from ..core.trade_state_manager import trade_state_manager
                    self.trade_state_manager = trade_state_manager
                except ImportError:
                    return
            
            # 获取状态信息
            status = self.trade_state_manager.get_status()
            
            # 在主线程中更新GUI
            self.parent.after(0, self._update_gui_status, status)
            
        except Exception as e:
            print(f"获取交易状态失败: {e}")
    
    def _update_gui_status(self, status: Dict[str, Any]):
        """在主线程中更新GUI状态"""
        try:
            # 更新状态标签
            current_state = status.get('current_state', 'unknown')
            self.status_label.config(text=current_state)
            
            # 设置状态颜色
            if current_state == 'idle':
                self.status_label.config(foreground="green")
            elif current_state in ['opening', 'closing']:
                self.status_label.config(foreground="orange")
            elif current_state == 'active':
                self.status_label.config(foreground="red")
            elif current_state == 'error':
                self.status_label.config(foreground="red")
            else:
                self.status_label.config(foreground="gray")
            
            # 更新持续时间
            duration = status.get('trade_duration_minutes', 0)
            if duration > 0:
                if duration < 60:
                    duration_text = f"{duration:.1f} 分钟"
                else:
                    hours = duration / 60
                    duration_text = f"{hours:.1f} 小时"
            else:
                duration_text = "--"
            self.duration_label.config(text=duration_text)
            
            # 更新交易信息
            trade_info = status.get('trade_info', {})
            if trade_info:
                signal_type = trade_info.get('signal_type', '--')
                amount = trade_info.get('amount', 0)
                info_text = f"{signal_type} ${amount:.2f}" if amount > 0 else signal_type
            else:
                info_text = "--"
            self.trade_info_label.config(text=info_text)
            
            # 更新统计信息
            filtered_count = status.get('filtered_signals_count', 0)
            self.filtered_count_label.config(text=str(filtered_count))
            
            history_count = status.get('state_history_count', 0)
            self.history_count_label.config(text=str(history_count))
            
            # 更新功能状态
            enabled = status.get('enabled', False)
            enabled_text = "✅ 启用" if enabled else "❌ 禁用"
            self.enabled_label.config(
                text=enabled_text,
                foreground="green" if enabled else "red"
            )
            
            # 更新按钮状态
            can_reset = current_state != 'idle'
            self.reset_button.config(state=tk.NORMAL if can_reset else tk.DISABLED)
            
        except Exception as e:
            print(f"更新GUI状态失败: {e}")
    
    def _force_reset(self):
        """强制重置交易状态"""
        if messagebox.askyesno("确认重置", "确定要强制重置交易状态吗？\n这将清除当前的交易状态信息。"):
            try:
                if self.trade_state_manager:
                    success = self.trade_state_manager.force_reset("用户手动重置")
                    if success:
                        messagebox.showinfo("重置成功", "交易状态已重置为空闲状态")
                        self._add_history_item("手动重置", "重置", "成功重置为空闲状态")
                    else:
                        messagebox.showerror("重置失败", "强制重置功能已禁用")
                else:
                    messagebox.showerror("错误", "交易状态管理器未初始化")
            except Exception as e:
                messagebox.showerror("重置异常", f"重置过程中发生异常: {e}")
    
    def _refresh_status(self):
        """刷新状态"""
        self._update_status()
        self._add_history_item("手动刷新", "刷新", "状态已刷新")
    
    def _show_details(self):
        """显示详细信息"""
        try:
            if self.trade_state_manager:
                status = self.trade_state_manager.get_status()
                filtered_signals = self.trade_state_manager.get_filtered_signals(20)
                trade_history = self.trade_state_manager.get_trade_history(10)
                
                # 创建详情窗口
                self._create_details_window(status, filtered_signals, trade_history)
            else:
                messagebox.showerror("错误", "交易状态管理器未初始化")
        except Exception as e:
            messagebox.showerror("详情异常", f"获取详情时发生异常: {e}")
    
    def _create_details_window(self, status, filtered_signals, trade_history):
        """创建详情窗口"""
        details_window = tk.Toplevel(self.parent)
        details_window.title("交易状态详情")
        details_window.geometry("800x600")
        
        # 创建笔记本控件
        notebook = ttk.Notebook(details_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 状态详情标签页
        status_frame = ttk.Frame(notebook)
        notebook.add(status_frame, text="状态详情")
        
        status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD)
        status_text.pack(fill=tk.BOTH, expand=True)
        status_text.insert(tk.END, json.dumps(status, indent=2, ensure_ascii=False))
        status_text.config(state=tk.DISABLED)
        
        # 过滤信号标签页
        filtered_frame = ttk.Frame(notebook)
        notebook.add(filtered_frame, text=f"过滤信号 ({len(filtered_signals)})")
        
        filtered_text = scrolledtext.ScrolledText(filtered_frame, wrap=tk.WORD)
        filtered_text.pack(fill=tk.BOTH, expand=True)
        for signal in filtered_signals:
            filtered_text.insert(tk.END, json.dumps(signal, indent=2, ensure_ascii=False) + "\n" + "-"*50 + "\n")
        filtered_text.config(state=tk.DISABLED)
        
        # 交易历史标签页
        history_frame = ttk.Frame(notebook)
        notebook.add(history_frame, text=f"交易历史 ({len(trade_history)})")
        
        history_text = scrolledtext.ScrolledText(history_frame, wrap=tk.WORD)
        history_text.pack(fill=tk.BOTH, expand=True)
        for trade in trade_history:
            history_text.insert(tk.END, json.dumps(trade, indent=2, ensure_ascii=False) + "\n" + "-"*50 + "\n")
        history_text.config(state=tk.DISABLED)
    
    def _clear_history(self):
        """清理历史记录"""
        if messagebox.askyesno("确认清理", "确定要清理历史记录吗？"):
            # 清理GUI历史显示
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            self._add_history_item("清理历史", "清理", "历史记录已清理")
    
    def _add_history_item(self, event_type: str, status: str, info: str):
        """添加历史记录项"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # 添加到树形视图
            self.history_tree.insert("", 0, values=(timestamp, event_type, status, info))
            
            # 限制显示数量
            items = self.history_tree.get_children()
            if len(items) > 50:
                self.history_tree.delete(items[-1])
                
        except Exception as e:
            print(f"添加历史记录失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1)
