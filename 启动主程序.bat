@echo off
REM 主程序启动脚本（清理版）

echo 🚀 启动交易预测程序...

REM 强制CPU模式
set CUDA_VISIBLE_DEVICES=-1
set TF_FORCE_GPU_ALLOW_GROWTH=false

REM CPU优化环境变量
set OMP_NUM_THREADS=16
set MKL_NUM_THREADS=16
set NUMBA_NUM_THREADS=16
set TF_ENABLE_ONEDNN_OPTS=1
set TF_NUM_INTRAOP_THREADS=16

echo ✅ CPU优化环境设置完成
echo 🖥️  AMD 16核处理器优化已启用

REM 尝试多种Python启动方式
echo 🔍 正在检测Python环境...

REM 方法1: 尝试直接使用python命令
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ 找到Python，使用直接命令启动
    python main.py
    goto :end
)

REM 方法2: 尝试使用py命令（Windows Python Launcher）
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ 找到Python Launcher，使用py命令启动
    py main.py
    goto :end
)

REM 方法3: 尝试conda环境
where conda >nul 2>&1
if not errorlevel 1 (
    echo 🔍 找到conda，尝试激活trading环境...
    call conda activate trading
    if not errorlevel 1 (
        echo ✅ conda环境 'trading' 已激活
        python main.py
        goto :end
    )
)

REM 如果所有方法都失败
echo ❌ 无法找到可用的Python环境
echo 💡 请确保Python已正确安装并添加到PATH中
echo 💡 或者安装Anaconda/Miniconda并创建名为 'trading' 的环境
pause
exit /b 1

:end
