#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元模型输入丰富化模块
将高阶特征、交互特征、衍生品背离特征等作为上下文特征输入给元模型
"""

import logging
import numpy as np
import pandas as pd
import traceback
from typing import Dict, List, Tuple, Optional, Any, Union

logger = logging.getLogger(__name__)

class MetaModelInputEnricher:
    """元模型输入增强器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化元模型输入增强器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.enrichment_stats = {}
        
        # 默认配置
        self.default_config = {
            'enable_enrichment': True,
            'include_higher_order_features': True,
            'include_interaction_features': True,
            'include_smart_money_features': True,
            'include_volatility_features': True,
            'include_momentum_features': True,
            'include_market_state_features': True,
            'feature_selection_threshold': 0.001,  # 特征重要性阈值
            'max_features_per_category': 10,       # 每类特征的最大数量
            'normalize_features': True,             # 是否标准化特征
        }
        
        # 合并配置
        self.effective_config = {**self.default_config, **self.config}
        
        logger.info(f"[MetaModelInputEnricher] 初始化完成，启用增强: {self.effective_config['enable_enrichment']}")

    def extract_higher_order_context_features(self, df: pd.DataFrame, target_name: str) -> Dict[str, float]:
        """
        提取高阶特征作为上下文特征
        
        Args:
            df: 包含高阶特征的DataFrame
            target_name: 目标名称
            
        Returns:
            高阶上下文特征字典
        """
        context_features = {}
        
        if not self.effective_config['include_higher_order_features']:
            return context_features
        
        try:
            # 获取最新行的数据
            latest_row = df.iloc[-1] if len(df) > 0 else pd.Series()
            
            # 🚀 1. RSI高阶特征
            rsi_features = {
                'rsi_velocity': 'RSI_velocity',
                'rsi_acceleration': 'RSI_acceleration', 
                'rsi_momentum_strength': 'RSI_momentum_strength',
                'rsi_adaptive_velocity': 'RSI_adaptive_velocity'
            }
            
            for context_name, col_name in rsi_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 2. MACD高阶特征
            macd_features = {
                'macd_velocity': 'MACD_velocity',
                'macd_acceleration': 'MACD_acceleration',
                'macd_hist_velocity': 'MACD_hist_velocity',
                'macd_hist_acceleration': 'MACD_hist_acceleration'
            }
            
            for context_name, col_name in macd_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 3. ADX高阶特征
            adx_features = {
                'adx_velocity': 'ADX_velocity',
                'adx_acceleration': 'ADX_acceleration',
                'adx_momentum_strength': 'ADX_momentum_strength'
            }
            
            for context_name, col_name in adx_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 提取高阶特征: {len(context_features)} 个")
            
        except Exception as e:
            logger.error(f"[MetaModelInputEnricher] ({target_name}) 高阶特征提取失败: {e}")
            logger.debug(traceback.format_exc())
        
        return context_features

    def extract_interaction_context_features(self, df: pd.DataFrame, target_name: str) -> Dict[str, float]:
        """
        提取交互特征作为上下文特征
        
        Args:
            df: 包含交互特征的DataFrame
            target_name: 目标名称
            
        Returns:
            交互上下文特征字典
        """
        context_features = {}
        
        if not self.effective_config['include_interaction_features']:
            return context_features
        
        try:
            # 获取最新行的数据
            latest_row = df.iloc[-1] if len(df) > 0 else pd.Series()
            
            # 🚀 1. 波动率-趋势交互特征
            volatility_trend_features = {
                'volatility_trend_strength': 'volatility_trend_strength',
                'volatility_trend_strength_norm': 'volatility_trend_strength_norm'
            }
            
            for context_name, col_name in volatility_trend_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 2. 量价交互特征
            price_volume_cols = [col for col in latest_row.index if 'price_volume_interaction' in col]
            for i, col in enumerate(price_volume_cols[:3]):  # 限制数量
                value = latest_row[col]
                context_features[f'price_volume_interaction_{i+1}'] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 3. 动量共振特征
            momentum_features = {
                'rsi_macd_momentum_resonance': 'rsi_macd_momentum_resonance',
                'bb_atr_compression_expansion': 'bb_atr_compression_expansion',
                'volume_volatility_efficiency': 'volume_volatility_efficiency'
            }
            
            for context_name, col_name in momentum_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 提取交互特征: {len(context_features)} 个")
            
        except Exception as e:
            logger.error(f"[MetaModelInputEnricher] ({target_name}) 交互特征提取失败: {e}")
            logger.debug(traceback.format_exc())
        
        return context_features

    def extract_smart_money_context_features(self, df: pd.DataFrame, target_name: str) -> Dict[str, float]:
        """
        提取聪明钱特征作为上下文特征
        
        Args:
            df: 包含聪明钱特征的DataFrame
            target_name: 目标名称
            
        Returns:
            聪明钱上下文特征字典
        """
        context_features = {}
        
        if not self.effective_config['include_smart_money_features']:
            return context_features
        
        try:
            # 获取最新行的数据
            latest_row = df.iloc[-1] if len(df) > 0 else pd.Series()
            
            # 🚀 1. 核心聪明钱背离特征
            smart_money_features = {
                'smart_money_divergence': 'smart_money_divergence',
                'smart_money_divergence_strength': 'smart_money_divergence_strength',
                'smart_money_divergence_trend': 'smart_money_divergence_trend',
                'smart_money_divergence_persistence': 'smart_money_divergence_persistence'
            }
            
            for context_name, col_name in smart_money_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 2. 资金费率特征
            funding_features = {
                'funding_price_divergence': 'funding_price_divergence',
                'funding_rate_extremity': 'funding_rate_extremity'
            }
            
            for context_name, col_name in funding_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 3. 多空比背离特征
            longshort_features = {
                'longshort_price_divergence_ratio': 'longshort_price_divergence_ratio',
                'longshort_price_divergence_signal': 'longshort_price_divergence_signal'
            }
            
            for context_name, col_name in longshort_features.items():
                if col_name in latest_row.index:
                    value = latest_row[col_name]
                    context_features[context_name] = float(value) if not pd.isna(value) else 0.0
            
            # 🚀 4. 综合情绪指数
            if 'smart_money_sentiment_index' in latest_row.index:
                value = latest_row['smart_money_sentiment_index']
                context_features['smart_money_sentiment_index'] = float(value) if not pd.isna(value) else 0.0
            
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 提取聪明钱特征: {len(context_features)} 个")
            
        except Exception as e:
            logger.error(f"[MetaModelInputEnricher] ({target_name}) 聪明钱特征提取失败: {e}")
            logger.debug(traceback.format_exc())
        
        return context_features

    def extract_market_state_context_features(self, df: pd.DataFrame, target_name: str) -> Dict[str, float]:
        """
        提取市场状态特征作为上下文特征
        
        Args:
            df: 包含市场状态数据的DataFrame
            target_name: 目标名称
            
        Returns:
            市场状态上下文特征字典
        """
        context_features = {}
        
        if not self.effective_config['include_market_state_features']:
            return context_features
        
        try:
            # 获取最新行的数据
            latest_row = df.iloc[-1] if len(df) > 0 else pd.Series()
            
            # 🚀 1. 基础市场状态指标
            if 'ATRr_14' in latest_row.index and 'ADX' in latest_row.index:
                atr_val = latest_row['ATRr_14']
                adx_val = latest_row['ADX']
                
                # 🚀 修复数据泄露：使用历史数据计算市场状态基准
                # 只使用当前时间点之前的数据计算基准值
                current_idx = latest_row.name
                historical_data = df.loc[:current_idx].iloc[:-1]  # 排除当前行

                if len(historical_data) > 0:
                    atr_median = historical_data['ATRr_14'].median() if 'ATRr_14' in historical_data.columns else atr_val
                    adx_median = historical_data['ADX'].median() if 'ADX' in historical_data.columns else adx_val
                else:
                    # 如果没有历史数据，使用当前值作为基准
                    atr_median = atr_val
                    adx_median = adx_val
                
                # 市场状态编码
                if atr_val > atr_median and adx_val > adx_median:
                    market_state = 1  # trending
                elif atr_val > atr_median and adx_val <= adx_median:
                    market_state = 2  # volatile
                elif atr_val <= atr_median and adx_val > adx_median:
                    market_state = 3  # ranging
                else:
                    market_state = 0  # calm
                
                context_features['market_state'] = float(market_state)
                context_features['atr_relative_position'] = float(atr_val / atr_median) if atr_median > 0 else 1.0
                context_features['adx_relative_position'] = float(adx_val / adx_median) if adx_median > 0 else 1.0
            
            # 🚀 2. 波动率状态
            if 'ATRr_14' in latest_row.index:
                atr_val = latest_row['ATRr_14']
                atr_rolling_mean = df['ATRr_14'].rolling(20).mean().iloc[-1] if len(df) >= 20 else atr_val
                atr_rolling_std = df['ATRr_14'].rolling(20).std().iloc[-1] if len(df) >= 20 else 0
                
                if atr_rolling_std > 0:
                    volatility_zscore = (atr_val - atr_rolling_mean) / atr_rolling_std
                    context_features['volatility_zscore'] = float(volatility_zscore)
                else:
                    context_features['volatility_zscore'] = 0.0
            
            # 🚀 3. 趋势强度
            if 'ADX' in latest_row.index:
                adx_val = latest_row['ADX']
                if adx_val >= 50:
                    trend_strength = 3  # 强趋势
                elif adx_val >= 25:
                    trend_strength = 2  # 中等趋势
                elif adx_val >= 15:
                    trend_strength = 1  # 弱趋势
                else:
                    trend_strength = 0  # 无趋势
                
                context_features['trend_strength'] = float(trend_strength)
            
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 提取市场状态特征: {len(context_features)} 个")
            
        except Exception as e:
            logger.error(f"[MetaModelInputEnricher] ({target_name}) 市场状态特征提取失败: {e}")
            logger.debug(traceback.format_exc())
        
        return context_features

    def extract_momentum_context_features(self, df: pd.DataFrame, target_name: str) -> Dict[str, float]:
        """
        提取动量特征作为上下文特征
        
        Args:
            df: 包含动量数据的DataFrame
            target_name: 目标名称
            
        Returns:
            动量上下文特征字典
        """
        context_features = {}
        
        if not self.effective_config['include_momentum_features']:
            return context_features
        
        try:
            # 获取最新行的数据
            latest_row = df.iloc[-1] if len(df) > 0 else pd.Series()
            
            # 🚀 1. RSI动量特征
            if 'RSI_14' in latest_row.index:
                rsi_val = latest_row['RSI_14']
                rsi_momentum = abs(rsi_val - 50) / 50  # RSI偏离中性值的程度
                context_features['rsi_momentum'] = float(rsi_momentum)
                
                # RSI极值状态
                if rsi_val >= 80:
                    rsi_state = 2  # 超买
                elif rsi_val >= 70:
                    rsi_state = 1  # 买入
                elif rsi_val <= 20:
                    rsi_state = -2  # 超卖
                elif rsi_val <= 30:
                    rsi_state = -1  # 卖出
                else:
                    rsi_state = 0  # 中性
                
                context_features['rsi_state'] = float(rsi_state)
            
            # 🚀 2. MACD动量特征
            if 'MACD' in latest_row.index and 'MACD_signal' in latest_row.index:
                macd_val = latest_row['MACD']
                macd_signal = latest_row['MACD_signal']
                macd_divergence = macd_val - macd_signal
                
                context_features['macd_divergence'] = float(macd_divergence)
                context_features['macd_signal_strength'] = float(abs(macd_divergence))
            
            # 🚀 3. 价格动量特征
            price_change_cols = [col for col in latest_row.index if col.startswith('price_change_')]
            for col in price_change_cols[:3]:  # 限制数量
                value = latest_row[col]
                context_features[f'momentum_{col}'] = float(value) if not pd.isna(value) else 0.0
            
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 提取动量特征: {len(context_features)} 个")
            
        except Exception as e:
            logger.error(f"[MetaModelInputEnricher] ({target_name}) 动量特征提取失败: {e}")
            logger.debug(traceback.format_exc())
        
        return context_features

    def enrich_context_features(self, df: pd.DataFrame, 
                              existing_context: Dict[str, float], 
                              target_name: str) -> Dict[str, float]:
        """
        丰富上下文特征
        
        Args:
            df: 包含所有特征的DataFrame
            existing_context: 现有上下文特征
            target_name: 目标名称
            
        Returns:
            丰富后的上下文特征字典
        """
        if not self.effective_config['enable_enrichment']:
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 特征丰富化已禁用")
            return existing_context
        
        logger.info(f"[MetaModelInputEnricher] ({target_name}) 开始丰富上下文特征...")
        
        enriched_context = existing_context.copy()
        
        try:
            # 1. 提取高阶特征
            higher_order_features = self.extract_higher_order_context_features(df, target_name)
            enriched_context.update(higher_order_features)
            
            # 2. 提取交互特征
            interaction_features = self.extract_interaction_context_features(df, target_name)
            enriched_context.update(interaction_features)
            
            # 3. 提取聪明钱特征
            smart_money_features = self.extract_smart_money_context_features(df, target_name)
            enriched_context.update(smart_money_features)
            
            # 4. 提取市场状态特征
            market_state_features = self.extract_market_state_context_features(df, target_name)
            enriched_context.update(market_state_features)
            
            # 5. 提取动量特征
            momentum_features = self.extract_momentum_context_features(df, target_name)
            enriched_context.update(momentum_features)
            
            # 统计信息
            original_count = len(existing_context)
            enriched_count = len(enriched_context)
            new_features_count = enriched_count - original_count
            
            self.enrichment_stats[target_name] = {
                'original_features': original_count,
                'enriched_features': enriched_count,
                'new_features': new_features_count,
                'categories': {
                    'higher_order': len(higher_order_features),
                    'interaction': len(interaction_features),
                    'smart_money': len(smart_money_features),
                    'market_state': len(market_state_features),
                    'momentum': len(momentum_features)
                }
            }
            
            logger.info(f"[MetaModelInputEnricher] ({target_name}) 特征丰富化完成，"
                       f"原始: {original_count}, 丰富后: {enriched_count} (+{new_features_count})")
            
            return enriched_context
            
        except Exception as e:
            logger.error(f"[MetaModelInputEnricher] ({target_name}) 特征丰富化失败: {e}")
            logger.debug(traceback.format_exc())
            return existing_context

    def get_enrichment_stats(self) -> Dict[str, Any]:
        """获取丰富化统计信息"""
        return self.enrichment_stats.copy()


# 便捷函数
def enrich_meta_model_context_features(df: pd.DataFrame,
                                     existing_context: Dict[str, float],
                                     target_config: Dict[str, Any],
                                     target_name: str) -> Dict[str, float]:
    """
    丰富元模型上下文特征的便捷函数
    
    Args:
        df: 包含所有特征的DataFrame
        existing_context: 现有上下文特征
        target_config: 目标配置
        target_name: 目标名称
        
    Returns:
        丰富后的上下文特征字典
    """
    # 提取元模型输入丰富化配置
    enrichment_config = target_config.get('meta_model_input_enrichment', {})
    
    # 创建丰富化器
    enricher = MetaModelInputEnricher(enrichment_config)
    
    # 丰富特征
    return enricher.enrich_context_features(df, existing_context, target_name)
