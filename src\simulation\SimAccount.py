# SimAccount.py

class Account:
    def __init__(self,
                 initial_balance=10000.0,
                 initial_total_profit_loss=0.0,
                 initial_total_trades_count=0,
                 initial_total_wins_count=0,
                 # 新增：分别统计UP和DOWN的初始值
                 initial_up_trades_count=0,
                 initial_up_wins_count=0,
                 initial_down_trades_count=0,
                 initial_down_wins_count=0):
        
        if initial_balance < 0:
            raise ValueError("初始余额不能为负数。")
        # ... (其他参数的初始校验可以保持或添加)

        self.initial_balance = float(initial_balance) 
        self.current_balance = float(initial_balance) + float(initial_total_profit_loss)

        # 当日统计 (总是从0开始新的一天)
        self.daily_profit_loss = 0.0
        self.daily_trades_count = 0
        self.daily_wins_count = 0
        self.daily_up_trades_count = 0
        self.daily_up_wins_count = 0
        self.daily_down_trades_count = 0
        self.daily_down_wins_count = 0

        # 总计统计
        self.total_profit_loss = float(initial_total_profit_loss)
        self.total_trades_count = int(initial_total_trades_count)
        self.total_wins_count = int(initial_total_wins_count)
        self.total_up_trades_count = int(initial_up_trades_count)
        self.total_up_wins_count = int(initial_up_wins_count)
        self.total_down_trades_count = int(initial_down_trades_count)
        self.total_down_wins_count = int(initial_down_wins_count)

        print(f"模拟账户已创建:")
        # ... (你现有的初始化打印信息) ...
        # 可以选择在这里也打印UP/DOWN的初始统计


    def deposit(self, amount):
        if amount <= 0:
            print("存款金额必须为正。")
            return False
        self.current_balance += float(amount)
        self.initial_balance += float(amount) 
        print(f"已存入 {amount:.2f} USDT. 当前余额: {self.current_balance:.2f} USDT, "
              f"基准初始余额更新为: {self.initial_balance:.2f} USDT")
        return True

    def reserve_funds(self, amount):
        """
        预留资金（开仓时调用）

        Args:
            amount: 需要预留的金额

        Returns:
            bool: 是否成功预留资金
        """
        if amount <= 0:
            print(f"❌ 预留资金失败: 金额必须为正数 ({amount:.2f})")
            return False

        if self.current_balance < amount:
            print(f"❌ 预留资金失败: 余额不足 (需要 {amount:.2f}, 可用 {self.current_balance:.2f})")
            return False

        self.current_balance -= amount
        print(f"💰 资金预留成功: 预留 {amount:.2f} USDT, 剩余余额: {self.current_balance:.2f} USDT")
        return True

    def release_funds(self, amount):
        """
        释放预留资金（取消交易时调用）

        Args:
            amount: 需要释放的金额
        """
        if amount <= 0:
            print(f"⚠️ 释放资金警告: 金额必须为正数 ({amount:.2f})")
            return

        self.current_balance += amount
        print(f"💰 资金释放: 释放 {amount:.2f} USDT, 当前余额: {self.current_balance:.2f} USDT")

    def record_trade_result(self, direction, amount_staked, profit_or_loss): # 添加 direction 参数
        """
        记录交易结果（结算时调用）
        注意：开仓时的资金已通过reserve_funds预留，这里处理结算返还

        Args:
            direction: 交易方向 (UP/DOWN)
            amount_staked: 下注金额
            profit_or_loss: 结算返还金额（盈利时=本金+盈利，亏损时=0）
        """
        is_win = profit_or_loss > 0  # 返还金额大于0才算盈利
        trade_direction = str(direction).upper() # 确保是大写

        # 🔧 修复：添加结算返还金额
        self.current_balance += profit_or_loss

        # 计算实际盈亏（用于统计）
        # 盈利时：actual_pnl = (本金 + 盈利) - 本金 = 盈利
        # 亏损时：actual_pnl = 0 - 本金 = -本金
        actual_pnl = profit_or_loss - amount_staked

        # 更新当日统计（使用实际盈亏）
        self.daily_profit_loss += actual_pnl
        self.daily_trades_count += 1
        if is_win:
            self.daily_wins_count += 1

        if trade_direction == "UP":
            self.daily_up_trades_count += 1
            if is_win:
                self.daily_up_wins_count += 1
        elif trade_direction == "DOWN":
            self.daily_down_trades_count += 1
            if is_win:
                self.daily_down_wins_count += 1

        # 更新总计统计（使用实际盈亏）
        self.total_profit_loss += actual_pnl
        self.total_trades_count += 1
        if is_win:
            self.total_wins_count += 1

        if trade_direction == "UP":
            self.total_up_trades_count += 1
            if is_win:
                self.total_up_wins_count += 1
        elif trade_direction == "DOWN":
            self.total_down_trades_count += 1
            if is_win:
                self.total_down_wins_count += 1

        # 🔧 修复：更清晰的交易记录日志
        if is_win:
            print(f"💰 交易记录 ({trade_direction}): 下注 {amount_staked:.2f}, 返还 {profit_or_loss:.2f}, "
                  f"净盈利 {actual_pnl:.2f}. 当前余额: {self.current_balance:.2f} USDT")
        else:
            print(f"💸 交易记录 ({trade_direction}): 下注 {amount_staked:.2f}, 返还 {profit_or_loss:.2f}, "
                  f"净亏损 {actual_pnl:.2f}. 当前余额: {self.current_balance:.2f} USDT")

    def get_stats(self):
        daily_win_rate = (self.daily_wins_count / self.daily_trades_count) * 100 if self.daily_trades_count > 0 else 0
        total_win_rate = (self.total_wins_count / self.total_trades_count) * 100 if self.total_trades_count > 0 else 0
        
        daily_up_win_rate = (self.daily_up_wins_count / self.daily_up_trades_count) * 100 if self.daily_up_trades_count > 0 else 0
        daily_down_win_rate = (self.daily_down_wins_count / self.daily_down_trades_count) * 100 if self.daily_down_trades_count > 0 else 0
        
        total_up_win_rate = (self.total_up_wins_count / self.total_up_trades_count) * 100 if self.total_up_trades_count > 0 else 0
        total_down_win_rate = (self.total_down_wins_count / self.total_down_trades_count) * 100 if self.total_down_trades_count > 0 else 0

        return {
            "current_balance": self.current_balance,
            "initial_balance_benchmark": self.initial_balance,
            "daily_profit_loss": self.daily_profit_loss,
            "daily_trades_count": self.daily_trades_count,
            "daily_wins_count": self.daily_wins_count,
            "daily_win_rate": daily_win_rate,
            
            "daily_up_trades_count": self.daily_up_trades_count,
            "daily_up_wins_count": self.daily_up_wins_count,
            "daily_up_win_rate": daily_up_win_rate,
            "daily_down_trades_count": self.daily_down_trades_count,
            "daily_down_wins_count": self.daily_down_wins_count,
            "daily_down_win_rate": daily_down_win_rate,
            
            "total_profit_loss_overall": self.total_profit_loss,
            "total_trades_count_overall": self.total_trades_count,
            "total_wins_count_overall": self.total_wins_count,
            "total_win_rate_overall": total_win_rate,

            "total_up_trades_count_overall": self.total_up_trades_count,
            "total_up_wins_count_overall": self.total_up_wins_count,
            "total_up_win_rate_overall": total_up_win_rate,
            "total_down_trades_count_overall": self.total_down_trades_count,
            "total_down_wins_count_overall": self.total_down_wins_count,
            "total_down_win_rate_overall": total_down_win_rate,
        }

    def reset_daily_stats(self):
        self.daily_profit_loss = 0.0
        self.daily_trades_count = 0
        self.daily_wins_count = 0
        self.daily_up_trades_count = 0
        self.daily_up_wins_count = 0
        self.daily_down_trades_count = 0
        self.daily_down_wins_count = 0
        print("当日统计数据已重置。")

    def __str__(self): # 可以选择更新 __str__ 以包含新统计信息
        stats = self.get_stats()
        return (
            f"模拟账户状态:\n"
            f"  当前可用余额: {stats['current_balance']:.2f} USDT\n"
            f"  基准初始余额: {stats['initial_balance_benchmark']:.2f} USDT\n"
            f"  当日盈亏: {stats['daily_profit_loss']:.2f} USDT ({stats['daily_wins_count']}/{stats['daily_trades_count']}, 胜率 {stats['daily_win_rate']:.2f}%)\n"
            f"    当日做多: {stats['daily_up_wins_count']}/{stats['daily_up_trades_count']} (胜率 {stats['daily_up_win_rate']:.2f}%)\n"
            f"    当日做空: {stats['daily_down_wins_count']}/{stats['daily_down_trades_count']} (胜率 {stats['daily_down_win_rate']:.2f}%)\n"
            f"  总计盈亏(模拟期): {stats['total_profit_loss_overall']:.2f} USDT "
            f"({stats['total_wins_count_overall']}/{stats['total_trades_count_overall']}, 胜率 {stats['total_win_rate_overall']:.2f}%)\n"
            f"    总计做多: {stats['total_up_wins_count_overall']}/{stats['total_up_trades_count_overall']} (胜率 {stats['total_up_win_rate_overall']:.2f}%)\n"
            f"    总计做空: {stats['total_down_wins_count_overall']}/{stats['total_down_trades_count_overall']} (胜率 {stats['total_down_win_rate_overall']:.2f}%)"
        )
