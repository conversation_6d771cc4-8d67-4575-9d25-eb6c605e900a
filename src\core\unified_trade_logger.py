#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级统一交易日志系统 V2.0 (修复版)
核心特性：一笔交易一行记录，异步写入，完整上下文捕获
"""

import os
import json
import time
import queue
import threading
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import uuid


class UnifiedTradeLogger:
    """
    企业级统一交易日志系统
    
    核心设计原则：
    1. 一笔交易一行记录（开仓+平仓合并）
    2. 异步队列写入，不阻塞主线程
    3. 完整上下文数据捕获
    4. 线程安全操作
    """
    
    # 🎯 简化的CSV列名（只保留关键信息）
    CSV_COLUMNS = [
        # 基础交易信息
        'trade_id', 'entry_timestamp', 'exit_timestamp', 'target_name', 'symbol',
        'direction', 'entry_price', 'exit_price', 'amount', 'payout_ratio',
        'result', 'profit_loss', 'exit_reason',

        # 决策概率信息（对应用户示例中的概率分布）
        'entry_signal_probability', 'entry_neutral_probability', 'entry_opposite_probability',

        # 🎯 混合智能决策过程信息
        'direction_advantage', 'up_advantage', 'down_advantage', 'meta_decision_reason',
        'threshold_check_result', 'signal_filtered_reason',

        # 🎯 简化的模型贡献信息（JSON格式存储）
        'individual_model_probabilities', 'base_model_predictions',

        # 🎯 全局市场状态信息
        'entry_market_regime', 'entry_atr_percent', 'entry_adx_value',

        # 🎯 V28.0 非对称融合决策分析参数
        'v28_meta_up_prob', 'v28_meta_down_prob',  # 元模型输出概率
        'v28_probability_advantage_score', 'v28_probability_advantage_weight',  # 概率优势分及权重
        'v28_base_consensus_score', 'v28_base_consensus_weight',  # 基础共识分及权重
        'v28_macro_trend_score', 'v28_macro_trend_weight',  # 宏观顺势分及权重
        'v28_comprehensive_confidence_score', 'v28_confidence_threshold',  # 综合信心分数及阈值
        'v28_base_model_up_prob', 'v28_base_model_down_prob',  # 基础模型概率
        'v28_base_model_up_signal', 'v28_base_model_down_signal',  # 基础模型信号
        'v28_model_divergence', 'v28_model_consistency',  # 模型分歧度及一致性
        'v28_fusion_result', 'v28_final_decision',  # 融合结果及最终决策

        # 🎯 简化的决策上下文（重要特征摘要，不是全部特征）
        'decision_context_summary'
    ]
    
    def __init__(self, base_log_dir: str = "trading_logs", auto_start: bool = True):
        """
        初始化统一交易日志系统
        
        Args:
            base_log_dir: 日志基础目录
            auto_start: 是否自动启动后台写入线程
        """
        self.base_log_dir = base_log_dir
        self.logger = self._setup_logger()
        
        # 核心数据结构
        self.pending_trades: Dict[str, Dict[str, Any]] = {}  # 暂存开仓数据
        self.write_queue = queue.Queue(maxsize=1000)  # 异步写入队列
        
        # 线程安全锁
        self.pending_lock = threading.Lock()
        self.stats_lock = threading.Lock()
        
        # 统计信息
        self.total_entries = 0
        self.total_exits = 0
        self.total_writes = 0
        self.failed_writes = 0
        
        # 后台写入线程
        self.writer_thread = None
        self.stop_event = threading.Event()
        
        if auto_start:
            self.start_writer_thread()
    
    def _setup_logger(self) -> logging.Logger:
        """设置内部日志记录器"""
        logger = logging.getLogger(f"UnifiedTradeLogger_{id(self)}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def start_writer_thread(self):
        """启动后台异步写入线程"""
        if self.writer_thread is None or not self.writer_thread.is_alive():
            self.stop_event.clear()
            self.writer_thread = threading.Thread(
                target=self._background_writer,
                name="TradeLogger-Writer",
                daemon=True
            )
            self.writer_thread.start()
            self.logger.info("后台写入线程已启动")
    
    def stop_writer_thread(self, timeout: float = 5.0):
        """停止后台写入线程"""
        if self.writer_thread and self.writer_thread.is_alive():
            self.stop_event.set()
            
            # 发送停止信号到队列
            try:
                self.write_queue.put({"__STOP__": True}, timeout=1.0)
            except queue.Full:
                pass
            
            self.writer_thread.join(timeout=timeout)
            
            if self.writer_thread.is_alive():
                self.logger.warning("后台写入线程未能在指定时间内停止")
            else:
                self.logger.info("后台写入线程已停止")
    
    def _get_current_log_file(self) -> str:
        """获取当前日志文件路径"""
        now = datetime.now()
        year_month_dir = os.path.join(
            self.base_log_dir, 
            str(now.year), 
            f"{now.month:02d}"
        )
        os.makedirs(year_month_dir, exist_ok=True)
        
        filename = f"trades_{now.strftime('%Y-%m-%d')}.csv"
        return os.path.join(year_month_dir, filename)
    
    def _ensure_csv_header(self, file_path: str):
        """确保CSV文件有正确的头部（仅在文件不存在时写入）"""
        if not os.path.exists(file_path):
            try:
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(','.join(self.CSV_COLUMNS) + '\n')
                self.logger.debug(f"CSV头部已写入: {file_path}")
            except Exception as e:
                self.logger.error(f"写入CSV头部失败: {e}")
                raise
    
    def _background_writer(self):
        """后台异步写入线程主函数"""
        self.logger.info("后台写入线程开始运行")
        
        while not self.stop_event.is_set():
            try:
                # 从队列获取数据（带超时）
                trade_data = self.write_queue.get(timeout=1.0)
                
                # 检查停止信号
                if isinstance(trade_data, dict) and trade_data.get("__STOP__"):
                    break
                
                # 写入CSV文件
                self._write_trade_to_csv(trade_data)
                
                with self.stats_lock:
                    self.total_writes += 1
                
                self.write_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"后台写入出错: {e}")
                with self.stats_lock:
                    self.failed_writes += 1
        
        self.logger.info("后台写入线程结束")

    def _create_decision_context_summary(self, context_data: Dict[str, Any]) -> str:
        """
        🎯 创建决策上下文摘要，将复杂的元模型特征合并为关键信息

        Args:
            context_data: 完整的上下文数据

        Returns:
            JSON格式的决策上下文摘要字符串
        """
        try:
            summary = {}

            # 1. AI初筛阈值信息
            ai_screening = {}
            if 'threshold_check_result' in context_data:
                threshold_result = context_data.get('threshold_check_result', {})
                if isinstance(threshold_result, str):
                    try:
                        threshold_result = json.loads(threshold_result)
                    except:
                        threshold_result = {}

                ai_screening = {
                    'up_threshold': threshold_result.get('up_threshold', 0.0),
                    'down_threshold': threshold_result.get('down_threshold', 0.0),
                    'up_gap': threshold_result.get('up_gap', 0.0),
                    'down_gap': threshold_result.get('down_gap', 0.0),
                    'up_passed': threshold_result.get('up_passed', False),
                    'down_passed': threshold_result.get('down_passed', False)
                }
            summary['ai_screening'] = ai_screening

            # 2. 人类智能复核信息
            human_review = {
                'direction_advantage_enabled': context_data.get('direction_advantage', 0.0) != 0.0,
                'up_advantage': context_data.get('up_advantage', 0.0),
                'down_advantage': context_data.get('down_advantage', 0.0),
                'advantage_threshold': 0.07  # 7%阈值
            }
            summary['human_review'] = human_review

            # 3. 全局市场状态摘要
            market_state = {
                'adx_trend_strength': context_data.get('entry_adx_value', 0.0),
                'atr_volatility': context_data.get('entry_atr_percent', 0.0),
                'market_regime': context_data.get('entry_market_regime', 'normal')
            }

            # 从复杂特征中提取关键市场指标
            for key, value in context_data.items():
                if 'global_trend_strength' in key:
                    market_state['trend_strength'] = value
                elif 'global_volatility_level' in key:
                    market_state['volatility_level'] = value
                elif 'global_ema_diff_pct' in key:
                    market_state['ema_trend_diff'] = value

            summary['market_state'] = market_state

            # 4. 基础模型贡献摘要（从individual_model_probabilities提取）
            model_contributions = {}
            individual_probs = context_data.get('individual_model_probabilities')
            if individual_probs:
                if isinstance(individual_probs, str):
                    try:
                        individual_probs = json.loads(individual_probs)
                    except:
                        individual_probs = {}

                # 提取关键模型概率
                for model_type in ['LSTM', 'UP', 'DOWN']:
                    up_key = f'{model_type}_model_up_prob'
                    down_key = f'{model_type}_model_down_prob'
                    if up_key in individual_probs and down_key in individual_probs:
                        up_prob = individual_probs[up_key]
                        down_prob = individual_probs[down_key]
                        signal = 'UP' if up_prob > down_prob else 'DOWN'
                        model_contributions[f'BTC_15m_{model_type}'] = {
                            'up_prob': f"{up_prob:.2%}",
                            'down_prob': f"{down_prob:.2%}",
                            'signal': signal
                        }

            summary['model_contributions'] = model_contributions

            # 🚀 新增：反向验证机制信息摘要
            reverse_validation = {}
            reverse_validation_info = context_data.get('reverse_validation_info')
            if reverse_validation_info:
                if isinstance(reverse_validation_info, str):
                    try:
                        reverse_validation_info = json.loads(reverse_validation_info)
                    except:
                        reverse_validation_info = {}

                # 提取关键的反向验证信息
                reverse_validation = {
                    'veto_applied': reverse_validation_info.get('up_model_veto_applied', False) or reverse_validation_info.get('down_model_veto_applied', False),
                    'veto_type': 'UP_model' if reverse_validation_info.get('up_model_veto_applied', False) else ('DOWN_model' if reverse_validation_info.get('down_model_veto_applied', False) else None),
                    'model_divergence': reverse_validation_info.get('model_divergence_precise', 0.0),
                    'market_certainty': reverse_validation_info.get('market_certainty', 0.0),
                    'dual_kill_risk': reverse_validation_info.get('dual_kill_risk', False)
                }
            summary['reverse_validation'] = reverse_validation

            # 🚀 新增：分层数据策略信息摘要
            layered_data_strategy = {}
            layered_data_info = context_data.get('layered_data_strategy_info')
            if layered_data_info:
                if isinstance(layered_data_info, str):
                    try:
                        layered_data_info = json.loads(layered_data_info)
                    except:
                        layered_data_info = {}

                # 提取关键的分层数据信息
                macro_features = layered_data_info.get('macro_features', {})
                lt_features = layered_data_info.get('long_term_normalized_features', {})

                layered_data_strategy = {
                    'macro_features_count': len(macro_features),
                    'lt_normalized_features_count': len(lt_features),
                    'price_vs_long_term_ema': macro_features.get('price_vs_long_term_ema', 0.0),
                    'macro_trend_direction': macro_features.get('macro_trend_direction', 0),
                    'macro_trend_strength': macro_features.get('macro_trend_strength', 0)
                }
            summary['layered_data_strategy'] = layered_data_strategy

            return json.dumps(summary, ensure_ascii=False)

        except Exception as e:
            self.logger.warning(f"创建决策上下文摘要失败: {e}")
            return json.dumps({'error': str(e)}, ensure_ascii=False)

    def _write_trade_to_csv(self, trade_data: Dict[str, Any]):
        """将完整的交易记录写入CSV文件"""
        try:
            file_path = self._get_current_log_file()

            # 确保CSV头部存在
            self._ensure_csv_header(file_path)

            # 准备CSV行数据
            row_values = []
            for column in self.CSV_COLUMNS:
                value = trade_data.get(column, '')

                # 处理特殊值
                if value is None or value == '':
                    row_values.append('')
                elif isinstance(value, (dict, list)):
                    # JSON数据需要用双引号包围并转义内部双引号
                    json_str = json.dumps(value, ensure_ascii=False)
                    escaped_json = json_str.replace('"', '""')
                    row_values.append(f'"{escaped_json}"')
                elif isinstance(value, str):
                    # 字符串如果包含逗号、双引号或换行符需要转义
                    if ',' in value or '"' in value or '\n' in value:
                        escaped_str = value.replace('"', '""')
                        row_values.append(f'"{escaped_str}"')
                    else:
                        row_values.append(value)
                else:
                    # 数字等其他类型直接转换为字符串
                    row_values.append(str(value))

            # 写入文件
            with open(file_path, 'a', encoding='utf-8', newline='') as f:
                f.write(','.join(row_values) + '\n')

            self.logger.debug(f"交易记录已写入: {trade_data.get('trade_id', 'Unknown')}")

        except Exception as e:
            self.logger.error(f"写入CSV失败: {e}")
            raise
    
    def record_trade_entry(self, target_name: str, symbol: str, direction: str,
                          entry_price: float, amount: float,
                          payout_ratio: float = 0.85,
                          trade_id: Optional[str] = None,
                          context_data: Optional[Dict[str, Any]] = None) -> str:
        """
        记录交易开仓信息（暂存到内存，不立即写入文件）
        
        Args:
            target_name: 目标策略名称
            symbol: 交易对符号
            direction: 交易方向 (LONG/SHORT)
            entry_price: 开仓价格
            amount: 交易金额
            payout_ratio: 盈利比例
            trade_id: 交易ID（可选，自动生成）
            context_data: 上下文数据（可选）
        
        Returns:
            交易ID
        """
        # 参数验证 - 支持多种交易方向格式，包括BLOCKED用于否决记录
        direction_upper = direction.upper()
        valid_directions = ['LONG', 'SHORT', 'UP', 'DOWN', 'BUY', 'SELL', 'BLOCKED']
        if direction_upper not in valid_directions:
            raise ValueError(f"无效的交易方向: {direction}，支持的方向: {valid_directions}")

        # 🔧 修复：统一交易方向格式，将UP/DOWN转换为LONG/SHORT
        if direction_upper == 'UP':
            direction = 'LONG'
        elif direction_upper == 'DOWN':
            direction = 'SHORT'
        elif direction_upper == 'BUY':
            direction = 'LONG'
        elif direction_upper == 'SELL':
            direction = 'SHORT'
        elif direction_upper == 'BLOCKED':
            direction = 'BLOCKED'  # 保持BLOCKED不变
        else:
            direction = direction_upper

        # 对于BLOCKED类型，允许价格和金额为0
        if direction != 'BLOCKED' and entry_price <= 0:
            raise ValueError(f"开仓价格必须大于0: {entry_price}")

        if direction != 'BLOCKED' and amount <= 0:
            raise ValueError(f"交易金额必须大于0: {amount}")
        
        # 生成交易ID
        if trade_id is None:
            timestamp = int(time.time() * 1000000)  # 微秒时间戳
            thread_id = threading.get_ident()
            unique_suffix = str(uuid.uuid4())[:8]
            trade_id = f"{target_name}_{timestamp}_{thread_id}_{unique_suffix}"
        
        # 构建基础交易数据
        trade_data = {
            'trade_id': trade_id,
            'entry_timestamp': datetime.now().isoformat(),
            'target_name': target_name,
            'symbol': symbol,
            'direction': direction.upper(),
            'entry_price': entry_price,
            'amount': amount,
            'payout_ratio': payout_ratio,
            
            # 平仓字段（稍后填充）
            'exit_timestamp': None,
            'exit_price': None,
            'result': None,
            'profit_loss': None,
            'exit_reason': None
        }
        
        # 🎯 简化的上下文数据处理
        if context_data:
            # 直接映射的关键字段
            direct_mapping_fields = [
                'entry_signal_probability', 'entry_neutral_probability', 'entry_opposite_probability',
                'direction_advantage', 'up_advantage', 'down_advantage', 'meta_decision_reason',
                'threshold_check_result', 'signal_filtered_reason',
                'individual_model_probabilities', 'base_model_predictions',
                'entry_market_regime', 'entry_atr_percent', 'entry_adx_value',
                # V28.0 非对称融合决策分析参数
                'v28_meta_up_prob', 'v28_meta_down_prob',
                'v28_probability_advantage_score', 'v28_probability_advantage_weight',
                'v28_base_consensus_score', 'v28_base_consensus_weight',
                'v28_macro_trend_score', 'v28_macro_trend_weight',
                'v28_comprehensive_confidence_score', 'v28_confidence_threshold',
                'v28_base_model_up_prob', 'v28_base_model_down_prob',
                'v28_base_model_up_signal', 'v28_base_model_down_signal',
                'v28_model_divergence', 'v28_model_consistency',
                'v28_fusion_result', 'v28_final_decision'
            ]

            for field in direct_mapping_fields:
                if field in context_data:
                    trade_data[field] = context_data[field]

            # 🎯 将复杂的元模型特征合并为决策上下文摘要
            decision_summary = self._create_decision_context_summary(context_data)
            trade_data['decision_context_summary'] = decision_summary
        
        # 暂存到pending_trades
        with self.pending_lock:
            self.pending_trades[trade_id] = trade_data
        
        with self.stats_lock:
            self.total_entries += 1
        
        self.logger.info(f"交易开仓暂存: {trade_id} - {symbol} {direction} @{entry_price}")
        
        return trade_id

    def record_trade_exit(self, trade_id: str, exit_price: float,
                         result: str, exit_reason: str = "expired") -> bool:
        """
        记录交易平仓信息（合并数据并异步写入）

        Args:
            trade_id: 交易ID
            exit_price: 平仓价格
            result: 交易结果 (WIN 或 LOSS)
            exit_reason: 平仓原因

        Returns:
            是否成功记录
        """
        # 验证参数
        result = result.upper()
        if result not in ['WIN', 'LOSS', 'BLOCKED']:
            raise ValueError(f"无效的交易结果: {result}")

        # 对于BLOCKED类型，允许价格为0或任意值
        if result != 'BLOCKED' and exit_price <= 0:
            raise ValueError(f"平仓价格必须大于0: {exit_price}")

        # 从pending_trades中获取并移除开仓数据
        with self.pending_lock:
            if trade_id not in self.pending_trades:
                self.logger.error(f"未找到开仓记录: {trade_id}")
                return False

            trade_data = self.pending_trades.pop(trade_id)

        # 合并平仓信息
        trade_data.update({
            'exit_timestamp': datetime.now().isoformat(),
            'exit_price': exit_price,
            'result': result,
            'exit_reason': exit_reason
        })

        # 计算盈亏
        entry_price = trade_data['entry_price']
        amount = trade_data['amount']
        payout_ratio = trade_data['payout_ratio']
        direction = trade_data['direction']

        if result == 'WIN':
            profit_loss = amount * payout_ratio
        elif result == 'BLOCKED':
            profit_loss = 0.0  # 被阻止的交易没有盈亏
        else:  # LOSS
            profit_loss = -amount

        trade_data['profit_loss'] = profit_loss

        # 放入异步写入队列
        try:
            self.write_queue.put(trade_data, timeout=1.0)

            with self.stats_lock:
                self.total_exits += 1

            self.logger.info(f"交易平仓: {trade_id} - {result} @{exit_price}, P&L: {profit_loss:.2f}")
            return True

        except queue.Full:
            self.logger.error(f"写入队列已满，交易记录丢失: {trade_id}")
            with self.stats_lock:
                self.failed_writes += 1
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取日志系统统计信息"""
        with self.stats_lock:
            with self.pending_lock:
                return {
                    'total_entries': self.total_entries,
                    'total_exits': self.total_exits,
                    'total_writes': self.total_writes,
                    'failed_writes': self.failed_writes,
                    'pending_trades': len(self.pending_trades),
                    'queue_size': self.write_queue.qsize(),
                    'writer_thread_alive': self.writer_thread.is_alive() if self.writer_thread else False
                }

    def get_pending_trades(self) -> Dict[str, Dict[str, Any]]:
        """获取当前暂存的交易记录"""
        with self.pending_lock:
            return self.pending_trades.copy()

    def flush_queue(self, timeout: float = 5.0):
        """强制刷新队列中的所有数据"""
        try:
            self.write_queue.put({"__FLUSH__": True}, timeout=timeout)
            self.logger.info("队列刷新信号已发送")
        except queue.Full:
            self.logger.warning("无法发送刷新信号，队列已满")

    def get_current_log_file(self) -> str:
        """获取当前日志文件路径（公共接口）"""
        return self._get_current_log_file()

    def get_csv_fieldnames(self) -> list:
        """获取CSV字段名列表（用于兼容性检查）"""
        return self.CSV_COLUMNS.copy()

    def log_complete_trade(self, trade_data: Dict[str, Any]) -> bool:
        """
        一次性记录完整交易（兼容方法）

        这个方法提供与UnifiedComprehensiveLogger的兼容性，
        直接将完整的交易数据写入队列

        Args:
            trade_data: 完整的交易数据字典

        Returns:
            是否成功记录
        """
        try:
            # 验证必要字段
            required_fields = ['trade_id', 'entry_timestamp', 'exit_timestamp',
                             'target_name', 'symbol', 'direction', 'entry_price',
                             'exit_price', 'amount', 'result']

            for field in required_fields:
                if field not in trade_data:
                    self.logger.error(f"缺少必要字段: {field}")
                    return False

            # 确保数据格式正确
            processed_data = {
                'trade_id': str(trade_data['trade_id']),
                'entry_timestamp': trade_data['entry_timestamp'],
                'exit_timestamp': trade_data['exit_timestamp'],
                'target_name': trade_data['target_name'],
                'symbol': trade_data['symbol'],
                'direction': trade_data['direction'],
                'entry_price': float(trade_data['entry_price']),
                'exit_price': float(trade_data['exit_price']),
                'amount': float(trade_data['amount']),
                'payout_ratio': float(trade_data.get('payout_ratio', 0.85)),
                'result': trade_data['result'].upper(),
                'profit_loss': float(trade_data.get('profit_loss', 0)),
                'exit_reason': trade_data.get('exit_reason', 'expired'),
                'entry_market_snapshot_json': trade_data.get('entry_market_snapshot_json', '{}'),
                'exit_market_snapshot_json': trade_data.get('exit_market_snapshot_json', '{}'),
                'related_prediction_id': trade_data.get('related_prediction_id', ''),
                'context_data': trade_data.get('context_data', {}),
                'decision_context_summary': self._create_decision_context_summary(
                    trade_data.get('context_data', {})
                )
            }

            # 放入异步写入队列
            self.write_queue.put(processed_data, timeout=1.0)

            with self.stats_lock:
                self.total_entries += 1
                self.total_exits += 1

            self.logger.info(f"完整交易记录: {processed_data['trade_id']} - {processed_data['result']}")
            return True

        except queue.Full:
            self.logger.error(f"写入队列已满，交易记录丢失: {trade_data.get('trade_id', 'Unknown')}")
            with self.stats_lock:
                self.failed_writes += 1
            return False
        except Exception as e:
            self.logger.error(f"记录完整交易失败: {e}")
            with self.stats_lock:
                self.failed_writes += 1
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.stats_lock:
            return {
                'total_entries': self.total_entries,
                'total_exits': self.total_exits,
                'total_writes': self.total_writes,
                'failed_writes': self.failed_writes,
                'pending_trades': len(self.pending_trades),
                'queue_size': self.write_queue.qsize(),
                'writer_thread_alive': self.writer_thread.is_alive() if self.writer_thread else False
            }

    def stop(self):
        """停止日志系统"""
        self.stop_writer_thread()
        self.logger.info("统一交易日志系统已停止")


# 全局实例管理
_global_unified_logger = None
_logger_lock = threading.Lock()


def get_unified_trade_logger(base_log_dir: str = "trading_logs_unified",
                           auto_start: bool = True) -> UnifiedTradeLogger:
    """
    🎯 获取全局统一交易日志记录器实例（单例模式）- 使用简化格式

    Args:
        base_log_dir: 日志基础目录
        auto_start: 是否自动启动

    Returns:
        UnifiedTradeLogger实例（简化的28字段格式）
    """

    global _global_unified_logger

    with _logger_lock:
        if _global_unified_logger is None:
            _global_unified_logger = UnifiedTradeLogger(
                base_log_dir=base_log_dir,
                auto_start=auto_start
            )

        return _global_unified_logger


def reset_unified_logger():
    """重置全局日志记录器（用于测试）"""
    global _global_unified_logger

    with _logger_lock:
        if _global_unified_logger:
            _global_unified_logger.stop()
        _global_unified_logger = None
