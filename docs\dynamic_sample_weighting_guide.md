# 动态样本加权使用指南

## 概述

动态样本加权系统基于"不同市场环境下样本具有不同学习价值"的理念，通过为训练样本分配不同权重，显著提升模型在复杂市场环境下的学习效率和适应能力。

## 核心理念

### 传统问题
- **均等对待**: 所有样本被同等对待，忽略了市场环境差异
- **信息浪费**: 高价值样本（如高波动期、极端事件）未得到充分利用
- **时间盲区**: 忽略了数据的时效性，旧数据与新数据权重相同

### 动态加权优势
- **智能区分**: 根据市场环境动态调整样本重要性
- **时效优先**: 越新的数据获得更高权重
- **波动敏感**: 高波动期样本获得更多关注
- **稀有珍贵**: 极端市场事件样本权重提升

## 加权策略详解

### 🕒 1. 时间衰减加权

#### 核心思想
越新的数据越重要，体现市场的时效性特征。

#### 计算公式
```python
时间权重 = exp(-衰减率 × (最大时间 - 样本时间)^衰减幂次)
```

#### 配置参数
```python
'time_decay_rate': 0.1,        # 衰减率，越大衰减越快
'time_decay_unit': 'days',     # 时间单位：'days', 'hours', 'samples'
'time_decay_power': 1.0,       # 衰减幂次，控制衰减曲线形状
```

#### 效果示例
```
衰减率=0.1, 单位=days:
- 最新数据: 权重 = 1.00
- 1天前数据: 权重 = 0.90
- 7天前数据: 权重 = 0.50
- 30天前数据: 权重 = 0.05
```

### 📈 2. 波动率加权

#### 核心思想
高波动期的样本包含更多市场信息，应获得更高权重。

#### 计算公式
```python
波动率权重 = (样本波动率 / 平均波动率)^波动率幂次
波动率权重 = clip(波动率权重, 最小值, 最大值)
```

#### 配置参数
```python
'volatility_column': 'ATRr_14',  # 波动率指标列名
'volatility_power': 1.5,         # 波动率幂次，控制权重放大程度
'volatility_cap': 3.0,           # 权重上限，防止过度放大
```

#### 效果示例
```
ATR=50 (低波动): 权重 = 0.5
ATR=100 (正常): 权重 = 1.0  
ATR=200 (高波动): 权重 = 2.8
ATR=400 (极高): 权重 = 3.0 (受上限限制)
```

### 🎯 3. 市场状态加权

#### 核心思想
不同市场状态下的样本具有不同的学习价值。

#### 状态识别
```python
# 基于ATR和ADX识别市场状态
if ATR > 中位数 and ADX > 中位数:
    状态 = 'trending'    # 趋势市场
elif ATR > 中位数 and ADX <= 中位数:
    状态 = 'volatile'    # 震荡市场
elif ATR <= 中位数 and ADX > 中位数:
    状态 = 'ranging'     # 区间市场
else:
    状态 = 'calm'        # 平静市场
```

#### 权重配置
```python
'market_state_weights': {
    'trending': 1.2,    # 趋势市场样本更重要
    'ranging': 0.8,     # 区间市场样本较不重要
    'volatile': 1.5,    # 震荡市场样本很重要
    'calm': 0.9         # 平静市场样本略不重要
}
```

### 💎 4. 样本稀有度加权

#### 核心思想
稀有的市场情况（如闪崩、暴涨）样本更珍贵。

#### 识别方法
```python
# 基于价格变化幅度识别稀有样本
price_changes = abs(price_change_1p)
lower_threshold = percentile(price_changes, 5%)   # 前5%
upper_threshold = percentile(price_changes, 95%)  # 后5%

稀有样本 = (price_changes <= lower_threshold) | (price_changes >= upper_threshold)
```

#### 权重分配
```python
'rarity_percentile_threshold': 0.05,  # 前后5%为稀有样本
'rarity_weight_multiplier': 2.0,      # 稀有样本权重翻倍
```

## 使用方法

### 1. 基础配置

```python
# 在目标配置中启用动态样本加权
target_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    
    # 🔑 启用动态样本加权
    'enable_dynamic_sample_weighting': True,
    
    # 🎛️ 详细配置
    'dynamic_sample_weighting': {
        'enable_dynamic_weighting': True,
        'weighting_strategies': ['time_decay', 'volatility'],
        'strategy_weights': {'time_decay': 0.4, 'volatility': 0.6},
        'combination_method': 'weighted_average',
        
        # 时间衰减配置
        'time_decay_rate': 0.1,
        'time_decay_unit': 'days',
        'time_decay_power': 1.0,
        
        # 波动率加权配置
        'volatility_column': 'ATRr_14',
        'volatility_power': 1.5,
        'volatility_cap': 3.0,
        
        # 权重标准化
        'normalize_weights': True,
        'weight_clip_min': 0.1,
        'weight_clip_max': 5.0
    }
}
```

### 2. 不同策略配置模板

#### 保守策略（稳定优先）
```python
conservative_weighting = {
    'enable_dynamic_weighting': True,
    'weighting_strategies': ['time_decay'],  # 只使用时间衰减
    'time_decay_rate': 0.05,                # 较慢衰减
    'time_decay_unit': 'days',
    'normalize_weights': True,
    'weight_clip_min': 0.3,                 # 较高最小权重
    'weight_clip_max': 2.0                  # 较低最大权重
}
```

#### 平衡策略（推荐配置）
```python
balanced_weighting = {
    'enable_dynamic_weighting': True,
    'weighting_strategies': ['time_decay', 'volatility'],
    'strategy_weights': {'time_decay': 0.4, 'volatility': 0.6},
    'time_decay_rate': 0.1,
    'volatility_column': 'ATRr_14',
    'volatility_power': 1.5,
    'volatility_cap': 3.0,
    'normalize_weights': True,
    'weight_clip_min': 0.1,
    'weight_clip_max': 5.0
}
```

#### 激进策略（最大差异化）
```python
aggressive_weighting = {
    'enable_dynamic_weighting': True,
    'weighting_strategies': ['time_decay', 'volatility', 'rarity'],
    'strategy_weights': {'time_decay': 0.3, 'volatility': 0.5, 'rarity': 0.2},
    'time_decay_rate': 0.2,                 # 更快衰减
    'volatility_power': 2.0,                # 更强波动率效应
    'volatility_cap': 5.0,                  # 更高权重上限
    'enable_rarity_weighting': True,
    'rarity_weight_multiplier': 3.0,        # 稀有样本权重3倍
    'weight_clip_min': 0.05,                # 更低最小权重
    'weight_clip_max': 10.0                 # 更高最大权重
}
```

### 3. 与训练流程集成

#### 自动集成（推荐）
```python
# 在训练配置中启用，系统自动处理
target_config = {
    'enable_dynamic_sample_weighting': True,
    'dynamic_sample_weighting': balanced_weighting,
    # 其他训练配置...
}

# 训练时自动应用样本权重
# model.fit(X, y, sample_weight=calculated_weights, ...)
```

#### 手动使用
```python
from src.core.dynamic_sample_weighting import calculate_dynamic_sample_weights
from src.core.training_utils import prepare_training_data_with_weights

# 计算样本权重
sample_weights = calculate_dynamic_sample_weights(df, target_config, target_name)

# 准备训练数据（包含SMOTE兼容性）
X_train, y_train, weights = prepare_training_data_with_weights(
    df, y, target_config, target_name
)

# 训练模型
model.fit(X_train, y_train, sample_weight=weights)
```

## 权重组合策略

### 1. 加权平均（推荐）
```python
'combination_method': 'weighted_average'
'strategy_weights': {'time_decay': 0.4, 'volatility': 0.6}

# 最终权重 = 0.4 × 时间权重 + 0.6 × 波动率权重
```

### 2. 乘积组合
```python
'combination_method': 'multiply'

# 最终权重 = 时间权重 × 波动率权重 × 稀有度权重
```

### 3. 最大值组合
```python
'combination_method': 'max'

# 最终权重 = max(时间权重, 波动率权重, 稀有度权重)
```

## 与SMOTE的兼容性

### 🎯 改进的处理策略（V2.0）

#### 1. 数据准备阶段
```python
# 步骤1: 权重计算（独立于模型训练）
original_sample_weights = calculate_dynamic_sample_weights(X_df, target_config, target_name)

# 步骤2: SMOTE应用（只在训练集上）
X_resampled, y_resampled = smote.fit_resample(X_train_scaled, y_train_raw)

# 步骤3: 权重映射（智能分配）
final_sample_weights = handle_smote_sample_weights(original_weights, X_original, X_resampled, y_original, y_resampled)
```

#### 2. 智能权重分配策略
```python
# 策略A: 类别中位数权重 + 随机扰动
for new_sample in generated_samples:
    label = new_sample.label
    base_weight = class_weight_stats[label]['median']  # 使用中位数（更稳定）

    # 添加小幅随机扰动避免权重过于均匀
    if class_weight_stats[label]['std'] > 0:
        noise_scale = min(0.1, class_weight_stats[label]['std'] * 0.2)
        noise = np.random.normal(0, noise_scale)
        final_weight = max(0.1, base_weight + noise)
    else:
        final_weight = base_weight

    smote_weights[new_sample.index] = final_weight

# 策略B: 权重标准化
original_total_weight = original_weights.sum()
smote_total_weight = smote_weights.sum()
if smote_total_weight > 0:
    weight_ratio = original_total_weight / smote_total_weight
    smote_weights[original_count:] *= weight_ratio  # 只调整新样本权重
```

### 配置示例
```python
target_config = {
    # 启用动态样本加权
    'enable_dynamic_sample_weighting': True,
    'dynamic_sample_weighting': {
        'enable_dynamic_weighting': True,
        'weighting_strategies': ['time_decay', 'volatility', 'market_state'],
        'strategy_weights': {'time_decay': 0.3, 'volatility': 0.4, 'market_state': 0.3},
        'combination_method': 'weighted_average',

        # 权重标准化
        'normalize_weights': True,
        'weight_clip_min': 0.1,
        'weight_clip_max': 5.0
    },

    # SMOTE配置
    'smote_enable': True,
    'smote_k_neighbors': 5,
    'smote_min_samples_threshold': 5,
    'smote_random_state': 42,

    # 目标类型（影响SMOTE策略）
    'target_variable_type': 'UP_ONLY'  # 'UP_ONLY', 'DOWN_ONLY', 'BOTH'
}
```

### 🔄 完整数据流
```python
# 1. 数据准备阶段
X_df = pd.DataFrame(features)
y_array = np.array(targets)

# 2. 使用统一工具
X_resampled, y_resampled, final_weights = prepare_training_data_with_weights(
    X_df=X_df,
    y_array=y_array,
    target_config=target_config,
    target_name=target_name,
    apply_smote=True
)

# 3. 模型训练
model = train_model_with_sample_weights(
    model=lgbm_model,
    X_train=X_resampled,
    y_train=y_resampled,
    sample_weights=final_weights,
    eval_set=eval_set,
    **other_params
)
```

## 🔍 效果监控与诊断

### 权重统计信息
```python
from src.core.training_utils import get_sample_weight_statistics

stats = get_sample_weight_statistics(sample_weights, y_array, target_name)

print(f"权重启用: {stats['enabled']}")
print(f"权重范围: [{stats['min_weight']:.4f}, {stats['max_weight']:.4f}]")
print(f"权重均值: {stats['mean_weight']:.4f}")
print(f"权重总和: {stats['weight_sum']:.2f}")

# 权重分布分析
print(f"权重百分位数:")
print(f"  P10: {stats['weight_percentiles']['p10']:.4f}")
print(f"  P25: {stats['weight_percentiles']['p25']:.4f}")
print(f"  P50: {stats['weight_percentiles']['p50']:.4f}")
print(f"  P75: {stats['weight_percentiles']['p75']:.4f}")
print(f"  P90: {stats['weight_percentiles']['p90']:.4f}")

# 按类别统计
for class_label, class_stats in stats['class_statistics'].items():
    print(f"类别 {class_label}:")
    print(f"  样本数: {class_stats['count']}")
    print(f"  权重范围: [{class_stats['min_weight']:.4f}, {class_stats['max_weight']:.4f}]")
    print(f"  平均权重: {class_stats['mean_weight']:.4f}")
    print(f"  权重标准差: {class_stats['std_weight']:.4f}")
```

### 🚨 潜在影响与注意事项

#### 1. 正面影响
- **提升少数类识别**: 通过权重增强重要样本的学习
- **时间适应性**: 新数据获得更高权重，模型更适应市场变化
- **市场状态感知**: 不同市场环境下的样本获得差异化权重
- **与SMOTE协同**: 智能权重分配避免SMOTE后权重失衡

#### 2. 潜在风险
- **过拟合风险**: 过高的权重可能导致模型过度关注特定样本
- **权重失衡**: 极端权重分布可能影响模型稳定性
- **计算开销**: 动态权重计算增加训练时间
- **调参复杂性**: 多种权重策略的组合增加超参数调优难度

#### 3. SMOTE集成风险
- **权重稀释**: 新生成样本可能稀释原始权重分布
- **类别偏差**: 不当的权重分配可能加剧类别不平衡
- **噪声放大**: 权重可能放大SMOTE生成样本中的噪声

### 📊 监控指标

#### 训练过程监控
```python
# 权重分布健康检查
def check_weight_health(sample_weights, threshold_ratio=10.0):
    """检查权重分布是否健康"""
    max_weight = sample_weights.max()
    min_weight = sample_weights.min()
    weight_ratio = max_weight / min_weight if min_weight > 0 else float('inf')

    health_status = {
        'weight_ratio': weight_ratio,
        'is_healthy': weight_ratio <= threshold_ratio,
        'max_weight': max_weight,
        'min_weight': min_weight,
        'recommendation': 'OK' if weight_ratio <= threshold_ratio else 'Consider weight clipping'
    }

    return health_status

# 使用示例
health = check_weight_health(final_sample_weights)
print(f"权重健康状态: {health}")
```

#### 模型性能监控
```python
# 对比有无权重的模型性能
def compare_weighted_performance(X, y, target_config):
    """对比有无样本权重的模型性能"""

    # 无权重训练
    model_no_weight = LGBMClassifier(**lgbm_params)
    model_no_weight.fit(X, y)

    # 有权重训练
    X_weighted, y_weighted, weights = prepare_training_data_with_weights(X, y, target_config, 'test')
    model_with_weight = LGBMClassifier(**lgbm_params)
    model_with_weight.fit(X_weighted, y_weighted, sample_weight=weights)

    # 性能对比
    return {
        'no_weight_score': model_no_weight.score(X_test, y_test),
        'with_weight_score': model_with_weight.score(X_test, y_test),
        'improvement': model_with_weight.score(X_test, y_test) - model_no_weight.score(X_test, y_test)
    }
```

## 🎯 最佳实践

### 1. 权重策略选择
```python
# 推荐配置组合
recommended_configs = {
    'conservative': {
        'weighting_strategies': ['time_decay'],
        'strategy_weights': {'time_decay': 1.0},
        'weight_clip_min': 0.5,
        'weight_clip_max': 2.0
    },
    'balanced': {
        'weighting_strategies': ['time_decay', 'volatility'],
        'strategy_weights': {'time_decay': 0.6, 'volatility': 0.4},
        'weight_clip_min': 0.3,
        'weight_clip_max': 3.0
    },
    'aggressive': {
        'weighting_strategies': ['time_decay', 'volatility', 'market_state'],
        'strategy_weights': {'time_decay': 0.3, 'volatility': 0.4, 'market_state': 0.3},
        'weight_clip_min': 0.1,
        'weight_clip_max': 5.0
    }
}
```

### 2. SMOTE集成最佳实践
```python
# 推荐的SMOTE + 权重配置
optimal_smote_config = {
    'smote_enable': True,
    'smote_k_neighbors': 5,
    'smote_min_samples_threshold': 10,  # 提高阈值确保质量
    'smote_random_state': 42,

    # 权重配置
    'enable_dynamic_sample_weighting': True,
    'dynamic_sample_weighting': {
        'normalize_weights': True,
        'weight_clip_min': 0.2,  # 避免权重过小
        'weight_clip_max': 4.0   # 避免权重过大
    }
}
```

### 3. 渐进式启用策略
```python
# 阶段1: 仅启用时间衰减
stage1_config = {
    'enable_dynamic_sample_weighting': True,
    'dynamic_sample_weighting': {
        'weighting_strategies': ['time_decay'],
        'time_decay_rate': 0.05  # 温和的衰减率
    }
}

# 阶段2: 添加波动率权重
stage2_config = {
    'enable_dynamic_sample_weighting': True,
    'dynamic_sample_weighting': {
        'weighting_strategies': ['time_decay', 'volatility'],
        'strategy_weights': {'time_decay': 0.7, 'volatility': 0.3}
    }
}

# 阶段3: 完整配置
stage3_config = {
    'enable_dynamic_sample_weighting': True,
    'dynamic_sample_weighting': {
        'weighting_strategies': ['time_decay', 'volatility', 'market_state'],
        'strategy_weights': {'time_decay': 0.4, 'volatility': 0.4, 'market_state': 0.2}
    }
}
```

## 🔧 故障排除

### 常见问题与解决方案

#### 1. 权重计算失败
```python
# 问题: 权重计算返回None
# 原因: 特征数据缺失或配置错误
# 解决: 检查数据完整性和配置

def debug_weight_calculation(X_df, target_config, target_name):
    """调试权重计算问题"""
    print(f"数据形状: {X_df.shape}")
    print(f"缺失值: {X_df.isnull().sum().sum()}")
    print(f"配置启用状态: {target_config.get('enable_dynamic_sample_weighting', False)}")

    # 检查必要的特征列
    required_columns = ['ATRr_14', 'close']  # 根据配置调整
    missing_columns = [col for col in required_columns if col not in X_df.columns]
    if missing_columns:
        print(f"缺失必要特征列: {missing_columns}")

    return missing_columns
```

#### 2. SMOTE权重映射异常
```python
# 问题: SMOTE后权重数量不匹配
# 原因: 权重映射逻辑错误
# 解决: 验证权重映射过程

def validate_smote_weights(original_weights, smote_weights, y_original, y_resampled):
    """验证SMOTE权重映射"""
    original_count = len(y_original)
    resampled_count = len(y_resampled)

    validation_results = {
        'original_count': original_count,
        'resampled_count': resampled_count,
        'weight_count': len(smote_weights),
        'count_match': len(smote_weights) == resampled_count,
        'original_weights_preserved': np.allclose(smote_weights[:original_count], original_weights),
        'new_weights_reasonable': True
    }

    # 检查新权重是否合理
    if resampled_count > original_count:
        new_weights = smote_weights[original_count:]
        validation_results['new_weights_reasonable'] = (
            new_weights.min() > 0 and
            new_weights.max() < 10 * original_weights.max()
        )

    return validation_results
```

#### 3. 模型训练性能下降
```python
# 问题: 使用权重后模型性能下降
# 原因: 权重分布不合理或过拟合
# 解决: 调整权重参数或使用更保守的配置

def diagnose_weight_impact(model_with_weights, model_without_weights, X_test, y_test):
    """诊断权重对模型性能的影响"""

    # 性能对比
    score_with = model_with_weights.score(X_test, y_test)
    score_without = model_without_weights.score(X_test, y_test)

    # 预测分布对比
    pred_with = model_with_weights.predict_proba(X_test)[:, 1]
    pred_without = model_without_weights.predict_proba(X_test)[:, 1]

    diagnosis = {
        'performance_change': score_with - score_without,
        'prediction_correlation': np.corrcoef(pred_with, pred_without)[0, 1],
        'prediction_variance_ratio': pred_with.var() / pred_without.var(),
        'recommendation': 'OK' if score_with >= score_without else 'Consider reducing weight strength'
    }

    return diagnosis
```

### 🎛️ 调优建议

1. **从保守配置开始**: 使用较小的权重范围和简单的策略
2. **逐步增加复杂性**: 先验证基础功能，再添加高级特性
3. **监控权重分布**: 定期检查权重健康状态
4. **A/B测试**: 对比有无权重的模型性能
5. **交叉验证**: 确保权重策略在不同数据集上的稳定性

### 权重分布可视化
```python
import matplotlib.pyplot as plt

# 权重分布直方图
plt.figure(figsize=(12, 4))

plt.subplot(1, 3, 1)
plt.hist(sample_weights, bins=50, alpha=0.7)
plt.title('样本权重分布')
plt.xlabel('权重值')
plt.ylabel('频次')

plt.subplot(1, 3, 2)
plt.plot(sample_weights)
plt.title('权重时间序列')
plt.xlabel('样本索引')
plt.ylabel('权重值')

plt.subplot(1, 3, 3)
plt.scatter(df['ATRr_14'], sample_weights, alpha=0.5)
plt.title('权重vs波动率')
plt.xlabel('ATR')
plt.ylabel('权重值')

plt.tight_layout()
plt.show()
```

## 参数调优指南

### 时间衰减参数
```python
# 快速衰减（适合高频交易）
'time_decay_rate': 0.2,  'time_decay_unit': 'hours'

# 中等衰减（适合日内交易）
'time_decay_rate': 0.1,  'time_decay_unit': 'days'

# 慢速衰减（适合长期策略）
'time_decay_rate': 0.05, 'time_decay_unit': 'days'
```

### 波动率参数
```python
# 温和放大
'volatility_power': 1.2, 'volatility_cap': 2.0

# 标准放大
'volatility_power': 1.5, 'volatility_cap': 3.0

# 强烈放大
'volatility_power': 2.0, 'volatility_cap': 5.0
```

### 权重限制
```python
# 保守限制
'weight_clip_min': 0.3, 'weight_clip_max': 2.0

# 标准限制
'weight_clip_min': 0.1, 'weight_clip_max': 5.0

# 宽松限制
'weight_clip_min': 0.05, 'weight_clip_max': 10.0
```

## 最佳实践

### 1. 渐进式启用
```python
# 第一步：只启用时间衰减
phase1_config = {
    'weighting_strategies': ['time_decay'],
    'time_decay_rate': 0.05  # 保守设置
}

# 第二步：添加波动率加权
phase2_config = {
    'weighting_strategies': ['time_decay', 'volatility'],
    'volatility_power': 1.2  # 温和设置
}

# 第三步：完整配置
phase3_config = {
    'weighting_strategies': ['time_decay', 'volatility', 'rarity'],
    # 完整参数...
}
```

### 2. A/B测试对比
```python
# 对照组：无权重
control_config = {'enable_dynamic_sample_weighting': False}

# 实验组：动态权重
experiment_config = {'enable_dynamic_sample_weighting': True, ...}

# 比较模型性能指标
```

### 3. 不同市场环境配置
```python
# 牛市配置：重视趋势
bull_market_config = {
    'weighting_strategies': ['time_decay', 'volatility'],
    'strategy_weights': {'time_decay': 0.6, 'volatility': 0.4}
}

# 熊市配置：重视波动
bear_market_config = {
    'weighting_strategies': ['time_decay', 'volatility', 'rarity'],
    'strategy_weights': {'time_decay': 0.3, 'volatility': 0.5, 'rarity': 0.2}
}

# 震荡市配置：平衡权重
sideways_config = {
    'weighting_strategies': ['time_decay', 'volatility'],
    'strategy_weights': {'time_decay': 0.5, 'volatility': 0.5}
}
```

## 故障排除

### 1. 权重计算失败
**问题**: 样本权重为None或全为1
**解决**:
- 检查波动率列是否存在
- 验证时间索引格式
- 确认配置参数有效性

### 2. 权重分布异常
**问题**: 权重过于集中或分散
**解决**:
- 调整权重上下限
- 修改幂次参数
- 检查数据质量

### 3. SMOTE兼容性问题
**问题**: SMOTE后权重长度不匹配
**解决**:
- 确保权重扩展逻辑正确
- 检查类别平均权重计算
- 验证数据一致性

### 4. 训练性能下降
**问题**: 启用权重后模型性能变差
**解决**:
- 降低权重差异程度
- 调整组合策略权重
- 尝试不同的权重策略

## 总结

动态样本加权系统提供了：

1. **智能区分**: 根据市场环境动态调整样本重要性
2. **时效优先**: 体现数据的时间价值
3. **波动敏感**: 重视高信息量的市场时期
4. **稀有珍贵**: 充分利用极端市场事件
5. **灵活组合**: 多种策略可自由组合
6. **SMOTE兼容**: 与样本平衡技术无缝集成
7. **易于监控**: 完善的统计和可视化支持

通过合理配置和使用动态样本加权，可以显著提升模型在不同市场环境下的学习效率和预测准确性。
