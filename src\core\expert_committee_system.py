#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8.0专家委员会系统 - 市场状态自适应的专家模型集成系统

这个模块实现了从"单一模型"到"专家委员会"的战略转型：
1. 精确的市场状态识别
2. 专家模型训练管理
3. 智能调度预测系统
"""

import os
import json
import logging
import traceback
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Optional, Any, Union
import pandas as pd
import numpy as np
import joblib

logger = logging.getLogger(__name__)


class ExpertCommitteeMarketStateAnalyzer:
    """
    V8.0专家委员会系统的市场状态分析器
    
    专门为专家模型训练优化，重点识别四种核心状态：
    1. Strong_Trend_Up (强上升趋势) - 趋势专家
    2. Strong_Trend_Down (强下降趋势) - 趋势专家  
    3. High_Volatility_Sideways (高波动盘整) - 危险状态，过滤信号
    4. Low_Volatility_Sideways (低波动盘整) - 盘整专家
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化专家委员会市场状态分析器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
        # V8.0专家委员会系统配置参数
        self.atr_period = self.config.get('expert_system_atr_period', 14)
        self.adx_period = self.config.get('expert_system_adx_period', 14)
        self.ema_fast = self.config.get('expert_system_ema_fast', 12)
        self.ema_slow = self.config.get('expert_system_ema_slow', 26)
        
        # 🎯 V8.0优化：更精确的阈值设定
        self.high_vol_threshold = self.config.get('expert_system_high_vol_threshold', 2.0)
        self.low_trend_threshold = self.config.get('expert_system_low_trend_threshold', 25)
        self.strong_trend_threshold = self.config.get('expert_system_strong_trend_threshold', 35)
        self.strong_up_threshold = self.config.get('expert_system_strong_up_threshold', 0.8)
        self.strong_down_threshold = self.config.get('expert_system_strong_down_threshold', -0.8)
        
        logger.info("ExpertCommitteeMarketStateAnalyzer 初始化完成")
    
    def identify_market_state(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        识别市场状态
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            dict: 包含不同市场状态的字典，每个状态都是0/1的序列
        """
        try:
            if df is None or df.empty:
                logger.warning("输入数据为空，无法识别市场状态")
                return self._get_default_states(len(df) if df is not None else 0)
            
            C = df['close']
            H = df['high'] 
            L = df['low']
            
            # 计算技术指标
            atr_percent = self._calculate_atr_percent(H, L, C)
            adx = self._calculate_adx(H, L, C)
            ema_diff_pct = self._calculate_ema_diff_pct(C)
            
            # 识别市场状态
            market_states = self._classify_market_states(atr_percent, adx, ema_diff_pct)
            
            logger.debug(f"市场状态识别完成，识别了 {len(market_states)} 种状态")
            return market_states
            
        except Exception as e:
            logger.error(f"市场状态识别失败: {e}")
            logger.debug(traceback.format_exc())
            return self._get_default_states(len(df) if df is not None else 0)
    
    def get_current_market_state(self, df: pd.DataFrame) -> str:
        """
        获取当前最新的市场状态（用于实时预测）
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            str: 当前市场状态名称
        """
        try:
            market_states = self.identify_market_state(df)
            
            if not market_states:
                return 'unknown'
            
            # 获取最新时间点的状态
            latest_idx = -1
            
            # 按优先级检查状态（危险状态优先）
            priority_states = [
                'high_vol_sideways',    # 最危险，优先识别
                'strong_trend_up',      # 强上升趋势
                'strong_trend_down',    # 强下降趋势  
                'low_vol_sideways',     # 低波动盘整
                'normal_trend'          # 正常趋势
            ]
            
            for state_name in priority_states:
                if state_name in market_states:
                    if market_states[state_name].iloc[latest_idx] == 1:
                        logger.info(f"当前市场状态: {state_name}")
                        return state_name
            
            logger.warning("无法确定当前市场状态，返回默认状态")
            return 'normal_trend'
            
        except Exception as e:
            logger.error(f"获取当前市场状态失败: {e}")
            return 'unknown'
    
    def _calculate_atr_percent(self, H: pd.Series, L: pd.Series, C: pd.Series) -> pd.Series:
        """计算ATR百分比（波动率指标）"""
        try:
            if len(C) >= self.atr_period:
                tr = np.maximum(H - L, np.maximum(abs(H - C.shift(1)), abs(L - C.shift(1))))
                atr = tr.rolling(window=self.atr_period, min_periods=1).mean()
                atr_percent = (atr / C) * 100
            else:
                atr_percent = pd.Series([1.0] * len(C), index=C.index)
            
            return atr_percent
            
        except Exception as e:
            logger.error(f"计算ATR百分比失败: {e}")
            return pd.Series([1.0] * len(C), index=C.index)
    
    def _calculate_adx(self, H: pd.Series, L: pd.Series, C: pd.Series) -> pd.Series:
        """计算ADX（趋势强度指标）"""
        try:
            if len(C) >= self.adx_period:
                # 计算True Range
                tr = np.maximum(H - L, np.maximum(abs(H - C.shift(1)), abs(L - C.shift(1))))
                
                # 计算方向性移动
                plus_dm = np.maximum(H.diff(), 0)
                minus_dm = np.maximum(-L.diff(), 0)
                plus_dm[H.diff() <= L.diff()] = 0
                minus_dm[L.diff() <= H.diff()] = 0
                
                # 平滑处理
                tr_smooth = tr.rolling(window=self.adx_period, min_periods=1).mean()
                plus_di = 100 * (plus_dm.rolling(window=self.adx_period, min_periods=1).mean() / tr_smooth)
                minus_di = 100 * (minus_dm.rolling(window=self.adx_period, min_periods=1).mean() / tr_smooth)
                
                # 计算ADX
                dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di + 1e-10)
                adx = dx.rolling(window=self.adx_period, min_periods=1).mean()
            else:
                adx = pd.Series([15.0] * len(C), index=C.index)
            
            return adx
            
        except Exception as e:
            logger.error(f"计算ADX失败: {e}")
            return pd.Series([15.0] * len(C), index=C.index)
    
    def _calculate_ema_diff_pct(self, C: pd.Series) -> pd.Series:
        """计算EMA差值百分比（趋势方向）"""
        try:
            if len(C) >= self.ema_slow:
                ema_fast_val = C.ewm(span=self.ema_fast, min_periods=1).mean()
                ema_slow_val = C.ewm(span=self.ema_slow, min_periods=1).mean()
                ema_diff_pct = ((ema_fast_val - ema_slow_val) / ema_slow_val) * 100
            else:
                ema_diff_pct = pd.Series([0.0] * len(C), index=C.index)
            
            return ema_diff_pct
            
        except Exception as e:
            logger.error(f"计算EMA差值百分比失败: {e}")
            return pd.Series([0.0] * len(C), index=C.index)
    
    def _classify_market_states(self, atr_percent: pd.Series, adx: pd.Series, ema_diff_pct: pd.Series) -> Dict[str, pd.Series]:
        """分类市场状态"""
        try:
            market_states = {}
            
            # 基础条件
            high_vol_condition = atr_percent > self.high_vol_threshold
            low_trend_condition = adx < self.low_trend_threshold
            strong_trend_condition = adx > self.strong_trend_threshold
            
            # 趋势方向条件
            strong_uptrend_condition = ema_diff_pct > self.strong_up_threshold
            strong_downtrend_condition = ema_diff_pct < self.strong_down_threshold
            
            # === 核心专家状态定义 ===
            
            # 1. 🔴 高波动盘整（最危险状态）- 过滤所有信号
            market_states['high_vol_sideways'] = (high_vol_condition & low_trend_condition).astype(int)

            # 2. 🟢 强上升趋势 - 上涨趋势专家
            market_states['strong_trend_up'] = (strong_trend_condition & strong_uptrend_condition).astype(int)

            # 3. 🔴 强下降趋势 - 下跌趋势专家  
            market_states['strong_trend_down'] = (strong_trend_condition & strong_downtrend_condition).astype(int)

            # 4. 🟡 低波动盘整 - 盘整专家
            low_vol_condition = atr_percent < (self.high_vol_threshold * 0.5)
            market_states['low_vol_sideways'] = (low_vol_condition & low_trend_condition).astype(int)

            # 5. 正常趋势（中等强度趋势）
            moderate_trend_condition = (adx >= self.low_trend_threshold) & (adx <= self.strong_trend_threshold)
            market_states['normal_trend'] = moderate_trend_condition.astype(int)
            
            # 保持兼容性
            market_states['strong_uptrend'] = market_states['strong_trend_up']
            market_states['strong_downtrend'] = market_states['strong_trend_down']
            
            return market_states
            
        except Exception as e:
            logger.error(f"市场状态分类失败: {e}")
            return self._get_default_states(len(atr_percent))
    
    def _get_default_states(self, length: int) -> Dict[str, pd.Series]:
        """获取默认市场状态"""
        default_index = pd.RangeIndex(length)
        return {
            'high_vol_sideways': pd.Series([0] * length, index=default_index),
            'strong_trend_up': pd.Series([0] * length, index=default_index),
            'strong_trend_down': pd.Series([0] * length, index=default_index),
            'low_vol_sideways': pd.Series([0] * length, index=default_index),
            'normal_trend': pd.Series([1] * length, index=default_index),
            # 兼容性
            'strong_uptrend': pd.Series([0] * length, index=default_index),
            'strong_downtrend': pd.Series([0] * length, index=default_index)
        }


class ExpertModelManager:
    """
    专家模型管理器
    
    负责管理不同市场状态的专家模型：
    - 模型文件命名规范
    - 模型保存和加载
    - 专家模型元数据管理
    """
    
    def __init__(self, model_base_dir: str = "models"):
        """
        初始化专家模型管理器
        
        Args:
            model_base_dir: 模型基础目录
        """
        self.model_base_dir = model_base_dir
        self.expert_states = [
            'strong_trend_up',
            'strong_trend_down', 
            'low_vol_sideways'
        ]
        logger.info(f"ExpertModelManager 初始化完成，模型目录: {model_base_dir}")
    
    def get_expert_model_path(self, target_name: str, market_state: str, fold: int = None) -> str:
        """
        获取专家模型文件路径
        
        Args:
            target_name: 目标名称
            market_state: 市场状态
            fold: 折数（可选）
            
        Returns:
            str: 专家模型文件路径
        """
        if fold is not None:
            filename = f"expert_{target_name}_{market_state}_fold_{fold}.joblib"
        else:
            filename = f"expert_{target_name}_{market_state}.joblib"
        
        return os.path.join(self.model_base_dir, filename)
    
    def get_expert_scaler_path(self, target_name: str, market_state: str) -> str:
        """获取专家模型缩放器路径"""
        filename = f"expert_scaler_{target_name}_{market_state}.joblib"
        return os.path.join(self.model_base_dir, filename)
    
    def get_expert_metadata_path(self, target_name: str, market_state: str) -> str:
        """获取专家模型元数据路径"""
        filename = f"expert_metadata_{target_name}_{market_state}.json"
        return os.path.join(self.model_base_dir, filename)
    
    def save_expert_model_metadata(self, target_name: str, market_state: str, 
                                 training_result: Dict[str, Any], 
                                 data_stats: Dict[str, Any]) -> bool:
        """
        保存专家模型元数据
        
        Args:
            target_name: 目标名称
            market_state: 市场状态
            training_result: 训练结果
            data_stats: 数据统计信息
            
        Returns:
            bool: 保存是否成功
        """
        try:
            metadata = {
                'expert_system_version': 'V8.0',
                'target_name': target_name,
                'market_state': market_state,
                'timestamp_utc': datetime.now(timezone.utc).isoformat(),
                'training_result': training_result,
                'data_stats': data_stats,
                'model_type': 'expert_committee_model'
            }
            
            metadata_path = self.get_expert_metadata_path(target_name, market_state)
            os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            logger.info(f"专家模型元数据已保存: {metadata_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存专家模型元数据失败: {e}")
            return False
    
    def load_expert_model_metadata(self, target_name: str, market_state: str) -> Optional[Dict[str, Any]]:
        """
        加载专家模型元数据
        
        Args:
            target_name: 目标名称
            market_state: 市场状态
            
        Returns:
            dict: 元数据字典，如果失败返回None
        """
        try:
            metadata_path = self.get_expert_metadata_path(target_name, market_state)
            
            if not os.path.exists(metadata_path):
                logger.warning(f"专家模型元数据文件不存在: {metadata_path}")
                return None
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            logger.debug(f"专家模型元数据加载成功: {metadata_path}")
            return metadata
            
        except Exception as e:
            logger.error(f"加载专家模型元数据失败: {e}")
            return None


class ExpertCommitteeTrainer:
    """
    V8.0专家委员会训练器

    负责为不同市场状态训练专属的专家模型：
    1. 数据分组：根据市场状态分割训练数据
    2. 独立训练：为每个市场状态训练专门的模型
    3. 专家保存：保存专家模型和相关文件
    """

    def __init__(self, model_manager: ExpertModelManager,
                 state_analyzer: ExpertCommitteeMarketStateAnalyzer):
        """
        初始化专家委员会训练器

        Args:
            model_manager: 专家模型管理器
            state_analyzer: 市场状态分析器
        """
        self.model_manager = model_manager
        self.state_analyzer = state_analyzer
        logger.info("ExpertCommitteeTrainer 初始化完成")

    def train_expert_models(self, df_with_features: pd.DataFrame,
                          target_series: pd.Series,
                          target_config: Dict[str, Any],
                          target_name: str) -> Dict[str, Any]:
        """
        训练专家模型

        Args:
            df_with_features: 包含特征的DataFrame
            target_series: 目标变量Series
            target_config: 目标配置
            target_name: 目标名称

        Returns:
            dict: 训练结果
        """
        try:
            logger.info(f"开始为 {target_name} 训练专家模型...")

            # 1. 识别市场状态
            market_states = self.state_analyzer.identify_market_state(df_with_features)

            # 2. 为每个专家状态分组数据并训练
            expert_results = {}

            for state_name in self.model_manager.expert_states:
                if state_name not in market_states:
                    logger.warning(f"市场状态 {state_name} 未找到，跳过")
                    continue

                # 获取该状态的数据
                state_mask = market_states[state_name] == 1
                state_data_count = state_mask.sum()

                logger.info(f"  市场状态 {state_name}: {state_data_count} 条数据")

                if state_data_count < 100:  # 最少数据量检查
                    logger.warning(f"  {state_name} 数据量不足 ({state_data_count} < 100)，跳过训练")
                    continue

                # 分割数据
                X_state = df_with_features[state_mask]
                y_state = target_series[state_mask]

                # 训练专家模型
                expert_result = self._train_single_expert(
                    X_state, y_state, target_config, target_name, state_name
                )

                expert_results[state_name] = expert_result

            logger.info(f"专家模型训练完成，成功训练 {len(expert_results)} 个专家")

            return {
                'success': True,
                'expert_results': expert_results,
                'total_experts': len(expert_results),
                'target_name': target_name
            }

        except Exception as e:
            logger.error(f"专家模型训练失败: {e}")
            logger.debug(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'target_name': target_name
            }

    def _train_single_expert(self, X_state: pd.DataFrame, y_state: pd.Series,
                           target_config: Dict[str, Any], target_name: str,
                           state_name: str) -> Dict[str, Any]:
        """
        训练单个专家模型

        Args:
            X_state: 该状态的特征数据
            y_state: 该状态的目标数据
            target_config: 目标配置
            target_name: 目标名称
            state_name: 状态名称

        Returns:
            dict: 训练结果
        """
        try:
            from sklearn.preprocessing import StandardScaler
            from sklearn.model_selection import TimeSeriesSplit
            from src.core import prediction

            logger.info(f"    开始训练 {target_name} 的 {state_name} 专家...")

            # 🎯 V8.0专家委员会：为不同专家设置不同策略
            expert_config = self._get_expert_specific_config(target_config, state_name)
            logger.info(f"      使用专家特定配置: {state_name}")

            # 1. 数据预处理
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_state)
            X_scaled_df = pd.DataFrame(X_scaled, columns=X_state.columns, index=X_state.index)

            # 2. 交叉验证训练
            n_splits = expert_config.get('cv_folds', 3)
            tscv = TimeSeriesSplit(n_splits=n_splits)

            models = []
            fold_results = []

            for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(X_scaled_df)):
                logger.info(f"      训练 {state_name} 专家第 {fold_idx + 1}/{n_splits} 折...")

                # 分割数据
                X_train_fold = X_scaled_df.iloc[train_idx]
                X_val_fold = X_scaled_df.iloc[val_idx]
                y_train_fold = y_state.iloc[train_idx]
                y_val_fold = y_state.iloc[val_idx]

                # 训练模型（使用专家特定配置）
                model = prediction.train_single_model(
                    X_train_fold, y_train_fold, expert_config
                )

                if model is None:
                    logger.warning(f"        {state_name} 专家第 {fold_idx + 1} 折训练失败")
                    continue

                models.append(model)

                # 验证
                try:
                    val_pred = model.predict(X_val_fold)
                    val_acc = (val_pred == y_val_fold).mean()
                    fold_results.append({
                        'fold': fold_idx + 1,
                        'val_accuracy': val_acc,
                        'train_samples': len(y_train_fold),
                        'val_samples': len(y_val_fold)
                    })
                    logger.info(f"        第 {fold_idx + 1} 折验证准确率: {val_acc:.4f}")
                except Exception as e:
                    logger.warning(f"        第 {fold_idx + 1} 折验证失败: {e}")

            if not models:
                raise ValueError(f"所有折的训练都失败了")

            # 3. 保存专家模型
            self._save_expert_artifacts(models, scaler, target_name, state_name,
                                      X_state.columns.tolist(), fold_results)

            # 4. 计算平均性能
            avg_val_acc = np.mean([r['val_accuracy'] for r in fold_results]) if fold_results else 0.0

            logger.info(f"    ✓ {state_name} 专家训练完成，平均验证准确率: {avg_val_acc:.4f}")

            return {
                'success': True,
                'state_name': state_name,
                'avg_val_accuracy': avg_val_acc,
                'n_folds': len(models),
                'fold_results': fold_results,
                'data_samples': len(X_state)
            }

        except Exception as e:
            logger.error(f"训练 {state_name} 专家失败: {e}")
            return {
                'success': False,
                'state_name': state_name,
                'error': str(e)
            }

    def _get_expert_specific_config(self, base_config: Dict[str, Any], state_name: str) -> Dict[str, Any]:
        """
        为不同专家生成特定配置

        Args:
            base_config: 基础配置
            state_name: 专家状态名称

        Returns:
            dict: 专家特定配置
        """
        # 复制基础配置
        expert_config = base_config.copy()

        # 🎯 V8.0专家委员会：为不同专家设置不同策略
        if state_name in ['strong_trend_up', 'strong_trend_down']:
            # 趋势专家：更积极的参数设置
            logger.info(f"      配置趋势专家 ({state_name}): 更深的树，更高的类别权重")
            expert_config.update({
                'max_depth': 10,                    # 更深的树，捕捉复杂趋势模式
                'num_leaves': 40,                   # 更多叶子节点
                'class_weight': {0: 1.0, 1: 40.0},  # 更高的正类权重，积极捕捉趋势信号
                'reg_alpha': 3.0,                   # 适中的正则化
                'reg_lambda': 5.0,
                'min_child_samples': 15,            # 较小的最小样本数，允许更细粒度分割
                'subsample': 0.9,                   # 高采样率，保留更多信息
                'colsample_bytree': 0.9,
                'learning_rate': 0.08,              # 稍高的学习率，加快收敛
                'n_estimators': 1200,               # 更多树，充分学习趋势模式

                # 🎯 专家特定优化目标
                'optuna_metric': 'f1_score',       # 趋势专家重视F1分数
                'optuna_n_trials': 120,            # 更多试验次数
            })

        elif state_name == 'low_vol_sideways':
            # 盘整专家：更保守的参数设置
            logger.info(f"      配置盘整专家 ({state_name}): 更浅的树，更强的正则化")
            expert_config.update({
                'max_depth': 5,                     # 更浅的树，避免过拟合噪音
                'num_leaves': 20,                   # 较少叶子节点
                'class_weight': {0: 1.0, 1: 25.0},  # 适中的正类权重，保守过滤噪音
                'reg_alpha': 8.0,                   # 更强的正则化，防止过拟合
                'reg_lambda': 12.0,
                'min_child_samples': 30,            # 较大的最小样本数，要求更强的统计显著性
                'subsample': 0.7,                   # 较低采样率，增加泛化能力
                'colsample_bytree': 0.7,
                'learning_rate': 0.05,              # 较低学习率，更稳定的学习
                'n_estimators': 800,                # 适中的树数量

                # 🎯 专家特定优化目标
                'optuna_metric': 'precision_positive_focused',  # 盘整专家重视精确率
                'optuna_n_trials': 100,
            })

        else:
            # 其他状态使用默认配置
            logger.info(f"      使用默认配置: {state_name}")

        return expert_config

    def _save_expert_artifacts(self, models: List, scaler, target_name: str,
                             state_name: str, feature_names: List[str],
                             fold_results: List[Dict]) -> None:
        """
        保存专家模型相关文件

        Args:
            models: 训练好的模型列表
            scaler: 缩放器
            target_name: 目标名称
            state_name: 状态名称
            feature_names: 特征名称列表
            fold_results: 折验证结果
        """
        try:
            # 确保目录存在
            os.makedirs(self.model_manager.model_base_dir, exist_ok=True)

            # 保存模型文件
            for i, model in enumerate(models):
                model_path = self.model_manager.get_expert_model_path(
                    target_name, state_name, fold=i+1
                )
                joblib.dump(model, model_path)
                logger.debug(f"      专家模型已保存: {model_path}")

            # 保存缩放器
            scaler_path = self.model_manager.get_expert_scaler_path(target_name, state_name)
            joblib.dump(scaler, scaler_path)

            # 保存特征列表
            features_path = scaler_path.replace('_scaler_', '_features_').replace('.joblib', '.json')
            with open(features_path, 'w', encoding='utf-8') as f:
                json.dump(feature_names, f, indent=2, ensure_ascii=False)

            # 保存元数据
            training_result = {
                'n_folds': len(models),
                'avg_val_accuracy': np.mean([r['val_accuracy'] for r in fold_results]) if fold_results else 0.0,
                'fold_results': fold_results
            }

            data_stats = {
                'n_features': len(feature_names),
                'total_samples': sum(r['train_samples'] + r['val_samples'] for r in fold_results)
            }

            self.model_manager.save_expert_model_metadata(
                target_name, state_name, training_result, data_stats
            )

            logger.info(f"      专家模型文件保存完成: {state_name}")

        except Exception as e:
            logger.error(f"保存专家模型文件失败: {e}")
            raise


class ExpertCommitteePredictor:
    """
    V8.0专家委员会预测器

    负责智能调度预测：
    1. 实时市场状态判断
    2. 动态加载对应专家模型
    3. 执行专家预测
    """

    def __init__(self, model_manager: ExpertModelManager,
                 state_analyzer: ExpertCommitteeMarketStateAnalyzer):
        """
        初始化专家委员会预测器

        Args:
            model_manager: 专家模型管理器
            state_analyzer: 市场状态分析器
        """
        self.model_manager = model_manager
        self.state_analyzer = state_analyzer
        self._loaded_experts = {}  # 缓存已加载的专家模型
        logger.info("ExpertCommitteePredictor 初始化完成")

    def predict_with_expert_committee(self, df_current: pd.DataFrame,
                                    target_name: str) -> Dict[str, Any]:
        """
        使用专家委员会进行预测

        Args:
            df_current: 当前数据
            target_name: 目标名称

        Returns:
            dict: 预测结果
        """
        try:
            # 1. 判断当前市场状态
            current_state = self.state_analyzer.get_current_market_state(df_current)

            logger.info(f"当前市场状态: {current_state}")

            # 2. 检查是否为危险状态
            if current_state == 'high_vol_sideways':
                logger.warning("当前为高波动盘整状态，过滤所有信号")
                return {
                    'success': True,
                    'prediction': 'FILTER',
                    'market_state': current_state,
                    'reason': '高波动盘整状态，风险过高'
                }

            # 3. 加载对应的专家模型
            expert_key = f"{target_name}_{current_state}"

            if expert_key not in self._loaded_experts:
                expert_artifacts = self._load_expert_model(target_name, current_state)
                if expert_artifacts is None:
                    logger.warning(f"无法加载 {current_state} 专家模型，使用默认预测")
                    return {
                        'success': False,
                        'error': f'专家模型不存在: {current_state}',
                        'market_state': current_state
                    }
                self._loaded_experts[expert_key] = expert_artifacts

            # 4. 执行专家预测
            expert_artifacts = self._loaded_experts[expert_key]
            prediction_result = self._predict_with_expert(df_current, expert_artifacts)

            prediction_result['market_state'] = current_state
            prediction_result['expert_used'] = current_state

            logger.info(f"专家委员会预测完成: {current_state} 专家")
            return prediction_result

        except Exception as e:
            logger.error(f"专家委员会预测失败: {e}")
            logger.debug(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'market_state': 'unknown'
            }

    def _load_expert_model(self, target_name: str, state_name: str) -> Optional[Dict[str, Any]]:
        """
        加载专家模型

        Args:
            target_name: 目标名称
            state_name: 状态名称

        Returns:
            dict: 专家模型文件，如果失败返回None
        """
        try:
            # 检查元数据
            metadata = self.model_manager.load_expert_model_metadata(target_name, state_name)
            if metadata is None:
                logger.warning(f"专家模型元数据不存在: {target_name}_{state_name}")
                return None

            # 加载缩放器
            scaler_path = self.model_manager.get_expert_scaler_path(target_name, state_name)
            if not os.path.exists(scaler_path):
                logger.warning(f"专家缩放器不存在: {scaler_path}")
                return None

            scaler = joblib.load(scaler_path)

            # 加载模型（所有折）
            models = []
            fold_idx = 1
            while True:
                model_path = self.model_manager.get_expert_model_path(
                    target_name, state_name, fold=fold_idx
                )
                if not os.path.exists(model_path):
                    break

                model = joblib.load(model_path)
                models.append(model)
                fold_idx += 1

            if not models:
                logger.warning(f"未找到专家模型文件: {target_name}_{state_name}")
                return None

            # 加载特征列表
            features_path = scaler_path.replace('_scaler_', '_features_').replace('.joblib', '.json')
            if os.path.exists(features_path):
                with open(features_path, 'r', encoding='utf-8') as f:
                    feature_names = json.load(f)
            else:
                feature_names = None

            logger.info(f"专家模型加载成功: {target_name}_{state_name} ({len(models)} 折)")

            return {
                'models': models,
                'scaler': scaler,
                'feature_names': feature_names,
                'metadata': metadata
            }

        except Exception as e:
            logger.error(f"加载专家模型失败: {e}")
            return None

    def _predict_with_expert(self, df_current: pd.DataFrame,
                           expert_artifacts: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用专家模型进行预测

        Args:
            df_current: 当前数据
            expert_artifacts: 专家模型文件

        Returns:
            dict: 预测结果
        """
        try:
            models = expert_artifacts['models']
            scaler = expert_artifacts['scaler']
            feature_names = expert_artifacts['feature_names']

            # 准备特征数据
            if feature_names:
                # 确保特征顺序一致
                missing_features = set(feature_names) - set(df_current.columns)
                if missing_features:
                    logger.warning(f"缺少特征: {missing_features}")
                    # 用0填充缺失特征
                    for feature in missing_features:
                        df_current[feature] = 0

                X_current = df_current[feature_names].iloc[-1:].values
            else:
                # 使用所有数值特征
                numeric_cols = df_current.select_dtypes(include=[np.number]).columns
                X_current = df_current[numeric_cols].iloc[-1:].values

            # 缩放特征
            X_scaled = scaler.transform(X_current)

            # 集成预测（所有折的平均）
            predictions = []
            probabilities = []

            for model in models:
                pred = model.predict(X_scaled)[0]
                predictions.append(pred)

                if hasattr(model, 'predict_proba'):
                    proba = model.predict_proba(X_scaled)[0]
                    probabilities.append(proba)

            # 投票决策
            final_prediction = max(set(predictions), key=predictions.count)

            # 平均概率
            if probabilities:
                avg_probabilities = np.mean(probabilities, axis=0)
                confidence = np.max(avg_probabilities)
            else:
                avg_probabilities = None
                confidence = len([p for p in predictions if p == final_prediction]) / len(predictions)

            return {
                'success': True,
                'prediction': int(final_prediction),
                'confidence': float(confidence),
                'probabilities': avg_probabilities.tolist() if avg_probabilities is not None else None,
                'n_models': len(models)
            }

        except Exception as e:
            logger.error(f"专家预测失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


# === V8.0专家委员会系统工厂函数 ===

def create_expert_committee_system(config: Dict[str, Any] = None,
                                 model_base_dir: str = "models") -> Tuple[ExpertCommitteeTrainer, ExpertCommitteePredictor]:
    """
    创建完整的V8.0专家委员会系统

    Args:
        config: 配置字典
        model_base_dir: 模型基础目录

    Returns:
        tuple: (训练器, 预测器)
    """
    # 创建组件
    state_analyzer = ExpertCommitteeMarketStateAnalyzer(config)
    model_manager = ExpertModelManager(model_base_dir)
    trainer = ExpertCommitteeTrainer(model_manager, state_analyzer)
    predictor = ExpertCommitteePredictor(model_manager, state_analyzer)

    logger.info("V8.0专家委员会系统创建完成")

    return trainer, predictor
