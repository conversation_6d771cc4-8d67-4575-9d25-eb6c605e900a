#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 配置管理器 - 外部化硬编码参数的统一管理

提供统一的配置参数获取接口，支持层级查找和类型转换，
解决代码中硬编码参数的问题，提高代码的灵活性和可维护性。
"""

import logging
from typing import Any, Dict, Optional, Union, Type, List
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from config import GLOBAL_DEFAULTS
except ImportError:
    # 如果无法导入，使用空字典作为默认值
    GLOBAL_DEFAULTS = {}

logger = logging.getLogger(__name__)


class ConfigManager:
    """
    🚀 配置管理器
    
    提供统一的配置参数获取接口，支持多层级配置查找：
    1. 函数参数 (最高优先级)
    2. 目标配置 (target_config)
    3. 模块配置 (module_config)
    4. 全局默认配置 (GLOBAL_DEFAULTS)
    5. 函数默认值 (最低优先级)
    """
    
    def __init__(self):
        """初始化配置管理器"""
        self.global_defaults = GLOBAL_DEFAULTS
        self._cache = {}  # 配置缓存
    
    def get_param(
        self,
        key: str,
        target_config: Optional[Dict[str, Any]] = None,
        module_config: Optional[Dict[str, Any]] = None,
        default: Any = None,
        param_type: Optional[Type] = None,
        module_name: str = 'default'
    ) -> Any:
        """
        获取配置参数，支持多层级查找
        
        Args:
            key: 参数键名
            target_config: 目标特定配置字典
            module_config: 模块配置字典
            default: 默认值
            param_type: 期望的参数类型
            module_name: 模块名称（用于从全局配置中查找）
            
        Returns:
            配置参数值
            
        Raises:
            TypeError: 如果参数类型不匹配
            ValueError: 如果参数值无效
        """
        try:
            # 1. 优先级1：目标配置
            if target_config and key in target_config:
                value = target_config[key]
                return self._validate_and_convert(value, param_type, key)
            
            # 2. 优先级2：模块配置
            if module_config and key in module_config:
                value = module_config[key]
                return self._validate_and_convert(value, param_type, key)
            
            # 3. 优先级3：全局默认配置
            global_value = self._get_from_global_defaults(key, module_name)
            if global_value is not None:
                return self._validate_and_convert(global_value, param_type, key)
            
            # 4. 优先级4：函数默认值
            if default is not None:
                return self._validate_and_convert(default, param_type, key)
            
            # 如果都没有找到，抛出异常
            raise ValueError(f"配置参数 '{key}' 未找到，且没有提供默认值")
            
        except Exception as e:
            logger.warning(f"获取配置参数 '{key}' 失败: {e}")
            return default
    
    def get_data_processing_param(
        self,
        key: str,
        target_config: Optional[Dict[str, Any]] = None,
        default: Any = None,
        param_type: Optional[Type] = None
    ) -> Any:
        """获取数据处理相关参数"""
        return self.get_param(
            key, target_config, None, default, param_type, 'data_processing'
        )
    
    def get_feature_engineering_param(
        self,
        key: str,
        target_config: Optional[Dict[str, Any]] = None,
        default: Any = None,
        param_type: Optional[Type] = None
    ) -> Any:
        """获取特征工程相关参数"""
        return self.get_param(
            key, target_config, None, default, param_type, 'feature_engineering'
        )
    
    def get_market_analysis_param(
        self,
        key: str,
        target_config: Optional[Dict[str, Any]] = None,
        default: Any = None,
        param_type: Optional[Type] = None
    ) -> Any:
        """获取市场分析相关参数"""
        return self.get_param(
            key, target_config, None, default, param_type, 'market_analysis'
        )
    
    def get_logging_param(
        self,
        key: str,
        target_config: Optional[Dict[str, Any]] = None,
        default: Any = None,
        param_type: Optional[Type] = None
    ) -> Any:
        """获取日志相关参数"""
        return self.get_param(
            key, target_config, None, default, param_type, 'logging'
        )
    
    def get_algorithm_param(
        self,
        key: str,
        target_config: Optional[Dict[str, Any]] = None,
        default: Any = None,
        param_type: Optional[Type] = None
    ) -> Any:
        """获取算法相关参数"""
        return self.get_param(
            key, target_config, None, default, param_type, 'algorithms'
        )
    
    def get_constant(
        self,
        category: str,
        key: str,
        default: str = ""
    ) -> str:
        """
        获取字符串常量
        
        Args:
            category: 常量分类 ('error_messages', 'success_messages', 'status_indicators')
            key: 常量键名
            default: 默认值
            
        Returns:
            字符串常量
        """
        try:
            constants = self.global_defaults.get('constants', {})
            category_constants = constants.get(category, {})
            return category_constants.get(key, default)
        except Exception as e:
            logger.warning(f"获取常量 '{category}.{key}' 失败: {e}")
            return default
    
    def _get_from_global_defaults(self, key: str, module_name: str) -> Any:
        """从全局默认配置中获取参数"""
        try:
            # 首先尝试从指定模块中获取
            if module_name in self.global_defaults:
                module_config = self.global_defaults[module_name]
                if isinstance(module_config, dict) and key in module_config:
                    return module_config[key]
            
            # 然后尝试从所有模块中查找
            for module_config in self.global_defaults.values():
                if isinstance(module_config, dict) and key in module_config:
                    return module_config[key]
            
            return None
        except Exception as e:
            logger.warning(f"从全局默认配置获取参数 '{key}' 失败: {e}")
            return None
    
    def _validate_and_convert(self, value: Any, param_type: Optional[Type], key: str) -> Any:
        """验证和转换参数类型"""
        if param_type is None:
            return value
        
        try:
            # 如果值已经是期望类型，直接返回
            if isinstance(value, param_type):
                return value
            
            # 尝试类型转换
            if param_type == bool:
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif param_type == int:
                return int(float(value))  # 先转float再转int，处理"1.0"这种情况
            elif param_type == float:
                return float(value)
            elif param_type == str:
                return str(value)
            elif param_type == list:
                if isinstance(value, (tuple, set)):
                    return list(value)
                elif isinstance(value, str):
                    # 尝试解析字符串列表
                    import ast
                    return ast.literal_eval(value)
                return [value]  # 单个值转为列表
            else:
                # 其他类型直接尝试转换
                return param_type(value)
                
        except (ValueError, TypeError, SyntaxError) as e:
            logger.warning(f"参数 '{key}' 类型转换失败: {value} -> {param_type}, 错误: {e}")
            return value
    
    def get_module_config(self, module_name: str) -> Dict[str, Any]:
        """获取整个模块的配置"""
        return self.global_defaults.get(module_name, {})
    
    def list_available_modules(self) -> List[str]:
        """列出所有可用的模块名称"""
        return list(self.global_defaults.keys())
    
    def validate_config(self, config: Dict[str, Any], module_name: str) -> Dict[str, Any]:
        """
        验证配置的完整性和正确性
        
        Args:
            config: 要验证的配置字典
            module_name: 模块名称
            
        Returns:
            验证后的配置字典
        """
        validated_config = config.copy()
        module_defaults = self.get_module_config(module_name)
        
        # 添加缺失的默认值
        for key, default_value in module_defaults.items():
            if key not in validated_config:
                validated_config[key] = default_value
                logger.debug(f"为模块 '{module_name}' 添加默认配置: {key} = {default_value}")
        
        return validated_config
    
    def clear_cache(self):
        """清空配置缓存"""
        self._cache.clear()


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例（单例模式）"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


# 便捷函数
def get_param(
    key: str,
    target_config: Optional[Dict[str, Any]] = None,
    module_config: Optional[Dict[str, Any]] = None,
    default: Any = None,
    param_type: Optional[Type] = None,
    module_name: str = 'default'
) -> Any:
    """便捷的参数获取函数"""
    return get_config_manager().get_param(
        key, target_config, module_config, default, param_type, module_name
    )


def get_data_processing_param(
    key: str,
    target_config: Optional[Dict[str, Any]] = None,
    default: Any = None,
    param_type: Optional[Type] = None
) -> Any:
    """便捷的数据处理参数获取函数"""
    return get_config_manager().get_data_processing_param(
        key, target_config, default, param_type
    )


def get_feature_engineering_param(
    key: str,
    target_config: Optional[Dict[str, Any]] = None,
    default: Any = None,
    param_type: Optional[Type] = None
) -> Any:
    """便捷的特征工程参数获取函数"""
    return get_config_manager().get_feature_engineering_param(
        key, target_config, default, param_type
    )


def get_constant(category: str, key: str, default: str = "") -> str:
    """便捷的常量获取函数"""
    return get_config_manager().get_constant(category, key, default)
