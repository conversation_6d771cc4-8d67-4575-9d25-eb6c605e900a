#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone SSH连接配置文件

请根据你的实际iPhone设置修改以下配置
"""

# iPhone SSH连接配置 (已测试可用)
IPHONE_CONFIG = {
    "host": "**************",  # iPhone IP地址
    "port": 22,                # SSH端口，通常为22
    "username": "mobile",      # SSH用户名
    "password": "sdzddhy",     # SSH密码 (已验证)
    "timeout": 15              # 连接超时时间（秒）
}

# 备用配置（如果mobile用户不行，可以尝试root用户）
IPHONE_CONFIG_ROOT = {
    "host": "**************",
    "port": 22,
    "username": "root",        # 使用root用户
    "password": "alpine",      # 或者其他密码如"sdzddhy"
    "timeout": 15
}

# ZXTouch配置
ZXTOUCH_CONFIG = {
    "script_path": "/var/mobile/Library/ZXTouch/scripts/zxtouch_trading_executor.js",
    "test_script_path": "/var/mobile/Library/ZXTouch/scripts/zxtouch_test_simple.js",
    "execution_timeout": 120,  # ZXTouch脚本执行超时时间（秒）
    "retry_count": 2,          # 重试次数
    "retry_delay": 5           # 重试间隔（秒）
}

# 常见的iPhone SSH配置组合
COMMON_CONFIGS = [
    # 配置1: mobile用户 + alpine密码
    {
        "name": "mobile_alpine",
        "host": "**************",
        "port": 22,
        "username": "mobile",
        "password": "alpine",
        "timeout": 15
    },
    # 配置2: root用户 + alpine密码
    {
        "name": "root_alpine", 
        "host": "**************",
        "port": 22,
        "username": "root",
        "password": "alpine",
        "timeout": 15
    },
    # 配置3: mobile用户 + 自定义密码
    {
        "name": "mobile_custom",
        "host": "**************", 
        "port": 22,
        "username": "mobile",
        "password": "sdzddhy",  # 根据trade_executor_jailbreak.py中的配置
        "timeout": 15
    },
    # 配置4: root用户 + 自定义密码
    {
        "name": "root_custom",
        "host": "**************",
        "port": 22, 
        "username": "root",
        "password": "sdzddhy",
        "timeout": 15
    }
]

def get_iphone_config():
    """获取iPhone配置"""
    return IPHONE_CONFIG

def get_zxtouch_config():
    """获取ZXTouch配置"""
    return ZXTOUCH_CONFIG

def get_common_configs():
    """获取常见配置列表"""
    return COMMON_CONFIGS

# 使用说明
"""
使用方法：

1. 修改IPHONE_CONFIG中的参数：
   - host: 你的iPhone IP地址
   - username: SSH用户名（通常是mobile或root）
   - password: SSH密码（请修改默认密码alpine）

2. 如果不确定配置，可以尝试COMMON_CONFIGS中的不同组合

3. 在其他脚本中导入配置：
   from iphone_config import get_iphone_config
   config = get_iphone_config()

4. 测试连接：
   python test_iphone_connection.py
"""
