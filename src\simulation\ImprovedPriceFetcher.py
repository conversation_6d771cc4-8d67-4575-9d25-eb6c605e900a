# ImprovedPriceFetcher.py
"""
改进的价格获取器，在原始PriceFetcher基础上添加健康检查和重连机制
"""

import time
import threading
import logging
from typing import Optional, Callable
from enum import Enum
from .PriceFetcher import PriceFetcher

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"
    DEGRADED = "degraded"  # 降级模式（虽然当前版本不使用，但保持兼容性）

class ImprovedPriceFetcher:
    """
    改进的价格获取器
    
    在原始PriceFetcher基础上添加：
    - 健康检查机制
    - 自动重连功能
    - 连接状态监控
    - 价格数据新鲜度检查
    """
    
    def __init__(self, 
                 symbol: str = "BTCUSDT",
                 health_check_interval: float = 30.0,
                 max_reconnect_attempts: int = 10,
                 price_stale_threshold: float = 120.0,
                 price_callback: Optional[Callable[[float], None]] = None,
                 state_callback: Optional[Callable[[ConnectionState], None]] = None):
        
        self.symbol = symbol.upper()
        self.health_check_interval = health_check_interval
        self.max_reconnect_attempts = max_reconnect_attempts
        self.price_stale_threshold = price_stale_threshold
        
        # 回调函数
        self.price_callback = price_callback
        self.state_callback = state_callback
        
        # 原始价格获取器
        self.price_fetcher = PriceFetcher(symbol=self.symbol)
        
        # 状态管理
        self.connection_state = ConnectionState.DISCONNECTED
        self.is_running = False
        self.reconnect_attempts = 0
        self.last_price_time = 0
        
        # 健康检查线程
        self.health_check_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_reconnects': 0,
            'total_errors': 0,
            'start_time': 0,
            'last_error_time': 0
        }
        
        logger.info(f"改进价格获取器初始化完成 - 交易对: {self.symbol}")
    
    def _update_connection_state(self, new_state: ConnectionState):
        """更新连接状态"""
        if self.connection_state != new_state:
            self.connection_state = new_state
            logger.info(f"连接状态变化: {new_state.value}")
            
            if self.state_callback:
                try:
                    self.state_callback(new_state)
                except Exception as e:
                    logger.error(f"状态回调执行失败: {e}")
    
    def _on_price_update(self, price: float):
        """价格更新处理"""
        self.last_price_time = time.time()
        
        if self.price_callback:
            try:
                self.price_callback(price)
            except Exception as e:
                logger.error(f"价格回调执行失败: {e}")
    
    def _health_check_worker(self):
        """健康检查工作线程 - 增强版本"""
        logger.info(f"健康检查线程启动 - {self.symbol}")
        consecutive_failures = 0
        max_consecutive_failures = 3

        while not self.stop_event.is_set():
            try:
                time.sleep(self.health_check_interval)

                if self.stop_event.is_set():
                    break

                current_time = time.time()
                health_issues = []

                # 1. 检查价格数据新鲜度
                if self.last_price_time > 0:
                    age = current_time - self.last_price_time

                    if age > self.price_stale_threshold:
                        health_issues.append(f"价格数据过期 {age:.0f} 秒")
                else:
                    health_issues.append("从未收到价格数据")

                # 2. 检查原始获取器状态
                if self.is_running and not self.price_fetcher.is_running:
                    health_issues.append("原始价格获取器已停止")

                # 3. 检查TWM状态
                if hasattr(self.price_fetcher, 'twm') and self.price_fetcher.twm:
                    if not self.price_fetcher.twm.is_alive():
                        health_issues.append("TWM进程已停止")

                # 4. 检查连接状态
                if self.connection_state == ConnectionState.FAILED:
                    health_issues.append("连接状态为失败")
                elif self.connection_state == ConnectionState.RECONNECTING:
                    # 如果重连时间过长，也认为有问题
                    if hasattr(self, '_reconnect_start_time'):
                        reconnect_duration = current_time - self._reconnect_start_time
                        if reconnect_duration > 300:  # 5分钟
                            health_issues.append(f"重连时间过长 ({reconnect_duration:.0f} 秒)")

                # 处理健康问题
                if health_issues:
                    consecutive_failures += 1
                    logger.warning(f"健康检查发现问题 (连续 {consecutive_failures} 次): {', '.join(health_issues)}")

                    # 如果连续多次失败，强制重连
                    if consecutive_failures >= max_consecutive_failures:
                        logger.error(f"连续 {consecutive_failures} 次健康检查失败，强制重连")
                        self.force_reconnect()
                        consecutive_failures = 0  # 重置计数
                    elif self.connection_state != ConnectionState.RECONNECTING:
                        # 如果不在重连状态，尝试重连
                        self._attempt_reconnect()
                else:
                    # 健康检查通过，重置失败计数
                    if consecutive_failures > 0:
                        logger.info(f"健康检查恢复正常 - {self.symbol}")
                        consecutive_failures = 0

            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                logger.exception("健康检查异常详细信息:")

        logger.info(f"健康检查线程结束 - {self.symbol}")
    
    def _attempt_reconnect(self):
        """尝试重连 - 增强版本"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"达到最大重连次数 ({self.max_reconnect_attempts})")
            self._update_connection_state(ConnectionState.FAILED)
            return

        self._update_connection_state(ConnectionState.RECONNECTING)
        self.reconnect_attempts += 1
        self.stats['total_reconnects'] += 1

        logger.info(f"第 {self.reconnect_attempts} 次重连尝试 - {self.symbol}")

        try:
            # 1. 强制停止原有连接
            if self.price_fetcher.is_running:
                logger.info("停止现有WebSocket连接...")
                self.price_fetcher.stop_stream()
                time.sleep(3)  # 增加等待时间确保完全停止

            # 2. 清理TWM实例（如果需要）
            if hasattr(self.price_fetcher, 'twm') and self.price_fetcher.twm:
                try:
                    if self.price_fetcher.twm.is_alive():
                        logger.info("停止TWM实例...")
                        self.price_fetcher.twm.stop()
                        time.sleep(2)
                except Exception as twm_stop_error:
                    logger.warning(f"停止TWM时出错: {twm_stop_error}")

            # 3. 重新创建PriceFetcher实例（彻底重置）
            logger.info("重新创建PriceFetcher实例...")
            old_symbol = self.price_fetcher.symbol_to_fetch
            self.price_fetcher = PriceFetcher(symbol=old_symbol)

            # 4. 重新启动
            logger.info("启动新的WebSocket连接...")
            self.price_fetcher.start_stream()

            # 5. 等待并验证连接
            verification_attempts = 0
            max_verification_attempts = 10

            while verification_attempts < max_verification_attempts:
                time.sleep(1)
                verification_attempts += 1

                if self.price_fetcher.is_running:
                    # 检查是否真的有价格数据
                    test_price = self.price_fetcher.get_current_price()
                    if test_price is not None:
                        logger.info(f"重连成功 - 获取到价格: {test_price}")
                        self._update_connection_state(ConnectionState.CONNECTED)
                        self.reconnect_attempts = 0  # 重置重连计数
                        return

                logger.info(f"等待连接稳定... ({verification_attempts}/{max_verification_attempts})")

            # 如果验证失败，安排下一次重连
            logger.warning(f"第 {self.reconnect_attempts} 次重连验证失败")
            if self.reconnect_attempts < self.max_reconnect_attempts:
                delay = min(2 ** self.reconnect_attempts, 30)  # 指数退避，最大30秒
                logger.info(f"将在 {delay} 秒后进行下一次重连尝试")
                threading.Timer(delay, self._attempt_reconnect).start()

        except Exception as e:
            logger.error(f"重连异常: {e}")
            logger.exception("重连异常详细信息:")
            self.stats['total_errors'] += 1
            self.stats['last_error_time'] = time.time()

            # 安排下一次重连
            if self.reconnect_attempts < self.max_reconnect_attempts:
                delay = min(2 ** self.reconnect_attempts, 30)
                logger.info(f"异常后将在 {delay} 秒后重试")
                threading.Timer(delay, self._attempt_reconnect).start()
    
    def start_stream(self) -> bool:
        """启动价格数据流"""
        if self.is_running:
            logger.warning(f"价格获取器已在运行 - {self.symbol}")
            return True
        
        logger.info(f"启动价格数据流 - {self.symbol}")
        self._update_connection_state(ConnectionState.CONNECTING)
        
        try:
            # 启动原始价格获取器
            self.price_fetcher.start_stream()
            
            if self.price_fetcher.is_running:
                self.is_running = True
                self.stats['start_time'] = time.time()
                self._update_connection_state(ConnectionState.CONNECTED)
                
                # 启动健康检查线程
                self.stop_event.clear()
                self.health_check_thread = threading.Thread(
                    target=self._health_check_worker,
                    daemon=True
                )
                self.health_check_thread.start()
                
                logger.info(f"价格数据流启动成功 - {self.symbol}")
                return True
            else:
                logger.error(f"原始价格获取器启动失败 - {self.symbol}")
                self._update_connection_state(ConnectionState.FAILED)
                return False
                
        except Exception as e:
            logger.error(f"启动价格数据流异常: {e}")
            self._update_connection_state(ConnectionState.FAILED)
            return False
    
    def stop_stream(self):
        """停止价格数据流"""
        if not self.is_running:
            logger.warning(f"价格获取器未运行 - {self.symbol}")
            return
        
        logger.info(f"停止价格数据流 - {self.symbol}")
        
        # 停止健康检查线程
        self.stop_event.set()
        
        # 停止原始价格获取器
        try:
            self.price_fetcher.stop_stream()
        except Exception as e:
            logger.error(f"停止原始价格获取器异常: {e}")
        
        self.is_running = False
        self._update_connection_state(ConnectionState.DISCONNECTED)
        
        logger.info(f"价格数据流已停止 - {self.symbol}")
    
    def get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            price = self.price_fetcher.get_current_price()
            if price is not None:
                self._on_price_update(price)
            return price
        except Exception as e:
            logger.error(f"获取价格异常: {e}")
            return None
    
    def is_price_fresh(self, max_age_seconds: float = 60.0) -> bool:
        """检查价格数据是否新鲜"""
        if self.last_price_time == 0:
            return False
        
        current_time = time.time()
        age = current_time - self.last_price_time
        return age <= max_age_seconds
    
    def get_last_update_time(self) -> float:
        """获取最后更新时间"""
        return self.last_price_time
    
    def get_connection_state(self) -> ConnectionState:
        """获取连接状态"""
        return self.connection_state
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = self.stats.copy()
        stats.update({
            'is_running': self.is_running,
            'connection_state': self.connection_state.value,
            'last_price_time': self.last_price_time,
            'reconnect_attempts': self.reconnect_attempts
        })
        return stats
    
    def force_reconnect(self):
        """强制重连"""
        logger.info(f"强制重连请求 - {self.symbol}")
        self.reconnect_attempts = 0  # 重置重连计数
        threading.Thread(target=self._attempt_reconnect, daemon=True).start()
    
    def set_price_callback(self, callback: Callable[[float], None]):
        """设置价格更新回调函数"""
        self.price_callback = callback
    
    def set_state_callback(self, callback: Callable[[ConnectionState], None]):
        """设置连接状态回调函数"""
        self.state_callback = callback
