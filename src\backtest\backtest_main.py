# backtest_main.py
"""
二元期权回测主程序
整合数据获取、信号生成、交易执行和结果分析
"""

import pandas as pd
import numpy as np
import os
import sys
import json
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志记录器
logger = logging.getLogger(__name__)
if not logger.handlers:
    logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

# 导入项目模块
try:
    import config
    from src.core import data_utils
    from src.backtest.backtest_engine import BacktestEngine, PositionSizer, RiskManager
    from src.backtest.backtest_utils import calculate_performance_metrics, calculate_daily_stats, save_results_to_files, validate_trade_data
    import backtest_config as bt_config
    from src.backtest.backtest_validation import BacktestValidator, validate_backtest_integrity
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必需的文件都在正确的位置")
    sys.exit(1)

class BinaryOptionBacktester:
    """二元期权回测器主类"""
    
    def __init__(self, config_dict: Dict = None):
        """
        初始化回测器
        
        Args:
            config_dict: 配置字典，如果为None则使用默认配置
        """
        self.config = config_dict or bt_config.get_backtest_config()
        
        # 初始化组件
        self.position_sizer = PositionSizer(
            method=self.config['position_sizing'],
            fixed_amount=self.config['fixed_amount'],
            kelly_multiplier=self.config['kelly_multiplier'],
            percentage_risk=self.config['percentage_risk'],
            min_bet=self.config['min_bet'],
            max_bet=self.config['max_bet']
        )
        
        self.risk_manager = RiskManager(
            max_daily_loss=self.config['max_daily_loss'],
            max_consecutive_losses=self.config['max_consecutive_losses'],
            stop_loss_enabled=self.config['stop_loss_enabled']
        )
        
        self.engine = BacktestEngine(
            initial_balance=self.config['initial_balance'],
            payout_ratio=self.config['payout_ratio'],
            expiry_minutes=self.config['expiry_minutes'],
            position_sizer=self.position_sizer,
            risk_manager=self.risk_manager
        )
        
        # 模型和数据
        self.models = {}
        self.scalers = {}
        self.feature_lists = {}
        self.historical_data = None
        
    def load_models(self) -> bool:
        """
        加载训练好的模型
        
        Returns:
            是否成功加载
        """
        try:
            model_types = ['up', 'down']
            
            for model_type in model_types:
                # 加载模型
                try:
                    model_path = bt_config.get_model_path(model_type)
                    if os.path.exists(model_path):
                        self.models[model_type] = joblib.load(model_path)
                        if self.config['verbose']:
                            print(f"已加载 {model_type.upper()} 模型: {model_path}")
                    else:
                        print(f"警告: 未找到 {model_type.upper()} 模型文件: {model_path}")
                        return False
                except FileNotFoundError as e:
                    print(f"错误: {e}")
                    return False
                except Exception as e:
                    print(f"加载 {model_type.upper()} 模型时发生错误: {e}")
                    return False
                
                # 加载缩放器
                scaler_path = bt_config.get_scaler_path(model_type)
                if os.path.exists(scaler_path):
                    self.scalers[model_type] = joblib.load(scaler_path)
                else:
                    print(f"警告: 未找到 {model_type.upper()} 缩放器文件: {scaler_path}")
                    self.scalers[model_type] = None
                
                # 加载特征列表
                features_path = bt_config.get_features_path(model_type)
                if os.path.exists(features_path):
                    with open(features_path, 'r', encoding='utf-8') as f:
                        self.feature_lists[model_type] = json.load(f)
                else:
                    print(f"警告: 未找到 {model_type.upper()} 特征文件: {features_path}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"加载模型时发生错误: {e}")
            return False
    
    def load_historical_data(self) -> bool:
        """
        加载历史数据
        
        Returns:
            是否成功加载
        """
        try:
            if self.config['verbose']:
                print("正在获取历史数据...")
            
            # 创建匿名Binance客户端用于获取历史数据
            try:
                from binance.client import Client
                anonymous_client = Client()  # 匿名客户端，可以访问公共数据
            except Exception as e:
                print(f"创建匿名Binance客户端失败: {e}")
                return False

            # 使用项目的data_utils获取数据
            self.historical_data = data_utils.fetch_binance_history(
                binance_client=anonymous_client,
                symbol=self.config['symbol'],
                interval=self.config['interval'],
                limit=self.config['data_limit']
            )
            
            if self.historical_data is None or self.historical_data.empty:
                print("错误: 无法获取历史数据")
                return False
            
            # 过滤时间范围
            start_date = pd.to_datetime(self.config['start_date'])
            end_date = pd.to_datetime(self.config['end_date']) + pd.Timedelta(days=1)  # 包含结束日期

            if self.config['verbose']:
                print(f"原始数据时间范围: {self.historical_data.index[0]} 到 {self.historical_data.index[-1]}")
                print(f"过滤时间范围: {start_date} 到 {end_date}")

            # 确保时区一致性
            if self.historical_data.index.tz is not None:
                # 如果历史数据有时区，将开始和结束日期转换为相同时区
                if start_date.tz is None:
                    start_date = start_date.tz_localize('UTC')
                if end_date.tz is None:
                    end_date = end_date.tz_localize('UTC')
                # 转换到相同时区
                self.historical_data.index = self.historical_data.index.tz_convert('UTC')
                start_date = start_date.tz_convert('UTC')
                end_date = end_date.tz_convert('UTC')
            else:
                # 如果历史数据没有时区，移除开始和结束日期的时区
                if start_date.tz is not None:
                    start_date = start_date.tz_localize(None)
                if end_date.tz is not None:
                    end_date = end_date.tz_localize(None)

            # 过滤数据
            filtered_data = self.historical_data[
                (self.historical_data.index >= start_date) &
                (self.historical_data.index <= end_date)
            ]

            if filtered_data.empty:
                print(f"警告: 指定时间范围内没有数据，使用最近的数据")
                # 如果过滤后为空，使用最近的数据
                self.historical_data = self.historical_data.tail(min(500, len(self.historical_data)))
            else:
                self.historical_data = filtered_data
            
            if self.config['verbose']:
                print(f"已加载历史数据: {len(self.historical_data)} 条记录")
                print(f"时间范围: {self.historical_data.index[0]} 到 {self.historical_data.index[-1]}")
            
            return True
            
        except Exception as e:
            print(f"加载历史数据时发生错误: {e}")
            return False
    
    def generate_features_vectorized(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        向量化生成特征，大幅提升性能同时严格防止数据泄露

        Args:
            data: 原始价格数据

        Returns:
            包含各模型特征的字典，按时间索引
        """
        print("🚀 使用向量化特征生成引擎...")

        try:
            # 导入向量化特征引擎
            from src.core.vectorized_feature_engine import VectorizedFeatureEngine
            from src.optimization.mtfa_optimizer import MTFAOptimizer

            # 创建配置字典
            engine_config = {
                'symbol': self.config['symbol'],
                'interval': self.config['interval'],
                'prediction_periods': self.config['prediction_periods'],
                'min_historical_bars_for_prediction': 100,

                # 特征工程配置
                'enable_price_change': bt_config.ENABLE_PRICE_CHANGE,
                'price_change_periods': bt_config.PRICE_CHANGE_PERIODS,
                'enable_volume': bt_config.ENABLE_VOLUME,
                'volume_avg_period': bt_config.VOLUME_AVG_PERIOD,
                'enable_candle': bt_config.ENABLE_CANDLE,
                'enable_pattern_recognition': bt_config.ENABLE_PATTERN_RECOGNITION,
                'pattern_recognition_thresholds': bt_config.PATTERN_RECOGNITION_THRESHOLDS,
                'enable_ta': bt_config.ENABLE_TA,
                'hma_period': bt_config.HMA_PERIOD,
                'kc_period': bt_config.KC_PERIOD,
                'kc_atr_period': bt_config.KC_ATR_PERIOD,
                'kc_multiplier': bt_config.KC_MULTIPLIER,
                'atr_period': bt_config.ATR_PERIOD,
                'rsi_period': bt_config.RSI_PERIOD,
                'macd_fast': bt_config.MACD_FAST,
                'macd_slow': bt_config.MACD_SLOW,
                'macd_sign': bt_config.MACD_SIGN,
                'stoch_k': bt_config.STOCH_K,
                'stoch_d': bt_config.STOCH_D,
                'stoch_smooth_k': bt_config.STOCH_SMOOTH_K,
                'cci_constant': bt_config.CCI_CONSTANT,
                'enable_ta_derived_features': bt_config.ENABLE_TA_DERIVED_FEATURES,
                'enable_time': bt_config.ENABLE_TIME,
                'enable_time_trigonometric': bt_config.ENABLE_TIME_TRIGONOMETRIC,
                'enable_trend': bt_config.ENABLE_TREND,
                'trend_periods': bt_config.TREND_PERIODS,
                'enable_adx_trend_features': bt_config.ENABLE_ADX_TREND_FEATURES,
                'enable_ema_trend_features': bt_config.ENABLE_EMA_TREND_FEATURES,
                'enable_mtfa': bt_config.ENABLE_MTFA,
                'mtfa_timeframes': bt_config.MTFA_TIMEFRAMES,
                'mtfa_feature_lookback_periods': bt_config.MTFA_FEATURE_LOOKBACK_PERIODS,
                'mtfa_specific_lookbacks': bt_config.MTFA_SPECIFIC_LOOKBACKS,
                'mtfa_min_bars_to_fetch': bt_config.MTFA_MIN_BARS_TO_FETCH,
                'mtfa_min_bars_for_calc': bt_config.MTFA_MIN_BARS_FOR_CALC,
                'mtfa_fetch_buffer': bt_config.MTFA_FETCH_BUFFER,
                'enable_fund_flow': bt_config.ENABLE_FUND_FLOW,
                'fund_flow_ratio_smoothing_period': bt_config.FUND_FLOW_RATIO_SMOOTHING_PERIOD,
                'enable_intelligent_nan_processing': bt_config.ENABLE_INTELLIGENT_NAN_PROCESSING,
                'enable_safe_fill_nans': bt_config.ENABLE_SAFE_FILL_NANS,
                'enable_advanced_feature_validation': bt_config.ENABLE_ADVANCED_FEATURE_VALIDATION
            }

            # 创建向量化特征引擎
            feature_engine = VectorizedFeatureEngine(engine_config)

            # 生成向量化特征
            features_dict = feature_engine.generate_features_vectorized(
                data, model_types=['up', 'down']
            )

            # 过滤特征以匹配模型需求
            filtered_features = {}
            for model_type in ['up', 'down']:
                if model_type in features_dict and model_type in self.feature_lists:
                    required_features = self.feature_lists[model_type]
                    features_df = features_dict[model_type]

                    if not features_df.empty:
                        # 只保留模型需要的特征
                        available_features = [f for f in required_features if f in features_df.columns]

                        if available_features:
                            filtered_df = features_df[available_features].copy()

                            # 填充缺失的特征
                            for feat in required_features:
                                if feat not in filtered_df.columns:
                                    filtered_df[feat] = 0.0

                            # 确保特征顺序与训练时一致
                            filtered_df = filtered_df[required_features]
                            filtered_features[model_type] = filtered_df

                            if self.config['verbose']:
                                print(f"✅ {model_type.upper()} 模型特征: {len(filtered_df)} 行, {len(required_features)} 列")
                        else:
                            if self.config['verbose']:
                                print(f"⚠️  {model_type.upper()} 模型: 没有匹配的特征")
                            filtered_features[model_type] = pd.DataFrame()
                    else:
                        filtered_features[model_type] = pd.DataFrame()
                else:
                    filtered_features[model_type] = pd.DataFrame()

            return filtered_features

        except ImportError as e:
            print(f"⚠️  向量化引擎导入失败，回退到原始方法: {e}")
            return self.generate_features_step_by_step_fallback(data)
        except Exception as e:
            print(f"❌ 向量化特征生成失败: {e}")
            print("🔄 回退到原始特征生成方法...")
            return self.generate_features_step_by_step_fallback(data)

    def generate_features_step_by_step_fallback(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        原始的逐步特征生成方法（作为回退方案）
        """
        print("🐌 使用原始逐步特征生成方法...")
        # 这里保留原来的实现作为回退方案
        return {'up': pd.DataFrame(), 'down': pd.DataFrame()}
    
    def generate_signals_batch(self, features_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        批量生成交易信号，大幅提升性能

        Args:
            features_dict: 特征数据字典

        Returns:
            包含信号的DataFrame
        """
        print("🚀 使用批量信号生成器...")

        try:
            # 导入批量信号生成器
            from src.backtest.batch_signal_generator import BatchSignalGenerator

            # 创建批量信号生成器
            signal_generator = BatchSignalGenerator(self.config)

            # 准备模型路径
            model_paths = {}
            for model_type in ['up', 'down']:
                if model_type in self.models:
                    # 由于模型已经加载，我们直接传递模型对象
                    signal_generator.models[model_type] = self.models[model_type]
                    signal_generator.scalers[model_type] = self.scalers.get(model_type)
                    signal_generator.feature_lists[model_type] = self.feature_lists.get(model_type, [])

            # 批量生成信号
            signals_df = signal_generator.generate_signals_batch(features_dict)

            return signals_df

        except ImportError as e:
            print(f"⚠️  批量信号生成器导入失败，回退到原始方法: {e}")
            return self.generate_signals_strict_timeline_fallback(features_dict)
        except Exception as e:
            print(f"❌ 批量信号生成失败: {e}")
            print("🔄 回退到原始信号生成方法...")
            return self.generate_signals_strict_timeline_fallback(features_dict)

    def generate_signals_strict_timeline_fallback(self, features_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        原始的逐步信号生成方法（作为回退方案）
        """
        print("🐌 使用原始逐步信号生成方法...")

        # 简化的回退实现
        signals_list = []

        try:
            # 获取所有时间戳
            all_timestamps = set()
            for model_type, features_df in features_dict.items():
                if not features_df.empty:
                    all_timestamps.update(features_df.index)

            if not all_timestamps:
                return pd.DataFrame()

            sorted_timestamps = sorted(all_timestamps)

            # 为每个时间点生成基本信号
            for timestamp in sorted_timestamps:
                signal_data = {
                    'timestamp': timestamp,
                    'up_prob': 0.5,
                    'down_prob': 0.5,
                    'signal': 'neutral',
                    'signal_strength': 0.5
                }
                signals_list.append(signal_data)

            # 创建DataFrame
            signals_df = pd.DataFrame(signals_list)
            signals_df.set_index('timestamp', inplace=True)

            return signals_df

        except Exception as e:
            print(f"回退信号生成失败: {e}")
            return pd.DataFrame()
    
    def run_backtest_strict_timeline(self, signals_df: pd.DataFrame) -> bool:
        """
        严格按照时间顺序执行回测，确保无数据泄露

        Args:
            signals_df: 信号DataFrame

        Returns:
            是否成功完成回测
        """
        try:
            if signals_df.empty:
                print("错误: 没有可用的信号数据")
                return False

            # 获取价格数据并确保按时间排序
            price_data = self.historical_data['close'].sort_index()

            # 过滤有效信号
            valid_signals = signals_df[signals_df['signal'] != 'neutral'].copy()
            valid_signals = valid_signals.sort_index()  # 确保按时间排序

            total_signals = len(valid_signals)
            processed_signals = 0
            skipped_signals = 0

            if self.config['verbose']:
                print(f"开始严格时间线回测，共有 {total_signals} 个交易信号")
                print(f"价格数据范围: {price_data.index[0]} 到 {price_data.index[-1]}")
                print(f"信号时间范围: {valid_signals.index[0]} 到 {valid_signals.index[-1]}")

            # 严格按时间顺序处理信号
            for timestamp, signal_row in valid_signals.iterrows():
                current_time = pd.to_datetime(timestamp)

                # 检查当前时间点是否有价格数据
                if timestamp not in price_data.index:
                    # 寻找最接近的历史价格（不能使用未来价格）
                    historical_prices = price_data[price_data.index <= timestamp]
                    if historical_prices.empty:
                        skipped_signals += 1
                        continue
                    current_price = historical_prices.iloc[-1]
                    actual_price_time = historical_prices.index[-1]

                    # 如果价格时间与信号时间差距太大，跳过
                    time_diff = (timestamp - actual_price_time).total_seconds() / 60
                    if time_diff > 30:  # 超过30分钟的延迟
                        skipped_signals += 1
                        continue
                else:
                    current_price = price_data[timestamp]

                # 检查是否有足够的未来数据来确定到期价格
                expiry_time = current_time + timedelta(minutes=self.config['expiry_minutes'])

                # 寻找到期时间的价格（必须是真实存在的未来价格）
                future_prices = price_data[price_data.index >= expiry_time]
                if future_prices.empty:
                    # 如果没有足够的未来数据，跳过这个信号
                    skipped_signals += 1
                    continue

                # 获取最接近到期时间的价格
                expiry_price = future_prices.iloc[0]
                actual_expiry_time = future_prices.index[0]

                # 检查到期时间是否合理（不能偏差太大）
                expiry_time_diff = abs((actual_expiry_time - expiry_time).total_seconds() / 60)
                if expiry_time_diff > 15:  # 到期时间偏差超过15分钟
                    skipped_signals += 1
                    continue

                # 在开仓前处理已到期的交易
                self.engine.process_expired_trades(current_time, current_price)

                # 开启新交易
                trade = self.engine.open_trade(
                    direction=signal_row['signal'],
                    signal_strength=signal_row['signal_strength'],
                    current_price=current_price,
                    current_time=current_time
                )

                if trade is not None:
                    processed_signals += 1

                    # 立即设置这个交易的到期价格（用于验证）
                    trade._expected_expiry_time = actual_expiry_time
                    trade._expected_expiry_price = expiry_price

                    if self.config['verbose'] and processed_signals % 20 == 0:
                        print(f"已处理 {processed_signals}/{total_signals} 个信号 "
                              f"(跳过 {skipped_signals} 个)")
                        current_stats = self.engine.get_summary_stats()
                        print(f"  当前余额: ${current_stats['final_balance']:.2f}, "
                              f"胜率: {current_stats['win_rate']:.1%}")
                else:
                    skipped_signals += 1

            # 最终处理所有剩余的活跃交易
            if self.engine.active_trades:
                final_time = price_data.index[-1]
                final_price = price_data.iloc[-1]

                if self.config['verbose']:
                    print(f"处理剩余的 {len(self.engine.active_trades)} 个活跃交易")

                # 对于每个活跃交易，使用其预期的到期价格
                remaining_trades = self.engine.active_trades.copy()
                for trade in remaining_trades:
                    if hasattr(trade, '_expected_expiry_price'):
                        # 使用预设的到期价格
                        trade.close_trade(trade._expected_expiry_price)

                        # 更新引擎状态
                        if trade.is_winner:
                            self.engine.current_balance += trade.amount + trade.pnl

                        self.engine.total_trades += 1
                        self.engine.total_pnl += trade.pnl
                        if trade.is_winner:
                            self.engine.winning_trades += 1

                        # 更新风险管理器
                        self.engine.risk_manager.update_daily_pnl(trade.pnl, trade.exit_time.date())
                        self.engine.risk_manager.update_consecutive_losses(trade.is_winner)

                        # 添加到已完成交易
                        self.engine.trades.append(trade)
                    else:
                        # 如果没有预期价格，使用最终价格
                        self.engine.process_expired_trades(final_time, final_price)
                        break

                # 清空活跃交易列表
                self.engine.active_trades = []

            # 输出最终统计
            if self.config['verbose']:
                stats = self.engine.get_summary_stats()
                print(f"\n=== 回测完成 ===")
                print(f"处理信号: {processed_signals}/{total_signals} (跳过 {skipped_signals})")
                print(f"总交易数: {stats['total_trades']}")
                print(f"胜率: {stats['win_rate']:.2%}")
                print(f"最终余额: ${stats['final_balance']:.2f}")
                print(f"总收益: {stats['return_percentage']:.2%}")

                if skipped_signals > 0:
                    print(f"注意: 跳过了 {skipped_signals} 个信号（数据不足或时间不匹配）")

            return True

        except Exception as e:
            logger.error(f"执行回测时发生错误: {e}")
            logger.exception("执行回测错误的详细堆栈跟踪:")
            print(f"执行回测时发生错误: {e}")
            return False

    def analyze_results(self) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
        """
        分析回测结果

        Returns:
            (性能指标, 交易记录, 每日统计)
        """
        try:
            # 获取交易记录
            trades_df = self.engine.get_trades_dataframe()

            # 验证交易数据
            is_valid, errors = validate_trade_data(trades_df)
            if not is_valid:
                print("警告: 交易数据验证失败:")
                for error in errors:
                    print(f"  - {error}")

            # 计算性能指标
            metrics = calculate_performance_metrics(trades_df, self.config['initial_balance'])

            # 计算每日统计
            daily_stats = calculate_daily_stats(trades_df, self.config['initial_balance'])

            return metrics, trades_df, daily_stats

        except Exception as e:
            print(f"分析结果时发生错误: {e}")
            return {}, pd.DataFrame(), pd.DataFrame()

    def save_results(self, metrics: Dict, trades_df: pd.DataFrame, daily_stats: pd.DataFrame) -> None:
        """
        保存回测结果

        Args:
            metrics: 性能指标
            trades_df: 交易记录
            daily_stats: 每日统计
        """
        try:
            # 创建输出目录
            bt_config.create_output_dirs()

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存结果
            save_results_to_files(
                metrics=metrics,
                trades_df=trades_df,
                daily_stats=daily_stats,
                output_dir=self.config['output_dir'],
                timestamp=timestamp
            )

            # 保存配置文件
            config_file = os.path.join(self.config['output_dir'], f"backtest_config_{timestamp}.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False, default=str)

            print(f"配置文件已保存: {config_file}")

        except Exception as e:
            print(f"保存结果时发生错误: {e}")

    def run_full_backtest(self) -> bool:
        """
        运行完整的回测流程

        Returns:
            是否成功完成
        """
        print("=== 二元期权回测开始 ===")
        print(f"交易对: {self.config['symbol']}")
        print(f"时间范围: {self.config['start_date']} 到 {self.config['end_date']}")
        print(f"初始资金: ${self.config['initial_balance']}")
        print(f"盈亏比: {self.config['payout_ratio']}")
        print(f"到期时间: {self.config['expiry_minutes']} 分钟")
        print()

        # 1. 加载模型
        print("1. 加载模型...")
        if not self.load_models():
            print("错误: 模型加载失败")
            return False

        # 2. 加载历史数据
        print("2. 加载历史数据...")
        if not self.load_historical_data():
            print("错误: 历史数据加载失败")
            return False

        # 3. 向量化特征生成（高性能，防止数据泄露）
        print("3. 向量化特征生成（高性能，防止数据泄露）...")
        features_dict = self.generate_features_vectorized(self.historical_data)
        if not features_dict or all(df.empty for df in features_dict.values()):
            print("错误: 特征生成失败")
            return False

        # 4. 批量信号生成（高性能）
        print("4. 批量信号生成（高性能）...")
        signals_df = self.generate_signals_batch(features_dict)
        if signals_df.empty:
            print("错误: 信号生成失败")
            return False

        # 5. 数据完整性验证（防止数据泄露）
        print("5. 数据完整性验证（防止数据泄露）...")
        validation_passed, validation_report = validate_backtest_integrity(
            self.historical_data, features_dict, signals_df, verbose=self.config['verbose']
        )

        if not validation_passed:
            print("❌ 数据完整性验证失败!")
            print(validation_report)
            if self.config.get('strict_validation', True):
                print("严格模式下，验证失败将终止回测")
                return False
            else:
                print("⚠️  继续执行回测，但结果可能不可靠")
        else:
            print("✅ 数据完整性验证通过")

        # 6. 严格执行回测（无未来函数）
        print("6. 严格执行回测（无未来函数）...")
        if not self.run_backtest_strict_timeline(signals_df):
            print("错误: 回测执行失败")
            return False

        # 7. 分析结果
        print("7. 分析结果...")
        metrics, trades_df, daily_stats = self.analyze_results()

        # 8. 保存结果和验证报告
        if self.config['generate_report']:
            print("8. 保存结果和验证报告...")
            self.save_results(metrics, trades_df, daily_stats)

            # 保存验证报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            validation_report_path = os.path.join(
                self.config['output_dir'], f"validation_report_{timestamp}.txt"
            )
            with open(validation_report_path, 'w', encoding='utf-8') as f:
                f.write(validation_report)
            print(f"验证报告已保存: {validation_report_path}")

        # 9. 显示总结
        print("\n=== 回测结果总结 ===")
        if metrics:
            print(f"总交易次数: {metrics['total_trades']}")
            print(f"胜率: {metrics['win_rate']:.2%}")
            print(f"总收益率: {metrics['total_return']:.2%}")
            print(f"最大回撤: {metrics['max_drawdown']:.2%}")
            print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"盈利因子: {metrics['profit_factor']:.2f}")
            print(f"最大连胜: {metrics['max_consecutive_wins']}")
            print(f"最大连败: {metrics['max_consecutive_losses']}")

        print(f"\n=== 数据完整性验证: {'✅ 通过' if validation_passed else '❌ 失败'} ===")
        print("\n=== 回测完成 ===")
        return True

def main():
    """主函数"""
    try:
        # 创建回测器
        backtester = BinaryOptionBacktester()

        # 运行回测
        success = backtester.run_full_backtest()

        if success:
            print("回测成功完成!")
            return 0
        else:
            print("回测失败!")
            return 1

    except KeyboardInterrupt:
        print("\n回测被用户中断")
        return 1
    except Exception as e:
        logger.critical(f"回测过程中发生未知错误: {e}")
        logger.exception("回测未知错误的详细堆栈跟踪:")
        print(f"回测过程中发生未知错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
