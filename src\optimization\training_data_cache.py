#!/usr/bin/env python3
"""
训练数据缓存器 - 优化训练过程中的数据获取和特征计算
避免重复的数据获取、特征工程和MTFA处理
"""

import os
import sys
import time
import hashlib
import pickle
import threading
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)

class TrainingDataCache:
    """训练数据缓存器"""
    
    def __init__(self, 
                 max_memory_mb: int = 1024,
                 cache_ttl_hours: int = 24,
                 enable_disk_cache: bool = True,
                 cache_dir: str = "./cache/training_data"):
        """
        初始化训练数据缓存器
        
        Args:
            max_memory_mb: 最大内存缓存大小（MB）
            cache_ttl_hours: 缓存生存时间（小时）
            enable_disk_cache: 是否启用磁盘缓存
            cache_dir: 磁盘缓存目录
        """
        self.max_memory_mb = max_memory_mb
        self.cache_ttl_hours = cache_ttl_hours
        self.enable_disk_cache = enable_disk_cache
        self.cache_dir = cache_dir
        
        # 内存缓存
        self._memory_cache = {}
        self._cache_metadata = {}  # 存储缓存元数据（时间戳、大小等）
        self._access_times = {}    # 记录访问时间（用于LRU）
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'disk_hits': 0,
            'disk_misses': 0,
            'total_memory_mb': 0
        }
        
        # 创建缓存目录
        if self.enable_disk_cache:
            os.makedirs(self.cache_dir, exist_ok=True)
        
        logger.info(f"训练数据缓存器初始化完成 - 内存限制: {max_memory_mb}MB, "
                   f"TTL: {cache_ttl_hours}小时, 磁盘缓存: {enable_disk_cache}")
    
    def _generate_cache_key(self, 
                           symbol: str,
                           interval: str,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           config_hash: Optional[str] = None,
                           data_type: str = "raw") -> str:
        """
        生成缓存键
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            start_time: 开始时间
            end_time: 结束时间
            config_hash: 配置哈希
            data_type: 数据类型（raw, features, mtfa等）
            
        Returns:
            缓存键字符串
        """
        key_parts = [symbol, interval, data_type]
        
        if start_time:
            key_parts.append(start_time.strftime('%Y%m%d_%H%M%S'))
        if end_time:
            key_parts.append(end_time.strftime('%Y%m%d_%H%M%S'))
        if config_hash:
            key_parts.append(config_hash[:8])  # 只取前8位
            
        return "_".join(key_parts)
    
    def _calculate_config_hash(self, config: Dict[str, Any]) -> str:
        """计算配置哈希值"""
        # 只包含影响特征计算的关键配置
        relevant_keys = [
            'enable_mtfa', 'mtfa_timeframes', 'enable_higher_order_features',
            'enable_interaction_features', 'enable_derivatives_divergence_features',
            'rsi_period', 'macd_fast', 'macd_slow', 'bb_period'
        ]
        
        relevant_config = {k: config.get(k) for k in relevant_keys if k in config}
        config_str = str(sorted(relevant_config.items()))
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _estimate_data_size_mb(self, data: Any) -> float:
        """估算数据大小（MB）"""
        try:
            if isinstance(data, pd.DataFrame):
                return data.memory_usage(deep=True).sum() / (1024 * 1024)
            elif isinstance(data, (list, tuple)):
                # 估算列表/元组大小
                return sum(self._estimate_data_size_mb(item) for item in data) if data else 0.001
            else:
                # 使用pickle估算其他对象大小
                return len(pickle.dumps(data)) / (1024 * 1024)
        except Exception:
            return 0.1  # 默认估算值
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        ttl_seconds = self.cache_ttl_hours * 3600
        expired_keys = []
        
        for key, metadata in self._cache_metadata.items():
            if current_time - metadata['timestamp'] > ttl_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_from_memory_cache(key)
            logger.debug(f"清理过期缓存: {key}")
    
    def _enforce_memory_limit(self):
        """强制执行内存限制"""
        while self._stats['total_memory_mb'] > self.max_memory_mb:
            # 找到最久未访问的缓存项
            if not self._access_times:
                break
                
            oldest_key = min(self._access_times.keys(), 
                           key=lambda k: self._access_times[k])
            self._remove_from_memory_cache(oldest_key)
            self._stats['evictions'] += 1
            logger.debug(f"内存限制清理缓存: {oldest_key}")
    
    def _remove_from_memory_cache(self, key: str):
        """从内存缓存中移除项目"""
        if key in self._memory_cache:
            # 更新内存统计
            if key in self._cache_metadata:
                self._stats['total_memory_mb'] -= self._cache_metadata[key]['size_mb']
            
            # 删除缓存项
            del self._memory_cache[key]
            self._cache_metadata.pop(key, None)
            self._access_times.pop(key, None)
    
    def get_cached_data(self, 
                       symbol: str,
                       interval: str,
                       config: Dict[str, Any],
                       data_type: str = "features",
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None) -> Optional[Any]:
        """
        获取缓存的数据
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            config: 配置字典
            data_type: 数据类型
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            缓存的数据，如果不存在则返回None
        """
        with self._lock:
            # 清理过期缓存
            self._cleanup_expired_cache()
            
            # 生成缓存键
            config_hash = self._calculate_config_hash(config)
            cache_key = self._generate_cache_key(
                symbol, interval, start_time, end_time, config_hash, data_type
            )
            
            # 检查内存缓存
            if cache_key in self._memory_cache:
                self._access_times[cache_key] = time.time()
                self._stats['hits'] += 1
                logger.debug(f"内存缓存命中: {cache_key}")
                return self._memory_cache[cache_key]
            
            # 检查磁盘缓存
            if self.enable_disk_cache:
                disk_path = os.path.join(self.cache_dir, f"{cache_key}.pkl")
                if os.path.exists(disk_path):
                    try:
                        with open(disk_path, 'rb') as f:
                            data = pickle.load(f)
                        
                        # 将数据加载到内存缓存
                        self._store_in_memory_cache(cache_key, data)
                        self._stats['disk_hits'] += 1
                        logger.debug(f"磁盘缓存命中: {cache_key}")
                        return data
                    except Exception as e:
                        logger.warning(f"磁盘缓存读取失败: {cache_key}, 错误: {e}")
                        # 删除损坏的缓存文件
                        try:
                            os.remove(disk_path)
                        except:
                            pass
            
            # 缓存未命中
            self._stats['misses'] += 1
            if self.enable_disk_cache:
                self._stats['disk_misses'] += 1
            
            return None
    
    def store_data(self, 
                   symbol: str,
                   interval: str,
                   config: Dict[str, Any],
                   data: Any,
                   data_type: str = "features",
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None):
        """
        存储数据到缓存
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            config: 配置字典
            data: 要缓存的数据
            data_type: 数据类型
            start_time: 开始时间
            end_time: 结束时间
        """
        with self._lock:
            # 生成缓存键
            config_hash = self._calculate_config_hash(config)
            cache_key = self._generate_cache_key(
                symbol, interval, start_time, end_time, config_hash, data_type
            )
            
            # 存储到内存缓存
            self._store_in_memory_cache(cache_key, data)
            
            # 存储到磁盘缓存
            if self.enable_disk_cache:
                self._store_to_disk_cache(cache_key, data)
            
            logger.debug(f"数据已缓存: {cache_key}")
    
    def _store_in_memory_cache(self, cache_key: str, data: Any):
        """存储数据到内存缓存"""
        # 估算数据大小
        data_size_mb = self._estimate_data_size_mb(data)
        
        # 存储数据
        self._memory_cache[cache_key] = data
        self._cache_metadata[cache_key] = {
            'timestamp': time.time(),
            'size_mb': data_size_mb
        }
        self._access_times[cache_key] = time.time()
        self._stats['total_memory_mb'] += data_size_mb
        
        # 强制执行内存限制
        self._enforce_memory_limit()
    
    def _store_to_disk_cache(self, cache_key: str, data: Any):
        """存储数据到磁盘缓存"""
        try:
            disk_path = os.path.join(self.cache_dir, f"{cache_key}.pkl")
            with open(disk_path, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.warning(f"磁盘缓存存储失败: {cache_key}, 错误: {e}")
    
    def clear_cache(self, data_type: Optional[str] = None):
        """清空缓存"""
        with self._lock:
            if data_type:
                # 清空特定类型的缓存
                keys_to_remove = [k for k in self._memory_cache.keys() 
                                if data_type in k]
                for key in keys_to_remove:
                    self._remove_from_memory_cache(key)
                logger.info(f"已清空 {data_type} 类型缓存，共 {len(keys_to_remove)} 项")
            else:
                # 清空所有缓存
                self._memory_cache.clear()
                self._cache_metadata.clear()
                self._access_times.clear()
                self._stats['total_memory_mb'] = 0
                logger.info("已清空所有内存缓存")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                **self._stats,
                'cache_items': len(self._memory_cache),
                'hit_rate': self._stats['hits'] / max(1, self._stats['hits'] + self._stats['misses']),
                'disk_hit_rate': self._stats['disk_hits'] / max(1, self._stats['disk_hits'] + self._stats['disk_misses']) if self.enable_disk_cache else 0
            }
    
    def print_cache_report(self):
        """打印缓存报告"""
        stats = self.get_cache_stats()
        print(f"\n📊 训练数据缓存统计报告")
        print(f"=" * 50)
        print(f"缓存项数量: {stats['cache_items']}")
        print(f"内存使用: {stats['total_memory_mb']:.1f} MB / {self.max_memory_mb} MB")
        print(f"命中率: {stats['hit_rate']:.1%}")
        if self.enable_disk_cache:
            print(f"磁盘命中率: {stats['disk_hit_rate']:.1%}")
        print(f"缓存命中: {stats['hits']}")
        print(f"缓存未命中: {stats['misses']}")
        print(f"缓存淘汰: {stats['evictions']}")


# 全局缓存实例
_global_training_cache = None

def get_training_data_cache() -> TrainingDataCache:
    """获取全局训练数据缓存实例"""
    global _global_training_cache
    if _global_training_cache is None:
        _global_training_cache = TrainingDataCache()
    return _global_training_cache
