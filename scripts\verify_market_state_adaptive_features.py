#!/usr/bin/env python3
"""
验证市场状态自适应特征工程配置
确认强化配置已正确应用
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_market_state_config():
    """验证市场状态自适应特征配置"""
    logger.info("🔍 验证市场状态自适应特征工程配置...")
    
    try:
        # 获取配置
        market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
        
        if not market_config:
            logger.error("❌ MARKET_STATE_ADAPTIVE_FEATURES_CONFIG 配置不存在")
            return False
        
        # 检查是否启用
        enabled = market_config.get('enable', False)
        logger.info(f"启用状态: {enabled}")
        
        if not enabled:
            logger.warning("⚠️ 市场状态自适应特征未启用")
            return False
        
        # 检查交互指标
        indicators = market_config.get('market_state_interaction_indicators', [])
        logger.info(f"交互指标数量: {len(indicators)}")
        
        # 检查SHAP重要特征是否包含
        shap_features = [
            'meta_prob_diff_up_vs_down',
            'meta_prob_sum_up_down', 
            'global_ema_short',
            'global_mdi',
            'global_ema_long'
        ]
        
        found_shap_features = []
        for feature in shap_features:
            if feature in indicators:
                found_shap_features.append(feature)
        
        logger.info(f"包含的SHAP重要特征: {len(found_shap_features)}/{len(shap_features)}")
        for feature in found_shap_features:
            logger.info(f"  ✅ {feature}")
        
        missing_features = set(shap_features) - set(found_shap_features)
        if missing_features:
            logger.warning(f"  ⚠️ 缺失的SHAP特征: {missing_features}")
        
        # 检查市场状态类型
        states = market_config.get('market_state_interaction_states', [])
        logger.info(f"市场状态类型数量: {len(states)}")
        
        # 检查新增的复合状态
        compound_states = [
            'bull_momentum', 'bear_momentum', 'consolidation_breakout',
            'trend_exhaustion', 'reversal_signal', 'accumulation_phase',
            'distribution_phase'
        ]
        
        found_compound_states = []
        for state in compound_states:
            if state in states:
                found_compound_states.append(state)
        
        logger.info(f"包含的复合状态: {len(found_compound_states)}/{len(compound_states)}")
        for state in found_compound_states:
            logger.info(f"  ✅ {state}")
        
        # 检查阈值配置
        thresholds = [
            'market_state_high_vol_threshold',
            'market_state_extreme_vol_threshold', 
            'momentum_strength_threshold',
            'reversal_signal_threshold'
        ]
        
        found_thresholds = []
        for threshold in thresholds:
            if threshold in market_config:
                value = market_config[threshold]
                found_thresholds.append(threshold)
                logger.info(f"  ✅ {threshold}: {value}")
        
        logger.info(f"配置的阈值参数: {len(found_thresholds)}/{len(thresholds)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证配置失败: {e}")
        return False

def estimate_feature_generation_impact():
    """估算特征生成的影响"""
    logger.info("📊 估算市场状态自适应特征生成影响...")
    
    try:
        market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
        
        indicators = market_config.get('market_state_interaction_indicators', [])
        states = market_config.get('market_state_interaction_states', [])
        
        # 计算将生成的交互特征数量
        total_interactions = len(indicators) * len(states)
        
        logger.info(f"预计生成的交互特征数量: {total_interactions}")
        logger.info(f"  - 交互指标: {len(indicators)} 个")
        logger.info(f"  - 市场状态: {len(states)} 个")
        logger.info(f"  - 交互组合: {len(indicators)} × {len(states)} = {total_interactions}")
        
        # 示例特征名称
        example_features = []
        for i, indicator in enumerate(indicators[:3]):  # 只显示前3个作为示例
            for j, state in enumerate(states[:2]):  # 只显示前2个作为示例
                feature_name = f"{indicator}_IN_{state}"
                example_features.append(feature_name)
        
        logger.info("示例生成的特征名称:")
        for feature in example_features:
            logger.info(f"  📈 {feature}")
        
        # 特别关注SHAP重要特征的交互
        shap_features = ['meta_prob_diff_up_vs_down', 'global_ema_short', 'global_mdi']
        key_states = ['strong_uptrend', 'bull_momentum', 'high_certainty']
        
        logger.info("\n🎯 关键SHAP特征的市场状态交互示例:")
        for feature in shap_features:
            if feature in indicators:
                for state in key_states:
                    if state in states:
                        interaction_feature = f"{feature}_IN_{state}"
                        logger.info(f"  🚀 {interaction_feature}")
        
        return total_interactions
        
    except Exception as e:
        logger.error(f"❌ 估算影响失败: {e}")
        return 0

def provide_implementation_guidance():
    """提供实施指导"""
    logger.info("📋 市场状态自适应特征工程实施指导:")
    
    guidance = [
        "1. 配置验证: ✅ 已完成强化配置",
        "2. 特征生成: 重新训练模型时将自动生成交互特征",
        "3. 预期效果: 模型将学会在不同市场制度下使用不同决策权重",
        "4. 关键优势:",
        "   - 自动生成如 'meta_prob_diff_up_vs_down_IN_strong_uptrend' 的特征",
        "   - 模型能感知当前市场制度并自适应调整",
        "   - 基于SHAP证明有效的特征进行交互",
        "5. 下一步: 重新训练基础模型和元模型以应用新特征",
        "6. 监控指标: 关注Class_1 recall是否从23.1%提升至≥50%"
    ]
    
    for item in guidance:
        logger.info(f"  {item}")

def main():
    """主函数"""
    logger.info("🎯 开始验证市场状态自适应特征工程强化配置...")
    
    try:
        # 1. 验证配置
        config_ok = verify_market_state_config()
        
        # 2. 估算影响
        feature_count = estimate_feature_generation_impact()
        
        # 3. 提供指导
        provide_implementation_guidance()
        
        if config_ok and feature_count > 0:
            logger.info("🎉 市场状态自适应特征工程强化配置验证成功！")
            logger.info(f"📊 预计将生成 {feature_count} 个交互特征")
            logger.info("🚀 这将显著提升模型的市场制度感知能力")
            return True
        else:
            logger.warning("⚠️ 配置验证部分失败，请检查上述错误")
            return False
        
    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 市场状态自适应特征工程强化配置验证成功！")
        print("📋 现在可以重新训练模型以应用新的自适应特征")
    else:
        print("\n❌ 配置验证失败！")
