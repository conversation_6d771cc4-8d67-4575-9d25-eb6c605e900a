#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚙️ 统一综合日志系统配置管理器 V1.0
为统一综合日志系统提供集中化配置管理

核心功能：
- 集中化配置管理
- 配置文件自动加载和保存
- 配置验证和默认值
- 环境变量支持
- 配置热更新
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
import logging
from dataclasses import dataclass, asdict, field


@dataclass
class LoggerConfig:
    """日志系统配置"""
    base_log_dir: str = "comprehensive_logs"
    queue_maxsize: int = 1000
    batch_size: int = 10
    flush_interval: float = 1.0
    auto_start: bool = True
    enable_compression: bool = False
    max_file_size_mb: int = 100
    backup_count: int = 5


@dataclass
class StorageConfig:
    """存储配置"""
    enable_auto_cleanup: bool = True
    cleanup_days: int = 30
    enable_backup: bool = True
    backup_interval_hours: int = 24
    compression_level: int = 6
    enable_encryption: bool = False
    encryption_key: str = ""


@dataclass
class AnalysisConfig:
    """分析配置"""
    enable_realtime_analysis: bool = True
    analysis_interval_minutes: int = 30
    failure_analysis_threshold: int = 5
    enable_charts: bool = True
    chart_format: str = "png"
    chart_dpi: int = 300


@dataclass
class MonitoringConfig:
    """监控配置"""
    enable_monitoring: bool = False
    check_interval_seconds: int = 30
    max_drawdown_threshold: float = -100.0
    consecutive_losses_threshold: int = 5
    win_rate_threshold: float = 30.0
    daily_loss_threshold: float = -200.0
    enable_email_alerts: bool = False
    email_smtp_server: str = "smtp.gmail.com"
    email_smtp_port: int = 587
    email_username: str = ""
    email_password: str = ""
    email_recipients: list = field(default_factory=list)


@dataclass
class PerformanceConfig:
    """性能配置"""
    enable_memory_optimization: bool = True
    max_memory_usage_mb: int = 512
    enable_parallel_processing: bool = True
    max_worker_threads: int = 4
    enable_caching: bool = True
    cache_size_mb: int = 64


@dataclass
class ComprehensiveLoggerConfig:
    """综合日志系统完整配置"""
    logger: LoggerConfig = field(default_factory=LoggerConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    analysis: AnalysisConfig = field(default_factory=AnalysisConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    
    # 元数据
    version: str = "1.0"
    created_at: str = ""
    updated_at: str = ""


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "comprehensive_logger_config.yaml"):
        self.config_file = Path(config_file)
        self.logger = logging.getLogger(f"{__name__}.ConfigManager")
        
        # 当前配置
        self.config: ComprehensiveLoggerConfig = ComprehensiveLoggerConfig()
        
        # 加载配置
        self.load_config()
        
        self.logger.info(f"配置管理器初始化完成: {config_file}")
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                # 根据文件扩展名选择加载方式
                if self.config_file.suffix.lower() == '.json':
                    config_data = self._load_json_config()
                elif self.config_file.suffix.lower() in ['.yaml', '.yml']:
                    config_data = self._load_yaml_config()
                else:
                    self.logger.warning(f"不支持的配置文件格式: {self.config_file.suffix}")
                    return False
                
                # 更新配置
                self._update_config_from_dict(config_data)
                
                self.logger.info(f"配置文件加载成功: {self.config_file}")
                return True
            else:
                # 创建默认配置文件
                self.save_config()
                self.logger.info(f"创建默认配置文件: {self.config_file}")
                return True
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def _load_json_config(self) -> Dict[str, Any]:
        """加载JSON配置文件"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_yaml_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        # 更新各个配置部分
        if 'logger' in config_data:
            self._update_dataclass_from_dict(self.config.logger, config_data['logger'])
        
        if 'storage' in config_data:
            self._update_dataclass_from_dict(self.config.storage, config_data['storage'])
        
        if 'analysis' in config_data:
            self._update_dataclass_from_dict(self.config.analysis, config_data['analysis'])
        
        if 'monitoring' in config_data:
            self._update_dataclass_from_dict(self.config.monitoring, config_data['monitoring'])
        
        if 'performance' in config_data:
            self._update_dataclass_from_dict(self.config.performance, config_data['performance'])
        
        # 更新元数据
        if 'version' in config_data:
            self.config.version = config_data['version']
        if 'created_at' in config_data:
            self.config.created_at = config_data['created_at']
        if 'updated_at' in config_data:
            self.config.updated_at = config_data['updated_at']
    
    def _update_dataclass_from_dict(self, dataclass_obj, data_dict: Dict[str, Any]):
        """从字典更新数据类对象"""
        for key, value in data_dict.items():
            if hasattr(dataclass_obj, key):
                setattr(dataclass_obj, key, value)
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            # 更新时间戳
            from datetime import datetime
            now = datetime.now().isoformat()
            
            if not self.config.created_at:
                self.config.created_at = now
            self.config.updated_at = now
            
            # 转换为字典
            config_dict = asdict(self.config)
            
            # 根据文件扩展名选择保存方式
            if self.config_file.suffix.lower() == '.json':
                self._save_json_config(config_dict)
            elif self.config_file.suffix.lower() in ['.yaml', '.yml']:
                self._save_yaml_config(config_dict)
            else:
                # 默认使用YAML格式
                self.config_file = self.config_file.with_suffix('.yaml')
                self._save_yaml_config(config_dict)
            
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _save_json_config(self, config_dict: Dict[str, Any]):
        """保存JSON配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
    
    def _save_yaml_config(self, config_dict: Dict[str, Any]):
        """保存YAML配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def get_config(self) -> ComprehensiveLoggerConfig:
        """获取当前配置"""
        return self.config
    
    def update_config(self, **kwargs) -> bool:
        """更新配置"""
        try:
            for section, updates in kwargs.items():
                if hasattr(self.config, section):
                    section_config = getattr(self.config, section)
                    self._update_dataclass_from_dict(section_config, updates)
                else:
                    self.logger.warning(f"未知的配置部分: {section}")
            
            return self.save_config()
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            return False
    
    def load_from_env(self):
        """从环境变量加载配置"""
        try:
            # 日志配置
            if os.getenv('LOGGER_BASE_LOG_DIR'):
                self.config.logger.base_log_dir = os.getenv('LOGGER_BASE_LOG_DIR')
            if os.getenv('LOGGER_QUEUE_MAXSIZE'):
                self.config.logger.queue_maxsize = int(os.getenv('LOGGER_QUEUE_MAXSIZE'))
            if os.getenv('LOGGER_BATCH_SIZE'):
                self.config.logger.batch_size = int(os.getenv('LOGGER_BATCH_SIZE'))
            if os.getenv('LOGGER_FLUSH_INTERVAL'):
                self.config.logger.flush_interval = float(os.getenv('LOGGER_FLUSH_INTERVAL'))
            
            # 监控配置
            if os.getenv('MONITORING_ENABLE'):
                self.config.monitoring.enable_monitoring = os.getenv('MONITORING_ENABLE').lower() == 'true'
            if os.getenv('MONITORING_EMAIL_USERNAME'):
                self.config.monitoring.email_username = os.getenv('MONITORING_EMAIL_USERNAME')
            if os.getenv('MONITORING_EMAIL_PASSWORD'):
                self.config.monitoring.email_password = os.getenv('MONITORING_EMAIL_PASSWORD')
            if os.getenv('MONITORING_EMAIL_RECIPIENTS'):
                recipients = os.getenv('MONITORING_EMAIL_RECIPIENTS').split(',')
                self.config.monitoring.email_recipients = [r.strip() for r in recipients]
            
            # 性能配置
            if os.getenv('PERFORMANCE_MAX_MEMORY_MB'):
                self.config.performance.max_memory_usage_mb = int(os.getenv('PERFORMANCE_MAX_MEMORY_MB'))
            if os.getenv('PERFORMANCE_MAX_THREADS'):
                self.config.performance.max_worker_threads = int(os.getenv('PERFORMANCE_MAX_THREADS'))
            
            self.logger.info("环境变量配置加载完成")
            
        except Exception as e:
            self.logger.error(f"从环境变量加载配置失败: {e}")
    
    def validate_config(self) -> Dict[str, List[str]]:
        """验证配置"""
        errors = {
            'logger': [],
            'storage': [],
            'analysis': [],
            'monitoring': [],
            'performance': []
        }
        
        # 验证日志配置
        if self.config.logger.queue_maxsize <= 0:
            errors['logger'].append("queue_maxsize 必须大于 0")
        if self.config.logger.batch_size <= 0:
            errors['logger'].append("batch_size 必须大于 0")
        if self.config.logger.flush_interval <= 0:
            errors['logger'].append("flush_interval 必须大于 0")
        
        # 验证存储配置
        if self.config.storage.cleanup_days <= 0:
            errors['storage'].append("cleanup_days 必须大于 0")
        if self.config.storage.backup_interval_hours <= 0:
            errors['storage'].append("backup_interval_hours 必须大于 0")
        
        # 验证监控配置
        if self.config.monitoring.check_interval_seconds <= 0:
            errors['monitoring'].append("check_interval_seconds 必须大于 0")
        if self.config.monitoring.enable_email_alerts:
            if not self.config.monitoring.email_username:
                errors['monitoring'].append("启用邮件告警时 email_username 不能为空")
            if not self.config.monitoring.email_recipients:
                errors['monitoring'].append("启用邮件告警时 email_recipients 不能为空")
        
        # 验证性能配置
        if self.config.performance.max_memory_usage_mb <= 0:
            errors['performance'].append("max_memory_usage_mb 必须大于 0")
        if self.config.performance.max_worker_threads <= 0:
            errors['performance'].append("max_worker_threads 必须大于 0")
        
        # 移除空的错误列表
        errors = {k: v for k, v in errors.items() if v}
        
        return errors
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config = ComprehensiveLoggerConfig()
        self.save_config()
        self.logger.info("配置已重置为默认值")
    
    def export_config(self, output_file: str) -> bool:
        """导出配置到文件"""
        try:
            output_path = Path(output_file)
            config_dict = asdict(self.config)
            
            if output_path.suffix.lower() == '.json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, ensure_ascii=False, indent=2)
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            self.logger.info(f"配置已导出到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("=" * 60)
        print("⚙️ 统一综合日志系统配置摘要")
        print("=" * 60)
        
        print(f"\n📝 日志配置:")
        print(f"  基础目录: {self.config.logger.base_log_dir}")
        print(f"  队列大小: {self.config.logger.queue_maxsize}")
        print(f"  批量大小: {self.config.logger.batch_size}")
        print(f"  刷新间隔: {self.config.logger.flush_interval}s")
        
        print(f"\n💾 存储配置:")
        print(f"  自动清理: {self.config.storage.enable_auto_cleanup}")
        print(f"  保留天数: {self.config.storage.cleanup_days}")
        print(f"  启用备份: {self.config.storage.enable_backup}")
        
        print(f"\n📊 分析配置:")
        print(f"  实时分析: {self.config.analysis.enable_realtime_analysis}")
        print(f"  分析间隔: {self.config.analysis.analysis_interval_minutes}分钟")
        print(f"  启用图表: {self.config.analysis.enable_charts}")
        
        print(f"\n📡 监控配置:")
        print(f"  启用监控: {self.config.monitoring.enable_monitoring}")
        print(f"  检查间隔: {self.config.monitoring.check_interval_seconds}秒")
        print(f"  邮件告警: {self.config.monitoring.enable_email_alerts}")
        
        print(f"\n⚡ 性能配置:")
        print(f"  内存优化: {self.config.performance.enable_memory_optimization}")
        print(f"  最大内存: {self.config.performance.max_memory_usage_mb}MB")
        print(f"  并行处理: {self.config.performance.enable_parallel_processing}")
        
        print("=" * 60)


# 全局配置管理器实例
_global_config_manager = None


def get_config_manager(config_file: str = "comprehensive_logger_config.yaml") -> ConfigManager:
    """获取全局配置管理器实例（单例模式）"""
    global _global_config_manager
    
    if _global_config_manager is None:
        _global_config_manager = ConfigManager(config_file)
    
    return _global_config_manager


def get_config() -> ComprehensiveLoggerConfig:
    """获取当前配置"""
    return get_config_manager().get_config()
