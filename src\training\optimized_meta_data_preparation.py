"""
优化的元模型训练数据准备模块

解决用户提到的关键问题：
1. 确保每个基础模型使用各自配置进行特征工程
2. 严格的索引对齐和NaN处理
3. 统一的数据质量保证
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from src.core.enhanced_logger import get_enhanced_logger


class OptimizedMetaDataPreparator:
    """优化的元模型数据准备器"""
    
    def __init__(self, base_models_config: Dict):
        """
        初始化数据准备器
        
        Args:
            base_models_config: 基础模型配置字典
        """
        self.base_models_config = base_models_config
        self.logger = get_enhanced_logger()
        
    def generate_oof_predictions_with_model_specific_features(
        self,
        df_raw_data: pd.DataFrame,
        target_data: pd.Series,
        trained_models_info: Dict
    ) -> pd.DataFrame:
        """
        为每个基础模型使用各自配置生成OOF预测
        
        Args:
            df_raw_data: 原始K线数据（未经特征工程）
            target_data: 目标变量
            trained_models_info: 已训练模型信息
            
        Returns:
            DataFrame: 包含所有基础模型OOF预测的DataFrame
        """
        self.logger.info("🚀 开始生成基础模型OOF预测（使用各自配置）...")
        
        oof_series_dict = {}
        common_index = target_data.index.copy()
        
        # 为每个基础模型生成独立的OOF预测
        for model_name, model_info in trained_models_info.items():
            self.logger.info(f"📊 处理基础模型: {model_name}")
            
            try:
                # 获取模型特定的配置
                model_config = model_info.get('config', {})
                model_dir = model_info.get('model_dir', '')
                
                # 🎯 关键优化：使用模型特定配置进行特征工程
                oof_series = self._generate_single_model_oof_with_config(
                    df_raw_data=df_raw_data,
                    target_data=target_data,
                    model_config=model_config,
                    model_dir=model_dir,
                    model_name=model_name
                )
                
                if oof_series is not None and not oof_series.empty:
                    oof_series_dict[f'oof_{model_name}'] = oof_series
                    # 更新公共索引（取交集）
                    common_index = common_index.intersection(oof_series.index)
                    self.logger.info(f"  ✅ {model_name}: {len(oof_series)} 个OOF预测")
                else:
                    self.logger.warning(f"  ⚠️ {model_name}: OOF生成失败")
                    
            except Exception as e:
                self.logger.error(f"处理模型 {model_name} 时出错: {e}")
                continue
        
        # 🎯 关键优化：使用严格的索引对齐
        return self._align_and_merge_oof_series(oof_series_dict, common_index)
    
    def _generate_single_model_oof_with_config(
        self,
        df_raw_data: pd.DataFrame,
        target_data: pd.Series,
        model_config: Dict,
        model_dir: str,
        model_name: str
    ) -> Optional[pd.Series]:
        """
        为单个模型使用特定配置生成OOF预测
        
        Args:
            df_raw_data: 原始数据
            target_data: 目标变量
            model_config: 模型特定配置
            model_dir: 模型目录
            model_name: 模型名称
            
        Returns:
            Series: OOF预测结果
        """
        try:
            # 1. 使用模型特定配置进行特征工程
            from src.core.unified_feature_engineering import generate_basic_features
            
            X_model_specific = generate_basic_features(
                data=df_raw_data.copy(),
                target_config=model_config
            )
            
            if X_model_specific is None or X_model_specific.empty:
                self.logger.error(f"模型 {model_name} 特征工程失败")
                return None
            
            # 2. 确保索引对齐
            common_idx = X_model_specific.index.intersection(target_data.index)
            X_aligned = X_model_specific.loc[common_idx]
            y_aligned = target_data.loc[common_idx]
            
            # 3. 从已训练模型生成OOF预测
            from src.training.optimized_training_module import generate_oof_predictions_from_trained_models
            
            oof_df = generate_oof_predictions_from_trained_models(
                X_unscaled_df=X_aligned,
                y_series=y_aligned,
                target_config=model_config,
                model_dir=model_dir,
                target_name=model_name
            )
            
            if oof_df is not None and not oof_df.empty:
                # 返回第一列作为OOF预测
                return oof_df.iloc[:, 0]
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"为模型 {model_name} 生成OOF时出错: {e}")
            return None
    
    def _align_and_merge_oof_series(
        self,
        oof_series_dict: Dict[str, pd.Series],
        common_index: pd.Index
    ) -> pd.DataFrame:
        """
        对齐和合并OOF Series
        
        Args:
            oof_series_dict: OOF Series字典
            common_index: 公共索引
            
        Returns:
            DataFrame: 合并后的OOF预测DataFrame
        """
        self.logger.info("🔧 对齐和合并OOF Series...")
        
        if not oof_series_dict:
            self.logger.error("没有有效的OOF预测数据")
            return pd.DataFrame()
        
        # 对齐所有Series到公共索引
        aligned_series = {}
        for name, series in oof_series_dict.items():
            aligned_series[name] = series.loc[common_index]
        
        # 🎯 关键优化：使用inner join确保严格索引对齐
        oof_df = pd.concat(aligned_series, axis=1, join='inner')
        
        self.logger.info(f"📊 合并结果: {oof_df.shape[0]} 样本, {oof_df.shape[1]} 个OOF特征")
        self.logger.info(f"🎯 OOF特征列表: {list(oof_df.columns)}")
        
        # 数据质量检查
        self._validate_oof_data_quality(oof_df)
        
        return oof_df
    
    def _validate_oof_data_quality(self, oof_df: pd.DataFrame) -> None:
        """
        验证OOF数据质量
        
        Args:
            oof_df: OOF预测DataFrame
        """
        self.logger.info("🔍 验证OOF数据质量...")
        
        # 检查NaN值
        nan_counts = oof_df.isnull().sum()
        total_nan = nan_counts.sum()
        
        if total_nan > 0:
            self.logger.warning(f"发现 {total_nan} 个NaN值:")
            for col, count in nan_counts.items():
                if count > 0:
                    ratio = count / len(oof_df)
                    self.logger.warning(f"  {col}: {count} NaN ({ratio:.2%})")
        
        # 检查数据范围
        for col in oof_df.columns:
            col_data = oof_df[col].dropna()
            if len(col_data) > 0:
                min_val, max_val = col_data.min(), col_data.max()
                mean_val = col_data.mean()
                self.logger.info(f"  {col}: 范围[{min_val:.4f}, {max_val:.4f}], 均值{mean_val:.4f}")
        
        # 检查索引连续性
        if not oof_df.index.is_monotonic_increasing:
            self.logger.warning("索引不是单调递增的")
        
        self.logger.info("✅ 数据质量检查完成")


def prepare_meta_training_data_optimized(
    df_raw_data: pd.DataFrame,
    target_data: pd.Series,
    trained_models_info: Dict,
    base_models_config: Dict,
    save_csv_files: bool = True,
    output_dir: str = "models/meta_model_data"
) -> Tuple[pd.DataFrame, pd.Series]:
    """
    优化的元模型训练数据准备函数

    Args:
        df_raw_data: 原始K线数据
        target_data: 目标变量
        trained_models_info: 已训练模型信息
        base_models_config: 基础模型配置
        save_csv_files: 是否保存CSV文件
        output_dir: CSV文件保存目录

    Returns:
        Tuple[DataFrame, Series]: (X_meta, y_meta)
    """
    import os
    logger = get_enhanced_logger()
    logger.info("🌟 开始优化的元模型训练数据准备...")

    # 创建数据准备器
    preparator = OptimizedMetaDataPreparator(base_models_config)

    # 生成OOF预测
    oof_df = preparator.generate_oof_predictions_with_model_specific_features(
        df_raw_data=df_raw_data,
        target_data=target_data,
        trained_models_info=trained_models_info
    )

    if oof_df.empty:
        logger.error("OOF预测生成失败")
        return pd.DataFrame(), pd.Series()

    # 🎯 关键修复：使用二分类元目标变量而不是原始三分类目标变量
    logger.info("🔧 创建二分类元目标变量...")

    # 导入目标变量创建函数
    from src.core.data_utils import create_meta_target_variable

    # 为元模型创建二分类目标变量
    # 使用第一个基础模型的配置作为参考
    first_model_config = next(iter(base_models_config.values()))

    # 创建包含价格数据的DataFrame（用于目标变量创建）
    df_with_prices = df_raw_data[['close']].copy()

    # 🚨 修复：为30分钟二元期权创建2周期目标变量
    # 临时修改配置以使用2周期(30分钟)
    meta_config = first_model_config.copy()
    meta_config['prediction_periods'] = 2  # 30分钟 = 2 * 15分钟

    # 创建二分类元目标变量
    df_with_meta_target, meta_target_col = create_meta_target_variable(
        df_with_prices,
        meta_config,  # 使用修正的配置
        meta_target_suffix="_meta_binary_2p"  # 明确标识为2周期
    )

    if df_with_meta_target is None or meta_target_col is None:
        logger.error("二分类元目标变量创建失败")
        return pd.DataFrame(), pd.Series()

    logger.info(f"✅ 二分类元目标变量创建成功: {meta_target_col}")

    # 获取二分类目标变量
    binary_target_data = df_with_meta_target[meta_target_col]

    # 确保目标变量与OOF预测严格对齐
    common_index = oof_df.index.intersection(binary_target_data.index)
    X_meta = oof_df.loc[common_index].copy()
    y_meta = binary_target_data.loc[common_index].copy()

    # 验证二分类目标变量
    unique_labels = set(y_meta.unique())
    expected_labels = {0, 1}
    if unique_labels != expected_labels:
        logger.warning(f"目标变量包含意外的标签: {unique_labels}, 期望: {expected_labels}")
    else:
        logger.info(f"✅ 二分类目标变量验证通过: {unique_labels}")

    # 最终数据质量保证
    logger.info("🔧 最终数据质量保证...")

    # 处理剩余的NaN值
    if X_meta.isnull().any().any():
        logger.info("🚀 修复数据泄露：使用安全方法处理剩余的NaN值...")
        X_meta = X_meta.ffill().fillna(0)  # 使用向前填充，然后用0填充

    # 验证最终数据
    assert len(X_meta) == len(y_meta), "X_meta和y_meta长度不一致"
    assert X_meta.index.equals(y_meta.index), "X_meta和y_meta索引不一致"

    logger.info(f"✅ 元模型训练数据准备完成: {X_meta.shape[0]} 样本, {X_meta.shape[1]} 特征")

    # 💾 新增：保存CSV文件功能
    if save_csv_files:
        logger.info("💾 保存元模型训练数据到CSV文件...")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 保存特征数据
        x_meta_csv_path = os.path.join(output_dir, "X_meta_features_oof.csv")
        X_meta.to_csv(x_meta_csv_path, index=True)
        logger.info(f"✅ 元模型特征数据已保存: {x_meta_csv_path}")

        # 保存目标变量
        y_meta_csv_path = os.path.join(output_dir, "y_meta_target.csv")
        y_meta.to_csv(y_meta_csv_path, index=True, header=True)
        logger.info(f"✅ 元模型目标变量已保存: {y_meta_csv_path}")

        # 保存数据摘要信息
        summary_info = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'samples_count': len(X_meta),
            'features_count': len(X_meta.columns),
            'feature_names': list(X_meta.columns),
            'target_distribution': y_meta.value_counts().to_dict(),
            'data_range': {
                'start_time': str(X_meta.index.min()),
                'end_time': str(X_meta.index.max())
            }
        }

        import json
        summary_path = os.path.join(output_dir, "meta_data_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary_info, f, indent=2, ensure_ascii=False)
        logger.info(f"✅ 数据摘要信息已保存: {summary_path}")

    return X_meta, y_meta
