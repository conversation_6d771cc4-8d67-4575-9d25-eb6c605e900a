#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MTFA 性能优化器
提供多时间框架分析的性能优化功能，包括配置优化、特征选择和并行处理
"""

import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

# MTFA 关键特征定义 - 只包含对高时间框架有意义的特征
MTFA_ESSENTIAL_FEATURES = {
    # 趋势指标 - 在高时间框架最重要
    'trend': [
        'HMA_', 'EMA_', 'SMA_',  # 移动平均线
        'MACD', 'macd_',         # MACD相关
        'adx_trend_', 'ema_trend_'  # 趋势特征
    ],
    
    # 波动率指标 - 高时间框架波动率更稳定
    'volatility': [
        'ATRr_', 'atr_', 'ATR_',  # ATR相关
        'KC', 'kc_',              # Keltner通道
        'volatility_'             # 波动率特征
    ],
    
    # 动量指标 - 长期动量在高时间框架有效
    'momentum': [
        'RSI_', 'rsi_',           # RSI
        'WILLR_', 'willr_',       # Williams %R
        'CCI_', 'cci_',           # CCI
        'STOCH', 'stoch_'         # 随机指标
    ],
    
    # 成交量指标 - 高时间框架成交量模式重要
    'volume': [
        'volume_', 'vol_',        # 成交量相关
        'volume_vs_avg', 'volume_change'  # 成交量变化
    ],
    
    # 价格位置指标 - 长期价格位置
    'price_position': [
        'close_pos_', 'price_pos_',  # 价格位置
        'body_size_norm', 'candle_range_norm'  # 标准化K线特征
    ]
}

def get_mtfa_optimized_config(base_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成MTFA优化配置，只启用对高时间框架有意义的特征
    
    Args:
        base_config: 基础配置字典
        
    Returns:
        优化后的MTFA配置
    """
    # 复制基础配置
    mtfa_config = base_config.copy()
    
    # 禁用对高时间框架意义不大的特征
    mtfa_optimizations = {
        # 禁用短期特征
        'enable_time_trigonometric': False,  # 时间编码在高时间框架意义不大
        'enable_pattern_recognition': False,  # K线形态在高时间框架噪音较大
        'enable_price_change': False,        # 短期价格变化不适用
        
        # 简化技术指标配置
        'enable_ta': True,                   # 保持技术指标
        'enable_candle': False,              # 禁用详细K线特征
        'enable_volume': True,               # 保持成交量特征
        
        # 优化技术指标参数 - 使用更适合高时间框架的参数
        'rsi_period': 14,                    # 标准RSI周期
        'macd_fast': 12,                     # 标准MACD参数
        'macd_slow': 26,
        'macd_sign': 9,
        'atr_period': 14,                    # 标准ATR周期
        'hma_period': 21,                    # 适合高时间框架的HMA
        
        # 简化EMA配置
        'ema_periods': [20, 50],             # 只保留关键EMA周期
        'ema_short_period_up': 20,
        'ema_long_period_up': 50,
        'ema_short_period_down': 20,
        'ema_long_period_down': 50,
        
        # 禁用衍生特征以提高性能
        'enable_ta_derived_features': False,
        'enable_adx_trend_features': True,   # ADX趋势在高时间框架重要
        'enable_ema_trend_features': True,   # EMA趋势在高时间框架重要
        
        # 禁用复杂特征
        'enable_timeframe_sensitivity': False,  # 避免递归计算
        'enable_fund_flow': False,              # 资金流向在高时间框架计算成本高
        
        # 优化价格变化配置
        'price_change_periods': [1, 3],      # 只保留最基本的价格变化
    }
    
    # 应用优化配置
    mtfa_config.update(mtfa_optimizations)
    
    logger.debug(f"MTFA配置优化完成，禁用了 {len([k for k, v in mtfa_optimizations.items() if v is False])} 个特征模块")
    
    return mtfa_config

def filter_mtfa_features(df_features: pd.DataFrame, timeframe: str, config: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    """
    过滤MTFA特征，只保留对高时间框架有意义的特征

    🚀 优化：使用完善的列过滤系统，支持显式排除列表和智能过滤规则

    Args:
        df_features: 包含所有特征的DataFrame
        timeframe: 时间框架字符串
        config: 可选的过滤配置

    Returns:
        过滤后的特征DataFrame
    """
    if df_features is None or df_features.empty:
        return df_features

    # 检查是否强制使用新过滤器
    force_new_filter = config.get('force_new_mtfa_filter', True) if config else True

    if force_new_filter:
        # 强制使用新过滤器，导入失败时报错
        try:
            from src.core.mtfa_column_filter import filter_mtfa_columns, get_default_mtfa_filter_config
        except ImportError as e:
            error_msg = (f"MTFA列过滤器导入失败 (时间框架: {timeframe}): {e}\n"
                       f"请确保 src.core.mtfa_column_filter 模块可用，或在配置中设置 "
                       f"'force_new_mtfa_filter': False 来使用传统方法")
            logger.error(error_msg)
            raise ImportError(error_msg) from e

        # 准备过滤配置
        filter_config = get_default_mtfa_filter_config()
        if config:
            filter_config.update(config.get('mtfa_filter_config', {}))

        # 添加MTFA特定的包含模式（基于原有的MTFA_ESSENTIAL_FEATURES）
        essential_patterns = []
        for category, patterns in MTFA_ESSENTIAL_FEATURES.items():
            essential_patterns.extend(patterns)

        filter_config['custom_inclusion_patterns'].extend(essential_patterns)

        # 执行过滤
        cols_to_keep = filter_mtfa_columns(df_features, timeframe, filter_config)

        if not cols_to_keep:
            logger.warning(f"MTFA特征过滤后没有保留任何特征 (时间框架: {timeframe})")
            return pd.DataFrame()

        filtered_df = df_features[cols_to_keep].copy()

        logger.debug(f"MTFA特征过滤完成 (时间框架: {timeframe}): "
                    f"原始 {len(df_features.columns)} 列 -> 保留 {len(cols_to_keep)} 列")

        return filtered_df
    else:
        # 兼容模式：尝试新过滤器，失败时回退到旧方法
        try:
            from src.core.mtfa_column_filter import filter_mtfa_columns, get_default_mtfa_filter_config

            # 准备过滤配置
            filter_config = get_default_mtfa_filter_config()
            if config:
                filter_config.update(config.get('mtfa_filter_config', {}))

            # 添加MTFA特定的包含模式（基于原有的MTFA_ESSENTIAL_FEATURES）
            essential_patterns = []
            for category, patterns in MTFA_ESSENTIAL_FEATURES.items():
                essential_patterns.extend(patterns)

            filter_config['custom_inclusion_patterns'].extend(essential_patterns)

            # 执行过滤
            cols_to_keep = filter_mtfa_columns(df_features, timeframe, filter_config)

            if not cols_to_keep:
                logger.warning(f"MTFA特征过滤后没有保留任何特征 (时间框架: {timeframe})")
                return pd.DataFrame()

            filtered_df = df_features[cols_to_keep].copy()

            logger.debug(f"MTFA特征过滤完成 (时间框架: {timeframe}): "
                        f"原始 {len(df_features.columns)} 列 -> 保留 {len(cols_to_keep)} 列")

            return filtered_df

        except ImportError as e:
            logger.warning(f"MTFA列过滤器不可用，使用传统方法 ({timeframe}): {e}")
            return _filter_mtfa_features_legacy(df_features, timeframe)
        except Exception as e:
            logger.error(f"MTFA列过滤器执行失败，使用传统方法 ({timeframe}): {e}")
            return _filter_mtfa_features_legacy(df_features, timeframe)

def _filter_mtfa_features_legacy(df_features: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    传统的MTFA特征过滤方法（保持向后兼容）
    """
    if df_features is None or df_features.empty:
        return df_features

    # 收集所有关键特征模式
    essential_patterns = []
    for category, patterns in MTFA_ESSENTIAL_FEATURES.items():
        essential_patterns.extend(patterns)

    # 基础列（总是排除）
    base_cols = {'open', 'high', 'low', 'close', 'volume', 'qav', 'n', 'tbbav', 'tbqav'}

    # 过滤特征列
    cols_to_keep = []

    for col in df_features.columns:
        # 跳过基础列
        if col in base_cols:
            continue

        # 跳过目标列和字符串列
        if (col.startswith('target_') or
            col.endswith('_name') or
            col == 'candlestick_pattern_name'):
            continue

        # 检查是否匹配关键特征模式
        is_essential = False
        for pattern in essential_patterns:
            if pattern in col:
                is_essential = True
                break

        # 只保留数值类型的关键特征
        if (is_essential and
            df_features[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']):
            cols_to_keep.append(col)

    if not cols_to_keep:
        logger.warning(f"MTFA特征过滤后没有保留任何特征 (时间框架: {timeframe})")
        return pd.DataFrame()

    filtered_df = df_features[cols_to_keep].copy()

    logger.debug(f"MTFA特征过滤完成 (时间框架: {timeframe}): "
                f"原始 {len(df_features.columns)} 列 -> 保留 {len(cols_to_keep)} 列")

    return filtered_df

def process_single_mtfa_timeframe(timeframe: str, 
                                 target_config: Dict[str, Any],
                                 primary_df: pd.DataFrame,
                                 client: Any,
                                 fetch_params: Dict[str, Any]) -> Tuple[str, Optional[pd.DataFrame]]:
    """
    处理单个MTFA时间框架（用于并行处理）
    
    Args:
        timeframe: 时间框架字符串
        target_config: 目标配置
        primary_df: 主时间框架数据
        client: Binance客户端
        fetch_params: 数据获取参数
        
    Returns:
        (timeframe, processed_features_df) 元组
    """
    try:
        start_time = time.time()
        target_name = target_config.get('name', 'Unknown')
        
        logger.debug(f"开始处理MTFA时间框架: {timeframe} (目标: {target_name})")
        
        # 导入必要的函数（避免循环导入）
        from src.core.data_utils import fetch_binance_history, add_classification_features
        from src.core.data_utils import interval_to_timedelta
        from datetime import timezone
        
        # 获取时间参数
        tf_timedelta = interval_to_timedelta(timeframe)
        primary_interval = target_config.get('interval', 'unknown')
        primary_timedelta = interval_to_timedelta(primary_interval)
        
        if tf_timedelta <= primary_timedelta:
            logger.warning(f"MTFA时间框架 {timeframe} <= 主时间框架 {primary_interval}，跳过")
            return timeframe, None
        
        # 获取数据 - 使用时间范围模式，不传递limit参数
        symbol = target_config.get('symbol', 'BTCUSDT')
        df_tf = fetch_binance_history(
            client, symbol, timeframe,
            start_dt=fetch_params['start_dt'],
            end_dt=fetch_params['end_dt']
        )
        
        if df_tf is None or df_tf.empty:
            logger.warning(f"未获取到 {timeframe} 数据")
            return timeframe, None
        
        # 使用优化配置计算特征
        mtfa_config = get_mtfa_optimized_config(target_config)
        mtfa_config['interval'] = timeframe
        mtfa_config['enable_mtfa'] = False  # 避免递归
        
        df_tf_features = add_classification_features(df_tf.copy(), mtfa_config)
        
        if df_tf_features is None or df_tf_features.empty:
            logger.warning(f"未能计算 {timeframe} 特征")
            return timeframe, None
        
        # 过滤特征
        df_filtered = filter_mtfa_features(df_tf_features, timeframe)
        
        if df_filtered.empty:
            logger.warning(f"{timeframe} 特征过滤后为空")
            return timeframe, None
        
        # 重命名特征列
        df_filtered.rename(columns=lambda x: f"{x}_{timeframe}", inplace=True)
        
        # 处理时区
        if not isinstance(df_filtered.index, pd.DatetimeIndex):
            df_filtered.index = pd.to_datetime(df_filtered.index)
        if df_filtered.index.tz is None:
            df_filtered.index = df_filtered.index.tz_localize('UTC')
        elif df_filtered.index.tz != timezone.utc:
            df_filtered.index = df_filtered.index.tz_convert('UTC')
        
        processing_time = time.time() - start_time
        logger.info(f"MTFA时间框架 {timeframe} 处理完成: {len(df_filtered.columns)} 个特征, "
                   f"耗时 {processing_time:.2f}秒")
        
        return timeframe, df_filtered
        
    except Exception as e:
        logger.error(f"处理MTFA时间框架 {timeframe} 时出错: {e}")
        return timeframe, None

class MTFAPerformanceOptimizer:
    """MTFA性能优化器"""
    
    def __init__(self, max_workers: int = 2, enable_parallel: bool = True):
        """
        初始化优化器
        
        Args:
            max_workers: 最大并行工作线程数（考虑API限制）
            enable_parallel: 是否启用并行处理
        """
        self.max_workers = max_workers
        self.enable_parallel = enable_parallel
        self.performance_stats = {}
    
    def optimize_mtfa_processing(self, 
                                primary_df: pd.DataFrame,
                                target_config: Dict[str, Any],
                                client: Any) -> pd.DataFrame:
        """
        优化的MTFA处理主函数
        
        Args:
            primary_df: 主时间框架数据
            target_config: 目标配置
            client: Binance客户端
            
        Returns:
            包含MTFA特征的DataFrame
        """
        start_time = time.time()
        target_name = target_config.get('name', 'Unknown')
        
        mtfa_timeframes = target_config.get('mtfa_timeframes', [])
        if not mtfa_timeframes:
            logger.info(f"MTFA时间框架为空，跳过MTFA处理 (目标: {target_name})")
            return primary_df
        
        logger.info(f"开始优化MTFA处理: {mtfa_timeframes} (目标: {target_name}, "
                   f"并行: {self.enable_parallel})")
        
        # 准备数据获取参数
        fetch_params = self._prepare_fetch_params(primary_df, target_config)
        
        if self.enable_parallel and len(mtfa_timeframes) > 1:
            # 并行处理
            processed_features = self._process_parallel(
                mtfa_timeframes, target_config, primary_df, client, fetch_params
            )
        else:
            # 串行处理（保持兼容性）
            processed_features = self._process_sequential(
                mtfa_timeframes, target_config, primary_df, client, fetch_params
            )
        
        # 合并特征
        result_df = self._merge_mtfa_features(primary_df, processed_features)
        
        total_time = time.time() - start_time
        self.performance_stats[target_name] = {
            'total_time': total_time,
            'timeframes_processed': len(processed_features),
            'features_added': result_df.shape[1] - primary_df.shape[1],
            'parallel_enabled': self.enable_parallel
        }
        
        logger.info(f"MTFA处理完成 (目标: {target_name}): "
                   f"添加 {result_df.shape[1] - primary_df.shape[1]} 个特征, "
                   f"耗时 {total_time:.2f}秒")
        
        return result_df
    
    def _prepare_fetch_params(self, primary_df: pd.DataFrame, 
                             target_config: Dict[str, Any]) -> Dict[str, Any]:
        """准备数据获取参数"""
        from src.core.data_utils import interval_to_timedelta
        
        primary_interval = target_config.get('interval', '5m')
        primary_timedelta = interval_to_timedelta(primary_interval)
        
        primary_start_time = primary_df.index.min()
        primary_end_time = primary_df.index.max()
        
        # 计算需要的历史数据量
        lookback_periods = target_config.get('mtfa_feature_lookback_periods', 200)
        
        return {
            'start_dt': primary_start_time - (lookback_periods * primary_timedelta),
            'end_dt': primary_end_time + primary_timedelta,
            'limit': min(target_config.get('mtfa_min_bars_to_fetch', 300), 1000)
        }
    
    def _process_parallel(self, timeframes: List[str], 
                         target_config: Dict[str, Any],
                         primary_df: pd.DataFrame,
                         client: Any,
                         fetch_params: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """并行处理多个时间框架"""
        processed_features = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_timeframe = {
                executor.submit(
                    process_single_mtfa_timeframe,
                    tf, target_config, primary_df, client, fetch_params
                ): tf for tf in timeframes
            }
            
            # 收集结果
            for future in as_completed(future_to_timeframe):
                timeframe = future_to_timeframe[future]
                try:
                    tf_name, tf_features = future.result(timeout=60)  # 60秒超时
                    if tf_features is not None:
                        processed_features[tf_name] = tf_features
                except Exception as e:
                    logger.error(f"并行处理时间框架 {timeframe} 失败: {e}")
        
        return processed_features
    
    def _process_sequential(self, timeframes: List[str],
                           target_config: Dict[str, Any],
                           primary_df: pd.DataFrame,
                           client: Any,
                           fetch_params: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """串行处理多个时间框架"""
        processed_features = {}
        
        for timeframe in timeframes:
            tf_name, tf_features = process_single_mtfa_timeframe(
                timeframe, target_config, primary_df, client, fetch_params
            )
            if tf_features is not None:
                processed_features[tf_name] = tf_features
        
        return processed_features
    
    def _merge_mtfa_features(self, primary_df: pd.DataFrame,
                            processed_features: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """合并MTFA特征到主DataFrame"""
        result_df = primary_df.copy()
        
        for timeframe, tf_features in processed_features.items():
            try:
                # 🚀 严格防止数据泄露：时间对齐仅使用ffill()
                tf_aligned = tf_features.reindex(result_df.index, method='ffill')

                # 🚨 关键改进：严格防止MTFA数据泄露
                if tf_aligned.isnull().sum().sum() > 0:
                    # 获取主时间框架开始时间之前的MTFA数据的最后一个值
                    main_start_time = result_df.index.min()
                    pre_start_mtfa = tf_features[tf_features.index < main_start_time]

                    if not pre_start_mtfa.empty:
                        # 使用主时间框架开始前的最后一个MTFA值填充开头NaN
                        pre_start_values = pre_start_mtfa.iloc[-1].to_dict()
                        for col in tf_aligned.columns:
                            if col in pre_start_values:
                                tf_aligned[col] = tf_aligned[col].fillna(pre_start_values[col])
                        logger.debug(f"使用 {timeframe} 历史数据填充开头NaN")
                    else:
                        logger.debug(f"无 {timeframe} 历史数据，使用0填充开头NaN")

                    # 🚀 严格的历史数据填充：绝不使用bfill()
                    tf_aligned.fillna(0.0, inplace=True)
                
                # 合并特征
                result_df = pd.merge(result_df, tf_aligned, 
                                   left_index=True, right_index=True, how='left')
                
                logger.debug(f"成功合并 {timeframe} 特征: {len(tf_features.columns)} 列")
                
            except Exception as e:
                logger.error(f"合并 {timeframe} 特征时出错: {e}")
        
        return result_df
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self.performance_stats.copy()
