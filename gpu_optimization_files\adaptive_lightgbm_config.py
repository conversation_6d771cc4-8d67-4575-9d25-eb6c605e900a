"""
自适应LightGBM配置
根据数据规模自动选择GPU或CPU
"""

import lightgbm as lgb
import numpy as np

def get_adaptive_lgb_params(n_samples, n_features, use_gpu_threshold=50000):
    """
    根据数据规模自适应选择参数
    
    Args:
        n_samples: 样本数量
        n_features: 特征数量
        use_gpu_threshold: 使用GPU的样本数阈值
    
    Returns:
        dict: 优化的参数配置
    """
    
    # 基础参数
    base_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'random_state': 42,
        'verbose': -1
    }
    
    # 根据数据规模选择设备和参数
    if n_samples >= use_gpu_threshold:
        print(f"🚀 大数据集({n_samples:,}样本)，使用GPU配置")
        
        params = base_params.copy()
        params.update({
            'device_type': 'gpu',
            'gpu_platform_id': 0,
            'gpu_device_id': 0,
            'gpu_use_dp': False,
            'num_leaves': min(255, max(31, n_samples // 1000)),
            'learning_rate': 0.05,
            'n_estimators': min(1000, max(100, n_samples // 100)),
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'min_data_in_leaf': max(20, n_samples // 10000),
            'lambda_l1': 0.1,
            'lambda_l2': 0.1
        })
        
    else:
        print(f"🖥️  中小数据集({n_samples:,}样本)，使用CPU配置")
        
        params = base_params.copy()
        params.update({
            'device_type': 'cpu',
            'num_threads': 16,  # AMD 16核
            'force_row_wise': True,
            'histogram_pool_size': -1,
            'num_leaves': min(31, max(15, n_samples // 500)),
            'learning_rate': 0.1,  # CPU可以用更高的学习率
            'n_estimators': min(500, max(50, n_samples // 50)),
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'min_data_in_leaf': max(10, n_samples // 5000),
            'lambda_l1': 0.1,
            'lambda_l2': 0.1
        })
    
    return params

def create_adaptive_lgb_model(X, y=None):
    """
    创建自适应的LightGBM模型
    
    Args:
        X: 特征数据
        y: 目标数据（可选，用于参数优化）
    
    Returns:
        LGBMClassifier: 配置好的模型
    """
    n_samples, n_features = X.shape
    
    # 获取自适应参数
    params = get_adaptive_lgb_params(n_samples, n_features)
    
    # 创建模型
    model = lgb.LGBMClassifier(**params)
    
    print(f"✅ 自适应LightGBM模型创建完成")
    print(f"   数据规模: {n_samples:,} × {n_features}")
    print(f"   设备类型: {params['device_type'].upper()}")
    print(f"   叶子数量: {params['num_leaves']}")
    print(f"   估计器数: {params['n_estimators']}")
    
    return model

# 使用示例
if __name__ == "__main__":
    # 测试不同规模的数据
    test_sizes = [(1000, 20), (10000, 50), (100000, 100)]
    
    for n_samples, n_features in test_sizes:
        print(f"\n测试数据规模: {n_samples} × {n_features}")
        X = np.random.random((n_samples, n_features))
        model = create_adaptive_lgb_model(X)
