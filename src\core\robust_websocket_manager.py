# robust_websocket_manager.py
"""
强化的WebSocket管理器 - 专门解决事件循环冲突问题
提供多种连接策略和智能降级机制
"""

import threading
import time
import logging
import traceback
import asyncio
import subprocess
import sys
from typing import Optional, Callable, Dict, Any
from enum import Enum
from binance import ThreadedWebsocketManager, Client
import requests
import json

logger = logging.getLogger(__name__)

class ConnectionStrategy(Enum):
    """连接策略枚举"""
    THREADED_WEBSOCKET = "threaded_websocket"
    SUBPROCESS_WEBSOCKET = "subprocess_websocket"
    REST_API_POLLING = "rest_api_polling"

class RobustWebSocketManager:
    """
    强化的WebSocket管理器
    
    特点：
    - 多种连接策略
    - 事件循环冲突检测和处理
    - 智能降级机制
    - 进程隔离选项
    """
    
    def __init__(self,
                 symbol: str = "BTCUSDT",
                 proxy_url: str = 'http://127.0.0.1:7897',
                 preferred_strategy: ConnectionStrategy = ConnectionStrategy.REST_API_POLLING):
        
        self.symbol = symbol.upper()
        self.proxy_url = proxy_url
        self.preferred_strategy = preferred_strategy
        self.current_strategy = None
        
        # 连接状态
        self.is_connected = False
        self.connection_lock = threading.RLock()
        
        # 数据存储
        self.latest_price = None
        self.last_price_update = 0
        self.price_lock = threading.RLock()
        
        # 连接对象
        self.twm: Optional[ThreadedWebsocketManager] = None
        self.rest_client: Optional[Client] = None
        self.subprocess_handle = None
        
        # 线程控制
        self.stop_event = threading.Event()
        self.worker_thread: Optional[threading.Thread] = None
        
        # 回调函数
        self.price_callback: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'strategy_attempts': {},
            'total_reconnects': 0,
            'uptime_start': time.time(),
            'last_error': None
        }
        
        logger.info(f"强化WebSocket管理器初始化 - {self.symbol}")
    
    def set_price_callback(self, callback: Callable[[float], None]):
        """设置价格更新回调函数"""
        self.price_callback = callback
    
    def _update_price(self, price: float):
        """更新价格数据"""
        with self.price_lock:
            self.latest_price = price
            self.last_price_update = time.time()
        
        # 调用回调函数
        if self.price_callback:
            try:
                self.price_callback(price)
            except Exception as e:
                logger.error(f"价格回调函数执行失败: {e}")
    
    def _detect_event_loop_conflict(self) -> bool:
        """检测事件循环冲突"""
        try:
            loop = asyncio.get_running_loop()
            logger.warning("检测到运行中的事件循环，可能存在冲突")
            return True
        except RuntimeError:
            logger.info("没有检测到运行中的事件循环")
            return False
    
    def _try_threaded_websocket(self) -> bool:
        """尝试使用ThreadedWebsocketManager"""
        try:
            logger.info("尝试ThreadedWebsocketManager策略...")
            
            # 检测事件循环冲突
            has_conflict = self._detect_event_loop_conflict()
            
            if has_conflict:
                # 使用简化配置
                twm_kwargs = {
                    "api_key": None,
                    "api_secret": None,
                    "https_proxy": self.proxy_url,
                }
            else:
                # 使用完整配置
                import aiohttp
                session_timeout = aiohttp.ClientTimeout(total=30, connect=10)
                twm_kwargs = {
                    "api_key": None,
                    "api_secret": None,
                    "https_proxy": self.proxy_url,
                    "requests_params": {"timeout": session_timeout}
                }
            
            self.twm = ThreadedWebsocketManager(**twm_kwargs)
            
            # 启动TWM
            self.twm.start()
            
            # 等待启动完成
            for i in range(20):  # 最多等待10秒
                time.sleep(0.5)
                if self.twm.is_alive():
                    break
            else:
                logger.error("TWM启动超时")
                return False
            
            # 启动价格流
            stream_name = self.twm.start_symbol_ticker_socket(
                symbol=self.symbol,
                callback=self._handle_websocket_message
            )
            
            if stream_name:
                logger.info(f"ThreadedWebsocket策略成功 - Stream ID: {stream_name}")
                self.current_strategy = ConnectionStrategy.THREADED_WEBSOCKET
                return True
            else:
                logger.error("价格流启动失败")
                return False
                
        except Exception as e:
            logger.error(f"ThreadedWebsocket策略失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    def _try_rest_api_polling(self) -> bool:
        """尝试使用REST API轮询"""
        try:
            logger.info("尝试REST API轮询策略...")
            
            self.rest_client = Client()
            # 测试连接
            self.rest_client.get_server_time()
            
            # 启动轮询线程
            self.worker_thread = threading.Thread(
                target=self._rest_polling_worker,
                daemon=True
            )
            self.worker_thread.start()
            
            logger.info("REST API轮询策略成功")
            self.current_strategy = ConnectionStrategy.REST_API_POLLING
            return True
            
        except Exception as e:
            logger.error(f"REST API轮询策略失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    def _rest_polling_worker(self):
        """REST API轮询工作线程"""
        logger.info("REST API轮询线程启动")
        
        while not self.stop_event.is_set():
            try:
                ticker = self.rest_client.get_symbol_ticker(symbol=self.symbol)
                price = float(ticker['price'])
                self._update_price(price)
                
                # 等待5秒后下次轮询
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"REST API轮询错误: {e}")
                time.sleep(10)  # 错误时等待更长时间
        
        logger.info("REST API轮询线程结束")
    
    def _handle_websocket_message(self, msg: Dict[str, Any]):
        """处理WebSocket消息"""
        try:
            if msg.get('e') == '24hrTicker' and msg.get('s') == self.symbol:
                price = float(msg['c'])
                self._update_price(price)
        except Exception as e:
            logger.error(f"WebSocket消息处理失败: {e}")
    
    def start(self) -> bool:
        """启动连接管理器"""
        logger.info(f"启动强化WebSocket管理器 - {self.symbol}")

        with self.connection_lock:
            if self.is_connected:
                logger.warning("管理器已经在运行中")
                return True

            self.stop_event.clear()

            # 🔧 智能策略选择：检测事件循环冲突
            has_event_loop_conflict = self._detect_event_loop_conflict()

            if has_event_loop_conflict:
                logger.warning("检测到事件循环冲突，跳过WebSocket策略，直接使用REST API")
                strategies = [ConnectionStrategy.REST_API_POLLING]
            else:
                # 尝试不同的连接策略
                strategies = [
                    ConnectionStrategy.THREADED_WEBSOCKET,
                    ConnectionStrategy.REST_API_POLLING
                ]

            for strategy in strategies:
                logger.info(f"尝试策略: {strategy.value}")

                # 记录尝试次数
                if strategy not in self.stats['strategy_attempts']:
                    self.stats['strategy_attempts'][strategy] = 0
                self.stats['strategy_attempts'][strategy] += 1

                success = False
                if strategy == ConnectionStrategy.THREADED_WEBSOCKET:
                    success = self._try_threaded_websocket()
                elif strategy == ConnectionStrategy.REST_API_POLLING:
                    success = self._try_rest_api_polling()

                if success:
                    self.is_connected = True
                    logger.info(f"连接成功，使用策略: {strategy.value}")
                    return True
                else:
                    logger.warning(f"策略 {strategy.value} 失败，尝试下一个")
                    self._cleanup_current_connection()

            logger.error("所有连接策略都失败了")
            return False

    def force_reconnect(self):
        """强制重连（软重连，避免事件循环冲突）"""
        logger.info(f"强制重连请求 - {self.symbol}")

        with self.connection_lock:
            if not self.is_connected:
                logger.warning("当前未连接，无需重连")
                return

            # 🔧 软重连：不停止当前连接，而是标记需要重连
            self.stats['total_reconnects'] += 1
            self.stats['last_error'] = "用户手动重连"

            # 如果使用REST API策略，立即获取一次价格
            if self.current_strategy == ConnectionStrategy.REST_API_POLLING:
                logger.info("REST API模式，立即获取价格")
                self._fetch_price_via_rest()
            else:
                logger.info("WebSocket模式，连接应该会自动恢复")

            logger.info(f"强制重连完成 - {self.symbol}")
    
    def _cleanup_current_connection(self):
        """清理当前连接"""
        try:
            if self.twm and self.twm.is_alive():
                self.twm.stop()
                self.twm = None
            
            if self.worker_thread and self.worker_thread.is_alive():
                self.stop_event.set()
                self.worker_thread.join(timeout=5)
                self.worker_thread = None
                
        except Exception as e:
            logger.warning(f"连接清理异常: {e}")
    
    def stop(self):
        """停止连接管理器"""
        logger.info("停止强化WebSocket管理器")
        
        with self.connection_lock:
            self.stop_event.set()
            self.is_connected = False
            self._cleanup_current_connection()
    
    def get_latest_price(self) -> Optional[float]:
        """获取最新价格"""
        with self.price_lock:
            return self.latest_price
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = time.time() - self.stats['uptime_start']
        return {
            **self.stats,
            'current_strategy': self.current_strategy.value if self.current_strategy else None,
            'is_connected': self.is_connected,
            'uptime_seconds': uptime,
            'latest_price': self.latest_price,
            'last_update_ago': time.time() - self.last_price_update if self.last_price_update > 0 else None
        }
