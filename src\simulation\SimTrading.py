# SimTrading.py

import time
from datetime import datetime, timedelta
from ..analysis.analysis_logger import log_trade_settlement # <--- 添加这行导入
from ..core.unified_trade_logger import get_unified_trade_logger # <--- 升级到统一日志系统

class Trade:
    PAYOUT_RATIO = 0.85
    MIN_BET = 5.0
    MAX_BET = 250.0
    CONTRACT_DURATION_MINUTES = 30

    def __init__(self, direction, entry_price, amount_staked, target_name="SimTrading", symbol="BTCUSDT", context_data=None):
        # ... (你的 __init__ 方法代码) ...
        if direction.upper() not in ["UP", "DOWN"]: raise ValueError("无效的交易方向")
        self.trade_id = int(time.time() * 1000)
        self.direction = direction.upper()
        self.entry_price = float(entry_price)
        self.amount_staked = float(amount_staked)
        self.entry_time = datetime.now()
        self.expiry_time = self.entry_time + timedelta(minutes=self.CONTRACT_DURATION_MINUTES)
        self.status = "OPEN"
        self.exit_price = None
        self.profit_loss = 0.0

        # 集成统一日志系统 - 记录开仓
        self.target_name = target_name
        self.symbol = symbol
        self.trade_logger = get_unified_trade_logger(
            base_log_dir="trading_logs_unified",
            auto_start=True
        )
        self.logger_trade_id = None

        try:
            # 转换方向名称以符合TradeLogger的要求
            logger_direction = "LONG" if self.direction == "UP" else "SHORT"

            # 🚀 V12.0: 传递完整的上下文数据（信号快照）
            self.logger_trade_id = self.trade_logger.record_trade_entry(
                target_name=self.target_name,
                symbol=self.symbol,
                direction=logger_direction,
                entry_price=self.entry_price,
                amount=self.amount_staked,
                payout_ratio=self.PAYOUT_RATIO,
                context_data=context_data  # 🚀 V12.0: 传递信号快照
            )
            print(f"[统一日志系统] 开仓记录成功: {self.logger_trade_id}")
        except Exception as e:
            print(f"[统一日志系统] 开仓记录失败: {e}")

        print(f"[{self.entry_time.strftime('%Y-%m-%d %H:%M:%S')}] 新交易 (ID: {self.trade_id}): "
              f"方向 {self.direction}, 开仓价 {self.entry_price:.2f}, 金额 {self.amount_staked:.2f}, "
              f"到期时间 {self.expiry_time.strftime('%Y-%m-%d %H:%M:%S')} (合约时长: {self.CONTRACT_DURATION_MINUTES}分钟)")

    def is_expired(self):
        return datetime.now() >= self.expiry_time

    def check_expiry(self, current_market_price):
        if self.status != "OPEN":
            return False

        if not self.is_expired():
            return False

        if current_market_price is None:
            self.status = "ERROR_NO_EXIT_PRICE"
            self.profit_loss = 0
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 交易结算失败 (ID: {self.trade_id}): 无法获取平仓价格。状态变为 {self.status}")
            # 即使是错误，也应该尝试记录，或者记录一个特殊的错误状态
            try:
                log_trade_settlement(
                    trade_obj=self,
                    market_snapshot_entry={'price': self.entry_price, 'timestamp': self.entry_time.isoformat()},
                    market_snapshot_exit={'price': None, 'timestamp': datetime.now().isoformat(), 'error': 'No exit price'},
                    related_prediction_id=f"{self.trade_id}_{self.entry_time.strftime('%Y%m%d_%H%M%S')}"
                )
            except Exception as e_log_err:
                print(f"!!! 记录交易结算错误日志失败: {e_log_err}")
            return True

        self.exit_price = float(current_market_price)
        changed = False

        if self.direction == "UP":
            if self.exit_price > self.entry_price:
                self.status = "WON"
                # 🔧 修复：返回本金 + 盈利 (因为本金已在开仓时扣除)
                self.profit_loss = self.amount_staked + (self.amount_staked * self.PAYOUT_RATIO)
            else:
                self.status = "LOST"
                # 🔧 修复：亏损时不返还任何金额（本金已在开仓时扣除）
                self.profit_loss = 0
            changed = True
        elif self.direction == "DOWN":
            if self.exit_price < self.entry_price:
                self.status = "WON"
                # 🔧 修复：返回本金 + 盈利 (因为本金已在开仓时扣除)
                self.profit_loss = self.amount_staked + (self.amount_staked * self.PAYOUT_RATIO)
            else:
                self.status = "LOST"
                # 🔧 修复：亏损时不返还任何金额（本金已在开仓时扣除）
                self.profit_loss = 0
            changed = True

        if changed:
            # 🔧 修复：更清晰的资金变动日志
            if self.status == "WON":
                actual_profit = self.amount_staked * self.PAYOUT_RATIO
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 交易结算 (ID: {self.trade_id}): 方向 {self.direction}, "
                      f"开仓 {self.entry_price:.2f}, 平仓 {self.exit_price:.2f}, 下注 {self.amount_staked:.2f}, "
                      f"结果: {self.status} ✅, 返还: {self.profit_loss:.2f} (本金 {self.amount_staked:.2f} + 盈利 {actual_profit:.2f})")
            else:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 交易结算 (ID: {self.trade_id}): 方向 {self.direction}, "
                      f"开仓 {self.entry_price:.2f}, 平仓 {self.exit_price:.2f}, 下注 {self.amount_staked:.2f}, "
                      f"结果: {self.status} ❌, 返还: {self.profit_loss:.2f} (本金已亏损)")

            # --- 集成统一日志系统 - 记录平仓 ---
            if self.logger_trade_id:
                try:
                    result = "WIN" if self.status == "WON" else "LOSS"
                    success = self.trade_logger.record_trade_exit(
                        trade_id=self.logger_trade_id,
                        exit_price=self.exit_price,
                        result=result,
                        exit_reason="expired"
                    )
                    if success:
                        print(f"[统一日志系统] 平仓记录成功: {self.logger_trade_id} - {result}")
                    else:
                        print(f"[统一日志系统] 平仓记录失败: {self.logger_trade_id}")
                except Exception as e:
                    print(f"[统一日志系统] 平仓记录异常: {e}")

            # --- 记录交易结算信息用于失败案例分析 ---
            try:
                exit_market_snapshot = {
                    'price': current_market_price,
                    'timestamp': datetime.now().isoformat(),
                    'price_change': self.exit_price - self.entry_price,
                    'price_change_percent': ((self.exit_price - self.entry_price) / self.entry_price) * 100 if self.entry_price != 0 else 0
                }

                entry_market_snapshot = {
                    'price': self.entry_price,
                    'timestamp': self.entry_time.isoformat()
                }

                log_trade_settlement(
                    trade_obj=self,
                    market_snapshot_entry=entry_market_snapshot,
                    market_snapshot_exit=exit_market_snapshot,
                    related_prediction_id=f"{self.trade_id}_{self.entry_time.strftime('%Y%m%d_%H%M%S')}"
                )

            except Exception as e:
                print(f"!!! 记录交易结算日志失败: {e}")

        return changed
        



    def __str__(self): 
        return (f"Trade(ID: {self.trade_id}, Dir: {self.direction}, EntryP: {self.entry_price:.2f}, "
                f"Stake: {self.amount_staked:.2f}, Status: {self.status}, "
                f"EntryT: {self.entry_time.strftime('%H:%M:%S')}, ExpiryT: {self.expiry_time.strftime('%H:%M:%S')}, "
                f"P/L: {self.profit_loss:.2f})")

class TradingEngine:
    def __init__(self, account_manager, price_fetcher_instance):
        self.active_trades = []
        self.trade_history = [] 
        self.account = account_manager
        self.price_fetcher = price_fetcher_instance

    def open_trade(self, direction, amount_staked, target_name="SimTrading", symbol="BTCUSDT", context_data=None):
        """
        开启新交易

        Args:
            direction: 交易方向 (UP/DOWN)
            amount_staked: 下注金额
            target_name: 目标名称
            symbol: 交易符号
            context_data: 上下文数据

        Returns:
            Trade对象或None
        """
        # 🔧 修复：先预留资金，确保余额管理正确
        if not self.account.reserve_funds(amount_staked):
            print(f"TradingEngine: 开仓失败 - 资金预留失败 (需要 {amount_staked:.2f}, "
                  f"可用 {self.account.current_balance + amount_staked:.2f})。")
            return None

        current_price = self.price_fetcher.get_current_price()
        if current_price is None:
            print("TradingEngine: 开仓失败 - 无法获取当前市场价格用于开仓。")
            # 🔧 修复：获取价格失败时释放预留资金
            self.account.release_funds(amount_staked)
            return None

        try:
            # 🚀 V12.0: 传递上下文数据到Trade对象
            trade = Trade(direction=direction,
                          entry_price=current_price,
                          amount_staked=amount_staked,
                          target_name=target_name,
                          symbol=symbol,
                          context_data=context_data)
            self.active_trades.append(trade)
            print(f"TradingEngine: 成功创建交易 {trade.trade_id}。当前活跃交易数: {len(self.active_trades)}")
            print(f"💰 资金状态: 预留 {amount_staked:.2f} USDT, 剩余可用: {self.account.current_balance:.2f} USDT")
            return trade
        except ValueError as e:
            print(f"TradingEngine: 开仓失败 (参数验证): {e}")
            # 🔧 修复：创建交易失败时释放预留资金
            self.account.release_funds(amount_staked)
            return None
        except Exception as e_trade_create:
            print(f"TradingEngine: 开仓失败 (创建交易对象时出错): {e_trade_create}")
            # 🔧 修复：创建交易失败时释放预留资金
            self.account.release_funds(amount_staked)
            return None

    # --- MODIFIED: process_expired_trades ---
    def process_expired_trades(self):
        trades_processed_this_call = 0 # 用于判断是否有交易被处理
        current_price_for_settlement = self.price_fetcher.get_current_price()
        
        # print(f"DEBUG TradingEngine: process_expired_trades - Current Time: {datetime.now().strftime('%H:%M:%S.%f')}, Settlement Price: {current_price_for_settlement}")

        if not self.active_trades:
            return

        # 使用索引迭代以安全地在循环中移除元素，或者迭代副本然后移除
        # 为了更清晰，我们先收集到期的，然后再统一处理和移除
        
        expired_and_open_trades = []
        for trade in self.active_trades:
            # print(f"DEBUG TradingEngine: Checking Trade ID {trade.trade_id}, Status: {trade.status}, Expiry Time: {trade.expiry_time.strftime('%H:%M:%S.%f')}")
            if trade.status == "OPEN" and trade.is_expired():
                expired_and_open_trades.append(trade)
        
        if not expired_and_open_trades:
            return

        for trade in expired_and_open_trades:
            # print(f"DEBUG TradingEngine: Trade ID {trade.trade_id} is OPEN and EXPIRED. Attempting to check_expiry.")
            changed = trade.check_expiry(current_market_price=current_price_for_settlement)
            # print(f"DEBUG TradingEngine: Trade ID {trade.trade_id} after check_expiry - Changed: {changed}, New Status: {trade.status}, P/L: {trade.profit_loss}")
            
            if changed: # 交易状态已更新 (WON, LOST, or ERROR_NO_EXIT_PRICE)
                trades_processed_this_call += 1
                if trade.status in ["WON", "LOST"]:
                    # --- 这是关键的修改 ---
                    self.account.record_trade_result(trade.direction, trade.amount_staked, trade.profit_loss)
                    # ---------------------

                    # 🔧 修复：通知交易状态管理器交易已完成
                    try:
                        from ..core.trade_state_manager import enhanced_trade_state_manager as trade_state_manager
                        trade_state_manager.complete_trade({
                            'trade_id': trade.trade_id,
                            'direction': trade.direction,
                            'amount': trade.amount_staked,
                            'result': trade.status,
                            'profit_loss': trade.profit_loss,
                            'exit_price': trade.exit_price,
                            'completion_reason': 'expired'
                        })
                        print(f"🔒 [交易状态管理] 交易 {trade.trade_id} 已完成，状态重置为空闲")
                    except Exception as e:
                        print(f"⚠️ [交易状态管理] 通知交易完成失败: {e}")

                self.trade_history.append(trade) # 添加到历史记录
                if trade in self.active_trades: # 从活跃列表中移除
                    self.active_trades.remove(trade)
        
        if trades_processed_this_call > 0:
            print(f"TradingEngine: 已处理 {trades_processed_this_call} 笔到期交易。剩余活跃交易: {len(self.active_trades)}")

    def cancel_trade(self, trade_id):
        """
        取消交易（释放预留资金）

        Args:
            trade_id: 交易ID

        Returns:
            bool: 是否成功取消
        """
        for trade in self.active_trades:
            if trade.trade_id == trade_id:
                # 释放预留资金
                self.account.release_funds(trade.amount_staked)

                # 从活跃交易列表中移除
                self.active_trades.remove(trade)

                print(f"TradingEngine: 交易 {trade_id} 已取消，资金已释放")
                return True

        print(f"TradingEngine: 未找到交易 {trade_id}")
        return False

    def get_active_trades_info(self):
        # ... (保持不变) ...
        info_list = []
        for trade in self.active_trades:
            remaining_time = trade.expiry_time - datetime.now()
            remaining_seconds = max(0, int(remaining_time.total_seconds()))
            info_list.append({
                "id": trade.trade_id, "direction": trade.direction, "entry_price": trade.entry_price,
                "amount_staked": trade.amount_staked, "entry_time": trade.entry_time.strftime('%H:%M:%S'),
                "expiry_time": trade.expiry_time.strftime('%H:%M:%S'),
                "remaining_seconds": remaining_seconds, "status": trade.status
            })
        return info_list

    def get_trade_history_info(self, limit=20):
        # ... (保持不变) ...
        info_list = []
        for trade in reversed(self.trade_history[-limit:]):
            info_list.append({
                "id": trade.trade_id, "direction": trade.direction, "entry_price": trade.entry_price,
                "exit_price": trade.exit_price, "amount_staked": trade.amount_staked,
                "entry_time": trade.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status": trade.status, "profit_loss": trade.profit_loss
            })
        return info_list

# --- 简单测试 (保持不变) ---
if __name__ == "__main__":

    def sync_state_with_manager(self):
        """与状态管理器同步状态"""
        try:
            from ..core.trade_state_manager import enhanced_trade_state_manager as trade_state_manager
            
            # 获取当前活跃交易数
            active_count = len(self.active_trades)
            
            # 获取状态管理器状态
            manager_status = trade_state_manager.get_status()
            manager_active = manager_status['current_state'] in ['opening', 'active', 'closing']
            
            # 如果状态不一致，修复
            if active_count == 0 and manager_active:
                # 模拟器无交易但状态管理器认为有，重置状态管理器
                trade_state_manager.force_reset("模拟器心跳检测：无活跃交易")
                print(f"🔄 [状态同步] 已重置状态管理器 (模拟器无活跃交易)")
            elif active_count > 0 and not manager_active:
                # 模拟器有交易但状态管理器不知道，通知状态管理器
                print(f"⚠️ [状态同步] 检测到状态不一致：模拟器有{active_count}个交易但状态管理器为idle")
                
        except Exception as e:
            print(f"⚠️ [状态同步] 同步失败: {e}")

    class MockAccount:
        def __init__(self, initial_balance=1000): self.current_balance = initial_balance; print(f"MockAccount: 初始余额 {self.current_balance}")
        # 修改 mock record_trade_result 以匹配新的签名
        def record_trade_result(self, direction, amount_staked, profit_or_loss): 
            self.current_balance += profit_or_loss
            print(f"MockAccount: 交易记录 ({direction}) - 赌注 {amount_staked:.2f}, 盈亏 {profit_or_loss:.2f}, 新余额 {self.current_balance:.2f}")

    class MockPriceFetcher:
        def __init__(self, initial_price=30000.0): self.mock_price = initial_price; self.price_sequence = [initial_price + i*5 for i in range(-2, 3)] # 产生一些价格波动
        def get_current_price(self): 
            # 循环使用价格序列，或者更简单地随机化一点
            price_to_return = self.price_sequence[int(time.time()) % len(self.price_sequence)]
            # self.mock_price += (time.time() % 2 - 1) * 0.1 # 微小波动
            print(f"MockPriceFetcher: 返回价格 {price_to_return:.2f}"); 
            return price_to_return

    Trade.CONTRACT_DURATION_MINUTES = 0.1 # 测试时用短时间
    mock_account = MockAccount(initial_balance=1000)
    mock_price_fetcher = MockPriceFetcher(initial_price=30000.00)
    engine = TradingEngine(account_manager=mock_account, price_fetcher_instance=mock_price_fetcher)

    print("\n--- 开设交易 ---")
    trade1 = engine.open_trade(direction="UP", amount_staked=10)
    if trade1: print(f"  成功开设 trade1: {trade1.trade_id}")
    
    print("\n--- 等待交易到期并结算 ---")
    start_wait_time = time.time()
    while engine.active_trades and time.time() - start_wait_time < (Trade.CONTRACT_DURATION_MINUTES * 60 + 5): # 等待时间稍长
        time.sleep(1)
        print(f". (活跃交易数: {len(engine.active_trades)}, 时间: {datetime.now().strftime('%H:%M:%S')})")
        engine.process_expired_trades()
        if not engine.active_trades:
            print("所有活跃交易已处理。")
            break
            
    print("\n--- 结算后账户状态 ---")
    print(f"MockAccount 当前余额: {mock_account.current_balance:.2f}")
    print("\n--- 交易历史 ---")
    history = engine.get_trade_history_info()
    if history:
        for h_info in history: 
            print(f"  ID: {h_info['id']}, Dir: {h_info['direction']}, EntryP: {h_info['entry_price']:.2f}, ExitP: {h_info['exit_price']:.2f if h_info['exit_price'] is not None else 'N/A'}, Status: {h_info['status']}, P/L: {h_info['profit_loss']:.2f}")
    else: 
        print("  无交易历史。")
