#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 数据迁移管理器 V1.0
将现有的 trading_logs_unified 和 analysis_logs 数据迁移到新的统一综合日志系统

核心功能：
- 迁移 trading_logs_unified 数据到新的 trades 层
- 迁移 analysis_logs 数据到新的 contexts 和 analysis 层
- 保持数据完整性和一致性
- 提供迁移进度跟踪和错误处理
- 支持增量迁移和回滚
"""

import os
import shutil
import pandas as pd
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import logging


class DataMigrationManager:
    """数据迁移管理器"""
    
    def __init__(self, 
                 old_trading_logs_dir: str = "trading_logs_unified",
                 old_analysis_logs_dir: str = "analysis_logs", 
                 new_comprehensive_logs_dir: str = "comprehensive_logs"):
        """
        初始化数据迁移管理器
        
        Args:
            old_trading_logs_dir: 旧的交易日志目录
            old_analysis_logs_dir: 旧的分析日志目录
            new_comprehensive_logs_dir: 新的综合日志目录
        """
        self.old_trading_logs_dir = Path(old_trading_logs_dir)
        self.old_analysis_logs_dir = Path(old_analysis_logs_dir)
        self.new_comprehensive_logs_dir = Path(new_comprehensive_logs_dir)
        
        self.logger = logging.getLogger(f"{__name__}.DataMigrationManager")
        
        # 迁移统计
        self.migration_stats = {
            'trades_migrated': 0,
            'contexts_migrated': 0,
            'analysis_migrated': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 创建新目录结构
        self._create_new_directory_structure()
        
        self.logger.info("数据迁移管理器初始化完成")
    
    def _create_new_directory_structure(self):
        """创建新的目录结构"""
        directories = [
            self.new_comprehensive_logs_dir / "trades",
            self.new_comprehensive_logs_dir / "contexts", 
            self.new_comprehensive_logs_dir / "analysis",
            self.new_comprehensive_logs_dir / "unified_views"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"新目录结构已创建: {self.new_comprehensive_logs_dir}")
    
    def migrate_all_data(self, backup_old_data: bool = True, 
                        dry_run: bool = False) -> Dict[str, Any]:
        """
        迁移所有数据
        
        Args:
            backup_old_data: 是否备份旧数据
            dry_run: 是否只是试运行（不实际迁移）
        
        Returns:
            迁移结果统计
        """
        self.migration_stats['start_time'] = datetime.now()
        
        try:
            self.logger.info(f"开始数据迁移 (dry_run={dry_run})")
            
            # 备份旧数据
            if backup_old_data and not dry_run:
                self._backup_old_data()
            
            # 迁移交易日志数据
            trades_result = self.migrate_trading_logs(dry_run=dry_run)
            self.migration_stats['trades_migrated'] = trades_result.get('migrated_count', 0)
            
            # 迁移分析日志数据
            analysis_result = self.migrate_analysis_logs(dry_run=dry_run)
            self.migration_stats['contexts_migrated'] = analysis_result.get('contexts_migrated', 0)
            self.migration_stats['analysis_migrated'] = analysis_result.get('analysis_migrated', 0)
            
            self.migration_stats['end_time'] = datetime.now()
            
            # 生成迁移报告
            migration_report = self._generate_migration_report()
            
            if not dry_run:
                self._save_migration_report(migration_report)
            
            self.logger.info("数据迁移完成")
            return migration_report
            
        except Exception as e:
            self.logger.error(f"数据迁移失败: {e}")
            self.migration_stats['errors'] += 1
            self.migration_stats['end_time'] = datetime.now()
            return {'error': str(e), 'stats': self.migration_stats}
    
    def migrate_trading_logs(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        迁移交易日志数据
        
        Args:
            dry_run: 是否只是试运行
        
        Returns:
            迁移结果
        """
        result = {'migrated_count': 0, 'errors': [], 'files_processed': 0}
        
        try:
            if not self.old_trading_logs_dir.exists():
                self.logger.warning(f"旧交易日志目录不存在: {self.old_trading_logs_dir}")
                return result
            
            # 遍历所有交易日志文件
            for year_dir in self.old_trading_logs_dir.glob("*"):
                if not year_dir.is_dir():
                    continue
                
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir():
                        continue
                    
                    for csv_file in month_dir.glob("trades_*.csv"):
                        try:
                            file_result = self._migrate_single_trading_file(csv_file, dry_run)
                            result['migrated_count'] += file_result['migrated_count']
                            result['files_processed'] += 1
                            
                            if file_result.get('errors'):
                                result['errors'].extend(file_result['errors'])
                                
                        except Exception as e:
                            error_msg = f"迁移文件失败 {csv_file}: {e}"
                            self.logger.error(error_msg)
                            result['errors'].append(error_msg)
                            self.migration_stats['errors'] += 1
            
            self.logger.info(f"交易日志迁移完成: {result['migrated_count']} 条记录, {result['files_processed']} 个文件")
            return result
            
        except Exception as e:
            self.logger.error(f"交易日志迁移失败: {e}")
            result['errors'].append(str(e))
            return result
    
    def _migrate_single_trading_file(self, csv_file: Path, dry_run: bool = False) -> Dict[str, Any]:
        """迁移单个交易日志文件"""
        result = {'migrated_count': 0, 'errors': []}
        
        try:
            # 读取原始数据
            df = pd.read_csv(csv_file)
            
            if df.empty:
                return result
            
            # 数据清洗和转换
            df_cleaned = self._clean_trading_data(df)
            
            if not dry_run:
                # 确定目标文件路径
                target_file = self._get_target_trading_file_path(csv_file)
                
                # 确保目标目录存在
                target_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 写入新格式
                if target_file.exists():
                    # 追加到现有文件
                    df_cleaned.to_csv(target_file, mode='a', header=False, index=False)
                else:
                    # 创建新文件
                    df_cleaned.to_csv(target_file, index=False)
                
                self.logger.debug(f"迁移文件: {csv_file} -> {target_file}")
            
            result['migrated_count'] = len(df_cleaned)
            return result
            
        except Exception as e:
            error_msg = f"迁移单个交易文件失败 {csv_file}: {e}"
            result['errors'].append(error_msg)
            return result
    
    def _clean_trading_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗和转换交易数据"""
        # 确保必要的列存在
        required_columns = [
            'trade_id', 'entry_timestamp', 'exit_timestamp', 'target_name', 'symbol',
            'direction', 'entry_price', 'exit_price', 'amount', 'payout_ratio',
            'result', 'profit_loss', 'exit_reason'
        ]
        
        # 添加缺失的列
        for col in required_columns:
            if col not in df.columns:
                df[col] = None
        
        # 数据类型转换
        if 'entry_timestamp' in df.columns:
            df['entry_timestamp'] = pd.to_datetime(df['entry_timestamp'], errors='coerce')
        
        if 'exit_timestamp' in df.columns:
            df['exit_timestamp'] = pd.to_datetime(df['exit_timestamp'], errors='coerce')
        
        # 移除无效记录
        df = df.dropna(subset=['trade_id', 'entry_timestamp'])
        
        return df
    
    def _get_target_trading_file_path(self, original_file: Path) -> Path:
        """获取目标交易文件路径"""
        # 从原始文件路径提取年月信息
        parts = original_file.parts
        
        # 查找年份和月份
        year = None
        month = None
        
        for part in parts:
            if part.isdigit() and len(part) == 4:  # 年份
                year = part
            elif part.isdigit() and len(part) == 2:  # 月份
                month = part
        
        if not year or not month:
            # 从文件名提取日期
            filename = original_file.stem
            if 'trades_' in filename:
                date_part = filename.replace('trades_', '')
                try:
                    date_obj = datetime.strptime(date_part, '%Y-%m-%d')
                    year = str(date_obj.year)
                    month = f"{date_obj.month:02d}"
                except ValueError:
                    # 使用当前日期作为后备
                    now = datetime.now()
                    year = str(now.year)
                    month = f"{now.month:02d}"
        
        # 构建目标路径
        target_dir = self.new_comprehensive_logs_dir / "trades" / year / month
        target_file = target_dir / original_file.name
        
        return target_file
    
    def migrate_analysis_logs(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        迁移分析日志数据
        
        Args:
            dry_run: 是否只是试运行
        
        Returns:
            迁移结果
        """
        result = {'contexts_migrated': 0, 'analysis_migrated': 0, 'errors': []}
        
        try:
            if not self.old_analysis_logs_dir.exists():
                self.logger.warning(f"旧分析日志目录不存在: {self.old_analysis_logs_dir}")
                return result
            
            # 迁移预测上下文数据
            prediction_context_file = self.old_analysis_logs_dir / "prediction_context.csv"
            if prediction_context_file.exists():
                contexts_result = self._migrate_prediction_contexts(prediction_context_file, dry_run)
                result['contexts_migrated'] = contexts_result.get('migrated_count', 0)
                if contexts_result.get('errors'):
                    result['errors'].extend(contexts_result['errors'])
            
            # 迁移交易结算数据（合并到交易数据中）
            trade_settlement_file = self.old_analysis_logs_dir / "trade_settlement.csv"
            if trade_settlement_file.exists():
                settlement_result = self._migrate_trade_settlements(trade_settlement_file, dry_run)
                if settlement_result.get('errors'):
                    result['errors'].extend(settlement_result['errors'])
            
            # 迁移失败分析数据
            failure_analysis_file = self.old_analysis_logs_dir / "failure_analysis_summary.csv"
            if failure_analysis_file.exists():
                analysis_result = self._migrate_failure_analysis(failure_analysis_file, dry_run)
                result['analysis_migrated'] = analysis_result.get('migrated_count', 0)
                if analysis_result.get('errors'):
                    result['errors'].extend(analysis_result['errors'])
            
            self.logger.info(f"分析日志迁移完成: {result['contexts_migrated']} 条上下文, {result['analysis_migrated']} 条分析")
            return result
            
        except Exception as e:
            self.logger.error(f"分析日志迁移失败: {e}")
            result['errors'].append(str(e))
            return result
    
    def _migrate_prediction_contexts(self, context_file: Path, dry_run: bool = False) -> Dict[str, Any]:
        """迁移预测上下文数据"""
        result = {'migrated_count': 0, 'errors': []}
        
        try:
            df = pd.read_csv(context_file)
            
            if df.empty:
                return result
            
            # 按日期分组
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
            df = df.dropna(subset=['timestamp'])
            
            if not dry_run:
                # 按日期分组写入不同文件
                for date, group in df.groupby(df['timestamp'].dt.date):
                    target_file = self._get_target_context_file_path(date)
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    if target_file.exists():
                        group.to_csv(target_file, mode='a', header=False, index=False)
                    else:
                        group.to_csv(target_file, index=False)
            
            result['migrated_count'] = len(df)
            return result
            
        except Exception as e:
            error_msg = f"迁移预测上下文失败: {e}"
            result['errors'].append(error_msg)
            return result
    
    def _get_target_context_file_path(self, date) -> Path:
        """获取目标上下文文件路径"""
        if isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        
        year = str(date.year)
        month = f"{date.month:02d}"
        filename = f"contexts_{date.strftime('%Y-%m-%d')}.csv"
        
        return self.new_comprehensive_logs_dir / "contexts" / year / month / filename

    def _migrate_trade_settlements(self, settlement_file: Path, dry_run: bool = False) -> Dict[str, Any]:
        """迁移交易结算数据（合并到交易数据中）"""
        result = {'migrated_count': 0, 'errors': []}

        try:
            df = pd.read_csv(settlement_file)

            if df.empty:
                return result

            # 转换为交易数据格式
            trades_data = []
            for _, row in df.iterrows():
                trade_data = {
                    'trade_id': row.get('trade_id'),
                    'entry_timestamp': row.get('entry_time'),
                    'exit_timestamp': row.get('exit_time'),
                    'target_name': 'Settlement_Import',
                    'symbol': row.get('symbol', 'BTCUSDT'),
                    'direction': row.get('direction'),
                    'entry_price': row.get('entry_price'),
                    'exit_price': row.get('exit_price'),
                    'amount': row.get('amount_staked'),
                    'payout_ratio': row.get('payout_ratio'),
                    'result': 'WIN' if row.get('status') == 'WON' else 'LOSS',
                    'profit_loss': row.get('profit_loss'),
                    'exit_reason': 'settlement_import',
                    'entry_market_snapshot_json': row.get('entry_market_snapshot_json'),
                    'exit_market_snapshot_json': row.get('exit_market_snapshot_json'),
                    'related_prediction_id': row.get('related_prediction_id')
                }
                trades_data.append(trade_data)

            if trades_data and not dry_run:
                # 转换为DataFrame并按日期分组写入
                trades_df = pd.DataFrame(trades_data)
                trades_df['entry_timestamp'] = pd.to_datetime(trades_df['entry_timestamp'], errors='coerce')
                trades_df = trades_df.dropna(subset=['entry_timestamp'])

                for date, group in trades_df.groupby(trades_df['entry_timestamp'].dt.date):
                    target_file = self._get_target_trading_file_path_by_date(date)
                    target_file.parent.mkdir(parents=True, exist_ok=True)

                    if target_file.exists():
                        group.to_csv(target_file, mode='a', header=False, index=False)
                    else:
                        group.to_csv(target_file, index=False)

            result['migrated_count'] = len(trades_data)
            return result

        except Exception as e:
            error_msg = f"迁移交易结算失败: {e}"
            result['errors'].append(error_msg)
            return result

    def _get_target_trading_file_path_by_date(self, date) -> Path:
        """根据日期获取目标交易文件路径"""
        if isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()

        year = str(date.year)
        month = f"{date.month:02d}"
        filename = f"trades_{date.strftime('%Y-%m-%d')}.csv"

        return self.new_comprehensive_logs_dir / "trades" / year / month / filename

    def _migrate_failure_analysis(self, failure_file: Path, dry_run: bool = False) -> Dict[str, Any]:
        """迁移失败分析数据"""
        result = {'migrated_count': 0, 'errors': []}

        try:
            df = pd.read_csv(failure_file)

            if df.empty:
                return result

            if not dry_run:
                # 直接复制到分析目录
                target_file = self.new_comprehensive_logs_dir / "analysis" / "failure_patterns.csv"
                df.to_csv(target_file, index=False)

            result['migrated_count'] = len(df)
            return result

        except Exception as e:
            error_msg = f"迁移失败分析失败: {e}"
            result['errors'].append(error_msg)
            return result

    def _backup_old_data(self):
        """备份旧数据"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = Path(f"backup_logs_{timestamp}")

        try:
            if self.old_trading_logs_dir.exists():
                shutil.copytree(self.old_trading_logs_dir, backup_dir / "trading_logs_unified")
                self.logger.info(f"交易日志已备份到: {backup_dir / 'trading_logs_unified'}")

            if self.old_analysis_logs_dir.exists():
                shutil.copytree(self.old_analysis_logs_dir, backup_dir / "analysis_logs")
                self.logger.info(f"分析日志已备份到: {backup_dir / 'analysis_logs'}")

        except Exception as e:
            self.logger.error(f"备份数据失败: {e}")
            raise

    def _generate_migration_report(self) -> Dict[str, Any]:
        """生成迁移报告"""
        duration = None
        if self.migration_stats['start_time'] and self.migration_stats['end_time']:
            duration = (self.migration_stats['end_time'] - self.migration_stats['start_time']).total_seconds()

        return {
            'migration_summary': {
                'start_time': self.migration_stats['start_time'].isoformat() if self.migration_stats['start_time'] else None,
                'end_time': self.migration_stats['end_time'].isoformat() if self.migration_stats['end_time'] else None,
                'duration_seconds': duration,
                'trades_migrated': self.migration_stats['trades_migrated'],
                'contexts_migrated': self.migration_stats['contexts_migrated'],
                'analysis_migrated': self.migration_stats['analysis_migrated'],
                'total_records_migrated': (
                    self.migration_stats['trades_migrated'] +
                    self.migration_stats['contexts_migrated'] +
                    self.migration_stats['analysis_migrated']
                ),
                'errors_count': self.migration_stats['errors']
            },
            'source_directories': {
                'trading_logs': str(self.old_trading_logs_dir),
                'analysis_logs': str(self.old_analysis_logs_dir)
            },
            'target_directory': str(self.new_comprehensive_logs_dir),
            'migration_status': 'completed' if self.migration_stats['errors'] == 0 else 'completed_with_errors'
        }

    def _save_migration_report(self, report: Dict[str, Any]):
        """保存迁移报告"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = self.new_comprehensive_logs_dir / f"migration_report_{timestamp}.json"

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"迁移报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"保存迁移报告失败: {e}")

    def verify_migration(self) -> Dict[str, Any]:
        """验证迁移结果"""
        verification_result = {
            'trades_verification': {},
            'contexts_verification': {},
            'analysis_verification': {},
            'overall_status': 'unknown'
        }

        try:
            # 验证交易数据
            trades_verification = self._verify_trades_migration()
            verification_result['trades_verification'] = trades_verification

            # 验证上下文数据
            contexts_verification = self._verify_contexts_migration()
            verification_result['contexts_verification'] = contexts_verification

            # 验证分析数据
            analysis_verification = self._verify_analysis_migration()
            verification_result['analysis_verification'] = analysis_verification

            # 总体状态
            all_passed = (
                trades_verification.get('status') == 'passed' and
                contexts_verification.get('status') == 'passed' and
                analysis_verification.get('status') == 'passed'
            )

            verification_result['overall_status'] = 'passed' if all_passed else 'failed'

            return verification_result

        except Exception as e:
            self.logger.error(f"迁移验证失败: {e}")
            verification_result['overall_status'] = 'error'
            verification_result['error'] = str(e)
            return verification_result

    def _verify_trades_migration(self) -> Dict[str, Any]:
        """验证交易数据迁移"""
        try:
            # 统计原始数据
            original_count = 0
            if self.old_trading_logs_dir.exists():
                for csv_file in self.old_trading_logs_dir.rglob("*.csv"):
                    try:
                        df = pd.read_csv(csv_file)
                        original_count += len(df)
                    except Exception:
                        continue

            # 统计迁移后数据
            migrated_count = 0
            trades_dir = self.new_comprehensive_logs_dir / "trades"
            if trades_dir.exists():
                for csv_file in trades_dir.rglob("*.csv"):
                    try:
                        df = pd.read_csv(csv_file)
                        migrated_count += len(df)
                    except Exception:
                        continue

            return {
                'status': 'passed' if migrated_count >= original_count * 0.95 else 'failed',
                'original_count': original_count,
                'migrated_count': migrated_count,
                'coverage_percentage': round(migrated_count / original_count * 100, 2) if original_count > 0 else 0
            }

        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def _verify_contexts_migration(self) -> Dict[str, Any]:
        """验证上下文数据迁移"""
        try:
            # 统计原始数据
            original_count = 0
            prediction_context_file = self.old_analysis_logs_dir / "prediction_context.csv"
            if prediction_context_file.exists():
                try:
                    df = pd.read_csv(prediction_context_file)
                    original_count = len(df)
                except Exception:
                    pass

            # 统计迁移后数据
            migrated_count = 0
            contexts_dir = self.new_comprehensive_logs_dir / "contexts"
            if contexts_dir.exists():
                for csv_file in contexts_dir.rglob("*.csv"):
                    try:
                        df = pd.read_csv(csv_file)
                        migrated_count += len(df)
                    except Exception:
                        continue

            return {
                'status': 'passed' if migrated_count >= original_count * 0.95 else 'failed',
                'original_count': original_count,
                'migrated_count': migrated_count,
                'coverage_percentage': round(migrated_count / original_count * 100, 2) if original_count > 0 else 0
            }

        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def _verify_analysis_migration(self) -> Dict[str, Any]:
        """验证分析数据迁移"""
        try:
            # 统计原始数据
            original_count = 0
            failure_analysis_file = self.old_analysis_logs_dir / "failure_analysis_summary.csv"
            if failure_analysis_file.exists():
                try:
                    df = pd.read_csv(failure_analysis_file)
                    original_count = len(df)
                except Exception:
                    pass

            # 统计迁移后数据
            migrated_count = 0
            analysis_dir = self.new_comprehensive_logs_dir / "analysis"
            if analysis_dir.exists():
                for csv_file in analysis_dir.rglob("*.csv"):
                    try:
                        df = pd.read_csv(csv_file)
                        migrated_count += len(df)
                    except Exception:
                        continue

            return {
                'status': 'passed' if migrated_count >= original_count * 0.95 else 'failed',
                'original_count': original_count,
                'migrated_count': migrated_count,
                'coverage_percentage': round(migrated_count / original_count * 100, 2) if original_count > 0 else 0
            }

        except Exception as e:
            return {'status': 'error', 'error': str(e)}
