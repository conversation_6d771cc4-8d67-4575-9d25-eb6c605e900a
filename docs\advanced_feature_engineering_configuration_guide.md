# 高级特征工程配置指南

## 📋 配置概览

### 默认启用状态

**重要**: 所有高级特征工程功能默认都是 **禁用** 状态，需要手动配置启用。

```python
# 默认状态
enable_advanced_feature_engineering = False  # 主开关：禁用
enable_shap_feature_filtering = True         # SHAP过滤：启用（但仅在特征>50时生效）
```

### 配置层级

高级特征工程采用 **目标级别配置**（target_config），不是全局配置。每个交易目标可以独立配置。

## 🚀 1. 基础启用方式

### 最简单的启用方法

```python
# 在目标配置中添加
target_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    
    # 🔑 主开关：启用高级特征工程
    'enable_advanced_feature_engineering': True,
    
    # 其他必要配置
    'enable_ta': True,        # 必需：技术指标
    'enable_volume': True,    # 推荐：成交量特征
    'enable_derivatives': True  # 可选：聪明钱特征需要
}
```

### 详细配置方式

```python
target_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    
    # 🔑 主开关
    'enable_advanced_feature_engineering': True,
    
    # 🎛️ 详细配置
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,   # 高阶特征
        'enable_interaction_features': True,    # 交互特征
        'enable_smart_money_features': True,    # 聪明钱特征
        'higher_order_smoothing': 3,            # 高阶特征平滑周期
        'interaction_threshold': 0.1,           # 交互特征阈值
        'smart_money_lookback': 14              # 聪明钱回看周期
    },
    
    # 🔬 SHAP特征选择配置
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.001,
    
    # 基础依赖配置
    'enable_ta': True,
    'enable_volume': True,
    'enable_derivatives': True
}
```

## 📊 2. 配置参数详解

### 主开关参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable_advanced_feature_engineering` | bool | `False` | 高级特征工程主开关 |
| `enable_shap_feature_filtering` | bool | `True` | SHAP特征过滤开关 |

### 高阶特征参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐范围 |
|--------|------|--------|------|----------|
| `enable_higher_order_features` | bool | `True` | 启用高阶特征 | - |
| `higher_order_smoothing` | int | `3` | 平滑周期 | 1-7 |

**高阶特征说明**:
- 生成速度、加速度、动量强度特征
- 支持RSI、MACD、ADX、ATR等指标
- 平滑周期越大越稳定，越小越敏感

### 交互特征参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐范围 |
|--------|------|--------|------|----------|
| `enable_interaction_features` | bool | `True` | 启用交互特征 | - |
| `interaction_threshold` | float | `0.1` | 相关性阈值 | 0.05-0.2 |

**交互特征说明**:
- 生成波动率-趋势、量价、动量共振等组合特征
- 阈值越小生成特征越多，越大越精选

### 聪明钱特征参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐范围 |
|--------|------|--------|------|----------|
| `enable_smart_money_features` | bool | `True` | 启用聪明钱特征 | - |
| `smart_money_lookback` | int | `14` | 回看周期 | 7-21 |

**聪明钱特征说明**:
- 需要衍生品数据（大户vs散户多空比、资金费率）
- 回看周期影响背离强度计算的稳定性

### SHAP特征选择参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐范围 |
|--------|------|--------|------|----------|
| `enable_shap_feature_filtering` | bool | `True` | 启用SHAP过滤 | - |
| `shap_importance_threshold` | float | `0.001` | 重要性阈值 | 0.0005-0.005 |

**SHAP过滤说明**:
- 仅在特征数>50时自动启用
- 阈值越大过滤越严格，特征越少

## 🎯 3. 不同策略配置模板

### 保守策略（稳定优先）

```python
conservative_config = {
    'name': 'BTC_5M_Conservative',
    'interval': '5m',
    
    # 主开关
    'enable_advanced_feature_engineering': True,
    
    # 保守配置
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,   # 启用：计算成本低，效果好
        'enable_interaction_features': False,   # 禁用：减少复杂性
        'enable_smart_money_features': True,    # 启用：高价值特征
        'higher_order_smoothing': 5,            # 更多平滑，减少噪音
        'smart_money_lookback': 21              # 更长周期，更稳定
    },
    
    # 严格的SHAP过滤
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.002,         # 更严格阈值
    
    # 基础配置
    'enable_ta': True,
    'enable_volume': True,
    'enable_derivatives': True
}
```

### 平衡策略（推荐配置）

```python
balanced_config = {
    'name': 'BTC_5M_Balanced',
    'interval': '5m',
    
    # 主开关
    'enable_advanced_feature_engineering': True,
    
    # 平衡配置（默认值）
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,
        'enable_interaction_features': True,
        'enable_smart_money_features': True,
        'higher_order_smoothing': 3,            # 标准平滑
        'interaction_threshold': 0.1,           # 标准阈值
        'smart_money_lookback': 14              # 标准周期
    },
    
    # 标准SHAP过滤
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.001,         # 标准阈值
    
    # 基础配置
    'enable_ta': True,
    'enable_volume': True,
    'enable_derivatives': True
}
```

### 激进策略（最大信息）

```python
aggressive_config = {
    'name': 'BTC_5M_Aggressive',
    'interval': '5m',
    
    # 主开关
    'enable_advanced_feature_engineering': True,
    
    # 激进配置
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,
        'enable_interaction_features': True,
        'enable_smart_money_features': True,
        'higher_order_smoothing': 1,            # 最少平滑，最大敏感性
        'interaction_threshold': 0.05,          # 更低阈值，更多特征
        'smart_money_lookback': 7               # 更短周期，更快响应
    },
    
    # 宽松的SHAP过滤
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.0005,        # 更宽松阈值
    
    # 基础配置
    'enable_ta': True,
    'enable_volume': True,
    'enable_derivatives': True
}
```

### 高性能策略（计算资源优化）

```python
performance_config = {
    'name': 'BTC_5M_Performance',
    'interval': '5m',
    
    # 主开关
    'enable_advanced_feature_engineering': True,
    
    # 性能优化配置
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,   # 低成本，高效果
        'enable_interaction_features': False,   # 高成本，暂时禁用
        'enable_smart_money_features': True,    # 中等成本，高价值
        'higher_order_smoothing': 3,
        'smart_money_lookback': 14
    },
    
    # 适中的SHAP过滤
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.001,
    
    # 基础配置
    'enable_ta': True,
    'enable_volume': True,
    'enable_derivatives': False              # 如果不需要聪明钱特征
}
```

## 🔄 4. 安全启用步骤

### 步骤1：备份现有配置

```python
# 备份当前工作的目标配置
backup_config = current_target_config.copy()
```

### 步骤2：渐进式启用

```python
# 第一步：只启用高阶特征（最安全）
phase1_config = current_target_config.copy()
phase1_config.update({
    'enable_advanced_feature_engineering': True,
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,
        'enable_interaction_features': False,
        'enable_smart_money_features': False,
        'higher_order_smoothing': 5  # 保守的平滑设置
    }
})

# 测试运行，确认无问题后进入第二步
```

```python
# 第二步：添加交互特征
phase2_config = phase1_config.copy()
phase2_config['advanced_feature_engineering'].update({
    'enable_interaction_features': True,
    'interaction_threshold': 0.15  # 保守阈值
})

# 测试运行，确认无问题后进入第三步
```

```python
# 第三步：添加聪明钱特征（需要衍生品数据）
phase3_config = phase2_config.copy()
phase3_config.update({
    'enable_derivatives': True  # 确保衍生品数据可用
})
phase3_config['advanced_feature_engineering'].update({
    'enable_smart_money_features': True,
    'smart_money_lookback': 21  # 保守周期
})
```

### 步骤3：启用SHAP过滤

```python
# 最后启用SHAP特征选择
final_config = phase3_config.copy()
final_config.update({
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.002  # 保守阈值
})
```

### 步骤4：监控和调优

```python
# 监控特征数量变化
print(f"原始特征数: {original_feature_count}")
print(f"高级特征后: {enhanced_feature_count}")
print(f"SHAP过滤后: {final_feature_count}")

# 监控模型性能
# 比较启用前后的回测结果
```

## 🔗 5. 依赖关系图

```
高级特征工程依赖关系:

基础数据 (OHLCV)
    ↓
技术指标 (enable_ta=True)
    ↓
高阶特征 ← 依赖技术指标
    ↓
交互特征 ← 依赖技术指标 + 成交量特征
    ↓
聪明钱特征 ← 依赖衍生品数据 (enable_derivatives=True)
    ↓
SHAP过滤 ← 依赖所有特征
```

### 必需依赖

| 功能模块 | 必需依赖 | 可选依赖 |
|----------|----------|----------|
| 高阶特征 | `enable_ta=True` | - |
| 交互特征 | `enable_ta=True` | `enable_volume=True` |
| 聪明钱特征 | `enable_derivatives=True` | `enable_ta=True` |
| SHAP过滤 | 任意特征 | - |

### 衍生品数据依赖

聪明钱特征需要以下衍生品数据列：

```python
# 必需的衍生品数据列
required_columns = [
    'futures_top_longshort_account_ratio',    # 大户多空比
    'futures_global_longshort_ratio',         # 全市场多空比
    'funding_rate'                            # 资金费率（可选）
]

# 检查数据可用性
def check_smart_money_data_availability(df):
    top_trader_cols = [col for col in df.columns if 'top_longshort' in col.lower()]
    global_ratio_cols = [col for col in df.columns if 'global_longshort' in col.lower()]
    
    if not top_trader_cols or not global_ratio_cols:
        print("⚠️ 警告：缺少聪明钱特征所需的衍生品数据")
        return False
    return True
```

## ⚙️ 6. 配置文件示例

### 在现有目标配置中添加

```python
# 假设你有现有的目标配置
PREDICTION_TARGETS = [
    {
        'name': 'BTC_UP_5M',
        'interval': '5m',
        'target_variable_type': 'UP',
        'target_threshold': 0.01,
        
        # 现有配置...
        'enable_ta': True,
        'enable_volume': True,
        
        # 🚀 新增：高级特征工程配置
        'enable_advanced_feature_engineering': True,
        'advanced_feature_engineering': {
            'enable_higher_order_features': True,
            'enable_interaction_features': True,
            'enable_smart_money_features': True,
            'higher_order_smoothing': 3,
            'interaction_threshold': 0.1,
            'smart_money_lookback': 14
        },
        
        # 🔬 新增：SHAP特征选择配置
        'enable_shap_feature_filtering': True,
        'shap_importance_threshold': 0.001,
        
        # 确保依赖可用
        'enable_derivatives': True
    }
]
```

### 创建配置模板函数

```python
def create_advanced_feature_config(base_config, strategy='balanced'):
    """
    创建高级特征工程配置模板
    
    Args:
        base_config: 基础目标配置
        strategy: 策略类型 ('conservative', 'balanced', 'aggressive', 'performance')
    """
    config = base_config.copy()
    
    # 策略配置映射
    strategy_configs = {
        'conservative': {
            'enable_higher_order_features': True,
            'enable_interaction_features': False,
            'enable_smart_money_features': True,
            'higher_order_smoothing': 5,
            'smart_money_lookback': 21,
            'shap_threshold': 0.002
        },
        'balanced': {
            'enable_higher_order_features': True,
            'enable_interaction_features': True,
            'enable_smart_money_features': True,
            'higher_order_smoothing': 3,
            'interaction_threshold': 0.1,
            'smart_money_lookback': 14,
            'shap_threshold': 0.001
        },
        'aggressive': {
            'enable_higher_order_features': True,
            'enable_interaction_features': True,
            'enable_smart_money_features': True,
            'higher_order_smoothing': 1,
            'interaction_threshold': 0.05,
            'smart_money_lookback': 7,
            'shap_threshold': 0.0005
        },
        'performance': {
            'enable_higher_order_features': True,
            'enable_interaction_features': False,
            'enable_smart_money_features': True,
            'higher_order_smoothing': 3,
            'smart_money_lookback': 14,
            'shap_threshold': 0.001
        }
    }
    
    strategy_config = strategy_configs.get(strategy, strategy_configs['balanced'])
    
    # 应用配置
    config.update({
        'enable_advanced_feature_engineering': True,
        'advanced_feature_engineering': {
            'enable_higher_order_features': strategy_config['enable_higher_order_features'],
            'enable_interaction_features': strategy_config['enable_interaction_features'],
            'enable_smart_money_features': strategy_config['enable_smart_money_features'],
            'higher_order_smoothing': strategy_config['higher_order_smoothing'],
            'interaction_threshold': strategy_config.get('interaction_threshold', 0.1),
            'smart_money_lookback': strategy_config['smart_money_lookback']
        },
        'enable_shap_feature_filtering': True,
        'shap_importance_threshold': strategy_config['shap_threshold'],
        'enable_derivatives': strategy_config['enable_smart_money_features']
    })
    
    return config

# 使用示例
enhanced_config = create_advanced_feature_config(
    base_config=existing_target_config,
    strategy='balanced'
)
```

## 🔍 7. 验证和监控

### 配置验证

```python
def validate_advanced_feature_config(target_config):
    """验证高级特征工程配置"""
    issues = []
    
    # 检查主开关
    if not target_config.get('enable_advanced_feature_engineering', False):
        return ["高级特征工程未启用"]
    
    # 检查依赖
    if not target_config.get('enable_ta', False):
        issues.append("缺少技术指标依赖 (enable_ta=False)")
    
    advanced_config = target_config.get('advanced_feature_engineering', {})
    
    # 检查聪明钱特征依赖
    if advanced_config.get('enable_smart_money_features', False):
        if not target_config.get('enable_derivatives', False):
            issues.append("聪明钱特征需要衍生品数据 (enable_derivatives=False)")
    
    # 检查参数范围
    smoothing = advanced_config.get('higher_order_smoothing', 3)
    if smoothing < 1 or smoothing > 10:
        issues.append(f"平滑周期超出推荐范围: {smoothing} (推荐1-10)")
    
    threshold = target_config.get('shap_importance_threshold', 0.001)
    if threshold < 0.0001 or threshold > 0.01:
        issues.append(f"SHAP阈值超出推荐范围: {threshold} (推荐0.0001-0.01)")
    
    return issues

# 使用示例
issues = validate_advanced_feature_config(target_config)
if issues:
    print("⚠️ 配置问题:")
    for issue in issues:
        print(f"  - {issue}")
else:
    print("✅ 配置验证通过")
```

### 运行时监控

```python
def monitor_advanced_features(df_before, df_after, target_name):
    """监控高级特征工程效果"""
    original_count = len(df_before.columns)
    final_count = len(df_after.columns)
    new_features = final_count - original_count
    
    print(f"📊 {target_name} 高级特征工程监控:")
    print(f"  原始特征: {original_count}")
    print(f"  最终特征: {final_count}")
    print(f"  新增特征: {new_features}")
    
    # 检查数据质量
    nan_count = df_after.isnull().sum().sum()
    inf_count = np.isinf(df_after.select_dtypes(include=[np.number])).sum().sum()
    
    print(f"  数据质量: NaN={nan_count}, Inf={inf_count}")
    
    if nan_count > len(df_after) * 0.1:
        print("  ⚠️ 警告: NaN值过多")
    
    if inf_count > 0:
        print("  ⚠️ 警告: 存在无穷大值")
    
    return {
        'original_features': original_count,
        'final_features': final_count,
        'new_features': new_features,
        'nan_count': nan_count,
        'inf_count': inf_count
    }
```

## 📝 8. 快速启用检查清单

### 启用前检查

- [ ] 确认基础依赖已启用 (`enable_ta=True`)
- [ ] 如需聪明钱特征，确认 `enable_derivatives=True`
- [ ] 备份现有配置
- [ ] 选择合适的策略模板

### 启用步骤

- [ ] 第一步：启用高阶特征，测试运行
- [ ] 第二步：启用交互特征，测试运行
- [ ] 第三步：启用聪明钱特征，测试运行
- [ ] 第四步：启用SHAP过滤，测试运行

### 启用后验证

- [ ] 检查特征数量变化是否合理
- [ ] 验证数据质量（NaN、Inf值）
- [ ] 监控模型训练时间变化
- [ ] 对比回测性能指标

### 常见问题排查

- [ ] 如果特征过多：提高SHAP阈值或禁用交互特征
- [ ] 如果聪明钱特征缺失：检查衍生品数据可用性
- [ ] 如果训练时间过长：使用性能优化配置
- [ ] 如果效果不佳：尝试不同策略模板

## 🎯 总结

高级特征工程系统提供了强大而灵活的特征增强能力：

1. **默认禁用**: 所有功能默认关闭，确保现有系统安全
2. **目标级配置**: 每个交易目标可独立配置，灵活性高
3. **渐进式启用**: 分步骤安全启用，降低风险
4. **策略模板**: 提供多种预设配置，适应不同需求
5. **完整监控**: 全面的验证和监控机制

通过这个配置指南，您可以安全、有效地将高级特征工程集成到现有交易系统中，显著提升模型性能。
