#!/usr/bin/env python3
"""
增强样本权重和数据质量监控测试脚本
验证优化建议三的实现效果
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_data(n_samples=1000):
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 生成时间序列
    dates = pd.date_range(start='2024-01-01', periods=n_samples, freq='15min')
    
    # 生成价格数据（带趋势和波动）
    np.random.seed(42)
    price_base = 50000
    trend = np.cumsum(np.random.randn(n_samples) * 0.001)
    noise = np.random.randn(n_samples) * 0.02
    
    close_prices = price_base * (1 + trend + noise)
    high_prices = close_prices * (1 + np.abs(np.random.randn(n_samples)) * 0.01)
    low_prices = close_prices * (1 - np.abs(np.random.randn(n_samples)) * 0.01)
    open_prices = close_prices + np.random.randn(n_samples) * 100
    
    # 生成成交量数据（带周期性）
    volume_base = 1000
    volume_cycle = np.sin(np.arange(n_samples) * 2 * np.pi / 96) * 0.3  # 24小时周期
    volume_noise = np.random.randn(n_samples) * 0.2
    volumes = volume_base * (1 + volume_cycle + volume_noise)
    volumes = np.maximum(volumes, 100)  # 确保正值
    
    # 创建DataFrame
    df = pd.DataFrame({
        'timestamp': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    })
    
    print(f"✅ 测试数据创建完成: {df.shape}")
    return df

def create_test_features_and_target(df):
    """创建测试特征和目标变量"""
    print("🔧 创建测试特征和目标变量...")
    
    # 创建一些基础特征
    features = pd.DataFrame(index=df.index)
    
    # 价格特征
    features['price_change'] = df['close'].pct_change()
    features['price_ma_5'] = df['close'].rolling(5).mean()
    features['price_ma_20'] = df['close'].rolling(20).mean()
    features['price_std_20'] = df['close'].rolling(20).std()
    
    # 成交量特征
    features['volume_ma_20'] = df['volume'].rolling(20).mean()
    features['volume_ratio'] = df['volume'] / features['volume_ma_20']
    
    # 技术指标特征
    features['rsi'] = calculate_rsi(df['close'], 14)
    features['bb_upper'], features['bb_lower'] = calculate_bollinger_bands(df['close'], 20)
    
    # 添加一些常数特征（用于测试质量监控）
    features['constant_feature_1'] = 1.0
    features['constant_feature_2'] = 0.0
    
    # 添加高相关特征（用于测试相关性检查）
    features['corr_feature_1'] = features['price_change'] + np.random.randn(len(features)) * 0.001
    features['corr_feature_2'] = features['price_change'] * 1.001 + np.random.randn(len(features)) * 0.001
    
    # 添加一些缺失值
    features.loc[features.index[:50], 'missing_feature'] = np.nan
    features.loc[features.index[50:], 'missing_feature'] = np.random.randn(len(features) - 50)
    
    # 创建目标变量（未来15分钟涨跌）
    future_return = df['close'].shift(-1) / df['close'] - 1
    target = (future_return > 0.002).astype(int)  # 0.2%阈值
    
    # 移除NaN
    valid_mask = ~(features.isnull().any(axis=1) | target.isnull())
    features_clean = features[valid_mask].fillna(0)
    target_clean = target[valid_mask]
    
    print(f"✅ 特征创建完成: {features_clean.shape}, 目标变量: {target_clean.shape}")
    print(f"📈 正样本比例: {target_clean.mean():.3f}")
    
    return features_clean, target_clean

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带"""
    ma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = ma + (std * std_dev)
    lower_band = ma - (std * std_dev)
    return upper_band, lower_band

def test_data_quality_monitor():
    """测试数据质量监控功能"""
    print("\n" + "="*60)
    print("🔍 测试数据质量监控功能")
    print("="*60)
    
    # 创建测试数据
    df = create_test_data(1000)
    X_train, y_train = create_test_features_and_target(df)
    
    try:
        from src.core.training_data_quality_monitor import perform_training_data_quality_check
        
        # 执行质量检查
        quality_report = perform_training_data_quality_check(
            X_train, y_train, "TEST_TARGET"
        )
        
        print(f"✅ 数据质量监控测试完成")
        print(f"📊 总体状态: {quality_report['overall_assessment']['status']}")
        print(f"⚠️  警告数量: {len(quality_report['warnings'])}")
        print(f"❌ 错误数量: {len(quality_report['errors'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据质量监控测试失败: {e}")
        return False

def test_enhanced_sample_weighting():
    """测试增强样本权重功能"""
    print("\n" + "="*60)
    print("⚖️ 测试增强样本权重功能")
    print("="*60)
    
    # 创建测试数据
    df = create_test_data(1000)
    
    try:
        from src.core.enhanced_sample_weighting import calculate_enhanced_sample_weights
        
        # 测试配置
        target_config = {
            'symbol': 'BTCUSDT',
            'interval': '15m'
        }
        
        weighting_config = {
            'enable_time_decay': True,
            'enable_volatility_weighting': True,
            'enable_volume_weighting': True,
            'enable_market_state_weighting': False,  # 暂时禁用，避免依赖问题
            
            'combination_method': 'weighted_average',
            'strategy_weights': {
                'time_decay': 0.4,
                'volatility': 0.4,
                'volume': 0.2,
                'market_state': 0.0
            }
        }
        
        # 计算权重
        weights = calculate_enhanced_sample_weights(
            df, target_config, "TEST_TARGET", weighting_config
        )
        
        print(f"✅ 增强样本权重计算完成")
        print(f"📊 权重形状: {weights.shape}")
        print(f"📈 权重范围: [{weights.min():.4f}, {weights.max():.4f}]")
        print(f"📊 权重均值: {weights.mean():.4f}")
        print(f"📊 权重标准差: {weights.std():.4f}")
        
        # 可视化权重分布
        plt.figure(figsize=(12, 8))
        
        # 子图1：权重时间序列
        plt.subplot(2, 2, 1)
        plt.plot(weights)
        plt.title('样本权重时间序列')
        plt.xlabel('样本索引')
        plt.ylabel('权重')
        
        # 子图2：权重分布直方图
        plt.subplot(2, 2, 2)
        plt.hist(weights, bins=50, alpha=0.7)
        plt.title('权重分布直方图')
        plt.xlabel('权重值')
        plt.ylabel('频次')
        
        # 子图3：权重与价格变化的关系
        plt.subplot(2, 2, 3)
        price_changes = df['close'].pct_change().fillna(0)
        plt.scatter(price_changes, weights, alpha=0.5)
        plt.title('权重 vs 价格变化')
        plt.xlabel('价格变化率')
        plt.ylabel('权重')
        
        # 子图4：权重与成交量的关系
        plt.subplot(2, 2, 4)
        volume_ratio = df['volume'] / df['volume'].rolling(20).mean()
        plt.scatter(volume_ratio.fillna(1), weights, alpha=0.5)
        plt.title('权重 vs 成交量比率')
        plt.xlabel('成交量比率')
        plt.ylabel('权重')
        
        plt.tight_layout()
        plt.savefig('enhanced_sample_weights_analysis.png', dpi=300, bbox_inches='tight')
        print(f"📊 权重分析图已保存: enhanced_sample_weights_analysis.png")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强样本权重测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证功能"""
    print("\n" + "="*60)
    print("🔧 测试配置验证功能")
    print("="*60)
    
    try:
        from config.enhanced_sample_weighting_config import (
            get_enhanced_weighting_config, 
            validate_enhanced_weighting_config
        )
        
        # 测试预设配置
        configs_to_test = [
            ('bull_market', 'short_term'),
            ('bear_market', 'medium_term'),
            ('sideways_market', 'long_term'),
            ('normal', 'medium_term')
        ]
        
        for market_condition, timeframe in configs_to_test:
            config = get_enhanced_weighting_config(market_condition, timeframe)
            is_valid, errors = validate_enhanced_weighting_config(config)
            
            status = "✅" if is_valid else "❌"
            print(f"{status} {market_condition}_{timeframe}: {'有效' if is_valid else f'无效 - {errors}'}")
        
        print(f"✅ 配置验证测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 增强样本权重和数据质量监控测试")
    print("="*60)
    
    test_results = []
    
    # 测试数据质量监控
    test_results.append(("数据质量监控", test_data_quality_monitor()))
    
    # 测试增强样本权重
    test_results.append(("增强样本权重", test_enhanced_sample_weighting()))
    
    # 测试配置验证
    test_results.append(("配置验证", test_config_validation()))
    
    # 总结测试结果
    print("\n" + "="*60)
    print("📋 测试结果总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！增强样本权重和数据质量监控功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")

if __name__ == "__main__":
    main()
