# 统一交易日志系统集成指南

## 🎯 核心特性

✅ **一笔交易一行记录** - 开仓和平仓数据合并到单行  
✅ **4个模型概率完整记录** - UP模型、DOWN模型、LSTM模型、元模型  
✅ **异步写入不阻塞** - 后台线程处理文件写入  
✅ **完整上下文捕获** - 决策时刻的所有关键信息  
✅ **线程安全操作** - 支持多线程并发访问  
✅ **CSV格式输出** - 标准格式便于分析  

## 📁 文件结构

```
src/core/unified_trade_logger.py  # 核心日志系统
trading_logs_unified/             # 日志输出目录
├── 2025/
│   └── 07/
│       └── trades_2025-07-06.csv
```

## 🔧 在prediction.py中的集成

### 1. 导入模块

```python
from src.core.unified_trade_logger import get_unified_trade_logger
```

### 2. 获取日志记录器

```python
# 在预测函数开始处
unified_logger = get_unified_trade_logger(
    base_log_dir="trading_logs_unified",
    auto_start=True
)
```

### 3. 记录基础模型交易开仓

```python
if should_send_signal_this_time:
    # 构建上下文数据
    context_data = {
        'entry_signal_probability': model_positive_class_prob,
        'entry_neutral_probability': 1.0 - avg_up_prob_value - avg_down_prob_value,
        'entry_opposite_probability': avg_down_prob_value if signal == "UP" else avg_up_prob_value,
        
        # 4个模型概率（核心要求！）
        'individual_model_probabilities': {
            f'{model_name}_up_prob': float(avg_up_prob_value),
            f'{model_name}_down_prob': float(avg_down_prob_value)
        },
        
        # 市场状态
        'entry_market_regime': 'normal',
        'entry_atr_percent': atr_percent,
        'entry_adx_value': latest_adx,
        
        # 特征快照
        'entry_top_features': top_features_dict,
        'meta_model_inputs': meta_inputs_dict
    }
    
    # 记录开仓（暂存到内存）
    trade_id = unified_logger.record_trade_entry(
        target_name=target_name,
        symbol=symbol_to_use,
        direction="LONG" if signal == "UP" else "SHORT",
        entry_price=current_price,
        amount=trade_amount,
        payout_ratio=0.85,
        context_data=context_data
    )
```

### 4. 记录元模型交易开仓

```python
if should_send_meta_signal:
    # 元模型上下文数据
    meta_context_data = {
        'entry_signal_probability': float(meta_model_probas[1]) if signal == "UP" else float(meta_model_probas[0]),
        'entry_neutral_probability': float(meta_model_probas[2]) if len(meta_model_probas) == 3 else 0.0,
        'entry_opposite_probability': float(meta_model_probas[0]) if signal == "UP" else float(meta_model_probas[1]),
        
        # 4个模型概率（完整版本）
        'individual_model_probabilities': {
            'UP_model_up_prob': up_model_up_prob,
            'UP_model_down_prob': up_model_down_prob,
            'DOWN_model_up_prob': down_model_up_prob,
            'DOWN_model_down_prob': down_model_down_prob,
            'LSTM_model_up_prob': lstm_up_prob,
            'LSTM_model_down_prob': lstm_down_prob,
            'Meta_model_up_prob': float(meta_model_probas[1]),
            'Meta_model_down_prob': float(meta_model_probas[0])
        },
        
        # 基础模型预测
        'base_model_predictions': base_predictions_dict,
        
        # 市场状态
        'entry_market_regime': global_market_state.get('market_regime', 'normal'),
        'entry_atr_percent': global_market_state.get('atr_percent', 0.0),
        'entry_adx_value': global_market_state.get('adx_value', 0.0),
        
        # 元模型输入
        'meta_model_inputs': meta_input_features_dict
    }
    
    # 记录元模型开仓
    meta_trade_id = unified_logger.record_trade_entry(
        target_name="MetaModel_Ensemble",
        symbol="BTCUSDT",
        direction="LONG" if signal == "UP" else "SHORT",
        entry_price=current_price,
        amount=meta_trade_amount,
        payout_ratio=0.85,
        context_data=meta_context_data
    )
```

## 🔧 在模拟器中的集成

### 记录交易平仓

```python
# 在SimTrading.py或相关模拟器代码中
from src.core.unified_trade_logger import get_unified_trade_logger

def close_trade(trade_id: str, exit_price: float, is_win: bool, exit_reason: str = "expired"):
    """平仓交易并记录"""
    unified_logger = get_unified_trade_logger()
    
    result = "WIN" if is_win else "LOSS"
    
    success = unified_logger.record_trade_exit(
        trade_id=trade_id,
        exit_price=exit_price,
        result=result,
        exit_reason=exit_reason
    )
    
    return success

# 处理到期交易
for trade in expired_trades:
    current_price = get_current_price(trade['symbol'])
    
    # 判断盈亏
    if trade['direction'] == 'LONG':
        is_win = current_price > trade['entry_price']
    else:  # SHORT
        is_win = current_price < trade['entry_price']
    
    # 记录平仓
    close_trade(
        trade_id=trade['trade_id'],
        exit_price=current_price,
        is_win=is_win,
        exit_reason="expired"
    )
```

## 📊 CSV输出格式

每笔交易一行，包含以下字段：

| 字段 | 描述 | 示例 |
|------|------|------|
| trade_id | 唯一交易ID | BTC_15m_UP_1751770771709172_27584_d56793a2 |
| entry_timestamp | 开仓时间 | 2025-07-06T10:58:47.131697 |
| exit_timestamp | 平仓时间 | 2025-07-06T10:58:48.132840 |
| target_name | 策略名称 | BTC_15m_UP |
| symbol | 交易对 | BTCUSDT |
| direction | 交易方向 | LONG/SHORT |
| entry_price | 开仓价格 | 108500.0 |
| exit_price | 平仓价格 | 108800.0 |
| amount | 交易金额 | 50.0 |
| payout_ratio | 盈利比例 | 0.85 |
| result | 交易结果 | WIN/LOSS |
| profit_loss | 盈亏金额 | 42.5 |
| exit_reason | 平仓原因 | expired |
| entry_signal_probability | 信号概率 | 0.78 |
| entry_neutral_probability | 中性概率 | 0.12 |
| entry_opposite_probability | 反向概率 | 0.10 |
| individual_model_probabilities | **4个模型概率** | JSON格式 |
| base_model_predictions | 基础模型预测 | JSON格式 |
| entry_market_regime | 市场状态 | bullish_trend |
| entry_atr_percent | ATR百分比 | 1.5 |
| entry_adx_value | ADX值 | 32.1 |
| entry_top_features | 关键特征 | JSON格式 |
| meta_model_inputs | 元模型输入 | JSON格式 |

## 🎯 4个模型概率示例

**元模型交易的`individual_model_probabilities`字段：**

```json
{
  "UP_model_up_prob": 0.78,
  "UP_model_down_prob": 0.22,
  "DOWN_model_up_prob": 0.35,
  "DOWN_model_down_prob": 0.65,
  "LSTM_model_up_prob": 0.84,
  "LSTM_model_down_prob": 0.16,
  "Meta_model_up_prob": 0.91,
  "Meta_model_down_prob": 0.09
}
```

这正是用户要求的"**特别是当时4个模型的概率分别都是多少！！！！！**"

## 🔍 数据分析

```python
import pandas as pd
import json

# 读取日志文件
df = pd.read_csv('trading_logs_unified/2025/07/trades_2025-07-06.csv')

# 分析4个模型表现
for idx, row in df.iterrows():
    if pd.notna(row['individual_model_probabilities']):
        model_probs = json.loads(row['individual_model_probabilities'])
        
        print(f"交易 {row['trade_id']}:")
        for model in ['UP', 'DOWN', 'LSTM', 'Meta']:
            up_key = f'{model}_model_up_prob'
            down_key = f'{model}_model_down_prob'
            if up_key in model_probs:
                print(f"  {model}模型: {model_probs[up_key]:.1%} / {model_probs[down_key]:.1%}")
```

## ✅ 系统优势

1. **数据完整性** - 每笔交易的完整生命周期记录
2. **性能优化** - 异步写入不影响交易执行
3. **易于分析** - 标准CSV格式，支持pandas直接读取
4. **扩展性强** - 预留字段支持未来功能扩展
5. **线程安全** - 支持多策略并发运行

## 🚀 立即开始

1. 导入`UnifiedTradeLogger`
2. 在交易信号发送时调用`record_trade_entry()`
3. 在交易平仓时调用`record_trade_exit()`
4. 系统自动合并数据并异步写入CSV

**现在您的交易系统拥有了企业级的"全景驾驶舱"日志功能！**
