#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 配置迁移工具

自动检测和建议配置访问模式的迁移。
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


@dataclass
class ConfigIssue:
    """配置问题"""
    file_path: str
    line_number: int
    line_content: str
    issue_type: str
    suggestion: str


class ConfigMigrationAnalyzer:
    """配置迁移分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues: List[ConfigIssue] = []
        
        # 需要检查的模式
        self.patterns = {
            'direct_dict_access': [
                r"config\s*\[\s*['\"]([^'\"]+)['\"]\s*\]",
                r"target_config\s*\[\s*['\"]([^'\"]+)['\"]\s*\]",
            ],
            'dict_get_access': [
                r"config\.get\s*\(\s*['\"]([^'\"]+)['\"]",
                r"target_config\.get\s*\(\s*['\"]([^'\"]+)['\"]",
            ],
            'prediction_targets_access': [
                r"PREDICTION_TARGETS\s*\[\s*\d+\s*\]",
                r"next\s*\(\s*.*PREDICTION_TARGETS.*\)",
            ]
        }
    
    def analyze_file(self, file_path: Path) -> List[ConfigIssue]:
        """分析单个文件"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # 跳过注释行
                if line_stripped.startswith('#'):
                    continue
                
                # 检查各种模式
                for issue_type, patterns in self.patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, line):
                            suggestion = self._get_suggestion(issue_type, line)
                            issues.append(ConfigIssue(
                                file_path=str(file_path.relative_to(self.project_root)),
                                line_number=line_num,
                                line_content=line_stripped,
                                issue_type=issue_type,
                                suggestion=suggestion
                            ))
        
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
        
        return issues
    
    def _get_suggestion(self, issue_type: str, line: str) -> str:
        """获取修复建议"""
        suggestions = {
            'direct_dict_access': (
                "使用 wrapper.get_str('key', required=True) 替代 config['key']"
            ),
            'dict_get_access': (
                "使用 wrapper.get('key', default=value) 替代 config.get('key', value)"
            ),
            'prediction_targets_access': (
                "使用 get_target_config_wrapper(target_name) 替代直接访问 PREDICTION_TARGETS"
            )
        }
        return suggestions.get(issue_type, "考虑使用类型安全的配置访问方法")
    
    def analyze_project(self, exclude_dirs: Set[str] = None) -> List[ConfigIssue]:
        """分析整个项目"""
        if exclude_dirs is None:
            exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
        
        all_issues = []
        
        for py_file in self.project_root.rglob('*.py'):
            # 跳过排除的目录
            if any(excluded in py_file.parts for excluded in exclude_dirs):
                continue
            
            issues = self.analyze_file(py_file)
            all_issues.extend(issues)
        
        return all_issues
    
    def generate_report(self, issues: List[ConfigIssue]) -> str:
        """生成分析报告"""
        if not issues:
            return "🎉 没有发现需要迁移的配置访问模式！"
        
        report = ["🚀 配置迁移分析报告", "=" * 50, ""]
        
        # 按文件分组
        issues_by_file = {}
        for issue in issues:
            if issue.file_path not in issues_by_file:
                issues_by_file[issue.file_path] = []
            issues_by_file[issue.file_path].append(issue)
        
        # 统计信息
        total_issues = len(issues)
        total_files = len(issues_by_file)
        
        report.extend([
            f"📊 统计信息:",
            f"   - 发现问题: {total_issues} 个",
            f"   - 涉及文件: {total_files} 个",
            ""
        ])
        
        # 按问题类型统计
        issue_type_counts = {}
        for issue in issues:
            issue_type_counts[issue.issue_type] = issue_type_counts.get(issue.issue_type, 0) + 1
        
        report.extend(["📋 问题类型分布:"])
        for issue_type, count in issue_type_counts.items():
            report.append(f"   - {issue_type}: {count} 个")
        report.append("")
        
        # 详细问题列表
        report.extend(["📝 详细问题列表:", ""])
        
        for file_path, file_issues in issues_by_file.items():
            report.append(f"📁 {file_path}")
            report.append("-" * (len(file_path) + 2))
            
            for issue in file_issues:
                report.extend([
                    f"   行 {issue.line_number}: {issue.issue_type}",
                    f"   代码: {issue.line_content}",
                    f"   建议: {issue.suggestion}",
                    ""
                ])
        
        # 迁移建议
        report.extend([
            "🔧 迁移建议:",
            "",
            "1. 导入必要的函数:",
            "   from config import get_target_config_wrapper",
            "",
            "2. 替换配置获取:",
            "   # 旧: config = get_target_config(target_name)",
            "   # 新: wrapper = get_target_config_wrapper(target_name)",
            "",
            "3. 替换配置访问:",
            "   # 旧: value = config['key']",
            "   # 新: value = wrapper.get_str('key', required=True)",
            "",
            "4. 添加类型安全:",
            "   # 字符串: wrapper.get_str('key', default='')",
            "   # 数值:   wrapper.get_float('key', default=0.0)",
            "   # 布尔:   wrapper.get_bool('key', default=False)",
            "   # 列表:   wrapper.get_list('key', default=[])",
            "",
            "📚 参考文档: docs/config_management_best_practices.md"
        ])
        
        return "\n".join(report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置迁移分析工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--output", help="输出报告到文件")
    parser.add_argument("--exclude", nargs="*", default=[], help="排除的目录")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = ConfigMigrationAnalyzer(args.project_root)
    
    # 分析项目
    print("🔍 正在分析项目配置访问模式...")
    exclude_dirs = set(args.exclude) if args.exclude else None
    issues = analyzer.analyze_project(exclude_dirs)
    
    # 生成报告
    report = analyzer.generate_report(issues)
    
    # 输出报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 报告已保存到: {args.output}")
    else:
        print(report)
    
    # 返回退出码
    return 1 if issues else 0


if __name__ == "__main__":
    sys.exit(main())
