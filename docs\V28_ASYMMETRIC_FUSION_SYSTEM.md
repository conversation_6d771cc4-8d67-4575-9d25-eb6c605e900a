# V28.0 非对称融合决策体系升级文档

## 🌟 概述

V28.0是对V27.0融合决策体系的重大进化，实现了为上涨和下跌信号量身定制的非对称评分体系。这一升级基于对不同信号类型特性的深入理解，为每种信号方向提供了最优化的决策权重配置。

## 🎯 核心理念

### 上涨信号：更信赖"将军"的判断力
- **概率优势权重：60%** - 更看重元模型的高精度判断
- **基础共识权重：25%** - 适度参考基础模型共识
- **宏观顺势权重：15%** - 保持宏观环境感知
- **决策门槛：45.0分** - 相对较低，鼓励高质量上涨信号

### 下跌信号：更信赖"专家组"的共识
- **概率优势权重：40%** - 适度参考元模型判断
- **基础共识权重：45%** - 核心！更看重多方专家共识
- **宏观顺势权重：15%** - 保持宏观环境感知
- **决策门槛：50.0分** - 略高门槛，确保下跌信号质量

## 🔧 技术实现

### 1. 配置参数升级

在 `config.py` 中新增V28.0非对称配置：

```python
# === 上涨信号评分体系 (更信赖"将军"的判断力) ===
UP_WEIGHT_PROB_ADVANTAGE = 0.60        # 概率优势权重60%
UP_WEIGHT_CONSENSUS = 0.25              # 基础共识权重25%
UP_WEIGHT_MACRO = 0.15                  # 宏观顺势权重15%
UP_FINAL_DECISION_THRESHOLD = 45.0      # 决策门槛45分

# === 下跌信号评分体系 (更信赖"专家组"的共识) ===
DOWN_WEIGHT_PROB_ADVANTAGE = 0.40       # 概率优势权重40%
DOWN_WEIGHT_CONSENSUS = 0.45             # 基础共识权重45%
DOWN_WEIGHT_MACRO = 0.15                 # 宏观顺势权重15%
DOWN_FINAL_DECISION_THRESHOLD = 50.0     # 决策门槛50分
```

### 2. 决策逻辑升级

在 `src/core/prediction.py` 的 `_make_intelligent_meta_decision` 函数中：

```python
# 确定信号方向和对应的非对称权重配置
signal_direction = "UP" if p_up > p_down else "DOWN"

if signal_direction == "UP":
    # 上涨信号评分体系：更信赖"将军"的判断力
    WEIGHT_PROB_ADVANTAGE = getattr(config, 'UP_WEIGHT_PROB_ADVANTAGE', 0.60)
    WEIGHT_CONSENSUS = getattr(config, 'UP_WEIGHT_CONSENSUS', 0.25)
    WEIGHT_MACRO = getattr(config, 'UP_WEIGHT_MACRO', 0.15)
    FINAL_DECISION_THRESHOLD = getattr(config, 'UP_FINAL_DECISION_THRESHOLD', 45.0)
    system_type = "上涨专用评分体系"
else:
    # 下跌信号评分体系：更信赖"专家组"的共识
    WEIGHT_PROB_ADVANTAGE = getattr(config, 'DOWN_WEIGHT_PROB_ADVANTAGE', 0.40)
    WEIGHT_CONSENSUS = getattr(config, 'DOWN_WEIGHT_CONSENSUS', 0.45)
    WEIGHT_MACRO = getattr(config, 'DOWN_WEIGHT_MACRO', 0.15)
    FINAL_DECISION_THRESHOLD = getattr(config, 'DOWN_FINAL_DECISION_THRESHOLD', 50.0)
    system_type = "下跌专用评分体系"
```

### 3. GUI显示升级

更新了GUI显示以反映V28.0的非对称特性：

```python
if decision_details and decision_details.get('version') == 'V28.0':
    final_gui_text = (
        f"🤖 V28.0 非对称融合决策元模型决策 ({datetime.now(tz).strftime('%H:%M:%S')})\n"
        f"🎨 使用体系: {decision_details['system_type']}\n"
        # ... 其他显示内容
    )
```

## 📊 测试验证

### 测试结果摘要

✅ **配置参数验证**
- 上涨信号权重总和: 1.00 (正确)
- 下跌信号权重总和: 1.00 (正确)

✅ **决策逻辑验证**
- 上涨信号场景：使用上涨专用评分体系，权重配置60%+25%+15%
- 下跌信号场景：使用下跌专用评分体系，权重配置40%+45%+15%

✅ **非对称行为验证**
- 相同市场条件下，不同信号方向使用不同的权重配置
- 上涨信号门槛45.0分，下跌信号门槛50.0分

### 实际测试案例

**上涨信号测试 (p_up=0.70)**
- 使用体系：上涨专用评分体系
- 综合分数：49.5/100
- 决策门槛：45.0
- 最终信号：UP ✅

**下跌信号测试 (p_up=0.30)**
- 使用体系：下跌专用评分体系
- 综合分数：43.0/100
- 决策门槛：50.0
- 最终信号：Neutral ⏸️

## 🔄 向后兼容性

V28.0保持了对V27.0的完全向后兼容：

1. **配置兼容**：V27.0的配置参数仍然保留，标记为已废弃但可用
2. **GUI兼容**：支持V27.0和V28.0的显示格式
3. **逻辑兼容**：如果V28.0参数不可用，会回退到V27.0逻辑

## 🚀 优势与特点

### 1. 精准定制
- 为不同信号类型提供量身定制的评分体系
- 上涨信号更依赖元模型的精准判断
- 下跌信号更依赖基础模型的集体智慧

### 2. 风险控制
- 下跌信号使用更高的决策门槛（50.0 vs 45.0）
- 更严格的下跌信号过滤，减少假信号

### 3. 灵活配置
- 所有参数通过配置文件管理，避免硬编码
- 支持独立调整上涨和下跌信号的权重配置

### 4. 智能适应
- 系统自动根据信号方向选择对应的评分体系
- 无需人工干预，全自动智能切换

## 📈 预期效果

1. **提升上涨信号精度**：通过更重视元模型判断，减少上涨信号的误报
2. **增强下跌信号可靠性**：通过更重视基础模型共识，提高下跌信号的准确性
3. **优化风险收益比**：不同的决策门槛有助于实现更好的风险控制
4. **提高整体性能**：非对称设计更符合市场的实际特性

## 🔧 使用说明

### 启用V28.0
V28.0会自动启用，无需额外配置。系统会自动检测并使用新的非对称参数。

### 参数调整
如需调整权重配置，修改 `config.py` 中的相应参数：
- `UP_WEIGHT_*` 系列参数控制上涨信号体系
- `DOWN_WEIGHT_*` 系列参数控制下跌信号体系

### 监控运行
通过GUI界面可以实时查看：
- 当前使用的评分体系类型
- 具体的权重配置
- 综合信心分数和决策门槛

## 📝 更新日志

**V28.0 (2025-01-24)**
- ✨ 新增非对称融合决策体系
- 🔧 为上涨和下跌信号提供独立的权重配置
- 📊 更新GUI显示以反映非对称特性
- 🧪 添加完整的测试验证
- 📚 提供详细的文档说明
- 🔄 保持向后兼容性

---

*V28.0 非对称融合决策体系 - 让AI更懂市场的不对称性*
