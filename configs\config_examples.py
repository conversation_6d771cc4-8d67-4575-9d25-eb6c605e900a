#!/usr/bin/env python3
# config_examples.py
"""
回测配置示例文件
展示如何配置不同类型的回测场景
"""

# =============================================================================
# 配置示例 1: 基础模型回测（传统模式）
# =============================================================================

def get_basic_backtest_config():
    """基础模型回测配置"""
    return {
        # 基础设置
        'SYMBOL': "BTCUSDT",
        'INITIAL_BALANCE': 1000.0,
        'BACKTEST_START_DATE': "2024-11-01",
        'BACKTEST_END_DATE': "2024-11-07",
        
        # 模型设置
        'USE_META_MODEL': False,
        'SIGNAL_MODE': "base_model",
        'PREDICTION_INTERVALS': ["15m"],
        'REALISTIC_MODE': False,
        
        # 交易设置
        'POSITION_SIZING_METHOD': "fixed",
        'FIXED_BET_AMOUNT': 5.0,
        'SIGNAL_THRESHOLD_UP': 0.7,
        'SIGNAL_THRESHOLD_DOWN': 0.7,
    }

# =============================================================================
# 配置示例 2: 元模型回测（真实交易模式）
# =============================================================================

def get_meta_model_backtest_config():
    """元模型真实交易回测配置"""
    return {
        # 基础设置
        'SYMBOL': "BTCUSDT",
        'INITIAL_BALANCE': 1000.0,
        'BACKTEST_START_DATE': "2024-11-01",
        'BACKTEST_END_DATE': "2024-11-07",
        
        # 元模型设置
        'USE_META_MODEL': True,
        'SIGNAL_MODE': "meta_model",
        'PREDICTION_INTERVALS': ["15m"],
        'REALISTIC_MODE': True,
        
        # 凯利公式设置
        'POSITION_SIZING_METHOD': "kelly",
        'KELLY_MIN_AMOUNT': 5.0,
        'KELLY_MAX_AMOUNT': 250.0,
        'KELLY_MAX_PERCENTAGE': 0.10,
        'SIGNAL_THRESHOLD_UP': 0.7,
        'SIGNAL_THRESHOLD_DOWN': 0.7,
    }

# =============================================================================
# 配置示例 3: 多间隔对比回测
# =============================================================================

def get_multi_interval_backtest_config():
    """多预测间隔对比回测配置"""
    return {
        # 基础设置
        'SYMBOL': "BTCUSDT",
        'INITIAL_BALANCE': 1000.0,
        'BACKTEST_START_DATE': "2024-11-01",
        'BACKTEST_END_DATE': "2024-11-07",
        
        # 多间隔设置
        'USE_META_MODEL': True,
        'SIGNAL_MODE': "meta_model",
        'PREDICTION_INTERVALS': ["1m", "15m"],  # 同时使用1分钟和15分钟
        'REALISTIC_MODE': True,
        
        # 交易设置
        'POSITION_SIZING_METHOD': "kelly",
        'KELLY_MIN_AMOUNT': 5.0,
        'KELLY_MAX_AMOUNT': 250.0,
        'KELLY_MAX_PERCENTAGE': 0.10,
    }

# =============================================================================
# 配置示例 4: 自定义间隔回测
# =============================================================================

def get_custom_interval_backtest_config():
    """自定义预测间隔回测配置"""
    return {
        # 基础设置
        'SYMBOL': "BTCUSDT",
        'INITIAL_BALANCE': 1000.0,
        'BACKTEST_START_DATE': "2024-11-01",
        'BACKTEST_END_DATE': "2024-11-07",
        
        # 自定义间隔设置
        'USE_META_MODEL': True,
        'SIGNAL_MODE': "meta_model",
        'PREDICTION_INTERVALS': ["custom"],
        'CUSTOM_INTERVAL': 5,  # 5分钟间隔
        'REALISTIC_MODE': True,
        
        # 交易设置
        'POSITION_SIZING_METHOD': "kelly",
        'KELLY_MIN_AMOUNT': 5.0,
        'KELLY_MAX_AMOUNT': 250.0,
        'KELLY_MAX_PERCENTAGE': 0.10,
    }

# =============================================================================
# 配置示例 5: 阈值优化配置
# =============================================================================

def get_threshold_optimization_config():
    """阈值优化回测配置"""
    return {
        # 基础设置
        'SYMBOL': "BTCUSDT",
        'INITIAL_BALANCE': 1000.0,
        'BACKTEST_START_DATE': "2024-11-01",
        'BACKTEST_END_DATE': "2024-11-07",
        
        # 优化设置
        'USE_META_MODEL': True,
        'SIGNAL_MODE': "meta_model",
        'PREDICTION_INTERVALS': ["15m"],
        'REALISTIC_MODE': True,
        
        # 阈值范围（用于优化）
        'THRESHOLD_RANGE': {
            'start': 0.5,
            'end': 0.9,
            'step': 0.05
        },
        
        # 交易设置
        'POSITION_SIZING_METHOD': "kelly",
        'KELLY_MIN_AMOUNT': 5.0,
        'KELLY_MAX_AMOUNT': 250.0,
        'KELLY_MAX_PERCENTAGE': 0.10,
    }

# =============================================================================
# 配置应用函数
# =============================================================================

def apply_config_to_backtest_config(config_dict):
    """
    将配置字典应用到 backtest_config.py
    
    Args:
        config_dict: 配置字典
    """
    import backtest_config as bt_config
    
    for key, value in config_dict.items():
        if hasattr(bt_config, key):
            setattr(bt_config, key, value)
        else:
            print(f"警告: 配置项 {key} 不存在于 backtest_config 中")

def print_current_config():
    """打印当前配置"""
    import backtest_config as bt_config
    
    print("当前回测配置:")
    print(f"  交易对: {bt_config.SYMBOL}")
    print(f"  初始资金: ${bt_config.INITIAL_BALANCE}")
    print(f"  回测时间: {bt_config.BACKTEST_START_DATE} 到 {bt_config.BACKTEST_END_DATE}")
    print(f"  使用元模型: {bt_config.USE_META_MODEL}")
    print(f"  信号模式: {bt_config.SIGNAL_MODE}")
    print(f"  预测间隔: {bt_config.PREDICTION_INTERVALS}")
    print(f"  真实交易模式: {bt_config.REALISTIC_MODE}")
    print(f"  仓位管理: {bt_config.POSITION_SIZING_METHOD}")
    
    if bt_config.REALISTIC_MODE:
        print(f"  凯利公式范围: ${bt_config.KELLY_MIN_AMOUNT} - ${bt_config.KELLY_MAX_AMOUNT}")
        print(f"  最大资金占比: {bt_config.KELLY_MAX_PERCENTAGE:.1%}")

# =============================================================================
# 使用示例
# =============================================================================

if __name__ == "__main__":
    print("回测配置示例")
    print("=" * 50)
    
    # 示例1: 应用元模型配置
    print("\n1. 应用元模型回测配置...")
    meta_config = get_meta_model_backtest_config()
    apply_config_to_backtest_config(meta_config)
    print_current_config()
    
    # 示例2: 应用自定义间隔配置
    print("\n2. 应用自定义间隔回测配置...")
    custom_config = get_custom_interval_backtest_config()
    apply_config_to_backtest_config(custom_config)
    print_current_config()
    
    print("\n配置应用完成！现在可以运行回测脚本。")
