#!/usr/bin/env python3
"""
V11.0 精英供养超级元模型训练模块

实施两阶段训练的精英供养超级元模型方案：
1. 第一阶段：识别并选拔精英基础模型
2. 第二阶段：只用精英模型的预测作为"纯净养料"训练超级元模型
"""

import os
import json
import numpy as np
import pandas as pd
import joblib
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Optional
import logging

# 设置日志
logger = logging.getLogger(__name__)

class EliteMetaModelTrainer:
    """精英供养超级元模型训练器"""
    
    def __init__(self, base_models_config: List[str], top_n_elites: int = 3):
        """
        初始化精英元模型训练器
        
        Args:
            base_models_config: 基础模型配置列表
            top_n_elites: 每个模型类型选择的精英数量
        """
        self.base_models_config = base_models_config
        self.top_n_elites = top_n_elites
        self.elite_models = {}  # 存储精英模型信息
        
    def identify_elite_models(self, df_data: pd.DataFrame, target_col: str) -> Dict[str, List[Dict]]:
        """
        第一步：通过嵌套交叉验证识别精英基础模型（外层循环）

        Args:
            df_data: 完整的训练数据
            target_col: 目标列名

        Returns:
            Dict: 每个模型类型的精英模型配置列表
        """
        logger.info("🏆 开始精英模型选拔赛（嵌套交叉验证-外层循环）...")
        elite_models = {}

        for model_name in self.base_models_config:
            logger.info(f"📊 评估模型: {model_name}")

            try:
                # 获取模型配置
                import config
                model_config = config.get_target_config(model_name)

                # 准备数据
                feature_cols = [col for col in df_data.columns if col != target_col]
                X = df_data[feature_cols]
                y = df_data[target_col]

                # 外层交叉验证：用于精英识别
                from sklearn.model_selection import TimeSeriesSplit
                outer_cv = TimeSeriesSplit(n_splits=10)  # 外层10折

                fold_performances = []

                logger.info(f"  🔄 运行外层交叉验证（10折）...")

                for fold_idx, (train_idx, val_idx) in enumerate(outer_cv.split(X)):
                    logger.info(f"    评估 Fold {fold_idx}...")

                    # 分割数据
                    X_train_outer = X.iloc[train_idx]
                    X_val_outer = X.iloc[val_idx]
                    y_train_outer = y.iloc[train_idx]
                    y_val_outer = y.iloc[val_idx]

                    # 训练模型
                    model = self._train_model_with_config(X_train_outer, y_train_outer, model_config)

                    if model is None:
                        logger.warning(f"    Fold {fold_idx} 训练失败")
                        continue

                    # 评估模型
                    performance = self._evaluate_model_performance(
                        model, X_val_outer, y_val_outer, fold_idx
                    )

                    if performance is not None:
                        # 保存模型配置和性能
                        fold_info = {
                            'fold_index': fold_idx,
                            'performance': performance,
                            'model_config': model_config.copy(),  # 保存配置用于后续重训练
                            'composite_score': performance['composite_score']
                        }
                        fold_performances.append(fold_info)

                        logger.info(f"    ✅ Fold {fold_idx}: 综合评分 {performance['composite_score']:.4f}")

                if not fold_performances:
                    logger.warning(f"模型 {model_name} 没有有效的fold性能数据")
                    continue

                # 按综合评分排序，选择Top N精英
                fold_performances.sort(key=lambda x: x['composite_score'], reverse=True)
                elite_folds = fold_performances[:self.top_n_elites]

                elite_models[model_name] = elite_folds

                logger.info(f"✅ {model_name} 精英选拔完成:")
                for i, elite in enumerate(elite_folds):
                    perf = elite['performance']
                    logger.info(f"  🥇 精英#{i+1}: Fold{elite['fold_index']} "
                               f"(综合评分: {elite['composite_score']:.4f}, "
                               f"F1: {perf.get('f1_score', 'N/A')}, "
                               f"AUC: {perf.get('auc_score', 'N/A'):.4f})")

            except Exception as e:
                logger.error(f"评估模型 {model_name} 时出错: {e}")
                import traceback
                traceback.print_exc()
                continue

        self.elite_models = elite_models
        logger.info(f"🎉 精英选拔完成！共识别出 {sum(len(elites) for elites in elite_models.values())} 个精英模型")
        return elite_models

    def _train_model_with_config(self, X_train: pd.DataFrame, y_train: pd.Series, model_config: dict):
        """使用配置训练模型"""
        try:
            import lightgbm as lgb
            from sklearn.preprocessing import StandardScaler, LabelEncoder
            import pandas as pd
            import numpy as np

            # 🎯 关键修复：数据类型转换和预处理
            logger.info(f"    原始特征数据形状: {X_train.shape}")
            logger.info(f"    原始特征类型: {X_train.dtypes.value_counts().to_dict()}")

            # 1. 复制数据避免修改原始数据
            X_processed = X_train.copy()

            # 2. 处理字符串类型的特征
            categorical_columns = []
            label_encoders = {}

            for col in X_processed.columns:
                if X_processed[col].dtype == 'object' or X_processed[col].dtype.name == 'category':
                    logger.info(f"    处理分类特征: {col}")
                    categorical_columns.append(col)

                    # 使用LabelEncoder处理分类特征
                    le = LabelEncoder()
                    # 处理NaN值
                    mask = X_processed[col].notna()
                    if mask.sum() > 0:  # 如果有非NaN值
                        X_processed.loc[mask, col] = le.fit_transform(X_processed.loc[mask, col].astype(str))
                        label_encoders[col] = le
                    else:
                        # 如果全是NaN，填充为0
                        X_processed[col] = 0

                    # 确保转换为数值类型
                    X_processed[col] = pd.to_numeric(X_processed[col], errors='coerce')

            # 3. 处理数值类型的特征
            for col in X_processed.columns:
                if col not in categorical_columns:
                    # 确保数值类型
                    X_processed[col] = pd.to_numeric(X_processed[col], errors='coerce')

            # 4. 处理NaN值
            X_processed = X_processed.fillna(0)

            # 5. 确保所有数据都是数值类型
            for col in X_processed.columns:
                if X_processed[col].dtype == 'object':
                    logger.warning(f"    强制转换剩余对象类型列: {col}")
                    X_processed[col] = pd.to_numeric(X_processed[col], errors='coerce').fillna(0)

            logger.info(f"    处理后特征类型: {X_processed.dtypes.value_counts().to_dict()}")
            logger.info(f"    分类特征数量: {len(categorical_columns)}")

            # 6. 数据标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_processed)

            # 7. LightGBM参数
            lgb_params = {
                'objective': 'multiclass' if len(y_train.unique()) > 2 else 'binary',
                'num_class': len(y_train.unique()) if len(y_train.unique()) > 2 else None,
                'metric': 'multi_logloss' if len(y_train.unique()) > 2 else 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.8,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }

            # 8. 创建数据集
            train_data = lgb.Dataset(X_scaled, label=y_train)

            # 9. 训练模型
            model = lgb.train(
                lgb_params,
                train_data,
                num_boost_round=200,
                valid_sets=[train_data],
                callbacks=[lgb.early_stopping(20), lgb.log_evaluation(0)]
            )

            return {
                'model': model,
                'scaler': scaler,
                'label_encoders': label_encoders,
                'categorical_columns': categorical_columns,
                'feature_columns': list(X_processed.columns)
            }

        except Exception as e:
            logger.error(f"训练模型时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _evaluate_model_performance(self, model_info: dict, X_val: pd.DataFrame,
                                   y_val: pd.Series, fold_idx: int) -> Optional[Dict]:
        """评估模型性能"""
        try:
            from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, log_loss
            from sklearn.preprocessing import LabelEncoder
            import pandas as pd
            import numpy as np

            model = model_info['model']
            scaler = model_info['scaler']
            label_encoders = model_info.get('label_encoders', {})
            categorical_columns = model_info.get('categorical_columns', [])
            feature_columns = model_info.get('feature_columns', list(X_val.columns))

            # 🎯 关键修复：与训练时相同的数据预处理
            logger.info(f"    验证数据预处理开始...")

            # 1. 复制数据
            X_val_processed = X_val.copy()

            # 2. 确保列顺序与训练时一致
            if set(X_val_processed.columns) != set(feature_columns):
                logger.warning(f"    验证数据列与训练数据列不完全匹配")
                # 只保留训练时使用的列
                available_cols = [col for col in feature_columns if col in X_val_processed.columns]
                X_val_processed = X_val_processed[available_cols]

                # 添加缺失的列（填充为0）
                for col in feature_columns:
                    if col not in X_val_processed.columns:
                        X_val_processed[col] = 0

                # 重新排序列
                X_val_processed = X_val_processed[feature_columns]

            # 3. 处理分类特征（使用训练时的编码器）
            for col in categorical_columns:
                if col in X_val_processed.columns and col in label_encoders:
                    le = label_encoders[col]
                    mask = X_val_processed[col].notna()

                    if mask.sum() > 0:
                        try:
                            # 处理未见过的类别
                            unique_vals = X_val_processed.loc[mask, col].astype(str).unique()
                            known_classes = set(le.classes_)

                            # 将未知类别映射为已知类别的第一个
                            val_series = X_val_processed.loc[mask, col].astype(str)
                            for val in unique_vals:
                                if val not in known_classes:
                                    val_series = val_series.replace(val, le.classes_[0])

                            X_val_processed.loc[mask, col] = le.transform(val_series)
                        except Exception as e:
                            logger.warning(f"    分类特征 {col} 编码失败: {e}，使用默认值0")
                            X_val_processed[col] = 0
                    else:
                        X_val_processed[col] = 0

                    # 确保数值类型
                    X_val_processed[col] = pd.to_numeric(X_val_processed[col], errors='coerce')

            # 4. 处理数值特征
            for col in X_val_processed.columns:
                if col not in categorical_columns:
                    X_val_processed[col] = pd.to_numeric(X_val_processed[col], errors='coerce')

            # 5. 处理NaN值
            X_val_processed = X_val_processed.fillna(0)

            # 6. 确保所有数据都是数值类型
            for col in X_val_processed.columns:
                if X_val_processed[col].dtype == 'object':
                    X_val_processed[col] = pd.to_numeric(X_val_processed[col], errors='coerce').fillna(0)

            logger.info(f"    验证数据预处理完成: {X_val_processed.shape}")

            # 7. 标准化
            X_val_scaled = scaler.transform(X_val_processed)

            # 8. 预测
            y_pred_proba = model.predict(X_val_scaled)

            # 9. 处理多分类情况
            if len(y_val.unique()) > 2:
                # 多分类：转换为二分类（合并类别）
                y_val_binary = (y_val == 1).astype(int)  # 假设1是正类
                if y_pred_proba.ndim > 1:
                    y_pred_proba_binary = y_pred_proba[:, 1] if y_pred_proba.shape[1] > 1 else y_pred_proba[:, 0]
                else:
                    y_pred_proba_binary = y_pred_proba
            else:
                y_val_binary = y_val
                y_pred_proba_binary = y_pred_proba

            # 10. 计算预测类别
            y_pred_binary = (y_pred_proba_binary > 0.5).astype(int)

            # 11. 计算性能指标
            accuracy = accuracy_score(y_val_binary, y_pred_binary)

            # F1分数（处理可能的异常情况）
            try:
                f1 = f1_score(y_val_binary, y_pred_binary, average='binary', zero_division=0)
            except:
                f1 = 0.0

            # AUC分数
            try:
                auc = roc_auc_score(y_val_binary, y_pred_proba_binary)
            except:
                auc = 0.5

            # Brier分数（对数损失的近似）
            try:
                brier = log_loss(y_val_binary, y_pred_proba_binary)
            except:
                brier = 1.0

            # 计算综合评分
            brier_bonus = max(0, 1.0 - brier)
            composite_score = f1 + 0.1 * brier_bonus  # 优先考虑F1分数

            performance = {
                'fold_index': fold_idx,
                'accuracy': accuracy,
                'f1_score': f1,
                'auc_score': auc,
                'brier_score': brier,
                'composite_score': composite_score
            }

            logger.info(f"    Fold {fold_idx} 性能: F1={f1:.4f}, AUC={auc:.4f}, 综合={composite_score:.4f}")

            return performance

        except Exception as e:
            logger.error(f"评估模型性能时出错: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def generate_elite_oof_predictions(self, df_data: pd.DataFrame, target_col: str) -> pd.DataFrame:
        """
        第二步：为精英模型生成专属OOF预测（嵌套交叉验证-内层循环）

        Args:
            df_data: 完整的训练数据
            target_col: 目标列名

        Returns:
            DataFrame: 包含精英模型OOF预测的数据
        """
        logger.info("🚀 开始生成精英模型专属OOF预测（嵌套交叉验证-内层循环）...")

        oof_predictions = pd.DataFrame(index=df_data.index)

        # 准备数据
        feature_cols = [col for col in df_data.columns if col != target_col]
        X = df_data[feature_cols]
        y = df_data[target_col]

        for model_name, elite_folds in self.elite_models.items():
            logger.info(f"📊 处理模型: {model_name}")

            try:
                # 为每个精英fold生成OOF预测
                for elite_info in elite_folds:
                    fold_idx = elite_info['fold_index']
                    model_config = elite_info['model_config']

                    logger.info(f"  🎯 为精英Fold{fold_idx}生成无泄露OOF预测...")

                    # 内层交叉验证：为该精英fold生成OOF
                    oof_col_name = f"oof_{model_name}_elite_fold{fold_idx}"
                    oof_predictions[oof_col_name] = self._generate_nested_oof_for_elite(
                        X, y, model_config, fold_idx, model_name
                    )

                    logger.info(f"  ✅ 完成精英Fold{fold_idx}的OOF预测")

            except Exception as e:
                logger.error(f"处理模型 {model_name} 时出错: {e}")
                import traceback
                traceback.print_exc()
                continue

        # 检查数据质量
        total_features = oof_predictions.shape[1]
        nan_counts = oof_predictions.isnull().sum()

        logger.info(f"🎉 精英OOF预测生成完成，共 {total_features} 个精英特征")
        logger.info(f"📊 数据质量检查:")
        for col in oof_predictions.columns:
            nan_count = nan_counts[col]
            valid_ratio = (len(oof_predictions) - nan_count) / len(oof_predictions)
            logger.info(f"  {col}: {nan_count} NaN值, 有效率 {valid_ratio:.2%}")

        return oof_predictions

    def _generate_nested_oof_for_elite(self, X: pd.DataFrame, y: pd.Series,
                                      model_config: dict, elite_fold_idx: int,
                                      model_name: str) -> pd.Series:
        """
        为单个精英模型生成嵌套交叉验证的OOF预测

        这是关键方法：使用内层交叉验证为精英模型生成无数据泄露的OOF预测

        Args:
            X: 特征数据
            y: 目标变量
            model_config: 模型配置
            elite_fold_idx: 精英fold索引
            model_name: 模型名称

        Returns:
            Series: OOF预测结果
        """
        try:
            logger.info(f"    🔄 运行内层交叉验证（5折）为精英Fold{elite_fold_idx}...")

            # 内层交叉验证：与外层CV完全独立
            from sklearn.model_selection import TimeSeriesSplit
            inner_cv = TimeSeriesSplit(n_splits=5)  # 内层5折，与外层10折独立

            # 初始化OOF预测数组
            oof_predictions = np.full(len(X), np.nan)

            # 内层交叉验证循环
            for inner_fold_idx, (train_idx, val_idx) in enumerate(inner_cv.split(X)):
                logger.info(f"      内层Fold {inner_fold_idx}...")

                # 分割数据
                X_train_inner = X.iloc[train_idx]
                X_val_inner = X.iloc[val_idx]
                y_train_inner = y.iloc[train_idx]
                y_val_inner = y.iloc[val_idx]

                # 使用精英fold的配置训练模型
                model_info = self._train_model_with_config(X_train_inner, y_train_inner, model_config)

                if model_info is None:
                    logger.warning(f"      内层Fold {inner_fold_idx} 训练失败")
                    continue

                # 预测验证集（使用与训练和评估相同的预处理）
                model = model_info['model']
                scaler = model_info['scaler']
                label_encoders = model_info.get('label_encoders', {})
                categorical_columns = model_info.get('categorical_columns', [])
                feature_columns = model_info.get('feature_columns', list(X_val_inner.columns))

                # 预处理验证数据
                X_val_processed = self._preprocess_data_for_prediction(
                    X_val_inner, label_encoders, categorical_columns, feature_columns
                )

                X_val_scaled = scaler.transform(X_val_processed)
                y_pred_proba = model.predict(X_val_scaled)

                # 处理预测结果
                if len(y.unique()) > 2:
                    # 多分类：取正类概率
                    if y_pred_proba.ndim > 1 and y_pred_proba.shape[1] > 1:
                        y_pred_proba = y_pred_proba[:, 1]  # 假设索引1是正类
                    elif y_pred_proba.ndim > 1:
                        y_pred_proba = y_pred_proba[:, 0]

                # 存储OOF预测
                oof_predictions[val_idx] = y_pred_proba

                logger.info(f"      ✅ 内层Fold {inner_fold_idx} 完成")

            # 检查OOF预测的完整性
            nan_count = np.isnan(oof_predictions).sum()
            valid_ratio = (len(oof_predictions) - nan_count) / len(oof_predictions)

            logger.info(f"    📊 精英Fold{elite_fold_idx} OOF质量: {nan_count} NaN值, 有效率 {valid_ratio:.2%}")

            # 对于NaN值，使用全局平均值填充（保守策略）
            if nan_count > 0:
                global_mean = np.nanmean(oof_predictions)
                if np.isnan(global_mean):
                    global_mean = 0.5  # 默认中性预测
                oof_predictions[np.isnan(oof_predictions)] = global_mean
                logger.info(f"    🔧 使用全局均值 {global_mean:.4f} 填充 {nan_count} 个NaN值")

            return pd.Series(oof_predictions, index=X.index)

        except Exception as e:
            logger.error(f"生成精英Fold{elite_fold_idx}的嵌套OOF时出错: {e}")
            import traceback
            traceback.print_exc()
            # 返回中性预测作为后备
            return pd.Series(0.5, index=X.index)

    def _preprocess_data_for_prediction(self, X_data: pd.DataFrame, label_encoders: dict,
                                       categorical_columns: list, feature_columns: list) -> pd.DataFrame:
        """
        为预测预处理数据（与训练时保持一致）

        Args:
            X_data: 原始特征数据
            label_encoders: 训练时的标签编码器
            categorical_columns: 分类特征列名
            feature_columns: 训练时的特征列名

        Returns:
            DataFrame: 预处理后的数据
        """
        try:
            import pandas as pd
            import numpy as np

            # 1. 复制数据
            X_processed = X_data.copy()

            # 2. 确保列顺序与训练时一致
            if set(X_processed.columns) != set(feature_columns):
                # 只保留训练时使用的列
                available_cols = [col for col in feature_columns if col in X_processed.columns]
                X_processed = X_processed[available_cols]

                # 添加缺失的列（填充为0）
                for col in feature_columns:
                    if col not in X_processed.columns:
                        X_processed[col] = 0

                # 重新排序列
                X_processed = X_processed[feature_columns]

            # 3. 处理分类特征
            for col in categorical_columns:
                if col in X_processed.columns and col in label_encoders:
                    le = label_encoders[col]
                    mask = X_processed[col].notna()

                    if mask.sum() > 0:
                        try:
                            # 处理未见过的类别
                            unique_vals = X_processed.loc[mask, col].astype(str).unique()
                            known_classes = set(le.classes_)

                            # 将未知类别映射为已知类别的第一个
                            val_series = X_processed.loc[mask, col].astype(str)
                            for val in unique_vals:
                                if val not in known_classes:
                                    val_series = val_series.replace(val, le.classes_[0])

                            X_processed.loc[mask, col] = le.transform(val_series)
                        except Exception as e:
                            logger.warning(f"分类特征 {col} 编码失败: {e}，使用默认值0")
                            X_processed[col] = 0
                    else:
                        X_processed[col] = 0

                    # 确保数值类型
                    X_processed[col] = pd.to_numeric(X_processed[col], errors='coerce')

            # 4. 处理数值特征
            for col in X_processed.columns:
                if col not in categorical_columns:
                    X_processed[col] = pd.to_numeric(X_processed[col], errors='coerce')

            # 5. 处理NaN值
            X_processed = X_processed.fillna(0)

            # 6. 确保所有数据都是数值类型
            for col in X_processed.columns:
                if X_processed[col].dtype == 'object':
                    X_processed[col] = pd.to_numeric(X_processed[col], errors='coerce').fillna(0)

            return X_processed

        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            # 返回填充为0的数据作为后备
            return pd.DataFrame(0, index=X_data.index, columns=feature_columns)

    def _generate_single_elite_oof(self, model_path: str, model_config: dict,
                                  df_data: pd.DataFrame, target_col: str,
                                  elite_fold_idx: int) -> pd.Series:
        """
        为单个精英模型生成OOF预测
        
        这是核心逻辑：为了给精英模型生成OOF，我们需要在除该fold之外的数据上
        重新训练临时模型，然后用临时模型预测该fold的数据
        """
        try:
            # 加载精英模型（用于参考配置）
            elite_model = joblib.load(model_path)
            
            # 设置交叉验证
            from sklearn.model_selection import TimeSeriesSplit
            n_splits = model_config.get('cv_folds', 5)
            tscv = TimeSeriesSplit(n_splits=n_splits)
            
            # 初始化OOF预测数组
            oof_predictions = np.full(len(df_data), np.nan)
            
            # 准备特征和目标
            feature_cols = [col for col in df_data.columns if col != target_col]
            X = df_data[feature_cols]
            y = df_data[target_col]
            
            # 交叉验证生成OOF
            for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(X)):
                # 只为目标精英fold生成预测
                if fold_idx == elite_fold_idx:
                    # 训练临时模型（在除当前fold外的所有数据上）
                    X_train_temp = X.iloc[train_idx]
                    y_train_temp = y.iloc[train_idx]
                    
                    # 使用与精英模型相同的配置训练临时模型
                    temp_model = self._train_temporary_model(X_train_temp, y_train_temp, model_config)
                    
                    if temp_model is not None:
                        # 预测当前fold
                        X_val = X.iloc[val_idx]
                        if hasattr(temp_model, 'predict_proba'):
                            pred_proba = temp_model.predict_proba(X_val)
                            # 取正类概率
                            oof_predictions[val_idx] = pred_proba[:, 1] if pred_proba.shape[1] > 1 else pred_proba[:, 0]
                        else:
                            oof_predictions[val_idx] = temp_model.predict(X_val)
            
            return pd.Series(oof_predictions, index=df_data.index)
            
        except Exception as e:
            logger.error(f"生成精英模型OOF预测时出错: {e}")
            return pd.Series(np.nan, index=df_data.index)
    
    def _train_temporary_model(self, X_train: pd.DataFrame, y_train: pd.Series, model_config: dict):
        """训练临时模型用于OOF生成"""
        try:
            # 使用LightGBM训练临时模型
            import lightgbm as lgb
            
            # 基本参数
            lgb_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.8,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            # 创建数据集
            train_data = lgb.Dataset(X_train, label=y_train)
            
            # 训练模型
            model = lgb.train(
                lgb_params,
                train_data,
                num_boost_round=100,
                valid_sets=[train_data],
                callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
            )
            
            return model
            
        except Exception as e:
            logger.error(f"训练临时模型时出错: {e}")
            return None

    def _get_trained_models_info(self) -> Dict:
        """
        获取已训练模型信息

        Returns:
            Dict: 模型信息字典
        """
        models_info = {}

        for model_name, elite_folds in self.elite_models.items():
            if elite_folds:
                # 使用第一个精英fold的配置
                first_fold = elite_folds[0]
                models_info[model_name] = {
                    'config': first_fold.get('model_config', {}),
                    'model_dir': first_fold.get('model_path', '').replace('/model.pkl', ''),
                    'elite_folds': elite_folds
                }

        return models_info

    def enable_optimized_data_preparation(self, enable: bool = True):
        """
        启用或禁用优化的数据准备方案

        Args:
            enable: 是否启用优化方案
        """
        self.use_optimized_data_prep = enable
        logger = get_enhanced_logger()
        if enable:
            logger.info("✅ 已启用优化的元模型数据准备方案")
        else:
            logger.info("📊 使用传统的元模型数据准备方案")

    def train_super_meta_model(self, oof_predictions: pd.DataFrame, target_data: pd.Series,
                              output_dir: str = "models/elite_meta_model",
                              df_full_hist_data_for_oof_input: pd.DataFrame = None,
                              force_include_features: List[str] = None) -> Dict:
        """
        第三步：训练超级元模型

        Args:
            oof_predictions: 精英模型的OOF预测数据
            target_data: 目标变量
            output_dir: 输出目录
            df_full_hist_data_for_oof_input: 完整历史数据，用于动态计算全局市场状态特征

        Returns:
            Dict: 训练结果
        """
        logger.info("🌟 开始训练超级元模型...")

        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 🎯 关键修复：改进数据准备和目标变量处理
            logger.info("📊 准备超级元模型训练数据...")

            # 🚀 新增：使用优化的数据准备方案（可选）
            use_optimized_preparation = getattr(self, 'use_optimized_data_prep', False)

            if use_optimized_preparation and df_full_hist_data_for_oof_input is not None:
                logger.info("🔧 使用优化的元模型数据准备方案...")
                try:
                    from src.training.optimized_meta_data_preparation import prepare_meta_training_data_optimized

                    # 获取已训练模型信息
                    trained_models_info = self._get_trained_models_info()

                    # 使用优化的数据准备（启用CSV保存）
                    X_meta, y_meta = prepare_meta_training_data_optimized(
                        df_raw_data=df_full_hist_data_for_oof_input,
                        target_data=target_data,
                        trained_models_info=trained_models_info,
                        base_models_config=self.base_models_config,
                        save_csv_files=True,  # 启用CSV文件保存
                        output_dir=output_dir  # 使用精英元模型的输出目录
                    )

                    logger.info("✅ 优化数据准备完成")

                except Exception as e:
                    logger.warning(f"优化数据准备失败，回退到传统方法: {e}")
                    use_optimized_preparation = False

            if not use_optimized_preparation:
                # 传统数据准备方法
                logger.info("📊 使用传统数据准备方法...")

                # 1. 处理NaN值（更智能的方式）
                logger.info(f"原始OOF数据: {oof_predictions.shape}")
                logger.info(f"NaN统计: {oof_predictions.isnull().sum().sum()} 个NaN值")

                # 🎯 优化：使用严格的索引对齐
                valid_rows = ~oof_predictions.isnull().all(axis=1)
                X_meta = oof_predictions[valid_rows].copy()

                # 确保目标变量与OOF预测严格对齐
                common_index = X_meta.index.intersection(target_data.index)
                X_meta = X_meta.loc[common_index]
                y_meta = target_data.loc[common_index]

                # 🚀 修复数据泄露：使用安全的NaN填充方法
                X_meta = X_meta.ffill().fillna(0)  # 使用向前填充，然后用0填充

            logger.info(f"📊 超级元模型训练数据: {X_meta.shape[0]} 样本, {X_meta.shape[1]} 个精英特征")
            logger.info(f"🎯 精英特征列表: {list(X_meta.columns)}")

            # 🚀 关键集成：添加动态全局市场状态特征（"神经网络"修复）
            if df_full_hist_data_for_oof_input is not None:
                logger.info("🔧 集成动态全局市场状态特征计算...")
                try:
                    # 导入add_meta_feature_engineering函数
                    import sys
                    import os as os_module
                    sys.path.append(os_module.path.dirname(os_module.path.dirname(os_module.path.dirname(__file__))))
                    from main import add_meta_feature_engineering

                    # 应用特征工程（包括动态全局市场状态特征）
                    X_meta_enhanced = add_meta_feature_engineering(
                        X_meta,
                        self.base_models_config,
                        precomputed_global_features=None,  # 强制使用动态计算
                        df_full_hist_data_for_oof_input=df_full_hist_data_for_oof_input
                    )

                    # 更新训练数据
                    original_feature_count = X_meta.shape[1]
                    X_meta = X_meta_enhanced
                    new_feature_count = X_meta.shape[1]
                    added_features = new_feature_count - original_feature_count

                    logger.info(f"✅ 动态特征工程完成！")
                    logger.info(f"  原始精英特征: {original_feature_count} 个")
                    logger.info(f"  新增全局特征: {added_features} 个")
                    logger.info(f"  总特征数: {new_feature_count} 个")

                    # 显示新增的全局特征
                    global_features = [col for col in X_meta.columns if col.startswith('global_')]
                    if global_features:
                        logger.info(f"🌍 新增全局市场状态特征: {global_features}")

                except Exception as e:
                    logger.warning(f"⚠️  动态全局特征计算失败，继续使用纯精英特征: {e}")
                    # 继续使用原始的精英特征
                    pass
            else:
                logger.info("⚠️  未提供历史数据，跳过动态全局特征计算")

            # 🌍 特征选择阶段：应用强制保留特征策略
            if force_include_features and len(force_include_features) > 0:
                logger.info(f"🎯 应用强制保留特征策略...")
                logger.info(f"  强制保留特征数量: {len(force_include_features)}")

                # 检查哪些强制特征存在于当前数据中
                available_force_features = [f for f in force_include_features if f in X_meta.columns]
                missing_force_features = [f for f in force_include_features if f not in X_meta.columns]

                logger.info(f"  可用强制特征: {len(available_force_features)} 个")
                if available_force_features:
                    logger.info(f"    特征列表: {available_force_features[:5]}...")

                if missing_force_features:
                    logger.warning(f"  缺失强制特征: {len(missing_force_features)} 个")
                    logger.warning(f"    缺失列表: {missing_force_features[:5]}...")

                # 如果需要进行特征选择（特征数量过多）
                import config
                max_features = getattr(config, 'META_MODEL_MAX_FEATURES', 50)
                if X_meta.shape[1] > max_features:
                    logger.info(f"  特征数量({X_meta.shape[1]})超过限制({max_features})，进行特征选择...")

                    try:
                        # 导入特征选择函数
                        from src.core.data_utils import enhanced_feature_selection_with_shap

                        # 应用特征选择（包含强制保留特征）
                        selected_features, selection_stats = enhanced_feature_selection_with_shap(
                            X_meta, y_meta.values,
                            target_config={'target_name': 'elite_meta_model'},
                            target_name='elite_meta_model',
                            device_type='cpu',
                            verbose=True,
                            force_include_features=available_force_features
                        )

                        # 应用特征选择结果
                        X_meta = X_meta[selected_features]

                        logger.info(f"  ✅ 特征选择完成: {len(X_meta.columns)} 个特征保留")
                        logger.info(f"  强制保留特征在最终特征中: {[f for f in available_force_features if f in selected_features]}")

                    except Exception as e:
                        logger.warning(f"  ⚠️  特征选择失败，使用所有特征: {e}")
                else:
                    logger.info(f"  特征数量({X_meta.shape[1]})在限制内，跳过特征选择")
            else:
                logger.info("  未配置强制保留特征，跳过特征选择")

            # 2. 目标变量分析和处理
            logger.info("🎯 目标变量分析:")
            target_counts = y_meta.value_counts().sort_index()
            logger.info(f"  目标分布: {target_counts.to_dict()}")

            # 处理多分类问题：转换为二分类
            if len(y_meta.unique()) > 2:
                logger.info("  检测到多分类问题，转换为二分类...")
                # 将类别1作为正类，其他作为负类
                y_meta_binary = (y_meta == 1).astype(int)
                logger.info(f"  二分类分布: {y_meta_binary.value_counts().to_dict()}")
                y_meta = y_meta_binary

            # 3. 数据质量检查
            if X_meta.shape[0] < 50:  # 恢复正常的最小样本要求
                raise ValueError(f"训练样本过少: {X_meta.shape[0]} < 50")

            if X_meta.shape[1] < 1:  # 至少需要1个特征
                raise ValueError(f"精英特征过少: {X_meta.shape[1]} < 1")

            # 检查目标变量的平衡性
            pos_ratio = y_meta.mean()
            logger.info(f"  正类比例: {pos_ratio:.4f}")

            if pos_ratio < 0.01 or pos_ratio > 0.99:
                logger.warning(f"  ⚠️  目标变量严重不平衡，可能影响模型性能")

            # 4. 特征统计
            logger.info("📈 精英特征统计:")
            for col in X_meta.columns:
                mean_val = X_meta[col].mean()
                std_val = X_meta[col].std()
                min_val = X_meta[col].min()
                max_val = X_meta[col].max()
                logger.info(f"  {col}: 均值={mean_val:.4f}, 标准差={std_val:.4f}, 范围=[{min_val:.4f}, {max_val:.4f}]")

                # 检查特征的有效性
                if std_val < 1e-6:
                    logger.warning(f"    ⚠️  特征 {col} 方差过小，可能是常数特征")

            # 训练超级元模型
            from sklearn.model_selection import TimeSeriesSplit
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import classification_report, roc_auc_score
            import lightgbm as lgb

            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=5)

            # 尝试多种模型
            models_to_try = {
                'LightGBM': self._create_lgb_model(),
                'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
                'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
            }

            best_model = None
            best_score = 0
            best_model_name = ""
            model_results = {}

            for model_name, model in models_to_try.items():
                logger.info(f"🧪 测试模型: {model_name}")

                # 交叉验证评估
                cv_scores = []
                for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(X_meta)):
                    X_train_cv = X_meta.iloc[train_idx]
                    X_val_cv = X_meta.iloc[val_idx]
                    y_train_cv = y_meta.iloc[train_idx]
                    y_val_cv = y_meta.iloc[val_idx]

                    # 训练模型
                    if model_name == 'LightGBM':
                        train_data = lgb.Dataset(X_train_cv, label=y_train_cv)
                        val_data = lgb.Dataset(X_val_cv, label=y_val_cv, reference=train_data)

                        model_cv = lgb.train(
                            self._get_lgb_params(),
                            train_data,
                            num_boost_round=200,
                            valid_sets=[val_data],
                            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(0)]
                        )

                        y_pred_proba = model_cv.predict(X_val_cv)
                    else:
                        model_cv = model.fit(X_train_cv, y_train_cv)
                        y_pred_proba = model_cv.predict_proba(X_val_cv)[:, 1]

                    # 🎯 关键修复：改进性能评估
                    try:
                        # 检查预测值的有效性
                        if len(np.unique(y_val_cv)) < 2:
                            logger.warning(f"    Fold {fold_idx} 验证集只有一个类别，跳过AUC计算")
                            cv_scores.append(0.5)
                            continue

                        # 确保预测值在合理范围内
                        y_pred_proba = np.clip(y_pred_proba, 1e-7, 1-1e-7)

                        auc_score = roc_auc_score(y_val_cv, y_pred_proba)
                        cv_scores.append(auc_score)

                        logger.info(f"    Fold {fold_idx}: AUC = {auc_score:.4f}")

                    except Exception as e:
                        logger.warning(f"    Fold {fold_idx} AUC计算失败: {e}")
                        cv_scores.append(0.5)  # 默认随机分类器性能

                avg_score = np.mean(cv_scores)
                model_results[model_name] = {
                    'avg_auc': avg_score,
                    'cv_scores': cv_scores,
                    'std_auc': np.std(cv_scores)
                }

                logger.info(f"  📊 {model_name} 平均AUC: {avg_score:.4f} ± {np.std(cv_scores):.4f}")

                if avg_score > best_score:
                    best_score = avg_score
                    best_model_name = model_name
                    best_model = model

            logger.info(f"🏆 最佳模型: {best_model_name} (AUC: {best_score:.4f})")

            # 使用最佳模型在全部数据上训练
            if best_model_name == 'LightGBM':
                train_data = lgb.Dataset(X_meta, label=y_meta)
                final_model = lgb.train(
                    self._get_lgb_params(),
                    train_data,
                    num_boost_round=300,
                    valid_sets=[train_data],
                    callbacks=[lgb.early_stopping(30), lgb.log_evaluation(0)]
                )
            else:
                final_model = best_model.fit(X_meta, y_meta)

            # 🎯 新增：阈值优化和概率校准
            logger.info("🔧 开始元模型阈值优化和概率校准...")

            # 分割数据用于阈值优化和概率校准
            from sklearn.model_selection import train_test_split
            X_train_opt, X_val_opt, y_train_opt, y_val_opt = train_test_split(
                X_meta, y_meta, test_size=0.2, random_state=42, stratify=y_meta
            )

            # 重新训练模型用于优化（使用训练集）
            if best_model_name == 'LightGBM':
                train_data_opt = lgb.Dataset(X_train_opt, label=y_train_opt)
                optimization_model = lgb.train(
                    self._get_lgb_params(),
                    train_data_opt,
                    num_boost_round=300,
                    valid_sets=[train_data_opt],
                    callbacks=[lgb.early_stopping(30), lgb.log_evaluation(0)]
                )
            else:
                optimization_model = best_model.fit(X_train_opt, y_train_opt)

            # 获取验证集预测概率
            if best_model_name == 'LightGBM':
                y_val_proba = optimization_model.predict(X_val_opt.values, num_iteration=optimization_model.best_iteration)
            else:
                y_val_proba = optimization_model.predict_proba(X_val_opt)[:, 1]

            # 1. 阈值优化
            threshold_result = None
            try:
                from src.core.threshold_optimization import ThresholdOptimizer

                threshold_optimizer = ThresholdOptimizer(target_name="elite_meta_model", verbose=True)
                threshold_result = threshold_optimizer.optimize_threshold(
                    y_val_opt.values, y_val_proba,
                    method='youden',  # 使用Youden指数优化
                    verbose=True
                )

                if threshold_result['success']:
                    logger.info(f"✅ 阈值优化成功: {threshold_result['optimal_threshold']:.4f}")
                    logger.info(f"   方法: {threshold_result['method']}")
                    logger.info(f"   F1分数: {threshold_result.get('f1_score', 'N/A'):.4f}")
                    logger.info(f"   精确率: {threshold_result.get('precision', 'N/A'):.4f}")
                    logger.info(f"   召回率: {threshold_result.get('recall', 'N/A'):.4f}")
                else:
                    logger.warning("⚠️ 阈值优化失败，使用默认阈值0.5")

            except Exception as e:
                logger.error(f"❌ 阈值优化过程出错: {e}")
                threshold_result = {'optimal_threshold': 0.5, 'success': False, 'method': 'default'}

            # 2. 概率校准
            calibrated_model = None
            calibration_result = None
            try:
                from sklearn.calibration import CalibratedClassifierCV
                from sklearn.metrics import brier_score_loss

                logger.info("🔧 开始概率校准...")

                # 创建校准模型
                if best_model_name == 'LightGBM':
                    # 对于LightGBM Booster，需要包装成sklearn兼容的形式
                    class LGBWrapper:
                        def __init__(self, booster):
                            self.booster = booster

                        def predict_proba(self, X):
                            proba_pos = self.booster.predict(X, num_iteration=self.booster.best_iteration)
                            proba_neg = 1 - proba_pos
                            return np.column_stack([proba_neg, proba_pos])

                        def predict(self, X):
                            proba = self.predict_proba(X)
                            return (proba[:, 1] > 0.5).astype(int)

                    base_model_wrapped = LGBWrapper(optimization_model)
                else:
                    base_model_wrapped = optimization_model

                # 使用Platt scaling进行校准
                calibrated_model = CalibratedClassifierCV(
                    base_model_wrapped,
                    method='sigmoid',  # Platt scaling
                    cv=3
                )
                calibrated_model.fit(X_train_opt, y_train_opt)

                # 评估校准效果
                y_val_proba_original = base_model_wrapped.predict_proba(X_val_opt)[:, 1]
                y_val_proba_calibrated = calibrated_model.predict_proba(X_val_opt)[:, 1]

                brier_original = brier_score_loss(y_val_opt, y_val_proba_original)
                brier_calibrated = brier_score_loss(y_val_opt, y_val_proba_calibrated)
                brier_improvement = brier_original - brier_calibrated

                calibration_result = {
                    'brier_original': brier_original,
                    'brier_calibrated': brier_calibrated,
                    'brier_improvement': brier_improvement,
                    'improvement_significant': brier_improvement > 0.001  # 改善阈值
                }

                logger.info(f"📊 概率校准结果:")
                logger.info(f"   原始Brier分数: {brier_original:.6f}")
                logger.info(f"   校准后Brier分数: {brier_calibrated:.6f}")
                logger.info(f"   改善程度: {brier_improvement:.6f}")
                logger.info(f"   是否采用校准: {calibration_result['improvement_significant']}")

            except Exception as e:
                logger.error(f"❌ 概率校准过程出错: {e}")
                calibration_result = {'improvement_significant': False}

            # 保存模型（根据校准结果决定保存哪个版本）
            model_path = os.path.join(output_dir, "elite_super_meta_model.joblib")
            calibrated_model_path = os.path.join(output_dir, "elite_super_meta_model_calibrated.joblib")

            # 始终保存原始模型
            joblib.dump(final_model, model_path)
            logger.info(f"✅ 原始元模型已保存: {model_path}")

            # 如果校准有效，保存校准模型
            if calibrated_model is not None and calibration_result.get('improvement_significant', False):
                joblib.dump(calibrated_model, calibrated_model_path)
                logger.info(f"✅ 校准元模型已保存: {calibrated_model_path}")

            # 保存特征列表
            features_path = os.path.join(output_dir, "elite_meta_features.json")
            with open(features_path, 'w') as f:
                json.dump(list(X_meta.columns), f, indent=2)

            # 💾 新增：保存训练数据为CSV格式（用于分析和调试）
            logger.info("💾 保存精英元模型训练数据到CSV文件...")

            # 保存特征数据
            x_meta_csv_path = os.path.join(output_dir, "X_meta_features_oof.csv")
            X_meta.to_csv(x_meta_csv_path, index=True)
            logger.info(f"✅ 精英元模型特征数据已保存: {x_meta_csv_path}")

            # 保存目标变量
            y_meta_csv_path = os.path.join(output_dir, "y_meta_target.csv")
            y_meta.to_csv(y_meta_csv_path, index=True, header=True)
            logger.info(f"✅ 精英元模型目标变量已保存: {y_meta_csv_path}")

            # 保存训练数据摘要
            training_data_summary = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'model_type': 'elite_super_meta_model',
                'samples_count': len(X_meta),
                'features_count': len(X_meta.columns),
                'feature_names': list(X_meta.columns),
                'target_distribution': y_meta.value_counts().to_dict(),
                'elite_models_used': self.elite_models,
                'data_range': {
                    'start_time': str(X_meta.index.min()),
                    'end_time': str(X_meta.index.max())
                }
            }

            training_summary_path = os.path.join(output_dir, "elite_training_data_summary.json")
            with open(training_summary_path, 'w', encoding='utf-8') as f:
                json.dump(training_data_summary, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"✅ 精英训练数据摘要已保存: {training_summary_path}")

            # --- 精英元模型SHAP分析 ---
            shap_analysis_results = None
            try:
                # 检查是否启用精英SHAP分析
                import config
                # 使用默认配置中的SHAP设置
                enable_elite_shap = config.DEFAULT_TARGET_CONFIG.get('enable_shap_analysis_elite', True)

                if enable_elite_shap:
                    logger.info("🔍 开始精英元模型SHAP可解释性分析...")

                    # 导入SHAP分析函数
                    from src.core.prediction import perform_shap_analysis

                    # 准备验证数据用于SHAP分析（使用训练数据的一部分）
                    from sklearn.model_selection import train_test_split
                    _, X_val_shap, _, y_val_shap = train_test_split(
                        X_meta, y_meta, test_size=0.2, random_state=42, stratify=y_meta
                    )

                    # 执行SHAP分析
                    shap_analysis_results = perform_shap_analysis(
                        final_model,
                        X_val_shap.values,
                        y_val_shap.values,
                        list(X_meta.columns),
                        output_dir
                    )

                    if shap_analysis_results:
                        logger.info("✅ 精英元模型SHAP分析完成")
                    else:
                        logger.warning("⚠️ 精英元模型SHAP分析未成功完成")
                else:
                    logger.info("⏭️ 精英元模型SHAP分析已禁用")

            except Exception as e_shap:
                logger.warning(f"⚠️ 精英元模型SHAP分析过程中出现错误: {e_shap}")
                logger.warning("SHAP分析失败不会影响精英元模型训练的成功")
                import traceback
                traceback.print_exc(limit=2)

            # 保存训练结果（包含阈值优化和概率校准信息）
            results = {
                'model_type': best_model_name,
                'best_auc': best_score,
                'model_results': model_results,
                'elite_features': list(X_meta.columns),
                'training_samples': X_meta.shape[0],
                'elite_models_used': self.elite_models,
                'shap_analysis': shap_analysis_results,  # 添加SHAP分析结果
                'threshold_optimization': threshold_result,  # 🎯 新增：阈值优化结果
                'probability_calibration': calibration_result,  # 🎯 新增：概率校准结果
                'optimal_threshold': threshold_result.get('optimal_threshold', 0.5) if threshold_result else 0.5,
                'use_calibrated_model': calibration_result.get('improvement_significant', False) if calibration_result else False,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            results_path = os.path.join(output_dir, "elite_meta_training_results.json")
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"🎉 超级元模型训练完成！")
            logger.info(f"📁 模型保存至: {model_path}")
            logger.info(f"📊 最终性能: AUC = {best_score:.4f}")

            return results

        except Exception as e:
            logger.error(f"训练超级元模型时出错: {e}")
            raise

    def _create_lgb_model(self):
        """创建LightGBM模型"""
        import lightgbm as lgb
        return lgb.LGBMClassifier(
            objective='binary',
            metric='binary_logloss',
            boosting_type='gbdt',
            num_leaves=31,
            learning_rate=0.1,
            feature_fraction=0.8,
            bagging_fraction=0.8,
            bagging_freq=5,
            verbose=-1,
            random_state=42
        )

    def _get_lgb_params(self):
        """获取LightGBM参数"""
        return {
            'objective': 'binary',
            'metric': 'auc',  # 使用AUC作为评估指标
            'boosting_type': 'gbdt',
            'num_leaves': 15,  # 减少叶子数防止过拟合
            'learning_rate': 0.05,  # 降低学习率
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'min_data_in_leaf': 10,  # 增加叶子节点最小样本数
            'lambda_l1': 0.1,  # 添加L1正则化
            'lambda_l2': 0.1,  # 添加L2正则化
            'verbose': -1,
            'random_state': 42,
            'force_col_wise': True  # 强制列式训练
        }
