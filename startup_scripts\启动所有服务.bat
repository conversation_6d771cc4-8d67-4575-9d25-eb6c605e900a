@echo off
title All Services - Trading System

echo ========================================
echo    All Services - Trading System
echo ========================================
echo.

echo [INFO] Starting all services...
echo [INFO] Current directory: %CD%
echo.

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not installed or not in PATH
    echo [ERROR] Please install Python and add to system PATH
    pause
    exit /b 1
)

echo [INFO] Python environment check passed
echo.

REM Display service selection menu
:MENU
echo ========================================
echo           Service Selection Menu
echo ========================================
echo.
echo 1. Start Prediction Program (main.py)
echo 2. Start Simulator (SimMain.py)
echo 3. Start Backtest Program (run_backtest.py)
echo 4. Start Unit Tests (run_unit_tests.py)
echo 5. Start Both Prediction and Simulator
echo 6. Exit
echo.
set /p choice=Please select service to start (1-6):

if "%choice%"=="1" goto START_MAIN
if "%choice%"=="2" goto START_SIM
if "%choice%"=="3" goto START_BACKTEST
if "%choice%"=="4" goto START_TESTS
if "%choice%"=="5" goto START_BOTH
if "%choice%"=="6" goto EXIT
echo [ERROR] Invalid selection, please try again
echo.
goto MENU

:START_MAIN
echo.
echo [INFO] Starting prediction program...
start "Prediction Program" cmd /k "python main.py"
goto MENU

:START_SIM
echo.
echo [INFO] Starting simulator...
start "Simulator" cmd /k "python SimMain.py"
goto MENU

:START_BACKTEST
echo.
echo [INFO] Starting backtest program...
start "Backtest Program" cmd /k "python run_backtest.py"
goto MENU

:START_TESTS
echo.
echo [INFO] Starting unit tests...
start "Unit Tests" cmd /k "python run_unit_tests.py"
goto MENU

:START_BOTH
echo.
echo [INFO] Starting both prediction program and simulator...
start "Prediction Program" cmd /k "python main.py"
timeout /t 2 /nobreak >nul
start "Simulator" cmd /k "python SimMain.py"
goto MENU

:EXIT
echo.
echo [INFO] Exiting service launcher
exit /b 0
