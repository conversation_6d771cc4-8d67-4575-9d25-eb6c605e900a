# 分层数据策略实施指南

## 📋 概述

分层数据策略是一种"两全其美"的数据管理方案，兼顾训练效率与模型稳健性：

- **短期数据（7777条）**：用于模型核心训练，保证效率和对近期市场的敏感度
- **长期数据（22222条）**：用于上下文分析，保证模型的泛化能力和对宏观市场状态的理解
- **MTFA数据（智能计算）**：🎯 优化：基于主时间框架计算合理条数，保持相同时间范围覆盖

## 🔧 配置说明

### 新增配置项

在 `config.py` 中新增了以下配置：

```python
# === 分层数据策略配置 ===
ENABLE_LAYERED_DATA_STRATEGY = True  # 🎛️ 分层数据策略总开关
DATA_FETCH_LIMIT = 7777              # 模型核心训练使用的短期数据条数
LONG_TERM_DATA_LIMIT = 22222         # 长期上下文分析数据条数
MTFA_DATA_LIMIT = 7777               # 🎯 基准数据条数，系统会根据时间框架自动计算合理条数
```

### 🎛️ 开关控制

**启用分层策略** (`ENABLE_LAYERED_DATA_STRATEGY = True`)：
- ✅ 使用7777条短期数据进行训练
- ✅ 使用22222条长期数据计算样本权重
- ✅ MTFA特征使用智能计算的数据条数（保持相同时间范围覆盖）

## 🎯 MTFA数据量智能计算

为了确保多时间框架分析的有效性，系统会根据主时间框架自动计算其他时间框架的合理数据条数：

**计算逻辑：**
```
基准：15分钟数据 7777条 → 覆盖约81天

自动计算：
- 30分钟数据：3888条（7777 × 15 ÷ 30）→ 覆盖相同的81天
- 1小时数据：1944条（7777 × 15 ÷ 60）→ 覆盖相同的81天
- 4小时数据：486条（7777 × 15 ÷ 240）→ 覆盖相同的81天
```

**优势：**
- ✅ 所有时间框架覆盖相同的历史时间范围
- ✅ 提供有意义的多时间框架对比分析
- ✅ 避免不同时间框架数据不匹配的问题
- ✅ 缓存文件大小合理（反映实际数据量差异）

**禁用分层策略** (`ENABLE_LAYERED_DATA_STRATEGY = False`)：
- 📋 回退到传统单一数据获取方式
- 📋 所有功能使用DATA_FETCH_LIMIT配置
- 📋 MTFA特征使用1000条传统限制

### 配置建议

- **短期数据**：7777条，平衡训练效率与数据充足性
- **长期数据**：22222条，提供充分的历史上下文
- **MTFA数据**：2000条，确保高时间框架特征的稳定性

## 🚀 核心功能

### 1. 分层数据管理器

`LayeredDataManager` 类负责协调不同层次的数据获取：

```python
from src.core.layered_data_strategy import get_layered_data_manager

# 创建管理器
manager = get_layered_data_manager(binance_client, target_config)

# 获取分层数据
layered_data = manager.fetch_layered_data()
# 返回: {'short_term': df, 'long_term': df, 'mtfa_context': df}
```

### 2. 增强样本权重计算

使用长期数据计算更准确的样本权重：

```python
# 基于长期数据上下文计算权重
weights = manager.calculate_enhanced_sample_weights(
    short_term_data,  # 训练数据
    long_term_data    # 上下文数据
)
```

### 3. 增强MTFA数据获取

独立获取更多MTFA历史数据：

```python
from src.core.layered_data_strategy import fetch_enhanced_mtfa_data

mtfa_data = fetch_enhanced_mtfa_data(
    binance_client, 
    symbol='BTCUSDT',
    timeframes=['30m', '1h', '4h'],
    main_timeframe='15m',
    limit=2000  # 使用MTFA_DATA_LIMIT
)
```

## 📊 实施效果

### 训练流程优化

1. **步骤0**：获取长期历史数据（22222条）用于宏观上下文分析
2. **步骤1**：获取短期数据（7777条）用于模型训练
3. **步骤2**：使用长期数据计算样本权重，应用到短期训练集
4. **步骤3**：MTFA特征独立获取2000条数据，确保指标稳定性

### 性能提升

- ✅ **训练效率**：使用7777条数据训练，速度快
- ✅ **权重质量**：基于22222条数据计算权重，更准确反映市场状态
- ✅ **特征稳定性**：MTFA特征使用2000条数据，指标更可靠
- ✅ **泛化能力**：长期数据提供宏观市场理解

## 🔄 集成说明

### 主训练流程集成

分层数据策略已集成到 `main.py` 的 `run_training_pipeline` 函数中：

1. **数据获取阶段**：自动使用分层策略获取数据
2. **样本权重计算**：优先使用长期数据计算权重
3. **MTFA处理**：使用增强的数据限制

### 自动回退机制

如果分层数据策略失败，系统会自动回退到传统方式：

```python
try:
    # 尝试分层数据策略
    layered_data = manager.fetch_layered_data()
    df_raw = layered_data.get('short_term')
except Exception as e:
    # 回退到传统方式
    df_raw = data_utils.fetch_binance_history(...)
```

## 📈 使用示例

### 基本使用

```python
# 在目标配置中启用相关功能
target_config = {
    'name': 'BTC_15m_UP',
    'symbol': 'BTCUSDT',
    'interval': '15m',
    'enable_mtfa': True,
    'enable_dynamic_sample_weighting': True,
    # ... 其他配置
}

# 运行训练，分层数据策略会自动生效
run_training_pipeline()
```

### 高级配置

```python
# 自定义分层数据限制
config.DATA_FETCH_LIMIT = 5000      # 短期数据
config.LONG_TERM_DATA_LIMIT = 15000  # 长期数据
config.MTFA_DATA_LIMIT = 1500        # MTFA数据
```

## 🧪 测试验证

运行测试脚本验证实施效果：

```bash
python test_layered_data_strategy.py
```

测试内容包括：
- 配置正确性验证
- 数据管理器功能测试
- 样本权重计算测试
- MTFA数据获取测试
- 主程序集成测试

## 📝 最佳实践

### 1. 数据量配置

- 确保 `LONG_TERM_DATA_LIMIT > DATA_FETCH_LIMIT`
- MTFA数据限制建议 >= 1000条
- 根据计算资源调整具体数值

### 2. 功能启用

```python
# 在目标配置中启用相关功能
target_config = {
    'enable_mtfa': True,                          # 启用MTFA特征
    'enable_dynamic_sample_weighting': True,      # 启用动态样本权重
    'enable_enhanced_sample_weighting': True,     # 启用增强样本权重
}
```

### 3. 监控日志

关注训练日志中的分层数据策略信息：

```
[LayeredDataStrategy] 配置 - 短期:7777, 长期:22222, MTFA:2000
✅ 分层数据策略成功，使用短期数据进行训练: 7777 条
📊 长期上下文数据可用: 22222 条
🔄 MTFA上下文数据可用: 2000 条
✅ 分层数据样本权重计算成功，权重范围: [0.1234, 2.5678]
```

## 🔮 未来扩展

### 1. 动态数据量调整

根据市场波动性动态调整数据获取量：

```python
# 高波动期使用更多历史数据
if market_volatility > threshold:
    config.LONG_TERM_DATA_LIMIT = 30000
```

### 2. 多时间框架权重

为不同时间框架的数据设置不同权重：

```python
# 近期数据权重更高
time_weights = {
    'recent': 1.5,    # 最近1000条
    'medium': 1.0,    # 中期数据
    'distant': 0.7    # 远期数据
}
```

### 3. 智能缓存策略

实现分层数据的智能缓存，减少重复获取：

```python
# 缓存长期数据，只更新增量部分
cache_manager.update_incremental(symbol, interval, new_data)
```

## ⚠️ 注意事项

1. **内存使用**：长期数据会增加内存使用，注意监控
2. **API限制**：注意Binance API的调用频率限制
3. **数据一致性**：确保不同层次数据的时间对齐
4. **回测验证**：使用长期数据进行充分的回测验证

## 🎯 总结

分层数据策略成功实现了效率与稳健性的平衡：

- **训练效率**：7777条数据保证快速训练
- **权重质量**：22222条数据提供准确的样本权重
- **特征稳定性**：2000条MTFA数据确保指标可靠性
- **自动回退**：失败时自动使用传统方式
- **完全集成**：无需修改现有训练流程

这种策略让您的模型既能快速响应近期市场变化，又能保持对长期市场规律的理解，是一个真正的"两全其美"解决方案。
