# -*- coding: utf-8 -*-
"""
优化的 Optuna 目标函数模块
改进参数约束处理和试验结果记录
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
import optuna
from optuna.trial import Trial
from dataclasses import dataclass
from enum import Enum

# 导入错误处理基础设施
from .prediction import (
    DataValidationError, ConfigurationError, PredictionExecutionError,
    log_prediction_error, create_error_result, create_success_result
)


class ConstraintViolationType(Enum):
    """约束违反类型枚举"""
    MIN_TRADES = "min_trades"
    MIN_WIN_RATE = "min_win_rate"
    MAX_CONSECUTIVE_LOSSES = "max_consecutive_losses"
    NEGATIVE_PROFIT = "negative_profit"
    TRADE_IMBALANCE = "trade_imbalance"
    INSUFFICIENT_CLASSES = "insufficient_classes"


@dataclass
class OptimizationConstraints:
    """优化约束配置"""
    min_trades: int = 20
    min_win_rate: float = 0.4
    max_consecutive_losses: int = 10
    min_profit_per_trade: float = 0.0
    require_trade_balance: bool = True
    trade_balance_min_ratio: float = 0.1  # 最小方向占比
    
    # 软约束惩罚值（避免使用 -inf）
    constraint_penalty_base: float = -1000.0
    constraint_penalty_multiplier: float = 10.0


@dataclass
class TrialMetrics:
    """试验指标数据结构"""
    # 基础指标
    profit_per_trade: float
    total_trades: int
    win_rate: float
    total_profit: float
    sharpe_ratio: float
    max_consecutive_losses: int
    
    # 交易分布
    up_trades: int
    down_trades: int
    trade_frequency: float
    
    # 约束检查结果
    constraint_violations: List[ConstraintViolationType]
    constraint_penalty: float
    
    # 优化目标
    final_objective: float
    optimization_strategy: str


class OptimizedOptunaObjective:
    """
    优化的 Optuna 目标函数类
    改进参数约束处理和试验结果记录
    """
    
    def __init__(self, 
                 constraints: OptimizationConstraints,
                 optimization_strategy: str = 'composite_score',
                 direction: str = 'maximize',
                 logger: Optional[logging.Logger] = None):
        """
        初始化优化目标函数
        
        Args:
            constraints: 优化约束配置
            optimization_strategy: 优化策略
            direction: 优化方向 ('maximize' 或 'minimize')
            logger: 日志记录器
        """
        self.constraints = constraints
        self.optimization_strategy = optimization_strategy
        self.direction = direction
        self.logger = logger or logging.getLogger(__name__)
        
        # 早停机制
        self.best_score_so_far = float('-inf') if direction == 'maximize' else float('inf')
        self.trials_without_improvement = 0
        self.early_stop_patience = 50
        
    def create_threshold_objective(self, 
                                 y_true: np.ndarray, 
                                 y_proba: np.ndarray,
                                 calculate_metrics_func,
                                 param_ranges: Dict[str, Tuple[float, float]]):
        """
        创建阈值优化目标函数
        
        Args:
            y_true: 真实标签
            y_proba: 预测概率
            calculate_metrics_func: 计算指标的函数
            param_ranges: 参数范围字典
            
        Returns:
            callable: Optuna 目标函数
        """
        def objective(trial: Trial) -> float:
            try:
                # === 🎯 使用 suggest_float 的 low/high 参数进行硬约束 ===
                params = {}
                for param_name, (low, high) in param_ranges.items():
                    if param_name in ['threshold_up', 'threshold_down']:
                        # 阈值参数使用较小的步长
                        params[param_name] = trial.suggest_float(
                            param_name, low=low, high=high, step=0.01
                        )
                    elif param_name in ['confidence_gap_up', 'confidence_gap_down']:
                        # 置信度间隔参数
                        params[param_name] = trial.suggest_float(
                            param_name, low=low, high=high, step=0.005
                        )
                    else:
                        # 其他参数
                        params[param_name] = trial.suggest_float(
                            param_name, low=low, high=high
                        )
                
                # === 计算指标 ===
                metrics = calculate_metrics_func(**params, verbose=False)
                
                # === 创建试验指标对象 ===
                trial_metrics = self._create_trial_metrics(metrics)
                
                # === 约束检查和惩罚计算 ===
                constraint_violations, penalty = self._check_constraints(trial_metrics)
                trial_metrics.constraint_violations = constraint_violations
                trial_metrics.constraint_penalty = penalty
                
                # === 计算优化目标 ===
                if constraint_violations:
                    # 有约束违反时，返回惩罚值而不是 -inf
                    final_objective = penalty
                    self.logger.debug(f"Trial {trial.number}: 约束违反 {constraint_violations}, 惩罚值: {penalty}")
                else:
                    # 无约束违反时，计算正常目标值
                    final_objective = self._calculate_objective(trial_metrics)
                
                trial_metrics.final_objective = final_objective
                
                # === 详细记录试验结果 ===
                self._record_trial_results(trial, trial_metrics, params)
                
                # === 早停检查 ===
                self._check_early_stopping(trial, final_objective)
                
                # === Pruner 支持 ===
                trial.report(final_objective, step=0)
                if trial.should_prune():
                    raise optuna.TrialPruned()
                
                return final_objective
                
            except optuna.TrialPruned:
                raise
            except Exception as e:
                self.logger.error(f"Trial {trial.number} 执行失败: {e}")
                # 返回最差的惩罚值
                return self.constraints.constraint_penalty_base * 10
        
        return objective
    
    def _create_trial_metrics(self, metrics: Dict[str, Any]) -> TrialMetrics:
        """从指标字典创建试验指标对象"""
        return TrialMetrics(
            profit_per_trade=metrics.get('average_profit_per_trade', 0.0),
            total_trades=metrics.get('num_trades', 0),
            win_rate=metrics.get('win_rate', 0.0),
            total_profit=metrics.get('total_profit', 0.0),
            sharpe_ratio=metrics.get('sharpe_ratio', 0.0),
            max_consecutive_losses=metrics.get('max_consecutive_losses', 0),
            up_trades=metrics.get('up_trades', 0),
            down_trades=metrics.get('down_trades', 0),
            trade_frequency=metrics.get('trade_frequency', 0.0),
            constraint_violations=[],
            constraint_penalty=0.0,
            final_objective=0.0,
            optimization_strategy=self.optimization_strategy
        )
    
    def _check_constraints(self, metrics: TrialMetrics) -> Tuple[List[ConstraintViolationType], float]:
        """
        检查约束并计算惩罚值
        
        Returns:
            Tuple[List[ConstraintViolationType], float]: 违反的约束类型列表和惩罚值
        """
        violations = []
        penalty = 0.0
        
        # 约束1: 最小交易次数
        if metrics.total_trades < self.constraints.min_trades:
            violations.append(ConstraintViolationType.MIN_TRADES)
            shortage = self.constraints.min_trades - metrics.total_trades
            penalty += self.constraints.constraint_penalty_base - shortage * self.constraints.constraint_penalty_multiplier
        
        # 约束2: 最小胜率
        if metrics.win_rate < self.constraints.min_win_rate:
            violations.append(ConstraintViolationType.MIN_WIN_RATE)
            shortage = self.constraints.min_win_rate - metrics.win_rate
            penalty += self.constraints.constraint_penalty_base - shortage * 100 * self.constraints.constraint_penalty_multiplier
        
        # 约束3: 最大连续亏损
        if metrics.max_consecutive_losses > self.constraints.max_consecutive_losses:
            violations.append(ConstraintViolationType.MAX_CONSECUTIVE_LOSSES)
            excess = metrics.max_consecutive_losses - self.constraints.max_consecutive_losses
            penalty += self.constraints.constraint_penalty_base - excess * self.constraints.constraint_penalty_multiplier
        
        # 约束4: 最小平均收益
        if metrics.profit_per_trade < self.constraints.min_profit_per_trade:
            violations.append(ConstraintViolationType.NEGATIVE_PROFIT)
            deficit = abs(metrics.profit_per_trade - self.constraints.min_profit_per_trade)
            penalty += self.constraints.constraint_penalty_base - deficit * 1000 * self.constraints.constraint_penalty_multiplier
        
        # 约束5: 交易平衡性
        if (self.constraints.require_trade_balance and 
            metrics.total_trades >= 10):
            total_directional_trades = metrics.up_trades + metrics.down_trades
            if total_directional_trades > 0:
                up_ratio = metrics.up_trades / total_directional_trades
                down_ratio = metrics.down_trades / total_directional_trades
                min_ratio = self.constraints.trade_balance_min_ratio
                
                if up_ratio < min_ratio or down_ratio < min_ratio:
                    violations.append(ConstraintViolationType.TRADE_IMBALANCE)
                    imbalance = abs(0.5 - up_ratio)  # 偏离50%的程度
                    penalty += self.constraints.constraint_penalty_base - imbalance * 100 * self.constraints.constraint_penalty_multiplier
        
        return violations, penalty
    
    def _calculate_objective(self, metrics: TrialMetrics) -> float:
        """计算优化目标值"""
        if self.optimization_strategy == 'profit_first':
            return metrics.total_profit
        
        elif self.optimization_strategy == 'balanced_profit_frequency':
            return metrics.profit_per_trade * np.sqrt(max(1, metrics.total_trades))
        
        elif self.optimization_strategy == 'frequency_under_winrate_constraint':
            if metrics.win_rate > 0.55:
                return metrics.total_trades + metrics.profit_per_trade * 10
            else:
                return metrics.profit_per_trade
        
        elif self.optimization_strategy == 'composite_score':
            if metrics.profit_per_trade > 0:
                # 正收益时：收益与交易次数正相关
                return metrics.profit_per_trade * (1 + np.log(max(1, metrics.total_trades)) * 0.1)
            else:
                # 负收益时：重惩罚，惩罚程度与交易次数成正比
                return metrics.profit_per_trade * (1 + metrics.total_trades * 0.1)
        
        elif self.optimization_strategy == 'risk_adjusted':
            return metrics.profit_per_trade * (1 + metrics.sharpe_ratio * 0.1)
        
        elif self.optimization_strategy == 'total_expected_profit':
            return (
                metrics.total_profit * 0.4 +
                metrics.profit_per_trade * np.sqrt(max(1, metrics.total_trades)) * 0.4 +
                metrics.win_rate * metrics.total_trades * 0.2
            )
        
        else:
            # 默认策略
            return metrics.total_profit
    
    def _record_trial_results(self, trial: Trial, metrics: TrialMetrics, params: Dict[str, Any]) -> None:
        """详细记录试验结果到 trial.user_attrs"""
        # 基础指标
        trial.set_user_attr('profit_per_trade', metrics.profit_per_trade)
        trial.set_user_attr('total_trades', metrics.total_trades)
        trial.set_user_attr('win_rate', metrics.win_rate)
        trial.set_user_attr('total_profit', metrics.total_profit)
        trial.set_user_attr('sharpe_ratio', metrics.sharpe_ratio)
        trial.set_user_attr('max_consecutive_losses', metrics.max_consecutive_losses)
        
        # 交易分布
        trial.set_user_attr('up_trades', metrics.up_trades)
        trial.set_user_attr('down_trades', metrics.down_trades)
        trial.set_user_attr('trade_frequency', metrics.trade_frequency)
        
        # 约束相关
        trial.set_user_attr('constraint_violations', [v.value for v in metrics.constraint_violations])
        trial.set_user_attr('constraint_penalty', metrics.constraint_penalty)
        trial.set_user_attr('constraints_satisfied', len(metrics.constraint_violations) == 0)

        # 记录具体的约束失败原因（用于统计分析）
        for violation in metrics.constraint_violations:
            trial.set_user_attr(f'constraint_{violation.value}', True)
        
        # 约束阈值（用于分析）
        trial.set_user_attr('min_trades_constraint', self.constraints.min_trades)
        trial.set_user_attr('min_win_rate_constraint', self.constraints.min_win_rate)
        trial.set_user_attr('max_consecutive_losses_constraint', self.constraints.max_consecutive_losses)
        
        # 优化相关
        trial.set_user_attr('optimization_strategy', self.optimization_strategy)
        trial.set_user_attr('final_objective', metrics.final_objective)
        trial.set_user_attr('direction', self.direction)
        
        # 参数记录
        for param_name, param_value in params.items():
            trial.set_user_attr(f'param_{param_name}', param_value)
        
        # 性能分析指标
        if metrics.total_trades > 0:
            trial.set_user_attr('profit_per_trade_normalized', metrics.total_profit / metrics.total_trades)
            trial.set_user_attr('trade_balance_ratio', 
                              min(metrics.up_trades, metrics.down_trades) / max(metrics.up_trades + metrics.down_trades, 1))
        
        # 风险指标
        trial.set_user_attr('risk_adjusted_return', 
                          metrics.profit_per_trade * (1 + metrics.sharpe_ratio * 0.1))
        
        # 记录为何此试验最优（如果是最优的话）
        if (self.direction == 'maximize' and metrics.final_objective > self.best_score_so_far) or \
           (self.direction == 'minimize' and metrics.final_objective < self.best_score_so_far):
            trial.set_user_attr('is_best_so_far', True)
            trial.set_user_attr('improvement_over_previous_best', 
                              metrics.final_objective - self.best_score_so_far)
            
            # 分析为什么这个试验更好
            improvement_reasons = []
            if len(metrics.constraint_violations) == 0:
                improvement_reasons.append("满足所有约束")
            if metrics.profit_per_trade > 0:
                improvement_reasons.append(f"正收益({metrics.profit_per_trade:.4f})")
            if metrics.win_rate > 0.5:
                improvement_reasons.append(f"高胜率({metrics.win_rate:.3f})")
            if metrics.total_trades >= self.constraints.min_trades:
                improvement_reasons.append(f"充足交易({metrics.total_trades})")
            
            trial.set_user_attr('improvement_reasons', improvement_reasons)
        else:
            trial.set_user_attr('is_best_so_far', False)
    
    def _check_early_stopping(self, trial: Trial, objective_value: float) -> None:
        """检查早停条件"""
        if (self.direction == 'maximize' and objective_value > self.best_score_so_far) or \
           (self.direction == 'minimize' and objective_value < self.best_score_so_far):
            self.best_score_so_far = objective_value
            self.trials_without_improvement = 0
        else:
            self.trials_without_improvement += 1
        
        if self.trials_without_improvement >= self.early_stop_patience:
            self.logger.info(f"早停触发: {self.early_stop_patience} 次试验无改进")
            trial.study.stop()


    def create_model_objective(self,
                             X_train: np.ndarray,
                             y_train: np.ndarray,
                             X_val: np.ndarray,
                             y_val: np.ndarray,
                             param_grid: Dict[str, Tuple],
                             model_type: str = 'lgbm',
                             cv_folds: int = 3):
        """
        创建模型超参数优化目标函数

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征
            y_val: 验证标签
            param_grid: 参数网格
            model_type: 模型类型
            cv_folds: 交叉验证折数

        Returns:
            callable: Optuna 目标函数
        """
        def objective(trial: Trial) -> float:
            try:
                # === 🎯 使用参数网格定义搜索空间 ===
                params = {}
                for param_name, param_config in param_grid.items():
                    param_type = param_config[0]

                    if param_type == 'int':
                        low, high = param_config[1], param_config[2]
                        params[param_name] = trial.suggest_int(param_name, low=low, high=high)

                    elif param_type == 'float':
                        low, high = param_config[1], param_config[2]
                        log_scale = param_config[3] if len(param_config) > 3 else False
                        params[param_name] = trial.suggest_float(
                            param_name, low=low, high=high, log=log_scale
                        )

                    elif param_type == 'categorical':
                        choices = param_config[1]
                        params[param_name] = trial.suggest_categorical(param_name, choices)

                # === 模型训练和评估 ===
                if model_type.lower() == 'lgbm':
                    score = self._evaluate_lgbm_params(
                        trial, params, X_train, y_train, X_val, y_val, cv_folds
                    )
                else:
                    raise ValueError(f"不支持的模型类型: {model_type}")

                # === 记录模型相关指标 ===
                trial.set_user_attr('model_type', model_type)
                trial.set_user_attr('cv_folds', cv_folds)
                trial.set_user_attr('train_samples', len(X_train))
                trial.set_user_attr('val_samples', len(X_val))

                # 记录参数
                for param_name, param_value in params.items():
                    trial.set_user_attr(f'param_{param_name}', param_value)

                return score

            except optuna.TrialPruned:
                raise
            except Exception as e:
                self.logger.error(f"模型优化 Trial {trial.number} 执行失败: {e}")
                return self.constraints.constraint_penalty_base

        return objective

    def _evaluate_lgbm_params(self,
                             trial: Trial,
                             params: Dict[str, Any],
                             X_train: np.ndarray,
                             y_train: np.ndarray,
                             X_val: np.ndarray,
                             y_val: np.ndarray,
                             cv_folds: int) -> float:
        """评估 LightGBM 参数"""
        try:
            import lightgbm as lgb
            from sklearn.model_selection import TimeSeriesSplit
            from sklearn.metrics import f1_score, precision_score, recall_score

            # 固定参数
            fixed_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'verbosity': -1,
                'random_state': 42 + trial.number,
                'n_jobs': -1
            }
            params.update(fixed_params)

            # 交叉验证
            if cv_folds > 1:
                tscv = TimeSeriesSplit(n_splits=cv_folds)
                cv_scores = []

                for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(X_train)):
                    X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]
                    y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]

                    # 检查类别分布
                    if len(np.unique(y_fold_val)) < 2:
                        cv_scores.append(0.0)
                        continue

                    # 训练模型
                    train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
                    val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)

                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        callbacks=[lgb.early_stopping(50, verbose=False)],
                        num_boost_round=1000
                    )

                    # 预测和评估
                    y_pred_proba = model.predict(X_fold_val)
                    y_pred = (y_pred_proba > 0.5).astype(int)

                    # 计算指标
                    if len(np.unique(y_pred)) >= 2:
                        score = f1_score(y_fold_val, y_pred)
                    else:
                        score = 0.0

                    cv_scores.append(score)

                    # 中间结果报告（用于剪枝）
                    trial.report(np.mean(cv_scores), step=fold_idx)
                    if trial.should_prune():
                        raise optuna.TrialPruned()

                final_score = np.mean(cv_scores)

                # 记录 CV 结果
                trial.set_user_attr('cv_scores', cv_scores)
                trial.set_user_attr('cv_mean', final_score)
                trial.set_user_attr('cv_std', np.std(cv_scores))

            else:
                # 单次验证
                train_data = lgb.Dataset(X_train, label=y_train)
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=[val_data],
                    callbacks=[lgb.early_stopping(50, verbose=False)],
                    num_boost_round=1000
                )

                y_pred_proba = model.predict(X_val)
                y_pred = (y_pred_proba > 0.5).astype(int)

                if len(np.unique(y_pred)) >= 2:
                    final_score = f1_score(y_val, y_pred)

                    # 记录详细指标
                    trial.set_user_attr('precision', precision_score(y_val, y_pred))
                    trial.set_user_attr('recall', recall_score(y_val, y_pred))
                else:
                    final_score = 0.0

                trial.set_user_attr('best_iteration', model.best_iteration)

            return final_score

        except Exception as e:
            self.logger.error(f"LightGBM 评估失败: {e}")
            return 0.0


def create_optimized_study(direction: str = 'maximize',
                         sampler_config: Optional[Dict[str, Any]] = None,
                         pruner_config: Optional[Dict[str, Any]] = None) -> optuna.Study:
    """
    创建优化的 Optuna Study

    Args:
        direction: 优化方向
        sampler_config: 采样器配置
        pruner_config: 剪枝器配置

    Returns:
        optuna.Study: 配置好的 Study 对象
    """
    # 默认采样器配置
    default_sampler_config = {
        'seed': 42,
        'n_startup_trials': 20,
        'n_ei_candidates': 24,
        'multivariate': True,
        'warn_independent_sampling': False
    }
    if sampler_config:
        default_sampler_config.update(sampler_config)

    # 默认剪枝器配置
    default_pruner_config = {
        'n_startup_trials': 10,
        'n_warmup_steps': 5,
        'interval_steps': 1
    }
    if pruner_config:
        default_pruner_config.update(pruner_config)

    study = optuna.create_study(
        direction=direction,
        sampler=optuna.samplers.TPESampler(**default_sampler_config),
        pruner=optuna.pruners.MedianPruner(**default_pruner_config)
    )

    return study


def analyze_study_results(study: optuna.Study,
                        top_n: int = 10,
                        logger: Optional[logging.Logger] = None) -> Dict[str, Any]:
    """
    分析 Optuna Study 结果

    Args:
        study: 完成的 Study 对象
        top_n: 分析前 N 个最佳试验
        logger: 日志记录器

    Returns:
        Dict[str, Any]: 分析结果
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # 获取最佳试验
    best_trial = study.best_trial

    # 获取前 N 个最佳试验
    trials_df = study.trials_dataframe()
    if study.direction == optuna.study.StudyDirection.MAXIMIZE:
        top_trials = trials_df.nlargest(top_n, 'value')
    else:
        top_trials = trials_df.nsmallest(top_n, 'value')

    # 分析结果
    analysis = {
        'best_trial': {
            'number': best_trial.number,
            'value': best_trial.value,
            'params': best_trial.params,
            'user_attrs': best_trial.user_attrs
        },
        'study_stats': {
            'n_trials': len(study.trials),
            'n_complete_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]),
            'n_pruned_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED]),
            'n_failed_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.FAIL])
        },
        'top_trials': top_trials.to_dict('records'),
        'parameter_importance': {}
    }

    # 参数重要性分析
    try:
        importance = optuna.importance.get_param_importances(study)
        analysis['parameter_importance'] = importance

        logger.info("参数重要性排序:")
        for param, importance_score in sorted(importance.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {param}: {importance_score:.4f}")
    except Exception as e:
        logger.warning(f"参数重要性分析失败: {e}")

    # 约束满足情况分析
    constraint_satisfied_trials = [
        t for t in study.trials
        if t.user_attrs.get('constraints_satisfied', False)
    ]

    analysis['constraint_analysis'] = {
        'total_trials': len(study.trials),
        'constraint_satisfied_trials': len(constraint_satisfied_trials),
        'constraint_satisfaction_rate': len(constraint_satisfied_trials) / len(study.trials) if study.trials else 0
    }

    logger.info(f"约束满足率: {analysis['constraint_analysis']['constraint_satisfaction_rate']:.2%}")

    return analysis
