#!/usr/bin/env python3
"""
基础模型偏向诊断脚本
检查UP/DOWN基础模型是否存在偏向问题，这可能是元模型偏向的根源
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_base_model_predictions():
    """加载基础模型的预测结果"""
    logger.info("📊 加载基础模型预测结果...")
    
    results = {}
    
    # UP模型目录
    up_model_dir = "trained_models_btc_15m_up"
    down_model_dir = "trained_models_btc_15m_down"
    
    for model_type, model_dir in [("UP", up_model_dir), ("DOWN", down_model_dir)]:
        try:
            # 查找最新的模型文件
            model_files = list(Path(model_dir).glob("model_BTC_15m_*_calibrated.joblib"))
            if not model_files:
                logger.warning(f"未找到{model_type}模型文件")
                continue
            
            # 加载一个代表性模型进行分析
            model_file = model_files[0]
            logger.info(f"加载{model_type}模型: {model_file}")
            
            # 这里我们主要关注模型的配置和特征重要性
            results[model_type] = {
                'model_file': str(model_file),
                'model_dir': model_dir
            }
            
        except Exception as e:
            logger.error(f"加载{model_type}模型失败: {e}")
    
    return results

def analyze_base_model_configs():
    """分析基础模型配置"""
    logger.info("🔍 分析基础模型配置...")
    
    try:
        up_config = config.get_target_config('BTC_15m_UP')
        down_config = config.get_target_config('BTC_15m_DOWN')
        
        logger.info("UP模型配置分析:")
        logger.info(f"  类别权重: {up_config.get('class_weight', 'None')}")
        logger.info(f"  目标阈值: {up_config.get('threshold_up', 'None')}")
        logger.info(f"  动态样本权重: {up_config.get('enable_dynamic_sample_weighting', False)}")
        logger.info(f"  质量权重: {up_config.get('enable_meta_label_quality_weighting', False)}")
        
        logger.info("DOWN模型配置分析:")
        logger.info(f"  类别权重: {down_config.get('class_weight', 'None')}")
        logger.info(f"  目标阈值: {down_config.get('threshold_down', 'None')}")
        logger.info(f"  动态样本权重: {down_config.get('enable_dynamic_sample_weighting', False)}")
        logger.info(f"  质量权重: {down_config.get('enable_meta_label_quality_weighting', False)}")
        
        # 检查配置一致性
        up_weight = up_config.get('class_weight', {})
        down_weight = down_config.get('class_weight', {})
        
        if up_weight != down_weight:
            logger.warning("⚠️ UP和DOWN模型的类别权重不一致！")
            logger.info(f"  UP权重: {up_weight}")
            logger.info(f"  DOWN权重: {down_weight}")
        
        return {
            'up_config': up_config,
            'down_config': down_config,
            'weight_consistent': up_weight == down_weight
        }
        
    except Exception as e:
        logger.error(f"配置分析失败: {e}")
        return None

def check_feature_importance_bias():
    """检查特征重要性是否存在偏向"""
    logger.info("📈 检查特征重要性偏向...")
    
    try:
        # 查找特征重要性报告
        up_importance_files = list(Path("trained_models_btc_15m_up").glob("*feature_importance*.csv"))
        down_importance_files = list(Path("trained_models_btc_15m_down").glob("*feature_importance*.csv"))
        
        results = {}
        
        for model_type, files in [("UP", up_importance_files), ("DOWN", down_importance_files)]:
            if not files:
                logger.warning(f"未找到{model_type}模型特征重要性文件")
                continue
            
            # 读取最新的特征重要性文件
            importance_file = files[0]
            df_importance = pd.read_csv(importance_file)
            
            # 分析top特征
            top_features = df_importance.head(10)
            logger.info(f"{model_type}模型Top 10特征:")
            for idx, row in top_features.iterrows():
                logger.info(f"  {row.get('feature', 'unknown')}: {row.get('importance', 0):.4f}")
            
            # 检查是否有明显的方向性偏向特征
            directional_features = []
            for _, row in top_features.iterrows():
                feature_name = row.get('feature', '')
                if any(keyword in feature_name.lower() for keyword in ['up', 'down', 'bull', 'bear']):
                    directional_features.append(feature_name)
            
            results[model_type] = {
                'top_features': top_features,
                'directional_features': directional_features
            }
            
            if directional_features:
                logger.warning(f"⚠️ {model_type}模型发现方向性偏向特征: {directional_features}")
        
        return results
        
    except Exception as e:
        logger.error(f"特征重要性分析失败: {e}")
        return None

def suggest_base_model_fixes(config_analysis, importance_analysis):
    """基于分析结果提出基础模型修复建议"""
    logger.info("💡 生成基础模型修复建议...")
    
    suggestions = []
    
    # 1. 配置修复建议
    if config_analysis and not config_analysis.get('weight_consistent', True):
        suggestions.append("🔧 统一UP和DOWN模型的类别权重配置")
    
    # 2. 特征工程建议
    if importance_analysis:
        for model_type, analysis in importance_analysis.items():
            if analysis.get('directional_features'):
                suggestions.append(f"🔧 检查{model_type}模型的方向性偏向特征，可能需要特征工程优化")
    
    # 3. 通用建议
    suggestions.extend([
        "📊 重新评估基础模型的目标变量定义，确保UP/DOWN标签平衡",
        "⚖️ 检查训练数据的时间分布，避免某个时期的市场偏向",
        "🎯 考虑使用更平衡的评估指标，如F1-score或AUC",
        "🔄 重新训练基础模型，使用修正后的配置"
    ])
    
    logger.info("修复建议:")
    for i, suggestion in enumerate(suggestions, 1):
        logger.info(f"  {i}. {suggestion}")
    
    return suggestions

def main():
    """主函数"""
    logger.info("🎯 开始基础模型偏向诊断...")
    
    try:
        # 1. 加载模型信息
        model_info = load_base_model_predictions()
        
        # 2. 分析配置
        config_analysis = analyze_base_model_configs()
        
        # 3. 检查特征重要性
        importance_analysis = check_feature_importance_bias()
        
        # 4. 生成修复建议
        suggestions = suggest_base_model_fixes(config_analysis, importance_analysis)
        
        logger.info("🎯 基础模型偏向诊断完成！")
        
        # 5. 生成诊断报告
        report = {
            'timestamp': pd.Timestamp.now(),
            'model_info': model_info,
            'config_analysis': config_analysis,
            'importance_analysis': importance_analysis,
            'suggestions': suggestions
        }
        
        return report
        
    except Exception as e:
        logger.error(f"❌ 基础模型偏向诊断失败: {e}")
        return None

if __name__ == "__main__":
    report = main()
    if report:
        print("\n🎉 基础模型偏向诊断完成！")
        print("📋 请查看日志中的详细分析和建议")
    else:
        print("\n❌ 基础模型偏向诊断失败！")
