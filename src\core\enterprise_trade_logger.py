#!/usr/bin/env python3
# enterprise_trade_logger.py
"""
企业级交易日志记录器

主要功能：
1. 异步日志写入（基于队列和线程）
2. 智能日志轮转（按日期/大小）
3. 分层目录结构管理
4. 高效数据分析接口
5. 线程安全和优雅关闭

作者: AugmentCode
版本: Enterprise v1.0
"""

import os
import csv
import json
import queue
import threading
import time
import atexit
import logging
import glob
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd


class LogRotationManager:
    """日志轮转管理器"""
    
    def __init__(self, base_dir: str, max_file_size: int = 10 * 1024 * 1024):
        """
        初始化日志轮转管理器
        
        Args:
            base_dir: 日志根目录
            max_file_size: 最大文件大小（字节），默认10MB
        """
        self.base_dir = Path(base_dir)
        self.max_file_size = max_file_size
        self.current_date = None
        self.current_file_path = None
        self.part_number = 1
        
        # 确保基础目录存在
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    def get_current_log_file(self) -> Tuple[Path, bool]:
        """
        获取当前应该写入的日志文件路径
        
        Returns:
            (文件路径, 是否为新文件)
        """
        today = datetime.now().strftime('%Y-%m-%d')
        year = datetime.now().strftime('%Y')
        month = datetime.now().strftime('%m')
        
        # 创建年/月目录结构
        log_dir = self.base_dir / year / month
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查是否需要日期轮转
        if self.current_date != today:
            self.current_date = today
            self.part_number = 1
            self.current_file_path = log_dir / f"trades_{today}.csv"
        
        # 检查是否需要大小轮转
        if (self.current_file_path and 
            self.current_file_path.exists() and 
            self.current_file_path.stat().st_size >= self.max_file_size):
            
            self.part_number += 1
            self.current_file_path = log_dir / f"trades_{today}_part_{self.part_number}.csv"
        
        is_new_file = not self.current_file_path.exists()
        return self.current_file_path, is_new_file


class AsyncLogWriter:
    """异步日志写入器"""
    
    def __init__(self, rotation_manager: LogRotationManager, 
                 batch_size: int = 10, flush_interval: float = 1.0):
        """
        初始化异步日志写入器
        
        Args:
            rotation_manager: 日志轮转管理器
            batch_size: 批量写入大小
            flush_interval: 刷新间隔（秒）
        """
        self.rotation_manager = rotation_manager
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        
        # 队列和线程控制
        self.write_queue = queue.Queue(maxsize=1000)
        self.stop_event = threading.Event()
        self.writer_thread = None
        self.file_lock = threading.Lock()
        
        # 统计信息
        self.total_writes = 0
        self.failed_writes = 0
        
        # 日志记录器
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """启动异步写入线程"""
        if self.writer_thread is None or not self.writer_thread.is_alive():
            self.stop_event.clear()
            self.writer_thread = threading.Thread(
                target=self._writer_worker, 
                name="TradeLogWriter",
                daemon=True
            )
            self.writer_thread.start()
            self.logger.info("异步日志写入线程已启动")
    
    def stop(self, timeout: float = 5.0):
        """停止异步写入线程"""
        if self.writer_thread and self.writer_thread.is_alive():
            self.logger.info("正在停止异步日志写入线程...")
            self.stop_event.set()
            self.writer_thread.join(timeout=timeout)
            
            if self.writer_thread.is_alive():
                self.logger.warning("异步写入线程未能在指定时间内停止")
            else:
                self.logger.info("异步日志写入线程已停止")
    
    def put_trade_data(self, trade_data: Dict[str, Any], timeout: float = 1.0):
        """
        将交易数据放入写入队列
        
        Args:
            trade_data: 交易数据字典
            timeout: 超时时间
        """
        try:
            self.write_queue.put(trade_data, timeout=timeout)
        except queue.Full:
            self.failed_writes += 1
            self.logger.error("写入队列已满，丢弃交易数据")
            raise RuntimeError("日志写入队列已满")
    
    def flush(self, timeout: float = 5.0):
        """强制刷新队列中的所有数据"""
        # 放入一个特殊的刷新标记
        try:
            self.write_queue.put({"__FLUSH__": True}, timeout=timeout)
        except queue.Full:
            self.logger.warning("无法放入刷新标记，队列已满")
    
    def _writer_worker(self):
        """后台写入工作线程"""
        batch_data = []
        last_flush_time = time.time()
        
        while not self.stop_event.is_set():
            try:
                # 尝试从队列获取数据
                try:
                    data = self.write_queue.get(timeout=0.1)
                except queue.Empty:
                    # 检查是否需要定时刷新
                    if (batch_data and 
                        time.time() - last_flush_time >= self.flush_interval):
                        self._write_batch(batch_data)
                        batch_data = []
                        last_flush_time = time.time()
                    continue
                
                # 处理刷新标记
                if isinstance(data, dict) and data.get("__FLUSH__"):
                    if batch_data:
                        self._write_batch(batch_data)
                        batch_data = []
                    last_flush_time = time.time()
                    continue
                
                # 添加到批次
                batch_data.append(data)
                
                # 检查是否达到批量大小
                if len(batch_data) >= self.batch_size:
                    self._write_batch(batch_data)
                    batch_data = []
                    last_flush_time = time.time()
                
            except Exception as e:
                self.logger.error(f"写入线程异常: {e}")
                self.failed_writes += 1
        
        # 线程停止前，处理剩余数据
        if batch_data:
            self._write_batch(batch_data)
        
        # 处理队列中剩余的数据
        remaining_data = []
        try:
            while True:
                data = self.write_queue.get_nowait()
                if not (isinstance(data, dict) and data.get("__FLUSH__")):
                    remaining_data.append(data)
        except queue.Empty:
            pass

        if remaining_data:
            self._write_batch(remaining_data)
        
        self.logger.info("写入线程工作完成")
    
    def _write_batch(self, batch_data: List[Dict[str, Any]]):
        """批量写入数据到CSV文件"""
        if not batch_data:
            return
        
        with self.file_lock:
            try:
                # 获取当前日志文件
                file_path, is_new_file = self.rotation_manager.get_current_log_file()
                
                # 获取字段名
                fieldnames = self._get_csv_fieldnames()
                
                # 写入数据
                with open(file_path, 'a', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    # 如果是新文件，写入表头
                    if is_new_file:
                        writer.writeheader()
                    
                    # 写入数据行
                    for data in batch_data:
                        writer.writerow(data)
                
                self.total_writes += len(batch_data)
                self.logger.debug(f"成功写入 {len(batch_data)} 条交易记录到 {file_path}")
                
            except Exception as e:
                self.failed_writes += len(batch_data)
                self.logger.error(f"批量写入失败: {e}")
    
    def _get_csv_fieldnames(self) -> List[str]:
        """获取CSV字段名列表（V2.0增强版）"""
        return [
            # 基础交易信息
            'trade_id', 'entry_timestamp', 'exit_timestamp', 'target_name',
            'symbol', 'direction', 'entry_price', 'exit_price', 'amount',
            'payout_ratio', 'result', 'profit_loss', 'exit_reason',

            # 模型预测信息
            'entry_signal_probability', 'entry_neutral_probability',
            'entry_opposite_probability', 'meta_model_inputs',

            # V2.0新增：个体模型概率信息
            'individual_model_probabilities',

            # V2.0新增：基础模型预测信息（用于元模型）
            'base_model_predictions',

            # 市场状态信息
            'entry_market_regime', 'entry_atr_percent', 'entry_adx_value',

            # 核心特征快照
            'entry_top_features'
        ]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取写入统计信息"""
        return {
            "total_writes": self.total_writes,
            "failed_writes": self.failed_writes,
            "queue_size": self.write_queue.qsize(),
            "is_running": self.writer_thread and self.writer_thread.is_alive()
        }


class TradeDataAnalyzer:
    """交易数据分析器"""
    
    def __init__(self, base_dir: str):
        """
        初始化交易数据分析器
        
        Args:
            base_dir: 日志根目录
        """
        self.base_dir = Path(base_dir)
        self.logger = logging.getLogger(__name__)
    
    def load_trade_logs(self, start_date: Optional[str] = None, 
                       end_date: Optional[str] = None) -> pd.DataFrame:
        """
        加载指定日期范围的交易日志
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            合并后的DataFrame
        """
        # 查找符合条件的CSV文件
        csv_files = self._find_csv_files(start_date, end_date)
        
        if not csv_files:
            self.logger.warning(f"未找到符合条件的日志文件")
            return pd.DataFrame()
        
        # 读取并合并所有CSV文件
        dataframes = []
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                if not df.empty:
                    dataframes.append(df)
                    self.logger.debug(f"成功读取 {file_path}: {len(df)} 条记录")
            except Exception as e:
                self.logger.error(f"读取文件失败 {file_path}: {e}")
        
        if not dataframes:
            return pd.DataFrame()
        
        # 合并所有DataFrame
        combined_df = pd.concat(dataframes, ignore_index=True)
        
        # 解析日期时间字段
        self._parse_datetime_columns(combined_df)
        
        # 解析JSON字段
        self._parse_json_columns(combined_df)
        
        self.logger.info(f"成功加载 {len(combined_df)} 条交易记录")
        return combined_df
    
    def _find_csv_files(self, start_date: Optional[str], 
                       end_date: Optional[str]) -> List[Path]:
        """查找符合日期范围的CSV文件"""
        csv_files = []
        
        # 如果没有指定日期范围，返回所有文件
        if not start_date and not end_date:
            pattern = str(self.base_dir / "**" / "*.csv")
            csv_files = [Path(f) for f in glob.glob(pattern, recursive=True)]
        else:
            # 解析日期范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d') if start_date else datetime.min
            end_dt = datetime.strptime(end_date, '%Y-%m-%d') if end_date else datetime.max
            
            # 遍历年/月目录结构
            for year_dir in self.base_dir.iterdir():
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue
                
                for month_dir in year_dir.iterdir():
                    if not month_dir.is_dir() or not month_dir.name.isdigit():
                        continue
                    
                    # 检查该月是否在日期范围内
                    month_start = datetime(int(year_dir.name), int(month_dir.name), 1)
                    month_end = month_start.replace(day=28) + timedelta(days=4)
                    month_end = month_end - timedelta(days=month_end.day)
                    
                    if month_end < start_dt or month_start > end_dt:
                        continue
                    
                    # 查找该月的CSV文件
                    for csv_file in month_dir.glob("*.csv"):
                        # 从文件名提取日期
                        file_date = self._extract_date_from_filename(csv_file.name)
                        if file_date and start_dt <= file_date <= end_dt:
                            csv_files.append(csv_file)
        
        return sorted(csv_files)
    
    def _extract_date_from_filename(self, filename: str) -> Optional[datetime]:
        """从文件名提取日期"""
        try:
            # 匹配 trades_YYYY-MM-DD.csv 或 trades_YYYY-MM-DD_part_N.csv
            if filename.startswith("trades_") and filename.endswith(".csv"):
                date_part = filename[7:17]  # 提取 YYYY-MM-DD 部分
                return datetime.strptime(date_part, '%Y-%m-%d')
        except ValueError:
            pass
        return None
    
    def _parse_datetime_columns(self, df: pd.DataFrame):
        """解析日期时间列"""
        datetime_columns = ['entry_timestamp', 'exit_timestamp']
        for col in datetime_columns:
            if col in df.columns:
                try:
                    df[col] = pd.to_datetime(df[col])
                except Exception as e:
                    self.logger.warning(f"解析日期时间列失败 {col}: {e}")
    
    def _parse_json_columns(self, df: pd.DataFrame):
        """解析JSON列"""
        json_columns = ['meta_model_inputs', 'entry_top_features']
        for col in json_columns:
            if col in df.columns:
                try:
                    def safe_json_parse(x):
                        if pd.isna(x) or x == '' or x is None:
                            return None
                        try:
                            return json.loads(str(x))
                        except (json.JSONDecodeError, TypeError):
                            return None

                    df[col] = df[col].apply(safe_json_parse)
                except Exception as e:
                    self.logger.warning(f"解析JSON列失败 {col}: {e}")


class EnterpriseTradeLogger:
    """
    企业级交易日志记录器

    主要功能：
    - 异步日志写入
    - 智能日志轮转
    - 分层目录结构
    - 高效数据分析
    - 线程安全操作
    """

    def __init__(self, base_log_dir: str = "trading_logs",
                 max_file_size: int = 10 * 1024 * 1024,
                 queue_maxsize: int = 1000,
                 batch_size: int = 10,
                 flush_interval: float = 1.0):
        """
        初始化企业级交易日志记录器

        Args:
            base_log_dir: 日志根目录
            max_file_size: 最大文件大小（字节）
            queue_maxsize: 队列最大大小
            batch_size: 批量写入大小
            flush_interval: 刷新间隔（秒）
        """
        self.base_log_dir = base_log_dir

        # 初始化组件
        self.rotation_manager = LogRotationManager(base_log_dir, max_file_size)
        self.async_writer = AsyncLogWriter(self.rotation_manager, batch_size, flush_interval)
        self.data_analyzer = TradeDataAnalyzer(base_log_dir)

        # 暂存待平仓的交易
        self.pending_trades = {}
        self.pending_lock = threading.Lock()

        # 统计信息
        self.total_entries = 0
        self.total_exits = 0

        # 日志记录器
        self.logger = logging.getLogger(__name__)

        # 注册退出处理
        atexit.register(self.stop)

    def start(self):
        """启动企业级日志记录器"""
        self.async_writer.start()
        self.logger.info("企业级交易日志记录器已启动")

    def stop(self, timeout: float = 10.0):
        """停止企业级日志记录器"""
        self.logger.info("正在停止企业级交易日志记录器...")

        # 刷新队列
        self.flush()

        # 停止异步写入器
        self.async_writer.stop(timeout)

        self.logger.info("企业级交易日志记录器已停止")

    def record_trade_entry(self, target_name: str, symbol: str, direction: str,
                          entry_price: float, amount: float,
                          payout_ratio: float = 0.85,
                          trade_id: Optional[str] = None,
                          context_data: Optional[Dict[str, Any]] = None) -> str:
        """
        记录交易开仓信息（异步）

        Args:
            target_name: 目标策略名称
            symbol: 交易对符号
            direction: 交易方向 (LONG/SHORT)
            entry_price: 开仓价格
            amount: 交易金额
            payout_ratio: 盈利比例
            trade_id: 交易ID（可选，自动生成）
            context_data: 上下文数据（可选）

        Returns:
            交易ID
        """
        # 参数验证
        if direction.upper() not in ['LONG', 'SHORT']:
            raise ValueError(f"无效的交易方向: {direction}")

        if entry_price <= 0:
            raise ValueError(f"开仓价格必须大于0: {entry_price}")

        if amount <= 0:
            raise ValueError(f"交易金额必须大于0: {amount}")

        # 生成交易ID
        if trade_id is None:
            import uuid
            timestamp = int(time.time() * 1000000)  # 使用微秒确保唯一性
            thread_id = threading.get_ident()
            unique_suffix = str(uuid.uuid4())[:8]  # 添加UUID确保唯一性
            trade_id = f"{target_name}_{timestamp}_{thread_id}_{unique_suffix}"

        # 处理上下文数据
        processed_context = self._process_context_data(context_data)

        # 构建开仓记录
        entry_data = {
            'trade_id': trade_id,
            'entry_timestamp': datetime.now().isoformat(),
            'target_name': target_name,
            'symbol': symbol,
            'direction': direction.upper(),
            'entry_price': entry_price,
            'amount': amount,
            'payout_ratio': payout_ratio,

            # 添加上下文信息
            **processed_context
        }

        # 保存到暂存区
        with self.pending_lock:
            self.pending_trades[trade_id] = entry_data.copy()

        self.total_entries += 1

        # V2.0修复：立即写入开仓记录到CSV
        try:
            # 创建完整的CSV记录（包含空的平仓字段）
            csv_record = entry_data.copy()
            csv_record.update({
                'exit_timestamp': None,
                'exit_price': None,
                'result': None,
                'profit_loss': None,
                'exit_reason': None
            })

            # 添加到异步写入队列
            self.async_writer.put_trade_data(csv_record)

        except Exception as e:
            self.logger.error(f"添加开仓记录到写入队列失败: {e}")

        # 记录日志
        context_summary = self._get_context_summary(processed_context)
        self.logger.info(f"交易开仓: {trade_id} - {symbol} {direction} "
                        f"@{entry_price}, 金额: {amount}{context_summary}")

        return trade_id

    def record_trade_exit(self, trade_id: str, exit_price: float,
                         result: str, exit_reason: str = "expired") -> bool:
        """
        记录交易平仓信息（异步）

        Args:
            trade_id: 交易ID
            exit_price: 平仓价格
            result: 交易结果 (WIN 或 LOSS)
            exit_reason: 平仓原因

        Returns:
            是否成功记录
        """
        # 验证参数
        result = result.upper()
        if result not in ['WIN', 'LOSS']:
            raise ValueError(f"无效的交易结果: {result}")

        if exit_price <= 0:
            raise ValueError(f"平仓价格必须大于0: {exit_price}")

        # 获取开仓信息
        with self.pending_lock:
            if trade_id not in self.pending_trades:
                self.logger.error(f"未找到交易ID: {trade_id}")
                return False

            entry_data = self.pending_trades.pop(trade_id)

        # 计算盈亏
        amount = entry_data['amount']
        payout_ratio = entry_data['payout_ratio']

        if result == 'WIN':
            profit_loss = amount * payout_ratio
        else:  # LOSS
            profit_loss = -amount

        # 构建完整的交易记录
        complete_trade_record = {
            **entry_data,  # 包含所有开仓信息和上下文
            'exit_timestamp': datetime.now().isoformat(),
            'exit_price': exit_price,
            'result': result,
            'profit_loss': profit_loss,
            'exit_reason': exit_reason
        }

        # 异步写入队列
        try:
            self.async_writer.put_trade_data(complete_trade_record)
            self.total_exits += 1

            self.logger.info(f"交易平仓: {trade_id} - {result}, 盈亏: {profit_loss:.2f}")
            return True

        except RuntimeError as e:
            self.logger.error(f"记录交易平仓失败: {e}")
            return False

    def _process_context_data(self, context_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """处理和验证上下文数据"""
        processed = {}

        if context_data is None:
            # 填充空值（V2.0增强版）
            processed.update({
                'entry_signal_probability': None,
                'entry_neutral_probability': None,
                'entry_opposite_probability': None,
                'meta_model_inputs': None,
                'individual_model_probabilities': None,  # V2.0新增
                'base_model_predictions': None,  # V2.0新增
                'entry_market_regime': None,
                'entry_atr_percent': None,
                'entry_adx_value': None,
                'entry_top_features': None
            })
            return processed

        # 处理模型预测信息（支持新的V2.0键名）
        processed['entry_signal_probability'] = context_data.get('entry_signal_probability') or context_data.get('signal_probability')
        processed['entry_neutral_probability'] = context_data.get('entry_neutral_probability') or context_data.get('neutral_probability')
        processed['entry_opposite_probability'] = context_data.get('entry_opposite_probability') or context_data.get('opposite_probability')

        # 处理元模型输入（转换为JSON字符串）
        meta_inputs = context_data.get('meta_model_inputs')
        if meta_inputs is not None:
            try:
                processed['meta_model_inputs'] = json.dumps(meta_inputs, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                self.logger.warning(f"无法序列化meta_model_inputs: {e}")
                processed['meta_model_inputs'] = str(meta_inputs)
        else:
            processed['meta_model_inputs'] = None

        # 处理市场状态信息（支持新的V2.0键名）
        processed['entry_market_regime'] = context_data.get('entry_market_regime') or context_data.get('market_regime')
        processed['entry_atr_percent'] = context_data.get('entry_atr_percent') or context_data.get('atr_percent')
        processed['entry_adx_value'] = context_data.get('entry_adx_value') or context_data.get('adx_value')

        # 处理核心特征快照（转换为JSON字符串）（支持新的V2.0键名）
        top_features = context_data.get('entry_top_features') or context_data.get('top_features')
        if top_features is not None:
            try:
                # 如果已经是字符串，直接使用；否则序列化
                if isinstance(top_features, str):
                    processed['entry_top_features'] = top_features
                else:
                    processed['entry_top_features'] = json.dumps(top_features, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                self.logger.warning(f"无法序列化top_features: {e}")
                processed['entry_top_features'] = str(top_features)
        else:
            processed['entry_top_features'] = None

        # V2.0新增：处理个体模型概率信息
        individual_probas = context_data.get('individual_model_probabilities')
        if individual_probas is not None:
            try:
                if isinstance(individual_probas, str):
                    # 验证是否为有效JSON
                    json.loads(individual_probas)
                    processed['individual_model_probabilities'] = individual_probas
                else:
                    processed['individual_model_probabilities'] = json.dumps(individual_probas, ensure_ascii=False)
            except (TypeError, ValueError, json.JSONDecodeError) as e:
                self.logger.warning(f"无法处理individual_model_probabilities: {e}")
                processed['individual_model_probabilities'] = str(individual_probas)
        else:
            processed['individual_model_probabilities'] = None

        # V2.0新增：处理基础模型预测信息（用于元模型）
        base_predictions = context_data.get('base_model_predictions')
        if base_predictions is not None:
            try:
                if isinstance(base_predictions, str):
                    json.loads(base_predictions)
                    processed['base_model_predictions'] = base_predictions
                else:
                    processed['base_model_predictions'] = json.dumps(base_predictions, ensure_ascii=False)
            except (TypeError, ValueError, json.JSONDecodeError) as e:
                self.logger.warning(f"无法处理base_model_predictions: {e}")
                processed['base_model_predictions'] = str(base_predictions)
        else:
            processed['base_model_predictions'] = None

        return processed

    def _get_context_summary(self, processed_context: Dict[str, Any]) -> str:
        """生成上下文信息的简要摘要"""
        summary_parts = []

        # 添加信号概率信息
        signal_prob = processed_context.get('entry_signal_probability')
        if signal_prob is not None:
            summary_parts.append(f"信号概率: {signal_prob:.3f}")

        # 添加市场状态信息
        market_regime = processed_context.get('entry_market_regime')
        if market_regime:
            summary_parts.append(f"市场状态: {market_regime}")

        # 添加ATR信息
        atr_percent = processed_context.get('entry_atr_percent')
        if atr_percent is not None:
            summary_parts.append(f"ATR: {atr_percent:.2f}%")

        if summary_parts:
            return f", {', '.join(summary_parts)}"
        else:
            return ""

    def flush(self):
        """强制刷新队列中的所有数据"""
        self.async_writer.flush()
        self.logger.info("已请求刷新日志队列")

    def load_trade_logs(self, start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> pd.DataFrame:
        """
        加载指定日期范围的交易日志

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            合并后的DataFrame
        """
        return self.data_analyzer.load_trade_logs(start_date, end_date)

    def get_pending_trades_count(self) -> int:
        """获取待平仓交易数量"""
        with self.pending_lock:
            return len(self.pending_trades)

    def get_pending_trade_ids(self) -> List[str]:
        """获取所有待平仓交易ID列表"""
        with self.pending_lock:
            return list(self.pending_trades.keys())

    def clear_pending_trades(self):
        """清空所有待平仓交易（谨慎使用）"""
        with self.pending_lock:
            cleared_count = len(self.pending_trades)
            self.pending_trades.clear()
            self.logger.warning(f"已清空 {cleared_count} 个待平仓交易")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        writer_stats = self.async_writer.get_stats()

        return {
            "total_entries": self.total_entries,
            "total_exits": self.total_exits,
            "pending_trades": self.get_pending_trades_count(),
            "base_log_dir": self.base_log_dir,
            "writer_stats": writer_stats
        }

    def get_current_log_file(self) -> str:
        """获取当前日志文件路径"""
        file_path, _ = self.rotation_manager.get_current_log_file()
        return str(file_path)


# 全局实例管理
_enterprise_logger_instance = None
_enterprise_logger_lock = threading.Lock()


def get_enterprise_trade_logger(base_log_dir: str = "trading_logs",
                               max_file_size: int = 10 * 1024 * 1024,
                               auto_start: bool = True,
                               **kwargs) -> EnterpriseTradeLogger:
    """
    获取企业级交易日志记录器实例（单例模式）

    Args:
        base_log_dir: 日志根目录
        max_file_size: 最大文件大小
        auto_start: 是否自动启动
        **kwargs: 其他配置参数

    Returns:
        企业级交易日志记录器实例
    """
    global _enterprise_logger_instance

    with _enterprise_logger_lock:
        if _enterprise_logger_instance is None:
            _enterprise_logger_instance = EnterpriseTradeLogger(
                base_log_dir=base_log_dir,
                max_file_size=max_file_size,
                **kwargs
            )

            if auto_start:
                _enterprise_logger_instance.start()

        return _enterprise_logger_instance


def reset_enterprise_logger():
    """重置企业级日志记录器实例（主要用于测试）"""
    global _enterprise_logger_instance

    with _enterprise_logger_lock:
        if _enterprise_logger_instance:
            _enterprise_logger_instance.stop()
            _enterprise_logger_instance = None


# 向后兼容的工厂函数
def get_trade_logger(log_file_path: str = None) -> EnterpriseTradeLogger:
    """
    向后兼容的工厂函数

    Args:
        log_file_path: 日志文件路径（将被转换为目录）

    Returns:
        企业级交易日志记录器实例
    """
    if log_file_path:
        # 从文件路径提取目录
        base_dir = os.path.dirname(log_file_path) or "trading_logs"
    else:
        base_dir = "trading_logs"

    return get_enterprise_trade_logger(base_log_dir=base_dir)


if __name__ == "__main__":
    # 简单的测试示例
    import tempfile
    import shutil

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()

    try:
        # 初始化企业级日志记录器
        logger = get_enterprise_trade_logger(base_log_dir=temp_dir)

        # 记录一些测试交易
        context_data = {
            "signal_probability": 0.75,
            "market_regime": "uptrend",
            "atr_percent": 2.5
        }

        trade_id = logger.record_trade_entry(
            target_name="TestStrategy",
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=50000.0,
            amount=10.0,
            context_data=context_data
        )

        print(f"记录开仓: {trade_id}")

        # 等待一下让异步写入完成
        time.sleep(2)

        # 记录平仓
        logger.record_trade_exit(trade_id, 51000.0, "WIN")

        # 获取统计信息
        stats = logger.get_stats()
        print(f"统计信息: {stats}")

        # 强制刷新
        logger.flush()
        time.sleep(1)

        # 加载数据
        df = logger.load_trade_logs()
        print(f"加载的交易记录数: {len(df)}")

        # 停止日志记录器
        logger.stop()

    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)
