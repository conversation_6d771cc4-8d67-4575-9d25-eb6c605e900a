# SimMain.py
import tkinter as tk
import time
import threading
from datetime import datetime
import traceback
import logging
import os
import math
import argparse # 用于处理命令行参数
import config
# 你的其他导入
from binance.client import Client 
# from binance import ThreadedWebsocketManager # PriceFetcher 自己管理 TWM
# import config # SimMain 通常不直接依赖主预测程序的 config.py, 除非共享某些设置
             # 如果需要，可以创建一个 sim_config.py 或通过其他方式管理模拟盘配置
from src.simulation.SimAccount import Account
from src.simulation.SimTrading import TradingEngine
from src.simulation.PriceFetcher import PriceFetcher
from src.simulation.ImprovedPriceFetcher import ImprovedPriceFetcher, ConnectionState
from src.simulation.RiskManager import KellyRiskManager
from src.simulation.SignalReceiver import SignalReceiver
from src.simulation.SimGUI import SimGUI

# 配置日志记录器
logger = logging.getLogger(__name__)
if not logger.handlers:
    logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

# API密钥加载逻辑 - 可选，当前 PriceFetcher 使用匿名TWM
# 如果你的 PriceFetcher 或其他组件需要认证的 Binance Client，则需要配置这些
SIM_API_KEY = os.getenv("SIM_BINANCE_API_KEY_SIMMAIN") # 使用特定于SimMain的环境变量
SIM_API_SECRET = os.getenv("SIM_BINANCE_API_SECRET_SIMMAIN")

if SIM_API_KEY or SIM_API_SECRET:
    print(f"SimMain: API密钥已为模拟盘加载 (SIM_API_KEY: {'Set' if SIM_API_KEY else 'Not Set'}, SIM_API_SECRET: {'Set' if SIM_API_SECRET else 'Not Set'})")
else:
    print("SimMain: 模拟盘的 API密钥未配置，需要时将以匿名模式运行相关组件。")

# print(f"DEBUG: SimMain.py MODULE LEVEL EXECUTION at {time.time()}") # 可以移除此调试打印


class SimApplication:
    def __init__(self, root_tk_window, target_symbol='BTCUSDT', signal_receiver_port=5008):
        print(f"SimMain: Initializing SimApplication for {target_symbol} on signal port {signal_receiver_port}")
        self.root = root_tk_window
        self.is_running = True

        self.auto_trade_enabled = True  # ✅ V11.0: 启用自动交易，但只处理元模型信号
        self.auto_trade_fixed_amount = 5.0
        self.kelly_risk_enabled = True 
        self.last_price_update_time = 0
        self.last_trade_processing_time = 0
        self.last_signal_check_time = 0
        self.last_stats_update_time = 0
        self.last_daily_reset_check_time = time.time()
        self.current_day_for_reset = datetime.now().day

        # 模拟账户初始化，可以根据需要调整初始余额
        # 也可以考虑从持久化存储中加载账户状态
        self.account = Account(initial_balance=40.0) 

        self.current_trading_symbol = target_symbol.upper()
        print(f"SimMain: 此模拟盘实例配置为交易 {self.current_trading_symbol}")

        # 连接状态跟踪
        self.connection_state = ConnectionState.DISCONNECTED
        self.last_price_time = 0
        self.price_stale_threshold = 120  # 价格数据超过2分钟认为过期

        # 价格监控相关
        self.last_price_check_time = 0
        self.price_check_interval = 60  # 每60秒检查一次价格有效性
        self.consecutive_price_failures = 0
        self.max_consecutive_failures = 3

        # 使用改进的PriceFetcher，具有自动重连和健康检查功能
        self.price_fetcher = ImprovedPriceFetcher(
            symbol=self.current_trading_symbol,
            health_check_interval=30.0,  # 30秒检查一次
            price_stale_threshold=self.price_stale_threshold,  # 2分钟过期阈值
            price_callback=self._on_price_update,
            state_callback=self._on_connection_state_change
        )

        # 启动价格流
        if self.price_fetcher.start_stream():
            print(f"SimMain: ImprovedPriceFetcher 已为 {self.current_trading_symbol} 启动数据流。")
        else:
            print(f"SimMain: 警告 - ImprovedPriceFetcher 启动失败，将尝试自动重连。")

        self.trading_engine = TradingEngine(account_manager=self.account, price_fetcher_instance=self.price_fetcher)
        # RiskManager 参数可以从配置文件或默认值设置
        self.risk_manager = KellyRiskManager(
            min_bet= getattr(config, 'SIM_MIN_BET', 5.0) if 'config' in globals() else 5.0, # 示例：尝试从config获取
            max_bet= getattr(config, 'SIM_MAX_BET', 250.0) if 'config' in globals() else 250.0,
            payout_ratio= getattr(config, 'SIM_PAYOUT_RATIO', 0.8) if 'config' in globals() else 0.8,
            max_kelly_fraction= getattr(config, 'SIM_MAX_KELLY_FRACTION', 0.1) if 'config' in globals() else 0.1
        )
        
        self.signal_receiver = SignalReceiver(host='0.0.0.0', port=signal_receiver_port)
        self.signal_receiver.run_server()

        gui_callbacks = {
            'on_manual_trade': self.handle_manual_trade,
            'on_toggle_auto_trade': self.handle_toggle_auto_trade,
            'on_toggle_kelly': self.handle_toggle_kelly,
            'on_set_account_state': self.handle_set_account_state,
            'on_reconnect_price': self.force_price_reconnect,
            'on_exit': self.shutdown_application
        }
        initial_gui_states = {'auto_trade': self.auto_trade_enabled, 'kelly': self.kelly_risk_enabled}
        self.gui = SimGUI(self.root, gui_callbacks, initial_states=initial_gui_states)
        
        self.root.title(f"{self.current_trading_symbol} 模拟盘 (信号监听于端口: {signal_receiver_port})")
        
        self.update_loop()
        print(f"SimMain: 模拟盘应用程序 ({self.current_trading_symbol} @ Port {signal_receiver_port}) 已初始化。")

    def handle_manual_trade(self, direction, amount_int):
        if self.is_running:
           if not (self.risk_manager.min_bet <= float(amount_int) <= self.risk_manager.max_bet):
                msg = f"手动下单失败：金额 {amount_int} 超出范围 ({self.risk_manager.min_bet:.0f}-{self.risk_manager.max_bet:.0f})。"
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "error"); 
                if self.gui and self.gui.root and self.gui.root.winfo_exists(): # Ensure parent window exists for messagebox
                    tk.messagebox.showerror("金额错误", msg, parent=self.gui.root)
                return
           trade = self.trading_engine.open_trade(direction, float(amount_int))
           if trade:
                if hasattr(self.gui, 'update_status'): self.gui.update_status(f"手动开仓 {direction} {amount_int} USDT ({self.current_trading_symbol}) 成功。", "info")
                self.update_gui_active_trades(); self.update_gui_account_stats()
           else: 
                if hasattr(self.gui, 'update_status'): self.gui.update_status(f"手动开仓 ({self.current_trading_symbol}) 失败 (详见控制台日志)。", "error")
        else: 
            if hasattr(self.gui, 'update_status'): self.gui.update_status("应用程序已停止，无法交易。", "warning")

    def handle_toggle_auto_trade(self, is_enabled, fixed_amount_int_from_gui):
        self.auto_trade_enabled = is_enabled
        if self.risk_manager.min_bet <= float(fixed_amount_int_from_gui) <= self.risk_manager.max_bet:
            self.auto_trade_fixed_amount = float(fixed_amount_int_from_gui)
            print(f"SimMain [{self.current_trading_symbol}]: 自动交易状态: {'启用' if self.auto_trade_enabled else '禁用'}, 固定金额: {self.auto_trade_fixed_amount:.2f}")
            if hasattr(self.gui, 'update_status'): self.gui.update_status(f"自动交易 {'启用' if is_enabled else '禁用'}.", "info")
        else:
            if hasattr(self.gui, 'update_status'): self.gui.update_status(f"警告: 自动交易金额 {fixed_amount_int_from_gui} 无效。将使用上次有效值或默认值。", "warning")
            print(f"SimMain [{self.current_trading_symbol}]: 自动交易金额 {fixed_amount_int_from_gui} 无效。开关状态: {self.auto_trade_enabled}。")

    def handle_toggle_kelly(self, is_enabled):
        self.kelly_risk_enabled = is_enabled
        print(f"SimMain [{self.current_trading_symbol}]: 凯利公式状态: {'启用' if self.kelly_risk_enabled else '禁用'}")

        # 🎯 正确的实现：更新预测程序的动态配置
        try:
            self._update_prediction_kelly_config(is_enabled)

            if is_enabled:
                status_msg = "凯利公式已启用 - 预测程序将使用凯利公式计算交易金额"
                status_type = "success"
            else:
                status_msg = "凯利公式已禁用 - 预测程序将使用固定金额模式"
                status_type = "info"

        except Exception as e:
            print(f"SimMain [{self.current_trading_symbol}]: 更新预测程序凯利配置失败: {e}")
            status_msg = f"凯利公式状态更新失败: {e}"
            status_type = "error"

        if hasattr(self.gui, 'update_status'):
            self.gui.update_status(status_msg, status_type)

    def _update_prediction_kelly_config(self, kelly_enabled):
        """更新预测程序的凯利公式配置"""
        # 这里应该实现与预测程序配置的同步
        # 目前作为占位符，实际实现需要根据具体的配置管理机制
        print(f"SimMain [{self.current_trading_symbol}]: TODO - 需要实现与预测程序配置的同步")
        print(f"SimMain [{self.current_trading_symbol}]: 应该更新 META_MODEL_STATIC_KELLY_CONFIG 的 trade_amount_strategy")
        print(f"SimMain [{self.current_trading_symbol}]: 目标策略: {'kelly_config' if kelly_enabled else 'fixed'}")

        # 实际实现可能需要：
        # 1. 修改动态配置文件 dynamic_params.json
        # 2. 或者通过API调用通知预测程序
        # 3. 或者通过共享配置文件进行通信

    def handle_set_account_state(self, params_dict):
        print(f"SimMain [{self.current_trading_symbol}]: 请求重置账户状态: {params_dict}")
        try:
            self.trading_engine.active_trades.clear()
            if hasattr(self.trading_engine, 'trade_history'):
                 self.trading_engine.trade_history.clear()
            self.account = Account(
                initial_balance=float(params_dict["balance"]),
                initial_total_profit_loss=float(params_dict.get("profit_loss", 0.0)),
                initial_total_trades_count=int(params_dict.get("total_trades", 0)),
                initial_total_wins_count=int(params_dict.get("total_wins", 0))
            )
            self.trading_engine.account = self.account 
            self.update_gui_account_stats()
            self.update_gui_active_trades()
            self.update_gui_trade_history()
            if hasattr(self.gui, 'update_status'): self.gui.update_status(f"账户已根据输入重置。", "info")
        except Exception as e:
            logger.error(f"[{self.current_trading_symbol}] 设置账户状态失败: {e}")
            logger.exception("设置账户状态失败的详细堆栈跟踪:")
            if hasattr(self.gui, 'update_status'): self.gui.update_status(f"设置账户状态失败: {e}", "error")
            print(f"SimMain [{self.current_trading_symbol}]: 设置账户状态失败 - {e}")

    def shutdown_application(self):
        print(f"SimMain [{self.current_trading_symbol}]: 收到退出信号，正在关闭应用程序...")
        self.is_running = False
        if hasattr(self, 'price_fetcher') and self.price_fetcher.is_running:
            print(f"SimMain [{self.current_trading_symbol}]: 正在停止 PriceFetcher WebSocket 流...")
            self.price_fetcher.stop_stream()
            if hasattr(self.price_fetcher, 'twm') and self.price_fetcher.twm.is_alive():
                print(f"SimMain [{self.current_trading_symbol}]: 正在停止 PriceFetcher 的 TWM...")
                self.price_fetcher.twm.stop()
                self.price_fetcher.twm.join(timeout=3)
        if hasattr(self, 'signal_receiver') and hasattr(self.signal_receiver, 'server_thread') and self.signal_receiver.server_thread.is_alive():
             print(f"SimMain [{self.current_trading_symbol}]: SignalReceiver 服务器线程仍在运行 (daemon，将随主程序退出)。")
        if self.root and self.root.winfo_exists():
            try:
                self.root.destroy()
                print(f"SimMain [{self.current_trading_symbol}]: Tkinter root已销毁。")
            except tk.TclError as e_tcl_destroy:
                 if "application has been destroyed" not in str(e_tcl_destroy).lower():
                     print(f"! 关闭 Tkinter root 时发生 TclError ({self.current_trading_symbol}): {e_tcl_destroy}")
            except Exception as e_destroy_root:
                print(f"! 关闭 Tkinter root 时发生未知错误 ({self.current_trading_symbol}): {e_destroy_root}")

    def update_loop(self):
        if not self.is_running: return
        current_time = time.time()
        try:
            if current_time - self.last_price_update_time >= 1:
                self._update_market_price(); self.last_price_update_time = current_time
            if current_time - self.last_trade_processing_time >= 1:
                self._process_trades(); self.last_trade_processing_time = current_time
            if current_time - self.last_signal_check_time >= 0.5: 
                self._check_and_process_external_signals(); self.last_signal_check_time = current_time
            if current_time - self.last_stats_update_time >= 1:
                self.update_gui_account_stats(); self.update_gui_active_trades(); self.update_gui_trade_history()
                self._check_price_data_health()  # 检查价格数据健康状态
                self.last_stats_update_time = current_time
            if current_time - self.last_price_check_time >= self.price_check_interval:
                self._monitor_price_validity()  # 主动监控价格有效性
                self.last_price_check_time = current_time
            if current_time - self.last_daily_reset_check_time >= 60: 
                self._check_daily_reset(); self.last_daily_reset_check_time = current_time
        except Exception as e_update_loop:
            logger.error(f"[{self.current_trading_symbol}] update_loop发生错误: {e_update_loop}")
            logger.exception("update_loop错误的详细堆栈跟踪:")
            print(f"!!! SimMain [{self.current_trading_symbol}] update_loop 发生错误: {e_update_loop}")
        if self.is_running:
            self.root.after(200, self.update_loop)

    def _get_price_with_fallback(self):
        """
        获取价格，带REST API备用机制
        优先使用WebSocket，失败时自动回退到REST API
        """
        # 首先尝试从WebSocket获取价格
        ws_price = self.price_fetcher.get_current_price()
        is_price_fresh = self.price_fetcher.is_price_fresh(max_age_seconds=self.price_stale_threshold)

        if ws_price is not None and is_price_fresh:
            return ws_price

        # WebSocket价格不可用或过期，使用REST API备用
        print(f"SimMain [{self.current_trading_symbol}]: WebSocket价格不可用，使用REST API备用")

        try:
            import requests
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={self.current_trading_symbol}"
            headers = {'User-Agent': 'Mozilla/5.0'}

            for attempt in range(2):
                try:
                    response = requests.get(url, timeout=3.0, headers=headers)
                    response.raise_for_status()
                    data = response.json()

                    if data and 'price' in data:
                        rest_price = float(data['price'])
                        print(f"SimMain [{self.current_trading_symbol}]: REST API获取价格成功: {rest_price}")
                        return rest_price

                except Exception as e:
                    print(f"SimMain [{self.current_trading_symbol}]: REST API尝试 {attempt + 1} 失败: {e}")
                    if attempt < 1:
                        time.sleep(0.5)

        except Exception as e:
            print(f"SimMain [{self.current_trading_symbol}]: REST API备用机制失败: {e}")

        # 如果REST API也失败，返回WebSocket价格（即使可能过期）
        print(f"SimMain [{self.current_trading_symbol}]: 所有价格获取方式失败，返回WebSocket缓存价格")
        return ws_price

    def _update_market_price(self):
        price = self._get_price_with_fallback()
        current_time = time.time()

        # 重新检查价格数据新鲜度（因为可能使用了REST API）
        is_price_fresh = True  # REST API获取的价格总是新鲜的
        if price == self.price_fetcher.get_current_price():
            # 如果价格来自WebSocket，检查新鲜度
            is_price_fresh = self.price_fetcher.is_price_fresh(max_age_seconds=self.price_stale_threshold)

        if price is not None and is_price_fresh:
            try:
                # 假设 SimGUI 中的价格变量是 self.gui.account_info_vars["btc_price"]
                # 并且 SimGUI.update_btc_price 会处理好币种前缀
                if hasattr(self.gui, 'account_info_vars') and "btc_price" in self.gui.account_info_vars:
                    current_price_str_in_gui = self.gui.account_info_vars.get("btc_price").get()
                    cleaned_price_str = current_price_str_in_gui.replace("$", "").split(" ")[0]
                    old_price = float(cleaned_price_str) if cleaned_price_str and cleaned_price_str != "----.--" else price
                else:
                    old_price = price
            except (AttributeError, ValueError, tk.TclError):
                old_price = price

            color = self.gui.neutral_color if hasattr(self.gui, 'neutral_color') else "#FFFFFF"
            if hasattr(self.gui, 'up_color') and price > old_price:
                color = self.gui.up_color
            elif hasattr(self.gui, 'down_color') and price < old_price:
                color = self.gui.down_color

            symbol_prefix_for_display = self.current_trading_symbol[:-4] if self.current_trading_symbol.endswith("USDT") else self.current_trading_symbol

            # 根据连接状态添加状态指示
            status_indicator = self._get_connection_status_indicator()
            price_display = f"{symbol_prefix_for_display} ${price:.2f} {status_indicator}"

            # 调试信息：显示当前连接状态
            if hasattr(self, '_last_debug_state') and self._last_debug_state != self.connection_state:
                print(f"SimMain: GUI更新 - 连接状态: {self.connection_state.value}, 指示器: {status_indicator}")
                self._last_debug_state = self.connection_state

            if hasattr(self.gui, 'update_btc_price'):
                self.gui.update_btc_price(price_display, color)
        else:
            # 价格数据过期或无效
            symbol_prefix_for_display = self.current_trading_symbol[:-4] if self.current_trading_symbol.endswith("USDT") else self.current_trading_symbol

            if price is None:
                status_msg = "(等待连接)"
            else:
                age_seconds = current_time - self.price_fetcher.get_last_update_time()
                status_msg = f"(数据过期 {age_seconds:.0f}s)"

            status_indicator = self._get_connection_status_indicator()
            price_display = f"{symbol_prefix_for_display} $----.-- {status_msg} {status_indicator}"

            if hasattr(self.gui, 'update_btc_price'):
                self.gui.update_btc_price(price_display, (self.gui.danger_color if hasattr(self.gui, 'danger_color') else "#FF0000"))

    def _on_price_update(self, price: float):
        """价格更新回调函数"""
        self.last_price_time = time.time()
        # 这里可以添加额外的价格更新逻辑

    def _on_connection_state_change(self, state: ConnectionState):
        """连接状态变化回调函数 - 增强版本"""
        self.connection_state = state
        print(f"SimMain: 连接状态变化为 {state.value}")

        # 根据连接状态采取相应行动
        if state == ConnectionState.FAILED:
            print(f"SimMain [{self.current_trading_symbol}]: WebSocket连接失败，将依赖REST API备用机制")
        elif state == ConnectionState.CONNECTED:
            print(f"SimMain [{self.current_trading_symbol}]: WebSocket连接已恢复")
        elif state == ConnectionState.RECONNECTING:
            print(f"SimMain [{self.current_trading_symbol}]: 正在重连WebSocket...")
        elif state == ConnectionState.DEGRADED:
            print(f"SimMain [{self.current_trading_symbol}]: 连接降级，使用REST API模式")
        if state == ConnectionState.FAILED:
            print("SimMain: 连接完全失败，请检查网络连接")
        elif state == ConnectionState.DEGRADED:
            print("SimMain: 已切换到降级模式（REST API轮询）")
        elif state == ConnectionState.CONNECTED:
            print("SimMain: WebSocket连接已恢复")

    def _get_connection_status_indicator(self) -> str:
        """获取连接状态指示器"""
        # 使用字符串值比较，避免枚举对象比较问题
        state_value = self.connection_state.value if hasattr(self.connection_state, 'value') else str(self.connection_state)

        if state_value == "connected":
            return "🟢"  # 绿色圆点表示正常连接
        elif state_value == "connecting":
            return "🔵"  # 蓝色圆点表示正在连接中
        elif state_value == "degraded":
            return "🟡"  # 黄色圆点表示降级模式
        elif state_value == "reconnecting":
            return "🔄"  # 循环箭头表示重连中
        elif state_value == "failed":
            return "🔴"  # 红色圆点表示连接失败
        elif state_value == "disconnected":
            return "⚪"  # 白色圆点表示已断开连接
        else:
            # 如果到了这里，说明状态匹配有问题
            print(f"DEBUG: 未匹配的状态值: {state_value}")
            return "❓"  # 问号表示未知状态

    def _check_price_data_health(self):
        """检查价格数据健康状态"""
        current_time = time.time()

        # 检查价格数据是否过期
        if not self.price_fetcher.is_price_fresh(max_age_seconds=self.price_stale_threshold):
            last_update_time = self.price_fetcher.get_last_update_time()
            if last_update_time > 0:
                age_seconds = current_time - last_update_time
                if age_seconds > self.price_stale_threshold * 2:  # 超过4分钟自动重连
                    print(f"SimMain: 价格数据过期超过 {age_seconds:.0f} 秒，自动重连...")
                    self.force_price_reconnect()

    def force_price_reconnect(self):
        """强制重连价格数据流"""
        print("SimMain: 强制重连价格数据流...")
        try:
            self.price_fetcher.force_reconnect()
            print("SimMain: 重连请求已发送")
        except Exception as e:
            print(f"SimMain: 重连失败 - {e}")

    def _monitor_price_validity(self):
        """主动监控价格有效性"""
        current_time = time.time()

        try:
            # 尝试获取价格（包含备用机制）
            price = self._get_price_with_fallback()

            if price is not None:
                # 价格获取成功，重置失败计数
                if self.consecutive_price_failures > 0:
                    print(f"SimMain [{self.current_trading_symbol}]: 价格监控恢复正常")
                    self.consecutive_price_failures = 0
            else:
                # 价格获取失败
                self.consecutive_price_failures += 1
                print(f"SimMain [{self.current_trading_symbol}]: 价格获取失败 (连续 {self.consecutive_price_failures} 次)")

                # 如果连续失败次数过多，强制重连
                if self.consecutive_price_failures >= self.max_consecutive_failures:
                    print(f"SimMain [{self.current_trading_symbol}]: 连续 {self.consecutive_price_failures} 次价格获取失败，强制重连")
                    self.force_price_reconnect()
                    self.consecutive_price_failures = 0  # 重置计数

            # 检查WebSocket连接状态
            if self.connection_state == ConnectionState.FAILED:
                print(f"SimMain [{self.current_trading_symbol}]: 检测到连接失败状态，尝试恢复")
                self.force_price_reconnect()

        except Exception as e:
            print(f"SimMain [{self.current_trading_symbol}]: 价格监控异常: {e}")
            self.consecutive_price_failures += 1

    def _process_trades(self):
        self.trading_engine.process_expired_trades()

    def _check_and_process_external_signals(self):
        if not self.auto_trade_enabled:
            return

        signal_data = self.signal_receiver.get_latest_signal(clear_after_get=True)
        
        if signal_data:
            signal_type = signal_data.get('type')
            target_name_from_signal = signal_data.get('target_name', '未知目标')
            amount_from_prediction_system = signal_data.get('amount')
            signal_symbol_from_payload = signal_data.get('symbol')

            # 🚀 V12.0: 获取信号快照上下文数据
            signal_context_data = signal_data.get('context_data', {})

            # 🛡️ V11.0: 信号来源验证 - 处理元模型信号和iPhone测试信号
            is_meta_model_signal = (
                target_name_from_signal and
                ('MetaModel' in target_name_from_signal or 'meta' in target_name_from_signal.lower())
            )

            # 📱 iPhone自动化测试信号
            is_iphone_test_signal = (
                target_name_from_signal and
                any(keyword in target_name_from_signal for keyword in ['TEST', 'QUICK', 'FINAL', 'CONTINUOUS', 'ZXTOUCH'])
            )

            if not is_meta_model_signal and not is_iphone_test_signal:
                msg = f"🚫 SimMain [{self.current_trading_symbol}]: 拒绝信号 (来源: {target_name_from_signal}) - 只处理元模型或iPhone测试信号"
                print(msg)
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "warning")
                return

            print(f"✅ SimMain [{self.current_trading_symbol}]: 收到元模型信号: 类型={signal_type}, "
                  f"来源目标='{target_name_from_signal}', "
                  f"信号币种='{signal_symbol_from_payload}', "
                  f"建议金额={amount_from_prediction_system}")

            if signal_symbol_from_payload is None:
                msg = f"SimMain [{self.current_trading_symbol}]: 忽略信号 (来源: {target_name_from_signal}) - 原因: 信号未提供币种信息。"
                print(msg)
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "warning")
                return

            if signal_symbol_from_payload.upper() != self.current_trading_symbol.upper():
                msg = (f"SimMain [{self.current_trading_symbol}]: 忽略信号 (来源: {target_name_from_signal}) - "
                       f"原因: 信号币种 '{signal_symbol_from_payload.upper()}' "
                       f"与本模拟盘配置的币种 '{self.current_trading_symbol.upper()}' 不匹配。")
                print(msg)
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "info")
                return 

            print(f"SimMain [{self.current_trading_symbol}]: 处理针对 '{self.current_trading_symbol}' 的信号 (来源: {target_name_from_signal})")

            if amount_from_prediction_system is None:
                msg = f"SimMain [{self.current_trading_symbol}]: 忽略信号 (来源: {target_name_from_signal}) - 原因: 未提供交易金额。"
                print(msg)
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "warning")
                return

            try:
                bet_amount_float_from_pred = float(amount_from_prediction_system)
            except ValueError:
                msg = f"SimMain [{self.current_trading_symbol}]: 忽略信号 (来源: {target_name_from_signal}) - 原因: 金额 '{amount_from_prediction_system}' 无效。"
                print(msg)
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "error")
                return

            current_sim_balance = self.account.get_stats().get('current_balance', 0.0)
            sim_min_bet_float = self.risk_manager.min_bet
            sim_max_bet_float = self.risk_manager.max_bet

            # ✅ 正确架构：使用预测程序计算的金额，只应用安全限制
            print(f"SimMain [{self.current_trading_symbol}]: 使用预测程序计算的交易金额: {bet_amount_float_from_pred:.2f} USDT")
            print(f"SimMain [{self.current_trading_symbol}]: 凯利公式状态: {'启用' if self.kelly_risk_enabled else '禁用'} (由预测程序处理)")

            # 应用最小/最大限制和余额限制
            final_bet_amount_for_sim_float = max(sim_min_bet_float, min(bet_amount_float_from_pred, sim_max_bet_float))
            final_bet_amount_for_sim_float = min(final_bet_amount_for_sim_float, current_sim_balance)
            final_bet_amount_for_sim_int = int(round(final_bet_amount_for_sim_float))
            
            sim_min_bet_int_rounded = int(round(sim_min_bet_float))

            if final_bet_amount_for_sim_int >= sim_min_bet_int_rounded and \
               final_bet_amount_for_sim_int <= current_sim_balance and \
               final_bet_amount_for_sim_int > 0:

                # 🔒 关键修复：开仓前检查交易状态管理器
                try:
                    from src.core.trade_state_manager import enhanced_trade_state_manager as trade_state_manager

                    if not trade_state_manager.can_start_new_trade():
                        msg = f"🔒 SimMain [{self.current_trading_symbol}]: 拒绝开仓 - 交易状态管理器阻止 (当前状态: {trade_state_manager._current_state.value})"
                        print(msg)
                        if hasattr(self.gui, 'update_status'):
                            self.gui.update_status(msg, "warning")
                        return

                    print(f"✅ SimMain [{self.current_trading_symbol}]: 交易状态检查通过，可以开仓")

                except Exception as e:
                    print(f"⚠️ SimMain [{self.current_trading_symbol}]: 交易状态检查异常: {e}")
                    # 为了安全起见，如果状态检查失败，拒绝开仓
                    msg = f"🔒 SimMain [{self.current_trading_symbol}]: 拒绝开仓 - 交易状态检查异常"
                    print(msg)
                    if hasattr(self.gui, 'update_status'):
                        self.gui.update_status(msg, "error")
                    return

                print(f"SimMain [{self.current_trading_symbol}]: 准备开仓 - 方向: {signal_type}, "
                      f"金额: {final_bet_amount_for_sim_int} (来自信号源: {target_name_from_signal})")

                # 📱 iPhone自动化交易调用 - 在模拟盘开仓前执行（并行独立检查）
                iphone_success = False
                if is_iphone_test_signal or is_meta_model_signal:
                    print(f"📱 SimMain [{self.current_trading_symbol}]: 并行执行iPhone自动化交易...")
                    iphone_success = self._execute_iphone_trade(signal_type, amount_from_prediction_system, target_name_from_signal)

                # 🚀 V12.0: 传递信号快照上下文数据
                trade = self.trading_engine.open_trade(
                    signal_type,
                    float(final_bet_amount_for_sim_int),
                    target_name_from_signal,
                    self.current_trading_symbol,
                    signal_context_data
                )

                # 🔧 修复：通知交易状态管理器开仓成功
                if trade:
                    try:
                        from src.core.trade_state_manager import enhanced_trade_state_manager as trade_state_manager

                        # 先开始交易（转到opening状态）
                        trade_started = trade_state_manager.start_trade({
                            'signal_type': signal_type,
                            'amount': float(final_bet_amount_for_sim_int),
                            'symbol': self.current_trading_symbol,
                            'source': 'simulator',
                            'target_name': target_name_from_signal
                        })

                        if trade_started:
                            # 然后确认开仓（转到active状态）
                            trade_state_manager.confirm_trade_opened({
                                'trade_id': trade.trade_id,
                                'direction': signal_type,
                                'amount': float(final_bet_amount_for_sim_int),
                                'entry_price': trade.entry_price,
                                'expiry_time': trade.expiry_time.isoformat()
                            })
                            print(f"🔒 [交易状态管理] 模拟盘交易 {trade.trade_id} 开仓流程完成")
                        else:
                            print(f"🔒 [交易状态管理] 交易状态管理器拒绝开仓")
                    except Exception as e:
                        print(f"⚠️ [交易状态管理] 通知开仓失败: {e}")
                if trade:
                    msg = f"SimMain [{self.current_trading_symbol}]: 自动开仓 {signal_type} {final_bet_amount_for_sim_int} 成功 (来源: {target_name_from_signal})."
                    print(msg)
                    if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "success")
                    self.update_gui_active_trades(); self.update_gui_account_stats()

                    # 📱 显示iPhone自动化结果
                    if is_iphone_test_signal or is_meta_model_signal:
                        if iphone_success:
                            iphone_msg = f"📱 iPhone自动化交易执行成功: {signal_type} {amount_from_prediction_system} USDT"
                            print(iphone_msg)
                            if hasattr(self.gui, 'update_status'):
                                self.gui.update_status(iphone_msg, "success")
                        else:
                            iphone_msg = f"📱 iPhone自动化交易执行失败: {signal_type} {amount_from_prediction_system} USDT"
                            print(iphone_msg)
                            if hasattr(self.gui, 'update_status'):
                                self.gui.update_status(iphone_msg, "error")
                else:
                    msg = f"SimMain [{self.current_trading_symbol}]: 自动开仓失败 (来源: {target_name_from_signal}) (详见控制台日志)."
                    print(msg)
                    if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "error")
            else:
                reason_sim = ""
                if final_bet_amount_for_sim_int <= 0 : reason_sim = "计算金额为0或负 "
                elif final_bet_amount_for_sim_int < sim_min_bet_int_rounded: reason_sim += f"低于模拟盘最小整数({sim_min_bet_int_rounded}) "
                if final_bet_amount_for_sim_int > current_sim_balance : reason_sim += f"超模拟余额({current_sim_balance:.0f})"
                msg = f"SimMain [{self.current_trading_symbol}]: 无法执行信号 (来源: {target_name_from_signal}) - 金额({final_bet_amount_for_sim_int})无效({reason_sim.strip()})."
                print(msg)
                if hasattr(self.gui, 'update_status'): self.gui.update_status(msg, "warning")

    def _check_daily_reset(self):
        now = datetime.now()
        if now.day != self.current_day_for_reset:
            print(f"SimMain [{self.current_trading_symbol}]: 检测到新的一天 ({now.strftime('%Y-%m-%d')}), 重置当日统计。")
            self.account.reset_daily_stats()
            self.current_day_for_reset = now.day
            self.update_gui_account_stats()
            if hasattr(self.gui, 'update_status'): self.gui.update_status("新的一天，当日统计已重置。", "info")

    def update_gui_account_stats(self):
        if self.gui and self.account and hasattr(self.gui, 'update_account_info'):
            self.gui.update_account_info(self.account.get_stats())

    def update_gui_active_trades(self):
        if self.gui and self.trading_engine and hasattr(self.gui, 'update_active_trades'):
            self.gui.update_active_trades(self.trading_engine.get_active_trades_info())

    def update_gui_trade_history(self):
        if self.gui and self.trading_engine and hasattr(self.gui, 'update_trade_history'):
            self.gui.update_trade_history(self.trading_engine.get_trade_history_info(limit=50))

    def _execute_iphone_trade(self, signal_type, amount, target_name):
        """执行iPhone自动化交易，返回执行结果"""
        try:
            # 检查iPhone交易信号开关
            from config import IPHONE_TRADING_SIGNAL_ENABLED
            if not IPHONE_TRADING_SIGNAL_ENABLED:
                print(f"📱 SimMain [{self.current_trading_symbol}]: iPhone交易信号已禁用 (IPHONE_TRADING_SIGNAL_ENABLED=False)")
                print(f"   信号类型: {signal_type}")
                print(f"   交易金额: {amount} USDT")
                print(f"   信号来源: {target_name}")
                print(f"🔒 跳过iPhone自动化交易执行")
                return False

            print(f"📱 SimMain [{self.current_trading_symbol}]: 开始执行iPhone自动化交易...")
            print(f"   信号类型: {signal_type}")
            print(f"   交易金额: {amount} USDT")
            print(f"   信号来源: {target_name}")

            # 导入iPhone自动化模块
            import sys
            import os

            # 获取项目根目录
            project_root = os.path.dirname(os.path.abspath(__file__))
            iphone_automation_path = os.path.join(project_root, 'iphone_automation')

            if iphone_automation_path not in sys.path:
                sys.path.insert(0, iphone_automation_path)

            # 导入iPhone自动化执行函数
            try:
                from iphone_automation.ssh_zxtouch_trader import execute_binance_trade
                print(f"✅ 成功导入iPhone自动化模块")
            except ImportError as import_error:
                print(f"❌ 导入iPhone自动化模块失败: {import_error}")
                print(f"📁 搜索路径: {iphone_automation_path}")
                print(f"📁 当前sys.path: {sys.path}")
                return False

            # 执行iPhone自动化交易
            success = execute_binance_trade(signal_type, amount)
            return success

        except ImportError as e:
            msg = f"📱 iPhone自动化模块导入失败: {e}"
            print(msg)
            return False
        except Exception as e:
            msg = f"📱 iPhone自动化交易执行异常: {e}"
            print(msg)
            return False


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="模拟盘交易程序")
    parser.add_argument("--symbol", type=str, default="BTCUSDT", 
                        help="要模拟交易的币种 (例如 ETHUSDT, BTCUSDT)")
    parser.add_argument("--port", type=int, default=5008, 
                        help="SignalReceiver 监听的端口号")
    args = parser.parse_args()

    # 移除模块级的 DEBUG 打印（如果之前有的话）
    # print(f"DEBUG: SimMain.py __main__ block, Parsed Args: symbol={args.symbol}, port={args.port}")

    app_instance = None
    main_tk_root_instance = None # 通常Tkinter的root在if __name__内创建
    try:
        main_tk_root_instance = tk.Tk()
        app_instance = SimApplication(main_tk_root_instance, 
                                      target_symbol=args.symbol, 
                                      signal_receiver_port=args.port)
        main_tk_root_instance.mainloop()
    except SystemExit:
        # 使用 args.symbol (如果 app_instance 还未完全初始化或已销毁)
        symbol_for_log = args.symbol if 'args' in locals() else (app_instance.current_trading_symbol if app_instance else 'App')
        print(f"SimMain [{symbol_for_log}]: Tkinter mainloop 已通过 SystemExit 正常退出。")
    except tk.TclError as e_tcl:
        symbol_for_log = args.symbol if 'args' in locals() else (app_instance.current_trading_symbol if app_instance else 'App')
        if "application has been destroyed" in str(e_tcl).lower():
            print(f"SimMain [{symbol_for_log}]: Tkinter 应用已被销毁。")
        else:
            logger.error(f"[{symbol_for_log}] 主程序发生TclError: {e_tcl}")
            logger.exception("主程序TclError的详细堆栈跟踪:")
            print(f"SimMain [{symbol_for_log}] 主程序发生 TclError: {e_tcl}")
    except Exception as e:
        symbol_for_log = args.symbol if 'args' in locals() else (app_instance.current_trading_symbol if app_instance else 'App')
        logger.critical(f"[{symbol_for_log}] 主程序发生严重错误: {e}")
        logger.exception("主程序严重错误的详细堆栈跟踪:")
        print(f"SimMain [{symbol_for_log}] 主程序发生严重错误: {e}")
    finally:
        symbol_for_log_final = args.symbol if 'args' in locals() else ('UnknownSim' if not app_instance else app_instance.current_trading_symbol)
        if app_instance and app_instance.is_running: # 检查 is_running 状态
            print(f"SimMain [{app_instance.current_trading_symbol}]: 主循环意外终止或正在关闭，尝试执行清理。")
            app_instance.shutdown_application() 
        elif app_instance and not app_instance.is_running: # 如果是正常关闭流程
            print(f"SimMain [{app_instance.current_trading_symbol}]: 已正常标记为非运行状态。")
        else: # app_instance 未成功创建或已被清理
            print(f"SimMain [{symbol_for_log_final}]: 实例未创建或已清理。")
        print(f"SimMain: 模拟盘应用程序 ({symbol_for_log_final}) 已关闭或尝试关闭。")
