# 策略二：样本权重优化 - 从"一刀切"到"精准调控"

## 📋 优化概述

本次优化实施了策略二的核心理念：通过精细化样本权重和降低类别权重，实现从粗暴权重惩罚到高质量数据驱动的转变。

## 🎯 优化内容

### 1. 细化市场状态权重 (v9_weight_map)

**优化位置**: `src/core/enhanced_sample_weighting.py` 第260-280行

**优化前**:
```python
state_weight_map = {
    'strong_trend_up': 3.0,
    'strong_trend_down': 3.0,
    'low_vol_sideways': 1.5,
    'high_vol_sideways': 0.1,
    # ...
}
```

**优化后**:
```python
state_weight_map = {
    # === 高价值状态（权重提升）===
    'strong_trend_up': 3.5,           # 强趋势：提升权重
    'strong_trend_down': 3.5,         # 强趋势：提升权重
    'low_vol_sideways': 2.5,          # 🚀 低波动盘整：大幅提升权重（突破前兆）
    
    # === 低价值状态（权重降低）===
    'high_vol_sideways': 0.05,       # 🔻 高波动盘整：大幅降低权重（噪音状态）
    'extreme_volatility': 0.02,      # 🔻 极端波动：最低权重（趋势末期）
    'panic_selling': 0.03,           # 🔻 恐慌性抛售：极低权重（情绪化噪音）
    'bubble_state': 0.03,            # 🔻 泡沫状态：极低权重（非理性状态）
}
```

**优化理由**:
- **低波动盘整后突破**：从1.5提升到2.5，因为这通常是新趋势的开始
- **趋势末期的高波动**：从0.1降低到0.02-0.05，因为这往往是趋势衰竭和反转的前兆

### 2. 引入成交量乘数因子

**优化位置**: `src/core/enhanced_sample_weighting.py` 第285-317行

**新增功能**:
```python
# 🚀 策略二增强：引入成交量作为乘数因子
volume_multipliers = np.ones(len(market_weights))

for i in range(min_len):
    vol_ratio = volume_vs_avg.iloc[i]
    if vol_ratio > 2.0:      # 放量突破
        volume_multipliers[i] = 1.5  # 1.5倍权重
    elif vol_ratio > 1.5:    # 适度放量
        volume_multipliers[i] = 1.2  # 1.2倍权重
    elif vol_ratio < 0.5:    # 缩量
        volume_multipliers[i] = 0.7  # 降低权重

# 应用成交量乘数
market_weights = market_weights * volume_multipliers
```

**优化理由**:
- **放量突破**：获得更高权重，因为成交量确认了价格突破的有效性
- **缩量突破**：降低权重，因为缺乏成交量支撑的突破往往不可持续

### 3. 大幅降低类别权重 (class_weight)

**优化位置**: `config.py`

| 模型 | 优化前 | 优化后 | 降幅 |
|------|--------|--------|------|
| UP模型 | {0: 1.0, 1: 30.0} | {0: 1.0, 1: 5.0} | 83% ↓ |
| DOWN模型 | {0: 1.0, 1: 100.0} | {0: 1.0, 1: 3.0} | 97% ↓ |
| 基础模板 | {0: 1.0, 1: 15.0} | {0: 1.0, 1: 4.0} | 73% ↓ |

**优化理由**:
- 在策略一优化目标变量后，正类样本数量显著增加，类别不平衡问题得到缓解
- 极端的class_weight会导致模型过度关注召回率而忽视精确率
- 温和的权重让模型依靠高质量的数据本身去学习，而不是靠粗暴的权重惩罚

## 📊 验证结果

### 测试脚本验证
运行 `scripts/test_sample_weight_optimization.py` 的结果：

```
🎯 测试策略二：优化市场状态权重
✅ 权重计算成功
📊 权重统计:
   - 样本数量: 500
   - 权重范围: [0.6669, 1.5203]
   - 权重均值: 1.0000
   - 权重标准差: 0.1270
   - 高权重样本(>90%分位): 50 (10.0%)
   - 低权重样本(<10%分位): 50 (10.0%)

🎯 测试策略二：class_weight优化
✅ UP模型 (BTC_15m_UP) class_weight: {0: 1.0, 1: 5.0}
   ✅ UP模型权重已优化 (≤10.0)
✅ DOWN模型 (BTC_15m_DOWN) class_weight: {0: 1.0, 1: 3.0}
   ✅ DOWN模型权重已优化 (≤10.0)
```

### 权重分布分析
- **权重范围合理**: [0.67, 1.52]，避免了极端权重
- **权重分布均衡**: 标准差0.127，说明权重变化适中
- **高低权重样本比例**: 各占10%，符合预期的精准调控

## 🎯 预期效果

### 1. 提升模型质量
- **减少过拟合**: 降低对极端权重的依赖
- **提升泛化能力**: 依靠数据质量而非权重技巧
- **平衡精确率和召回率**: 避免过度追求召回率

### 2. 增强信号质量
- **突破信号更可靠**: 低波动盘整后的突破获得更高权重
- **噪音过滤更有效**: 高波动混乱状态权重大幅降低
- **成交量确认**: 放量突破获得额外权重加成

### 3. 系统稳定性
- **减少极端预测**: 温和的class_weight避免模型过于激进
- **提升一致性**: 精细化的权重策略提供更稳定的训练环境
- **便于调优**: 合理的权重范围便于后续微调

## 🔄 后续建议

### 1. 监控指标
- **样本权重分布**: 定期检查权重分布是否合理
- **模型性能**: 观察精确率和召回率的平衡
- **预测一致性**: 监控预测结果的稳定性

### 2. 进一步优化
- **动态权重调整**: 根据市场条件动态调整权重策略
- **多时间框架权重**: 考虑不同时间框架的权重差异
- **特征重要性权重**: 结合特征重要性进行权重调整

### 3. A/B测试
- **对比测试**: 与优化前的模型进行对比测试
- **实盘验证**: 在模拟盘环境中验证优化效果
- **持续改进**: 根据实际表现持续优化权重策略

## 📝 总结

策略二的样本权重优化成功实现了从"一刀切"到"精准调控"的转变：

1. ✅ **细化市场状态权重** - 提升低波动盘整权重，降低高波动噪音权重
2. ✅ **引入成交量乘数因子** - 放量突破获得更高权重
3. ✅ **大幅降低class_weight** - 从极端值降至温和水平
4. ✅ **实现精准调控** - 依靠高质量数据而非粗暴权重

这些优化将显著提升模型的训练质量和预测稳定性，为后续的策略实施奠定了坚实基础。
