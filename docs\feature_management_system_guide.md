# 特征管理系统使用指南

## 概述

特征管理系统通过集中化的特征注册表和工厂模式，解决了原有系统中硬编码特征名、默认值分散管理和特征一致性验证困难的问题。

## 系统架构

### 1. 特征注册表 (`src/core/feature_registry.py`)

集中管理所有特征的元数据，包括：
- 特征名称和类型
- 默认值和数据类型
- 配置依赖关系
- 验证函数

### 2. 特征常量 (`src/core/feature_constants.py`)

统一管理硬编码的特征名字符串：
- 基础特征名常量
- 特征分组定义
- 默认值常量
- 验证规则

### 3. 动态特征生成

基于配置参数动态生成特征名和默认值：
- 技术指标特征（RSI、HMA、ATR等）
- 价格变化特征
- MTFA特征
- 平滑特征

## 主要改进

### ✅ 解决的问题

1. **硬编码特征名分散** → 集中到常量文件
2. **默认值管理混乱** → 统一的默认值生成
3. **特征名不一致** → 动态构建器确保一致性
4. **缺乏验证机制** → 完整的特征验证系统
5. **配置依赖不明确** → 明确的依赖关系定义

### 🚀 新增功能

1. **特征元数据管理**：每个特征都有完整的元数据描述
2. **动态特征生成**：基于配置参数自动生成特征名
3. **特征验证**：训练阶段验证特征名一致性
4. **类型安全**：明确的数据类型定义
5. **向后兼容**：保持现有代码正常工作

## 使用方法

### 1. 基础使用

```python
# 获取特征注册表
from src.core.feature_registry import get_feature_registry

registry = get_feature_registry()

# 获取所有特征
all_features = registry.get_all_features()

# 按类型获取特征
from src.core.feature_registry import FeatureType
candle_features = registry.get_features_by_type(FeatureType.CANDLE)
```

### 2. 动态特征生成

```python
# 配置参数
config = {
    'rsi_period': 14,
    'hma_period': 21,
    'atr_period': 14,
    'price_change_periods': [1, 3, 5],
    'enable_mtfa': True,
    'mtfa_timeframes': ['15m', '1h']
}

# 生成动态特征
dynamic_features = registry.generate_dynamic_features(config)

# 获取所有默认值
feature_defaults = registry.get_feature_defaults(config)
```

### 3. 特征验证

```python
# 在目标配置中启用验证
target_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    # ... 其他配置
    
    # 启用特征验证
    'enable_feature_validation': True,
    'strict_feature_validation': False  # 开发阶段可设为True
}

# 验证将在 add_classification_features 中自动执行
```

### 4. 使用特征常量

```python
from src.core.feature_constants import (
    get_all_feature_names,
    get_feature_group,
    get_feature_default_value,
    validate_feature_name
)

# 获取所有静态特征名
static_features = get_all_feature_names()

# 获取特定分组的特征
candle_features = get_feature_group('candle')

# 获取特征默认值
default_val = get_feature_default_value('RSI_14')

# 验证特征名
is_valid = validate_feature_name('body_size')
```

## 特征类型定义

### FeatureType 枚举

```python
class FeatureType(Enum):
    PRICE = "price"           # 价格相关特征
    VOLUME = "volume"         # 成交量特征
    TECHNICAL = "technical"   # 技术指标
    CANDLE = "candle"        # K线形态
    TIME = "time"            # 时间特征
    FUND_FLOW = "fund_flow"  # 资金流向
    TREND = "trend"          # 趋势特征
    MTFA = "mtfa"            # 多时间框架
    DERIVED = "derived"      # 衍生特征
    TARGET = "target"        # 目标变量
```

### FeatureDataType 枚举

```python
class FeatureDataType(Enum):
    FLOAT = "float64"
    INT = "int64"
    BOOL = "bool"
    STRING = "object"
```

## 特征名构建器

### 技术指标特征

```python
from src.core.feature_registry import FeatureNameBuilder

# RSI特征名
rsi_name = FeatureNameBuilder.build_technical_indicator_name('RSI', 14)
# 结果: "RSI_14"

# CCI特征名
cci_name = FeatureNameBuilder.build_technical_indicator_name('CCI', 14, constant=0.015)
# 结果: "CCI_14_0.015"
```

### 价格变化特征

```python
# 价格变化特征名
price_change = FeatureNameBuilder.build_price_change_name(3)
# 结果: "price_change_3p"
```

### MTFA特征

```python
# MTFA特征名
mtfa_feature = FeatureNameBuilder.build_mtfa_feature_name('RSI_14', '1h')
# 结果: "RSI_14_1h"
```

## 配置选项

### 特征验证配置

```python
target_config = {
    # 基础验证
    'enable_feature_validation': True,    # 启用特征验证
    'strict_feature_validation': False,   # 严格模式（开发用）
    
    # 验证行为
    'log_feature_statistics': True,       # 记录特征统计
    'feature_consistency_check': True,    # 一致性检查
}
```

### 开发阶段配置

```python
# 开发和调试阶段
development_config = {
    'enable_feature_validation': True,
    'strict_feature_validation': True,    # 严格模式，发现问题立即报错
    'log_feature_statistics': True,
    'feature_consistency_check': True,
}
```

### 生产环境配置

```python
# 生产环境
production_config = {
    'enable_feature_validation': True,
    'strict_feature_validation': False,   # 宽松模式，只记录警告
    'log_feature_statistics': False,      # 减少日志输出
    'feature_consistency_check': True,
}
```

## 验证结果解读

### 验证输出示例

```
特征验证失败 (5m):
  缺失特征 (1): ['candlestick_pattern_name']
  意外特征 (134): ['volatility_stability_score', 'price_change_accel', ...]
```

### 结果字段说明

- **total_registered**: 注册表中的特征总数
- **total_found**: DataFrame中发现的特征数
- **missing_features**: 注册表中有但DataFrame中缺失的特征
- **unexpected_features**: DataFrame中有但注册表中没有的特征
- **validation_passed**: 验证是否通过

## 最佳实践

### 1. 添加新特征

```python
# 1. 在特征注册表中注册
from src.core.feature_registry import FeatureMetadata, FeatureType, FeatureDataType

new_feature = FeatureMetadata(
    name="new_indicator",
    feature_type=FeatureType.TECHNICAL,
    data_type=FeatureDataType.FLOAT,
    default_value=0.0,
    description="新的技术指标",
    dependencies=['close'],
    config_params=['new_indicator_period'],
    is_dynamic=True
)

registry.register_feature(new_feature)

# 2. 在特征常量中添加（如果是静态特征）
# 3. 在动态生成器中添加逻辑（如果是动态特征）
```

### 2. 特征名一致性

```python
# ✅ 正确：使用构建器
feature_name = FeatureNameBuilder.build_technical_indicator_name('RSI', period)

# ❌ 错误：硬编码
feature_name = f"RSI_{period}"  # 可能与注册表不一致
```

### 3. 默认值管理

```python
# ✅ 正确：使用注册表
default_val = registry.get_feature_defaults(config)[feature_name]

# ❌ 错误：硬编码
default_val = 50.0  # 可能与注册表不一致
```

## 故障排除

### 1. 特征验证失败

**问题**: 大量意外特征
**解决**: 
- 检查是否有新增的特征未在注册表中注册
- 更新特征注册表或常量定义

**问题**: 缺失特征
**解决**:
- 检查特征计算逻辑是否正确
- 确认配置参数是否正确设置

### 2. 导入错误

**问题**: `ImportError: cannot import name 'get_feature_registry'`
**解决**:
- 确保 `src/core/feature_registry.py` 文件存在
- 检查文件语法是否正确

### 3. 性能问题

**问题**: 特征验证影响性能
**解决**:
- 生产环境设置 `enable_feature_validation: False`
- 或使用 `strict_feature_validation: False`

## 未来扩展

### 长期规划：完整的工厂模式

```python
# 未来可能的扩展
class FeatureFactory:
    def create_feature(self, feature_type: str, config: dict) -> pd.Series:
        """根据类型和配置创建特征"""
        pass
    
    def validate_feature(self, feature: pd.Series, metadata: FeatureMetadata) -> bool:
        """验证特征是否符合元数据定义"""
        pass
```

### 特征版本管理

```python
# 特征版本控制
@dataclass
class FeatureVersion:
    version: str
    created_date: datetime
    deprecated: bool = False
    migration_path: Optional[str] = None
```

## 总结

特征管理系统提供了：

1. **集中化管理** - 所有特征元数据统一管理
2. **类型安全** - 明确的类型定义和验证
3. **动态生成** - 基于配置的特征名生成
4. **一致性保证** - 训练阶段的特征验证
5. **向后兼容** - 不影响现有代码

这个系统为未来的特征工程提供了坚实的基础，支持更复杂的特征管理需求。
