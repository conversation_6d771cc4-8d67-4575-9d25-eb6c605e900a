#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征注册表和管理系统单元测试
测试特征注册、动态生成、验证等功能
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 忽略警告
warnings.filterwarnings('ignore')

class TestFeatureRegistry(unittest.TestCase):
    """测试特征注册表"""
    
    def setUp(self):
        try:
            from src.core.feature_registry import get_feature_registry, FeatureMetadata, FeatureType, FeatureDataType
            self.registry = get_feature_registry()
            self.FeatureMetadata = FeatureMetadata
            self.FeatureType = FeatureType
            self.FeatureDataType = FeatureDataType
        except ImportError:
            self.skipTest("Feature registry not available")
    
    def test_get_all_features(self):
        """测试获取所有特征"""
        all_features = self.registry.get_all_features()
        self.assertIsInstance(all_features, dict)
        self.assertGreater(len(all_features), 0)
    
    def test_get_features_by_type(self):
        """测试按类型获取特征"""
        candle_features = self.registry.get_features_by_type(self.FeatureType.CANDLE)
        self.assertIsInstance(candle_features, list)
        
        # 检查返回的都是FeatureMetadata对象
        for feature in candle_features:
            self.assertIsInstance(feature, self.FeatureMetadata)
            self.assertEqual(feature.feature_type, self.FeatureType.CANDLE)
    
    def test_dynamic_feature_generation(self):
        """测试动态特征生成"""
        config = {
            'rsi_period': 14,
            'hma_period': 21,
            'atr_period': 14,
            'price_change_periods': [1, 3, 5],
            'enable_mtfa': True,
            'mtfa_timeframes': ['15m', '1h']
        }
        
        dynamic_features = self.registry.generate_dynamic_features(config)
        self.assertIsInstance(dynamic_features, dict)
        self.assertGreater(len(dynamic_features), 0)
        
        # 检查特定特征是否生成
        self.assertIn('RSI_14', dynamic_features)
        self.assertIn('HMA_21', dynamic_features)
        self.assertIn('ATRr_14', dynamic_features)
        self.assertIn('price_change_1p', dynamic_features)
    
    def test_feature_defaults_generation(self):
        """测试特征默认值生成"""
        config = {
            'rsi_period': 14,
            'hma_period': 21,
            'price_change_periods': [1, 3]
        }
        
        defaults = self.registry.get_feature_defaults(config)
        self.assertIsInstance(defaults, dict)
        self.assertGreater(len(defaults), 0)
        
        # 检查特定默认值
        if 'RSI_14' in defaults:
            self.assertEqual(defaults['RSI_14'], 50.0)
        if 'body_size' in defaults:
            self.assertEqual(defaults['body_size'], 0.0)
        if 'close_pos_in_candle' in defaults:
            self.assertEqual(defaults['close_pos_in_candle'], 0.5)
    
    def test_feature_validation(self):
        """测试特征验证"""
        # 创建模拟DataFrame列
        mock_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'body_size', 'candle_range', 'RSI_14', 'MACD',
            'unexpected_feature', 'target_up_1p'
        ]
        
        config = {'rsi_period': 14}
        
        validation_result = self.registry.validate_feature_names(mock_columns, config)
        
        self.assertIsInstance(validation_result, dict)
        self.assertIn('validation_passed', validation_result)
        self.assertIn('missing_features', validation_result)
        self.assertIn('unexpected_features', validation_result)
        self.assertIn('total_registered', validation_result)
        self.assertIn('total_found', validation_result)
    
    def test_register_new_feature(self):
        """测试注册新特征"""
        new_feature = self.FeatureMetadata(
            name="test_feature",
            feature_type=self.FeatureType.TECHNICAL,
            data_type=self.FeatureDataType.FLOAT,
            default_value=0.0,
            description="Test feature"
        )
        
        # 注册新特征
        self.registry.register_feature(new_feature)
        
        # 验证特征已注册
        retrieved_feature = self.registry.get_feature("test_feature")
        self.assertIsNotNone(retrieved_feature)
        self.assertEqual(retrieved_feature.name, "test_feature")
        self.assertEqual(retrieved_feature.feature_type, self.FeatureType.TECHNICAL)


class TestFeatureConstants(unittest.TestCase):
    """测试特征常量"""
    
    def setUp(self):
        try:
            from src.core.feature_constants import (
                get_all_feature_names, get_feature_group, get_feature_default_value,
                validate_feature_name, is_dynamic_feature, BASE_FEATURE_DEFAULTS
            )
            self.get_all_feature_names = get_all_feature_names
            self.get_feature_group = get_feature_group
            self.get_feature_default_value = get_feature_default_value
            self.validate_feature_name = validate_feature_name
            self.is_dynamic_feature = is_dynamic_feature
            self.BASE_FEATURE_DEFAULTS = BASE_FEATURE_DEFAULTS
        except ImportError:
            self.skipTest("Feature constants not available")
    
    def test_get_all_feature_names(self):
        """测试获取所有特征名"""
        all_features = self.get_all_feature_names()
        self.assertIsInstance(all_features, set)
        self.assertGreater(len(all_features), 0)
        
        # 检查一些已知特征
        self.assertIn('body_size', all_features)
        self.assertIn('MACD', all_features)
    
    def test_get_feature_group(self):
        """测试获取特征分组"""
        candle_features = self.get_feature_group('candle')
        self.assertIsInstance(candle_features, set)
        self.assertGreater(len(candle_features), 0)
        
        # 检查K线特征
        self.assertIn('body_size', candle_features)
        self.assertIn('candle_range', candle_features)
    
    def test_get_feature_default_value(self):
        """测试获取特征默认值"""
        # 测试已知特征的默认值
        self.assertEqual(self.get_feature_default_value('body_size'), 0.0)
        self.assertEqual(self.get_feature_default_value('close_pos_in_candle'), 0.5)
        self.assertEqual(self.get_feature_default_value('volume_vs_avg'), 1.0)
        
        # 测试动态特征
        self.assertEqual(self.get_feature_default_value('RSI_14'), 50.0)
        self.assertEqual(self.get_feature_default_value('WILLR_14'), -50.0)
    
    def test_validate_feature_name(self):
        """测试特征名验证"""
        # 测试有效特征名
        self.assertTrue(self.validate_feature_name('body_size'))
        self.assertTrue(self.validate_feature_name('MACD'))
        self.assertTrue(self.validate_feature_name('RSI_14'))
        
        # 测试无效特征名
        self.assertFalse(self.validate_feature_name('open'))  # 基础列
        self.assertFalse(self.validate_feature_name('target_up_1p'))  # 目标列
        self.assertFalse(self.validate_feature_name(''))  # 空字符串
    
    def test_is_dynamic_feature(self):
        """测试动态特征识别"""
        # 测试动态特征
        self.assertTrue(self.is_dynamic_feature('RSI_14'))
        self.assertTrue(self.is_dynamic_feature('HMA_21'))
        
        # 测试静态特征
        self.assertFalse(self.is_dynamic_feature('body_size'))
        self.assertFalse(self.is_dynamic_feature('MACD'))
    
    def test_base_feature_defaults(self):
        """测试基础特征默认值"""
        self.assertIsInstance(self.BASE_FEATURE_DEFAULTS, dict)
        self.assertGreater(len(self.BASE_FEATURE_DEFAULTS), 0)
        
        # 检查一些关键默认值
        self.assertIn('body_size', self.BASE_FEATURE_DEFAULTS)
        self.assertIn('close_pos_in_candle', self.BASE_FEATURE_DEFAULTS)


class TestFeatureNameBuilder(unittest.TestCase):
    """测试特征名构建器"""
    
    def setUp(self):
        try:
            from src.core.feature_registry import FeatureNameBuilder
            self.builder = FeatureNameBuilder
        except ImportError:
            self.skipTest("FeatureNameBuilder not available")
    
    def test_technical_indicator_names(self):
        """测试技术指标名称构建"""
        # 测试RSI
        rsi_name = self.builder.build_technical_indicator_name('RSI', 14)
        self.assertEqual(rsi_name, 'RSI_14')
        
        # 测试HMA
        hma_name = self.builder.build_technical_indicator_name('HMA', 21)
        self.assertEqual(hma_name, 'HMA_21')
        
        # 测试ATR
        atr_name = self.builder.build_technical_indicator_name('ATR', 14)
        self.assertEqual(atr_name, 'ATRr_14')
        
        # 测试CCI（带常数）
        cci_name = self.builder.build_technical_indicator_name('CCI', 14, constant=0.015)
        self.assertEqual(cci_name, 'CCI_14_0.015')
    
    def test_price_change_names(self):
        """测试价格变化名称构建"""
        name_1p = self.builder.build_price_change_name(1)
        self.assertEqual(name_1p, 'price_change_1p')
        
        name_3p = self.builder.build_price_change_name(3)
        self.assertEqual(name_3p, 'price_change_3p')
    
    def test_volume_change_names(self):
        """测试成交量变化名称构建"""
        name_1p = self.builder.build_volume_change_name(1)
        self.assertEqual(name_1p, 'volume_change_1p')
        
        name_5p = self.builder.build_volume_change_name(5)
        self.assertEqual(name_5p, 'volume_change_5p')
    
    def test_smooth_feature_names(self):
        """测试平滑特征名称构建"""
        smooth_name = self.builder.build_smooth_feature_name('upper_shadow', 3)
        self.assertEqual(smooth_name, 'upper_shadow_smooth3p')
        
        smooth_name2 = self.builder.build_smooth_feature_name('body_size', 5)
        self.assertEqual(smooth_name2, 'body_size_smooth5p')
    
    def test_mtfa_feature_names(self):
        """测试MTFA特征名称构建"""
        mtfa_name = self.builder.build_mtfa_feature_name('RSI_14', '1h')
        self.assertEqual(mtfa_name, 'RSI_14_1h')
        
        mtfa_name2 = self.builder.build_mtfa_feature_name('body_size', '15m')
        self.assertEqual(mtfa_name2, 'body_size_15m')


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
