#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器

提供统一的配置访问接口，整合静态配置、动态配置和强化配置管理器，
支持类型转换、默认值处理和验证。

主要功能：
1. 统一的配置访问接口
2. 多配置源整合（静态、动态、强化）
3. 类型转换和验证
4. 默认值处理
5. 线程安全访问
6. 配置优先级管理
"""

import threading
import logging
from typing import Any, Dict, List, Optional, Union, Type, TypeVar, Callable
from enum import Enum
import json
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

T = TypeVar('T')

class ConfigPriority(Enum):
    """配置优先级枚举"""
    DYNAMIC = 1      # 动态配置（最高优先级）
    ROBUST = 2       # 强化配置管理器
    STATIC = 3       # 静态配置（最低优先级）

class ConfigValidationError(Exception):
    """配置验证异常"""
    def __init__(self, key: str, value: Any, expected_type: Type, message: str = None):
        self.key = key
        self.value = value
        self.expected_type = expected_type
        self.message = message or f"配置项 '{key}' 的值 '{value}' 无法转换为类型 {expected_type.__name__}"
        super().__init__(self.message)

class ConfigAccessResult:
    """配置访问结果"""
    def __init__(self, value: Any, source: str, found: bool = True, converted: bool = False):
        self.value = value
        self.source = source  # 配置来源
        self.found = found    # 是否找到配置
        self.converted = converted  # 是否进行了类型转换
    
    def __repr__(self):
        return f"ConfigAccessResult(value={self.value}, source={self.source}, found={self.found}, converted={self.converted})"

class UnifiedConfigManager:
    """
    统一配置管理器
    
    整合多个配置源，提供统一的配置访问接口
    """
    
    def __init__(self):
        """初始化统一配置管理器"""
        self._lock = threading.RLock()
        self._static_config = None
        self._dynamic_config_manager = None
        self._robust_config_manager = None
        
        # 配置缓存
        self._cache = {}
        self._cache_enabled = True
        self._cache_ttl = 300  # 缓存5分钟
        
        # 类型转换器
        self._type_converters = {
            int: self._convert_to_int,
            float: self._convert_to_float,
            bool: self._convert_to_bool,
            str: self._convert_to_str,
            list: self._convert_to_list,
            dict: self._convert_to_dict
        }
        
        # 验证器
        self._validators = {}
        
        logger.info("统一配置管理器初始化完成")
    
    def initialize(self, static_config=None, dynamic_config_manager=None, robust_config_manager=None):
        """
        初始化配置源
        
        Args:
            static_config: 静态配置模块
            dynamic_config_manager: 动态配置管理器
            robust_config_manager: 强化配置管理器
        """
        with self._lock:
            self._static_config = static_config
            self._dynamic_config_manager = dynamic_config_manager
            self._robust_config_manager = robust_config_manager
            
            # 清空缓存
            self._cache.clear()
            
            logger.info(f"配置源初始化完成: static={static_config is not None}, "
                       f"dynamic={dynamic_config_manager is not None}, "
                       f"robust={robust_config_manager is not None}")
    
    def get(self, key: str, default: Any = None, dtype: Type[T] = None, 
            target_name: str = None, validate: bool = True, 
            use_cache: bool = True) -> T:
        """
        获取配置值
        
        Args:
            key: 配置键名
            default: 默认值
            dtype: 期望的数据类型
            target_name: 目标名称（用于获取目标特定配置）
            validate: 是否进行验证
            use_cache: 是否使用缓存
            
        Returns:
            配置值
            
        Raises:
            ConfigValidationError: 当配置验证失败时
        """
        cache_key = f"{key}_{target_name}_{dtype.__name__ if dtype else 'None'}"
        
        # 检查缓存
        if use_cache and self._cache_enabled:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result.value
        
        # 获取配置值
        result = self._get_config_value(key, target_name)
        
        # 如果没有找到配置，使用默认值
        if not result.found:
            if default is not None:
                result = ConfigAccessResult(default, "default", True)
            else:
                logger.warning(f"配置项 '{key}' 未找到且无默认值")
                return None
        
        # 类型转换
        if dtype is not None and result.value is not None:
            try:
                converted_value = self._convert_type(result.value, dtype)
                result.value = converted_value
                result.converted = True
            except Exception as e:
                if validate:
                    raise ConfigValidationError(key, result.value, dtype, str(e))
                else:
                    logger.warning(f"配置项 '{key}' 类型转换失败: {e}")
        
        # 验证
        if validate and key in self._validators:
            validator = self._validators[key]
            if not validator(result.value):
                raise ConfigValidationError(key, result.value, dtype, "验证失败")
        
        # 缓存结果
        if use_cache and self._cache_enabled:
            self._set_cache(cache_key, result)
        
        logger.debug(f"获取配置: {key} = {result.value} (来源: {result.source})")
        return result.value
    
    def _get_config_value(self, key: str, target_name: str = None) -> ConfigAccessResult:
        """
        从配置源获取配置值
        
        Args:
            key: 配置键名
            target_name: 目标名称
            
        Returns:
            配置访问结果
        """
        with self._lock:
            # 优先级1: 动态配置
            if self._dynamic_config_manager:
                try:
                    if target_name:
                        # 尝试获取目标特定配置
                        target_params = self._dynamic_config_manager.get_target_params(target_name)
                        if key in target_params:
                            return ConfigAccessResult(target_params[key], "dynamic_target", True)
                    
                    # 尝试获取全局动态配置
                    global_value = self._dynamic_config_manager.get_global_param(key)
                    if global_value is not None:
                        return ConfigAccessResult(global_value, "dynamic_global", True)
                except Exception as e:
                    logger.debug(f"动态配置获取失败: {e}")
            
            # 优先级2: 强化配置管理器
            if self._robust_config_manager and target_name:
                try:
                    wrapper = self._robust_config_manager.get_target_config(target_name)
                    if hasattr(wrapper, 'get'):
                        value = wrapper.get(key)
                        if value is not None:
                            return ConfigAccessResult(value, "robust", True)
                except Exception as e:
                    logger.debug(f"强化配置获取失败: {e}")
            
            # 优先级3: 静态配置
            if self._static_config:
                try:
                    # 尝试直接获取
                    if hasattr(self._static_config, key):
                        value = getattr(self._static_config, key)
                        return ConfigAccessResult(value, "static_direct", True)

                    # 尝试从目标配置获取
                    if target_name and hasattr(self._static_config, 'get_target_config'):
                        target_config = self._static_config.get_target_config(target_name)
                        if key in target_config:
                            return ConfigAccessResult(target_config[key], "static_target", True)
                except Exception as e:
                    logger.debug(f"静态配置获取失败: {e}")

            return ConfigAccessResult(None, "not_found", False)
    
    def _convert_type(self, value: Any, target_type: Type[T]) -> T:
        """
        类型转换
        
        Args:
            value: 原始值
            target_type: 目标类型
            
        Returns:
            转换后的值
        """
        if value is None:
            return None
        
        if isinstance(value, target_type):
            return value
        
        if target_type in self._type_converters:
            return self._type_converters[target_type](value)
        
        # 尝试直接转换
        try:
            return target_type(value)
        except (ValueError, TypeError) as e:
            raise ConfigValidationError("", value, target_type, f"类型转换失败: {e}")
    
    def _convert_to_int(self, value: Any) -> int:
        """转换为整数"""
        if isinstance(value, bool):
            return int(value)
        if isinstance(value, str):
            return int(float(value))  # 支持 "1.0" -> 1
        return int(value)
    
    def _convert_to_float(self, value: Any) -> float:
        """转换为浮点数"""
        if isinstance(value, bool):
            return float(value)
        return float(value)
    
    def _convert_to_bool(self, value: Any) -> bool:
        """转换为布尔值"""
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', 'enabled')
        return bool(value)
    
    def _convert_to_str(self, value: Any) -> str:
        """转换为字符串"""
        return str(value)
    
    def _convert_to_list(self, value: Any) -> list:
        """转换为列表"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return [item.strip() for item in value.split(',')]
        if hasattr(value, '__iter__') and not isinstance(value, (str, dict)):
            return list(value)
        return [value]
    
    def _convert_to_dict(self, value: Any) -> dict:
        """转换为字典"""
        if isinstance(value, str):
            return json.loads(value)
        return dict(value)
    
    def _get_from_cache(self, key: str) -> Optional[ConfigAccessResult]:
        """从缓存获取配置"""
        if key in self._cache:
            result, timestamp = self._cache[key]
            if (datetime.now() - timestamp).total_seconds() < self._cache_ttl:
                return result
            else:
                del self._cache[key]
        return None
    
    def _set_cache(self, key: str, result: ConfigAccessResult):
        """设置缓存"""
        self._cache[key] = (result, datetime.now())
    
    def clear_cache(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            logger.info("配置缓存已清空")
    
    def add_validator(self, key: str, validator: Callable[[Any], bool]):
        """
        添加配置验证器
        
        Args:
            key: 配置键名
            validator: 验证函数
        """
        self._validators[key] = validator
        logger.info(f"为配置项 '{key}' 添加验证器")
    
    def get_config_info(self, key: str, target_name: str = None) -> Dict[str, Any]:
        """
        获取配置信息（用于调试）
        
        Args:
            key: 配置键名
            target_name: 目标名称
            
        Returns:
            配置信息字典
        """
        result = self._get_config_value(key, target_name)
        return {
            'key': key,
            'target_name': target_name,
            'value': result.value,
            'source': result.source,
            'found': result.found,
            'type': type(result.value).__name__ if result.value is not None else 'None'
        }

    # 便捷方法
    def get_int(self, key: str, default: int = None, target_name: str = None) -> int:
        """获取整数配置"""
        return self.get(key, default, int, target_name)

    def get_float(self, key: str, default: float = None, target_name: str = None) -> float:
        """获取浮点数配置"""
        return self.get(key, default, float, target_name)

    def get_bool(self, key: str, default: bool = None, target_name: str = None) -> bool:
        """获取布尔值配置"""
        return self.get(key, default, bool, target_name)

    def get_str(self, key: str, default: str = None, target_name: str = None) -> str:
        """获取字符串配置"""
        return self.get(key, default, str, target_name)

    def get_list(self, key: str, default: list = None, target_name: str = None) -> list:
        """获取列表配置"""
        return self.get(key, default, list, target_name)

    def get_dict(self, key: str, default: dict = None, target_name: str = None) -> dict:
        """获取字典配置"""
        return self.get(key, default, dict, target_name)


# 全局配置管理器实例
_global_config_manager = None
_global_config_manager_lock = threading.Lock()

def get_config_manager() -> UnifiedConfigManager:
    """
    获取全局配置管理器实例（单例模式）

    Returns:
        统一配置管理器实例
    """
    global _global_config_manager

    if _global_config_manager is None:
        with _global_config_manager_lock:
            if _global_config_manager is None:
                _global_config_manager = UnifiedConfigManager()

    return _global_config_manager

def initialize_config_manager(static_config=None, dynamic_config_manager=None, robust_config_manager=None):
    """
    初始化全局配置管理器

    Args:
        static_config: 静态配置模块
        dynamic_config_manager: 动态配置管理器
        robust_config_manager: 强化配置管理器
    """
    config_manager = get_config_manager()
    config_manager.initialize(static_config, dynamic_config_manager, robust_config_manager)
    logger.info("全局配置管理器初始化完成")

# 便捷函数
def get_config(key: str, default: Any = None, dtype: Type[T] = None,
               target_name: str = None, validate: bool = True) -> T:
    """
    便捷函数：获取配置值

    Args:
        key: 配置键名
        default: 默认值
        dtype: 期望的数据类型
        target_name: 目标名称
        validate: 是否进行验证

    Returns:
        配置值
    """
    return get_config_manager().get(key, default, dtype, target_name, validate)

def get_config_int(key: str, default: int = None, target_name: str = None) -> int:
    """便捷函数：获取整数配置"""
    return get_config_manager().get_int(key, default, target_name)

def get_config_float(key: str, default: float = None, target_name: str = None) -> float:
    """便捷函数：获取浮点数配置"""
    return get_config_manager().get_float(key, default, target_name)

def get_config_bool(key: str, default: bool = None, target_name: str = None) -> bool:
    """便捷函数：获取布尔值配置"""
    return get_config_manager().get_bool(key, default, target_name)

def get_config_str(key: str, default: str = None, target_name: str = None) -> str:
    """便捷函数：获取字符串配置"""
    return get_config_manager().get_str(key, default, target_name)

def get_config_list(key: str, default: list = None, target_name: str = None) -> list:
    """便捷函数：获取列表配置"""
    return get_config_manager().get_list(key, default, target_name)

def get_config_dict(key: str, default: dict = None, target_name: str = None) -> dict:
    """便捷函数：获取字典配置"""
    return get_config_manager().get_dict(key, default, target_name)

def clear_config_cache():
    """便捷函数：清空配置缓存"""
    get_config_manager().clear_cache()

def get_config_info(key: str, target_name: str = None) -> Dict[str, Any]:
    """便捷函数：获取配置信息"""
    return get_config_manager().get_config_info(key, target_name)
