#!/usr/bin/env python3
# enterprise_usage_examples.py
"""
企业级 TradeLogger 使用示例

展示以下功能：
1. 基础异步日志记录
2. 日志轮转和目录结构
3. 数据分析和查询
4. 性能测试
5. 与现有系统集成
"""

import sys
import os
import time
import tempfile
import shutil
import threading
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.enterprise_trade_logger import get_enterprise_trade_logger, reset_enterprise_logger


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 获取企业级日志记录器
        logger = get_enterprise_trade_logger(
            base_log_dir=temp_dir,
            max_file_size=1024 * 1024,  # 1MB for testing
            batch_size=5,
            flush_interval=0.5
        )
        
        print(f"日志目录: {temp_dir}")
        print(f"当前日志文件: {logger.get_current_log_file()}")
        
        # 记录一些交易
        for i in range(10):
            context_data = {
                "signal_probability": 0.7 + i * 0.02,
                "market_regime": "uptrend" if i % 2 == 0 else "sideways",
                "atr_percent": 2.0 + i * 0.1,
                "meta_model_inputs": {
                    "model_1": 0.6 + i * 0.03,
                    "model_2": 0.65 + i * 0.02,
                    "confidence": 0.8 + i * 0.01
                },
                "top_features": {
                    f"feature_{j}": 0.1 + j * 0.05 for j in range(5)
                }
            }
            
            trade_id = logger.record_trade_entry(
                target_name=f"Strategy_{i % 3}",
                symbol="BTCUSDT",
                direction="LONG" if i % 2 == 0 else "SHORT",
                entry_price=50000 + i * 100,
                amount=10 + i,
                context_data=context_data
            )
            
            print(f"记录开仓 {i+1}: {trade_id}")
            
            # 模拟一些延迟
            time.sleep(0.1)
            
            # 随机平仓一些交易
            if i > 2 and i % 3 == 0:
                exit_price = 50000 + i * 100 + (100 if i % 2 == 0 else -50)
                result = "WIN" if exit_price > 50000 + i * 100 else "LOSS"
                
                logger.record_trade_exit(trade_id, exit_price, result)
                print(f"记录平仓 {i+1}: {result}")
        
        # 等待异步写入完成
        print("等待异步写入完成...")
        time.sleep(2)
        
        # 获取统计信息
        stats = logger.get_stats()
        print(f"\n统计信息:")
        print(f"  总开仓数: {stats['total_entries']}")
        print(f"  总平仓数: {stats['total_exits']}")
        print(f"  待平仓数: {stats['pending_trades']}")
        print(f"  写入统计: {stats['writer_stats']}")
        
        # 强制刷新
        logger.flush()
        time.sleep(1)
        
        # 查看目录结构
        print(f"\n目录结构:")
        for root, dirs, files in os.walk(temp_dir):
            level = root.replace(temp_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"{subindent}{file} ({file_size} bytes)")
        
    finally:
        # 清理
        reset_enterprise_logger()
        shutil.rmtree(temp_dir, ignore_errors=True)


def example_log_rotation():
    """日志轮转示例"""
    print("\n=== 日志轮转示例 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 设置小的文件大小以触发轮转
        logger = get_enterprise_trade_logger(
            base_log_dir=temp_dir,
            max_file_size=2048,  # 2KB
            batch_size=1,  # 立即写入
            flush_interval=0.1
        )
        
        print(f"最大文件大小: 2KB")
        
        # 记录大量交易以触发轮转
        for i in range(50):
            # 创建较大的上下文数据
            context_data = {
                "signal_probability": 0.7,
                "market_regime": f"market_state_with_long_description_{i}",
                "atr_percent": 2.5,
                "meta_model_inputs": {
                    f"model_{j}": 0.6 + j * 0.01 for j in range(20)
                },
                "top_features": {
                    f"feature_with_long_name_{j}": 0.1 + j * 0.001 for j in range(50)
                }
            }
            
            trade_id = logger.record_trade_entry(
                target_name=f"LargeDataStrategy_{i}",
                symbol="BTCUSDT",
                direction="LONG",
                entry_price=50000,
                amount=10,
                context_data=context_data
            )
            
            # 立即平仓
            logger.record_trade_exit(trade_id, 51000, "WIN")
            
            if i % 10 == 0:
                print(f"已记录 {i+1} 笔交易")
                time.sleep(0.2)  # 让异步写入有时间处理
        
        # 等待写入完成
        time.sleep(3)
        logger.flush()
        time.sleep(1)
        
        # 查看轮转结果
        print(f"\n轮转结果:")
        csv_files = []
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                if file.endswith('.csv'):
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    csv_files.append((file_path, file_size))
                    print(f"  {file}: {file_size} bytes")
        
        print(f"总共生成了 {len(csv_files)} 个CSV文件")
        
    finally:
        reset_enterprise_logger()
        shutil.rmtree(temp_dir, ignore_errors=True)


def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        logger = get_enterprise_trade_logger(base_log_dir=temp_dir)
        
        # 生成多天的测试数据
        base_date = datetime.now() - timedelta(days=5)
        
        for day in range(5):
            current_date = base_date + timedelta(days=day)
            
            # 模拟每天的交易
            for i in range(20):
                context_data = {
                    "signal_probability": 0.6 + (i % 10) * 0.04,
                    "market_regime": ["uptrend", "downtrend", "sideways"][i % 3],
                    "atr_percent": 1.5 + (i % 5) * 0.5,
                    "meta_model_inputs": {
                        "ensemble_score": 0.7 + (i % 8) * 0.03
                    }
                }
                
                trade_id = logger.record_trade_entry(
                    target_name=f"Strategy_{i % 4}",
                    symbol=["BTCUSDT", "ETHUSDT", "ADAUSDT"][i % 3],
                    direction="LONG" if i % 2 == 0 else "SHORT",
                    entry_price=50000 + i * 10,
                    amount=10 + i % 5,
                    context_data=context_data
                )
                
                # 随机平仓
                if i % 2 == 0:
                    exit_price = 50000 + i * 10 + (50 if i % 4 == 0 else -30)
                    result = "WIN" if exit_price > 50000 + i * 10 else "LOSS"
                    logger.record_trade_exit(trade_id, exit_price, result)
        
        # 等待写入完成
        time.sleep(3)
        logger.flush()
        time.sleep(2)
        
        # 数据分析
        print("开始数据分析...")
        
        # 加载所有数据
        all_data = logger.load_trade_logs()
        print(f"总交易记录数: {len(all_data)}")
        
        if len(all_data) > 0:
            # 基础统计
            print(f"\n基础统计:")
            print(f"  完成的交易: {len(all_data[all_data['result'].notna()])}")
            print(f"  盈利交易: {len(all_data[all_data['result'] == 'WIN'])}")
            print(f"  亏损交易: {len(all_data[all_data['result'] == 'LOSS'])}")
            
            # 按策略分析
            if 'target_name' in all_data.columns:
                strategy_stats = all_data.groupby('target_name').agg({
                    'trade_id': 'count',
                    'result': lambda x: (x == 'WIN').sum() / len(x.dropna()) if len(x.dropna()) > 0 else 0,
                    'profit_loss': 'sum'
                }).round(3)
                strategy_stats.columns = ['交易数', '胜率', '总盈亏']
                print(f"\n按策略统计:")
                print(strategy_stats)
            
            # 按市场状态分析
            if 'entry_market_regime' in all_data.columns:
                regime_stats = all_data.groupby('entry_market_regime').agg({
                    'trade_id': 'count',
                    'result': lambda x: (x == 'WIN').sum() / len(x.dropna()) if len(x.dropna()) > 0 else 0
                }).round(3)
                regime_stats.columns = ['交易数', '胜率']
                print(f"\n按市场状态统计:")
                print(regime_stats)
            
            # 信号概率分析
            if 'entry_signal_probability' in all_data.columns:
                prob_data = all_data[all_data['entry_signal_probability'].notna()]
                if len(prob_data) > 0:
                    # 按概率区间分析
                    prob_data['prob_range'] = pd.cut(
                        prob_data['entry_signal_probability'], 
                        bins=[0, 0.6, 0.7, 0.8, 1.0], 
                        labels=['低(0-0.6)', '中(0.6-0.7)', '高(0.7-0.8)', '极高(0.8-1.0)']
                    )
                    
                    prob_stats = prob_data.groupby('prob_range').agg({
                        'trade_id': 'count',
                        'result': lambda x: (x == 'WIN').sum() / len(x.dropna()) if len(x.dropna()) > 0 else 0
                    }).round(3)
                    prob_stats.columns = ['交易数', '胜率']
                    print(f"\n按信号概率统计:")
                    print(prob_stats)
        
        # 按日期范围查询
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        
        recent_data = logger.load_trade_logs(start_date, end_date)
        print(f"\n最近3天的交易记录: {len(recent_data)} 条")
        
    finally:
        reset_enterprise_logger()
        shutil.rmtree(temp_dir, ignore_errors=True)


def example_performance_test():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        logger = get_enterprise_trade_logger(
            base_log_dir=temp_dir,
            batch_size=50,  # 大批量写入
            flush_interval=2.0
        )
        
        # 测试大量并发写入
        num_trades = 1000
        num_threads = 5
        
        def worker_thread(thread_id, trades_per_thread):
            """工作线程函数"""
            for i in range(trades_per_thread):
                context_data = {
                    "signal_probability": 0.7,
                    "market_regime": "test_regime",
                    "atr_percent": 2.5,
                    "meta_model_inputs": {"test": 0.8},
                    "top_features": {f"f{j}": 0.1 for j in range(10)}
                }
                
                trade_id = logger.record_trade_entry(
                    target_name=f"PerfTest_T{thread_id}",
                    symbol="BTCUSDT",
                    direction="LONG",
                    entry_price=50000,
                    amount=10,
                    context_data=context_data
                )
                
                # 立即平仓
                logger.record_trade_exit(trade_id, 51000, "WIN")
                
                if i % 50 == 0:
                    print(f"线程 {thread_id}: 已处理 {i+1} 笔交易")
        
        # 启动多个线程
        print(f"启动 {num_threads} 个线程，每个处理 {num_trades // num_threads} 笔交易")
        
        start_time = time.time()
        threads = []
        
        for t in range(num_threads):
            thread = threading.Thread(
                target=worker_thread, 
                args=(t, num_trades // num_threads)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 等待异步写入完成
        logger.flush()
        time.sleep(5)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 性能统计
        stats = logger.get_stats()
        print(f"\n性能测试结果:")
        print(f"  总耗时: {duration:.2f} 秒")
        print(f"  总交易数: {num_trades}")
        print(f"  平均TPS: {num_trades / duration:.2f} 交易/秒")
        print(f"  写入统计: {stats['writer_stats']}")
        
        # 验证数据完整性
        final_data = logger.load_trade_logs()
        print(f"  最终记录数: {len(final_data)}")
        print(f"  数据完整性: {'✅' if len(final_data) == num_trades else '❌'}")
        
    finally:
        reset_enterprise_logger()
        shutil.rmtree(temp_dir, ignore_errors=True)


def example_integration_with_prediction():
    """与预测系统集成示例"""
    print("\n=== 预测系统集成示例 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 模拟预测系统的集成
        def mock_prediction_system():
            """模拟的预测系统"""
            logger = get_enterprise_trade_logger(base_log_dir=temp_dir)
            
            # 模拟预测循环
            for cycle in range(5):
                print(f"预测周期 {cycle + 1}")
                
                # 模拟预测结果
                prediction_result = {
                    "signal": "LONG",
                    "confidence": 0.75 + cycle * 0.03,
                    "entry_price": 50000 + cycle * 100,
                    "amount": 15.0
                }
                
                # 收集上下文数据
                context_data = {
                    "signal_probability": prediction_result["confidence"],
                    "neutral_probability": 0.15,
                    "opposite_probability": 0.10,
                    "meta_model_inputs": {
                        "base_model_1": 0.72 + cycle * 0.02,
                        "base_model_2": 0.68 + cycle * 0.01,
                        "ensemble_confidence": 0.85 + cycle * 0.01
                    },
                    "market_regime": ["uptrend", "strong_uptrend", "volatile_uptrend"][cycle % 3],
                    "atr_percent": 2.0 + cycle * 0.2,
                    "adx_value": 40 + cycle * 2,
                    "top_features": {
                        f"technical_indicator_{i}": 0.1 + i * 0.02 for i in range(10)
                    }
                }
                
                # 如果信号确认，记录交易
                if prediction_result["confidence"] > 0.7:
                    trade_id = logger.record_trade_entry(
                        target_name=f"MetaModel_V2_Cycle_{cycle}",
                        symbol="BTCUSDT",
                        direction=prediction_result["signal"],
                        entry_price=prediction_result["entry_price"],
                        amount=prediction_result["amount"],
                        context_data=context_data
                    )
                    
                    print(f"  交易信号确认: {trade_id}")
                    print(f"  信号概率: {prediction_result['confidence']:.3f}")
                    print(f"  市场状态: {context_data['market_regime']}")
                    
                    # 模拟交易结果（延迟平仓）
                    time.sleep(0.5)
                    
                    exit_price = prediction_result["entry_price"] + (200 if cycle % 2 == 0 else -100)
                    result = "WIN" if exit_price > prediction_result["entry_price"] else "LOSS"
                    
                    logger.record_trade_exit(trade_id, exit_price, result)
                    print(f"  交易结果: {result}")
                else:
                    print(f"  信号置信度不足: {prediction_result['confidence']:.3f}")
                
                time.sleep(0.2)
            
            return logger
        
        # 运行模拟预测系统
        logger = mock_prediction_system()
        
        # 等待异步写入完成
        time.sleep(2)
        logger.flush()
        time.sleep(1)
        
        # 分析预测系统的表现
        df = logger.load_trade_logs()
        
        if len(df) > 0:
            print(f"\n预测系统分析结果:")
            print(f"  总信号数: {len(df)}")
            print(f"  平均信号概率: {df['entry_signal_probability'].mean():.3f}")
            print(f"  胜率: {(df['result'] == 'WIN').mean():.2%}")
            print(f"  总盈亏: {df['profit_loss'].sum():.2f}")
            
            # 按市场状态分析
            regime_performance = df.groupby('entry_market_regime').agg({
                'result': lambda x: (x == 'WIN').mean(),
                'profit_loss': 'sum'
            }).round(3)
            print(f"\n按市场状态表现:")
            print(regime_performance)
        
    finally:
        reset_enterprise_logger()
        shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    print("企业级 TradeLogger 使用示例")
    print("=" * 60)
    
    # 运行所有示例
    example_basic_usage()
    example_log_rotation()
    example_data_analysis()
    example_performance_test()
    example_integration_with_prediction()
    
    print("\n所有示例完成！")
