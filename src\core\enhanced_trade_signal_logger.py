#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 增强交易信号日志记录器 V1.0
专门用于生成用户期望的详细交易日志格式

核心功能：
- 统一的交易信号日志输出格式
- 包含决策过程透明度信息
- 市场状态快照
- 风险管理信息
- 性能追踪统计
"""

import json
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np

@dataclass
class BaseModelPrediction:
    """基础模型预测结果"""
    name: str
    probability: float
    decision: str
    consistency: str  # 例如: "10/10折一致"
    
@dataclass
class MetaModelAnalysis:
    """元模型分析结果"""
    raw_probabilities: List[float]  # [P_down, P_up]
    market_certainty: float
    dynamic_thresholds: Dict[str, float]  # {"UP": 0.508, "DOWN": 0.572}
    threshold_adjustment: float  # 确定性调整值
    
@dataclass
class DecisionResult:
    """决策结果"""
    final_signal: str
    trigger_reason: str
    signal_strength: str
    veto_status: str
    
@dataclass
class RiskManagement:
    """风险管理信息"""
    recommended_position: str
    stop_loss_suggestion: str
    risk_level: str
    expiry_time: str
    
@dataclass
class MarketSnapshot:
    """市场状态快照"""
    current_price: float
    price_change_pct: float
    atr_volatility: float
    ema_trend: str
    volume_status: str
    
@dataclass
class PerformanceStats:
    """性能统计"""
    recent_win_rate: float
    recent_trades_count: int
    consecutive_status: str
    model_consistency: float
    prediction_confidence: float

class EnhancedTradeSignalLogger:
    """增强交易信号日志记录器"""
    
    def __init__(self):
        self.timezone = timezone.utc
        
    def generate_comprehensive_trade_log(self,
                                       base_models: List[BaseModelPrediction],
                                       meta_analysis: MetaModelAnalysis,
                                       decision: DecisionResult,
                                       risk_mgmt: RiskManagement,
                                       market: MarketSnapshot,
                                       performance: PerformanceStats,
                                       system_info: Dict[str, Any] = None) -> str:
        """
        生成用户期望的详细交易日志格式
        
        Args:
            base_models: 基础模型预测列表
            meta_analysis: 元模型分析结果
            decision: 决策结果
            risk_mgmt: 风险管理信息
            market: 市场状态快照
            performance: 性能统计
            system_info: 系统信息
            
        Returns:
            格式化的交易日志字符串
        """
        current_time = datetime.now(self.timezone)
        
        # 构建日志内容
        log_content = []
        
        # === 标题和时间 ===
        log_content.append("=== 🎯 BTC 30分钟二元期权交易信号 ===")
        log_content.append(f"时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        log_content.append(f"当前价格: ${market.current_price:,.2f} ({market.price_change_pct:+.2%})")
        log_content.append("")
        
        # === 基础模型预测 ===
        log_content.append("📊 基础模型预测:")
        for model in base_models:
            log_content.append(f"  - {model.name}: 概率={model.probability:.2%}, 决策={model.decision} ({model.consistency})")
        log_content.append("")
        
        # === 元模型分析 ===
        log_content.append("🧠 元模型分析:")
        p_down, p_up = meta_analysis.raw_probabilities
        log_content.append(f"  - 原始概率: [{p_down:.2%}, {p_up:.2%}] ({'DOWN优势' if p_down > p_up else 'UP优势'})")
        log_content.append(f"  - 市场确定性: {meta_analysis.market_certainty:.2f} ({'高' if meta_analysis.market_certainty > 0.7 else '中等' if meta_analysis.market_certainty > 0.4 else '低'})")
        
        up_threshold = meta_analysis.dynamic_thresholds.get('UP', 0.5)
        down_threshold = meta_analysis.dynamic_thresholds.get('DOWN', 0.5)
        log_content.append(f"  - 动态阈值: UP={up_threshold:.1%}, DOWN={down_threshold:.1%} (确定性调整{meta_analysis.threshold_adjustment:+.1%})")
        log_content.append("")
        
        # === V19.0决策结果 ===
        log_content.append("⚡ V19.0决策结果:")
        log_content.append(f"  - 最终信号: {decision.final_signal}")
        log_content.append(f"  - 触发原因: {decision.trigger_reason}")
        log_content.append(f"  - 信号强度: {decision.signal_strength}")
        log_content.append(f"  - 一票否决权: {decision.veto_status}")
        log_content.append("")
        
        # === 风险管理 ===
        log_content.append("🛡️ 风险管理:")
        log_content.append(f"  - 推荐仓位: {risk_mgmt.recommended_position}")
        log_content.append(f"  - 止损建议: {risk_mgmt.stop_loss_suggestion}")
        log_content.append(f"  - 风险等级: {risk_mgmt.risk_level}")
        log_content.append(f"  - 到期时间: {risk_mgmt.expiry_time}")
        log_content.append("")
        
        # === 市场状态 ===
        log_content.append("📈 市场状态:")
        log_content.append(f"  - ATR波动率: {market.atr_volatility:.2f}% ({'高' if market.atr_volatility > 2.5 else '正常' if market.atr_volatility > 1.0 else '低'})")
        log_content.append(f"  - EMA趋势: {market.ema_trend}")
        log_content.append(f"  - 成交量: {market.volume_status}")
        log_content.append("")
        
        # === 性能统计 ===
        log_content.append("📊 性能统计:")
        log_content.append(f"  - 最近{performance.recent_trades_count}次胜率: {performance.recent_win_rate:.0%} ({int(performance.recent_win_rate * performance.recent_trades_count)}胜{performance.recent_trades_count - int(performance.recent_win_rate * performance.recent_trades_count)}负)")
        log_content.append(f"  - 连续状态: {performance.consecutive_status}")
        log_content.append(f"  - 模型一致性: {performance.model_consistency:.0%} ({'高度一致' if performance.model_consistency > 0.8 else '中等分歧' if performance.model_consistency > 0.5 else '高度分歧'})")
        log_content.append(f"  - 预测置信度: {performance.prediction_confidence:.1f}/10")
        log_content.append("")
        
        # === 系统信息 ===
        if system_info:
            log_content.append("🔧 系统信息:")
            log_content.append(f"  - 模型版本: {system_info.get('model_version', 'V19.0')} ({system_info.get('model_type', '非对称动态阈值')})")
            log_content.append(f"  - 计算耗时: {system_info.get('computation_time', 0):.2f}秒")
            log_content.append(f"  - 数据质量: {system_info.get('data_quality', '优秀')} ({system_info.get('data_completeness', 100)}%完整)")
            log_content.append("")
        
        # === 交易建议 ===
        if decision.final_signal in ["UP_Meta", "DOWN_Meta"]:
            signal_type = "看涨" if "UP" in decision.final_signal else "看跌"
            expected_win_rate = max(p_up if "UP" in decision.final_signal else p_down, 0.5)
            log_content.append("💡 交易建议:")
            log_content.append(f"  执行30分钟{signal_type}期权，仓位{risk_mgmt.recommended_position}，预期胜率{expected_win_rate:.0%}")
        else:
            log_content.append("💡 交易建议:")
            log_content.append("  观望等待，当前市场条件不适合开仓")
        
        return "\n".join(log_content)
    
    def print_comprehensive_trade_log(self, *args, **kwargs):
        """打印详细交易日志"""
        log_content = self.generate_comprehensive_trade_log(*args, **kwargs)
        print(log_content)
        return log_content

# 全局实例
enhanced_trade_signal_logger = EnhancedTradeSignalLogger()

def create_base_model_prediction(name: str, probability: float, decision: str, 
                               consistency: str = "N/A") -> BaseModelPrediction:
    """创建基础模型预测对象"""
    return BaseModelPrediction(name, probability, decision, consistency)

def create_meta_model_analysis(raw_probabilities: List[float], market_certainty: float,
                             dynamic_thresholds: Dict[str, float], 
                             threshold_adjustment: float = 0.0) -> MetaModelAnalysis:
    """创建元模型分析对象"""
    return MetaModelAnalysis(raw_probabilities, market_certainty, dynamic_thresholds, threshold_adjustment)

def create_decision_result(final_signal: str, trigger_reason: str, 
                         signal_strength: str, veto_status: str = "通过") -> DecisionResult:
    """创建决策结果对象"""
    return DecisionResult(final_signal, trigger_reason, signal_strength, veto_status)

def create_risk_management(recommended_position: str, stop_loss_suggestion: str,
                         risk_level: str, expiry_time: str) -> RiskManagement:
    """创建风险管理对象"""
    return RiskManagement(recommended_position, stop_loss_suggestion, risk_level, expiry_time)

def create_market_snapshot(current_price: float, price_change_pct: float,
                         atr_volatility: float, ema_trend: str, volume_status: str) -> MarketSnapshot:
    """创建市场快照对象"""
    return MarketSnapshot(current_price, price_change_pct, atr_volatility, ema_trend, volume_status)

def create_performance_stats(recent_win_rate: float, recent_trades_count: int,
                           consecutive_status: str, model_consistency: float,
                           prediction_confidence: float) -> PerformanceStats:
    """创建性能统计对象"""
    return PerformanceStats(recent_win_rate, recent_trades_count, consecutive_status,
                          model_consistency, prediction_confidence)

def extract_data_from_prediction_context(all_core_infos: Dict[str, Any],
                                        meta_model_probas: np.ndarray,
                                        meta_input_data: Dict[str, Any],
                                        final_signal: str,
                                        current_price: float,
                                        global_market_state: Dict[str, Any] = None) -> Tuple[List[BaseModelPrediction], MetaModelAnalysis, MarketSnapshot]:
    """
    从预测上下文中提取数据，转换为日志记录器所需的格式

    Args:
        all_core_infos: 所有基础模型的核心信息
        meta_model_probas: 元模型概率数组 [P_down, P_up]
        meta_input_data: 元模型输入数据
        final_signal: 最终信号
        current_price: 当前价格
        global_market_state: 全局市场状态

    Returns:
        (基础模型预测列表, 元模型分析, 市场快照)
    """
    # 提取基础模型预测
    base_models = []
    for model_name, core_info in all_core_infos.items():
        if isinstance(core_info, dict) and not core_info.get('error', False):
            # 提取概率信息
            p_up = core_info.get('model_positive_class_prob', 0.5)
            p_down = 1.0 - p_up

            # 确定主要概率和决策
            if 'UP' in model_name.upper():
                main_prob = p_up
                decision = "UP" if p_up > 0.5 else "DOWN"
            else:
                main_prob = p_down
                decision = "DOWN" if p_down > 0.5 else "UP"

            # 模拟一致性信息（实际应该从模型元数据中获取）
            consistency = "8/10折一致"  # 默认值，可以根据实际情况调整

            base_models.append(BaseModelPrediction(
                name=model_name,
                probability=main_prob,
                decision=decision,
                consistency=consistency
            ))

    # 提取元模型分析
    p_down, p_up = float(meta_model_probas[0]), float(meta_model_probas[1])

    # 计算市场确定性（基于概率分布的熵）
    entropy = -p_down * np.log2(p_down + 1e-10) - p_up * np.log2(p_up + 1e-10)
    market_certainty = 1.0 - entropy  # 熵越低，确定性越高

    # 提取动态阈值（从配置或计算中获取）
    dynamic_thresholds = {
        'UP': meta_input_data.get('dynamic_up_threshold', 0.5),
        'DOWN': meta_input_data.get('dynamic_down_threshold', 0.5)
    }

    # 计算阈值调整
    threshold_adjustment = market_certainty * 0.02  # 示例计算

    meta_analysis = MetaModelAnalysis(
        raw_probabilities=[p_down, p_up],
        market_certainty=market_certainty,
        dynamic_thresholds=dynamic_thresholds,
        threshold_adjustment=threshold_adjustment
    )

    # 提取市场快照
    if global_market_state is None:
        global_market_state = {}

    # 计算价格变化百分比（示例）
    price_change_pct = global_market_state.get('price_change_pct', 0.0)

    # ATR波动率
    atr_volatility = global_market_state.get('global_atr_percent', 1.5)

    # EMA趋势
    ema_diff_pct = global_market_state.get('global_ema_diff_pct', 0.0)
    if ema_diff_pct > 0.1:
        ema_trend = f"短期上行 (+{ema_diff_pct:.2f}%)"
    elif ema_diff_pct < -0.1:
        ema_trend = f"短期下行 ({ema_diff_pct:.2f}%)"
    else:
        ema_trend = "横盘整理"

    # 成交量状态（示例）
    volume_status = "高于平均 (+23%)"  # 默认值，应该从实际数据中获取

    market_snapshot = MarketSnapshot(
        current_price=current_price,
        price_change_pct=price_change_pct,
        atr_volatility=atr_volatility,
        ema_trend=ema_trend,
        volume_status=volume_status
    )

    return base_models, meta_analysis, market_snapshot

def generate_decision_result_from_signal(final_signal: str, meta_model_probas: np.ndarray,
                                       dynamic_thresholds: Dict[str, float]) -> DecisionResult:
    """
    根据最终信号生成决策结果

    Args:
        final_signal: 最终信号
        meta_model_probas: 元模型概率
        dynamic_thresholds: 动态阈值

    Returns:
        决策结果对象
    """
    p_down, p_up = float(meta_model_probas[0]), float(meta_model_probas[1])

    if "UP" in final_signal:
        trigger_reason = f"上涨概率{p_up:.2%} >= {dynamic_thresholds.get('UP', 0.5):.1%}阈值"
        signal_strength = "强" if p_up > 0.7 else "中等" if p_up > 0.6 else "弱"
        excess = p_up - dynamic_thresholds.get('UP', 0.5)
        trigger_reason += f" (超出阈值{excess:.2%})"
    elif "DOWN" in final_signal:
        trigger_reason = f"下跌概率{p_down:.2%} >= {dynamic_thresholds.get('DOWN', 0.5):.1%}阈值"
        signal_strength = "强" if p_down > 0.7 else "中等" if p_down > 0.6 else "弱"
        excess = p_down - dynamic_thresholds.get('DOWN', 0.5)
        trigger_reason += f" (超出阈值{excess:.2%})"
    else:
        trigger_reason = f"概率不足(上涨{p_up:.2%}<{dynamic_thresholds.get('UP', 0.5):.1%}且下跌{p_down:.2%}<{dynamic_thresholds.get('DOWN', 0.5):.1%})"
        signal_strength = "无"

    # 一票否决权状态（示例）
    veto_status = "通过 (无否决)"  # 默认值，应该从实际检查中获取

    return DecisionResult(
        final_signal=final_signal,
        trigger_reason=trigger_reason,
        signal_strength=signal_strength,
        veto_status=veto_status
    )
