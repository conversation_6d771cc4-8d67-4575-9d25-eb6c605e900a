# 元模型预检查机制

## 概述

为了提高系统效率并减少不必要的资源消耗，我们在元模型预测系统中实现了预检查机制。该机制在开始耗时的预测计算之前，先验证所有必要条件是否满足。

## 预检查项目

### 1. 交易状态管理器检查
- **检查内容**: 验证当前是否有活跃交易阻止新交易
- **检查方法**: 调用 `trade_state_manager.can_start_new_trade()`
- **失败处理**: 如果有活跃交易，跳过整个预测流程
- **返回值**: `(False, "Skipped_Active_Trade")`

### 2. 信号发送冷却期检查
- **检查内容**: 验证距离上次信号发送是否已过冷却期
- **检查方法**: 比较当前时间与上次发送时间
- **冷却时间**: 默认300秒（5分钟），可通过配置调整
- **失败处理**: 如果仍在冷却期，跳过整个预测流程
- **返回值**: `(False, "Skipped_Cooldown")`

### 3. 交易金额有效性检查
- **检查内容**: 预计算交易金额，验证是否为有效值
- **检查方法**: 使用默认概率调用 `calculate_trade_amount_from_strategy()`
- **有效条件**: 计算出的金额必须大于0
- **失败处理**: 如果金额无效，跳过整个预测流程
- **返回值**: `(False, "Skipped_Invalid_Amount")`

## 执行流程

```
元模型预测开始
    ↓
🔍 预检查阶段
    ├── 交易状态检查
    ├── 冷却期检查
    └── 金额有效性检查
    ↓
条件检查结果
    ├── ❌ 任一条件失败 → 跳过预测，返回相应错误码
    └── ✅ 所有条件满足 → 继续执行完整预测流程
```

## 优势

1. **提高效率**: 避免在明知无法发送信号的情况下执行耗时计算
2. **节省资源**: 减少不必要的模型加载、特征工程和预测计算
3. **清晰日志**: 提供明确的跳过原因，便于调试和监控
4. **快速响应**: 在预检查阶段就能快速确定是否需要执行预测

## 日志输出示例

### 预检查通过
```
--- 🔍 预检查阶段：验证预测执行条件 ---
✅ [预检查] 交易状态检查通过
✅ [预检查] 信号冷却检查通过
✅ [预检查] 交易金额检查通过 (预估: $5.00)
🎯 [预检查完成] 所有条件满足，开始执行完整预测流程
```

### 预检查失败（交易状态阻止）
```
--- 🔍 预检查阶段：验证预测执行条件 ---
❌ [预检查失败] 交易状态阻止新交易
    当前状态: active
    交易持续时间: 12.5 分钟
⏭️  跳过整个元模型预测流程
```

### 预检查失败（冷却期）
```
--- 🔍 预检查阶段：验证预测执行条件 ---
✅ [预检查] 交易状态检查通过
❌ [预检查失败] 信号发送处于冷却期
    剩余冷却时间: 180.5秒
⏭️  跳过整个元模型预测流程
```

## 配置参数

- `signal_cooldown_seconds`: 信号发送冷却时间（默认300秒）
- `META_MODEL_STATIC_KELLY_CONFIG`: Kelly策略配置，用于金额计算

## 注意事项

1. 预检查机制不会影响正常的预测流程，只是在开始前进行验证
2. 如果预检查失败，函数会立即返回，不会执行任何模型相关操作
3. 预检查的异常不会阻止预测流程，会记录警告并继续执行
4. 所有预检查结果都会记录在日志中，便于监控和调试
