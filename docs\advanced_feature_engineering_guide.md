# 高级特征工程使用指南

## 概述

高级特征工程系统实现了从"多"到"精"的特征优化策略，通过创建高阶特征、智能交互特征和深化衍生品特征，显著提升模型性能上限。

## 核心理念

### 从"多"到"精"的转变

1. **传统方法**: 大量单一指标特征
2. **高级方法**: 精选高质量组合特征

### 三大核心优化

1. **高阶特征**: 速度、加速度信号
2. **交互特征**: 强强联合的组合指标
3. **聪明钱特征**: 深化衍生品分析

## 功能模块

### 🚀 1. 高阶特征（速度、加速度信号）

#### 核心思想
单一指标的威力有限，但指标的变化趋势往往蕴含更强信号。

#### 实现原理
```python
# 一阶导数（速度）
RSI_velocity = RSI.diff()

# 二阶导数（加速度）  
RSI_acceleration = RSI_velocity.diff()

# 动量强度（距离中性值的加速度）
RSI_momentum_strength = (RSI - 50) * RSI_acceleration
```

#### 支持的指标
- **RSI**: 速度、加速度、动量强度、自适应速度
- **MACD**: 速度、加速度、动量强度
- **MACD_histogram**: 速度、加速度（极佳的动量转折指标）
- **ADX**: 速度、加速度
- **ATR**: 速度、加速度
- **布林带位置**: 速度、加速度
- **Williams %R**: 速度、加速度

#### 实际意义
```python
# 示例：RSI突破信号强度分析
正在加速突破70的RSI > 在71停滞不前的RSI
# RSI=71, velocity=+2.5, acceleration=+0.8 (强看涨)
# RSI=71, velocity=+0.1, acceleration=-0.2 (弱看涨)
```

### 🤝 2. 交互特征（强强联合）

#### 核心思想
指标之间的组合与变化趋势往往蕴含更强的信号。

#### 主要交互类型

##### 波动率-趋势交互
```python
# ADX * ATR: 高ADX配合高ATR意味着趋势启动或高潮
volatility_trend_strength = ADX * ATR
volatility_trend_strength_norm = (ADX/100) * (ATR/ATR_MA50)
```

##### 量价交互
```python
# 价格变化 * 成交量变化: 放量上涨/下跌信号更强
price_volume_interaction = price_change_1p * volume_change_1p
```

##### RSI-MACD动量共振
```python
# RSI偏离中性值程度 * MACD强度
rsi_deviation = abs(RSI - 50) / 50
macd_strength = abs(MACD)
momentum_resonance = rsi_deviation * macd_strength
```

##### 布林带-ATR压缩/扩张
```python
# 布林带极端位置 * ATR相对强度
bb_extremity = abs(BB_position - 0.5) * 2
atr_relative = ATR / ATR_MA20
compression_expansion = bb_extremity * atr_relative
```

##### 成交量-波动率效率
```python
# 单位成交量产生的波动效率
volume_efficiency = ATR / (volume_vs_avg + 1e-8)
```

### 💰 3. 聪明钱特征（深化衍生品分析）

#### 核心思想
大户与散户的行为差异往往预示市场转折。

#### 核心指标

##### 聪明钱背离指标
```python
# 基础背离
smart_money_divergence = top_trader_ratio - global_longshort_ratio

# 背离强度（标准化）
divergence_std = smart_money_divergence.rolling(14).std()
divergence_strength = smart_money_divergence / (divergence_std + 1e-8)

# 背离趋势
divergence_trend = smart_money_divergence.diff()

# 背离持续性
divergence_persistence = 连续同向背离天数 * 背离方向
```

##### 资金费率与价格背离
```python
# 资金费率上升但价格下跌 = 看涨背离
funding_change = funding_rate.diff()
price_change = price_change_1p
funding_price_divergence = funding_change * (-price_change)
```

##### 多空比变化率背离
```python
# 多空比变化率 / 价格变化率
longshort_change_rate = top_trader_ratio.pct_change()
divergence_ratio = longshort_change_rate / (price_change_rate + 1e-8)

# 背离信号强度
divergence_signal = sign(price_change) * sign(-longshort_change)
```

##### 综合聪明钱情绪指数
```python
# 标准化并平均多个聪明钱指标
smart_money_sentiment_index = mean([
    normalized(divergence_strength),
    normalized(funding_price_divergence), 
    normalized(longshort_price_divergence_signal)
])
```

## 使用方法

### 1. 基础配置

```python
# 在目标配置中启用高级特征工程
target_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    
    # 启用高级特征工程
    'enable_advanced_feature_engineering': True,
    
    # 高级特征工程配置
    'advanced_feature_engineering': {
        'enable_higher_order_features': True,
        'enable_interaction_features': True,
        'enable_smart_money_features': True,
        'higher_order_smoothing': 3,
        'interaction_threshold': 0.1,
        'smart_money_lookback': 14
    },
    
    # 确保基础特征可用
    'enable_ta': True,
    'enable_volume': True,
    'enable_derivatives': True  # 聪明钱特征需要
}
```

### 2. 直接使用高级特征工程器

```python
from src.core.advanced_feature_engineering import AdvancedFeatureEngineer

# 创建特征工程器
config = {
    'enable_higher_order_features': True,
    'enable_interaction_features': True,
    'enable_smart_money_features': True,
    'higher_order_smoothing': 3
}

engineer = AdvancedFeatureEngineer(config)

# 应用高级特征工程
enhanced_df = engineer.apply_advanced_feature_engineering(df, 'BTC_5M')

# 获取统计信息
stats = engineer.get_feature_creation_stats()
```

### 3. 集成到现有流程

```python
from src.core.data_utils import add_classification_features

# 在现有特征工程中自动应用
df_with_features = add_classification_features(df, target_config)
# 如果enable_advanced_feature_engineering=True，会自动应用高级特征
```

## 配置参数详解

### 高阶特征配置

```python
higher_order_config = {
    'enable_higher_order_features': True,  # 启用高阶特征
    'higher_order_smoothing': 3,           # 平滑周期（减少噪音）
}
```

### 交互特征配置

```python
interaction_config = {
    'enable_interaction_features': True,   # 启用交互特征
    'interaction_threshold': 0.1,          # 相关性阈值
}
```

### 聪明钱特征配置

```python
smart_money_config = {
    'enable_smart_money_features': True,   # 启用聪明钱特征
    'smart_money_lookback': 14,            # 回看周期
}
```

## 增强的SHAP特征选择

### 新增SHAP阈值过滤

```python
# 在目标配置中启用SHAP特征选择
target_config = {
    'enable_shap_feature_filtering': True,
    'shap_importance_threshold': 0.001,  # SHAP重要性绝对阈值
}

# 使用增强的特征选择
from src.core.data_utils import enhanced_feature_selection_with_shap

selected_features, stats = enhanced_feature_selection_with_shap(
    X_df, y_array, target_config, target_name
)
```

### SHAP过滤流程

1. **第0阶段**: SHAP重要性阈值过滤
   - 训练快速LightGBM模型
   - 计算SHAP重要性
   - 移除平均绝对SHAP值 < 阈值的特征

2. **第1阶段**: LightGBM重要性初筛
3. **第2阶段**: RFE递归特征消除

## 实际效果

### 测试结果示例

```
🎯 高级特征工程效果:
原始特征: 24 个
高阶特征: +26 个 (速度13个, 加速度7个, 动量6个)
交互特征: +10 个 (波动率-趋势2个, 量价2个, 共振1个)
聪明钱特征: +9 个 (背离4个, 资金费率2个, 情绪1个)
总计: 24 → 69 个特征 (+45个)

特征质量:
- RSI速度范围: [-18.22, 18.87]
- MACD加速度范围: [-17.80, 18.51]  
- 波动率-趋势强度: [641.72, 10625.03]
- 聪明钱背离: [-0.277, 0.254]
```

### 与传统方法对比

| 方面 | 传统方法 | 高级方法 | 改进 |
|------|----------|----------|------|
| 特征类型 | 单一指标值 | 多维度组合 | **质量提升** |
| 信号强度 | 静态阈值 | 动态趋势 | **适应性强** |
| 市场洞察 | 技术面 | 技术+资金面 | **全面分析** |
| 噪音处理 | 简单过滤 | 智能平滑 | **稳定性好** |

## 最佳实践

### 1. 特征选择策略

```python
# 保守策略：注重稳定性
conservative_config = {
    'enable_higher_order_features': True,
    'enable_interaction_features': False,  # 减少复杂性
    'enable_smart_money_features': True,
    'higher_order_smoothing': 5,           # 更多平滑
    'shap_importance_threshold': 0.002     # 更严格阈值
}

# 激进策略：追求最大信息
aggressive_config = {
    'enable_higher_order_features': True,
    'enable_interaction_features': True,
    'enable_smart_money_features': True,
    'higher_order_smoothing': 1,           # 最少平滑
    'shap_importance_threshold': 0.0005    # 更宽松阈值
}
```

### 2. 不同市场环境配置

```python
# 高波动市场
high_volatility_config = {
    'higher_order_smoothing': 5,           # 更多平滑减少噪音
    'smart_money_lookback': 7,             # 更短回看期
    'shap_importance_threshold': 0.002     # 更严格过滤
}

# 低波动市场  
low_volatility_config = {
    'higher_order_smoothing': 2,           # 保持敏感性
    'smart_money_lookback': 21,            # 更长回看期
    'shap_importance_threshold': 0.0005    # 保留更多特征
}
```

### 3. 计算资源优化

```python
# 高性能配置
performance_config = {
    'enable_higher_order_features': True,  # 计算成本低
    'enable_interaction_features': False,  # 计算成本高
    'enable_smart_money_features': True,   # 中等成本
    'higher_order_smoothing': 3
}
```

## 监控和调试

### 特征创建统计

```python
# 获取详细统计信息
stats = engineer.get_feature_creation_stats()

print(f"高阶特征: {stats['higher_order']['created_count']} 个")
print(f"交互特征: {stats['interaction']['created_count']} 个") 
print(f"聪明钱特征: {stats['smart_money']['created_count']} 个")
```

### 特征质量检查

```python
# 检查数据质量
nan_count = df.isnull().sum().sum()
inf_count = np.isinf(df.select_dtypes(include=[np.number])).sum().sum()

print(f"数据质量: NaN={nan_count}, Inf={inf_count}")
```

## 故障排除

### 1. 高阶特征计算失败

**问题**: 某些指标的高阶特征未生成
**解决**: 
- 检查基础指标是否存在
- 确认数据长度足够（建议>50）
- 调整平滑参数

### 2. 交互特征过多

**问题**: 生成了过多交互特征
**解决**:
- 提高`interaction_threshold`
- 选择性启用交互类型
- 使用SHAP过滤

### 3. 聪明钱特征缺失

**问题**: 聪明钱特征未生成
**解决**:
- 确保衍生品数据可用
- 检查列名匹配
- 验证数据质量

### 4. 内存使用过高

**问题**: 特征过多导致内存不足
**解决**:
- 使用更严格的SHAP阈值
- 选择性启用特征类型
- 分批处理数据

## 总结

高级特征工程系统提供了：

1. **质量导向**: 从数量到质量的根本转变
2. **智能组合**: 多维度特征交互分析
3. **市场洞察**: 技术面+资金面综合视角
4. **精准过滤**: SHAP基础的科学特征选择
5. **易于集成**: 无缝融入现有工作流程

通过这个系统，可以显著提升模型的性能上限，特别是在复杂市场环境下的适应能力。
