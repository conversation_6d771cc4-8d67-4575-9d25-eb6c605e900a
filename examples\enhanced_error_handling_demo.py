#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 增强错误处理与日志记录演示

展示如何使用新的handle_training_exception装饰器和EnhancedLogger
来实现统一的错误处理和详细的上下文日志记录。
"""

import sys
import os
import logging
import traceback
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.error_handler import handle_training_exception, get_enhanced_logger
from binance.exceptions import BinanceAPIException, BinanceRequestException
import requests.exceptions

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def demo_enhanced_logger():
    """演示增强日志记录器的使用"""
    print("=== 增强日志记录器演示 ===")
    
    enhanced_logger = get_enhanced_logger(__name__)
    
    # 基础日志记录
    enhanced_logger.info("开始处理数据", target_name="BTC_15m", data_size=1000)
    
    # 带多个上下文的日志
    enhanced_logger.warning(
        "数据质量检查发现问题",
        target_name="ETH_5m",
        missing_values=25,
        data_quality_score=0.85,
        action_taken="使用插值填充"
    )
    
    # 错误日志
    enhanced_logger.error(
        "模型训练失败",
        target_name="BNB_1h",
        error_type="ValueError",
        model_type="RandomForest",
        feature_count=150
    )
    
    print("✅ 增强日志记录器演示完成\n")


@handle_training_exception(
    function_name="demo_successful_function",
    fallback_result="success_fallback",
    include_traceback=True
)
def demo_successful_function(target_name="TEST_TARGET"):
    """演示成功执行的函数"""
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        "执行成功的函数",
        target_name=target_name,
        operation="data_processing"
    )
    
    # 模拟一些处理
    result = {"status": "success", "processed_items": 100}
    
    enhanced_logger.info(
        "函数执行完成",
        target_name=target_name,
        result_items=result["processed_items"]
    )
    
    return result


@handle_training_exception(
    function_name="demo_binance_api_error",
    fallback_result={"error": "API_ERROR", "data": []},
    include_traceback=True
)
def demo_binance_api_error(target_name="API_TEST"):
    """演示Binance API异常处理"""
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        "模拟Binance API调用",
        target_name=target_name,
        api_endpoint="get_klines"
    )
    
    # 模拟Binance API异常
    # 创建一个模拟的响应对象
    class MockResponse:
        def __init__(self):
            self.status_code = 400
            self.text = '{"code":-1003,"msg":"IP被禁止访问"}'

    mock_response = MockResponse()
    raise BinanceAPIException(response=mock_response, status_code=400)


@handle_training_exception(
    function_name="demo_network_error",
    fallback_result={"error": "NETWORK_ERROR", "retry_suggested": True},
    include_traceback=True
)
def demo_network_error(target_name="NETWORK_TEST"):
    """演示网络连接异常处理"""
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        "模拟网络请求",
        target_name=target_name,
        url="https://api.binance.com/api/v3/klines"
    )
    
    # 模拟网络连接异常
    raise requests.exceptions.ConnectionError("网络连接失败")


@handle_training_exception(
    function_name="demo_data_processing_error",
    fallback_result={"error": "DATA_PROCESSING_ERROR", "processed": False},
    include_traceback=True
)
def demo_data_processing_error(target_name="DATA_TEST"):
    """演示数据处理异常"""
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        "开始数据处理",
        target_name=target_name,
        input_shape=(1000, 50)
    )
    
    # 模拟数据处理异常
    raise ValueError("特征矩阵包含无效值")


@handle_training_exception(
    function_name="demo_file_not_found_error",
    fallback_result={"error": "FILE_NOT_FOUND", "use_default": True},
    include_traceback=True
)
def demo_file_not_found_error(target_name="FILE_TEST"):
    """演示文件未找到异常"""
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        "尝试加载模型文件",
        target_name=target_name,
        model_path="/path/to/model.pkl"
    )
    
    # 模拟文件未找到异常
    raise FileNotFoundError("模型文件不存在")


def demo_exception_handling():
    """演示各种异常处理场景"""
    print("=== 异常处理演示 ===")
    
    # 1. 成功执行
    print("1. 成功执行演示:")
    result = demo_successful_function("SUCCESS_TARGET")
    print(f"   结果: {result}\n")
    
    # 2. Binance API异常
    print("2. Binance API异常演示:")
    result = demo_binance_api_error("BINANCE_TARGET")
    print(f"   回退结果: {result}\n")
    
    # 3. 网络连接异常
    print("3. 网络连接异常演示:")
    result = demo_network_error("NETWORK_TARGET")
    print(f"   回退结果: {result}\n")
    
    # 4. 数据处理异常
    print("4. 数据处理异常演示:")
    result = demo_data_processing_error("DATA_TARGET")
    print(f"   回退结果: {result}\n")
    
    # 5. 文件未找到异常
    print("5. 文件未找到异常演示:")
    result = demo_file_not_found_error("FILE_TARGET")
    print(f"   回退结果: {result}\n")
    
    print("✅ 异常处理演示完成\n")


def demo_training_pipeline_simulation():
    """模拟训练流水线中的错误处理"""
    print("=== 训练流水线错误处理模拟 ===")
    
    enhanced_logger = get_enhanced_logger(__name__)
    
    targets = ["BTC_15m", "ETH_5m", "BNB_1h", "ADA_30m"]
    
    for target in targets:
        enhanced_logger.info(
            f"开始处理目标 {target}",
            target_name=target,
            pipeline_step="initialization"
        )
        
        try:
            # 模拟数据获取
            if target == "BTC_15m":
                result = demo_successful_function(target)
                enhanced_logger.info(
                    f"目标 {target} 处理成功",
                    target_name=target,
                    result=result
                )
            elif target == "ETH_5m":
                result = demo_binance_api_error(target)
                enhanced_logger.warning(
                    f"目标 {target} 遇到API问题，使用回退方案",
                    target_name=target,
                    fallback_used=True
                )
            elif target == "BNB_1h":
                result = demo_network_error(target)
                enhanced_logger.warning(
                    f"目标 {target} 遇到网络问题，使用回退方案",
                    target_name=target,
                    fallback_used=True
                )
            else:
                result = demo_data_processing_error(target)
                enhanced_logger.error(
                    f"目标 {target} 数据处理失败",
                    target_name=target,
                    critical_error=True
                )
                
        except Exception as e:
            enhanced_logger.error(
                f"目标 {target} 处理过程中发生未捕获异常",
                target_name=target,
                exception_type=type(e).__name__,
                exception_message=str(e)
            )
    
    print("✅ 训练流水线错误处理模拟完成\n")


def main():
    """主函数"""
    print("🚀 增强错误处理与日志记录演示")
    print("=" * 50)
    
    try:
        # 1. 演示增强日志记录器
        demo_enhanced_logger()
        
        # 2. 演示异常处理
        demo_exception_handling()
        
        # 3. 模拟训练流水线
        demo_training_pipeline_simulation()
        
        print("🎉 所有演示完成!")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
