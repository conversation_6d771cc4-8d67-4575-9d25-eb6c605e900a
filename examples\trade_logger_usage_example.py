# trade_logger_usage_example.py
"""
TradeLogger 使用示例
展示如何在主交易逻辑中集成交易日志记录功能
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.trade_logger import TradeLogger, get_trade_logger


class MockTradingSystem:
    """模拟交易系统，展示TradeLogger的集成方式"""
    
    def __init__(self):
        # 初始化交易日志记录器
        self.trade_logger = get_trade_logger("results/logs/example_trade_log.csv")
        
        # 模拟的交易参数
        self.current_btc_price = 50000.0
        self.current_eth_price = 3000.0
        
        print("模拟交易系统初始化完成")
    
    def simulate_prediction_signal(self, target_name: str, symbol: str, direction: str, confidence: float):
        """
        模拟预测系统发出交易信号
        
        Args:
            target_name: 策略名称
            symbol: 交易对
            direction: 交易方向
            confidence: 信号置信度
        """
        print(f"\n=== 收到预测信号 ===")
        print(f"策略: {target_name}")
        print(f"交易对: {symbol}")
        print(f"方向: {direction}")
        print(f"置信度: {confidence:.2f}")
        
        # 根据置信度计算交易金额
        base_amount = 10.0
        amount = base_amount * confidence
        
        # 获取当前价格
        if symbol == "BTCUSDT":
            current_price = self.current_btc_price
        elif symbol == "ETHUSDT":
            current_price = self.current_eth_price
        else:
            current_price = 1000.0  # 默认价格
        
        # 记录开仓
        trade_id = self.trade_logger.record_trade_entry(
            target_name=target_name,
            symbol=symbol,
            direction=direction,
            entry_price=current_price,
            amount=amount,
            payout_ratio=0.85
        )
        
        print(f"开仓记录完成，交易ID: {trade_id}")
        
        # 模拟交易执行延迟
        time.sleep(1)
        
        # 模拟交易结果（这里简化为随机结果）
        import random
        is_win = random.random() < 0.6  # 60% 胜率
        
        # 模拟价格变动
        price_change_percent = random.uniform(-0.05, 0.05)  # ±5%
        exit_price = current_price * (1 + price_change_percent)
        
        # 根据方向和价格变动确定结果
        if direction.upper() in ['LONG', 'UP']:
            actual_win = exit_price > current_price
        else:  # SHORT/DOWN
            actual_win = exit_price < current_price
        
        # 记录平仓
        result = "WIN" if actual_win else "LOSS"
        success = self.trade_logger.record_trade_exit(
            trade_id=trade_id,
            exit_price=exit_price,
            result=result,
            exit_reason="expired"
        )
        
        if success:
            print(f"平仓记录完成: {result}, 价格变动: {current_price:.2f} -> {exit_price:.2f}")
        else:
            print("平仓记录失败")
        
        return trade_id, result


def example_integration_with_prediction_system():
    """示例：与预测系统集成"""
    print("=== 与预测系统集成示例 ===")
    
    trading_system = MockTradingSystem()
    
    # 模拟多个预测信号
    signals = [
        ("BTC_15m_UP", "BTCUSDT", "LONG", 0.8),
        ("BTC_15m_DOWN", "BTCUSDT", "SHORT", 0.7),
        ("MetaModel", "ETHUSDT", "LONG", 0.9),
        ("ETH_5m_DOWN", "ETHUSDT", "SHORT", 0.6),
    ]
    
    for target_name, symbol, direction, confidence in signals:
        trading_system.simulate_prediction_signal(target_name, symbol, direction, confidence)
        time.sleep(0.5)  # 模拟信号间隔
    
    print(f"\n待平仓交易数量: {trading_system.trade_logger.get_pending_trades_count()}")


def example_integration_with_simtrading():
    """示例：与SimTrading.py集成"""
    print("\n=== 与SimTrading集成示例 ===")
    
    # 这个示例展示如何在现有的SimTrading.py中集成TradeLogger
    
    trade_logger = get_trade_logger("results/logs/sim_trade_log.csv")
    
    # 模拟SimTrading.py中的Trade类开仓过程
    class MockSimTrade:
        def __init__(self, direction, entry_price, amount_staked):
            self.trade_id = int(time.time() * 1000)
            self.direction = direction.upper()
            self.entry_price = float(entry_price)
            self.amount_staked = float(amount_staked)
            self.entry_time = datetime.now()
            
            # 集成TradeLogger - 记录开仓
            self.logger_trade_id = trade_logger.record_trade_entry(
                target_name="SimTrading",  # 或者从外部传入
                symbol="BTCUSDT",  # 或者从外部传入
                direction=self.direction,
                entry_price=self.entry_price,
                amount=self.amount_staked,
                payout_ratio=0.85
            )
            
            print(f"SimTrade开仓: {self.trade_id}, Logger ID: {self.logger_trade_id}")
        
        def settle_trade(self, exit_price, is_win):
            """模拟交易结算"""
            result = "WIN" if is_win else "LOSS"
            
            # 集成TradeLogger - 记录平仓
            success = trade_logger.record_trade_exit(
                trade_id=self.logger_trade_id,
                exit_price=exit_price,
                result=result,
                exit_reason="expired"
            )
            
            if success:
                print(f"SimTrade平仓: {self.trade_id}, 结果: {result}")
            else:
                print(f"SimTrade平仓记录失败: {self.trade_id}")
    
    # 模拟几笔交易
    trades = []
    
    # 开仓
    trade1 = MockSimTrade("UP", 50000.0, 10.0)
    trade2 = MockSimTrade("DOWN", 3000.0, 15.0)
    trades.extend([trade1, trade2])
    
    time.sleep(1)
    
    # 平仓
    trade1.settle_trade(51000.0, True)   # 盈利
    trade2.settle_trade(2950.0, True)    # 盈利
    
    print(f"剩余待平仓交易: {trade_logger.get_pending_trades_count()}")


def example_integration_with_main_prediction():
    """示例：与main.py预测系统集成"""
    print("\n=== 与main.py预测系统集成示例 ===")
    
    # 这个示例展示如何在main.py的预测逻辑中集成TradeLogger
    
    trade_logger = get_trade_logger("results/logs/main_prediction_log.csv")
    
    def mock_run_prediction_cycle_for_target(target_config, binance_client=None):
        """模拟prediction.py中的预测函数"""
        target_name = target_config.get('name', 'unknown_target')
        symbol = target_config.get('symbol', 'BTCUSDT')
        
        print(f"执行预测: {target_name}")
        
        # 模拟预测结果
        import random
        should_trade = random.random() > 0.5
        
        if should_trade:
            direction = random.choice(['UP', 'DOWN'])
            confidence = random.uniform(0.6, 0.9)
            amount = 10.0 * confidence
            current_price = 50000.0 if symbol == 'BTCUSDT' else 3000.0
            
            # 记录开仓（在发送信号给模拟盘之前）
            trade_id = trade_logger.record_trade_entry(
                target_name=target_name,
                symbol=symbol,
                direction=direction,
                entry_price=current_price,
                amount=amount,
                payout_ratio=0.85
            )
            
            print(f"预测信号: {direction}, 金额: {amount:.2f}, 交易ID: {trade_id}")
            
            # 这里通常会发送信号给模拟盘
            # _notify_simulator(signal_type=direction, amount=amount, ...)
            
            # 模拟一段时间后收到交易结果（实际中这会通过回调或轮询获得）
            time.sleep(1)
            
            # 模拟交易结果
            exit_price = current_price * random.uniform(0.95, 1.05)
            is_win = random.random() > 0.4
            result = "WIN" if is_win else "LOSS"
            
            # 记录平仓
            trade_logger.record_trade_exit(
                trade_id=trade_id,
                exit_price=exit_price,
                result=result,
                exit_reason="expired"
            )
            
            print(f"交易结果: {result}")
        else:
            print("无交易信号")
    
    # 模拟多个目标的预测
    target_configs = [
        {'name': 'BTC_15m_UP', 'symbol': 'BTCUSDT'},
        {'name': 'BTC_15m_DOWN', 'symbol': 'BTCUSDT'},
        {'name': 'MetaModel', 'symbol': 'BTCUSDT'},
    ]
    
    for config in target_configs:
        mock_run_prediction_cycle_for_target(config)
        time.sleep(0.5)


def show_csv_content(file_path: str):
    """显示CSV文件内容"""
    if os.path.exists(file_path):
        print(f"\n=== {file_path} 内容 ===")
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if content.strip():
                print(content)
            else:
                print("文件为空")
    else:
        print(f"文件不存在: {file_path}")


if __name__ == "__main__":
    print("TradeLogger 使用示例")
    print("=" * 50)
    
    # 确保结果目录存在
    os.makedirs("results/logs", exist_ok=True)
    
    # 运行各种集成示例
    example_integration_with_prediction_system()
    example_integration_with_simtrading()
    example_integration_with_main_prediction()
    
    # 显示生成的日志文件内容
    log_files = [
        "results/logs/example_trade_log.csv",
        "results/logs/sim_trade_log.csv", 
        "results/logs/main_prediction_log.csv"
    ]
    
    for log_file in log_files:
        show_csv_content(log_file)
    
    print("\n示例运行完成！")
