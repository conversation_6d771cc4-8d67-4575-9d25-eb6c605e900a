#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 统一综合日志系统Web仪表板
基于Flask的实时监控和分析Web界面

核心功能：
- 实时交易监控
- 性能分析图表
- 失败案例分析
- 系统配置管理
- 告警管理
"""

import os
import sys
import json
import threading
from datetime import datetime, timedelta
from pathlib import Path
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_socketio import SocketIO, emit
import plotly.graph_objs as go
import plotly.utils

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.core.unified_comprehensive_logger import get_unified_comprehensive_logger
from src.core.comprehensive_log_analyzer import ComprehensiveLogAnalyzer
from src.core.realtime_log_monitor import get_realtime_monitor
from src.core.comprehensive_logger_config import get_config_manager


class DashboardApp:
    """Web仪表板应用"""
    
    def __init__(self, host='127.0.0.1', port=5000, debug=False):
        self.host = host
        self.port = port
        self.debug = debug
        
        # 初始化Flask应用
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'comprehensive_logger_dashboard_2025'
        
        # 初始化SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 初始化核心组件
        self.logger = get_unified_comprehensive_logger()
        self.analyzer = ComprehensiveLogAnalyzer()
        self.monitor = get_realtime_monitor()
        self.config_manager = get_config_manager()
        
        # 设置路由
        self._setup_routes()
        self._setup_socketio_events()
        
        # 启动实时监控
        self.monitor.start_monitoring()
        
        # 添加告警回调
        self.monitor.add_alert_callback(self._handle_alert)
        
        print(f"🌐 Web仪表板初始化完成: http://{host}:{port}")
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/overview')
        def api_overview():
            """获取概览数据"""
            try:
                # 获取基础统计
                stats = self.logger.get_statistics()
                
                # 获取监控状态
                monitor_status = self.monitor.get_monitoring_status()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'basic_stats': stats.get('basic_stats', {}),
                        'monitoring_data': monitor_status.get('monitoring_data', {}),
                        'is_monitoring': monitor_status.get('is_monitoring', False)
                    }
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/performance/<int:days>')
        def api_performance(days):
            """获取性能分析数据"""
            try:
                dashboard_data = self.analyzer.generate_performance_dashboard(days)
                return jsonify({
                    'success': True,
                    'data': dashboard_data
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/trades')
        def api_trades():
            """获取交易数据"""
            try:
                days = request.args.get('days', 7, type=int)
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                end_date = datetime.now().strftime('%Y-%m-%d')
                
                trades_df = self.logger.load_trade_logs(start_date, end_date)
                
                if trades_df is not None and not trades_df.empty:
                    # 转换为JSON格式
                    trades_data = trades_df.to_dict('records')
                    return jsonify({
                        'success': True,
                        'data': trades_data,
                        'total': len(trades_data)
                    })
                else:
                    return jsonify({
                        'success': True,
                        'data': [],
                        'total': 0
                    })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/failure_analysis/<int:days>')
        def api_failure_analysis(days):
            """获取失败案例分析"""
            try:
                analysis_result = self.logger.analyze_failures(days)
                return jsonify({
                    'success': True,
                    'data': analysis_result
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/config')
        def api_get_config():
            """获取系统配置"""
            try:
                config = self.config_manager.get_config()
                from dataclasses import asdict
                config_dict = asdict(config)
                return jsonify({
                    'success': True,
                    'data': config_dict
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/config', methods=['POST'])
        def api_update_config():
            """更新系统配置"""
            try:
                config_updates = request.json
                success = self.config_manager.update_config(**config_updates)
                
                return jsonify({
                    'success': success,
                    'message': '配置更新成功' if success else '配置更新失败'
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/monitoring/start', methods=['POST'])
        def api_start_monitoring():
            """启动监控"""
            try:
                self.monitor.start_monitoring()
                return jsonify({
                    'success': True,
                    'message': '监控已启动'
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/monitoring/stop', methods=['POST'])
        def api_stop_monitoring():
            """停止监控"""
            try:
                self.monitor.stop_monitoring()
                return jsonify({
                    'success': True,
                    'message': '监控已停止'
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/charts/pnl_chart/<int:days>')
        def api_pnl_chart(days):
            """获取盈亏图表数据"""
            try:
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                end_date = datetime.now().strftime('%Y-%m-%d')
                
                trades_df = self.logger.load_trade_logs(start_date, end_date)
                
                if trades_df is None or trades_df.empty:
                    return jsonify({'success': False, 'error': '没有数据'})
                
                # 创建累积盈亏图表
                trades_df['entry_timestamp'] = pd.to_datetime(trades_df['entry_timestamp'])
                trades_sorted = trades_df.sort_values('entry_timestamp')
                cumulative_pnl = trades_sorted['profit_loss'].cumsum()
                
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=trades_sorted['entry_timestamp'],
                    y=cumulative_pnl,
                    mode='lines',
                    name='累积盈亏',
                    line=dict(color='blue', width=2)
                ))
                
                fig.update_layout(
                    title='累积盈亏曲线',
                    xaxis_title='时间',
                    yaxis_title='累积盈亏',
                    hovermode='x unified'
                )
                
                return jsonify({
                    'success': True,
                    'data': json.loads(plotly.utils.PlotlyJSONEncoder().encode(fig))
                })
                
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            """静态文件服务"""
            return send_from_directory(self.app.static_folder, filename)
    
    def _setup_socketio_events(self):
        """设置SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            print(f"客户端连接: {request.sid}")
            emit('status', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            print(f"客户端断开: {request.sid}")
        
        @self.socketio.on('request_update')
        def handle_request_update():
            """客户端请求更新数据"""
            try:
                # 发送最新的概览数据
                stats = self.logger.get_statistics()
                monitor_status = self.monitor.get_monitoring_status()
                
                emit('data_update', {
                    'type': 'overview',
                    'data': {
                        'basic_stats': stats.get('basic_stats', {}),
                        'monitoring_data': monitor_status.get('monitoring_data', {}),
                        'timestamp': datetime.now().isoformat()
                    }
                })
            except Exception as e:
                emit('error', {'message': str(e)})
    
    def _handle_alert(self, alert):
        """处理告警"""
        try:
            # 通过WebSocket发送告警到所有连接的客户端
            self.socketio.emit('alert', {
                'type': alert['type'],
                'severity': alert['severity'],
                'message': alert['message'],
                'value': alert['value'],
                'threshold': alert['threshold'],
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            print(f"发送告警到Web客户端失败: {e}")
    
    def run(self):
        """运行Web应用"""
        print(f"🚀 启动Web仪表板: http://{self.host}:{self.port}")
        self.socketio.run(
            self.app,
            host=self.host,
            port=self.port,
            debug=self.debug,
            allow_unsafe_werkzeug=True
        )
    
    def stop(self):
        """停止应用"""
        self.monitor.stop_monitoring()
        print("🛑 Web仪表板已停止")


def create_app():
    """创建Flask应用实例"""
    return DashboardApp()


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='统一综合日志系统Web仪表板')
    parser.add_argument('--host', default='127.0.0.1', help='主机地址')
    parser.add_argument('--port', type=int, default=5000, help='端口号')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    try:
        app = DashboardApp(host=args.host, port=args.port, debug=args.debug)
        app.run()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断，正在停止...")
        if 'app' in locals():
            app.stop()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
