# safe_fill_nans 数据泄露修复文档

## 问题描述

原始的 `safe_fill_nans` 函数存在数据泄露问题：

```python
# 原始代码（有问题）
hist_value = series.dropna().mean()  # 使用了整个序列的均值，包括未来数据
```

在时间序列预测中，这会导致严重的数据泄露，因为：
1. 计算均值时使用了未来的数据点
2. 用这个包含未来信息的均值填充历史时刻的NaN值
3. 模型在训练时实际上"看到"了未来的信息

## 修复方案

### 1. 新增参数控制

```python
def safe_fill_nans(series, default_value=0, use_historical_only=True):
```

- `use_historical_only=True`: 严格历史数据模式，避免数据泄露
- `use_historical_only=False`: 传统模式，保持向后兼容

### 2. 历史数据模式实现

```python
if use_historical_only:
    # 逐个位置处理，只使用当前位置之前的数据
    for i in range(len(filled)):
        if pd.isna(filled.iloc[i]):
            # 只使用当前位置之前的历史数据
            historical_data = filled.iloc[:i]
            
            if len(historical_data) > 0 and not historical_data.isna().all():
                # 使用历史数据的最后一个有效值（向前填充）
                last_valid = historical_data.dropna()
                if len(last_valid) > 0:
                    hist_value = last_valid.iloc[-1]
                else:
                    hist_value = default_value
            else:
                # 没有历史数据，使用默认值
                hist_value = default_value
```

### 3. 填充策略

**历史数据模式 (`use_historical_only=True`)**:
- 序列开头的NaN: 使用 `default_value`
- 中间的NaN: 使用最近的历史有效值（向前填充）
- 严格保证不使用未来数据

**传统模式 (`use_historical_only=False`)**:
- 先进行向前填充
- 剩余NaN使用全局均值填充（可能包含未来数据）

## 使用场景

### 训练和预测场景（推荐使用历史模式）

```python
# 特征工程中
df_out[col] = safe_fill_nans(df_out[col], default_val, use_historical_only=True)

# 模型训练中
X_clean[col] = safe_fill_nans(X_clean[col], default_value=0, use_historical_only=True)

# OOF预测中
oof_series = safe_fill_nans(oof_series, default_value=0.5, use_historical_only=True)
```

### 最终清理场景（可以使用传统模式）

```python
# 最终数据清理，特征工程完成后
df_out[col] = safe_fill_nans(df_out[col], default_value=intelligent_default, use_historical_only=False)
```

## 性能影响

- **历史模式**: 约20x慢于传统模式（因为需要逐个位置处理）
- **传统模式**: 保持原有性能
- **建议**: 在训练/预测时使用历史模式，在最终清理时可考虑传统模式

## 测试验证

修复后的函数通过了以下测试：

1. **数据泄露测试**: 验证历史模式不使用未来数据
2. **边界情况测试**: 全NaN、开头NaN、无NaN序列
3. **时间序列测试**: 模拟真实股价数据场景
4. **性能测试**: 确保两种模式都能正确填充

## 修复影响

### 已更新的调用位置

1. `src/core/data_utils.py`:
   - 数据获取时的NaN填充
   - 技术指标计算中的NaN填充
   - K线特征平滑处理
   - 分类特征处理

2. `main.py`:
   - OOF预测数据清理
   - 训练数据预处理
   - 验证数据清理

### 向后兼容性

- 默认使用历史模式 (`use_historical_only=True`)
- 可通过参数切换到传统模式
- 所有现有调用都已更新为明确指定模式

## 总结

这个修复彻底解决了 `safe_fill_nans` 函数的数据泄露问题，确保在时间序列预测中：

1. ✅ 严格遵循时间顺序，不使用未来数据
2. ✅ 保持向后兼容性
3. ✅ 提供灵活的使用选项
4. ✅ 通过全面测试验证

**建议**: 在所有训练和预测相关的代码中使用 `use_historical_only=True`，只在最终数据清理时考虑使用传统模式。
