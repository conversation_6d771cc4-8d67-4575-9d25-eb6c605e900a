# 统一特征工程接口使用指南

## 概述

统一特征工程接口为基础模型和元模型的特征工程提供了标准化的接口，确保特征名称和顺序一致，并增加了数据验证步骤来处理NaN/Inf值。

## 主要功能

### 1. 统一的特征工程接口
- **BaseFeatureEngineer**: 抽象基类，定义标准接口
- **BasicModelFeatureEngineer**: 基础模型特征工程器
- **MetaModelFeatureEngineer**: 元模型特征工程器

### 2. 数据验证和清理
- 自动检测和处理NaN值
- 自动检测和处理Inf值
- 数据类型验证
- 特征名称标准化
- 异常值裁剪

### 3. 特征一致性保证
- 特征名称标准化
- 特征顺序保证
- 缺失特征自动填充
- 多余特征自动移除

## 快速开始

### 基础模型特征工程

```python
from src.core.unified_feature_engineering import generate_basic_features
import pandas as pd

# 准备K线数据
kline_data = pd.DataFrame({
    'open': [100, 101, 102],
    'high': [105, 106, 107],
    'low': [99, 100, 101],
    'close': [104, 105, 106],
    'volume': [1000, 1100, 1200]
})

# 目标配置
target_config = {
    'model_type': 'up',
    'prediction_horizon': 1
}

# 生成特征
features = generate_basic_features(
    data=kline_data,
    target_config=target_config
)

print(f"生成特征形状: {features.shape}")
print(f"特征列名: {list(features.columns)}")
```

### 元模型特征工程

```python
from src.core.unified_feature_engineering import generate_meta_features
import pandas as pd

# 准备基础模型预测数据
meta_data = pd.DataFrame({
    'oof_proba_BTC_15m_UP_pfavorable': [0.6, 0.7, 0.5],
    'oof_proba_BTC_15m_DOWN_pfavorable': [0.4, 0.3, 0.5]
})

# 基础模型列表
base_models = ['BTC_15m_UP', 'BTC_15m_DOWN']

# 生成元模型特征
meta_features = generate_meta_features(
    data=meta_data,
    base_models_for_meta=base_models
)

print(f"元模型特征形状: {meta_features.shape}")
print(f"特征列名: {list(meta_features.columns)}")
```

## 高级用法

### 使用工厂模式

```python
from src.core.unified_feature_engineering import FeatureEngineeringFactory

# 创建工厂
factory = FeatureEngineeringFactory()

# 获取默认配置
basic_config = factory.get_default_feature_config('basic')
meta_config = factory.get_default_feature_config('meta')

# 自定义配置
basic_config['nan_fill_strategy'] = 'mean'
meta_config['enable_lag_features'] = False

# 创建特征工程器
basic_engineer = factory.create_basic_feature_engineer(
    config=None, 
    feature_config=basic_config
)

meta_engineer = factory.create_meta_feature_engineer(
    config=None,
    feature_config=meta_config
)

# 使用特征工程器
features = basic_engineer.generate_features(kline_data, target_config)
meta_features = meta_engineer.generate_features(meta_data, None, base_models)
```

### 创建完整管道

```python
from src.core.unified_feature_engineering import create_feature_engineering_pipeline
import config

# 创建完整的特征工程管道
pipeline = create_feature_engineering_pipeline(config)

# 使用管道
basic_features = pipeline['basic'].generate_features(kline_data, target_config)
meta_features = pipeline['meta'].generate_features(meta_data, None, base_models)
```

### 数据验证和清理

```python
from src.core.unified_feature_engineering import BaseFeatureEngineer
import numpy as np

# 创建包含问题的数据
problematic_data = pd.DataFrame({
    'feature1': [1.0, np.nan, 3.0],
    'feature2': [4.0, np.inf, 6.0],
    'feature3': [7.0, 8.0, -np.inf]
})

# 创建特征工程器
engineer = BasicModelFeatureEngineer()

# 验证和清理数据
cleaned_data, validation_result = engineer.validate_and_clean_data(problematic_data)

print(f"验证结果: {validation_result.summary()}")
print(f"清理后数据:\n{cleaned_data}")
```

### 特征一致性检查

```python
from src.core.unified_feature_engineering import validate_feature_consistency

# 比较两个特征DataFrame
df1 = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})
df2 = pd.DataFrame({'a': [1, 2], 'b': [3, 4], 'c': [5, 6]})

is_consistent, differences = validate_feature_consistency(df1, df2)
print(f"一致性: {is_consistent}")
print(f"差异: {differences}")
```

## 配置选项

### 基础模型特征配置

```python
BASIC_MODEL_FEATURE_CONFIG = {
    'enable_price_features': True,        # 价格变化特征
    'enable_volume_features': True,       # 成交量特征
    'enable_technical_indicators': True,  # 技术指标
    'nan_fill_strategy': 'median',        # NaN填充策略
    'inf_fill_strategy': 'clip',          # Inf处理策略
    'enable_outlier_clipping': True,      # 异常值裁剪
    'outlier_clip_quantile': 0.99         # 裁剪分位数
}
```

### 元模型特征配置

```python
UNIFIED_META_MODEL_FEATURE_CONFIG = {
    'enable_prob_diff': True,             # 概率差异特征
    'enable_prob_sum': True,              # 概率总和特征
    'enable_lag_features': True,          # 滞后特征
    'enable_change_features': True,       # 变化特征
    'lag_periods': [1],                   # 滞后期数
    'change_periods': [1],                # 变化期数
    'nan_fill_strategy': 'median',        # NaN填充策略
    'inf_fill_strategy': 'clip'           # Inf处理策略
}
```

## 最佳实践

### 1. 特征一致性
- 始终使用相同的特征工程器配置进行训练和预测
- 保存特征名称列表以确保顺序一致
- 定期验证特征一致性

### 2. 数据质量
- 启用数据验证功能
- 选择合适的NaN/Inf处理策略
- 监控特征统计信息的变化

### 3. 性能优化
- 启用特征缓存（如果可用）
- 考虑并行处理（实验性功能）
- 定期清理不需要的特征

### 4. 错误处理
- 捕获FeatureEngineeringException异常
- 检查验证结果的错误和警告
- 记录特征工程过程的日志

## 迁移指南

### 从现有代码迁移

1. **替换基础特征生成**：
```python
# 旧方式
features = data_utils.add_classification_features(data, target_config)

# 新方式
features = generate_basic_features(data, target_config=target_config)
```

2. **替换元模型特征生成**：
```python
# 旧方式
enhanced = add_meta_feature_engineering(X_meta_df, base_models, global_features)

# 新方式
enhanced = generate_meta_features(X_meta_df, base_models_for_meta=base_models, 
                                 precomputed_global_features=global_features)
```

3. **添加数据验证**：
```python
# 在特征生成后添加验证
features, validation_result = engineer.validate_and_clean_data(features)
if not validation_result.is_valid:
    logger.error(f"特征验证失败: {validation_result.errors}")
```

## 故障排除

### 常见问题

1. **ImportError**: 确保所有依赖模块已正确导入
2. **FeatureEngineeringException**: 检查输入数据格式和配置
3. **特征不一致**: 使用相同的配置和特征名称列表
4. **性能问题**: 考虑禁用某些特征类型或启用缓存

### 调试技巧

1. 启用详细日志记录
2. 检查验证结果的警告信息
3. 比较新旧特征工程的输出
4. 使用特征一致性验证函数

这个统一的特征工程接口提供了更好的代码组织、数据质量保证和特征一致性，是构建可靠机器学习系统的重要基础。
