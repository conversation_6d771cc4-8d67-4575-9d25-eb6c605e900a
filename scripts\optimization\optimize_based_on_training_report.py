#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于训练报告的自动优化工具
分析训练报告并自动生成优化建议和配置调整

使用场景:
1. 每次模型训练后的自动化分析
2. 性能问题的快速诊断
3. 参数优化建议的生成
4. 训练结果的系统化评估

功能:
- 自动解析训练日志和报告
- 识别性能瓶颈和问题
- 生成具体的优化建议
- 自动调整配置参数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import config
import pandas as pd
import numpy as np
import json
import re
from pathlib import Path
from datetime import datetime

class TrainingReportAnalyzer:
    """训练报告分析器"""
    
    def __init__(self):
        self.report_data = {}
        self.optimization_suggestions = []
        
    def parse_training_log(self, log_content):
        """解析训练日志内容"""
        print("📊 解析训练日志...")
        
        # 提取关键指标
        patterns = {
            'accuracy': r'验证集准确率.*?(\d+\.\d+)',
            'f1_scores': r'f1-score\s+(\d+\.\d+)',
            'precision': r'precision\s+(\d+\.\d+)',
            'recall': r'recall\s+(\d+\.\d+)',
            'win_rate': r'胜率[：:]\s*(\d+\.\d+)%',
            'trade_count': r'交易次数[：:]\s*(\d+)',
            'optuna_best': r'Optuna最佳值[：:]\s*([-\d\.]+)',
            'class_distribution': r'Class_(\d+).*?(\d+)'
        }
        
        for key, pattern in patterns.items():
            matches = re.findall(pattern, log_content)
            if matches:
                if key == 'class_distribution':
                    # 处理类别分布
                    class_dist = {}
                    for match in matches:
                        class_num, count = match
                        class_dist[f'class_{class_num}'] = int(count)
                    self.report_data[key] = class_dist
                elif key in ['f1_scores', 'precision', 'recall']:
                    # 处理多个值
                    self.report_data[key] = [float(m) for m in matches]
                else:
                    # 处理单个值
                    try:
                        self.report_data[key] = float(matches[0])
                    except:
                        self.report_data[key] = matches[0]
        
        print(f"✅ 解析完成，提取了 {len(self.report_data)} 个关键指标")
        return self.report_data
    
    def analyze_performance_issues(self):
        """分析性能问题"""
        print("\n🔍 分析性能问题...")
        
        issues = []
        
        # 检查胜率问题
        win_rate = self.report_data.get('win_rate', 0)
        if win_rate < 30:
            issues.append({
                'type': 'critical',
                'issue': f'胜率过低 ({win_rate}%)',
                'suggestion': '检查标签生成逻辑和基础模型质量',
                'priority': 1
            })
        elif win_rate < 45:
            issues.append({
                'type': 'warning',
                'issue': f'胜率偏低 ({win_rate}%)',
                'suggestion': '调整类别权重和阈值设置',
                'priority': 2
            })
        
        # 检查类别不平衡问题
        class_dist = self.report_data.get('class_distribution', {})
        if class_dist:
            total_samples = sum(class_dist.values())
            neutral_ratio = class_dist.get('class_2', 0) / total_samples
            
            if neutral_ratio > 0.8:
                issues.append({
                    'type': 'critical',
                    'issue': f'严重的类别不平衡 (中性类别占{neutral_ratio:.1%})',
                    'suggestion': '使用更激进的类别权重设置',
                    'priority': 1
                })
        
        # 检查F1分数问题
        f1_scores = self.report_data.get('f1_scores', [])
        if len(f1_scores) >= 3:
            if f1_scores[0] < 0.3 or f1_scores[1] < 0.3:  # Class_0和Class_1的F1分数
                issues.append({
                    'type': 'warning',
                    'issue': '交易类别F1分数过低',
                    'suggestion': '增加类别权重，调整特征工程',
                    'priority': 2
                })
        
        # 检查Optuna优化问题
        optuna_best = self.report_data.get('optuna_best', 0)
        if optuna_best <= -100:
            issues.append({
                'type': 'critical',
                'issue': 'Optuna优化失败',
                'suggestion': '检查优化目标函数和参数范围',
                'priority': 1
            })
        
        self.optimization_suggestions.extend(issues)
        
        print(f"✅ 发现 {len(issues)} 个性能问题")
        return issues
    
    def generate_config_adjustments(self):
        """生成配置调整建议"""
        print("\n⚙️ 生成配置调整建议...")
        
        adjustments = {}
        
        # 基于胜率调整类别权重
        win_rate = self.report_data.get('win_rate', 0)
        if win_rate < 30:
            adjustments['META_MODEL_LGBM_CLASS_WEIGHT'] = {0: 5.0, 1: 5.0, 2: 0.2}
            adjustments['_reason_class_weight'] = '胜率过低，使用极激进的类别权重'
        elif win_rate < 45:
            adjustments['META_MODEL_LGBM_CLASS_WEIGHT'] = {0: 3.0, 1: 3.0, 2: 0.3}
            adjustments['_reason_class_weight'] = '胜率偏低，使用激进的类别权重'
        
        # 基于交易次数调整阈值
        trade_count = self.report_data.get('trade_count', 0)
        if trade_count < 100:
            adjustments['META_SIGNAL_UP_THRESHOLD'] = 0.48
            adjustments['META_SIGNAL_DOWN_THRESHOLD'] = 0.48
            adjustments['_reason_threshold'] = '交易次数过少，降低阈值'
        elif trade_count > 1000:
            adjustments['META_SIGNAL_UP_THRESHOLD'] = 0.65
            adjustments['META_SIGNAL_DOWN_THRESHOLD'] = 0.65
            adjustments['_reason_threshold'] = '交易次数过多，提高阈值'
        
        # 基于类别分布调整过滤参数
        class_dist = self.report_data.get('class_distribution', {})
        if class_dist:
            total_samples = sum(class_dist.values())
            neutral_ratio = class_dist.get('class_2', 0) / total_samples
            
            if neutral_ratio > 0.85:
                adjustments['META_SIGNAL_CONSENSUS_THRESHOLD'] = 0.4
                adjustments['META_SIGNAL_MIN_PROBABILITY'] = 0.5
                adjustments['_reason_filter'] = '中性类别过多，放宽过滤条件'
        
        print(f"✅ 生成了 {len([k for k in adjustments.keys() if not k.startswith('_')])} 个配置调整")
        return adjustments
    
    def save_optimization_report(self, output_path="optimization_report.json"):
        """保存优化报告"""
        print(f"\n💾 保存优化报告到 {output_path}...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'training_metrics': self.report_data,
            'performance_issues': self.optimization_suggestions,
            'config_adjustments': self.generate_config_adjustments(),
            'summary': {
                'total_issues': len(self.optimization_suggestions),
                'critical_issues': len([i for i in self.optimization_suggestions if i['type'] == 'critical']),
                'win_rate': self.report_data.get('win_rate', 0),
                'trade_count': self.report_data.get('trade_count', 0)
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("✅ 优化报告已保存")
        return report

def analyze_latest_training():
    """分析最新的训练结果"""
    print("🎯 基于训练报告的自动优化分析")
    print("=" * 60)
    
    # 查找最新的训练日志
    log_files = [
        "logs/training.log",
        "logs/main.log",
        "meta_model_data/training_log.txt"
    ]
    
    latest_log = None
    for log_file in log_files:
        if os.path.exists(log_file):
            latest_log = log_file
            break
    
    if not latest_log:
        print("❌ 未找到训练日志文件")
        return None
    
    print(f"📄 分析日志文件: {latest_log}")
    
    # 读取日志内容
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            log_content = f.read()
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")
        return None
    
    # 创建分析器并分析
    analyzer = TrainingReportAnalyzer()
    analyzer.parse_training_log(log_content)
    analyzer.analyze_performance_issues()
    
    # 生成报告
    report = analyzer.save_optimization_report("scripts/optimization/latest_optimization_report.json")
    
    # 显示摘要
    print("\n" + "=" * 60)
    print("📋 优化分析摘要:")
    print(f"   胜率: {report['summary']['win_rate']}%")
    print(f"   交易次数: {report['summary']['trade_count']}")
    print(f"   发现问题: {report['summary']['total_issues']} 个")
    print(f"   严重问题: {report['summary']['critical_issues']} 个")
    
    # 显示主要建议
    if analyzer.optimization_suggestions:
        print("\n🚀 主要优化建议:")
        for i, suggestion in enumerate(analyzer.optimization_suggestions[:3], 1):
            print(f"   {i}. [{suggestion['type'].upper()}] {suggestion['issue']}")
            print(f"      建议: {suggestion['suggestion']}")
    
    return report

def main():
    """主函数"""
    report = analyze_latest_training()
    
    if report:
        print(f"\n✅ 分析完成！详细报告已保存到:")
        print(f"   scripts/optimization/latest_optimization_report.json")
        print(f"\n💡 下一步:")
        print(f"   1. 查看详细报告了解具体问题")
        print(f"   2. 根据建议调整配置参数")
        print(f"   3. 重新训练模型验证效果")

if __name__ == "__main__":
    main()
