# application_state.py
"""
中心化的应用程序状态管理器

这个模块提供了一个线程安全的中心化状态管理系统，用于管理应用程序的全局状态，
包括Binance客户端、WebSocket管理器、调度器、事件标志等。

主要特性：
- 线程安全的状态访问和修改
- 中心化的资源管理
- 跨线程状态共享
- 清晰的状态生命周期管理
"""

import threading
import queue
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timezone
from dataclasses import dataclass, field
from binance.client import Client

try:
    from binance import ThreadedWebsocketManager
except ImportError:
    try:
        from binance.websocket import ThreadedWebsocketManager
    except ImportError:
        print("⚠️ Binance WebSocket导入失败，将使用REST API模式")
        ThreadedWebsocketManager = None

try:
    from apscheduler.schedulers.background import BackgroundScheduler
except ImportError:
    print("⚠️ APScheduler导入失败，调度功能将不可用")
    BackgroundScheduler = None


@dataclass
class PredictionResult:
    """预测结果数据结构"""
    target_name: str
    symbol: str
    interval: str
    prediction_class: int
    probabilities: List[float]
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrainingStatus:
    """训练状态数据结构"""
    is_training: bool = False
    current_target: Optional[str] = None
    progress: float = 0.0
    start_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    status_message: str = ""


class ThreadSafeStateContainer:
    """线程安全的状态容器"""
    
    def __init__(self):
        self._lock = threading.RLock()  # 使用可重入锁
        self._data = {}
    
    def get(self, key: str, default=None):
        """线程安全地获取值"""
        with self._lock:
            return self._data.get(key, default)
    
    def set(self, key: str, value: Any):
        """线程安全地设置值"""
        with self._lock:
            self._data[key] = value
    
    def update(self, updates: Dict[str, Any]):
        """线程安全地批量更新"""
        with self._lock:
            self._data.update(updates)
    
    def pop(self, key: str, default=None):
        """线程安全地弹出值"""
        with self._lock:
            return self._data.pop(key, default)
    
    def keys(self):
        """获取所有键"""
        with self._lock:
            return list(self._data.keys())
    
    def items(self):
        """获取所有键值对"""
        with self._lock:
            return list(self._data.items())


class ApplicationState:
    """
    中心化的应用程序状态管理器
    
    这个类管理应用程序的所有全局状态，包括：
    - Binance客户端和WebSocket管理器
    - 调度器和事件标志
    - 预测结果和训练状态
    - 线程安全的状态共享
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化应用程序状态"""
        if hasattr(self, '_initialized'):
            return

        # 核心资源
        self.binance_client: Optional[Client] = None
        self.shared_binance_twm: Optional[ThreadedWebsocketManager] = None
        self.scheduler: Optional[BackgroundScheduler] = None
        self.main_root = None  # Tkinter主窗口

        # 事件和标志
        self.stop_event = threading.Event()
        self.training_in_progress_flag = threading.Event()

        # 状态容器
        self.shared_state = ThreadSafeStateContainer()
        self.config_state = ThreadSafeStateContainer()

        # 预测结果队列（线程安全）
        self.prediction_results_queue = queue.Queue(maxsize=1000)
        self.latest_predictions = ThreadSafeStateContainer()

        # 训练状态
        self.training_status = TrainingStatus()
        self.training_status_lock = threading.RLock()

        # 实时数据
        self.latest_prices = ThreadSafeStateContainer()
        self.latest_klines = ThreadSafeStateContainer()

        # 应用程序配置
        self.app_timezone = timezone.utc
        self.dynamic_config_manager = None
        self.robust_config_manager = None

        # 价格和URL状态
        self.last_ticker_price = None
        self.simulator_url_from_cmd = None

        # 状态变化监听器
        self._state_listeners = {}  # {event_type: [callback_functions]}
        self._listeners_lock = threading.Lock()

        # 标记为已初始化
        self._initialized = True

        print("ApplicationState: 中心化状态管理器已初始化")
    
    @classmethod
    def get_instance(cls) -> 'ApplicationState':
        """获取单例实例"""
        return cls()
    
    # === 核心资源管理 ===
    
    def set_binance_client(self, client: Client):
        """设置Binance客户端"""
        self.binance_client = client
        print("ApplicationState: Binance客户端已设置")
    
    def get_binance_client(self) -> Optional[Client]:
        """获取Binance客户端"""
        return self.binance_client
    
    def set_shared_twm(self, twm: ThreadedWebsocketManager):
        """设置共享的ThreadedWebsocketManager"""
        self.shared_binance_twm = twm
        print("ApplicationState: 共享TWM已设置")
    
    def get_shared_twm(self) -> Optional[ThreadedWebsocketManager]:
        """获取共享的ThreadedWebsocketManager"""
        return self.shared_binance_twm
    
    def set_scheduler(self, scheduler: BackgroundScheduler):
        """设置调度器"""
        self.scheduler = scheduler
        print("ApplicationState: 调度器已设置")
    
    def get_scheduler(self) -> Optional[BackgroundScheduler]:
        """获取调度器"""
        return self.scheduler
    
    def set_main_root(self, root):
        """设置Tkinter主窗口"""
        self.main_root = root
        print("ApplicationState: 主窗口已设置")
    
    def get_main_root(self):
        """获取Tkinter主窗口"""
        return self.main_root

    # === 配置管理器管理 ===

    def set_dynamic_config_manager(self, manager):
        """设置动态配置管理器"""
        self.dynamic_config_manager = manager
        print("ApplicationState: 动态配置管理器已设置")

    def get_dynamic_config_manager(self):
        """获取动态配置管理器"""
        return self.dynamic_config_manager

    def set_robust_config_manager(self, manager):
        """设置健壮配置管理器"""
        self.robust_config_manager = manager
        print("ApplicationState: 健壮配置管理器已设置")

    def get_robust_config_manager(self):
        """获取健壮配置管理器"""
        return self.robust_config_manager

    # === 价格和URL状态管理 ===

    def set_last_ticker_price(self, price: Optional[float]):
        """设置最后的ticker价格"""
        self.last_ticker_price = price

    def get_last_ticker_price(self) -> Optional[float]:
        """获取最后的ticker价格"""
        return self.last_ticker_price

    def set_simulator_url_from_cmd(self, url: Optional[str]):
        """设置从命令行传入的模拟器URL"""
        self.simulator_url_from_cmd = url
        print(f"ApplicationState: 模拟器URL已设置为 {url}")

    def get_simulator_url_from_cmd(self) -> Optional[str]:
        """获取从命令行传入的模拟器URL"""
        return self.simulator_url_from_cmd

    # === 时区管理 ===

    def set_app_timezone(self, timezone_obj):
        """设置应用程序时区"""
        self.app_timezone = timezone_obj
        print(f"ApplicationState: 应用程序时区已设置为 {timezone_obj}")

    def get_app_timezone(self):
        """获取应用程序时区"""
        return self.app_timezone
    
    # === 事件和标志管理 ===
    
    def request_stop(self):
        """请求停止应用程序"""
        self.stop_event.set()
        print("ApplicationState: 停止事件已设置")
    
    def is_stop_requested(self) -> bool:
        """检查是否请求停止"""
        return self.stop_event.is_set()
    
    def set_training_in_progress(self, in_progress: bool):
        """设置训练进行状态"""
        if in_progress:
            self.training_in_progress_flag.set()
        else:
            self.training_in_progress_flag.clear()

        with self.training_status_lock:
            self.training_status.is_training = in_progress
            if in_progress:
                self.training_status.start_time = datetime.now()
            else:
                self.training_status.start_time = None
                self.training_status.current_target = None
                self.training_status.progress = 0.0

        # 通知状态监听器
        self._notify_state_listeners('training_status', {
            'is_training': in_progress,
            'status': self.get_training_status()
        })

        print(f"ApplicationState: 训练状态已设置为 {'进行中' if in_progress else '空闲'}")
    
    def is_training_in_progress(self) -> bool:
        """检查是否正在训练"""
        return self.training_in_progress_flag.is_set()
    
    # === 训练状态管理 ===
    
    def update_training_status(self, **kwargs):
        """更新训练状态"""
        with self.training_status_lock:
            for key, value in kwargs.items():
                if hasattr(self.training_status, key):
                    setattr(self.training_status, key, value)

        # 通知状态监听器
        self._notify_state_listeners('training_progress', {
            'status': self.get_training_status(),
            'updates': kwargs
        })
    
    def get_training_status(self) -> TrainingStatus:
        """获取训练状态的副本"""
        with self.training_status_lock:
            return TrainingStatus(
                is_training=self.training_status.is_training,
                current_target=self.training_status.current_target,
                progress=self.training_status.progress,
                start_time=self.training_status.start_time,
                estimated_completion=self.training_status.estimated_completion,
                status_message=self.training_status.status_message
            )
    
    # === 预测结果管理 ===
    
    def add_prediction_result(self, result: PredictionResult):
        """添加预测结果"""
        try:
            self.prediction_results_queue.put_nowait(result)
            # 同时更新最新预测
            key = f"{result.target_name}_{result.symbol}_{result.interval}"
            self.latest_predictions.set(key, result)

            # 通知状态监听器
            self._notify_state_listeners('prediction_result', {
                'result': result,
                'target_name': result.target_name,
                'symbol': result.symbol,
                'interval': result.interval
            })

        except queue.Full:
            print("ApplicationState: 预测结果队列已满，丢弃最旧的结果")
            try:
                self.prediction_results_queue.get_nowait()  # 移除最旧的
                self.prediction_results_queue.put_nowait(result)  # 添加新的

                # 通知状态监听器
                self._notify_state_listeners('prediction_result', {
                    'result': result,
                    'target_name': result.target_name,
                    'symbol': result.symbol,
                    'interval': result.interval
                })

            except queue.Empty:
                pass
    
    def get_latest_prediction(self, target_name: str, symbol: str, interval: str) -> Optional[PredictionResult]:
        """获取指定目标的最新预测"""
        key = f"{target_name}_{symbol}_{interval}"
        return self.latest_predictions.get(key)
    
    def get_all_latest_predictions(self) -> Dict[str, PredictionResult]:
        """获取所有最新预测"""
        return dict(self.latest_predictions.items())
    
    # === 实时数据管理 ===
    
    def update_latest_price(self, symbol: str, price: float):
        """更新最新价格"""
        self.latest_prices.set(symbol.upper(), {
            'price': price,
            'timestamp': datetime.now()
        })
    
    def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取最新价格"""
        return self.latest_prices.get(symbol.upper())
    
    def update_latest_kline(self, symbol: str, interval: str, kline_data: Dict[str, Any]):
        """更新最新K线数据"""
        key = f"{symbol.upper()}_{interval}"
        self.latest_klines.set(key, {
            'data': kline_data,
            'timestamp': datetime.now()
        })
    
    def get_latest_kline(self, symbol: str, interval: str) -> Optional[Dict[str, Any]]:
        """获取最新K线数据"""
        key = f"{symbol.upper()}_{interval}"
        return self.latest_klines.get(key)
    
    # === 通用状态管理 ===
    
    def set_shared_value(self, key: str, value: Any):
        """设置共享值"""
        self.shared_state.set(key, value)
    
    def get_shared_value(self, key: str, default=None):
        """获取共享值"""
        return self.shared_state.get(key, default)
    
    def update_shared_values(self, updates: Dict[str, Any]):
        """批量更新共享值"""
        self.shared_state.update(updates)
    
    # === 配置状态管理 ===
    
    def set_config_value(self, key: str, value: Any):
        """设置配置值"""
        self.config_state.set(key, value)
    
    def get_config_value(self, key: str, default=None):
        """获取配置值"""
        return self.config_state.get(key, default)
    
    # === 清理和关闭 ===
    
    def cleanup(self):
        """清理资源"""
        print("ApplicationState: 开始清理资源...")
        
        # 停止调度器
        if self.scheduler and self.scheduler.running:
            try:
                self.scheduler.shutdown(wait=False)
                print("ApplicationState: 调度器已停止")
            except Exception as e:
                print(f"ApplicationState: 停止调度器时出错: {e}")
        
        # 停止TWM
        if self.shared_binance_twm:
            try:
                self.shared_binance_twm.stop()
                print("ApplicationState: 共享TWM已停止")
            except Exception as e:
                print(f"ApplicationState: 停止TWM时出错: {e}")
        
        # 清理状态
        self.request_stop()
        self.set_training_in_progress(False)

        # 清理状态监听器
        with self._listeners_lock:
            self._state_listeners.clear()

        print("ApplicationState: 资源清理完成")

    # === 状态监听器管理 ===

    def add_state_listener(self, event_type: str, callback: Callable[[str, Any], None]) -> None:
        """
        添加状态变化监听器

        Args:
            event_type: 事件类型 (如 'training_status', 'prediction_result', 'button_state')
            callback: 回调函数，接收 (event_type, data) 参数
        """
        with self._listeners_lock:
            if event_type not in self._state_listeners:
                self._state_listeners[event_type] = []
            self._state_listeners[event_type].append(callback)
        print(f"ApplicationState: 已添加 '{event_type}' 状态监听器")

    def remove_state_listener(self, event_type: str, callback: Callable[[str, Any], None]) -> None:
        """
        移除状态变化监听器

        Args:
            event_type: 事件类型
            callback: 要移除的回调函数
        """
        with self._listeners_lock:
            if event_type in self._state_listeners:
                try:
                    self._state_listeners[event_type].remove(callback)
                    if not self._state_listeners[event_type]:
                        del self._state_listeners[event_type]
                    print(f"ApplicationState: 已移除 '{event_type}' 状态监听器")
                except ValueError:
                    pass

    def _notify_state_listeners(self, event_type: str, data: Any) -> None:
        """
        通知状态变化监听器

        Args:
            event_type: 事件类型
            data: 事件数据
        """
        with self._listeners_lock:
            listeners = self._state_listeners.get(event_type, []).copy()

        for callback in listeners:
            try:
                callback(event_type, data)
            except Exception as e:
                print(f"ApplicationState: 状态监听器回调失败 ({event_type}): {e}")


# 全局实例访问函数
def get_app_state() -> ApplicationState:
    """获取应用程序状态实例"""
    return ApplicationState.get_instance()
