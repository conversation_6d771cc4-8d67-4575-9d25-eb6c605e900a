#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
data_utils.py 核心功能单元测试
测试时间转换、目标变量创建、NaN填充、特征计算等核心功能
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
from datetime import timedelta, datetime, timezone
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 忽略警告以保持测试输出清洁
warnings.filterwarnings('ignore')

class TestIntervalToTimedelta(unittest.TestCase):
    """测试 interval_to_timedelta 函数"""
    
    def setUp(self):
        from src.core.data_utils import interval_to_timedelta
        self.interval_to_timedelta = interval_to_timedelta
    
    def test_minute_intervals(self):
        """测试分钟间隔"""
        self.assertEqual(self.interval_to_timedelta('1m'), timedelta(minutes=1))
        self.assertEqual(self.interval_to_timedelta('5m'), timedelta(minutes=5))
        self.assertEqual(self.interval_to_timedelta('15m'), timedelta(minutes=15))
        self.assertEqual(self.interval_to_timedelta('30m'), timedelta(minutes=30))
    
    def test_hour_intervals(self):
        """测试小时间隔"""
        self.assertEqual(self.interval_to_timedelta('1h'), timedelta(hours=1))
        self.assertEqual(self.interval_to_timedelta('4h'), timedelta(hours=4))
        self.assertEqual(self.interval_to_timedelta('12h'), timedelta(hours=12))
    
    def test_day_intervals(self):
        """测试天间隔"""
        self.assertEqual(self.interval_to_timedelta('1d'), timedelta(days=1))
        self.assertEqual(self.interval_to_timedelta('3d'), timedelta(days=3))
        self.assertEqual(self.interval_to_timedelta('7d'), timedelta(days=7))
    
    def test_week_intervals(self):
        """测试周间隔"""
        self.assertEqual(self.interval_to_timedelta('1w'), timedelta(weeks=1))
        self.assertEqual(self.interval_to_timedelta('2w'), timedelta(weeks=2))
    
    def test_invalid_intervals(self):
        """测试无效间隔"""
        # 应该返回默认值或抛出异常
        with self.assertRaises((ValueError, AttributeError)):
            self.interval_to_timedelta('invalid')
        
        with self.assertRaises((ValueError, AttributeError)):
            self.interval_to_timedelta('1x')
        
        with self.assertRaises((ValueError, AttributeError)):
            self.interval_to_timedelta('')
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试大数值
        self.assertEqual(self.interval_to_timedelta('60m'), timedelta(minutes=60))
        self.assertEqual(self.interval_to_timedelta('24h'), timedelta(hours=24))
        
        # 测试零值（如果支持）
        try:
            result = self.interval_to_timedelta('0m')
            self.assertEqual(result, timedelta(minutes=0))
        except:
            pass  # 如果不支持零值，跳过


class TestSafeFillNans(unittest.TestCase):
    """测试 safe_fill_nans 函数"""
    
    def setUp(self):
        from src.core.data_utils import safe_fill_nans
        self.safe_fill_nans = safe_fill_nans
    
    def test_no_nans(self):
        """测试没有NaN的序列"""
        series = pd.Series([1.0, 2.0, 3.0, 4.0, 5.0])
        result = self.safe_fill_nans(series)
        pd.testing.assert_series_equal(result, series)
    
    def test_nans_at_beginning(self):
        """测试开头有NaN的序列"""
        series = pd.Series([np.nan, np.nan, 3.0, 4.0, 5.0])
        result = self.safe_fill_nans(series, default_value=1.0, use_historical_only=True)
        expected = pd.Series([1.0, 1.0, 3.0, 4.0, 5.0])
        pd.testing.assert_series_equal(result, expected)
    
    def test_nans_in_middle(self):
        """测试中间有NaN的序列"""
        series = pd.Series([1.0, 2.0, np.nan, np.nan, 5.0])
        result = self.safe_fill_nans(series, default_value=0.0, use_historical_only=True)
        expected = pd.Series([1.0, 2.0, 2.0, 2.0, 5.0])  # 向前填充
        pd.testing.assert_series_equal(result, expected)
    
    def test_nans_at_end(self):
        """测试结尾有NaN的序列"""
        series = pd.Series([1.0, 2.0, 3.0, np.nan, np.nan])
        result = self.safe_fill_nans(series, default_value=0.0, use_historical_only=True)
        expected = pd.Series([1.0, 2.0, 3.0, 3.0, 3.0])  # 向前填充
        pd.testing.assert_series_equal(result, expected)
    
    def test_all_nans(self):
        """测试全部为NaN的序列"""
        series = pd.Series([np.nan, np.nan, np.nan])
        result = self.safe_fill_nans(series, default_value=42.0)
        expected = pd.Series([42.0, 42.0, 42.0])
        pd.testing.assert_series_equal(result, expected)
    
    def test_historical_vs_traditional_mode(self):
        """测试历史模式vs传统模式的差异"""
        series = pd.Series([np.nan, np.nan, 1.0, 2.0, 100.0])  # 后面的大值不应影响前面
        
        # 历史模式
        result_hist = self.safe_fill_nans(series, default_value=0.5, use_historical_only=True)
        expected_hist = pd.Series([0.5, 0.5, 1.0, 2.0, 100.0])
        pd.testing.assert_series_equal(result_hist, expected_hist)
        
        # 传统模式（可能使用全局均值）
        result_trad = self.safe_fill_nans(series, default_value=0.5, use_historical_only=False)
        # 传统模式可能会用全局均值填充前面的NaN
        self.assertFalse(result_trad.isna().any())  # 确保没有NaN
    
    def test_empty_series(self):
        """测试空序列"""
        series = pd.Series([], dtype=float)
        result = self.safe_fill_nans(series)
        pd.testing.assert_series_equal(result, series)
    
    def test_none_input(self):
        """测试None输入"""
        result = self.safe_fill_nans(None)
        self.assertIsNone(result)


class TestCreateTargetVariable(unittest.TestCase):
    """测试 create_target_variable 函数"""
    
    def setUp(self):
        from src.core.data_utils import create_target_variable
        self.create_target_variable = create_target_variable
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='5T')
        np.random.seed(42)
        self.test_df = pd.DataFrame({
            'open': np.random.uniform(29000, 31000, 100),
            'high': np.random.uniform(30000, 32000, 100),
            'low': np.random.uniform(28000, 30000, 100),
            'close': np.random.uniform(29000, 31000, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
    
    def test_binary_target_up(self):
        """测试二元上涨目标"""
        config = {
            'target_variable_type': 'binary',
            'target_direction': 'up',
            'target_periods': [1, 3],
            'target_thresholds': [0.01, 0.02],
            'drop_neutral_targets': False
        }
        
        result_df = self.create_target_variable(self.test_df.copy(), config)
        
        # 检查目标列是否存在
        self.assertIn('target_up_1p', result_df.columns)
        self.assertIn('target_up_3p', result_df.columns)
        
        # 检查目标值是否为0或1
        self.assertTrue(result_df['target_up_1p'].isin([0, 1]).all())
        self.assertTrue(result_df['target_up_3p'].isin([0, 1]).all())
    
    def test_binary_target_down(self):
        """测试二元下跌目标"""
        config = {
            'target_variable_type': 'binary',
            'target_direction': 'down',
            'target_periods': [1],
            'target_thresholds': [0.01],
            'drop_neutral_targets': False
        }
        
        result_df = self.create_target_variable(self.test_df.copy(), config)
        
        # 检查目标列是否存在
        self.assertIn('target_down_1p', result_df.columns)
        
        # 检查目标值是否为0或1
        self.assertTrue(result_df['target_down_1p'].isin([0, 1]).all())
    
    def test_multiclass_target(self):
        """测试多分类目标"""
        config = {
            'target_variable_type': 'multiclass',
            'target_periods': [1],
            'target_thresholds': [0.01],
            'drop_neutral_targets': False
        }
        
        result_df = self.create_target_variable(self.test_df.copy(), config)
        
        # 检查目标列是否存在
        self.assertIn('target_multiclass_1p', result_df.columns)
        
        # 检查目标值是否为-1, 0, 1
        unique_values = result_df['target_multiclass_1p'].dropna().unique()
        self.assertTrue(all(val in [-1, 0, 1] for val in unique_values))
    
    def test_drop_neutral_targets(self):
        """测试删除中性目标"""
        config = {
            'target_variable_type': 'binary',
            'target_direction': 'up',
            'target_periods': [1],
            'target_thresholds': [0.01],
            'drop_neutral_targets': True
        }
        
        original_len = len(self.test_df)
        result_df = self.create_target_variable(self.test_df.copy(), config)
        
        # 删除中性目标后，数据应该减少
        self.assertLessEqual(len(result_df), original_len)
        
        # 目标列应该只包含0和1，没有中性值
        if 'target_up_1p' in result_df.columns:
            unique_values = result_df['target_up_1p'].dropna().unique()
            self.assertTrue(all(val in [0, 1] for val in unique_values))
    
    def test_invalid_config(self):
        """测试无效配置"""
        # 测试缺少必要参数
        config = {
            'target_variable_type': 'binary'
            # 缺少其他必要参数
        }
        
        # 应该处理错误或使用默认值
        try:
            result_df = self.create_target_variable(self.test_df.copy(), config)
            # 如果没有抛出异常，检查是否有合理的默认行为
            self.assertIsInstance(result_df, pd.DataFrame)
        except Exception:
            # 如果抛出异常，这也是可接受的
            pass


class TestPriceChangeFeatures(unittest.TestCase):
    """测试价格变化特征计算"""
    
    def setUp(self):
        from src.core.data_utils import _add_price_change_features
        self.add_price_change_features = _add_price_change_features
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=50, freq='5T')
        self.test_df = pd.DataFrame({
            'open': [100.0] * 50,
            'high': [105.0] * 50,
            'low': [95.0] * 50,
            'close': [100.0 + i for i in range(50)],  # 递增的收盘价
            'volume': [1000.0] * 50
        }, index=dates)
        
        self.config = {
            'price_change_periods': [1, 3, 5],
            'enable_price_change': True
        }
    
    def test_price_change_calculation(self):
        """测试价格变化计算"""
        result_df = self.add_price_change_features(self.test_df.copy(), self.config, 'test')
        
        # 检查价格变化列是否存在
        self.assertIn('price_change_1p', result_df.columns)
        self.assertIn('price_change_3p', result_df.columns)
        self.assertIn('price_change_5p', result_df.columns)
        
        # 检查计算结果
        # 由于收盘价是递增的，价格变化应该为正
        price_change_1p = result_df['price_change_1p'].dropna()
        self.assertTrue((price_change_1p > 0).all())
        
        # 检查3期价格变化应该大于1期
        price_change_3p = result_df['price_change_3p'].dropna()
        self.assertTrue((price_change_3p > price_change_1p.iloc[:len(price_change_3p)]).all())
    
    def test_disabled_price_change(self):
        """测试禁用价格变化特征"""
        config = self.config.copy()
        config['enable_price_change'] = False
        
        result_df = self.add_price_change_features(self.test_df.copy(), config, 'test')
        
        # 不应该添加价格变化列
        self.assertNotIn('price_change_1p', result_df.columns)
        self.assertNotIn('price_change_3p', result_df.columns)
    
    def test_empty_periods(self):
        """测试空的周期列表"""
        config = self.config.copy()
        config['price_change_periods'] = []
        
        result_df = self.add_price_change_features(self.test_df.copy(), config, 'test')
        
        # 不应该添加任何价格变化列
        price_change_cols = [col for col in result_df.columns if col.startswith('price_change_')]
        self.assertEqual(len(price_change_cols), 0)


class TestTechnicalIndicators(unittest.TestCase):
    """测试技术指标计算"""
    
    def setUp(self):
        from src.core.data_utils import _add_technical_indicators
        self.add_technical_indicators = _add_technical_indicators
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='5T')
        np.random.seed(42)
        
        # 创建有趋势的价格数据
        base_price = 30000
        trend = np.linspace(0, 1000, 100)
        noise = np.random.normal(0, 50, 100)
        prices = base_price + trend + noise
        
        self.test_df = pd.DataFrame({
            'open': prices + np.random.normal(0, 10, 100),
            'high': prices + np.abs(np.random.normal(0, 20, 100)),
            'low': prices - np.abs(np.random.normal(0, 20, 100)),
            'close': prices,
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
        
        self.config = {
            'enable_ta': True,
            'rsi_period': 14,
            'hma_period': 14,
            'atr_period': 14,
            'willr_period': 14,
            'cci_period': 14,
            'cci_constant': 0.015,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_sign': 9
        }
    
    def test_rsi_calculation(self):
        """测试RSI计算"""
        result_df = self.add_technical_indicators(self.test_df.copy(), self.config, 'test')
        
        # 检查RSI列是否存在
        self.assertIn('RSI_14', result_df.columns)
        
        # 检查RSI值范围
        rsi_values = result_df['RSI_14'].dropna()
        self.assertTrue((rsi_values >= 0).all())
        self.assertTrue((rsi_values <= 100).all())
    
    def test_macd_calculation(self):
        """测试MACD计算"""
        result_df = self.add_technical_indicators(self.test_df.copy(), self.config, 'test')
        
        # 检查MACD相关列是否存在
        self.assertIn('MACD', result_df.columns)
        self.assertIn('MACD_signal', result_df.columns)
        self.assertIn('MACD_histogram', result_df.columns)
        
        # 检查MACD值是否为数值
        macd_values = result_df['MACD'].dropna()
        self.assertTrue(np.isfinite(macd_values).all())
    
    def test_atr_calculation(self):
        """测试ATR计算"""
        result_df = self.add_technical_indicators(self.test_df.copy(), self.config, 'test')
        
        # 检查ATR列是否存在
        self.assertIn('ATRr_14', result_df.columns)
        
        # 检查ATR值应该为正
        atr_values = result_df['ATRr_14'].dropna()
        self.assertTrue((atr_values >= 0).all())
    
    def test_disabled_ta(self):
        """测试禁用技术指标"""
        config = self.config.copy()
        config['enable_ta'] = False
        
        result_df = self.add_technical_indicators(self.test_df.copy(), config, 'test')
        
        # 不应该添加技术指标列
        self.assertNotIn('RSI_14', result_df.columns)
        self.assertNotIn('MACD', result_df.columns)


class TestFindOptimalThreshold(unittest.TestCase):
    """测试最优阈值查找"""
    
    def setUp(self):
        from src.core.data_utils import find_optimal_threshold
        self.find_optimal_threshold = find_optimal_threshold
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 1000
        
        # 创建有区分度的数据
        self.y_true = np.random.choice([0, 1], size=n_samples, p=[0.7, 0.3])
        
        # 创建与真实标签相关的概率
        self.y_proba = np.random.random(n_samples)
        # 让正类的概率更高
        self.y_proba[self.y_true == 1] += 0.3
        self.y_proba = np.clip(self.y_proba, 0, 1)
    
    def test_f1_method(self):
        """测试F1分数方法"""
        result = self.find_optimal_threshold(
            self.y_true, self.y_proba, 
            target_name="test", method="f1"
        )
        
        self.assertIsNotNone(result)
        self.assertIn('optimal_threshold', result)
        self.assertIn('f1_score', result)
        self.assertIn('precision', result)
        self.assertIn('recall', result)
        
        # 检查阈值范围
        self.assertGreaterEqual(result['optimal_threshold'], 0)
        self.assertLessEqual(result['optimal_threshold'], 1)
        
        # 检查F1分数范围
        self.assertGreaterEqual(result['f1_score'], 0)
        self.assertLessEqual(result['f1_score'], 1)
    
    def test_precision_recall_method(self):
        """测试精确率-召回率方法"""
        result = self.find_optimal_threshold(
            self.y_true, self.y_proba,
            target_name="test", method="precision_recall"
        )
        
        self.assertIsNotNone(result)
        self.assertIn('optimal_threshold', result)
        self.assertEqual(result['method'], 'precision_recall')
    
    def test_youden_method(self):
        """测试Youden指数方法"""
        result = self.find_optimal_threshold(
            self.y_true, self.y_proba,
            target_name="test", method="youden"
        )
        
        self.assertIsNotNone(result)
        self.assertIn('optimal_threshold', result)
        self.assertEqual(result['method'], 'youden')
    
    def test_precision_constrained_method(self):
        """测试精确率约束方法"""
        result = self.find_optimal_threshold(
            self.y_true, self.y_proba,
            target_name="test", method="precision_constrained_recall",
            min_precision=0.6
        )
        
        self.assertIsNotNone(result)
        self.assertIn('optimal_threshold', result)
        self.assertEqual(result['method'], 'precision_constrained_recall')
        
        # 检查精确率约束
        if result['precision'] is not None:
            self.assertGreaterEqual(result['precision'], 0.6)
    
    def test_unsupported_method(self):
        """测试不支持的方法"""
        result = self.find_optimal_threshold(
            self.y_true, self.y_proba,
            target_name="test", method="grid_search"
        )
        
        # 应该返回None或抛出错误
        self.assertIsNone(result)
    
    def test_invalid_input(self):
        """测试无效输入"""
        # 测试长度不匹配
        result = self.find_optimal_threshold(
            self.y_true[:100], self.y_proba,
            target_name="test", method="f1"
        )
        self.assertIsNone(result)
        
        # 测试空输入
        result = self.find_optimal_threshold(
            [], [],
            target_name="test", method="f1"
        )
        self.assertIsNone(result)
        
        # 测试非二分类标签
        y_invalid = np.array([0, 1, 2, 3])
        y_proba_invalid = np.array([0.1, 0.3, 0.7, 0.9])
        result = self.find_optimal_threshold(
            y_invalid, y_proba_invalid,
            target_name="test", method="f1"
        )
        self.assertIsNone(result)


class TestPrepareFeaturesForPrediction(unittest.TestCase):
    """测试预测特征准备"""

    def setUp(self):
        from src.core.data_utils import prepare_features_for_prediction
        self.prepare_features_for_prediction = prepare_features_for_prediction

        # 创建训练特征
        self.training_features = ['feature_1', 'feature_2', 'feature_3', 'feature_4']

        # 创建预测数据
        dates = pd.date_range('2023-01-01', periods=10, freq='5T')
        self.prediction_df = pd.DataFrame({
            'feature_1': np.random.random(10),
            'feature_2': np.random.random(10),
            'feature_3': np.random.random(10),
            # 缺少 feature_4
            'extra_feature': np.random.random(10)  # 额外特征
        }, index=dates)

    def test_missing_features_alignment(self):
        """测试缺失特征的对齐"""
        try:
            result = self.prepare_features_for_prediction(
                self.prediction_df.copy(),
                self.training_features
            )

            # 检查结果包含所有训练特征
            for feature in self.training_features:
                self.assertIn(feature, result.columns)

            # 检查缺失特征被填充
            self.assertIn('feature_4', result.columns)

            # 检查额外特征被移除
            self.assertNotIn('extra_feature', result.columns)

        except Exception:
            # 如果函数不存在或实现不同，跳过测试
            self.skipTest("prepare_features_for_prediction function not available")

    def test_feature_order_preservation(self):
        """测试特征顺序保持"""
        try:
            result = self.prepare_features_for_prediction(
                self.prediction_df.copy(),
                self.training_features
            )

            # 检查特征顺序
            self.assertEqual(list(result.columns), self.training_features)

        except Exception:
            self.skipTest("prepare_features_for_prediction function not available")


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
