#!/usr/bin/env python3
"""
系统级性能优化脚本
针对AMD处理器 + RTX 3070的优化配置
"""

import os
import sys
import multiprocessing as mp
import platform
import psutil

def get_system_info():
    """获取系统信息"""
    print("🔍 系统信息检测...")
    
    info = {
        'cpu_count': mp.cpu_count(),
        'cpu_freq': psutil.cpu_freq(),
        'memory': psutil.virtual_memory(),
        'platform': platform.platform(),
        'python_version': sys.version,
        'architecture': platform.architecture()
    }
    
    print(f"CPU核心数: {info['cpu_count']}")
    print(f"CPU频率: {info['cpu_freq'].current:.0f} MHz")
    print(f"内存总量: {info['memory'].total / (1024**3):.1f} GB")
    print(f"可用内存: {info['memory'].available / (1024**3):.1f} GB")
    print(f"系统平台: {info['platform']}")
    
    return info

def setup_environment_variables():
    """设置系统环境变量"""
    print("\n🔧 设置环境变量...")
    
    cpu_count = mp.cpu_count()
    
    # CPU线程优化
    env_vars = {
        'OMP_NUM_THREADS': str(cpu_count),
        'MKL_NUM_THREADS': str(cpu_count),
        'NUMBA_NUM_THREADS': str(cpu_count),
        'OPENBLAS_NUM_THREADS': str(cpu_count),
        'VECLIB_MAXIMUM_THREADS': str(cpu_count),
        
        # TensorFlow优化
        'TF_CPP_MIN_LOG_LEVEL': '1',
        'TF_ENABLE_ONEDNN_OPTS': '1',
        'TF_NUM_INTEROP_THREADS': '4',
        'TF_NUM_INTRAOP_THREADS': str(cpu_count),
        
        # 内存优化
        'MALLOC_TRIM_THRESHOLD_': '100000',
        'MALLOC_MMAP_THRESHOLD_': '131072',
        
        # Python优化
        'PYTHONHASHSEED': '0',
        'PYTHONUNBUFFERED': '1',
        
        # AMD处理器特定优化
        'AMD_LOG_LEVEL': '1',
        
        # GPU相关（如果可用）
        'CUDA_VISIBLE_DEVICES': '0',
        'CUDA_CACHE_DISABLE': '0',
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    print(f"✅ 环境变量设置完成")

def create_startup_script():
    """创建启动优化脚本"""
    print("\n📝 创建启动优化脚本...")
    
    startup_content = '''@echo off
REM AMD处理器 + RTX 3070 系统优化启动脚本

echo 🚀 启动系统优化...

REM 设置CPU线程数
set OMP_NUM_THREADS=16
set MKL_NUM_THREADS=16
set NUMBA_NUM_THREADS=16
set OPENBLAS_NUM_THREADS=16

REM TensorFlow优化
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=1
set TF_NUM_INTEROP_THREADS=4
set TF_NUM_INTRAOP_THREADS=16

REM 内存优化
set MALLOC_TRIM_THRESHOLD_=100000
set MALLOC_MMAP_THRESHOLD_=131072

REM Python优化
set PYTHONHASHSEED=0
set PYTHONUNBUFFERED=1

REM GPU优化
set CUDA_VISIBLE_DEVICES=0
set CUDA_CACHE_DISABLE=0

echo ✅ 系统优化设置完成

REM 启动Python程序
python %*
'''
    
    with open('optimized_start.bat', 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ 启动脚本已创建: optimized_start.bat")
    print("   使用方法: optimized_start.bat main.py")

def create_performance_monitor():
    """创建性能监控脚本"""
    print("\n📊 创建性能监控脚本...")
    
    monitor_content = '''#!/usr/bin/env python3
"""
性能监控脚本
实时监控系统资源使用情况
"""

import psutil
import time
import threading
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval=5):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        print("🔍 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("⏹️  性能监控已停止")
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # 内存使用情况
                memory = psutil.virtual_memory()
                
                # 磁盘使用情况
                disk = psutil.disk_usage('/')
                
                # GPU使用情况（如果可用）
                gpu_info = self._get_gpu_info()
                
                # 打印监控信息
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"\\n[{timestamp}] 系统性能监控:")
                print(f"  CPU: {cpu_percent:.1f}%")
                print(f"  内存: {memory.percent:.1f}% ({memory.used/(1024**3):.1f}GB/{memory.total/(1024**3):.1f}GB)")
                print(f"  磁盘: {disk.percent:.1f}%")
                
                if gpu_info:
                    print(f"  GPU: {gpu_info}")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(interval)
    
    def _get_gpu_info(self):
        """获取GPU信息"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]
                return f"{gpu.load*100:.1f}% ({gpu.memoryUsed}MB/{gpu.memoryTotal}MB)"
        except ImportError:
            pass
        return None
    
    def get_current_stats(self):
        """获取当前统计信息"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'process_count': len(psutil.pids())
        }

# 全局监控实例
monitor = PerformanceMonitor()

def start_performance_monitoring(interval=10):
    """启动性能监控"""
    monitor.start_monitoring(interval)

def stop_performance_monitoring():
    """停止性能监控"""
    monitor.stop_monitoring()

def get_performance_stats():
    """获取性能统计"""
    return monitor.get_current_stats()

if __name__ == "__main__":
    print("🚀 启动性能监控...")
    monitor.start_monitoring(5)
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\\n停止监控...")
        monitor.stop_monitoring()
'''
    
    with open('performance_monitor.py', 'w', encoding='utf-8') as f:
        f.write(monitor_content)
    
    print("✅ 性能监控脚本已创建: performance_monitor.py")

def optimize_python_settings():
    """优化Python设置"""
    print("\n🐍 优化Python设置...")
    
    # 创建.pythonrc文件
    pythonrc_content = '''# Python启动优化配置
import sys
import os

# 设置优化环境变量
os.environ.setdefault('PYTHONHASHSEED', '0')
os.environ.setdefault('PYTHONUNBUFFERED', '1')

# 优化导入路径
if '.' not in sys.path:
    sys.path.insert(0, '.')

print("🐍 Python优化配置已加载")
'''
    
    with open('.pythonrc', 'w', encoding='utf-8') as f:
        f.write(pythonrc_content)
    
    # 设置PYTHONSTARTUP环境变量
    pythonrc_path = os.path.abspath('.pythonrc')
    os.environ['PYTHONSTARTUP'] = pythonrc_path
    
    print("✅ Python优化设置完成")

def main():
    """主函数"""
    print("🚀 系统级性能优化")
    print("=" * 50)
    
    # 获取系统信息
    system_info = get_system_info()
    
    # 设置环境变量
    setup_environment_variables()
    
    # 创建启动脚本
    create_startup_script()
    
    # 创建性能监控
    create_performance_monitor()
    
    # 优化Python设置
    optimize_python_settings()
    
    print("\n🎉 系统级优化完成！")
    print("\n📋 使用说明：")
    print("1. 使用优化启动: optimized_start.bat main.py")
    print("2. 性能监控: python performance_monitor.py")
    print("3. 在代码中导入: from performance_monitor import start_performance_monitoring")

if __name__ == "__main__":
    main()
