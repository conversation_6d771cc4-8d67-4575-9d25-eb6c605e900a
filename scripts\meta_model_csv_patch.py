
# 🎯 CSV生成补丁：在元模型训练完成后保存CSV文件
def save_meta_model_training_data_to_csv(X_meta_df, y_meta_series, output_dir="models/meta_model_data"):
    """
    保存元模型训练数据到CSV文件
    
    Args:
        X_meta_df: 元模型特征DataFrame
        y_meta_series: 元模型目标变量Series
        output_dir: 输出目录
    """
    import os
    import logging
    
    logger = logging.getLogger(__name__)
    logger.info("💾 保存元模型训练数据到CSV文件...")
    
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存特征数据
        x_meta_csv_path = os.path.join(output_dir, "X_meta_features_oof.csv")
        X_meta_df.to_csv(x_meta_csv_path, index=True)
        logger.info(f"✅ 元模型特征数据已保存: {x_meta_csv_path}")
        
        # 保存目标变量
        y_meta_csv_path = os.path.join(output_dir, "y_meta_target.csv")
        y_meta_series.to_csv(y_meta_csv_path, index=True, header=True)
        logger.info(f"✅ 元模型目标变量已保存: {y_meta_csv_path}")
        
        # 保存数据摘要
        summary_path = os.path.join(output_dir, "meta_data_summary.json")
        summary = {
            "timestamp": pd.Timestamp.now().isoformat(),
            "samples_count": len(X_meta_df),
            "features_count": len(X_meta_df.columns),
            "feature_names": list(X_meta_df.columns),
            "target_distribution": y_meta_series.value_counts().to_dict(),
            "data_time_range": {
                "start": str(X_meta_df.index.min()),
                "end": str(X_meta_df.index.max())
            }
        }
        
        import json
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"✅ 数据摘要已保存: {summary_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 保存CSV文件失败: {e}")
        return False
