#!/usr/bin/env python3
"""
测试市场状态配置桥接功能
验证data_utils.py能否正确读取config.py中的MARKET_STATE_ADAPTIVE_FEATURES_CONFIG
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_reading():
    """测试配置读取功能"""
    logger.info("🔍 测试市场状态配置读取...")
    
    try:
        # 模拟data_utils.py中的配置读取函数
        def _get_market_state_config():
            """从全局config模块读取市场状态自适应特征配置"""
            try:
                market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
                return market_config
            except Exception as e:
                logger.warning(f"读取市场状态配置失败: {e}")
                return {}
        
        # 测试配置读取
        market_config = _get_market_state_config()
        
        if not market_config:
            logger.error("❌ 无法读取MARKET_STATE_ADAPTIVE_FEATURES_CONFIG")
            return False
        
        logger.info("✅ 成功读取市场状态配置")
        
        # 检查关键配置项
        enabled = market_config.get('enable', False)
        logger.info(f"启用状态: {enabled}")
        
        indicators = market_config.get('market_state_interaction_indicators', [])
        logger.info(f"交互指标数量: {len(indicators)}")
        
        states = market_config.get('market_state_interaction_states', [])
        logger.info(f"市场状态数量: {len(states)}")
        
        # 检查SHAP重要特征
        shap_features = [
            'meta_prob_diff_up_vs_down',
            'meta_prob_sum_up_down', 
            'global_ema_short',
            'global_mdi'
        ]
        
        found_shap = [f for f in shap_features if f in indicators]
        logger.info(f"包含的SHAP特征: {len(found_shap)}/{len(shap_features)}")
        for feature in found_shap:
            logger.info(f"  ✅ {feature}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置读取测试失败: {e}")
        return False

def test_feature_generation_simulation():
    """模拟特征生成过程"""
    logger.info("🧪 模拟市场状态自适应特征生成...")
    
    try:
        # 读取配置
        market_config = getattr(config, 'MARKET_STATE_ADAPTIVE_FEATURES_CONFIG', {})
        
        if not market_config.get('enable', False):
            logger.warning("⚠️ 市场状态自适应特征未启用")
            return False
        
        indicators = market_config.get('market_state_interaction_indicators', [])
        states = market_config.get('market_state_interaction_states', [])
        
        logger.info(f"将生成交互特征: {len(indicators)} × {len(states)} = {len(indicators) * len(states)}")
        
        # 模拟生成一些关键交互特征
        key_interactions = []
        
        # 重点关注SHAP重要特征
        shap_features = ['meta_prob_diff_up_vs_down', 'global_ema_short', 'global_mdi']
        key_states = ['strong_uptrend', 'bull_momentum', 'high_certainty']
        
        for feature in shap_features:
            if feature in indicators:
                for state in key_states:
                    if state in states:
                        interaction_name = f"{feature}_IN_{state}"
                        key_interactions.append(interaction_name)
        
        logger.info("关键交互特征示例:")
        for interaction in key_interactions[:10]:  # 显示前10个
            logger.info(f"  🚀 {interaction}")
        
        # 模拟创建DataFrame测试
        n_samples = 100
        test_df = pd.DataFrame(index=range(n_samples))
        
        # 模拟添加一些基础特征
        for indicator in indicators[:5]:  # 只测试前5个指标
            test_df[indicator] = np.random.randn(n_samples)
        
        # 模拟市场状态
        for state in states[:3]:  # 只测试前3个状态
            test_df[f"market_state_{state}"] = np.random.choice([0, 1], n_samples)
        
        # 模拟生成交互特征
        interaction_count = 0
        for indicator in indicators[:5]:
            if indicator in test_df.columns:
                for state in states[:3]:
                    state_col = f"market_state_{state}"
                    if state_col in test_df.columns:
                        interaction_col = f"{indicator}_IN_{state}"
                        test_df[interaction_col] = test_df[indicator] * test_df[state_col]
                        interaction_count += 1
        
        logger.info(f"✅ 成功模拟生成 {interaction_count} 个交互特征")
        logger.info(f"测试DataFrame形状: {test_df.shape}")
        
        # 显示一些生成的特征名称
        interaction_cols = [col for col in test_df.columns if '_IN_' in col]
        logger.info("生成的交互特征示例:")
        for col in interaction_cols[:5]:
            logger.info(f"  📈 {col}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 特征生成模拟失败: {e}")
        return False

def test_integration_with_data_utils():
    """测试与data_utils.py的集成"""
    logger.info("🔗 测试与data_utils.py的集成...")
    
    try:
        # 尝试导入data_utils模块
        from src.core.data_utils import _add_market_state_adaptive_features
        logger.info("✅ 成功导入_add_market_state_adaptive_features函数")
        
        # 检查函数是否包含我们添加的配置读取代码
        import inspect
        source = inspect.getsource(_add_market_state_adaptive_features)
        
        # 检查关键代码片段
        key_snippets = [
            "_get_market_state_config",
            "MARKET_STATE_ADAPTIVE_FEATURES_CONFIG",
            "market_state_interaction_indicators",
            "market_state_interaction_states"
        ]
        
        found_snippets = []
        for snippet in key_snippets:
            if snippet in source:
                found_snippets.append(snippet)
        
        logger.info(f"函数中包含的关键代码片段: {len(found_snippets)}/{len(key_snippets)}")
        for snippet in found_snippets:
            logger.info(f"  ✅ {snippet}")
        
        missing_snippets = set(key_snippets) - set(found_snippets)
        if missing_snippets:
            logger.warning(f"  ⚠️ 缺失的代码片段: {missing_snippets}")
        
        return len(found_snippets) == len(key_snippets)
        
    except Exception as e:
        logger.error(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始测试市场状态配置桥接功能...")
    
    try:
        # 1. 测试配置读取
        config_ok = test_config_reading()
        
        # 2. 模拟特征生成
        generation_ok = test_feature_generation_simulation()
        
        # 3. 测试与data_utils的集成
        integration_ok = test_integration_with_data_utils()
        
        # 总结结果
        if config_ok and generation_ok and integration_ok:
            logger.info("🎉 市场状态配置桥接功能测试全部通过！")
            logger.info("📋 配置修改已生效，重新训练模型时将使用新的自适应特征")
            return True
        else:
            logger.warning("⚠️ 部分测试失败，请检查上述错误")
            logger.info("测试结果:")
            logger.info(f"  配置读取: {'✅' if config_ok else '❌'}")
            logger.info(f"  特征生成: {'✅' if generation_ok else '❌'}")
            logger.info(f"  代码集成: {'✅' if integration_ok else '❌'}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 市场状态配置桥接功能测试成功！")
        print("📋 现在可以重新训练模型以应用强化的自适应特征")
    else:
        print("\n❌ 配置桥接功能测试失败！")
