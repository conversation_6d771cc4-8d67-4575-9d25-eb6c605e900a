#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 日志系统兼容层 V1.0
为现有代码提供无缝的日志系统升级支持

核心功能：
- 提供与旧系统完全兼容的接口
- 自动路由到新的统一综合日志系统
- 保持现有代码调用方式不变
- 支持渐进式迁移
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, Union
from .unified_comprehensive_logger import get_unified_comprehensive_logger


class UnifiedTradeLoggerCompatibility:
    """
    统一交易日志记录器兼容类
    提供与原 UnifiedTradeLogger 完全相同的接口
    """
    
    def __init__(self, base_log_dir: str = "trading_logs_unified", auto_start: bool = True):
        """
        🎯 初始化兼容层 - 修改为使用简化格式并保持原目录

        Args:
            base_log_dir: 日志基础目录（保持原目录不变）
            auto_start: 是否自动启动（兼容参数）
        """
        self.base_log_dir = base_log_dir
        self.logger = logging.getLogger(f"{__name__}.UnifiedTradeLoggerCompatibility")

        # 🎯 直接使用简化的UnifiedTradeLogger，而不是重定向到comprehensive_logs
        from .unified_trade_logger import UnifiedTradeLogger
        self.unified_logger = UnifiedTradeLogger(
            base_log_dir=base_log_dir,  # 保持原目录
            auto_start=auto_start
        )

        self.logger.info(f"兼容层初始化完成，使用简化格式写入: {base_log_dir}")
    
    def record_trade_entry(self, target_name: str, symbol: str, direction: str,
                          entry_price: float, amount: float, payout_ratio: float = 0.85,
                          trade_id: Optional[str] = None, 
                          context_data: Optional[Dict[str, Any]] = None) -> str:
        """
        记录交易开仓信息（兼容接口）
        
        Args:
            target_name: 目标策略名称
            symbol: 交易对符号
            direction: 交易方向
            entry_price: 开仓价格
            amount: 交易金额
            payout_ratio: 盈利比例
            trade_id: 交易ID（可选）
            context_data: 上下文数据（可选）
        
        Returns:
            交易ID
        """
        # 🎯 使用简化的UnifiedTradeLogger
        return self.unified_logger.record_trade_entry(
            target_name=target_name,
            symbol=symbol,
            direction=direction,
            entry_price=entry_price,
            amount=amount,
            payout_ratio=payout_ratio,
            context_data=context_data
        )
    
    def record_trade_exit(self, trade_id: str, exit_price: float,
                         result: str, exit_reason: str = "expired") -> bool:
        """
        记录交易平仓信息（兼容接口）
        
        Args:
            trade_id: 交易ID
            exit_price: 平仓价格
            result: 交易结果 (WIN 或 LOSS)
            exit_reason: 平仓原因
        
        Returns:
            是否成功记录
        """
        # 🎯 使用简化的UnifiedTradeLogger
        return self.unified_logger.record_trade_exit(
            trade_id=trade_id,
            exit_price=exit_price,
            result=result,
            exit_reason=exit_reason
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息（兼容接口）"""
        stats = self.comprehensive_logger.get_statistics()
        
        # 转换为旧格式
        basic_stats = stats.get('basic_stats', {})
        return {
            'total_entries': basic_stats.get('total_entries', 0),
            'total_exits': basic_stats.get('total_exits', 0),
            'total_writes': sum(
                layer_stats.get('total_writes', 0) 
                for layer_stats in stats.get('writer_stats', {}).values()
            ),
            'failed_writes': sum(
                layer_stats.get('failed_writes', 0) 
                for layer_stats in stats.get('writer_stats', {}).values()
            ),
            'pending_trades_count': basic_stats.get('pending_trades_count', 0)
        }
    
    def stop(self, timeout: float = 5.0):
        """停止日志系统（兼容接口）"""
        self.comprehensive_logger.stop(timeout)
    
    def flush(self, timeout: float = 5.0):
        """刷新队列（兼容接口）"""
        self.comprehensive_logger.flush_all_queues(timeout)


# 全局兼容实例管理
_global_compatibility_logger = None
_compatibility_logger_lock = None


def get_unified_trade_logger(base_log_dir: str = "trading_logs_unified",
                           auto_start: bool = True) -> UnifiedTradeLoggerCompatibility:
    """
    🎯 获取统一交易日志记录器（兼容接口）- 强制使用简化格式

    这个函数确保所有调用都使用简化的28字段格式，写入trading_logs_unified目录

    Args:
        base_log_dir: 日志基础目录（强制使用trading_logs_unified）
        auto_start: 是否自动启动

    Returns:
        使用简化格式的日志记录器实例
    """
    global _global_compatibility_logger, _compatibility_logger_lock
    
    if _compatibility_logger_lock is None:
        import threading
        _compatibility_logger_lock = threading.Lock()
    
    with _compatibility_logger_lock:
        if _global_compatibility_logger is None:
            # 🎯 强制使用trading_logs_unified目录和简化格式
            _global_compatibility_logger = UnifiedTradeLoggerCompatibility(
                base_log_dir="trading_logs_unified",  # 强制使用简化格式目录
                auto_start=auto_start
            )
            print(f"[兼容层] 强制使用简化格式，目录: trading_logs_unified")

        return _global_compatibility_logger


def reset_unified_logger():
    """重置全局日志记录器（兼容接口）"""
    global _global_compatibility_logger
    
    if _compatibility_logger_lock is None:
        import threading
        _compatibility_logger_lock = threading.Lock()
    
    with _compatibility_logger_lock:
        if _global_compatibility_logger:
            _global_compatibility_logger.stop()
        _global_compatibility_logger = None


# 分析日志兼容函数
def log_prediction_context(target_name: str, symbol: str, signal_data: Dict[str, Any],
                          market_data: Dict[str, Any], model_data: Dict[str, Any],
                          filter_data: Dict[str, Any]) -> bool:
    """
    🚫 已废弃：重定向到简化的UnifiedTradeLogger

    为了确保只使用简化的28字段日志格式，此函数现在被忽略

    Args:
        target_name: 目标策略名称
        symbol: 交易对符号
        signal_data: 信号数据
        market_data: 市场数据
        model_data: 模型数据
        filter_data: 过滤器数据

    Returns:
        是否成功记录
    """
    import warnings
    warnings.warn(
        "log_prediction_context已废弃，系统自动重定向到简化的UnifiedTradeLogger",
        DeprecationWarning,
        stacklevel=2
    )

    # 🎯 重定向到简化的UnifiedTradeLogger - 不记录预测上下文，只在实际交易时记录
    print(f"[废弃警告] log_prediction_context调用被忽略，只在实际交易时记录到简化格式")
    return True  # 返回成功以保持兼容性


def log_trade_settlement(trade_obj, market_snapshot_entry=None, 
                        market_snapshot_exit=None, related_prediction_id=None) -> bool:
    """
    记录交易结算信息（兼容接口）
    
    Args:
        trade_obj: 交易对象
        market_snapshot_entry: 开仓时市场快照
        market_snapshot_exit: 平仓时市场快照
        related_prediction_id: 相关预测ID
    
    Returns:
        是否成功记录
    """
    logger = get_unified_comprehensive_logger()
    
    # 转换为新格式
    trade_data = {
        'trade_id': getattr(trade_obj, 'trade_id', str(trade_obj.trade_id)),
        'entry_timestamp': trade_obj.entry_time.isoformat() if hasattr(trade_obj.entry_time, 'isoformat') else str(trade_obj.entry_time),
        'exit_timestamp': datetime.now().isoformat(),
        'target_name': getattr(trade_obj, 'target_name', 'Unknown'),
        'symbol': getattr(trade_obj, 'symbol', 'BTCUSDT'),
        'direction': trade_obj.direction,
        'entry_price': trade_obj.entry_price,
        'exit_price': getattr(trade_obj, 'exit_price', 0),
        'amount': getattr(trade_obj, 'amount_staked', 0),
        'payout_ratio': getattr(trade_obj, 'PAYOUT_RATIO', 0.85),
        'result': 'WIN' if getattr(trade_obj, 'status', 'LOST') == 'WON' else 'LOSS',
        'profit_loss': getattr(trade_obj, 'profit_loss', 0),
        'exit_reason': 'expired',
        'entry_market_snapshot_json': json.dumps(market_snapshot_entry or {}),
        'exit_market_snapshot_json': json.dumps(market_snapshot_exit or {}),
        'related_prediction_id': related_prediction_id
    }
    
    return logger.log_complete_trade(trade_data)


def initialize_loggers():
    """
    初始化日志记录器（兼容接口）
    
    这个函数保持与原 analysis_logger.initialize_loggers() 的兼容性
    """
    # 获取统一综合日志系统，这会自动初始化所有必要的组件
    logger = get_unified_comprehensive_logger()
    
    print("[CompatibilityLayer] 统一综合日志系统已初始化")
    print("[CompatibilityLayer] 兼容层已激活，现有代码无需修改")
    
    return True


# 失败案例分析兼容函数
def analyze_failure_patterns(days_back: int = 7) -> Optional[Dict[str, Any]]:
    """
    分析失败模式（兼容接口）
    
    Args:
        days_back: 回溯天数
    
    Returns:
        分析结果
    """
    logger = get_unified_comprehensive_logger()
    return logger.analyze_failures(days_back)


def generate_comprehensive_report(days_back: int = 7) -> Optional[Dict[str, Any]]:
    """
    生成综合报告（兼容接口）
    
    Args:
        days_back: 回溯天数
    
    Returns:
        报告结果
    """
    logger = get_unified_comprehensive_logger()
    return logger.analyze_failures(days_back)


# 导入兼容性支持
# 这些导入语句让现有代码可以继续使用原来的导入方式
from .unified_comprehensive_logger import get_unified_comprehensive_logger

# 为了完全兼容，我们也提供原始模块的别名
UnifiedTradeLogger = UnifiedTradeLoggerCompatibility
