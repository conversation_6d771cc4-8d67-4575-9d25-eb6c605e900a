# 升级版 TradeLogger 使用指南

## 概述

升级版 TradeLogger 在原有功能基础上，增加了完整的模型决策上下文信息记录功能，帮助您深入分析每笔交易的决策过程。

## 新增功能

### 1. 完整的CSV字段结构

升级后的CSV文件包含 **21个字段**，按逻辑分为4组：

#### 基础交易信息 (13个字段)
```
trade_id, entry_timestamp, exit_timestamp, target_name, symbol, direction, 
entry_price, exit_price, amount, payout_ratio, result, profit_loss, exit_reason
```

#### 模型预测信息 (4个字段)
```
entry_signal_probability     # 信号方向预测概率
entry_neutral_probability    # 中性预测概率
entry_opposite_probability   # 相反方向预测概率
meta_model_inputs           # 元模型输入特征(JSON格式)
```

#### 市场状态信息 (3个字段)
```
entry_market_regime         # 市场状态 (如: strong_uptrend, sideways_volatile)
entry_atr_percent          # ATR波动率百分比
entry_adx_value            # ADX趋势强度值
```

#### 核心特征快照 (1个字段)
```
entry_top_features         # Top 10重要特征值(JSON格式)
```

### 2. 升级的API接口

#### record_trade_entry() 方法新增参数

```python
def record_trade_entry(self, 
                      target_name: str,
                      symbol: str,
                      direction: str,
                      entry_price: float,
                      amount: float,
                      payout_ratio: float = 0.85,
                      trade_id: Optional[str] = None,
                      context_data: Optional[Dict[str, Any]] = None) -> str:
```

**新增的 `context_data` 参数结构：**

```python
context_data = {
    # 模型预测信息
    "signal_probability": 0.75,        # 信号方向预测概率
    "neutral_probability": 0.15,       # 中性预测概率  
    "opposite_probability": 0.10,      # 相反方向预测概率
    "meta_model_inputs": {             # 元模型输入特征字典
        "base_model_1_prob": 0.72,
        "base_model_2_prob": 0.68,
        "ensemble_confidence": 0.85,
        "volatility_regime": "medium"
    },
    
    # 市场状态信息
    "market_regime": "strong_uptrend",  # 市场状态字符串
    "atr_percent": 2.34,               # ATR波动率百分比
    "adx_value": 45.6,                 # ADX趋势强度值
    
    # 核心特征快照
    "top_features": {                   # Top 10重要特征字典
        "ma_cross_signal": 0.0234,
        "rsi_divergence": -0.0156,
        "volume_surge": 0.0189,
        "support_resistance": 0.0098,
        "momentum_indicator": 0.0145
    }
}
```

## 使用示例

### 1. 基础使用（向后兼容）

```python
from src.core.trade_logger import get_trade_logger

# 获取日志记录器
trade_logger = get_trade_logger("results/logs/trade_log.csv")

# 基础交易记录（无上下文数据）
trade_id = trade_logger.record_trade_entry(
    target_name="BTC_15m_Basic",
    symbol="BTCUSDT",
    direction="LONG",
    entry_price=50000.0,
    amount=10.0
)

# 记录平仓
trade_logger.record_trade_exit(
    trade_id=trade_id,
    exit_price=51000.0,
    result="WIN"
)
```

### 2. 增强使用（完整上下文）

```python
from src.core.trade_logger import get_trade_logger

# 构建完整上下文数据
context_data = {
    # 模型预测信息
    "signal_probability": 0.75,
    "neutral_probability": 0.15,
    "opposite_probability": 0.10,
    "meta_model_inputs": {
        "base_model_1": 0.72,
        "base_model_2": 0.68,
        "ensemble_confidence": 0.85
    },
    
    # 市场状态信息
    "market_regime": "strong_uptrend",
    "atr_percent": 2.34,
    "adx_value": 45.6,
    
    # 核心特征快照
    "top_features": {
        "ma_cross_signal": 0.0234,
        "rsi_divergence": -0.0156,
        "volume_surge": 0.0189
    }
}

# 获取日志记录器
trade_logger = get_trade_logger("results/logs/enhanced_trade_log.csv")

# 增强交易记录（包含完整上下文）
trade_id = trade_logger.record_trade_entry(
    target_name="BTC_15m_Enhanced",
    symbol="BTCUSDT",
    direction="LONG",
    entry_price=50000.0,
    amount=15.0,
    context_data=context_data  # 传入完整上下文
)

# 记录平仓
trade_logger.record_trade_exit(
    trade_id=trade_id,
    exit_price=51500.0,
    result="WIN"
)
```

### 3. 与预测系统集成

```python
def run_prediction_cycle_for_target(target_name: str):
    """
    在 prediction.py 中的集成示例
    """
    # ... 执行预测逻辑 ...
    
    # 收集模型预测信息
    prediction_probs = model.predict_proba(features)  # [DOWN, NEUTRAL, UP]
    signal_direction = "UP" if prediction_probs[2] > 0.7 else None
    
    # 收集市场状态信息
    market_regime = get_current_market_regime()
    atr_percent = calculate_atr_percentage()
    adx_value = get_adx_value()
    
    # 收集核心特征快照
    top_features = get_shap_top_features(features, n_features=10)
    
    # 如果信号确认，记录交易
    if signal_direction and prediction_probs[2] > 0.7:
        context_data = {
            "signal_probability": prediction_probs[2],
            "neutral_probability": prediction_probs[1],
            "opposite_probability": prediction_probs[0],
            "meta_model_inputs": get_meta_model_inputs(),
            "market_regime": market_regime,
            "atr_percent": atr_percent,
            "adx_value": adx_value,
            "top_features": top_features
        }
        
        trade_logger = get_trade_logger("results/logs/prediction_trades.csv")
        trade_id = trade_logger.record_trade_entry(
            target_name=target_name,
            symbol="BTCUSDT",
            direction=signal_direction,
            entry_price=current_price,
            amount=calculate_position_size(),
            context_data=context_data
        )
        
        return trade_id
    
    return None
```

## CSV输出示例

```csv
trade_id,entry_timestamp,exit_timestamp,target_name,symbol,direction,entry_price,exit_price,amount,payout_ratio,result,profit_loss,exit_reason,entry_signal_probability,entry_neutral_probability,entry_opposite_probability,meta_model_inputs,entry_market_regime,entry_atr_percent,entry_adx_value,entry_top_features
BTC_15m_Enhanced_1751705176184,2025-07-05T16:46:16.184734,2025-07-05T16:46:16.185734,BTC_15m_Enhanced,BTCUSDT,LONG,50000.0,51000.0,15.0,0.85,WIN,12.75,expired,0.75,0.15,0.1,"{""base_model_1"": 0.72, ""ensemble_confidence"": 0.85}",strong_uptrend,2.34,45.6,"{""ma_cross_signal"": 0.0234, ""rsi_divergence"": -0.0156}"
```

## 数据分析优势

### 1. 模型性能分析
- 通过 `entry_signal_probability` 分析不同置信度下的胜率
- 通过 `meta_model_inputs` 分析各子模型的贡献度

### 2. 市场环境分析
- 通过 `entry_market_regime` 分析不同市场状态下的表现
- 通过 `entry_atr_percent` 和 `entry_adx_value` 分析波动率和趋势强度的影响

### 3. 特征重要性分析
- 通过 `entry_top_features` 分析哪些特征在成功/失败交易中最重要
- 结合SHAP值进行特征贡献度分析

### 4. 复盘分析示例

```python
import pandas as pd
import json

# 读取交易日志
df = pd.read_csv("results/logs/enhanced_trade_log.csv")

# 分析高置信度交易的表现
high_confidence = df[df['entry_signal_probability'] > 0.8]
print(f"高置信度交易胜率: {(high_confidence['result'] == 'WIN').mean():.2%}")

# 分析不同市场状态下的表现
market_performance = df.groupby('entry_market_regime')['result'].apply(
    lambda x: (x == 'WIN').mean()
).sort_values(ascending=False)
print("不同市场状态胜率:")
print(market_performance)

# 分析失败交易的特征
failed_trades = df[df['result'] == 'LOSS']
for _, trade in failed_trades.iterrows():
    print(f"失败交易 {trade['trade_id']}:")
    print(f"  信号概率: {trade['entry_signal_probability']:.3f}")
    print(f"  市场状态: {trade['entry_market_regime']}")
    if pd.notna(trade['entry_top_features']):
        features = json.loads(trade['entry_top_features'])
        print(f"  关键特征: {list(features.keys())[:3]}")
```

## 向后兼容性

- 所有原有的API调用方式完全兼容
- 不传入 `context_data` 时，上下文字段将为空值
- 现有的交易记录和分析代码无需修改

## 注意事项

1. **JSON字段处理**: `meta_model_inputs` 和 `entry_top_features` 字段以JSON字符串存储，读取时需要使用 `json.loads()` 解析
2. **文件大小**: 由于增加了上下文信息，CSV文件会比原来大，建议定期归档
3. **性能影响**: JSON序列化会有轻微的性能开销，但对整体系统影响很小
4. **数据完整性**: 建议在 `prediction.py` 中确保上下文数据的完整性和准确性
