# EnhancedPriceFetcher.py
"""
增强的价格获取器，使用WebSocketConnectionManager
提供更稳定的价格数据获取，支持自动重连和降级机制
"""

import time
import os
import threading
import logging
import sys
from typing import Optional, Callable

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from core.websocket_manager import WebSocketConnectionManager, ConnectionState

logger = logging.getLogger(__name__)

class EnhancedPriceFetcher:
    """
    增强的价格获取器
    
    功能：
    - 使用WebSocketConnectionManager进行稳定的价格获取
    - 自动重连和错误恢复
    - 降级机制（WebSocket失败时使用REST API）
    - 连接状态监控
    - 价格更新回调
    """
    
    def __init__(self, 
                 symbol: str = "BTCUSDT",
                 proxy_url: str = 'http://127.0.0.1:7897',
                 price_callback: Optional[Callable[[float], None]] = None,
                 state_callback: Optional[Callable[[ConnectionState], None]] = None):
        
        self.symbol = symbol.upper()
        self.proxy_url = proxy_url
        
        # 价格数据
        self.latest_price = None
        self.last_update_time = 0
        self.price_lock = threading.RLock()
        
        # 连接管理器
        self.connection_manager = WebSocketConnectionManager(
            symbol=self.symbol,
            proxy_url=self.proxy_url,
            max_reconnect_attempts=20,
            initial_retry_delay=1.0,
            max_retry_delay=60.0,
            health_check_interval=30.0,
            degraded_mode_poll_interval=5.0
        )
        
        # 设置回调函数
        self.connection_manager.set_price_callback(self._on_price_update)
        if state_callback:
            self.connection_manager.set_state_callback(state_callback)
        
        # 外部回调
        self.external_price_callback = price_callback
        
        # 状态
        self.is_running = False
        
        logger.info(f"增强价格获取器初始化完成 - 交易对: {self.symbol}")
    
    def _on_price_update(self, price: float):
        """内部价格更新回调"""
        with self.price_lock:
            self.latest_price = price
            self.last_update_time = time.time()
        
        # 调用外部回调
        if self.external_price_callback:
            try:
                self.external_price_callback(price)
            except Exception as e:
                logger.error(f"外部价格回调执行失败: {e}")
    
    def set_price_callback(self, callback: Callable[[float], None]):
        """设置价格更新回调函数"""
        self.external_price_callback = callback
    
    def set_state_callback(self, callback: Callable[[ConnectionState], None]):
        """设置连接状态回调函数"""
        self.connection_manager.set_state_callback(callback)
    
    def start_stream(self) -> bool:
        """启动价格数据流"""
        if self.is_running:
            logger.warning(f"价格获取器已在运行 - {self.symbol}")
            return True
        
        logger.info(f"启动价格数据流 - {self.symbol}")
        
        if self.connection_manager.start():
            self.is_running = True
            logger.info(f"价格数据流启动成功 - {self.symbol}")
            return True
        else:
            logger.error(f"价格数据流启动失败 - {self.symbol}")
            return False
    
    def stop_stream(self):
        """停止价格数据流"""
        if not self.is_running:
            logger.warning(f"价格获取器未运行 - {self.symbol}")
            return
        
        logger.info(f"停止价格数据流 - {self.symbol}")
        
        self.connection_manager.stop()
        self.is_running = False
        
        logger.info(f"价格数据流已停止 - {self.symbol}")
    
    def get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        with self.price_lock:
            return self.latest_price
    
    def get_last_update_time(self) -> float:
        """获取最后更新时间"""
        with self.price_lock:
            return self.last_update_time
    
    def get_connection_state(self) -> ConnectionState:
        """获取连接状态"""
        return self.connection_manager.get_connection_state()
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = self.connection_manager.get_stats()
        
        with self.price_lock:
            stats.update({
                'latest_price': self.latest_price,
                'last_update_time': self.last_update_time,
                'is_running': self.is_running
            })
        
        return stats
    
    def force_reconnect(self):
        """强制重连"""
        logger.info(f"强制重连请求 - {self.symbol}")
        self.connection_manager.force_reconnect()
    
    def reset_to_websocket(self):
        """尝试从降级模式恢复到WebSocket模式"""
        return self.connection_manager.reset_to_websocket()
    
    def is_price_fresh(self, max_age_seconds: float = 60.0) -> bool:
        """检查价格数据是否新鲜"""
        if not self.latest_price:
            return False
        
        current_time = time.time()
        with self.price_lock:
            age = current_time - self.last_update_time
        
        return age <= max_age_seconds
    
    def wait_for_price(self, timeout_seconds: float = 30.0) -> Optional[float]:
        """等待价格数据"""
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            price = self.get_current_price()
            if price is not None:
                return price
            time.sleep(0.1)
        
        logger.warning(f"等待价格数据超时 - {self.symbol}")
        return None


# 测试代码
if __name__ == "__main__":
    import logging
    from datetime import datetime
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    def price_callback(price: float):
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"[{current_time}] 价格更新: {price:.2f}")
    
    def state_callback(state: ConnectionState):
        print(f"连接状态变化: {state.value}")
    
    # 创建增强价格获取器
    fetcher = EnhancedPriceFetcher(
        symbol="BTCUSDT",
        price_callback=price_callback,
        state_callback=state_callback
    )
    
    try:
        # 启动价格流
        if fetcher.start_stream():
            print("价格流启动成功")
            
            # 运行测试
            for i in range(120):  # 运行2分钟
                time.sleep(1)
                
                price = fetcher.get_current_price()
                state = fetcher.get_connection_state()
                
                if i % 10 == 0:  # 每10秒打印一次状态
                    stats = fetcher.get_stats()
                    print(f"状态: {state.value}, 价格: {price}, 重连次数: {stats['total_reconnects']}")
                
                # 测试强制重连（在第30秒）
                if i == 30:
                    print("测试强制重连...")
                    fetcher.force_reconnect()
        
        else:
            print("价格流启动失败")
    
    except KeyboardInterrupt:
        print("用户中断")
    
    finally:
        # 停止价格流
        fetcher.stop_stream()
        print("测试结束")
