# 数据泄露预防最佳实践指南

## 🎯 概述

本指南提供了在量化交易系统开发中预防数据泄露的最佳实践，确保模型的可靠性和实际交易性能的一致性。

## 🔍 数据泄露的类型和识别

### 1. 时间泄露（Temporal Leakage）

**定义**: 使用未来时间点的信息来预测当前或过去的结果

**常见形式**:
```python
# ❌ 错误示例
df['future_price'] = df['close'].shift(-1)  # 使用未来价格
df['target'] = (df['future_price'] > df['close']).astype(int)

# ✅ 正确示例  
df['past_price'] = df['close'].shift(1)  # 使用历史价格
df['price_change'] = df['close'].pct_change()  # 当前变化
```

### 2. 统计泄露（Statistical Leakage）

**定义**: 使用全局统计量进行特征缩放或填充

**常见形式**:
```python
# ❌ 错误示例
df['normalized'] = (df['price'] - df['price'].mean()) / df['price'].std()

# ✅ 正确示例
df['normalized'] = df['price'].rolling(20).apply(
    lambda x: (x.iloc[-1] - x.mean()) / x.std() if len(x) > 1 else 0
)
```

### 3. 目标泄露（Target Leakage）

**定义**: 特征中包含目标变量的信息

**常见形式**:
```python
# ❌ 错误示例
df['price_vs_target'] = df['close'] / df['target_price']

# ✅ 正确示例
df['price_vs_sma'] = df['close'] / df['close'].rolling(20).mean()
```

## 🛡️ 预防策略

### 1. 严格的时间序列处理

#### 时间顺序验证
```python
def validate_time_order(df):
    """验证数据的时间顺序"""
    if not df.index.is_monotonic_increasing:
        raise ValueError("数据未按时间排序")
    
    # 检查时间间隔
    time_diffs = df.index.to_series().diff().dropna()
    expected_interval = pd.Timedelta('15T')
    
    abnormal_gaps = time_diffs[time_diffs > expected_interval * 3]
    if len(abnormal_gaps) > 0:
        print(f"警告: 发现 {len(abnormal_gaps)} 个异常时间间隔")
```

#### 安全的特征计算
```python
def safe_technical_indicator(prices, window):
    """安全的技术指标计算"""
    # 使用expanding window而不是全局统计
    return prices.rolling(window=window, min_periods=1).mean()

def safe_zscore(series, window=20):
    """安全的Z-score计算"""
    rolling_mean = series.rolling(window).mean()
    rolling_std = series.rolling(window).std()
    return (series - rolling_mean) / rolling_std
```

### 2. 安全的数据填充策略

#### NaN值处理
```python
def safe_fill_nans(series, method='ffill', default_value=0):
    """安全的NaN填充"""
    if method == 'ffill':
        # 向前填充（使用历史数据）
        filled = series.ffill()
        # 对于开头的NaN，使用默认值
        return filled.fillna(default_value)
    
    elif method == 'rolling_mean':
        # 使用滚动均值
        rolling_mean = series.rolling(20, min_periods=1).mean()
        return series.fillna(rolling_mean)
    
    else:
        return series.fillna(default_value)
```

#### 异常值处理
```python
def safe_outlier_clipping(series, method='iqr', window=100):
    """安全的异常值处理"""
    if method == 'iqr':
        # 使用滚动IQR
        rolling_q25 = series.rolling(window).quantile(0.25)
        rolling_q75 = series.rolling(window).quantile(0.75)
        rolling_iqr = rolling_q75 - rolling_q25
        
        lower_bound = rolling_q25 - 1.5 * rolling_iqr
        upper_bound = rolling_q75 + 1.5 * rolling_iqr
        
        return series.clip(lower_bound, upper_bound)
    
    elif method == 'zscore':
        # 使用滚动Z-score
        rolling_mean = series.rolling(window).mean()
        rolling_std = series.rolling(window).std()
        
        z_scores = (series - rolling_mean) / rolling_std
        return series.where(abs(z_scores) <= 3, rolling_mean)
```

### 3. 正确的训练/测试分割

#### 时间序列分割
```python
from sklearn.model_selection import TimeSeriesSplit

def safe_time_series_split(X, y, n_splits=5, test_size=0.2):
    """安全的时间序列分割"""
    
    # 确保数据按时间排序
    if not X.index.is_monotonic_increasing:
        X = X.sort_index()
        y = y.sort_index()
    
    # 使用TimeSeriesSplit
    tscv = TimeSeriesSplit(n_splits=n_splits)
    
    for train_idx, test_idx in tscv.split(X):
        X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
        y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
        
        # 验证时间顺序
        assert X_train.index.max() < X_test.index.min(), "训练集和测试集时间重叠"
        
        yield X_train, X_test, y_train, y_test
```

#### 独立的特征缩放
```python
def safe_feature_scaling(X_train, X_test, method='standard'):
    """安全的特征缩放"""
    from sklearn.preprocessing import StandardScaler, RobustScaler
    
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'robust':
        scaler = RobustScaler()
    
    # 只在训练集上拟合
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)  # 使用训练集的参数
    
    return X_train_scaled, X_test_scaled, scaler
```

## 🔧 实施工具和检查

### 1. 自动化检查函数

```python
def check_data_leakage(df, feature_cols, target_col):
    """自动检查数据泄露"""
    
    issues = []
    
    # 检查1: 特征名称
    future_keywords = ['future', 'next', 'ahead', 'forward', 'target']
    for col in feature_cols:
        if any(keyword in col.lower() for keyword in future_keywords):
            issues.append(f"特征名称可疑: {col}")
    
    # 检查2: 特征与目标的异常相关性
    for col in feature_cols:
        if col in df.columns and target_col in df.columns:
            corr = df[col].corr(df[target_col])
            if abs(corr) > 0.95:
                issues.append(f"特征 {col} 与目标异常相关: {corr:.3f}")
    
    # 检查3: 时间顺序
    if not df.index.is_monotonic_increasing:
        issues.append("数据未按时间排序")
    
    return issues
```

### 2. 持续监控

```python
class DataLeakageMonitor:
    """数据泄露监控器"""
    
    def __init__(self):
        self.checks = []
    
    def add_check(self, check_func, name):
        """添加检查函数"""
        self.checks.append((name, check_func))
    
    def run_all_checks(self, data):
        """运行所有检查"""
        results = {}
        
        for name, check_func in self.checks:
            try:
                result = check_func(data)
                results[name] = {
                    'passed': len(result) == 0,
                    'issues': result
                }
            except Exception as e:
                results[name] = {
                    'passed': False,
                    'error': str(e)
                }
        
        return results

# 使用示例
monitor = DataLeakageMonitor()
monitor.add_check(
    lambda df: check_data_leakage(df, feature_cols, target_col),
    'basic_leakage_check'
)
```

## 📋 开发流程集成

### 1. 代码审查清单

在每次代码提交前检查：

- [ ] 所有特征计算只使用历史数据
- [ ] NaN填充使用安全方法（ffill + 默认值）
- [ ] 异常值处理使用滚动统计量
- [ ] 特征缩放在训练/测试分割后进行
- [ ] 时间序列分割正确实施
- [ ] 没有使用全局统计量

### 2. 自动化测试

```python
def test_no_data_leakage():
    """数据泄露测试"""
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 生成特征
    features = generate_features(test_data)
    
    # 运行检查
    issues = check_data_leakage(features, feature_cols, target_col)
    
    assert len(issues) == 0, f"发现数据泄露: {issues}"

def test_time_series_integrity():
    """时间序列完整性测试"""
    
    # 测试数据分割
    X_train, X_test, y_train, y_test = safe_time_series_split(X, y)
    
    # 验证时间顺序
    assert X_train.index.max() < X_test.index.min()
```

### 3. CI/CD集成

```yaml
# .github/workflows/data_leakage_check.yml
name: Data Leakage Check

on: [push, pull_request]

jobs:
  check-leakage:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Run data leakage checks
      run: python scripts/validate_data_leakage_fix.py
```

## 🎯 最佳实践总结

### 核心原则

1. **时间因果性**: 只使用当前时间点之前的信息
2. **独立性**: 训练集和测试集完全独立
3. **一致性**: 特征工程在训练和推理时保持一致
4. **透明性**: 所有数据处理步骤可追溯和验证

### 实施检查清单

#### 特征工程阶段
- [ ] 使用滚动窗口而非全局统计
- [ ] NaN填充只使用历史数据
- [ ] 异常值处理使用滚动阈值
- [ ] 特征名称不包含未来信息关键词

#### 模型训练阶段
- [ ] 严格的时间序列分割
- [ ] 独立的特征缩放
- [ ] 交叉验证保持时间顺序
- [ ] 超参数优化在正确的数据分割上进行

#### 模型评估阶段
- [ ] 使用时间序列交叉验证
- [ ] 性能指标在合理范围内
- [ ] 特征重要性分析合理
- [ ] 模型在不同时间段表现一致

#### 生产部署阶段
- [ ] 特征计算管道与训练时一致
- [ ] 实时数据处理无未来信息
- [ ] 持续监控模型性能
- [ ] 定期检查数据泄露

通过遵循这些最佳实践，可以有效防止数据泄露，确保量化交易模型的可靠性和实际性能的一致性。
