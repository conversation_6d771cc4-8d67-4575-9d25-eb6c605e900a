#!/usr/bin/env python3
"""
修复元模型CSV文件生成问题
确保X_meta_features_oof.csv和y_meta_target.csv能够正确生成
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_current_meta_model_training_method():
    """检查当前元模型训练方法"""
    logger.info("🔍 检查当前元模型训练方法...")
    
    # 检查是否使用优化的数据准备
    try:
        from src.training.optimized_meta_data_preparation import prepare_meta_training_data_optimized
        logger.info("✅ 优化的数据准备模块可用")
        
        # 检查是否在prediction.py中被调用
        with open("src/core/prediction.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "prepare_meta_training_data_optimized" in content:
            logger.info("✅ prediction.py已集成优化的数据准备")
        else:
            logger.warning("⚠️ prediction.py未集成优化的数据准备，这是CSV文件不生成的原因")
            
        return True
        
    except ImportError as e:
        logger.error(f"❌ 优化的数据准备模块不可用: {e}")
        return False

def check_meta_model_data_directory():
    """检查元模型数据目录"""
    logger.info("📁 检查元模型数据目录...")
    
    meta_dir = getattr(config, 'META_MODEL_SAVE_DIR', 'meta_model_data')
    full_path = os.path.join("models", meta_dir) if not meta_dir.startswith("models") else meta_dir
    
    logger.info(f"元模型数据目录: {full_path}")
    
    if os.path.exists(full_path):
        logger.info("✅ 目录存在")
        
        # 检查现有文件
        files = os.listdir(full_path)
        logger.info(f"目录中的文件: {files}")
        
        # 检查是否有CSV文件
        csv_files = [f for f in files if f.endswith('.csv')]
        if csv_files:
            logger.info(f"✅ 发现CSV文件: {csv_files}")
        else:
            logger.warning("⚠️ 未发现CSV文件")
            
        # 检查目标CSV文件
        target_files = ['X_meta_features_oof.csv', 'y_meta_target.csv']
        for target_file in target_files:
            if target_file in files:
                file_path = os.path.join(full_path, target_file)
                file_size = os.path.getsize(file_path)
                logger.info(f"✅ {target_file}: {file_size} bytes")
            else:
                logger.warning(f"❌ 缺失: {target_file}")
                
    else:
        logger.warning(f"⚠️ 目录不存在: {full_path}")
        
    return full_path

def create_csv_generation_patch():
    """创建CSV生成补丁"""
    logger.info("🔧 创建CSV生成补丁...")
    
    patch_code = '''
# 🎯 CSV生成补丁：在元模型训练完成后保存CSV文件
def save_meta_model_training_data_to_csv(X_meta_df, y_meta_series, output_dir="models/meta_model_data"):
    """
    保存元模型训练数据到CSV文件
    
    Args:
        X_meta_df: 元模型特征DataFrame
        y_meta_series: 元模型目标变量Series
        output_dir: 输出目录
    """
    import os
    import logging
    
    logger = logging.getLogger(__name__)
    logger.info("💾 保存元模型训练数据到CSV文件...")
    
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存特征数据
        x_meta_csv_path = os.path.join(output_dir, "X_meta_features_oof.csv")
        X_meta_df.to_csv(x_meta_csv_path, index=True)
        logger.info(f"✅ 元模型特征数据已保存: {x_meta_csv_path}")
        
        # 保存目标变量
        y_meta_csv_path = os.path.join(output_dir, "y_meta_target.csv")
        y_meta_series.to_csv(y_meta_csv_path, index=True, header=True)
        logger.info(f"✅ 元模型目标变量已保存: {y_meta_csv_path}")
        
        # 保存数据摘要
        summary_path = os.path.join(output_dir, "meta_data_summary.json")
        summary = {
            "timestamp": pd.Timestamp.now().isoformat(),
            "samples_count": len(X_meta_df),
            "features_count": len(X_meta_df.columns),
            "feature_names": list(X_meta_df.columns),
            "target_distribution": y_meta_series.value_counts().to_dict(),
            "data_time_range": {
                "start": str(X_meta_df.index.min()),
                "end": str(X_meta_df.index.max())
            }
        }
        
        import json
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"✅ 数据摘要已保存: {summary_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 保存CSV文件失败: {e}")
        return False
'''
    
    # 保存补丁代码到文件
    patch_file = "scripts/meta_model_csv_patch.py"
    with open(patch_file, "w", encoding="utf-8") as f:
        f.write(patch_code)
    
    logger.info(f"✅ CSV生成补丁已创建: {patch_file}")
    return patch_file

def suggest_integration_methods():
    """建议集成方法"""
    logger.info("💡 建议的集成方法:")
    
    methods = [
        "方法1: 修改prediction.py中的train_meta_model函数，在训练完成后调用CSV保存功能",
        "方法2: 使用优化的元模型训练流程（elite_meta_model.py）",
        "方法3: 在主训练脚本中手动调用CSV保存功能",
        "方法4: 创建独立的CSV生成脚本，从已有的元模型数据生成CSV"
    ]
    
    for i, method in enumerate(methods, 1):
        logger.info(f"  {i}. {method}")
    
    logger.info("\n🎯 推荐方法1：这是最直接的解决方案")

def create_manual_csv_generation_script():
    """创建手动CSV生成脚本"""
    logger.info("📝 创建手动CSV生成脚本...")
    
    script_content = '''#!/usr/bin/env python3
"""
手动生成元模型CSV文件
从当前训练数据生成X_meta_features_oof.csv和y_meta_target.csv
"""

import os
import sys
import pandas as pd
import numpy as np
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def generate_csv_from_current_training():
    """从当前训练数据生成CSV文件"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 开始从当前训练数据生成CSV文件...")
    
    try:
        # 这里需要调用实际的元模型训练数据准备流程
        # 用户需要根据实际情况修改这部分代码
        
        logger.info("⚠️ 请根据您的实际训练流程修改此脚本")
        logger.info("   1. 获取原始K线数据")
        logger.info("   2. 获取目标变量")
        logger.info("   3. 获取已训练模型信息")
        logger.info("   4. 调用优化的数据准备函数")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ CSV生成失败: {e}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    generate_csv_from_current_training()
'''
    
    script_file = "scripts/manual_csv_generation.py"
    with open(script_file, "w", encoding="utf-8") as f:
        f.write(script_content)
    
    logger.info(f"✅ 手动CSV生成脚本已创建: {script_file}")
    return script_file

def main():
    """主函数"""
    logger.info("🎯 开始修复元模型CSV文件生成问题...")
    
    try:
        # 1. 检查当前训练方法
        training_method_ok = check_current_meta_model_training_method()
        
        # 2. 检查数据目录
        meta_dir = check_meta_model_data_directory()
        
        # 3. 创建补丁
        patch_file = create_csv_generation_patch()
        
        # 4. 建议集成方法
        suggest_integration_methods()
        
        # 5. 创建手动生成脚本
        manual_script = create_manual_csv_generation_script()
        
        logger.info("🎯 问题诊断完成！")
        logger.info("📋 问题原因：当前的train_meta_model函数没有集成CSV保存功能")
        logger.info("🔧 解决方案：需要在元模型训练完成后调用CSV保存功能")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 元模型CSV生成问题诊断完成！")
    else:
        print("\n❌ 元模型CSV生成问题诊断失败！")
