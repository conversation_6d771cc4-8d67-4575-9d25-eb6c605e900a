#!/usr/bin/env python3
"""
训练管道协调器 - 统一管理所有优化类的协作
集成 TrainingDataCache, MemoryOptimizer, PerformanceMonitor, ProgressTracker, OptimizedGUIUpdater
"""

import time
import logging
from typing import Dict, List, Optional, Any, Callable
from contextlib import contextmanager

# 导入优化类
from .training_data_cache import TrainingDataCache, get_training_data_cache
from .performance_monitor import PerformanceMonitor, get_performance_monitor
from .progress_tracker import ProgressTracker, get_progress_tracker

# 导入现有的优化类
try:
    from src.core.memory_optimizer import MemoryOptimizer, get_memory_optimizer
    MEMORY_OPTIMIZER_AVAILABLE = True
except ImportError:
    MEMORY_OPTIMIZER_AVAILABLE = False

try:
    from src.core.gui_update_manager import GUIUpdateManager
    GUI_UPDATE_MANAGER_AVAILABLE = True
except ImportError:
    GUI_UPDATE_MANAGER_AVAILABLE = False

try:
    from src.utils.model_cache_manager import Model<PERSON>ache<PERSON>anager, get_global_cache_manager
    MODEL_CACHE_MANAGER_AVAILABLE = True
except ImportError:
    MODEL_CACHE_MANAGER_AVAILABLE = False

logger = logging.getLogger(__name__)

class TrainingPipelineCoordinator:
    """训练管道协调器"""
    
    def __init__(self, 
                 enable_data_cache: bool = True,
                 enable_memory_optimization: bool = True,
                 enable_performance_monitoring: bool = True,
                 enable_progress_tracking: bool = True,
                 enable_gui_optimization: bool = True,
                 enable_model_cache: bool = True):
        """
        初始化训练管道协调器
        
        Args:
            enable_data_cache: 是否启用数据缓存
            enable_memory_optimization: 是否启用内存优化
            enable_performance_monitoring: 是否启用性能监控
            enable_progress_tracking: 是否启用进度跟踪
            enable_gui_optimization: 是否启用GUI优化
            enable_model_cache: 是否启用模型缓存
        """
        self.enable_data_cache = enable_data_cache
        self.enable_memory_optimization = enable_memory_optimization and MEMORY_OPTIMIZER_AVAILABLE
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_progress_tracking = enable_progress_tracking
        self.enable_gui_optimization = enable_gui_optimization and GUI_UPDATE_MANAGER_AVAILABLE
        self.enable_model_cache = enable_model_cache and MODEL_CACHE_MANAGER_AVAILABLE
        
        # 初始化优化器
        self.data_cache = get_training_data_cache() if self.enable_data_cache else None
        self.performance_monitor = get_performance_monitor() if self.enable_performance_monitoring else None
        self.progress_tracker = get_progress_tracker() if self.enable_progress_tracking else None
        self.memory_optimizer = None  # 按需创建
        self.gui_update_manager = None  # 按需创建
        self.model_cache_manager = get_global_cache_manager() if self.enable_model_cache else None
        
        # 统计信息
        self._optimization_stats = {
            'data_cache_hits': 0,
            'data_cache_misses': 0,
            'memory_optimizations': 0,
            'performance_stages': 0,
            'progress_updates': 0
        }
        
        logger.info(f"训练管道协调器初始化完成 - "
                   f"数据缓存: {self.enable_data_cache}, "
                   f"内存优化: {self.enable_memory_optimization}, "
                   f"性能监控: {self.enable_performance_monitoring}, "
                   f"进度跟踪: {self.enable_progress_tracking}, "
                   f"GUI优化: {self.enable_gui_optimization}, "
                   f"模型缓存: {self.enable_model_cache}")
    
    @contextmanager
    def training_stage(self, 
                      stage_name: str, 
                      target_name: Optional[str] = None,
                      total_steps: int = 1,
                      enable_memory_monitoring: bool = True):
        """
        训练阶段上下文管理器 - 集成所有优化功能
        
        Args:
            stage_name: 阶段名称
            target_name: 目标名称
            total_steps: 总步数
            enable_memory_monitoring: 是否启用内存监控
        """
        # 开始进度跟踪
        stage_id = None
        if self.enable_progress_tracking:
            stage_id = self.progress_tracker.start_stage(stage_name, total_steps)
        
        # 开始性能监控
        performance_context = None
        if self.enable_performance_monitoring:
            performance_context = self.performance_monitor.monitor_stage(stage_name, target_name)
            performance_context.__enter__()
            self._optimization_stats['performance_stages'] += 1
        
        # 获取内存优化器（如果需要）
        memory_optimizer = None
        if self.enable_memory_optimization and enable_memory_monitoring:
            try:
                memory_optimizer = get_memory_optimizer({'name': target_name} if target_name else {})
            except Exception as e:
                logger.warning(f"获取内存优化器失败: {e}")
        
        try:
            # 返回协调器实例，提供优化功能访问
            yield TrainingStageContext(
                coordinator=self,
                stage_name=stage_name,
                target_name=target_name,
                stage_id=stage_id,
                memory_optimizer=memory_optimizer
            )
        finally:
            # 完成进度跟踪
            if self.enable_progress_tracking and stage_id:
                self.progress_tracker.finish_stage(stage_id)
            
            # 完成性能监控
            if performance_context:
                try:
                    performance_context.__exit__(None, None, None)
                except Exception as e:
                    logger.warning(f"性能监控上下文退出失败: {e}")
    
    def get_cached_data(self, 
                       symbol: str,
                       interval: str,
                       config: Dict[str, Any],
                       data_type: str = "features") -> Optional[Any]:
        """获取缓存数据"""
        if not self.enable_data_cache or not self.data_cache:
            return None
        
        cached_data = self.data_cache.get_cached_data(symbol, interval, config, data_type)
        
        if cached_data is not None:
            self._optimization_stats['data_cache_hits'] += 1
            logger.debug(f"数据缓存命中: {symbol}_{interval}_{data_type}")
        else:
            self._optimization_stats['data_cache_misses'] += 1
        
        return cached_data
    
    def store_cached_data(self, 
                         symbol: str,
                         interval: str,
                         config: Dict[str, Any],
                         data: Any,
                         data_type: str = "features"):
        """存储数据到缓存"""
        if not self.enable_data_cache or not self.data_cache:
            return
        
        self.data_cache.store_data(symbol, interval, config, data, data_type)
        logger.debug(f"数据已缓存: {symbol}_{interval}_{data_type}")
    
    def optimize_dataframe_memory(self, df, target_config: Optional[Dict] = None):
        """优化DataFrame内存使用"""
        if not self.enable_memory_optimization:
            return df
        
        try:
            if not self.memory_optimizer:
                self.memory_optimizer = get_memory_optimizer(target_config or {})
            
            optimized_df = self.memory_optimizer.optimize_dtype(df, aggressive=False, verbose=False)
            self._optimization_stats['memory_optimizations'] += 1
            return optimized_df
        except Exception as e:
            logger.warning(f"DataFrame内存优化失败: {e}")
            return df
    
    def update_progress(self, increment: int = 1, message: Optional[str] = None):
        """更新进度"""
        if not self.enable_progress_tracking or not self.progress_tracker:
            return
        
        self.progress_tracker.update_progress(increment=increment, message=message)
        self._optimization_stats['progress_updates'] += 1
    
    def record_custom_metric(self, metric_name: str, value: float, target_name: Optional[str] = None):
        """记录自定义性能指标"""
        if not self.enable_performance_monitoring or not self.performance_monitor:
            return
        
        self.performance_monitor.record_custom_metric(metric_name, value, target_name)
    
    def preload_models(self):
        """预加载所有模型"""
        if not self.enable_model_cache or not self.model_cache_manager:
            return {}
        
        return self.model_cache_manager.preload_all_models()
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = self._optimization_stats.copy()
        
        # 添加各优化器的统计信息
        if self.enable_data_cache and self.data_cache:
            stats['data_cache_stats'] = self.data_cache.get_cache_stats()
        
        if self.enable_performance_monitoring and self.performance_monitor:
            stats['performance_summary'] = self.performance_monitor.get_performance_summary()
        
        if self.enable_progress_tracking and self.progress_tracker:
            stats['progress_info'] = self.progress_tracker.get_overall_progress()
        
        if self.enable_model_cache and self.model_cache_manager:
            stats['model_cache_stats'] = self.model_cache_manager.get_cache_statistics()
        
        return stats
    
    def print_optimization_report(self):
        """打印优化报告"""
        print(f"\n🚀 训练管道优化报告")
        print(f"=" * 60)
        
        stats = self.get_optimization_stats()
        
        print(f"数据缓存命中: {stats['data_cache_hits']}")
        print(f"数据缓存未命中: {stats['data_cache_misses']}")
        print(f"内存优化次数: {stats['memory_optimizations']}")
        print(f"性能监控阶段: {stats['performance_stages']}")
        print(f"进度更新次数: {stats['progress_updates']}")
        
        # 打印各优化器的详细报告
        if self.enable_data_cache and self.data_cache:
            self.data_cache.print_cache_report()
        
        if self.enable_performance_monitoring and self.performance_monitor:
            self.performance_monitor.print_performance_report()
        
        if self.enable_progress_tracking and self.progress_tracker:
            self.progress_tracker.print_progress_report()
    
    def cleanup(self):
        """清理资源"""
        if self.enable_data_cache and self.data_cache:
            # 可以选择性清理缓存
            pass

        if self.enable_performance_monitoring and self.performance_monitor:
            # 可以选择性重置监控器
            pass

        logger.info("训练管道协调器清理完成")

    def get_memory_optimizer(self, target_config: Optional[Dict] = None):
        """获取内存优化器实例"""
        if not self.enable_memory_optimization:
            return None

        try:
            return get_memory_optimizer(target_config or {})
        except Exception as e:
            logger.warning(f"获取内存优化器失败: {e}")
            return None


class TrainingStageContext:
    """训练阶段上下文"""
    
    def __init__(self, 
                 coordinator: TrainingPipelineCoordinator,
                 stage_name: str,
                 target_name: Optional[str],
                 stage_id: Optional[str],
                 memory_optimizer: Optional[Any]):
        self.coordinator = coordinator
        self.stage_name = stage_name
        self.target_name = target_name
        self.stage_id = stage_id
        self.memory_optimizer = memory_optimizer
    
    def get_cached_data(self, symbol: str, interval: str, config: Dict, data_type: str = "features"):
        """获取缓存数据"""
        return self.coordinator.get_cached_data(symbol, interval, config, data_type)
    
    def store_cached_data(self, symbol: str, interval: str, config: Dict, data: Any, data_type: str = "features"):
        """存储缓存数据"""
        self.coordinator.store_cached_data(symbol, interval, config, data, data_type)
    
    def optimize_memory(self, df):
        """优化内存"""
        if self.memory_optimizer:
            try:
                return self.memory_optimizer.optimize_dtype(df, aggressive=False, verbose=False)
            except Exception as e:
                logger.warning(f"内存优化失败: {e}")
        return df
    
    def update_progress(self, increment: int = 1, message: Optional[str] = None):
        """更新进度"""
        self.coordinator.update_progress(increment, message)
    
    def record_metric(self, metric_name: str, value: float):
        """记录指标"""
        self.coordinator.record_custom_metric(metric_name, value, self.target_name)


# 全局协调器实例
_global_coordinator = None

def get_training_pipeline_coordinator() -> TrainingPipelineCoordinator:
    """获取全局训练管道协调器实例"""
    global _global_coordinator
    if _global_coordinator is None:
        _global_coordinator = TrainingPipelineCoordinator()
    return _global_coordinator
