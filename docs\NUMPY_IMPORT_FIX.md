# numpy导入错误修复文档

## 🎯 问题描述

在实时预测过程中，元模型特征工程失败，出现以下错误：

```
!!! 元模型特征构建或预测失败: cannot access local variable 'np' where it is not associated with a value
```

这个错误发生在计算 `meta_confidence_divergence` 特征时，该特征需要使用 `numpy.log2` 函数来计算概率分布的熵。

## 🔍 根本原因

### 错误代码
在 `src/core/prediction.py` 的 `apply_realtime_meta_feature_engineering` 函数中：

```python
# 7. 🚀 新增：置信度分歧特征 (基于概率分布的熵)
# 计算概率分布的熵来衡量不确定性
import numpy as np  # ❌ 问题：局部导入导致作用域冲突
prob_entropy = -up_prob * np.log2(up_prob + 1e-10) - (1-up_prob) * np.log2(1-up_prob + 1e-10)
down_entropy = -down_prob_raw * np.log2(down_prob_raw + 1e-10) - (1-down_prob_raw) * np.log2(1-down_prob_raw + 1e-10)
enhanced_data['meta_confidence_divergence'] = abs(prob_entropy - down_entropy)
```

### 问题分析
1. **重复导入**: 文件顶部已经有 `import numpy as np`，函数内又重新导入
2. **作用域冲突**: 局部导入可能与全局导入产生作用域冲突
3. **变量访问错误**: Python解释器无法正确访问 `np` 变量

## 🔧 解决方案

### 修复代码
移除函数内的重复导入，直接使用全局的 `numpy`：

```python
# 7. 🚀 新增：置信度分歧特征 (基于概率分布的熵)
# 计算概率分布的熵来衡量不确定性
prob_entropy = -up_prob * np.log2(up_prob + 1e-10) - (1-up_prob) * np.log2(1-up_prob + 1e-10)
down_entropy = -down_prob_raw * np.log2(down_prob_raw + 1e-10) - (1-down_prob_raw) * np.log2(1-down_prob_raw + 1e-10)
enhanced_data['meta_confidence_divergence'] = abs(prob_entropy - down_entropy)
print(f"    ✓ 添加特征: meta_confidence_divergence = {enhanced_data['meta_confidence_divergence']:.3f}")
```

### 修改位置
- **文件**: `src/core/prediction.py`
- **函数**: `apply_realtime_meta_feature_engineering()`
- **行数**: 第999-1004行

## ✅ 验证结果

### 测试案例
```python
# 输入数据
up_prob = 0.650
down_prob_raw = 0.700

# 计算结果
prob_entropy = 0.934068    # UP概率的熵
down_entropy = 0.881291    # DOWN概率的熵
meta_confidence_divergence = 0.052777  # 置信度分歧
```

### 验证步骤
1. ✅ **numpy可用性**: 版本1.26.4，log2函数正常
2. ✅ **基础计算**: `np.log2(0.65 + 1e-10) = -0.621488`
3. ✅ **熵计算**: `entropy(0.65) = 0.934068`
4. ✅ **特征计算**: `meta_confidence_divergence = 0.052777`

## 📊 特征计算详情

### meta_confidence_divergence 特征
**含义**: 基于概率分布熵的置信度分歧度量

**计算公式**:
```python
# UP模型概率的熵
prob_entropy = -p_up * log2(p_up) - (1-p_up) * log2(1-p_up)

# DOWN模型概率的熵  
down_entropy = -p_down * log2(p_down) - (1-p_down) * log2(1-p_down)

# 置信度分歧
meta_confidence_divergence = abs(prob_entropy - down_entropy)
```

**取值范围**: [0, 2]
- 0: 两个模型的置信度完全一致
- 2: 两个模型的置信度完全相反

### 所有新增特征汇总
| 特征名 | 示例值 | 含义 |
|--------|--------|------|
| `meta_model_divergence_precise` | 0.350000 | 精确分歧度 |
| `meta_market_certainty` | 1.350000 | 市场确定性 |
| `meta_dual_kill_risk` | 1.000000 | 多空双杀风险 |
| `meta_confidence_divergence` | 0.052777 | 置信度分歧 |

## 🚀 修复效果

### 修复前
```
!!! 元模型特征构建或预测失败: cannot access local variable 'np' where it is not associated with a value
```

### 修复后
```
✓ 添加特征: meta_model_divergence_precise = 0.350
✓ 添加特征: meta_market_certainty = 1.350
✓ 添加特征: meta_dual_kill_risk = 1.0
✓ 添加特征: meta_confidence_divergence = 0.053
```

## 🎯 预防措施

### 1. 避免重复导入
- 在文件顶部统一导入所有需要的库
- 避免在函数内重复导入已有的库

### 2. 作用域管理
- 使用全局导入的库，避免局部导入冲突
- 确保变量作用域的一致性

### 3. 错误处理
- 在关键计算部分添加try-catch块
- 提供详细的错误信息和调试输出

### 4. 测试验证
- 为新增特征编写单元测试
- 验证边界情况和异常处理

## 📝 技术细节

### 熵计算原理
信息熵用于衡量概率分布的不确定性：
- 熵值越高，不确定性越大
- 熵值越低，确定性越高
- 通过比较两个模型的熵值差异，可以衡量它们的置信度分歧

### numpy.log2 函数
- 计算以2为底的对数
- 用于信息论中的熵计算
- 添加小常数(1e-10)避免log(0)的数学错误

这个修复确保了元模型特征工程的稳定运行，所有四个新增特征现在都能正确计算，不再出现numpy导入错误。
