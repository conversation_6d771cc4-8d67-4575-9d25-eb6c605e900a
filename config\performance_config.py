#!/usr/bin/env python3
"""
训练流程性能优化配置文件

这个文件包含了所有性能优化相关的配置选项，
用户可以根据自己的硬件配置和需求调整这些参数。
"""

# ===== 数据处理优化配置 =====

# 数据缓存配置
ENABLE_DATA_CACHING = True                    # 是否启用数据缓存
DATA_CACHE_MAX_SIZE_MB = 500                  # 数据缓存最大大小（MB）
DATA_CACHE_TTL_SECONDS = 3600                 # 数据缓存生存时间（秒）

# 特征工程优化
ENABLE_FEATURE_CACHING = True                 # 是否启用特征缓存
ENABLE_VECTORIZED_FEATURES = True             # 是否启用向量化特征计算
FEATURE_BATCH_SIZE = 1000                     # 特征批处理大小

# 数据预处理优化
ENABLE_MEMORY_OPTIMIZATION = True             # 是否启用内存优化
AUTO_DOWNCAST_DTYPES = True                   # 是否自动降级数据类型
OPTIMIZE_CATEGORICAL_FEATURES = True          # 是否优化分类特征

# ===== 模型训练优化配置 =====

# Optuna超参数优化
ENABLE_ADAPTIVE_OPTUNA = True                 # 是否启用自适应Optuna配置
OPTUNA_PARALLEL_JOBS = 1                      # Optuna并行作业数（建议保持为1，因为LightGBM已使用多线程）

# 数据大小自适应配置
OPTUNA_SMALL_DATA_THRESHOLD = 500             # 小数据集阈值
OPTUNA_LARGE_DATA_THRESHOLD = 2000            # 大数据集阈值

# 小数据集优化参数
OPTUNA_SMALL_DATA_MAX_TRIALS = 30             # 小数据集最大试验次数
OPTUNA_SMALL_DATA_CV_FOLDS = 2                # 小数据集交叉验证折数

# 中等数据集优化参数
OPTUNA_MEDIUM_DATA_MAX_TRIALS = 50            # 中等数据集最大试验次数
OPTUNA_MEDIUM_DATA_CV_FOLDS = 3               # 中等数据集交叉验证折数

# 大数据集优化参数
OPTUNA_LARGE_DATA_MAX_TRIALS = 100            # 大数据集最大试验次数
OPTUNA_LARGE_DATA_CV_FOLDS = 3                # 大数据集交叉验证折数

# 早停和剪枝配置
ENABLE_OPTUNA_PRUNING = True                  # 是否启用Optuna剪枝
OPTUNA_PRUNING_WARMUP_STEPS = 10              # 剪枝预热步数
OPTUNA_PRUNING_INTERVAL_STEPS = 1             # 剪枝检查间隔

# 特征选择优化
ENABLE_FAST_FEATURE_SELECTION = True          # 是否启用快速特征选择
RFE_PRESCREENING_RATIO = 0.5                  # RFE预筛选比例
RFE_PARALLEL_JOBS = 1                         # RFE并行作业数

# ===== 并行训练配置 =====

# 并行训练设置
ENABLE_PARALLEL_TRAINING = False              # 是否启用并行训练（实验性功能）
MAX_PARALLEL_WORKERS = 2                      # 最大并行工作线程数
PARALLEL_TRAINING_MEMORY_LIMIT_GB = 8         # 并行训练内存限制（GB）

# ===== 内存和资源管理配置 =====

# 内存优化设置
MEMORY_OPTIMIZATION_LEVEL = 2                 # 内存优化级别（1-3，3为最激进）
ENABLE_AUTO_GARBAGE_COLLECTION = True         # 是否启用自动垃圾回收
GC_FREQUENCY_SECONDS = 300                    # 垃圾回收频率（秒）

# 内存监控设置
ENABLE_MEMORY_MONITORING = True               # 是否启用内存监控
MEMORY_WARNING_THRESHOLD_PERCENT = 80         # 内存警告阈值（百分比）
MEMORY_CRITICAL_THRESHOLD_PERCENT = 90        # 内存严重警告阈值（百分比）

# GPU/CPU资源配置
PREFER_GPU_TRAINING = True                    # 是否优先使用GPU训练
GPU_MEMORY_FRACTION = 0.8                     # GPU内存使用比例
CPU_THREAD_LIMIT = None                       # CPU线程限制（None为自动）

# ===== 进度监控和用户体验配置 =====

# 进度显示设置
ENABLE_DETAILED_PROGRESS = True               # 是否启用详细进度显示
PROGRESS_UPDATE_INTERVAL_SECONDS = 1.0        # 进度更新间隔（秒）
ENABLE_TIME_ESTIMATION = True                 # 是否启用时间估算

# GUI优化设置
GUI_UPDATE_INTERVAL_SECONDS = 0.5             # GUI更新间隔（秒）
ENABLE_BATCH_GUI_UPDATES = True               # 是否启用批量GUI更新
MAX_GUI_UPDATE_QUEUE_SIZE = 10                # GUI更新队列最大大小

# 性能监控设置
ENABLE_PERFORMANCE_MONITORING = True          # 是否启用性能监控
PERFORMANCE_LOG_LEVEL = 'INFO'                # 性能日志级别（DEBUG, INFO, WARNING, ERROR）
ENABLE_BOTTLENECK_DETECTION = True            # 是否启用瓶颈检测
BOTTLENECK_THRESHOLD_PERCENT = 20             # 瓶颈检测阈值（百分比）

# ===== 代码执行优化配置 =====

# 算法优化设置
ENABLE_ALGORITHM_OPTIMIZATION = True          # 是否启用算法优化
PREFER_VECTORIZED_OPERATIONS = True           # 是否优先使用向量化操作
ENABLE_NUMBA_ACCELERATION = False             # 是否启用Numba加速（需要安装numba）

# 数据IO优化
ENABLE_FAST_IO = True                         # 是否启用快速IO
IO_BUFFER_SIZE_KB = 64                        # IO缓冲区大小（KB）
ENABLE_COMPRESSION = True                     # 是否启用数据压缩

# ===== 调试和诊断配置 =====

# 性能诊断设置
ENABLE_PERFORMANCE_PROFILING = False          # 是否启用性能分析（调试用）
PROFILING_OUTPUT_DIR = './performance_logs'   # 性能分析输出目录
ENABLE_MEMORY_PROFILING = False               # 是否启用内存分析（调试用）

# 缓存诊断设置
ENABLE_CACHE_STATISTICS = True                # 是否启用缓存统计
CACHE_STATISTICS_INTERVAL_SECONDS = 300       # 缓存统计输出间隔（秒）

# ===== 训练管道优化配置 =====

# 训练数据缓存配置
TRAINING_DATA_CACHE_CONFIG = {
    'enable_cache': True,                     # 是否启用训练数据缓存
    'max_memory_mb': 1024,                    # 最大内存缓存大小（MB）
    'cache_ttl_hours': 24,                    # 缓存生存时间（小时）
    'enable_disk_cache': True,                # 是否启用磁盘缓存
    'cache_dir': './cache/training_data'      # 磁盘缓存目录
}

# 性能监控配置
PERFORMANCE_MONITOR_CONFIG = {
    'enable_monitoring': True,                # 是否启用性能监控
    'enable_memory_monitoring': True,         # 是否启用内存监控
    'bottleneck_time_threshold': 30,          # 时间瓶颈阈值（秒）
    'bottleneck_memory_threshold': 500,       # 内存瓶颈阈值（MB）
    'enable_bottleneck_detection': True       # 是否启用瓶颈检测
}

# 进度跟踪配置
PROGRESS_TRACKER_CONFIG = {
    'enable_tracking': True,                  # 是否启用进度跟踪
    'enable_time_estimation': True,           # 是否启用时间估算
    'historical_data_limit': 10,              # 历史数据保留数量
    'update_interval_seconds': 1.0            # 进度更新间隔（秒）
}

# 训练管道协调器配置
TRAINING_PIPELINE_COORDINATOR_CONFIG = {
    'enable_data_cache': True,                # 是否启用数据缓存
    'enable_memory_optimization': True,       # 是否启用内存优化
    'enable_performance_monitoring': True,    # 是否启用性能监控
    'enable_progress_tracking': True,         # 是否启用进度跟踪
    'enable_gui_optimization': True,          # 是否启用GUI优化
    'enable_model_cache': True                # 是否启用模型缓存
}

# ===== 高级优化配置 =====

# 实验性功能（谨慎启用）
ENABLE_EXPERIMENTAL_OPTIMIZATIONS = False     # 是否启用实验性优化
ENABLE_MIXED_PRECISION_TRAINING = False       # 是否启用混合精度训练
ENABLE_GRADIENT_CHECKPOINTING = False         # 是否启用梯度检查点

# 自适应优化设置
ENABLE_ADAPTIVE_OPTIMIZATION = True           # 是否启用自适应优化
ADAPTATION_LEARNING_RATE = 0.1                # 自适应学习率
ADAPTATION_WINDOW_SIZE = 10                   # 自适应窗口大小

# ===== 配置验证和工具函数 =====

def validate_performance_config():
    """验证性能配置的合理性"""
    warnings = []
    
    # 检查内存配置
    if MEMORY_WARNING_THRESHOLD_PERCENT >= MEMORY_CRITICAL_THRESHOLD_PERCENT:
        warnings.append("内存警告阈值应小于严重警告阈值")
    
    # 检查并行配置
    if ENABLE_PARALLEL_TRAINING and MAX_PARALLEL_WORKERS > 4:
        warnings.append("并行工作线程数过多可能导致资源竞争")
    
    # 检查Optuna配置
    if OPTUNA_SMALL_DATA_MAX_TRIALS > OPTUNA_LARGE_DATA_MAX_TRIALS:
        warnings.append("小数据集试验次数不应超过大数据集")
    
    # 检查更新间隔
    if GUI_UPDATE_INTERVAL_SECONDS < 0.1:
        warnings.append("GUI更新间隔过短可能导致界面卡顿")
    
    return warnings

def get_optimized_config_for_system():
    """根据系统配置获取优化的性能配置"""
    import psutil
    import multiprocessing
    
    # 获取系统信息
    cpu_count = multiprocessing.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # 根据系统配置调整参数
    config_adjustments = {}
    
    # CPU配置调整
    if cpu_count >= 8:
        config_adjustments['MAX_PARALLEL_WORKERS'] = min(4, cpu_count // 2)
        config_adjustments['ENABLE_PARALLEL_TRAINING'] = True
    elif cpu_count >= 4:
        config_adjustments['MAX_PARALLEL_WORKERS'] = 2
    else:
        config_adjustments['ENABLE_PARALLEL_TRAINING'] = False
    
    # 内存配置调整
    if memory_gb >= 16:
        config_adjustments['DATA_CACHE_MAX_SIZE_MB'] = 1000
        config_adjustments['MEMORY_OPTIMIZATION_LEVEL'] = 1
    elif memory_gb >= 8:
        config_adjustments['DATA_CACHE_MAX_SIZE_MB'] = 500
        config_adjustments['MEMORY_OPTIMIZATION_LEVEL'] = 2
    else:
        config_adjustments['DATA_CACHE_MAX_SIZE_MB'] = 200
        config_adjustments['MEMORY_OPTIMIZATION_LEVEL'] = 3
        config_adjustments['ENABLE_DATA_CACHING'] = True  # 低内存系统更需要缓存
    
    return config_adjustments

def print_performance_config_summary():
    """打印性能配置摘要"""
    print("=== 训练流程性能优化配置摘要 ===")
    print(f"数据缓存: {'启用' if ENABLE_DATA_CACHING else '禁用'}")
    print(f"内存优化: {'启用' if ENABLE_MEMORY_OPTIMIZATION else '禁用'}")
    print(f"自适应Optuna: {'启用' if ENABLE_ADAPTIVE_OPTUNA else '禁用'}")
    print(f"并行训练: {'启用' if ENABLE_PARALLEL_TRAINING else '禁用'}")
    print(f"性能监控: {'启用' if ENABLE_PERFORMANCE_MONITORING else '禁用'}")
    print(f"详细进度: {'启用' if ENABLE_DETAILED_PROGRESS else '禁用'}")
    
    # 检查配置警告
    warnings = validate_performance_config()
    if warnings:
        print("\n⚠️  配置警告:")
        for warning in warnings:
            print(f"  - {warning}")
    else:
        print("\n✅ 配置验证通过")

if __name__ == "__main__":
    print_performance_config_summary()
    
    # 获取系统优化建议
    system_config = get_optimized_config_for_system()
    if system_config:
        print(f"\n💡 系统优化建议:")
        for key, value in system_config.items():
            print(f"  {key}: {value}")
