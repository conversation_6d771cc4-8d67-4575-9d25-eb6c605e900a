#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的状态管理器

优化PredictionStateManager的线程安全性，将目标相关状态分组管理，
提供更清晰的状态管理接口。

主要改进：
1. 目标状态分组管理
2. 完善的线程安全机制
3. 统一的状态访问接口
4. 与ConfigManager集成
5. 状态生命周期管理
"""

import threading
import time
import logging
from typing import Dict, Any, Optional, List, Union
from collections import defaultdict
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

# 设置日志
logger = logging.getLogger(__name__)

class SignalState(Enum):
    """信号状态枚举"""
    NEUTRAL = "Neutral"
    UP = "UP"
    DOWN = "DOWN"
    ERROR = "Error"

@dataclass
class TargetState:
    """
    单个目标的状态管理
    
    将与特定目标相关的所有状态集中管理
    """
    target_name: str
    
    # 信号相关状态
    last_signal_state: SignalState = SignalState.NEUTRAL
    last_signal_sent_time: float = 0.0
    last_signal_type_sent: str = ""
    last_pre_alarm_play_time: float = 0.0
    
    # 策略执行计数
    strategy_execution_counter: int = 0
    
    # 预测相关状态
    last_prediction_time: float = 0.0
    last_prediction_result: Optional[Dict[str, Any]] = None
    last_meta_predictions: Dict[str, float] = field(default_factory=dict)
    
    # 性能统计
    total_signals_sent: int = 0
    successful_predictions: int = 0
    failed_predictions: int = 0
    
    # 状态锁
    _lock: threading.RLock = field(default_factory=threading.RLock, init=False)
    
    def __post_init__(self):
        """初始化后处理"""
        if not hasattr(self, '_lock'):
            self._lock = threading.RLock()
    
    def get_signal_state(self) -> SignalState:
        """线程安全地获取信号状态"""
        with self._lock:
            return self.last_signal_state
    
    def set_signal_state(self, state: Union[SignalState, str]):
        """线程安全地设置信号状态"""
        with self._lock:
            if isinstance(state, str):
                try:
                    state = SignalState(state)
                except ValueError:
                    logger.warning(f"无效的信号状态: {state}，使用NEUTRAL")
                    state = SignalState.NEUTRAL
            self.last_signal_state = state
            logger.debug(f"目标 {self.target_name} 信号状态更新为: {state.value}")
    
    def update_signal_sent(self, signal_type: str, timestamp: float = None):
        """更新信号发送信息"""
        with self._lock:
            if timestamp is None:
                timestamp = time.time()
            self.last_signal_sent_time = timestamp
            self.last_signal_type_sent = signal_type
            self.total_signals_sent += 1
            logger.debug(f"目标 {self.target_name} 发送信号: {signal_type}")
    
    def update_pre_alarm_time(self, timestamp: float = None):
        """更新预警播放时间"""
        with self._lock:
            if timestamp is None:
                timestamp = time.time()
            self.last_pre_alarm_play_time = timestamp
    
    def increment_strategy_counter(self):
        """增加策略执行计数"""
        with self._lock:
            self.strategy_execution_counter += 1
            return self.strategy_execution_counter
    
    def update_prediction_result(self, result: Dict[str, Any], timestamp: float = None):
        """更新预测结果"""
        with self._lock:
            if timestamp is None:
                timestamp = time.time()
            self.last_prediction_time = timestamp
            self.last_prediction_result = result.copy() if result else None
            
            # 更新统计
            if result and result.get('success', False):
                self.successful_predictions += 1
            else:
                self.failed_predictions += 1
    
    def update_meta_predictions(self, predictions: Dict[str, float]):
        """更新元模型预测"""
        with self._lock:
            self.last_meta_predictions = predictions.copy() if predictions else {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取目标统计信息"""
        with self._lock:
            total_predictions = self.successful_predictions + self.failed_predictions
            success_rate = (self.successful_predictions / total_predictions) if total_predictions > 0 else 0.0
            
            return {
                'target_name': self.target_name,
                'last_signal_state': self.last_signal_state.value,
                'total_signals_sent': self.total_signals_sent,
                'total_predictions': total_predictions,
                'successful_predictions': self.successful_predictions,
                'failed_predictions': self.failed_predictions,
                'success_rate': success_rate,
                'strategy_execution_counter': self.strategy_execution_counter,
                'last_signal_sent_time': self.last_signal_sent_time,
                'last_prediction_time': self.last_prediction_time
            }
    
    def reset_statistics(self):
        """重置统计信息"""
        with self._lock:
            self.total_signals_sent = 0
            self.successful_predictions = 0
            self.failed_predictions = 0
            self.strategy_execution_counter = 0
            logger.info(f"目标 {self.target_name} 统计信息已重置")

class EnhancedPredictionStateManager:
    """
    增强的预测状态管理器
    
    提供线程安全的状态管理，支持目标分组和统一访问接口
    """
    
    def __init__(self):
        """初始化状态管理器"""
        self._global_lock = threading.RLock()
        
        # 目标状态管理
        self._target_states: Dict[str, TargetState] = {}
        
        # 全局状态
        self._global_state = {
            'last_signal_alert_time': 0.0,
            'music_fadeout_timer': None,
            'prediction_context_logger': None,
            'meta_model_instance': None,
            'meta_model_feature_names': None,
            'meta_model_loaded_successfully': False
        }
        
        # 状态变更监听器
        self._state_change_listeners = []
        
        logger.info("增强的预测状态管理器初始化完成")
    
    def initialize_targets(self, targets_config: List[Dict[str, Any]]):
        """
        初始化目标状态
        
        Args:
            targets_config: 目标配置列表
        """
        with self._global_lock:
            for target_config in targets_config:
                if isinstance(target_config, dict) and 'name' in target_config:
                    target_name = target_config['name']
                    if target_name not in self._target_states:
                        self._target_states[target_name] = TargetState(target_name)
                        logger.info(f"初始化目标状态: {target_name}")
    
    def get_target_state(self, target_name: str) -> Optional[TargetState]:
        """
        获取目标状态对象
        
        Args:
            target_name: 目标名称
            
        Returns:
            目标状态对象，如果不存在则返回None
        """
        with self._global_lock:
            return self._target_states.get(target_name)
    
    def ensure_target_state(self, target_name: str) -> TargetState:
        """
        确保目标状态存在，如果不存在则创建
        
        Args:
            target_name: 目标名称
            
        Returns:
            目标状态对象
        """
        with self._global_lock:
            if target_name not in self._target_states:
                self._target_states[target_name] = TargetState(target_name)
                logger.info(f"创建新的目标状态: {target_name}")
            return self._target_states[target_name]
    
    def get_all_target_names(self) -> List[str]:
        """获取所有目标名称"""
        with self._global_lock:
            return list(self._target_states.keys())
    
    # === 信号状态管理 ===
    
    def get_signal_state(self, target_name: str) -> SignalState:
        """获取目标的信号状态"""
        target_state = self.get_target_state(target_name)
        if target_state:
            return target_state.get_signal_state()
        return SignalState.NEUTRAL
    
    def set_signal_state(self, target_name: str, state: Union[SignalState, str]):
        """设置目标的信号状态"""
        target_state = self.ensure_target_state(target_name)
        old_state = target_state.get_signal_state()
        target_state.set_signal_state(state)
        
        # 通知状态变更监听器
        self._notify_state_change('signal_state', target_name, old_state, state)
    
    def get_all_signal_states(self) -> Dict[str, SignalState]:
        """获取所有目标的信号状态"""
        with self._global_lock:
            return {name: state.get_signal_state() 
                   for name, state in self._target_states.items()}
    
    # === 信号发送时间管理 ===
    
    def get_last_signal_sent_time(self, target_name: str) -> float:
        """获取目标的最后信号发送时间"""
        target_state = self.get_target_state(target_name)
        if target_state:
            with target_state._lock:
                return target_state.last_signal_sent_time
        return 0.0
    
    def update_signal_sent(self, target_name: str, signal_type: str, timestamp: float = None):
        """更新信号发送信息"""
        target_state = self.ensure_target_state(target_name)
        target_state.update_signal_sent(signal_type, timestamp)
        
        # 通知状态变更监听器
        self._notify_state_change('signal_sent', target_name, None, signal_type)
    
    def get_all_signal_sent_times(self) -> Dict[str, float]:
        """获取所有目标的信号发送时间"""
        with self._global_lock:
            result = {}
            for name, state in self._target_states.items():
                with state._lock:
                    result[name] = state.last_signal_sent_time
            return result
    
    # === 预警时间管理 ===
    
    def get_last_pre_alarm_play_time(self, target_name: str) -> float:
        """获取目标的最后预警播放时间"""
        target_state = self.get_target_state(target_name)
        if target_state:
            with target_state._lock:
                return target_state.last_pre_alarm_play_time
        return 0.0
    
    def update_pre_alarm_time(self, target_name: str, timestamp: float = None):
        """更新预警播放时间"""
        target_state = self.ensure_target_state(target_name)
        target_state.update_pre_alarm_time(timestamp)
    
    def get_all_pre_alarm_times(self) -> Dict[str, float]:
        """获取所有目标的预警播放时间"""
        with self._global_lock:
            result = {}
            for name, state in self._target_states.items():
                with state._lock:
                    result[name] = state.last_pre_alarm_play_time
            return result

    # === 策略执行计数管理 ===

    def get_strategy_execution_counter(self, target_name: str) -> int:
        """获取目标的策略执行计数"""
        target_state = self.get_target_state(target_name)
        if target_state:
            with target_state._lock:
                return target_state.strategy_execution_counter
        return 0

    def increment_strategy_counter(self, target_name: str) -> int:
        """增加目标的策略执行计数"""
        target_state = self.ensure_target_state(target_name)
        return target_state.increment_strategy_counter()

    def get_all_strategy_counters(self) -> Dict[str, int]:
        """获取所有目标的策略执行计数"""
        with self._global_lock:
            result = {}
            for name, state in self._target_states.items():
                with state._lock:
                    result[name] = state.strategy_execution_counter
            return result

    # === 元模型预测管理 ===

    def get_last_meta_predictions(self, target_name: str = None) -> Dict[str, float]:
        """获取最后的元模型预测"""
        if target_name:
            target_state = self.get_target_state(target_name)
            if target_state:
                with target_state._lock:
                    return target_state.last_meta_predictions.copy()
            return {}
        else:
            # 返回全局的元模型预测（向后兼容）
            with self._global_lock:
                # 如果有多个目标，合并它们的预测
                all_predictions = {}
                for state in self._target_states.values():
                    with state._lock:
                        all_predictions.update(state.last_meta_predictions)
                return all_predictions

    def set_last_meta_predictions(self, predictions: Dict[str, float], target_name: str = None):
        """设置最后的元模型预测"""
        if target_name:
            target_state = self.ensure_target_state(target_name)
            target_state.update_meta_predictions(predictions)
        else:
            # 全局设置（向后兼容）- 为所有目标设置相同的预测
            with self._global_lock:
                for state in self._target_states.values():
                    state.update_meta_predictions(predictions)

    # === 全局状态管理 ===

    def get_last_signal_alert_time(self) -> float:
        """获取最后信号警报时间"""
        with self._global_lock:
            return self._global_state['last_signal_alert_time']

    def set_last_signal_alert_time(self, timestamp: float):
        """设置最后信号警报时间"""
        with self._global_lock:
            self._global_state['last_signal_alert_time'] = timestamp

    def get_music_fadeout_timer(self):
        """获取音乐淡出定时器"""
        with self._global_lock:
            return self._global_state['music_fadeout_timer']

    def set_music_fadeout_timer(self, timer_instance):
        """设置音乐淡出定时器"""
        with self._global_lock:
            # 取消之前的定时器
            old_timer = self._global_state['music_fadeout_timer']
            if old_timer and hasattr(old_timer, 'cancel'):
                try:
                    old_timer.cancel()
                except Exception:
                    pass
            self._global_state['music_fadeout_timer'] = timer_instance

    def get_prediction_context_logger(self):
        """获取预测上下文日志记录器"""
        with self._global_lock:
            return self._global_state['prediction_context_logger']

    def set_prediction_context_logger(self, logger_instance):
        """设置预测上下文日志记录器"""
        with self._global_lock:
            self._global_state['prediction_context_logger'] = logger_instance

    # === 元模型实例管理 ===

    def get_meta_model_instance(self):
        """获取元模型实例"""
        with self._global_lock:
            return self._global_state['meta_model_instance']

    def set_meta_model_instance(self, model_instance):
        """设置元模型实例"""
        with self._global_lock:
            self._global_state['meta_model_instance'] = model_instance

    def get_meta_model_feature_names(self) -> Optional[List[str]]:
        """获取元模型特征名称"""
        with self._global_lock:
            return self._global_state['meta_model_feature_names']

    def set_meta_model_feature_names(self, feature_names: List[str]):
        """设置元模型特征名称"""
        with self._global_lock:
            self._global_state['meta_model_feature_names'] = feature_names.copy() if feature_names else None

    def is_meta_model_loaded_successfully(self) -> bool:
        """检查元模型是否加载成功"""
        with self._global_lock:
            return self._global_state['meta_model_loaded_successfully']

    def set_meta_model_loaded_successfully(self, loaded: bool):
        """设置元模型加载状态"""
        with self._global_lock:
            self._global_state['meta_model_loaded_successfully'] = loaded

    # === 统计和监控 ===

    def get_target_statistics(self, target_name: str) -> Optional[Dict[str, Any]]:
        """获取目标统计信息"""
        target_state = self.get_target_state(target_name)
        if target_state:
            return target_state.get_statistics()
        return None

    def get_all_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有目标的统计信息"""
        with self._global_lock:
            return {name: state.get_statistics()
                   for name, state in self._target_states.items()}

    def reset_target_statistics(self, target_name: str):
        """重置目标统计信息"""
        target_state = self.get_target_state(target_name)
        if target_state:
            target_state.reset_statistics()

    def reset_all_statistics(self):
        """重置所有目标的统计信息"""
        with self._global_lock:
            for state in self._target_states.values():
                state.reset_statistics()
            logger.info("所有目标统计信息已重置")

    # === 状态变更监听 ===

    def add_state_change_listener(self, listener_func):
        """添加状态变更监听器"""
        with self._global_lock:
            self._state_change_listeners.append(listener_func)

    def remove_state_change_listener(self, listener_func):
        """移除状态变更监听器"""
        with self._global_lock:
            if listener_func in self._state_change_listeners:
                self._state_change_listeners.remove(listener_func)

    def _notify_state_change(self, change_type: str, target_name: str, old_value: Any, new_value: Any):
        """通知状态变更监听器"""
        for listener in self._state_change_listeners:
            try:
                listener(change_type, target_name, old_value, new_value)
            except Exception as e:
                logger.error(f"状态变更监听器执行失败: {e}")

    # === 兼容性方法 ===

    def get_last_signal_state(self, target_name: str) -> str:
        """获取信号状态（字符串格式，向后兼容）"""
        return self.get_signal_state(target_name).value

    def set_last_signal_state(self, target_name: str, state: str):
        """设置信号状态（字符串格式，向后兼容）"""
        self.set_signal_state(target_name, state)

    def get_all_last_signal_states(self) -> Dict[str, str]:
        """获取所有信号状态（字符串格式，向后兼容）"""
        return {name: state.value for name, state in self.get_all_signal_states().items()}


# === 全局状态管理器实例 ===

_global_enhanced_state_manager = None
_global_state_manager_lock = threading.Lock()

def get_enhanced_state_manager() -> EnhancedPredictionStateManager:
    """
    获取全局增强状态管理器实例（单例模式）

    Returns:
        增强的预测状态管理器实例
    """
    global _global_enhanced_state_manager

    if _global_enhanced_state_manager is None:
        with _global_state_manager_lock:
            if _global_enhanced_state_manager is None:
                _global_enhanced_state_manager = EnhancedPredictionStateManager()

    return _global_enhanced_state_manager

def initialize_enhanced_state_manager(targets_config: List[Dict[str, Any]]):
    """
    初始化全局增强状态管理器

    Args:
        targets_config: 目标配置列表
    """
    state_manager = get_enhanced_state_manager()
    state_manager.initialize_targets(targets_config)
    logger.info("全局增强状态管理器初始化完成")

# === 便捷函数 ===

def get_target_signal_state(target_name: str) -> SignalState:
    """便捷函数：获取目标信号状态"""
    return get_enhanced_state_manager().get_signal_state(target_name)

def set_target_signal_state(target_name: str, state: Union[SignalState, str]):
    """便捷函数：设置目标信号状态"""
    get_enhanced_state_manager().set_signal_state(target_name, state)

def update_target_signal_sent(target_name: str, signal_type: str, timestamp: float = None):
    """便捷函数：更新目标信号发送"""
    get_enhanced_state_manager().update_signal_sent(target_name, signal_type, timestamp)

def get_target_statistics(target_name: str) -> Optional[Dict[str, Any]]:
    """便捷函数：获取目标统计信息"""
    return get_enhanced_state_manager().get_target_statistics(target_name)

def get_all_target_statistics() -> Dict[str, Dict[str, Any]]:
    """便捷函数：获取所有目标统计信息"""
    return get_enhanced_state_manager().get_all_statistics()

# === 迁移辅助函数 ===

class StateManagerMigrationHelper:
    """
    状态管理器迁移辅助类

    帮助从旧的PredictionStateManager迁移到新的EnhancedPredictionStateManager
    """

    @staticmethod
    def migrate_from_old_manager(old_manager, enhanced_manager: EnhancedPredictionStateManager):
        """
        从旧的状态管理器迁移数据

        Args:
            old_manager: 旧的PredictionStateManager实例
            enhanced_manager: 新的EnhancedPredictionStateManager实例
        """
        try:
            # 迁移信号状态
            if hasattr(old_manager, '_last_signal_state'):
                for target_name, state in old_manager._last_signal_state.items():
                    enhanced_manager.set_signal_state(target_name, state)

            # 迁移信号发送时间
            if hasattr(old_manager, '_last_signal_sent_time_per_target'):
                for target_name, timestamp in old_manager._last_signal_sent_time_per_target.items():
                    target_state = enhanced_manager.ensure_target_state(target_name)
                    with target_state._lock:
                        target_state.last_signal_sent_time = timestamp

            # 迁移信号类型
            if hasattr(old_manager, '_last_signal_type_sent_per_target'):
                for target_name, signal_type in old_manager._last_signal_type_sent_per_target.items():
                    target_state = enhanced_manager.ensure_target_state(target_name)
                    with target_state._lock:
                        target_state.last_signal_type_sent = signal_type

            # 迁移预警时间
            if hasattr(old_manager, '_last_pre_alarm_play_time'):
                for target_name, timestamp in old_manager._last_pre_alarm_play_time.items():
                    target_state = enhanced_manager.ensure_target_state(target_name)
                    with target_state._lock:
                        target_state.last_pre_alarm_play_time = timestamp

            # 迁移策略执行计数
            if hasattr(old_manager, '_strategy_execution_counters'):
                for target_name, counter in old_manager._strategy_execution_counters.items():
                    target_state = enhanced_manager.ensure_target_state(target_name)
                    with target_state._lock:
                        target_state.strategy_execution_counter = counter

            # 迁移元模型预测
            if hasattr(old_manager, '_last_meta_predictions'):
                enhanced_manager.set_last_meta_predictions(old_manager._last_meta_predictions)

            # 迁移全局状态
            if hasattr(old_manager, '_last_signal_alert_time'):
                enhanced_manager.set_last_signal_alert_time(old_manager._last_signal_alert_time)

            if hasattr(old_manager, '_music_fadeout_timer'):
                enhanced_manager.set_music_fadeout_timer(old_manager._music_fadeout_timer)

            if hasattr(old_manager, '_prediction_context_logger'):
                enhanced_manager.set_prediction_context_logger(old_manager._prediction_context_logger)

            if hasattr(old_manager, '_meta_model_instance'):
                enhanced_manager.set_meta_model_instance(old_manager._meta_model_instance)

            if hasattr(old_manager, '_meta_model_feature_names'):
                enhanced_manager.set_meta_model_feature_names(old_manager._meta_model_feature_names)

            if hasattr(old_manager, '_meta_model_loaded_successfully'):
                enhanced_manager.set_meta_model_loaded_successfully(old_manager._meta_model_loaded_successfully)

            logger.info("状态管理器迁移完成")

        except Exception as e:
            logger.error(f"状态管理器迁移失败: {e}")
            raise

    @staticmethod
    def create_compatibility_wrapper(enhanced_manager: EnhancedPredictionStateManager):
        """
        创建兼容性包装器，使新的状态管理器兼容旧的接口

        Args:
            enhanced_manager: 新的状态管理器

        Returns:
            兼容性包装器对象
        """
        class CompatibilityWrapper:
            def __init__(self, manager):
                self._manager = manager

            # 兼容旧接口的方法
            def get_last_signal_state(self, target_name):
                return self._manager.get_last_signal_state(target_name)

            def set_last_signal_state(self, target_name, state):
                self._manager.set_last_signal_state(target_name, state)

            def get_all_last_signal_states(self):
                return self._manager.get_all_last_signal_states()

            def get_last_signal_sent_time(self, target_name):
                return self._manager.get_last_signal_sent_time(target_name)

            def set_last_signal_sent_time(self, target_name, timestamp):
                self._manager.update_signal_sent(target_name, "", timestamp)

            def get_all_last_signal_sent_times(self):
                return self._manager.get_all_signal_sent_times()

            def get_last_pre_alarm_play_time(self, target_name):
                return self._manager.get_last_pre_alarm_play_time(target_name)

            def set_last_pre_alarm_play_time(self, target_name, timestamp):
                self._manager.update_pre_alarm_time(target_name, timestamp)

            def get_all_last_pre_alarm_play_time(self):
                return self._manager.get_all_pre_alarm_times()

            def get_strategy_execution_counter(self, target_name):
                return self._manager.get_strategy_execution_counter(target_name)

            def increment_strategy_execution_counter(self, target_name):
                return self._manager.increment_strategy_counter(target_name)

            def get_all_strategy_execution_counters(self):
                return self._manager.get_all_strategy_counters()

            def get_last_meta_predictions(self):
                return self._manager.get_last_meta_predictions()

            def set_last_meta_predictions(self, predictions):
                self._manager.set_last_meta_predictions(predictions)

            # 全局状态方法
            def get_last_signal_alert_time(self):
                return self._manager.get_last_signal_alert_time()

            def set_last_signal_alert_time(self, timestamp):
                self._manager.set_last_signal_alert_time(timestamp)

            def get_music_fadeout_timer(self):
                return self._manager.get_music_fadeout_timer()

            def set_music_fadeout_timer(self, timer):
                self._manager.set_music_fadeout_timer(timer)

            def get_prediction_context_logger(self):
                return self._manager.get_prediction_context_logger()

            def set_prediction_context_logger(self, logger):
                self._manager.set_prediction_context_logger(logger)

            def get_meta_model_instance(self):
                return self._manager.get_meta_model_instance()

            def set_meta_model_instance(self, model):
                self._manager.set_meta_model_instance(model)

            def get_meta_model_feature_names(self):
                return self._manager.get_meta_model_feature_names()

            def set_meta_model_feature_names(self, names):
                self._manager.set_meta_model_feature_names(names)

            def is_meta_model_loaded_successfully(self):
                return self._manager.is_meta_model_loaded_successfully()

            def set_meta_model_loaded_successfully(self, loaded):
                self._manager.set_meta_model_loaded_successfully(loaded)

        return CompatibilityWrapper(enhanced_manager)
