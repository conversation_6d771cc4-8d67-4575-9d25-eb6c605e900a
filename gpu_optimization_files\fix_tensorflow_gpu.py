#!/usr/bin/env python3
"""
修复TensorFlow GPU支持
为CUDA 12.8/12.9安装正确的TensorFlow版本
"""

import subprocess
import sys
import os

def uninstall_current_tensorflow():
    """卸载当前TensorFlow"""
    print("🗑️  卸载当前TensorFlow...")
    
    packages_to_remove = [
        'tensorflow',
        'tensorflow-intel', 
        'tensorflow-cpu',
        'tensorflow-gpu'
    ]
    
    for package in packages_to_remove:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'uninstall', package, '-y'], 
                          capture_output=True, check=False)
            print(f"  已卸载: {package}")
        except:
            pass

def install_tensorflow_gpu():
    """安装支持CUDA 12的TensorFlow GPU版本"""
    print("🚀 安装TensorFlow GPU版本...")
    
    try:
        # 方法1: 尝试安装最新的TensorFlow（应该支持CUDA 12）
        print("尝试安装最新TensorFlow...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'tensorflow[and-cuda]==2.17.0'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ TensorFlow 2.17.0 安装成功")
            return True
        else:
            print(f"❌ 安装失败: {result.stderr}")
            
        # 方法2: 尝试conda安装
        print("尝试使用conda安装...")
        result = subprocess.run([
            'conda', 'install', 'tensorflow-gpu', '-c', 'conda-forge', '-y'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 通过conda安装成功")
            return True
        else:
            print(f"❌ conda安装失败: {result.stderr}")
            
        # 方法3: 安装nightly版本
        print("尝试安装nightly版本...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'tf-nightly[and-cuda]'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ nightly版本安装成功")
            return True
        else:
            print(f"❌ nightly安装失败: {result.stderr}")
            
        return False
        
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def test_gpu_support():
    """测试GPU支持"""
    print("\n🧪 测试GPU支持...")
    
    try:
        import tensorflow as tf
        
        print(f"TensorFlow版本: {tf.__version__}")
        
        # 检查构建信息
        build_info = tf.sysconfig.get_build_info()
        is_cuda_build = build_info.get('is_cuda_build', False)
        print(f"CUDA构建: {is_cuda_build}")
        
        if is_cuda_build:
            cuda_version = build_info.get('cuda_version', '未知')
            cudnn_version = build_info.get('cudnn_version', '未知')
            print(f"内置CUDA版本: {cuda_version}")
            print(f"内置cuDNN版本: {cudnn_version}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备:")
            for i, gpu in enumerate(gpus):
                print(f"  GPU {i}: {gpu}")
            
            # 测试GPU计算
            try:
                with tf.device('/GPU:0'):
                    a = tf.constant([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
                    b = tf.constant([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]])
                    c = tf.matmul(a, b)
                    print(f"✅ GPU计算测试成功: {c.numpy()}")
                
                return True
            except Exception as e:
                print(f"❌ GPU计算测试失败: {e}")
                return False
        else:
            print("❌ 未检测到GPU设备")
            return False
            
    except ImportError as e:
        print(f"❌ TensorFlow导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False

def setup_cuda_environment():
    """设置CUDA环境变量"""
    print("\n🔧 设置CUDA环境变量...")
    
    # 常见的CUDA安装路径
    cuda_paths = [
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0",
        r"C:\cuda",
        r"C:\tools\cuda"
    ]
    
    cuda_path = None
    for path in cuda_paths:
        if os.path.exists(path):
            cuda_path = path
            break
    
    if cuda_path:
        print(f"找到CUDA安装路径: {cuda_path}")
        
        # 设置环境变量
        os.environ['CUDA_PATH'] = cuda_path
        os.environ['CUDA_HOME'] = cuda_path
        
        # 添加到PATH
        cuda_bin = os.path.join(cuda_path, 'bin')
        cuda_lib = os.path.join(cuda_path, 'lib', 'x64')
        
        current_path = os.environ.get('PATH', '')
        if cuda_bin not in current_path:
            os.environ['PATH'] = f"{cuda_bin};{current_path}"
        if cuda_lib not in current_path:
            os.environ['PATH'] = f"{cuda_lib};{os.environ['PATH']}"
        
        print(f"✅ CUDA环境变量设置完成")
        print(f"  CUDA_PATH: {cuda_path}")
        print(f"  已添加到PATH: {cuda_bin}, {cuda_lib}")
    else:
        print("❌ 未找到CUDA安装路径")
        print("请确认CUDA已正确安装")

def create_gpu_test_script():
    """创建GPU测试脚本"""
    print("\n📝 创建GPU测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
"""
GPU功能测试脚本
"""

import tensorflow as tf
import numpy as np
import time

def test_gpu_performance():
    """测试GPU性能"""
    print("🚀 GPU性能测试")
    print("-" * 40)
    
    # 检查GPU
    gpus = tf.config.list_physical_devices('GPU')
    if not gpus:
        print("❌ 未检测到GPU")
        return
    
    print(f"✅ 检测到 {len(gpus)} 个GPU")
    
    # 设置GPU内存增长
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")
    
    # 创建测试数据
    matrix_size = 2000
    print(f"创建 {matrix_size}x{matrix_size} 矩阵进行测试...")
    
    # CPU测试
    print("\\n🖥️  CPU测试:")
    with tf.device('/CPU:0'):
        cpu_a = tf.random.normal([matrix_size, matrix_size])
        cpu_b = tf.random.normal([matrix_size, matrix_size])
        
        start_time = time.time()
        cpu_result = tf.matmul(cpu_a, cpu_b)
        cpu_time = time.time() - start_time
        
        print(f"  CPU计算时间: {cpu_time:.4f}秒")
    
    # GPU测试
    print("\\n🚀 GPU测试:")
    with tf.device('/GPU:0'):
        gpu_a = tf.random.normal([matrix_size, matrix_size])
        gpu_b = tf.random.normal([matrix_size, matrix_size])
        
        start_time = time.time()
        gpu_result = tf.matmul(gpu_a, gpu_b)
        gpu_time = time.time() - start_time
        
        print(f"  GPU计算时间: {gpu_time:.4f}秒")
    
    # 计算加速比
    if cpu_time > 0 and gpu_time > 0:
        speedup = cpu_time / gpu_time
        print(f"\\n🎉 GPU加速比: {speedup:.2f}x")
    
    # LSTM测试
    print("\\n🧠 LSTM GPU测试:")
    test_lstm_gpu()

def test_lstm_gpu():
    """测试LSTM GPU性能"""
    try:
        # 创建LSTM模型
        model = tf.keras.Sequential([
            tf.keras.layers.LSTM(128, return_sequences=True, input_shape=(60, 50)),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(64),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        
        # 生成测试数据
        X = tf.random.normal([1000, 60, 50])
        y = tf.random.uniform([1000, 1]) > 0.5
        
        print("开始LSTM训练测试...")
        start_time = time.time()
        
        with tf.device('/GPU:0'):
            history = model.fit(X, y, epochs=2, batch_size=32, verbose=1)
        
        training_time = time.time() - start_time
        print(f"✅ LSTM GPU训练完成，耗时: {training_time:.2f}秒")
        
    except Exception as e:
        print(f"❌ LSTM GPU测试失败: {e}")

if __name__ == "__main__":
    print("🧪 TensorFlow GPU功能测试")
    print("=" * 50)
    
    test_gpu_performance()
'''
    
    with open('test_gpu_performance.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ GPU测试脚本已创建: test_gpu_performance.py")

def main():
    """主函数"""
    print("🔧 修复TensorFlow GPU支持")
    print("=" * 50)
    
    # 设置CUDA环境
    setup_cuda_environment()
    
    # 卸载当前版本
    uninstall_current_tensorflow()
    
    # 安装GPU版本
    success = install_tensorflow_gpu()
    
    if success:
        # 测试GPU支持
        gpu_works = test_gpu_support()
        
        if gpu_works:
            print("\n🎉 TensorFlow GPU支持修复成功！")
            
            # 创建测试脚本
            create_gpu_test_script()
            
            print("\n📋 下一步:")
            print("1. 重启Python环境")
            print("2. 运行: python test_gpu_performance.py")
            print("3. 在你的代码中使用GPU加速")
        else:
            print("\n❌ GPU支持仍有问题，可能需要:")
            print("1. 重新安装CUDA/cuDNN")
            print("2. 检查驱动版本兼容性")
            print("3. 重启系统")
    else:
        print("\n❌ TensorFlow GPU安装失败")
        print("建议手动安装或检查网络连接")

if __name__ == "__main__":
    main()
