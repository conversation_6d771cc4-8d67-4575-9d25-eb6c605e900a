#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态样本加权模块
实现基于不同市场环境的样本加权策略，提升模型学习效率
"""

import logging
import numpy as np
import pandas as pd
import traceback
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DynamicSampleWeighter:
    """动态样本加权器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化动态样本加权器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.weighting_stats = {}
        
        # 默认配置
        self.default_config = {
            'enable_dynamic_weighting': True,
            'weighting_strategies': ['time_decay', 'volatility'],
            'strategy_weights': {'time_decay': 0.4, 'volatility': 0.6},
            'combination_method': 'weighted_average',  # 'weighted_average', 'multiply', 'max'
            
            # 时间衰减配置
            'time_decay_rate': 0.1,           # 衰减率
            'time_decay_unit': 'days',        # 'days', 'hours', 'samples'
            'time_decay_power': 1.0,          # 衰减幂次
            
            # 波动率加权配置
            'volatility_column': 'ATRr_14',   # 波动率指标列
            'volatility_power': 1.5,          # 波动率权重幂次
            'volatility_cap': 3.0,            # 波动率权重上限
            
            # 市场状态加权配置
            'enable_market_state_weighting': False,
            'market_state_weights': {
                'trending': 1.2,
                'ranging': 0.8,
                'volatile': 1.5,
                'calm': 0.9
            },
            
            # 样本稀有度加权配置
            'enable_rarity_weighting': False,
            'rarity_percentile_threshold': 0.05,  # 前5%和后5%为稀有样本
            'rarity_weight_multiplier': 2.0,
            
            # 权重标准化配置
            'normalize_weights': True,
            'weight_clip_min': 0.1,           # 最小权重
            'weight_clip_max': 5.0,           # 最大权重
            'target_weight_sum': None         # None表示使用样本数
        }
        
        # 合并配置
        self.effective_config = {**self.default_config, **self.config}
        
        logger.info(f"[DynamicSampleWeighter] 初始化完成，策略: {self.effective_config['weighting_strategies']}")

    def calculate_time_decay_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算时间衰减权重
        
        Args:
            df: 包含时间索引的DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            时间衰减权重数组
        """
        try:
            decay_rate = self.effective_config['time_decay_rate']
            decay_unit = self.effective_config['time_decay_unit']
            decay_power = self.effective_config['time_decay_power']
            
            # 获取时间信息
            if isinstance(df.index, pd.DatetimeIndex):
                timestamps = df.index
            else:
                # 如果没有时间索引，使用行号作为时间代理
                timestamps = pd.date_range(start='2023-01-01', periods=len(df), freq='5T')
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 使用行号作为时间代理")
            
            # 计算时间差
            max_time = timestamps.max()
            if decay_unit == 'days':
                time_diffs = (max_time - timestamps).total_seconds() / (24 * 3600)
                time_diffs = np.array(time_diffs)  # 确保是numpy数组
            elif decay_unit == 'hours':
                time_diffs = (max_time - timestamps).total_seconds() / 3600
                time_diffs = np.array(time_diffs)  # 确保是numpy数组
            else:  # samples
                time_diffs = np.arange(len(df))[::-1]  # 最新样本为0
            
            # 计算指数衰减权重
            time_weights = np.exp(-decay_rate * (time_diffs ** decay_power))
            
            # 统计信息
            self.weighting_stats['time_decay'] = {
                'min_weight': time_weights.min(),
                'max_weight': time_weights.max(),
                'mean_weight': time_weights.mean(),
                'std_weight': time_weights.std(),
                'decay_rate': decay_rate,
                'decay_unit': decay_unit
            }
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 时间衰减权重计算完成，"
                       f"范围: [{time_weights.min():.4f}, {time_weights.max():.4f}]")
            
            return time_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 时间衰减权重计算失败: {e}")
            logger.debug(traceback.format_exc())
            return np.ones(len(df))

    def calculate_volatility_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算波动率权重
        
        Args:
            df: 包含波动率数据的DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            波动率权重数组
        """
        try:
            volatility_col = self.effective_config['volatility_column']
            volatility_power = self.effective_config['volatility_power']
            volatility_cap = self.effective_config['volatility_cap']
            
            # 检查波动率列是否存在
            if volatility_col not in df.columns:
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 波动率列 '{volatility_col}' 不存在，"
                             f"可用列: {list(df.columns)[:10]}...")
                return np.ones(len(df))
            
            # 获取波动率数据
            volatility = df[volatility_col].copy()
            
            # 处理NaN值
            volatility = volatility.fillna(volatility.median())
            
            # 计算相对波动率
            volatility_mean = volatility.mean()
            if volatility_mean <= 0:
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 波动率均值为0或负数")
                return np.ones(len(df))
            
            relative_volatility = volatility / volatility_mean
            
            # 计算波动率权重
            volatility_weights = relative_volatility ** volatility_power
            
            # 应用权重上限
            volatility_weights = np.clip(volatility_weights, 0.1, volatility_cap)
            
            # 统计信息
            self.weighting_stats['volatility'] = {
                'min_weight': volatility_weights.min(),
                'max_weight': volatility_weights.max(),
                'mean_weight': volatility_weights.mean(),
                'std_weight': volatility_weights.std(),
                'volatility_power': volatility_power,
                'volatility_cap': volatility_cap,
                'volatility_mean': volatility_mean
            }
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 波动率权重计算完成，"
                       f"范围: [{volatility_weights.min():.4f}, {volatility_weights.max():.4f}]")
            
            return volatility_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 波动率权重计算失败: {e}")
            logger.debug(traceback.format_exc())
            return np.ones(len(df))

    def calculate_market_state_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算市场状态权重
        
        Args:
            df: 包含市场状态数据的DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            市场状态权重数组
        """
        try:
            if not self.effective_config['enable_market_state_weighting']:
                return np.ones(len(df))
            
            state_weights_map = self.effective_config['market_state_weights']
            
            # 尝试识别市场状态
            market_states = self._identify_market_states(df)
            
            # 映射权重
            state_weights = np.array([state_weights_map.get(state, 1.0) for state in market_states])
            
            # 统计信息
            unique_states, state_counts = np.unique(market_states, return_counts=True)
            self.weighting_stats['market_state'] = {
                'states': dict(zip(unique_states, state_counts)),
                'min_weight': state_weights.min(),
                'max_weight': state_weights.max(),
                'mean_weight': state_weights.mean()
            }
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 市场状态权重计算完成，"
                       f"状态分布: {dict(zip(unique_states, state_counts))}")
            
            return state_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 市场状态权重计算失败: {e}")
            logger.debug(traceback.format_exc())
            return np.ones(len(df))

    def calculate_rarity_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算样本稀有度权重
        
        Args:
            df: 输入DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            稀有度权重数组
        """
        try:
            if not self.effective_config['enable_rarity_weighting']:
                return np.ones(len(df))
            
            threshold = self.effective_config['rarity_percentile_threshold']
            multiplier = self.effective_config['rarity_weight_multiplier']
            
            # 基于价格变化幅度计算稀有度
            if 'price_change_1p' in df.columns:
                price_changes = np.abs(df['price_change_1p'])
            elif 'close' in df.columns:
                price_changes = np.abs(df['close'].pct_change())
            else:
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 无法计算稀有度权重，缺少价格数据")
                return np.ones(len(df))
            
            # 计算百分位数
            lower_threshold = np.percentile(price_changes.dropna(), threshold * 100)
            upper_threshold = np.percentile(price_changes.dropna(), (1 - threshold) * 100)
            
            # 计算稀有度权重
            rarity_weights = np.ones(len(df))
            rare_mask = (price_changes <= lower_threshold) | (price_changes >= upper_threshold)
            rarity_weights[rare_mask] = multiplier
            
            # 统计信息
            rare_count = rare_mask.sum()
            self.weighting_stats['rarity'] = {
                'rare_samples': rare_count,
                'rare_percentage': rare_count / len(df) * 100,
                'lower_threshold': lower_threshold,
                'upper_threshold': upper_threshold,
                'multiplier': multiplier
            }
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 稀有度权重计算完成，"
                       f"稀有样本: {rare_count}/{len(df)} ({rare_count/len(df)*100:.1f}%)")
            
            return rarity_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 稀有度权重计算失败: {e}")
            logger.debug(traceback.format_exc())
            return np.ones(len(df))

    def _identify_market_states(self, df: pd.DataFrame) -> List[str]:
        """
        识别市场状态
        
        Args:
            df: 输入DataFrame
            
        Returns:
            市场状态列表
        """
        try:
            states = []
            
            # 基于ATR和趋势识别市场状态
            if 'ATRr_14' in df.columns and 'ADX' in df.columns:
                atr = df['ATRr_14']
                adx = df['ADX']
                
                atr_median = atr.median()
                adx_median = adx.median()
                
                for i in range(len(df)):
                    atr_val = atr.iloc[i]
                    adx_val = adx.iloc[i]
                    
                    if atr_val > atr_median and adx_val > adx_median:
                        states.append('trending')
                    elif atr_val > atr_median and adx_val <= adx_median:
                        states.append('volatile')
                    elif atr_val <= atr_median and adx_val > adx_median:
                        states.append('ranging')
                    else:
                        states.append('calm')
            else:
                # 简化状态识别
                states = ['normal'] * len(df)
            
            return states
            
        except Exception as e:
            logger.warning(f"[DynamicSampleWeighter] 市场状态识别失败: {e}")
            return ['normal'] * len(df)

    def combine_weights(self, weight_arrays: Dict[str, np.ndarray], target_name: str) -> np.ndarray:
        """
        组合多个权重数组
        
        Args:
            weight_arrays: 权重数组字典
            target_name: 目标名称（用于日志）
            
        Returns:
            组合后的权重数组
        """
        try:
            combination_method = self.effective_config['combination_method']
            strategy_weights = self.effective_config['strategy_weights']
            
            if not weight_arrays:
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 没有权重数组可组合")
                return np.ones(1)
            
            # 获取数组长度
            array_length = len(next(iter(weight_arrays.values())))
            
            if combination_method == 'weighted_average':
                # 加权平均
                combined_weights = np.zeros(array_length)
                total_weight = 0
                
                for strategy, weights in weight_arrays.items():
                    strategy_weight = strategy_weights.get(strategy, 1.0)
                    combined_weights += weights * strategy_weight
                    total_weight += strategy_weight
                
                if total_weight > 0:
                    combined_weights /= total_weight
                else:
                    combined_weights = np.ones(array_length)
                    
            elif combination_method == 'multiply':
                # 乘积组合
                combined_weights = np.ones(array_length)
                for weights in weight_arrays.values():
                    combined_weights *= weights
                    
            elif combination_method == 'max':
                # 最大值组合
                combined_weights = np.ones(array_length)
                for weights in weight_arrays.values():
                    combined_weights = np.maximum(combined_weights, weights)
                    
            else:
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 未知的组合方法: {combination_method}")
                combined_weights = np.ones(array_length)
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 权重组合完成，方法: {combination_method}")
            
            return combined_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 权重组合失败: {e}")
            logger.debug(traceback.format_exc())
            return np.ones(len(next(iter(weight_arrays.values()))))

    def normalize_weights(self, weights: np.ndarray, target_name: str) -> np.ndarray:
        """
        标准化权重
        
        Args:
            weights: 原始权重数组
            target_name: 目标名称（用于日志）
            
        Returns:
            标准化后的权重数组
        """
        try:
            if not self.effective_config['normalize_weights']:
                return weights
            
            weight_min = self.effective_config['weight_clip_min']
            weight_max = self.effective_config['weight_clip_max']
            target_sum = self.effective_config['target_weight_sum']
            
            # 裁剪权重
            normalized_weights = np.clip(weights, weight_min, weight_max)
            
            # 标准化权重和
            if target_sum is None:
                target_sum = len(weights)  # 默认权重和等于样本数
            
            current_sum = normalized_weights.sum()
            if current_sum > 0:
                normalized_weights = normalized_weights * (target_sum / current_sum)
            else:
                normalized_weights = np.ones(len(weights)) * (target_sum / len(weights))
            
            # 统计信息
            self.weighting_stats['normalization'] = {
                'original_sum': weights.sum(),
                'normalized_sum': normalized_weights.sum(),
                'min_weight': normalized_weights.min(),
                'max_weight': normalized_weights.max(),
                'mean_weight': normalized_weights.mean(),
                'std_weight': normalized_weights.std()
            }
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 权重标准化完成，"
                       f"范围: [{normalized_weights.min():.4f}, {normalized_weights.max():.4f}], "
                       f"总和: {normalized_weights.sum():.2f}")
            
            return normalized_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 权重标准化失败: {e}")
            logger.debug(traceback.format_exc())
            return weights

    def calculate_sample_weights(self, df: pd.DataFrame, target_name: str) -> np.ndarray:
        """
        计算样本权重
        
        Args:
            df: 输入DataFrame
            target_name: 目标名称（用于日志）
            
        Returns:
            样本权重数组
        """
        if not self.effective_config['enable_dynamic_weighting']:
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 动态加权已禁用")
            return np.ones(len(df))
        
        logger.info(f"[DynamicSampleWeighter] ({target_name}) 开始计算样本权重...")
        
        try:
            strategies = self.effective_config['weighting_strategies']
            weight_arrays = {}
            
            # 计算各种权重
            if 'time_decay' in strategies:
                weight_arrays['time_decay'] = self.calculate_time_decay_weights(df, target_name)
            
            if 'volatility' in strategies:
                weight_arrays['volatility'] = self.calculate_volatility_weights(df, target_name)
            
            if 'market_state' in strategies:
                weight_arrays['market_state'] = self.calculate_market_state_weights(df, target_name)
            
            if 'rarity' in strategies:
                weight_arrays['rarity'] = self.calculate_rarity_weights(df, target_name)
            
            # 组合权重
            if weight_arrays:
                combined_weights = self.combine_weights(weight_arrays, target_name)
            else:
                logger.warning(f"[DynamicSampleWeighter] ({target_name}) 没有启用任何权重策略")
                combined_weights = np.ones(len(df))
            
            # 标准化权重
            final_weights = self.normalize_weights(combined_weights, target_name)
            
            # 总体统计
            self.weighting_stats['final'] = {
                'strategies_used': list(weight_arrays.keys()),
                'total_samples': len(df),
                'min_weight': final_weights.min(),
                'max_weight': final_weights.max(),
                'mean_weight': final_weights.mean(),
                'std_weight': final_weights.std(),
                'weight_sum': final_weights.sum()
            }
            
            logger.info(f"[DynamicSampleWeighter] ({target_name}) 样本权重计算完成，"
                       f"策略: {list(weight_arrays.keys())}, "
                       f"权重范围: [{final_weights.min():.4f}, {final_weights.max():.4f}]")
            
            return final_weights
            
        except Exception as e:
            logger.error(f"[DynamicSampleWeighter] ({target_name}) 样本权重计算失败: {e}")
            logger.debug(traceback.format_exc())
            return np.ones(len(df))

    def get_weighting_stats(self) -> Dict[str, Any]:
        """获取权重统计信息"""
        return self.weighting_stats.copy()


# 便捷函数
def calculate_dynamic_sample_weights(df: pd.DataFrame, 
                                   target_config: Dict[str, Any], 
                                   target_name: str) -> np.ndarray:
    """
    计算动态样本权重的便捷函数
    
    Args:
        df: 输入DataFrame
        target_config: 目标配置
        target_name: 目标名称
        
    Returns:
        样本权重数组
    """
    # 提取动态加权配置
    weighting_config = target_config.get('dynamic_sample_weighting', {})
    
    # 创建加权器
    weighter = DynamicSampleWeighter(weighting_config)
    
    # 计算权重
    return weighter.calculate_sample_weights(df, target_name)
