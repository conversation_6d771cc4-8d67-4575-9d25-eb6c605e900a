# realistic_backtest_main.py
"""
真实交易情况回测主程序
完全模拟您的实际交易流程：
1. 1分钟/15分钟K线收盘触发预测
2. 根据信号决定是否开仓
3. 凯利公式决定开仓金额（5-250u，最高不超过本金10%）
"""

import pandas as pd
import numpy as np
import os
import sys
import json
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志记录器
logger = logging.getLogger(__name__)
if not logger.handlers:
    logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

try:
    import config
    from src.core import data_utils
    from src.backtest.realistic_backtest_engine import RealisticBacktestEngine, KellyPositionCalculator
    from src.backtest.backtest_utils import calculate_performance_metrics, calculate_daily_stats, save_results_to_files
    import backtest_config as bt_config
    from src.backtest.backtest_validation import validate_backtest_integrity
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必需的文件都在正确的位置")
    sys.exit(1)

class RealisticBinaryOptionBacktester:
    """真实交易情况二元期权回测器"""

    def __init__(self, config_dict: Dict = None):
        """
        初始化回测器

        Args:
            config_dict: 配置字典，如果为None则使用默认配置
        """
        self.config = config_dict or bt_config.get_backtest_config()

        # 预测间隔配置 - 支持自定义
        self.prediction_intervals = self.config.get('prediction_intervals', ['15m'])

        # 确保预测间隔是列表格式
        if isinstance(self.prediction_intervals, str):
            self.prediction_intervals = [self.prediction_intervals]

        # 触发间隔（与预测间隔相同）
        self.trigger_intervals = self.prediction_intervals

        # 初始化真实交易引擎
        self.engine = RealisticBacktestEngine(
            initial_balance=self.config['initial_balance'],
            payout_ratio=self.config['payout_ratio'],
            prediction_intervals=self.prediction_intervals
        )

        # 模型和数据
        self.models = {}
        self.scalers = {}
        self.feature_lists = {}
        self.meta_model = None
        self.meta_scaler = None
        self.meta_features = None
        self.historical_data = {}  # 存储多个时间间隔的数据

        # 信号生成模式
        self.signal_mode = self.config.get('signal_mode', 'meta_model')  # 'meta_model', 'base_models', 'both'
        
    def load_models(self) -> bool:
        """加载训练好的模型（基础模型和元模型）"""
        try:
            success = True

            # 1. 加载基础模型（如果需要）
            if self.signal_mode in ['base_models', 'both']:
                model_types = ['up', 'down']

                for model_type in model_types:
                    # 加载模型
                    model_path = bt_config.get_model_path(model_type)
                    if os.path.exists(model_path):
                        self.models[model_type] = joblib.load(model_path)
                        if self.config['verbose']:
                            print(f"已加载 {model_type.upper()} 基础模型: {model_path}")
                    else:
                        print(f"警告: 未找到 {model_type.upper()} 基础模型文件: {model_path}")
                        success = False

                    # 加载缩放器
                    scaler_path = bt_config.get_scaler_path(model_type)
                    if os.path.exists(scaler_path):
                        self.scalers[model_type] = joblib.load(scaler_path)
                    else:
                        print(f"警告: 未找到 {model_type.upper()} 缩放器文件: {scaler_path}")
                        self.scalers[model_type] = None

                    # 加载特征列表
                    features_path = bt_config.get_features_path(model_type)
                    if os.path.exists(features_path):
                        with open(features_path, 'r', encoding='utf-8') as f:
                            self.feature_lists[model_type] = json.load(f)
                    else:
                        print(f"警告: 未找到 {model_type.upper()} 特征文件: {features_path}")
                        success = False

            # 2. 加载元模型（如果需要）
            if self.signal_mode in ['meta_model', 'both']:
                # 元模型文件路径 - 使用配置中的路径
                meta_model_dir = bt_config.META_MODEL_DIR
                meta_model_path = os.path.join(meta_model_dir, "meta_model_lgbm.joblib")
                meta_features_path = os.path.join(meta_model_dir, "meta_model_features.json")

                # 加载元模型
                if os.path.exists(meta_model_path):
                    self.meta_model = joblib.load(meta_model_path)
                    if self.config['verbose']:
                        print(f"已加载元模型: {meta_model_path}")
                else:
                    print(f"警告: 未找到元模型文件: {meta_model_path}")
                    success = False

                # 加载元模型特征列表
                if os.path.exists(meta_features_path):
                    with open(meta_features_path, 'r', encoding='utf-8') as f:
                        self.meta_features = json.load(f)
                    if self.config['verbose']:
                        print(f"已加载元模型特征列表: {len(self.meta_features)} 个特征")
                else:
                    print(f"警告: 未找到元模型特征文件: {meta_features_path}")
                    success = False

            return success

        except Exception as e:
            print(f"加载模型时发生错误: {e}")
            return False
    
    def load_historical_data(self) -> bool:
        """加载多个时间间隔的历史数据"""
        try:
            if self.config['verbose']:
                print(f"正在获取历史数据，间隔: {self.prediction_intervals}")

            # 为每个预测间隔获取数据
            for interval in self.prediction_intervals:
                # 计算数据限制（较大间隔需要较少数据点）
                interval_multiplier = {
                    '1m': 1,
                    '5m': 5,
                    '15m': 15,
                    '30m': 30,
                    '1h': 60,
                    '4h': 240,
                    '1d': 1440
                }.get(interval, 15)

                data_limit = max(100, self.config['data_limit'] // interval_multiplier)

                # 获取数据 - 创建匿名客户端
                try:
                    from binance.client import Client
                    binance_client = Client()  # 匿名客户端，用于获取历史数据
                except ImportError:
                    binance_client = None

                data = data_utils.fetch_binance_history(
                    symbol=self.config['symbol'],
                    interval=interval,
                    binance_client=binance_client,
                    limit=data_limit
                )

                if data is None or data.empty:
                    print(f"错误: 无法获取 {interval} 历史数据")
                    return False

                # 过滤时间范围 - 处理时区问题
                start_date = pd.to_datetime(self.config['start_date'])
                end_date = pd.to_datetime(self.config['end_date']) + pd.Timedelta(days=1)  # 包含结束日期

                if self.config['verbose']:
                    print(f"  原始数据时间范围: {data.index[0]} 到 {data.index[-1]}")
                    print(f"  过滤时间范围: {start_date} 到 {end_date}")

                # 确保时区一致性
                if data.index.tz is not None:
                    if start_date.tz is None:
                        start_date = start_date.tz_localize('UTC')
                        end_date = end_date.tz_localize('UTC')
                    data.index = data.index.tz_convert('UTC')
                    start_date = start_date.tz_convert('UTC')
                    end_date = end_date.tz_convert('UTC')

                # 过滤数据
                filtered_data = data[(data.index >= start_date) & (data.index <= end_date)]

                if filtered_data.empty:
                    print(f"警告: {interval} 数据在指定时间范围内为空，使用最近的数据")
                    # 如果过滤后为空，使用最近的数据
                    filtered_data = data.tail(min(200, len(data)))

                data = filtered_data

                self.historical_data[interval] = data

                if self.config['verbose']:
                    print(f"已加载 {interval} 数据: {len(data)} 条记录")

            if not self.historical_data:
                print("错误: 没有成功加载任何历史数据")
                return False

            if self.config['verbose']:
                all_start_times = [data.index[0] for data in self.historical_data.values() if not data.empty]
                all_end_times = [data.index[-1] for data in self.historical_data.values() if not data.empty]
                if all_start_times and all_end_times:
                    print(f"数据时间范围: {min(all_start_times)} 到 {max(all_end_times)}")

            return True

        except Exception as e:
            print(f"加载历史数据时发生错误: {e}")
            return False
    
    def generate_prediction_at_kline_close(self, kline_time: datetime, interval: str) -> Dict:
        """
        在K线收盘时生成预测 - 完全模拟您的真实预测流程

        Args:
            kline_time: K线收盘时间
            interval: K线间隔 (如 '1m', '15m', '5m' 等)

        Returns:
            预测结果字典
        """
        try:
            # 获取到当前K线收盘时间为止的历史数据
            if interval not in self.historical_data:
                return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'no_data'}

            historical_data = self.historical_data[interval][self.historical_data[interval].index <= kline_time]

            if len(historical_data) < 100:  # 需要足够的历史数据
                return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'insufficient_data'}

            # 根据信号模式选择预测方法
            if self.signal_mode == 'meta_model':
                return self._generate_meta_model_prediction(historical_data, kline_time, interval)
            elif self.signal_mode == 'base_models':
                return self._generate_base_models_prediction(historical_data, kline_time, interval)
            elif self.signal_mode == 'both':
                # 先生成基础模型预测，再用元模型综合
                base_predictions = self._generate_base_models_prediction(historical_data, kline_time, interval)
                meta_prediction = self._generate_meta_model_prediction(historical_data, kline_time, interval, base_predictions)
                return meta_prediction
            else:
                return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'unknown_mode'}
            
        except Exception as e:
            if self.config['verbose']:
                print(f"生成预测时发生错误: {e}")
            return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'error'}

    def _generate_base_models_prediction(self, historical_data: pd.DataFrame, kline_time: datetime, interval: str) -> Dict:
        """生成基础模型预测"""
        try:
            # 生成特征 - 使用与主程序完全相同的逻辑
            target_config = {
                'symbol': self.config['symbol'],
                'interval': interval,
                'prediction_periods': [self.config.get('prediction_periods', 30)],
                'target_variable_type': 'UP_ONLY',
                'target_threshold': 0.002,
                'min_historical_bars_for_prediction': 100,

                # 特征工程配置 - 与主程序保持完全一致
                'enable_price_change': bt_config.ENABLE_PRICE_CHANGE,
                'price_change_periods': bt_config.PRICE_CHANGE_PERIODS,
                'enable_volume': bt_config.ENABLE_VOLUME,
                'volume_avg_period': bt_config.VOLUME_AVG_PERIOD,
                'enable_candle': bt_config.ENABLE_CANDLE,
                'enable_ta': bt_config.ENABLE_TA,
                'enable_time': bt_config.ENABLE_TIME,
                'enable_trend': bt_config.ENABLE_TREND,
                'enable_mtfa': bt_config.ENABLE_MTFA,
                'mtfa_timeframes': bt_config.MTFA_TIMEFRAMES,
                'enable_fund_flow': bt_config.ENABLE_FUND_FLOW
            }

            # 生成特征
            df_with_features = data_utils.add_classification_features(
                historical_data.copy(), target_config
            )

            if df_with_features is None or df_with_features.empty:
                return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'no_features'}

            # 获取最新的特征向量
            if kline_time not in df_with_features.index:
                return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'no_current_features'}

            current_features = df_with_features.loc[kline_time]

            # 为每个基础模型生成预测
            predictions = {}
            for model_type in ['up', 'down']:
                if model_type not in self.models or model_type not in self.feature_lists:
                    predictions[f'{model_type}_prob'] = 0.5
                    continue

                try:
                    # 获取模型需要的特征
                    required_features = self.feature_lists[model_type]
                    feature_values = []

                    for feat_name in required_features:
                        if feat_name in current_features.index:
                            feat_val = current_features[feat_name]
                            if pd.isna(feat_val) or np.isinf(feat_val):
                                feat_val = 0.0
                            feature_values.append(feat_val)
                        else:
                            feature_values.append(0.0)

                    feature_vector = np.array(feature_values).reshape(1, -1)

                    # 缩放特征
                    if self.scalers[model_type] is not None:
                        feature_vector = self.scalers[model_type].transform(feature_vector)

                    # 模型预测
                    model = self.models[model_type]
                    prob_array = model.predict_proba(feature_vector)[0]

                    if len(prob_array) > 1:
                        raw_prob = prob_array[1]  # 正类概率

                        # 🎯 修复元模型特征语义：保持DOWN模型原始预测逻辑
                        if model_type == 'down':
                            # DOWN模型：保持原始下跌概率，让元模型正确理解
                            # DOWN模型的高概率表示强烈看跌
                            prob = raw_prob  # 保持下跌概率原始语义
                        else:
                            # UP模型，直接使用看涨概率
                            prob = raw_prob
                    else:
                        prob = prob_array[0]

                    prob = max(0.0, min(1.0, float(prob)))
                    predictions[f'{model_type}_prob'] = prob

                except Exception as e:
                    if self.config['verbose']:
                        print(f"基础模型 {model_type} 预测失败: {e}")
                    predictions[f'{model_type}_prob'] = 0.5

            # 生成交易信号
            up_prob = predictions.get('up_prob', 0.5)
            down_prob = predictions.get('down_prob', 0.5)

            signal = 'neutral'
            if up_prob > self.config.get('signal_threshold_up', 0.7) and up_prob > down_prob:
                signal = 'up'
            elif down_prob > self.config.get('signal_threshold_down', 0.7) and down_prob > up_prob:
                signal = 'down'

            return {
                'signal': signal,
                'up_prob': up_prob,
                'down_prob': down_prob,
                'interval': interval,
                'kline_time': kline_time,
                'source': 'base_models'
            }

        except Exception as e:
            if self.config['verbose']:
                print(f"基础模型预测失败: {e}")
            return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'base_error'}

    def _generate_base_models_prediction_simple(self, historical_data: pd.DataFrame, kline_time: datetime,
                                               interval: str) -> Dict:
        """
        生成基础模型预测 - 简化版本：专为元模型设计

        这个方法专门为元模型提供基础预测，避免重复的复杂特征工程
        只返回基础模型的预测概率，不进行信号判断
        """
        try:
            # 如果基础模型未加载，返回中性预测
            if not self.models or 'up' not in self.models or 'down' not in self.models:
                return {'up_prob': 0.5, 'down_prob': 0.5, 'source': 'no_base_models'}

            # 使用已有的基础模型预测方法，但只返回概率
            full_prediction = self._generate_base_models_prediction(historical_data, kline_time, interval)

            return {
                'up_prob': full_prediction.get('up_prob', 0.5),
                'down_prob': full_prediction.get('down_prob', 0.5),
                'source': 'base_models_simple'
            }

        except Exception as e:
            if self.config['verbose']:
                print(f"简化基础模型预测失败: {e}")
            return {'up_prob': 0.5, 'down_prob': 0.5, 'source': 'base_simple_error'}

    def _apply_backtest_meta_feature_engineering(self, base_predictions: Dict, kline_time: datetime) -> Dict:
        """
        在回测中应用元模型特征工程，与训练和实时预测保持一致

        参数:
        - base_predictions: 基础模型预测结果
        - kline_time: 当前K线时间

        返回:
        - 添加了特征工程的字典
        """
        # 创建基础概率特征字典
        meta_input_data = {}

        # 从基础预测中提取概率特征
        # 根据实际的基础模型配置来映射特征名
        if 'up_prob' in base_predictions:
            meta_input_data['prob_UP_Model'] = base_predictions['up_prob']
        if 'down_prob' in base_predictions:
            meta_input_data['prob_DOWN_Model'] = base_predictions['down_prob']

        # 如果基础预测中包含具体的模型概率，也需要添加
        for key, value in base_predictions.items():
            if key.endswith('_prob') and key not in ['up_prob', 'down_prob']:
                # 将基础模型的概率映射到元模型特征名
                # 例如：'BTC_15m_UP_prob' -> 'prob_BTC_15m_UP'
                model_name = key.replace('_prob', '')
                meta_feature_name = f'prob_{model_name}'
                meta_input_data[meta_feature_name] = value

        # 识别UP和DOWN模型的概率特征
        up_prob_features = [key for key in meta_input_data.keys() if 'UP' in key.upper()]
        down_prob_features = [key for key in meta_input_data.keys() if 'DOWN' in key.upper()]

        # 1. 基础模型概率差异 (Probability Difference)
        if len(up_prob_features) >= 1 and len(down_prob_features) >= 1:
            if len(up_prob_features) == 1:
                up_prob = meta_input_data[up_prob_features[0]]
            else:
                up_prob = np.mean([meta_input_data[key] for key in up_prob_features])

            if len(down_prob_features) == 1:
                down_prob = meta_input_data[down_prob_features[0]]
            else:
                down_prob = np.mean([meta_input_data[key] for key in down_prob_features])

            meta_input_data['meta_prob_diff_up_vs_down'] = up_prob - down_prob

            # 2. 基础模型概率总和 (Combined Conviction)
            meta_input_data['meta_prob_sum_up_down'] = up_prob + down_prob

        # 3. 滞后特征和变化特征 - 从历史状态获取
        # 获取上一次的预测概率（如果有的话）
        last_predictions = self._get_last_meta_predictions(kline_time)

        # 为每个基础概率特征添加滞后和变化特征
        for key in list(meta_input_data.keys()):
            if key.startswith('prob_') or any(model_name in key for model_name in ['UP', 'DOWN']):
                current_prob = meta_input_data[key]

                # 滞后特征
                lag_feature_name = f"meta_lag1_{key}"
                if last_predictions and key in last_predictions:
                    lag_value = last_predictions[key]
                else:
                    # 如果没有历史数据，使用当前值
                    lag_value = current_prob
                meta_input_data[lag_feature_name] = lag_value

                # 变化特征
                change_feature_name = f"meta_change1_{key}"
                if last_predictions and key in last_predictions:
                    change_value = current_prob - last_predictions[key]
                else:
                    # 如果没有历史数据，变化为0
                    change_value = 0.0
                meta_input_data[change_feature_name] = change_value

        # 保存当前预测概率供下次使用
        current_predictions = {key: val for key, val in meta_input_data.items()
                             if key.startswith('prob_') or any(model_name in key for model_name in ['UP', 'DOWN'])}
        self._save_last_meta_predictions(kline_time, current_predictions)

        return meta_input_data

    def _get_last_meta_predictions(self, current_time: datetime) -> Dict:
        """获取上一次的元模型预测概率"""
        if not hasattr(self, '_meta_prediction_history'):
            self._meta_prediction_history = {}

        # 查找最近的历史预测
        last_predictions = None
        last_time = None

        for time_key, predictions in self._meta_prediction_history.items():
            if time_key < current_time:
                if last_time is None or time_key > last_time:
                    last_time = time_key
                    last_predictions = predictions

        return last_predictions

    def _save_last_meta_predictions(self, current_time: datetime, predictions: Dict):
        """保存当前的元模型预测概率"""
        if not hasattr(self, '_meta_prediction_history'):
            self._meta_prediction_history = {}

        self._meta_prediction_history[current_time] = predictions.copy()

        # 清理过老的历史数据，只保留最近的几个时间点
        if len(self._meta_prediction_history) > 10:
            sorted_times = sorted(self._meta_prediction_history.keys())
            for old_time in sorted_times[:-10]:
                del self._meta_prediction_history[old_time]

    def _generate_meta_model_prediction(self, historical_data: pd.DataFrame, kline_time: datetime,
                                       interval: str, base_predictions: Dict = None) -> Dict:
        """
        生成元模型预测 - 集成特征工程版本：使用与训练和实时预测相同的特征工程

        元模型的设计理念：
        - 接收基础模型的预测概率作为主要特征
        - 应用与训练时相同的特征工程
        - 输出三种信号：上涨(up)、下跌(down)、中性(neutral)
        """
        try:
            if self.meta_model is None or self.meta_features is None:
                return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'no_meta_model'}

            # 如果没有基础预测，先生成
            if base_predictions is None:
                base_predictions = self._generate_base_models_prediction_simple(historical_data, kline_time, interval)

            # 应用元模型特征工程（与训练和实时预测保持一致）
            meta_features_dict = self._apply_backtest_meta_feature_engineering(base_predictions, kline_time)

            # 确保所有训练时的特征都存在，缺失的用默认值填充
            for feature_name in self.meta_features:
                if feature_name not in meta_features_dict:
                    if 'prob' in feature_name.lower():
                        default_value = 0.5
                    else:
                        default_value = 0.0
                    meta_features_dict[feature_name] = default_value

            # 按照训练时的特征顺序构建特征向量
            feature_values = []
            for feat_name in self.meta_features:
                if feat_name in meta_features_dict:
                    feat_val = meta_features_dict[feat_name]
                    if pd.isna(feat_val) or np.isinf(feat_val):
                        feat_val = 0.5 if 'prob' in feat_name.lower() else 0.0
                    feature_values.append(feat_val)
                else:
                    feature_values.append(0.5 if 'prob' in feat_name.lower() else 0.0)

            # 元模型预测
            meta_input = np.array(feature_values).reshape(1, -1)
            meta_pred_class = self.meta_model.predict(meta_input)[0]
            meta_probas = self.meta_model.predict_proba(meta_input)[0]

            # 解析元模型输出（假设：0=DOWN, 1=UP, 2=NEUTRAL）
            if meta_pred_class == 1:  # UP
                signal = 'up'
                up_prob = meta_probas[1] if len(meta_probas) > 1 else 0.5
                down_prob = meta_probas[0] if len(meta_probas) > 0 else 0.5
            elif meta_pred_class == 0:  # DOWN
                signal = 'down'
                up_prob = meta_probas[1] if len(meta_probas) > 1 else 0.5
                down_prob = meta_probas[0] if len(meta_probas) > 0 else 0.5
            else:  # NEUTRAL
                signal = 'neutral'
                up_prob = meta_probas[1] if len(meta_probas) > 1 else 0.5
                down_prob = meta_probas[0] if len(meta_probas) > 0 else 0.5

            return {
                'signal': signal,
                'up_prob': float(up_prob),
                'down_prob': float(down_prob),
                'meta_class': int(meta_pred_class),
                'meta_probas': [float(p) for p in meta_probas],
                'interval': interval,
                'kline_time': kline_time,
                'source': 'meta_model_enhanced',
                'base_predictions': base_predictions,
                'meta_features_used': len(feature_values),
                'meta_features_dict': meta_features_dict if self.config.get('verbose', False) else None
            }

        except Exception as e:
            if self.config['verbose']:
                print(f"元模型预测失败: {e}")
                import traceback
                traceback.print_exc()
            return {'signal': 'neutral', 'up_prob': 0.5, 'down_prob': 0.5, 'source': 'meta_error'}
    
    def run_realistic_backtest(self) -> bool:
        """
        运行真实交易情况回测
        
        Returns:
            是否成功完成回测
        """
        try:
            if self.config['verbose']:
                print("开始真实交易情况回测...")
                print(f"触发间隔: {self.trigger_intervals}")
                print(f"初始资金: ${self.config['initial_balance']}")
                print(f"凯利公式限制: 最低{self.engine.kelly_calculator.min_bet}u, "
                      f"最高{self.engine.kelly_calculator.max_bet}u, "
                      f"最高不超过本金{self.engine.kelly_calculator.max_capital_ratio:.0%}")
            
            # 获取所有K线收盘时间点
            all_kline_times = set()
            
            for interval in self.trigger_intervals:
                if interval in self.historical_data:
                    kline_times = self.historical_data[interval].index
                    for kline_time in kline_times:
                        all_kline_times.add((kline_time, interval))
            
            # 按时间排序
            sorted_kline_events = sorted(all_kline_times, key=lambda x: x[0])
            
            total_events = len(sorted_kline_events)
            processed_events = 0
            
            if self.config['verbose']:
                print(f"总共 {total_events} 个K线收盘事件需要处理")
            
            # 逐个处理K线收盘事件
            for kline_time, interval in sorted_kline_events:
                processed_events += 1
                
                # 处理已到期的交易
                current_price = None
                if interval in self.historical_data and kline_time in self.historical_data[interval].index:
                    current_price = self.historical_data[interval].loc[kline_time, 'close']
                    self.engine.process_expired_trades(kline_time, current_price)
                
                # 在K线收盘时触发预测
                prediction_result = self.generate_prediction_at_kline_close(kline_time, interval)
                
                # 如果有信号，尝试开仓
                if prediction_result['signal'] != 'neutral' and current_price is not None:
                    kline_data = {
                        'timestamp': kline_time,
                        'interval': interval,
                        'close': current_price
                    }
                    
                    trade = self.engine.process_kline_close(kline_data, prediction_result)
                    
                    if trade and self.config['verbose'] and processed_events % 100 == 0:
                        print(f"已处理 {processed_events}/{total_events} 个事件, "
                              f"开仓: {trade.direction} ${trade.amount:.2f}, "
                              f"当前余额: ${self.engine.current_balance:.2f}")
                
                elif self.config['verbose'] and processed_events % 500 == 0:
                    print(f"已处理 {processed_events}/{total_events} 个事件")
            
            # 处理剩余的活跃交易
            if self.engine.active_trades:
                # 获取最后的时间和价格
                final_times = []
                final_prices = []

                for interval in self.historical_data:
                    if not self.historical_data[interval].empty:
                        final_times.append(self.historical_data[interval].index[-1])
                        final_prices.append(self.historical_data[interval].iloc[-1]['close'])

                if final_times:
                    final_time = max(final_times)
                    final_price = final_prices[final_times.index(final_time)]

                    if self.config['verbose']:
                        print(f"处理剩余的 {len(self.engine.active_trades)} 个活跃交易")

                    self.engine.process_expired_trades(final_time, final_price)
            
            # 输出最终统计
            if self.config['verbose']:
                stats = self.engine.get_summary_stats()
                print(f"\n=== 真实交易情况回测完成 ===")
                print(f"K线触发统计: {stats['kline_triggers']}")
                print(f"总交易数: {stats['total_trades']}")
                print(f"胜率: {stats['win_rate']:.2%}")
                print(f"最终余额: ${stats['final_balance']:.2f}")
                print(f"总收益: {stats['return_percentage']:.2%}")
                print(f"平均交易金额: ${stats['avg_trade_amount']:.2f}")
                print(f"最大交易金额: ${stats['max_trade_amount']:.2f}")
            
            return True

        except Exception as e:
            logger.error(f"执行真实交易情况回测时发生错误: {e}")
            logger.exception("执行真实交易情况回测错误的详细堆栈跟踪:")
            print(f"执行真实交易情况回测时发生错误: {e}")
            return False

    def analyze_results(self) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
        """分析回测结果"""
        try:
            # 获取交易记录
            trades_df = self.engine.get_trades_dataframe()

            # 计算性能指标
            metrics = calculate_performance_metrics(trades_df, self.config['initial_balance'])

            # 添加真实交易特有的指标
            engine_stats = self.engine.get_summary_stats()
            kline_stats = engine_stats.get('kline_triggers', {})

            # 计算凯利效率
            executed_trades = kline_stats.get('executed_trades', 0)
            total_signals = kline_stats.get('total_signals', 0)
            kelly_efficiency = executed_trades / max(total_signals, 1) if total_signals > 0 else 0

            # 计算最小交易金额
            min_trade_amount = min([t.amount for t in self.engine.trades]) if self.engine.trades else 0

            metrics.update({
                'kline_triggers': kline_stats,
                'avg_trade_amount': engine_stats.get('avg_trade_amount', 0),
                'max_trade_amount': engine_stats.get('max_trade_amount', 0),
                'min_trade_amount': min_trade_amount,
                'kelly_efficiency': kelly_efficiency
            })

            # 计算每日统计
            daily_stats = calculate_daily_stats(trades_df, self.config['initial_balance'])

            return metrics, trades_df, daily_stats

        except Exception as e:
            print(f"分析结果时发生错误: {e}")
            return {}, pd.DataFrame(), pd.DataFrame()

    def save_results(self, metrics: Dict, trades_df: pd.DataFrame, daily_stats: pd.DataFrame) -> None:
        """保存回测结果"""
        try:
            # 创建输出目录
            bt_config.create_output_dirs()

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存结果
            save_results_to_files(
                metrics=metrics,
                trades_df=trades_df,
                daily_stats=daily_stats,
                output_dir=self.config['output_dir'],
                timestamp=timestamp
            )

            # 保存真实交易配置
            realistic_config = {
                'backtest_type': 'realistic_trading_simulation',
                'trigger_intervals': self.trigger_intervals,
                'kelly_calculator_settings': {
                    'min_bet': self.engine.kelly_calculator.min_bet,
                    'max_bet': self.engine.kelly_calculator.max_bet,
                    'max_capital_ratio': self.engine.kelly_calculator.max_capital_ratio,
                    'payout_ratio': self.engine.kelly_calculator.payout_ratio
                },
                'original_config': self.config
            }

            config_file = os.path.join(self.config['output_dir'], f"realistic_backtest_config_{timestamp}.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(realistic_config, f, indent=2, ensure_ascii=False, default=str)

            print(f"真实交易回测配置已保存: {config_file}")

        except Exception as e:
            print(f"保存结果时发生错误: {e}")

    def run_full_realistic_backtest(self) -> bool:
        """运行完整的真实交易情况回测流程"""
        print("=== 真实交易情况二元期权回测开始 ===")
        print(f"交易对: {self.config['symbol']}")
        print(f"时间范围: {self.config['start_date']} 到 {self.config['end_date']}")
        print(f"初始资金: ${self.config['initial_balance']}")
        print(f"盈亏比: {self.config['payout_ratio']}")
        print(f"触发间隔: {', '.join(self.prediction_intervals)}K线收盘")
        print(f"信号模式: {self.signal_mode}")
        print(f"凯利公式: 最低5u, 最高250u, 最高不超过本金10%")
        print()

        # 1. 加载模型
        print("1. 加载模型...")
        if not self.load_models():
            print("错误: 模型加载失败")
            return False

        # 2. 加载历史数据
        print(f"2. 加载{', '.join(self.prediction_intervals)}历史数据...")
        if not self.load_historical_data():
            print("错误: 历史数据加载失败")
            return False

        # 3. 执行真实交易情况回测
        print("3. 执行真实交易情况回测...")
        if not self.run_realistic_backtest():
            print("错误: 回测执行失败")
            return False

        # 4. 分析结果
        print("4. 分析结果...")
        metrics, trades_df, daily_stats = self.analyze_results()

        # 5. 保存结果
        if self.config['generate_report']:
            print("5. 保存结果...")
            self.save_results(metrics, trades_df, daily_stats)

        # 6. 显示总结
        print("\n=== 真实交易情况回测结果总结 ===")
        if metrics:
            print(f"K线触发统计:")
            kline_stats = metrics.get('kline_triggers', {})
            for interval in self.prediction_intervals:
                print(f"  - {interval}K线触发: {kline_stats.get(interval, 0)} 次")
            print(f"  - 总信号数: {kline_stats.get('total_signals', 0)}")
            print(f"  - 实际执行交易: {kline_stats.get('executed_trades', 0)}")
            print(f"  - 凯利效率: {metrics.get('kelly_efficiency', 0):.1%}")
            print()
            print(f"交易统计:")
            print(f"  - 总交易次数: {metrics.get('total_trades', 0)}")
            print(f"  - 胜率: {metrics.get('win_rate', 0):.2%}")
            print(f"  - 总收益率: {metrics.get('total_return', 0):.2%}")
            print(f"  - 最大回撤: {metrics.get('max_drawdown', 0):.2%}")
            print(f"  - 夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
            print(f"  - 盈利因子: {metrics.get('profit_factor', 0):.2f}")
            print()
            print(f"凯利公式统计:")
            print(f"  - 平均交易金额: ${metrics.get('avg_trade_amount', 0):.2f}")
            print(f"  - 最大交易金额: ${metrics.get('max_trade_amount', 0):.2f}")
            print(f"  - 最小交易金额: ${metrics.get('min_trade_amount', 0):.2f}")

        print("\n=== 真实交易情况回测完成 ===")
        return True

def run_realistic_backtest(**kwargs):
    """
    运行真实交易回测的包装函数
    用于与优化器兼容

    Args:
        **kwargs: 回测参数

    Returns:
        dict: 回测结果
    """
    try:
        # 创建配置字典
        config_dict = {}

        # 从kwargs中提取配置参数
        if 'start_date' in kwargs:
            config_dict['start_date'] = kwargs['start_date']
        if 'end_date' in kwargs:
            config_dict['end_date'] = kwargs['end_date']
        if 'initial_balance' in kwargs:
            config_dict['initial_balance'] = kwargs['initial_balance']
        if 'fixed_amount' in kwargs:
            config_dict['fixed_amount'] = kwargs['fixed_amount']
        if 'up_threshold' in kwargs:
            config_dict['signal_threshold_up'] = kwargs['up_threshold']
        if 'down_threshold' in kwargs:
            config_dict['signal_threshold_down'] = kwargs['down_threshold']

        # 创建回测器
        backtester = RealisticBinaryOptionBacktester(config_dict)

        # 加载模型
        if not backtester.load_models():
            return {
                'win_rate': 0.5,
                'total_return_pct': 0.0,
                'max_drawdown_pct': 0.0,
                'sharpe_ratio': 0.0,
                'total_trades': 0,
                'up_threshold': kwargs.get('up_threshold', 0.7),
                'down_threshold': kwargs.get('down_threshold', 0.7)
            }

        # 加载历史数据
        if not backtester.load_historical_data():
            return {
                'win_rate': 0.5,
                'total_return_pct': 0.0,
                'max_drawdown_pct': 0.0,
                'sharpe_ratio': 0.0,
                'total_trades': 0,
                'up_threshold': kwargs.get('up_threshold', 0.7),
                'down_threshold': kwargs.get('down_threshold', 0.7)
            }

        # 运行回测
        if backtester.run_realistic_backtest():
            # 获取结果
            stats = backtester.engine.get_summary_stats()

            return {
                'win_rate': stats.get('win_rate', 0.5),
                'total_return_pct': stats.get('total_return_pct', 0.0),
                'max_drawdown_pct': stats.get('max_drawdown_pct', 0.0),
                'sharpe_ratio': stats.get('sharpe_ratio', 0.0),
                'total_trades': stats.get('total_trades', 0),
                'up_threshold': kwargs.get('up_threshold', 0.7),
                'down_threshold': kwargs.get('down_threshold', 0.7)
            }
        else:
            return {
                'win_rate': 0.5,
                'total_return_pct': 0.0,
                'max_drawdown_pct': 0.0,
                'sharpe_ratio': 0.0,
                'total_trades': 0,
                'up_threshold': kwargs.get('up_threshold', 0.7),
                'down_threshold': kwargs.get('down_threshold', 0.7)
            }

    except Exception as e:
        print(f"回测包装函数错误: {e}")
        return {
            'win_rate': 0.5,
            'total_return_pct': 0.0,
            'max_drawdown_pct': 0.0,
            'sharpe_ratio': 0.0,
            'total_trades': 0,
            'up_threshold': kwargs.get('up_threshold', 0.7),
            'down_threshold': kwargs.get('down_threshold', 0.7)
        }

def main():
    """主函数"""
    try:
        # 创建真实交易回测器
        backtester = RealisticBinaryOptionBacktester()

        # 运行回测
        success = backtester.run_full_realistic_backtest()

        if success:
            print("真实交易情况回测成功完成!")
            return 0
        else:
            print("真实交易情况回测失败!")
            return 1

    except KeyboardInterrupt:
        print("\n回测被用户中断")
        return 1
    except Exception as e:
        logger.critical(f"回测过程中发生未知错误: {e}")
        logger.exception("回测未知错误的详细堆栈跟踪:")
        print(f"回测过程中发生未知错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
