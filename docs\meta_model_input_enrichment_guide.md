# 元模型输入丰富化使用指南

## 概述

元模型输入丰富化系统基于"元模型性能上限取决于输入信息质量"的理念，将高阶特征、交互特征、聪明钱特征等作为上下文特征输入给元模型，显著提升元模型的决策能力和预测准确性。

## 核心理念

### 传统元模型输入
- **基础预测概率**: 各基础模型的预测概率
- **简单上下文**: ATR、ADX、RSI等基础指标
- **信息有限**: 缺乏深层市场洞察

### 丰富化元模型输入
- **多维特征**: 高阶、交互、聪明钱、市场状态、动量特征
- **深度信息**: 从单点值到趋势变化、从技术面到资金面
- **智能组合**: 特征间的交互和组合效应

## 特征类别详解

### 📊 1. 基础模型预测概率
```python
'base_model_predictions': {
    'p_favorable': 0.65,    # 基础模型预测的有利概率
    'p_up': 0.45,          # 上涨概率
    'p_down': 0.35         # 下跌概率
}
```

### 🎯 2. 传统上下文特征
```python
'traditional_context': {
    'atr_percent': 1.5,           # ATR百分比
    'adx_value': 35.0,            # ADX趋势强度值
    'prediction_confidence': 0.3, # 预测置信度
    'rsi_value': 65.0,            # RSI值
    'volume_ratio': 1.2,          # 成交量比率
    'price_change_1p': 0.01       # 1期价格变化
}
```

### 🚀 3. 高阶特征（速度、加速度）
```python
'higher_order_features': {
    'rsi_velocity': -1.5,         # RSI速度（一阶导数）
    'rsi_acceleration': -1.0,     # RSI加速度（二阶导数）
    'rsi_momentum_strength': 1.4, # RSI动量强度
    'rsi_adaptive_velocity': 0.8, # RSI自适应速度
    'macd_velocity': 2.1,         # MACD速度
    'macd_acceleration': -0.5,    # MACD加速度
    'macd_hist_velocity': 1.2,    # MACD柱状图速度
    'macd_hist_acceleration': 0.3,# MACD柱状图加速度
    'adx_velocity': 0.9,          # ADX速度
    'adx_acceleration': -0.2      # ADX加速度
}
```

### 🤝 4. 交互特征（强强联合）
```python
'interaction_features': {
    'volatility_trend_strength': 4584.2,      # 波动率-趋势强度交互
    'volatility_trend_strength_norm': 1.86,   # 标准化版本
    'price_volume_interaction_1': -0.0007,    # 量价交互特征1
    'price_volume_interaction_2': 0.0012,     # 量价交互特征2
    'price_volume_interaction_3': -0.0003,    # 量价交互特征3
    'rsi_macd_momentum_resonance': 25.3,      # RSI-MACD动量共振
    'bb_atr_compression_expansion': 1.4,      # 布林带-ATR压缩扩张
    'volume_volatility_efficiency': 2.8       # 成交量-波动率效率
}
```

### 💰 5. 聪明钱特征（深化衍生品分析）
```python
'smart_money_features': {
    'smart_money_divergence': 0.18,           # 聪明钱背离指标
    'smart_money_divergence_strength': 1.41,  # 背离强度
    'smart_money_divergence_trend': 0.02,     # 背离趋势
    'smart_money_divergence_persistence': 3,  # 背离持续性
    'funding_price_divergence': -0.0001,      # 资金费率-价格背离
    'funding_rate_extremity': 0,              # 资金费率极值状态
    'longshort_price_divergence_ratio': 2.5,  # 多空比-价格背离比率
    'longshort_price_divergence_signal': 1,   # 背离信号
    'smart_money_sentiment_index': 0.3        # 综合情绪指数
}
```

### 🎯 6. 市场状态特征
```python
'market_state_features': {
    'market_state': 1,              # 市场状态 (0:calm, 1:trending, 2:volatile, 3:ranging)
    'atr_relative_position': 1.24,  # ATR相对位置
    'adx_relative_position': 1.50,  # ADX相对位置
    'volatility_zscore': 0.8,       # 波动率Z分数
    'trend_strength': 3             # 趋势强度等级 (0:无, 1:弱, 2:中, 3:强)
}
```

### 📈 7. 动量特征
```python
'momentum_features': {
    'rsi_momentum': 0.3,                    # RSI动量（偏离中性值程度）
    'rsi_state': 1,                         # RSI状态 (-2:超卖, -1:卖出, 0:中性, 1:买入, 2:超买)
    'macd_divergence': 5.2,                 # MACD背离
    'macd_signal_strength': 5.2,            # MACD信号强度
    'momentum_price_change_1p': 0.01,       # 动量价格变化1期
    'momentum_price_change_3p': 0.025,      # 动量价格变化3期
    'momentum_price_change_5p': 0.04        # 动量价格变化5期
}
```

## 使用方法

### 1. 自动集成（推荐）

#### 在目标配置中启用
```python
target_config = {
    'name': 'BTC_5M',
    'interval': '5m',
    
    # 🔑 启用元模型输入丰富化
    'meta_model_input_enrichment': {
        'enable_enrichment': True,
        'include_higher_order_features': True,
        'include_interaction_features': True,
        'include_smart_money_features': True,
        'include_market_state_features': True,
        'include_momentum_features': True,
        'feature_selection_threshold': 0.001,
        'max_features_per_category': 10,
        'normalize_features': True
    },
    
    # 确保依赖特征可用
    'enable_advanced_feature_engineering': True,
    'enable_derivatives': True  # 聪明钱特征需要
}
```

#### 自动应用
系统会在`run_prediction_cycle_for_target`函数中自动应用特征丰富化：

```python
# 在prediction.py中自动执行
enriched_context_features = enrich_meta_model_context_features(
    df_klines_main, context_features, target_config_static, target_name
)
```

### 2. 手动使用

#### 直接使用丰富化器
```python
from src.core.meta_model_input_enrichment import MetaModelInputEnricher

# 创建丰富化器
config = {
    'enable_enrichment': True,
    'include_higher_order_features': True,
    'include_interaction_features': True,
    'include_smart_money_features': True,
    'include_market_state_features': True,
    'include_momentum_features': True
}

enricher = MetaModelInputEnricher(config)

# 基础上下文特征
existing_context = {
    'atr_percent': 1.5,
    'adx_value': 35.0,
    'prediction_confidence': 0.3,
    'rsi_value': 65.0,
    'volume_ratio': 1.2,
    'price_change_1p': 0.01
}

# 丰富化特征
enriched_context = enricher.enrich_context_features(df, existing_context, target_name)
```

#### 使用便捷函数
```python
from src.core.meta_model_input_enrichment import enrich_meta_model_context_features

enriched_context = enrich_meta_model_context_features(
    df, existing_context, target_config, target_name
)
```

### 3. 分类别提取

#### 只提取特定类别特征
```python
# 只提取高阶特征
higher_order_features = enricher.extract_higher_order_context_features(df, target_name)

# 只提取交互特征
interaction_features = enricher.extract_interaction_context_features(df, target_name)

# 只提取聪明钱特征
smart_money_features = enricher.extract_smart_money_context_features(df, target_name)

# 只提取市场状态特征
market_state_features = enricher.extract_market_state_context_features(df, target_name)

# 只提取动量特征
momentum_features = enricher.extract_momentum_context_features(df, target_name)
```

## 配置策略

### 1. 保守策略（稳定优先）
```python
conservative_enrichment = {
    'enable_enrichment': True,
    'include_higher_order_features': True,   # 启用：计算成本低，效果好
    'include_interaction_features': False,   # 禁用：减少复杂性
    'include_smart_money_features': True,    # 启用：高价值特征
    'include_market_state_features': True,   # 启用：重要的环境信息
    'include_momentum_features': False,      # 禁用：可能与其他特征重复
    'max_features_per_category': 5,          # 限制特征数量
    'feature_selection_threshold': 0.002     # 更严格的阈值
}
```

### 2. 平衡策略（推荐配置）
```python
balanced_enrichment = {
    'enable_enrichment': True,
    'include_higher_order_features': True,
    'include_interaction_features': True,
    'include_smart_money_features': True,
    'include_market_state_features': True,
    'include_momentum_features': True,
    'max_features_per_category': 10,
    'feature_selection_threshold': 0.001,
    'normalize_features': True
}
```

### 3. 激进策略（最大信息）
```python
aggressive_enrichment = {
    'enable_enrichment': True,
    'include_higher_order_features': True,
    'include_interaction_features': True,
    'include_smart_money_features': True,
    'include_market_state_features': True,
    'include_momentum_features': True,
    'max_features_per_category': 15,         # 更多特征
    'feature_selection_threshold': 0.0005,   # 更宽松的阈值
    'normalize_features': True
}
```

### 4. 高性能策略（计算优化）
```python
performance_enrichment = {
    'enable_enrichment': True,
    'include_higher_order_features': True,   # 低成本，高效果
    'include_interaction_features': False,   # 高成本，暂时禁用
    'include_smart_money_features': True,    # 中等成本，高价值
    'include_market_state_features': True,   # 低成本，重要信息
    'include_momentum_features': False,      # 中等成本，可能重复
    'max_features_per_category': 8,
    'feature_selection_threshold': 0.001
}
```

## 增强的META_MODEL_INPUT_FEATURES_CONFIG

### 使用增强配置
```python
from src.config.enhanced_meta_model_config import (
    get_enhanced_meta_model_config,
    get_meta_model_enrichment_config,
    get_all_feature_names,
    get_required_features,
    validate_context_features
)

# 获取完整的元模型配置
meta_config = get_enhanced_meta_model_config()

# 获取丰富化配置
enrichment_config = get_meta_model_enrichment_config()

# 获取所有特征名称
all_features = get_all_feature_names()  # 48个特征

# 获取必需特征
required_features = get_required_features()  # 7个必需特征

# 验证上下文特征
validation_result = validate_context_features(context_features)
```

### 配置结构
```python
ENHANCED_META_MODEL_INPUT_FEATURES_CONFIG = {
    'base_model_predictions': {...},    # 基础模型预测概率
    'traditional_context': {...},       # 传统上下文特征
    'higher_order_features': {...},     # 高阶特征
    'interaction_features': {...},      # 交互特征
    'smart_money_features': {...},      # 聪明钱特征
    'market_state_features': {...},     # 市场状态特征
    'momentum_features': {...}          # 动量特征
}
```

## 效果监控

### 丰富化统计
```python
# 获取丰富化统计信息
stats = enricher.get_enrichment_stats()

print(f"原始特征: {stats[target_name]['original_features']}")
print(f"丰富后特征: {stats[target_name]['enriched_features']}")
print(f"新增特征: {stats[target_name]['new_features']}")

# 按类别统计
categories = stats[target_name]['categories']
for category, count in categories.items():
    print(f"{category}: {count} 个")
```

### 特征质量检查
```python
# 检查特征值质量
nan_count = sum(1 for v in enriched_context.values() if pd.isna(v))
inf_count = sum(1 for v in enriched_context.values() if np.isinf(v))

print(f"数据质量: NaN={nan_count}, Inf={inf_count}")

# 特征值范围检查
for feature_name, value in enriched_context.items():
    if abs(value) > 1000:  # 检查异常大的值
        print(f"异常值: {feature_name} = {value}")
```

### 特征重要性分析
```python
# 在元模型训练后分析特征重要性
feature_importance = meta_model.feature_importances_
feature_names = list(enriched_context.keys())

importance_dict = dict(zip(feature_names, feature_importance))
sorted_importance = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

print("Top 10 重要特征:")
for feature, importance in sorted_importance[:10]:
    print(f"  {feature}: {importance:.4f}")
```

## 依赖关系

### 必需依赖
1. **高阶特征**: 需要启用高级特征工程
2. **交互特征**: 需要技术指标和成交量特征
3. **聪明钱特征**: 需要衍生品数据
4. **市场状态特征**: 需要ATR和ADX指标
5. **动量特征**: 需要基础技术指标

### 配置依赖链
```python
target_config = {
    # 基础依赖
    'enable_ta': True,                    # 技术指标
    'enable_volume': True,                # 成交量特征
    'enable_derivatives': True,           # 衍生品数据
    
    # 高级特征工程
    'enable_advanced_feature_engineering': True,
    'advanced_feature_engineering': {...},
    
    # 元模型输入丰富化
    'meta_model_input_enrichment': {...}
}
```

## 最佳实践

### 1. 渐进式启用
```python
# 第一步：启用高阶特征
phase1_config = {
    'include_higher_order_features': True,
    'include_interaction_features': False,
    'include_smart_money_features': False,
    'include_market_state_features': False,
    'include_momentum_features': False
}

# 第二步：添加市场状态特征
phase2_config = {
    'include_higher_order_features': True,
    'include_market_state_features': True,
    # 其他保持False
}

# 第三步：逐步添加其他特征类别
```

### 2. A/B测试对比
```python
# 对照组：传统上下文特征
control_config = {'enable_enrichment': False}

# 实验组：丰富化特征
experiment_config = {'enable_enrichment': True, ...}

# 比较元模型性能指标
```

### 3. 特征选择优化
```python
# 基于重要性阈值过滤
enrichment_config = {
    'feature_selection_threshold': 0.001,  # 移除重要性<0.001的特征
    'max_features_per_category': 10,       # 每类最多10个特征
}
```

## 故障排除

### 1. 特征提取失败
**问题**: 某些特征类别提取失败
**解决**:
- 检查依赖特征是否存在
- 验证数据质量和格式
- 确认配置参数正确

### 2. 特征数量过多
**问题**: 丰富化后特征过多，影响性能
**解决**:
- 调整`max_features_per_category`
- 提高`feature_selection_threshold`
- 选择性启用特征类别

### 3. 特征值异常
**问题**: 存在NaN或Inf值
**解决**:
- 检查原始数据质量
- 验证特征计算逻辑
- 启用特征标准化

### 4. 元模型性能下降
**问题**: 丰富化后元模型性能变差
**解决**:
- 检查特征相关性
- 调整特征选择策略
- 尝试不同的特征组合

## 总结

元模型输入丰富化系统提供了：

1. **信息维度扩展**: 从6个基础特征扩展到48+个高质量特征
2. **多层次洞察**: 技术面+资金面+市场状态的全方位分析
3. **智能特征组合**: 高阶导数+交互效应+背离分析
4. **灵活配置**: 支持不同策略和性能需求
5. **自动集成**: 无缝融入现有预测流程
6. **质量保证**: 完善的验证和监控机制

通过这个系统，元模型能够获得更丰富、更深层的市场信息，显著提升决策质量和预测准确性，真正实现"信息质量决定性能上限"的目标。
