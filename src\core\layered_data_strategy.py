"""
分层数据策略模块
实现用户建议的分层数据获取策略：
- 短期数据（7777条）用于模型核心训练
- 长期数据（22222条）用于上下文分析和样本权重计算
- MTFA特征独立获取更多历史数据
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, Any
import logging
from datetime import datetime, timedelta

import config
from src.core import data_utils

# 使用标准logging而不是enhanced_logger
import logging
logger = logging.getLogger(__name__)


class LayeredDataManager:
    """分层数据管理器"""
    
    def __init__(self, binance_client, target_config: Dict[str, Any]):
        self.binance_client = binance_client
        self.target_config = target_config
        self.symbol = target_config.get('symbol', 'BTCUSDT')
        self.interval = target_config.get('interval', '15m')
        
        # 数据限制配置
        self.short_term_limit = getattr(config, 'DATA_FETCH_LIMIT', 7777)
        self.long_term_limit = getattr(config, 'LONG_TERM_DATA_LIMIT', 22222)
        self.mtfa_limit = getattr(config, 'MTFA_DATA_LIMIT', 2000)
        
        logger.info(f"初始化分层数据管理器 - 短期:{self.short_term_limit}, 长期:{self.long_term_limit}, MTFA:{self.mtfa_limit}")
    
    def fetch_layered_data(self) -> Dict[str, pd.DataFrame]:
        """
        获取分层数据

        Returns:
            dict: 包含不同层次数据的字典
                - 'short_term': 短期训练数据
                - 'long_term': 长期上下文数据
                - 'mtfa_context': MTFA上下文数据
        """
        # 检查分层数据策略开关
        if not getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True):
            logger.info(f"分层数据策略已禁用，返回空结果")
            return {}

        logger.info(f"开始获取 {self.symbol}@{self.interval} 的分层数据...")
        
        layered_data = {}
        
        # 1. 获取长期数据用于宏观上下文分析
        logger.info(f"  步骤 1: 获取长期历史数据 ({self.long_term_limit}条) 用于宏观上下文分析...")
        try:
            df_long_term = data_utils.fetch_binance_history(
                self.binance_client, 
                self.symbol, 
                self.interval, 
                limit=self.long_term_limit
            )
            
            if df_long_term is not None and not df_long_term.empty:
                layered_data['long_term'] = df_long_term
                logger.info(f"  ✅ 长期数据获取成功: {len(df_long_term)} 条")
                logger.info(f"  📅 时间范围: {df_long_term.index[0]} 到 {df_long_term.index[-1]}")
            else:
                logger.warning("  ❌ 长期数据获取失败")
                
        except Exception as e:
            logger.error(f"  ❌ 长期数据获取异常: {e}")
        
        # 2. 获取短期数据用于模型核心训练
        logger.info(f"  步骤 2: 获取短期数据 ({self.short_term_limit}条) 用于模型训练...")
        try:
            df_short_term = data_utils.fetch_binance_history(
                self.binance_client, 
                self.symbol, 
                self.interval, 
                limit=self.short_term_limit
            )
            
            if df_short_term is not None and not df_short_term.empty:
                layered_data['short_term'] = df_short_term
                logger.info(f"  ✅ 短期数据获取成功: {len(df_short_term)} 条")
                logger.info(f"  📅 时间范围: {df_short_term.index[0]} 到 {df_short_term.index[-1]}")
            else:
                logger.warning("  ❌ 短期数据获取失败")
                
        except Exception as e:
            logger.error(f"  ❌ 短期数据获取异常: {e}")
        
        # 3. 为MTFA特征获取多时间框架数据
        if self.target_config.get('enable_mtfa', False):
            mtfa_timeframes = self.target_config.get('mtfa_timeframes', ['30m', '1h', '4h'])
            logger.info(f"  步骤 3: 获取MTFA多时间框架数据 ({len(mtfa_timeframes)}个时间框架) 用于高时间框架特征...")

            # 🎯 修复：计算基于主时间框架的合理数据条数
            base_interval_minutes = self._get_interval_minutes(self.interval)

            mtfa_data = {}
            for timeframe in mtfa_timeframes:
                if timeframe == self.interval:
                    continue  # 跳过主时间框架

                try:
                    # 计算该时间框架需要的数据条数（保持相同时间范围）
                    tf_minutes = self._get_interval_minutes(timeframe)
                    tf_limit = max(100, self.mtfa_limit * base_interval_minutes // tf_minutes)  # 最少100条

                    logger.info(f"    获取 {timeframe} 数据 ({tf_limit}条，覆盖相同时间范围)...")
                    df_tf = data_utils.fetch_binance_history(
                        self.binance_client,
                        self.symbol,
                        timeframe,  # 🎯 修复：使用正确的时间框架
                        limit=tf_limit  # 🎯 修复：使用计算出的合理条数
                    )

                    if df_tf is not None and not df_tf.empty:
                        mtfa_data[timeframe] = df_tf
                        logger.info(f"    ✅ {timeframe} 数据获取成功: {len(df_tf)} 条")
                    else:
                        logger.warning(f"    ❌ {timeframe} 数据获取失败")

                except Exception as e:
                    logger.error(f"    ❌ {timeframe} 数据获取异常: {e}")

            if mtfa_data:
                layered_data['mtfa_context'] = mtfa_data
                logger.info(f"  ✅ MTFA多时间框架数据获取完成: {len(mtfa_data)} 个时间框架")
            else:
                logger.warning("  ❌ 所有MTFA时间框架数据获取失败")
        
        logger.info(f"分层数据获取完成，共获取 {len(layered_data)} 个数据层")
        return layered_data

    def _get_interval_minutes(self, interval):
        """
        将时间间隔字符串转换为分钟数

        Args:
            interval: 时间间隔字符串（如 '15m', '1h', '4h'）

        Returns:
            int: 对应的分钟数
        """
        interval = interval.lower()

        if interval.endswith('m'):
            return int(interval[:-1])
        elif interval.endswith('h'):
            return int(interval[:-1]) * 60
        elif interval.endswith('d'):
            return int(interval[:-1]) * 24 * 60
        else:
            # 默认假设是分钟
            try:
                return int(interval)
            except:
                logger.warning(f"无法解析时间间隔: {interval}，使用默认值15分钟")
                return 15

    def calculate_enhanced_sample_weights(self,
                                        short_term_data: pd.DataFrame,
                                        long_term_data: Optional[pd.DataFrame] = None) -> Optional[np.ndarray]:
        """
        使用长期数据计算增强的样本权重

        Args:
            short_term_data: 短期训练数据
            long_term_data: 长期上下文数据

        Returns:
            样本权重数组，如果计算失败则返回None
        """
        # 检查分层数据策略开关
        if not getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True):
            logger.info("分层数据策略已禁用，跳过增强样本权重计算")
            return None

        if not self.target_config.get('enable_dynamic_sample_weighting', False):
            return None

        logger.info("使用长期数据计算增强样本权重...")
        
        try:
            # 如果有长期数据，使用长期数据计算权重上下文
            context_data = long_term_data if long_term_data is not None else short_term_data
            
            # 计算市场状态和波动率上下文
            market_context = self._calculate_market_context(context_data)
            
            # 基于上下文为短期数据计算权重
            sample_weights = self._calculate_contextual_weights(short_term_data, market_context)
            
            logger.info(f"样本权重计算完成，范围: [{sample_weights.min():.4f}, {sample_weights.max():.4f}]")
            return sample_weights
            
        except Exception as e:
            logger.error(f"样本权重计算失败: {e}")
            return None
    
    def _calculate_market_context(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算市场上下文信息"""
        try:
            # 计算长期波动率分位数
            returns = data['close'].pct_change().dropna()
            volatility = returns.rolling(window=20).std()
            
            # 计算趋势强度
            price_ma_short = data['close'].rolling(window=20).mean()
            price_ma_long = data['close'].rolling(window=50).mean()
            trend_strength = (price_ma_short - price_ma_long) / price_ma_long
            
            # 计算成交量相对强度
            volume_ma = data['volume'].rolling(window=20).mean()
            volume_strength = data['volume'] / volume_ma
            
            return {
                'volatility_percentiles': volatility.quantile([0.25, 0.5, 0.75]).to_dict(),
                'trend_strength_std': trend_strength.std(),
                'volume_strength_mean': volume_strength.mean(),
                'price_range': (data['close'].min(), data['close'].max())
            }
            
        except Exception as e:
            logger.warning(f"市场上下文计算失败: {e}")
            return {}
    
    def _calculate_contextual_weights(self, 
                                    short_data: pd.DataFrame, 
                                    market_context: Dict[str, Any]) -> np.ndarray:
        """基于市场上下文计算样本权重"""
        try:
            weights = np.ones(len(short_data))
            
            if not market_context:
                return weights
            
            # 时间衰减权重
            time_decay = np.exp(-0.1 * np.arange(len(short_data))[::-1])
            weights *= time_decay
            
            # 基于波动率的权重调整
            if 'volatility_percentiles' in market_context:
                returns = short_data['close'].pct_change().dropna()
                if len(returns) > 0:
                    volatility = returns.rolling(window=min(20, len(returns))).std()
                    vol_weights = np.where(volatility > market_context['volatility_percentiles'].get(0.75, 0), 1.2, 1.0)
                    if len(vol_weights) == len(weights):
                        weights *= vol_weights
            
            # 归一化权重
            weights = weights / weights.mean()
            
            return weights
            
        except Exception as e:
            logger.warning(f"上下文权重计算失败: {e}")
            return np.ones(len(short_data))


def get_layered_data_manager(binance_client, target_config: Dict[str, Any]) -> LayeredDataManager:
    """获取分层数据管理器实例"""
    return LayeredDataManager(binance_client, target_config)


def fetch_enhanced_mtfa_data(binance_client,
                           symbol: str,
                           timeframes: list,
                           main_timeframe: str,
                           limit: Optional[int] = None) -> Dict[str, pd.DataFrame]:
    """
    增强的MTFA数据获取，使用独立的数据限制

    Args:
        binance_client: Binance客户端
        symbol: 交易对
        timeframes: 目标时间框架列表
        main_timeframe: 主时间框架
        limit: 数据限制，如果为None则使用配置中的MTFA_DATA_LIMIT

    Returns:
        各时间框架的数据字典
    """
    # 检查分层数据策略开关
    if not getattr(config, 'ENABLE_LAYERED_DATA_STRATEGY', True):
        logger.info("分层数据策略已禁用，使用传统MTFA数据获取")
        # 回退到传统限制
        limit = 1000 if limit is None else min(limit, 1000)
    else:
        if limit is None:
            limit = getattr(config, 'MTFA_DATA_LIMIT', 2000)

    logger.info(f"开始获取增强MTFA数据，限制: {limit} 条")
    
    mtfa_data = {}
    
    for timeframe in timeframes:
        if timeframe == main_timeframe:
            continue
            
        try:
            logger.info(f"  获取 {timeframe} 数据...")
            tf_data = data_utils.fetch_binance_history(
                binance_client, symbol, timeframe, limit=limit
            )
            
            if tf_data is not None and not tf_data.empty:
                mtfa_data[timeframe] = tf_data
                logger.info(f"  ✅ {timeframe} 数据获取成功: {len(tf_data)} 条")
            else:
                logger.warning(f"  ❌ {timeframe} 数据获取失败")
                
        except Exception as e:
            logger.error(f"  ❌ {timeframe} 数据获取异常: {e}")
    
    return mtfa_data
