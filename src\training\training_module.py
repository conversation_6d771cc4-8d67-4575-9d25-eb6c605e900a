"""
训练模块 - 负责单个目标的训练流程
"""
import os
import json
import traceback
from datetime import datetime, timezone
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, brier_score_loss, classification_report
import joblib

from src.core import data_utils
from src.core import prediction
from src.utils.purged_cross_validation import PurgedTimeSeriesSplit, calculate_optimal_purge_length
from src.core.error_handler import handle_training_exception, get_enhanced_logger
from lightgbm import LGBMClassifier
import lightgbm as lgb


def train_single_model(X_train, y_train, target_config):
    """
    训练单个模型

    Args:
        X_train: 训练特征
        y_train: 训练目标
        target_config: 目标配置

    Returns:
        训练好的模型
    """
    try:
        model_type = target_config.get('model_type', 'lightgbm')

        if model_type.lower() == 'lightgbm':
            # 获取LightGBM参数
            lgbm_params = target_config.get('lightgbm_params', {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            })

            # 创建并训练模型
            model = LGBMClassifier(**lgbm_params)
            model.fit(X_train, y_train)

            return model
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

    except Exception as e:
        print(f"训练模型时出错: {e}")
        return None


@handle_training_exception(
    function_name="get_unscaled_features_and_target",
    fallback_result=(None, None, None),
    include_traceback=True
)
def get_unscaled_features_and_target(df_hist_data, target_config, binance_client, target_name):
    """
    获取未缩放的特征和目标变量

    Args:
        df_hist_data: 历史数据DataFrame
        target_config: 目标配置
        binance_client: Binance客户端
        target_name: 目标名称

    Returns:
        tuple: (X_unscaled_df, y_series, all_feature_names)
    """
    # 获取增强日志记录器
    enhanced_logger = get_enhanced_logger(__name__)

    # 1. 生成特征 - 根据模型类型选择特征生成函数
    model_type = target_config.get('model_type', 'LGBM')

    if model_type == 'LSTM':
        enhanced_logger.info(
            f"为 {target_name} 生成LSTM专用精简特征",
            target_name=target_name,
            input_data_shape=df_hist_data.shape,
            model_type=model_type
        )
        df_with_features = data_utils.add_lstm_features(df_hist_data, target_config)
    else:
        enhanced_logger.info(
            f"为 {target_name} 生成技术指标特征",
            target_name=target_name,
            input_data_shape=df_hist_data.shape,
            model_type=model_type
        )
        df_with_features = data_utils.add_classification_features(df_hist_data, target_config)

    if df_with_features is None or df_with_features.empty:
        raise ValueError(f"无法为 {target_name} 生成特征")

    # 2. 创建目标变量
    enhanced_logger.info(
        f"为 {target_name} 创建目标变量",
        target_name=target_name,
        features_shape=df_with_features.shape
    )
    df_with_target, target_col_name = data_utils.create_target_variable(
        df_with_features, target_config, binance_client
    )

    if df_with_target is None or target_col_name is None:
        raise ValueError(f"无法为 {target_name} 创建目标变量")

    # 3. 分离特征和目标
    feature_columns = [col for col in df_with_target.columns if col != target_col_name]
    X_unscaled_df = df_with_target[feature_columns].copy()
    y_series = df_with_target[target_col_name].copy()

    # 4. 清理数据
    enhanced_logger.info(
        f"清理 {target_name} 的数据",
        target_name=target_name,
        before_cleaning_shape=X_unscaled_df.shape
    )
    # 删除包含NaN的行
    valid_mask = ~(X_unscaled_df.isnull().any(axis=1) | y_series.isnull())
    X_unscaled_df = X_unscaled_df[valid_mask]
    y_series = y_series[valid_mask]

    enhanced_logger.info(
        f"{target_name} 数据准备完成",
        target_name=target_name,
        final_X_shape=X_unscaled_df.shape,
        final_y_shape=y_series.shape,
        feature_count=len(feature_columns)
    )

    return X_unscaled_df, y_series, feature_columns


@handle_training_exception(
    function_name="train_target",
    fallback_result={
        'success': False,
        'error': 'Training failed due to exception',
        'gui_metrics': {'status': '训练失败', 'accuracy': 'N/A', 'error_message': 'Exception occurred'},
        'console_summary': {'name': 'Unknown', 'status': '训练失败', 'error_message': 'Exception occurred'}
    },
    include_traceback=True
)
def train_target(target_name, target_config, X_unscaled_df, y_series, all_feature_names,
                model_dir, binance_client, app_state, gui, _main_root):
    """
    训练单个目标

    Args:
        target_name: 目标名称
        target_config: 目标配置
        X_unscaled_df: 未缩放的特征DataFrame
        y_series: 目标变量Series
        all_feature_names: 所有特征名称列表
        model_dir: 模型保存目录
        binance_client: Binance客户端
        app_state: 应用状态
        gui: GUI对象
        _main_root: 主窗口对象

    Returns:
        dict: 训练结果
    """
    # 获取增强日志记录器
    enhanced_logger = get_enhanced_logger(__name__)

    enhanced_logger.info(
        f"开始训练 {target_name}",
        target_name=target_name,
        input_X_shape=X_unscaled_df.shape,
        input_y_shape=y_series.shape,
        feature_count=len(all_feature_names)
    )

    # 1. 选择交叉验证方法
    enhanced_logger.info(
        f"设置 {target_name} 的交叉验证方法",
        target_name=target_name,
        preprocessing_step="cv_setup"
    )

    n_splits = target_config.get('cv_folds', 3)
    use_purged_cv = target_config.get('purged_cv_enable', False)

    if use_purged_cv:
        # 使用Purged K-Fold交叉验证
        print(f"    使用 Purged K-Fold 交叉验证训练 {target_name}...")

        # 计算purge长度
        if target_config.get('purged_cv_auto_purge_calculation', True):
            prediction_periods = target_config.get('prediction_periods', [1])
            period = prediction_periods[0] if isinstance(prediction_periods, list) and prediction_periods else 1
            purge_length = calculate_optimal_purge_length(
                target_forward_period=period,
                max_feature_window=target_config.get('max_feature_window', 20),
                safety_factor=1.5
            )
            print(f"      自动计算的purge长度: {purge_length}")
        else:
            purge_length = target_config.get('purged_cv_purge_length', 10)
            print(f"      配置的purge长度: {purge_length}")

        embargo_length = target_config.get('purged_cv_embargo_length', 0)
        min_train_size = target_config.get('purged_cv_min_train_size', None)

        tscv = PurgedTimeSeriesSplit(
            n_splits=n_splits,
            purge_length=purge_length,
            embargo_length=embargo_length,
            min_train_size=min_train_size
        )
        cv_method = "Purged K-Fold"
    else:
        # 使用传统的TimeSeriesSplit
        print(f"    使用传统时间序列交叉验证训练 {target_name}...")
        tscv = TimeSeriesSplit(n_splits=n_splits)
        cv_method = "TimeSeriesSplit"

    # 2. 交叉验证训练（修复数据泄露）
    print(f"    使用 {n_splits} 折 {cv_method} 交叉验证训练 {target_name}（无数据泄露）...")

    fold_results = []
    models = []
    fold_scalers = []  # 存储每个fold的Scaler

    for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(X_unscaled_df)):
        print(f"      训练第 {fold_idx + 1}/{n_splits} 折...")

        # 分割原始数据
        X_train_fold_raw = X_unscaled_df.iloc[train_idx]
        X_val_fold_raw = X_unscaled_df.iloc[val_idx]
        y_train_fold = y_series.iloc[train_idx]
        y_val_fold = y_series.iloc[val_idx]

        # 为当前fold单独拟合Scaler（避免数据泄露）
        fold_scaler = StandardScaler()
        X_train_fold_scaled = fold_scaler.fit_transform(X_train_fold_raw)
        X_val_fold_scaled = fold_scaler.transform(X_val_fold_raw)  # 只transform，不fit

        # 转换为DataFrame
        X_train_fold = pd.DataFrame(X_train_fold_scaled, columns=X_unscaled_df.columns, index=X_train_fold_raw.index)
        X_val_fold = pd.DataFrame(X_val_fold_scaled, columns=X_unscaled_df.columns, index=X_val_fold_raw.index)

        # 保存fold Scaler
        fold_scalers.append(fold_scaler)

        # 训练模型
        model = train_single_model(
            X_train_fold, y_train_fold, target_config
        )
            
            if model is None:
                print(f"        第 {fold_idx + 1} 折训练失败")
                continue
            
            models.append(model)
            
            # 评估模型
            y_train_pred = model.predict_proba(X_train_fold)[:, 1]
            y_val_pred = model.predict_proba(X_val_fold)[:, 1]
            
            train_acc = accuracy_score(y_train_fold, (y_train_pred > 0.5).astype(int))
            val_acc = accuracy_score(y_val_fold, (y_val_pred > 0.5).astype(int))
            train_brier = brier_score_loss(y_train_fold, y_train_pred)
            val_brier = brier_score_loss(y_val_fold, y_val_pred)
            
            fold_result = {
                'fold': fold_idx + 1,
                'train_accuracy': train_acc,
                'val_accuracy': val_acc,
                'train_brier': train_brier,
                'val_brier': val_brier,
                'model_type': type(model).__name__
            }
            fold_results.append(fold_result)
            
            print(f"        第 {fold_idx + 1} 折结果: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}")
        
        if not models:
            raise ValueError(f"所有折的训练都失败了")
        
        # 4. 训练最终模型（使用全局Scaler）
        print(f"    训练 {target_name} 的最终模型...")

        # 为最终模型创建全局Scaler
        final_scaler = StandardScaler()
        X_scaled_final = final_scaler.fit_transform(X_unscaled_df)
        X_scaled_final_df = pd.DataFrame(X_scaled_final, columns=X_unscaled_df.columns, index=X_unscaled_df.index)

        final_model = train_single_model(X_scaled_final_df, y_series, target_config)
        if final_model is None:
            raise ValueError(f"最终模型训练失败: {target_name}")

        # 5. 保存模型和相关文件
        print(f"    保存 {target_name} 的模型文件...")

        # 保存最终Scaler（用于实时预测）
        scaler_filename = f"scaler_{target_name}.joblib"
        scaler_path = os.path.join(model_dir, scaler_filename)
        joblib.dump(final_scaler, scaler_path)

        # 保存特征列表
        features_filename = f"features_{target_name}.json"
        features_path = os.path.join(model_dir, features_filename)
        with open(features_path, 'w') as f:
            json.dump(all_feature_names, f, indent=2)

        # 保存最终模型
        final_model_filename = f"model_{target_name}.joblib"
        final_model_path = os.path.join(model_dir, final_model_filename)
        joblib.dump(final_model, final_model_path)

        # 保存fold模型和对应的Scaler（用于OOF预测）
        model_artifacts = []
        for i, (model, fold_scaler) in enumerate(zip(models, fold_scalers)):
            # 保存fold模型
            model_filename = f"model_{target_name}_fold_{i+1}.joblib"
            model_path = os.path.join(model_dir, model_filename)
            joblib.dump(model, model_path)

            # 保存fold Scaler
            fold_scaler_filename = f"scaler_{target_name}_fold_{i+1}.joblib"
            fold_scaler_path = os.path.join(model_dir, fold_scaler_filename)
            joblib.dump(fold_scaler, fold_scaler_path)

            model_artifacts.append({
                'fold': i + 1,
                'filename': model_filename,
                'scaler_filename': fold_scaler_filename,
                'model_type': type(model).__name__
            })

            print(f"      保存第 {i + 1} 折模型和Scaler: {model_filename}, {fold_scaler_filename}")
        
        # 6. 计算平均性能
        avg_train_acc = np.mean([r['train_accuracy'] for r in fold_results])
        avg_val_acc = np.mean([r['val_accuracy'] for r in fold_results])
        avg_train_brier = np.mean([r['train_brier'] for r in fold_results])
        avg_val_brier = np.mean([r['val_brier'] for r in fold_results])
        
        # 7. 准备返回结果
        gui_metrics = {
            'status': '训练成功',
            'accuracy': f"{avg_val_acc:.4f}",
            'train_accuracy': avg_train_acc,
            'val_accuracy': avg_val_acc,
            'train_brier': avg_train_brier,
            'val_brier': avg_val_brier,
            'n_folds': len(fold_results),
            'model_type': fold_results[0]['model_type'] if fold_results else 'Unknown'
        }
        
        console_summary = {
            'name': target_name,
            'status': '训练成功',
            'model_type': fold_results[0]['model_type'] if fold_results else 'Unknown',
            'num_folds_or_runs': len(fold_results),
            'train_accuracy': avg_train_acc,
            'val_accuracy': avg_val_acc,
            'train_brier': avg_train_brier,
            'val_brier': avg_val_brier,
            'fold_results': fold_results
        }
        
        # 7. 保存元数据
        metadata = {
            'target_name': target_name,
            'timestamp_utc': datetime.now(timezone.utc).isoformat(),
            'scaler_filename': scaler_filename,
            'features_filename': features_filename,
            'model_artifacts': model_artifacts,
            'training_summary': gui_metrics,
            'fold_evaluations': fold_results,
            'data_shape': X_unscaled_df.shape,
            'target_length': len(y_series),
            'cross_validation': {
                'method': cv_method,
                'n_splits': n_splits,
                'purged_cv_enabled': use_purged_cv,
                'purge_length': purge_length if use_purged_cv else None,
                'embargo_length': embargo_length if use_purged_cv else None
            }
        }
        
        metadata_path = os.path.join(model_dir, f"metadata_{target_name}.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
    enhanced_logger.info(
        f"{target_name} 训练完成",
        target_name=target_name,
        avg_validation_accuracy=avg_val_acc,
        training_status="success"
    )

    return {
        'success': True,
        'gui_metrics': gui_metrics,
        'console_summary': console_summary,
        'data_shape': X_unscaled_df.shape,
        'target_length': len(y_series)
    }
