# 脚本工具目录

这个目录包含了各种有用的维护和优化工具脚本。

## 📁 目录结构

```
scripts/
├── lstm_tools/              # LSTM相关工具
│   └── generate_lstm_oof_cache.py
├── optimization/            # 优化分析工具
│   └── optimize_based_on_training_report.py
└── README.md               # 本文件
```

## 🛠️ 工具说明

### LSTM工具 (`lstm_tools/`)

#### `generate_lstm_oof_cache.py`
**功能**: 生成LSTM模型的真实OOF预测缓存

**使用场景**:
- LSTM模型重新训练后需要生成新的OOF缓存
- 现有OOF缓存损坏或过期时重新生成
- 从测试缓存升级到真实缓存

**效果**:
- 元模型训练时间从20-30分钟缩短到2-5分钟
- 一次生成，长期受益
- 保留LSTM模型的价值贡献

**使用方法**:
```bash
cd scripts/lstm_tools
python generate_lstm_oof_cache.py
```

### 优化工具 (`optimization/`)

#### `optimize_based_on_training_report.py`
**功能**: 基于训练报告自动分析和生成优化建议

**使用场景**:
- 每次模型训练后的自动化分析
- 性能问题的快速诊断
- 参数优化建议的生成
- 训练结果的系统化评估

**功能特性**:
- 自动解析训练日志和报告
- 识别性能瓶颈和问题
- 生成具体的优化建议
- 自动调整配置参数

**使用方法**:
```bash
cd scripts/optimization
python optimize_based_on_training_report.py
```

**输出文件**:
- `latest_optimization_report.json` - 详细的优化分析报告

## 📋 使用建议

### 推荐工作流程

1. **训练模型后立即分析**:
   ```bash
   python scripts/optimization/optimize_based_on_training_report.py
   ```

2. **如果LSTM训练缓慢**:
   ```bash
   python scripts/lstm_tools/generate_lstm_oof_cache.py
   ```

3. **查看优化报告**:
   ```bash
   # 查看生成的JSON报告
   cat scripts/optimization/latest_optimization_report.json
   ```

### 维护建议

- **定期运行**: 每次重要的模型训练后运行优化分析
- **备份缓存**: LSTM OOF缓存文件很重要，建议定期备份
- **监控效果**: 应用优化建议后，对比前后的训练效果

## ⚠️ 注意事项

1. **路径依赖**: 这些脚本需要从项目根目录或脚本目录运行
2. **依赖检查**: 确保所有必要的Python包都已安装
3. **权限问题**: 确保脚本有读写相关文件的权限
4. **备份重要**: 在应用配置更改前，建议备份原始配置

## 🔧 故障排除

### 常见问题

1. **导入错误**: 确保Python路径设置正确
2. **文件不存在**: 检查相关的模型文件和日志文件是否存在
3. **权限问题**: 确保有足够的文件系统权限

### 获取帮助

如果遇到问题，可以：
1. 检查脚本输出的错误信息
2. 查看相关的日志文件
3. 确认配置文件的正确性
