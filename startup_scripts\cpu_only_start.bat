@echo off
REM CPU专用启动脚本 - 禁用所有GPU使用

echo 🚫 启动CPU专用模式...

REM 禁用GPU
set CUDA_VISIBLE_DEVICES=-1
set TF_FORCE_GPU_ALLOW_GROWTH=false

REM CPU优化环境变量
set OMP_NUM_THREADS=16
set MKL_NUM_THREADS=16
set NUMBA_NUM_THREADS=16
set OPENBLAS_NUM_THREADS=16

REM TensorFlow CPU优化
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=1
set TF_NUM_INTEROP_THREADS=4
set TF_NUM_INTRAOP_THREADS=16

echo ✅ CPU专用环境设置完成
echo 🖥️  AMD 16核处理器优化已启用
echo 🚫 GPU使用已完全禁用

REM 启动Python程序
python %*
