# backtest_config.py
"""
二元期权回测配置文件
配置回测的各种参数，包括数据源、交易规则、风险管理等

本配置文件包含以下主要部分：
1. 基础数据配置 - 交易对、时间周期、回测范围等
2. 策略配置 - 信号阈值、资金管理、风险控制等
3. 模型配置 - 预测模型路径和文件名设置
4. 特征工程配置 - 与主程序保持一致的特征参数
5. 输出配置 - 报告生成、图表设置等
6. 高级配置 - 性能优化、调试选项等

使用说明：
- 修改配置后需要重新运行回测程序
- 建议先在小时间范围内测试配置的有效性
- 关键参数如信号阈值、资金管理参数需要谨慎调整
"""

import os
from datetime import datetime

# =============================================================================
# 数据配置 - 控制回测使用的数据源和时间范围
# =============================================================================

# 交易对配置
SYMBOL = "BTCUSDT"                    # 交易对符号，支持币安所有现货交易对
                                      # 常用选项: "BTCUSDT", "ETHUSDT", "BNBUSDT"
                                      # 注意：不同交易对的波动性和流动性差异较大

INTERVAL = "15m"                      # K线时间周期，用于获取历史价格数据
                                      # 支持: "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d"
                                      # 推荐: "15m" 或 "30m" 用于二元期权交易
                                      # 注意：更小的周期会增加数据量和计算时间

PREDICTION_PERIODS = 2                # 预测周期数，决定二元期权的到期时间
                                      # 计算公式：到期时间 = PREDICTION_PERIODS × INTERVAL
                                      # 例如：2 × 15分钟 = 30分钟到期
                                      # 推荐范围：1-4个周期，过长会降低预测准确性

EXPIRY_MINUTES = 30                   # 二元期权到期时间（分钟）
                                      # 应该等于 PREDICTION_PERIODS × INTERVAL的分钟数
                                      # 常用选项：15分钟、30分钟、60分钟
                                      # 注意：到期时间影响盈利概率和风险

# 二元期权交易规则配置
PAYOUT_RATIO = 0.85                   # 盈利比率，胜利时的收益百分比
                                      # 范围：0.7-0.9（70%-90%）
                                      # 例如：0.85表示投入100元，胜利时获得185元（净利润85元）
                                      # 注意：不同平台的盈利比率可能不同

MIN_BET_AMOUNT = 5.0                  # 最小下注金额（美元）
                                      # 用于限制单笔交易的最小规模
                                      # 建议设置为初始资金的1-5%

MAX_BET_AMOUNT = 250.0                # 最大下注金额（美元）
                                      # 用于控制单笔交易的最大风险
                                      # 建议不超过初始资金的20-30%

INITIAL_BALANCE = 100.0               # 初始资金（美元）
                                      # 回测开始时的账户余额
                                      # 建议根据实际交易资金设置

# 回测时间范围配置
BACKTEST_START_DATE = "2025-05-25"    # 回测开始日期，格式：YYYY-MM-DD
                                      # 建议选择有足够历史数据的日期
                                      # 注意：开始日期前需要有足够的数据用于特征计算

BACKTEST_END_DATE = "2025-05-26"      # 回测结束日期，格式：YYYY-MM-DD
                                      # 建议先用较短时间范围测试配置
                                      # 注意：时间范围过长会增加计算时间

DATA_FETCH_LIMIT = 10000              # 获取历史数据的最大K线数量
                                      # 用于控制内存使用和提高性能
                                      # 建议：短期回测5000-10000，长期回测20000+
                                      # 注意：数据量不足可能影响特征计算质量

# =============================================================================
# 策略配置 - 控制交易信号生成和资金管理策略
# =============================================================================

# 信号阈值配置 - 决定何时产生交易信号
SIGNAL_THRESHOLD_UP = 0.2700            # 看涨信号概率阈值（0.0-1.0）
                                       # UP模型集成预测概率超过此值时产生买涨信号
                                       # 已设置为与config.py中ensemble_prediction_threshold一致
                                       # 注意：此阈值专门针对UP模型的集成预测优化

SIGNAL_THRESHOLD_DOWN = 0.5500          # 看跌信号概率阈值（0.0-1.0）
                                       # DOWN模型集成预测概率超过此值时产生买跌信号
                                       # 已设置为与config.py中ensemble_prediction_threshold一致
                                       # 注意：此阈值专门针对DOWN模型的集成预测优化

NEUTRAL_ZONE_THRESHOLD = 0.33          # 中性区间阈值（0.0-0.5）
                                       # 当上涨和下跌概率都低于此值时保持观望
                                       # 用于过滤掉不确定的市场状态
                                       # 推荐：0.3-0.4，避免在震荡市中频繁交易

# 资金管理策略配置 - 控制每笔交易的下注金额
POSITION_SIZING_METHOD = "kelly"       # 仓位管理方法，支持以下选项：
                                       # "fixed" - 固定金额下注，使用FIXED_BET_AMOUNT
                                       # "kelly" - 凯利公式动态调整，根据胜率和赔率计算
                                       # "percentage" - 固定百分比风险，使用PERCENTAGE_RISK
                                       # 推荐："kelly"用于长期稳定，"fixed"用于测试

FIXED_BET_AMOUNT = 5.0                # 固定下注金额（美元）
                                       # 仅在POSITION_SIZING_METHOD="fixed"时使用
                                       # 建议设置为初始资金的2-10%
                                       # 优点：简单稳定，缺点：无法根据表现调整

KELLY_MULTIPLIER = 0.25                # 凯利公式保守系数（0.0-1.0）
                                       # 用于降低凯利公式的激进程度
                                       # 0.25表示使用25%的凯利建议仓位
                                       # 推荐：0.1-0.5，过高会增加风险

PERCENTAGE_RISK = 0.02                 # 每次交易风险百分比（0.0-0.1）
                                       # 仅在POSITION_SIZING_METHOD="percentage"时使用
                                       # 表示每次交易风险占当前资金的百分比
                                       # 推荐：0.01-0.05（1%-5%）

# 风险控制配置 - 保护资金安全的重要设置
MAX_DAILY_LOSS = 200.0                 # 每日最大亏损限制（美元）
                                       # 当日亏损达到此值时停止交易
                                       # 建议设置为初始资金的20-50%
                                       # 注意：设置过低可能错过反弹机会

MAX_CONSECUTIVE_LOSSES = 5             # 最大连续亏损次数（整数）
                                       # 连续亏损达到此次数时暂停交易
                                       # 推荐：3-8次，用于避免连续不利的市场条件
                                       # 可以配合冷却期使用

STOP_LOSS_ENABLED = True               # 是否启用止损功能（True/False）
                                       # 启用后会根据上述风险控制参数执行止损
                                       # 建议：True，特别是在自动交易中

DAILY_RESET_ENABLED = True             # 是否启用每日重置功能（True/False）
                                       # 每日开始时重置风险控制计数器
                                       # 建议：True，避免前一日的亏损影响新一日的交易

# =============================================================================
# 模型配置 - 指定预测模型的路径和文件名
# =============================================================================

# 预测模型目录路径
UP_MODEL_DIR = "trained_models_btc_15m_up"      # 看涨模型目录
                                                # 存放训练好的上涨预测模型文件
                                                # 包括模型文件、缩放器、特征列表等

DOWN_MODEL_DIR = "trained_models_btc_15m_down"  # 看跌模型目录
                                                # 存放训练好的下跌预测模型文件
                                                # 结构与UP模型目录相同

META_MODEL_DIR = "models/meta_model_data"       # 元模型目录
                                                # 存放元模型相关文件
                                                # 元模型用于综合基础模型的预测结果

# 模型文件名模板（使用实际的文件名格式）
MODEL_FILENAME = "model_BTC_15m_{}_30m_fold4_calibrated.joblib"  # 模型文件名模板
                                                                 # {}会被替换为"UP"或"DOWN"
                                                                 # fold4表示使用第4折的模型
                                                                 # calibrated表示使用校准后的模型

SCALER_FILENAME = "scaler_BTC_15m_{}_30m.joblib"       # 特征缩放器文件名模板
                                                       # 用于标准化输入特征
                                                       # 必须与训练时使用的缩放器一致

FEATURES_FILENAME = "final_selected_features_BTC_15m_{}_30m.json"  # 特征列表文件名模板
                                                                   # 包含模型使用的特征名称列表
                                                                   # 确保特征顺序与训练时一致

# =============================================================================
# 元模型配置 - 控制元模型的使用和行为
# =============================================================================

# 元模型开关配置
USE_META_MODEL = True                # 是否启用元模型功能（True/False）
                                     # 元模型可以综合多个基础模型的预测结果
                                     # 通常能提供更稳定和准确的预测

SIGNAL_MODE = "meta_model"           # 信号生成模式，支持以下选项：
                                     # "base_model" - 仅使用基础模型（UP/DOWN）
                                     # "meta_model" - 仅使用元模型
                                     # "both" - 同时使用基础模型和元模型
                                     # 推荐："meta_model"用于生产，"base_model"用于调试

# 元模型文件配置
META_MODEL_FILENAME = "meta_model_lgbm.joblib"         # 元模型文件名
                                                        # 通常使用LightGBM训练的元模型
                                                        # 文件应位于META_MODEL_DIR目录中

META_FEATURES_FILENAME = "meta_model_features.json"    # 元模型特征列表文件名
                                                        # 包含元模型使用的特征名称
                                                        # 通常包括基础模型的预测概率等

# =============================================================================
# 预测间隔配置 - 控制预测触发的时间间隔
# =============================================================================

# 预测间隔设置
PREDICTION_INTERVALS = ["30m"]        # 预测间隔列表，支持以下选项：
                                      # ["1m"] - 每分钟触发预测（高频交易）
                                      # ["15m"] - 每15分钟触发预测（推荐）
                                      # ["1m", "15m"] - 多时间框架预测
                                      # ["custom"] - 使用自定义间隔
                                      # 注意：间隔越短，计算量越大

CUSTOM_INTERVAL = 5                   # 自定义间隔分钟数（1-60分钟）
                                      # 仅在PREDICTION_INTERVALS包含"custom"时使用
                                      # 例如：5表示每5分钟触发一次预测
                                      # 建议：根据交易策略的时间敏感性设置

# =============================================================================
# 真实交易模式配置 - 模拟真实交易环境的特殊设置
# =============================================================================

# 真实交易模式开关
REALISTIC_MODE = True                # 是否启用真实交易情况回测（True/False）
                                     # True：模拟真实交易环境，包括延迟、滑点等
                                     # False：理想化回测，忽略实际交易中的限制
                                     # 推荐：True，获得更准确的回测结果

# 凯利公式配置（真实交易模式专用）
KELLY_MIN_AMOUNT = 5.0                # 凯利公式计算的最小交易金额（美元）
                                      # 即使凯利公式建议更小的金额，也不会低于此值
                                      # 用于避免过小的交易金额

KELLY_MAX_AMOUNT = 250.0              # 凯利公式计算的最大交易金额（美元）
                                      # 即使凯利公式建议更大的金额，也不会超过此值
                                      # 用于控制单笔交易的最大风险

KELLY_MAX_PERCENTAGE = 0.10           # 凯利公式最大资金占比（0.0-1.0）
                                      # 单笔交易金额不超过当前资金的此百分比
                                      # 例如：0.10表示最多使用10%的资金进行单笔交易
                                      # 推荐：0.05-0.15（5%-15%）

# =============================================================================
# 特征工程配置 - 与主程序config.py保持完全一致，确保回测使用相同的特征
# =============================================================================

# 价格变动特征配置
ENABLE_PRICE_CHANGE = True            # 是否启用价格变动特征（True/False）
                                      # 计算不同周期的价格变化率
                                      # 这是最基础和重要的技术指标

PRICE_CHANGE_PERIODS = [1, 2, 3, 5, 10]  # 价格变动计算周期列表
                                          # 分别计算1、2、3、5、10个K线周期的价格变化
                                          # 用于捕捉不同时间尺度的价格动量
                                          # 注意：周期过多会增加特征维度

# 成交量特征配置
ENABLE_VOLUME = True                  # 是否启用成交量特征（True/False）
                                      # 成交量是重要的市场情绪指标
                                      # 配合价格分析可以提高预测准确性

VOLUME_AVG_PERIOD = 9                 # 成交量移动平均周期（整数）
                                      # 用于计算成交量相对于历史平均的比率
                                      # 推荐：5-20个周期

# K线形态特征配置
ENABLE_CANDLE = True                  # 是否启用K线形态特征（True/False）
                                      # 包括开盘价、收盘价、最高价、最低价的关系
                                      # 反映市场的买卖力量对比

ENABLE_PATTERN_RECOGNITION = True     # 是否启用K线模式识别（True/False）
                                      # 识别经典的K线形态如十字星、锤子线等
                                      # 这些形态通常预示着价格反转或延续

PATTERN_RECOGNITION_THRESHOLDS = {    # K线模式识别的阈值参数字典
    "doji_threshold": 0.1,            # 十字星识别阈值（实体相对于影线的比例）
    "hammer_body_ratio": 0.3,         # 锤子线实体比例阈值
    "hammer_shadow_ratio": 2.0,       # 锤子线影线比例阈值
    "shooting_star_ratio": 2.0,       # 流星线影线比例阈值
    "marubozu_threshold": 0.95,       # 光头光脚线阈值
    "spinning_top_threshold": 0.6     # 陀螺线阈值
}

# 技术分析指标配置
ENABLE_TA = True                      # 是否启用技术分析指标（True/False）
                                      # 包括移动平均、RSI、MACD等经典技术指标
                                      # 这些指标是量化交易的核心工具

HMA_PERIOD = 23                       # Hull移动平均周期（整数）
                                      # Hull MA能够减少滞后性，更快响应价格变化
                                      # 推荐：20-30个周期

KC_PERIOD = 29                        # 肯特纳通道周期（整数）
                                      # 用于识别价格突破和支撑阻力位
                                      # 推荐：20-40个周期

KC_ATR_PERIOD = 14                    # 肯特纳通道ATR周期（整数）
                                      # ATR用于计算通道宽度
                                      # 通常设置为KC_PERIOD的一半

KC_MULTIPLIER = 2.3352                # 肯特纳通道乘数（浮点数）
                                      # 控制通道的宽度，值越大通道越宽
                                      # 推荐：1.5-3.0

ATR_PERIOD = 16                       # 平均真实波幅周期（整数）
                                      # 衡量市场波动性的重要指标
                                      # 推荐：14-21个周期

RSI_PERIOD = 22                       # 相对强弱指数周期（整数）
                                      # 用于判断超买超卖状态
                                      # 推荐：14-30个周期

MACD_FAST = 17                        # MACD快线周期（整数）
                                      # 短期指数移动平均周期
                                      # 推荐：12-20个周期

MACD_SLOW = 30                        # MACD慢线周期（整数）
                                      # 长期指数移动平均周期
                                      # 推荐：26-35个周期

MACD_SIGN = 11                        # MACD信号线周期（整数）
                                      # 用于生成买卖信号
                                      # 推荐：9-15个周期

STOCH_K = 16                          # 随机指标K值周期（整数）
                                      # 衡量价格在一定周期内的相对位置
                                      # 推荐：14-21个周期

STOCH_D = 2                           # 随机指标D值平滑周期（整数）
                                      # 对K值进行平滑处理
                                      # 推荐：3-5个周期

STOCH_SMOOTH_K = 6                    # 随机指标K值平滑周期（整数）
                                      # 进一步平滑K值，减少噪音
                                      # 推荐：3-9个周期

CCI_CONSTANT = 0.015                  # 商品通道指数常数（浮点数）
                                      # 标准值为0.015，通常不需要修改
                                      # 用于标准化CCI指标

ENABLE_TA_DERIVED_FEATURES = True     # 是否启用技术指标衍生特征（True/False）
                                      # 包括指标的变化率、交叉信号等
                                      # 能够提供更丰富的市场信息

# 时间特征配置
ENABLE_TIME = True                    # 是否启用时间特征（True/False）
                                      # 包括小时、星期、月份等时间信息
                                      # 用于捕捉市场的时间规律

ENABLE_TIME_TRIGONOMETRIC = True      # 是否启用三角函数时间特征（True/False）
                                      # 将时间信息转换为周期性的三角函数
                                      # 更好地表示时间的周期性特征

# 趋势特征配置
ENABLE_TREND = True                   # 是否启用趋势特征（True/False）
                                      # 计算不同周期的价格趋势
                                      # 趋势是技术分析的核心概念

TREND_PERIODS = [5, 10, 20]           # 趋势计算周期列表
                                      # 分别计算短期、中期、长期趋势
                                      # 推荐：包含3-5个不同的周期

ENABLE_ADX_TREND_FEATURES = True      # 是否启用ADX趋势特征（True/False）
                                      # ADX用于衡量趋势强度
                                      # 帮助区分趋势市和震荡市

ENABLE_EMA_TREND_FEATURES = True      # 是否启用EMA趋势特征（True/False）
                                      # 基于指数移动平均的趋势分析
                                      # 比简单移动平均更敏感

# 多时间框架分析配置（MTFA）
ENABLE_MTFA = True                    # 是否启用多时间框架分析（True/False）
                                      # 同时分析多个时间周期的数据
                                      # 能够提供更全面的市场视角

MTFA_TIMEFRAMES = ['30m', '1h', '4h'] # 多时间框架列表
                                      # 除了主要时间框架外，还分析这些更大的时间框架
                                      # 推荐：包含2-4个更大的时间框架
                                      # 注意：时间框架过多会增加计算复杂度

MTFA_FEATURE_LOOKBACK_PERIODS = 200  # MTFA特征回看周期数（整数）
                                      # 用于计算更大时间框架的技术指标
                                      # 推荐：100-300个周期

MTFA_SPECIFIC_LOOKBACKS = {}          # 特定时间框架的回看周期字典
                                      # 可以为不同时间框架设置不同的回看周期
                                      # 例如：{'1h': 100, '4h': 50}

MTFA_MIN_BARS_TO_FETCH = 50           # MTFA最小获取K线数量（整数）
                                      # 确保有足够的数据进行计算
                                      # 推荐：50-100个K线

MTFA_MIN_BARS_FOR_CALC = 50           # MTFA最小计算K线数量（整数）
                                      # 开始计算技术指标所需的最小数据量
                                      # 应该小于或等于MIN_BARS_TO_FETCH

MTFA_FETCH_BUFFER = 10                # MTFA数据获取缓冲区（整数）
                                      # 额外获取的数据量，用于处理边界情况
                                      # 推荐：5-20个K线

# 资金流特征配置
ENABLE_FUND_FLOW = True               # 是否启用资金流特征（True/False）
                                      # 分析买卖盘的资金流向
                                      # 对于判断市场情绪很有帮助

FUND_FLOW_RATIO_SMOOTHING_PERIOD = 15 # 资金流比率平滑周期（整数）
                                      # 对资金流数据进行平滑处理
                                      # 推荐：10-30个周期

# 高级数据处理配置
ENABLE_INTELLIGENT_NAN_PROCESSING = True  # 是否启用智能NaN处理（True/False）
                                           # 智能处理缺失数据，而不是简单填充
                                           # 提高数据质量和模型性能

ENABLE_SAFE_FILL_NANS = True          # 是否启用安全NaN填充（True/False）
                                      # 使用安全的方法填充缺失值
                                      # 避免引入未来信息

MIN_HISTORICAL_BARS_FOR_PREDICTION = 100  # 预测所需的最小历史K线数量（整数）
                                           # 确保有足够的历史数据进行特征计算
                                           # 推荐：100-200个K线

ENABLE_ADVANCED_FEATURE_VALIDATION = True  # 是否启用高级特征验证（True/False）
                                           # 验证特征的有效性和一致性
                                           # 帮助发现数据质量问题

# =============================================================================
# 回测输出配置 - 控制回测结果的保存和展示
# =============================================================================

# 输出目录配置
OUTPUT_DIR = "results/backtest_results"           # 主输出目录
                                                  # 所有回测结果都保存在此目录下
                                                  # 建议使用绝对路径或相对于项目根目录的路径

CHARTS_DIR = os.path.join(OUTPUT_DIR, "charts")   # 图表保存目录
                                                  # 存放回测生成的各种图表文件
                                                  # 包括收益曲线、回撤图等

REPORTS_DIR = os.path.join(OUTPUT_DIR, "reports") # 报告保存目录
                                                  # 存放详细的回测报告文件
                                                  # 包括HTML、PDF、Excel等格式

LOGS_DIR = os.path.join(OUTPUT_DIR, "logs")       # 日志保存目录
                                                  # 存放回测过程中的日志文件
                                                  # 用于调试和问题排查

# 报告生成配置
GENERATE_DETAILED_REPORT = True       # 是否生成详细报告（True/False）
                                      # 包含完整的回测统计信息和分析
                                      # 建议：True，便于深入分析结果

GENERATE_CHARTS = True                # 是否生成图表（True/False）
                                      # 生成可视化的回测结果图表
                                      # 建议：True，图表更直观

SAVE_TRADE_LOG = True                 # 是否保存交易日志（True/False）
                                      # 记录每笔交易的详细信息
                                      # 建议：True，便于分析具体交易

SAVE_DAILY_STATS = True               # 是否保存每日统计（True/False）
                                      # 记录每日的盈亏和统计信息
                                      # 建议：True，便于分析日间表现

# 图表样式配置
CHART_STYLE = "seaborn-v0_8"          # 图表样式主题
                                      # 支持matplotlib的所有样式
                                      # 推荐："seaborn-v0_8", "ggplot", "bmh"

CHART_DPI = 300                       # 图表分辨率（DPI）
                                      # 控制图表的清晰度
                                      # 推荐：150-300，值越高越清晰但文件越大

CHART_FIGSIZE = (12, 8)               # 图表尺寸（宽度, 高度）
                                      # 以英寸为单位的图表大小
                                      # 推荐：(10, 6)到(16, 10)

# =============================================================================
# 性能评估配置 - 控制回测性能指标的计算
# =============================================================================

# 评估指标开关
CALCULATE_SHARPE_RATIO = True         # 是否计算夏普比率（True/False）
                                      # 衡量风险调整后的收益
                                      # 重要指标，建议启用

CALCULATE_MAX_DRAWDOWN = True         # 是否计算最大回撤（True/False）
                                      # 衡量最大的资金损失幅度
                                      # 风险控制的重要指标

CALCULATE_WIN_RATE = True             # 是否计算胜率（True/False）
                                      # 盈利交易占总交易的比例
                                      # 基础但重要的指标

CALCULATE_PROFIT_FACTOR = True        # 是否计算盈利因子（True/False）
                                      # 总盈利与总亏损的比率
                                      # 衡量策略盈利能力

CALCULATE_CALMAR_RATIO = True         # 是否计算卡尔玛比率（True/False）
                                      # 年化收益率与最大回撤的比率
                                      # 综合评估收益和风险

# 基准和风险配置
RISK_FREE_RATE = 0.02                 # 无风险利率（年化比率）
                                      # 用于计算夏普比率等指标
                                      # 通常使用国债收益率，如2%

BENCHMARK_RETURN = 0.0                # 基准收益率（年化比率）
                                      # 用于比较策略表现
                                      # 可以设置为市场指数收益率

# =============================================================================
# 调试和日志配置 - 控制程序运行时的信息输出和调试功能
# =============================================================================

# 日志级别配置
LOG_LEVEL = "INFO"                    # 日志输出级别，支持以下选项：
                                      # "DEBUG" - 详细调试信息，用于开发调试
                                      # "INFO" - 一般信息，推荐用于生产环境
                                      # "WARNING" - 警告信息，只显示重要提醒
                                      # "ERROR" - 错误信息，只显示错误

VERBOSE_OUTPUT = True                 # 是否输出详细信息（True/False）
                                      # True：显示详细的回测过程信息
                                      # False：只显示关键信息，适合批量回测

PROGRESS_BAR = True                   # 是否显示进度条（True/False）
                                      # 显示回测进度，便于了解完成情况
                                      # 建议：True，特别是长时间回测

# 调试选项配置
DEBUG_MODE = False                    # 是否启用调试模式（True/False）
                                      # 启用后会输出更多调试信息
                                      # 仅在开发和问题排查时使用

SAVE_INTERMEDIATE_DATA = False        # 是否保存中间数据（True/False）
                                      # 保存特征计算、信号生成等中间结果
                                      # 用于调试，但会占用大量存储空间

VALIDATE_SIGNALS = True               # 是否验证信号有效性（True/False）
                                      # 检查生成的交易信号是否合理
                                      # 建议：True，避免异常信号

# 数据完整性验证配置
STRICT_VALIDATION = True              # 是否启用严格验证模式（True/False）
                                      # True：验证失败时终止回测
                                      # False：验证失败时继续运行但记录警告

ENABLE_DATA_LEAKAGE_CHECK = True      # 是否启用数据泄露检测（True/False）
                                      # 检测是否使用了未来数据进行预测
                                      # 重要：确保回测结果的可靠性

ENABLE_GRANGER_CAUSALITY_TEST = True  # 是否启用格兰杰因果检验（True/False）
                                      # 检验特征与目标变量的因果关系
                                      # 用于验证特征的有效性

MAX_GRANGER_LAGS = 5                  # 格兰杰检验最大滞后期（整数）
                                      # 检验的最大时间滞后
                                      # 推荐：3-10个周期

# =============================================================================
# 高级配置 - 专业用户的高级设置选项
# =============================================================================

# 交易成本配置
SLIPPAGE = 0.0                        # 滑点成本（比率）
                                      # 二元期权通常无滑点，保持0.0
                                      # 如果模拟其他交易类型，可以设置如0.001

COMMISSION = 0.0                      # 手续费成本（比率）
                                      # 二元期权通常无手续费，保持0.0
                                      # 如果模拟其他交易类型，可以设置如0.001

# 市场时间配置
MARKET_OPEN_HOUR = 0                  # 市场开放时间（0-23小时）
                                      # 加密货币市场24小时开放，通常设置为0
                                      # 股票市场可以设置为具体开盘时间

MARKET_CLOSE_HOUR = 24                # 市场关闭时间（1-24小时）
                                      # 加密货币市场24小时开放，通常设置为24
                                      # 股票市场可以设置为具体收盘时间

EXCLUDE_WEEKENDS = False              # 是否排除周末交易（True/False）
                                      # 加密货币24/7交易，设置为False
                                      # 股票市场通常设置为True

# 数据质量控制配置
MIN_DATA_QUALITY_SCORE = 0.8          # 最小数据质量分数（0.0-1.0）
                                      # 低于此分数的数据会被标记或过滤
                                      # 推荐：0.7-0.9

HANDLE_MISSING_DATA = True            # 是否处理缺失数据（True/False）
                                      # 自动处理数据中的缺失值
                                      # 建议：True，提高数据完整性

DATA_VALIDATION_ENABLED = True        # 是否启用数据验证（True/False）
                                      # 验证数据的格式和范围
                                      # 建议：True，确保数据质量

# 并行处理配置
ENABLE_MULTIPROCESSING = False        # 是否启用多进程处理（True/False）
                                      # 回测通常是顺序的，不建议启用
                                      # 某些特征计算可能支持并行

MAX_WORKERS = 4                       # 最大工作进程数（整数）
                                      # 仅在启用多进程时有效
                                      # 推荐：CPU核心数的50%-100%

# =============================================================================
# 辅助函数 - 提供配置相关的实用功能
# =============================================================================

def get_model_path(model_type="up"):
    """
    获取指定类型模型的文件路径

    Args:
        model_type (str): 模型类型，支持以下选项：
            - "up": 看涨模型
            - "down": 看跌模型
            - "meta": 元模型

    Returns:
        str: 模型文件的完整路径

    Raises:
        FileNotFoundError: 当找不到指定模型文件时
        ValueError: 当model_type参数无效时

    Example:
        >>> up_model_path = get_model_path("up")
        >>> print(up_model_path)
        models/trained_models_btc_15m_up/model_BTC_15m_UP_30m_fold4_calibrated.joblib
    """
    if model_type.lower() == "up":
        # 优先使用校准版本，如果不存在则使用普通版本
        calibrated_filename = "model_BTC_15m_UP_30m_fold4_calibrated.joblib"
        calibrated_path = os.path.join(UP_MODEL_DIR, calibrated_filename)
        if os.path.exists(calibrated_path):
            return calibrated_path
        else:
            # 回退到普通版本
            filename = "model_BTC_15m_UP_30m_fold4.joblib"
            fallback_path = os.path.join(UP_MODEL_DIR, filename)
            if os.path.exists(fallback_path):
                return fallback_path
            else:
                # 如果fold4不存在，尝试其他fold（从高到低）
                for fold in [3, 2, 1, 0]:
                    fold_filename = f"model_BTC_15m_UP_30m_fold{fold}.joblib"
                    fold_path = os.path.join(UP_MODEL_DIR, fold_filename)
                    if os.path.exists(fold_path):
                        return fold_path
                raise FileNotFoundError(f"No UP model found in {UP_MODEL_DIR}")

    elif model_type.lower() == "down":
        # DOWN模型使用最后一个fold（通常性能最好）
        filename = "model_BTC_15m_DOWN_30m_fold4.joblib"
        model_path = os.path.join(DOWN_MODEL_DIR, filename)
        if os.path.exists(model_path):
            return model_path
        else:
            # 如果fold4不存在，尝试其他fold（从高到低）
            for fold in [3, 2, 1, 0]:
                fold_filename = f"model_BTC_15m_DOWN_30m_fold{fold}.joblib"
                fold_path = os.path.join(DOWN_MODEL_DIR, fold_filename)
                if os.path.exists(fold_path):
                    return fold_path
            raise FileNotFoundError(f"No DOWN model found in {DOWN_MODEL_DIR}")

    elif model_type.lower() == "meta":
        # 元模型路径
        meta_path = os.path.join(META_MODEL_DIR, "meta_model_lgbm.joblib")
        if os.path.exists(meta_path):
            return meta_path
        else:
            raise FileNotFoundError(f"Meta model not found: {meta_path}")
    else:
        raise ValueError(f"Unknown model type: {model_type}. Supported types: 'up', 'down', 'meta'")

def get_scaler_path(model_type="up"):
    """
    获取指定类型模型的特征缩放器文件路径

    Args:
        model_type (str): 模型类型，"up" 或 "down"

    Returns:
        str: 缩放器文件的完整路径

    Raises:
        ValueError: 当model_type参数无效时

    Example:
        >>> scaler_path = get_scaler_path("up")
        >>> print(scaler_path)
        models/trained_models_btc_15m_up/scaler_BTC_15m_UP_30m.joblib
    """
    if model_type.lower() == "up":
        filename = SCALER_FILENAME.format("UP")
        return os.path.join(UP_MODEL_DIR, filename)
    elif model_type.lower() == "down":
        filename = SCALER_FILENAME.format("DOWN")
        return os.path.join(DOWN_MODEL_DIR, filename)
    else:
        raise ValueError(f"Unknown model type: {model_type}. Supported types: 'up', 'down'")

def get_features_path(model_type="up"):
    """
    获取指定类型模型的特征列表文件路径

    Args:
        model_type (str): 模型类型，"up"、"down" 或 "meta"

    Returns:
        str: 特征列表文件的完整路径

    Raises:
        ValueError: 当model_type参数无效时

    Example:
        >>> features_path = get_features_path("up")
        >>> print(features_path)
        models/trained_models_btc_15m_up/final_selected_features_BTC_15m_UP_30m.json
    """
    if model_type.lower() == "up":
        filename = FEATURES_FILENAME.format("UP")
        return os.path.join(UP_MODEL_DIR, filename)
    elif model_type.lower() == "down":
        filename = FEATURES_FILENAME.format("DOWN")
        return os.path.join(DOWN_MODEL_DIR, filename)
    elif model_type.lower() == "meta":
        return os.path.join(META_MODEL_DIR, "meta_model_features.json")
    else:
        raise ValueError(f"Unknown model type: {model_type}. Supported types: 'up', 'down', 'meta'")

def create_output_dirs():
    """
    创建所有必要的输出目录

    如果目录不存在，则自动创建。这确保回测结果能够正确保存。
    包括主输出目录、图表目录、报告目录和日志目录。

    Example:
        >>> create_output_dirs()
        # 创建 results/backtest_results/charts, reports, logs 等目录
    """
    for dir_path in [OUTPUT_DIR, CHARTS_DIR, REPORTS_DIR, LOGS_DIR]:
        os.makedirs(dir_path, exist_ok=True)

def get_backtest_config():
    """
    获取完整的回测配置字典

    将所有配置参数整理成一个字典，便于传递给回测引擎。
    这个函数确保所有必要的配置参数都被包含，并且使用统一的键名。

    Returns:
        dict: 包含所有回测配置参数的字典，键名使用下划线命名法

    Example:
        >>> config = get_backtest_config()
        >>> print(config['symbol'])
        BTCUSDT
        >>> print(config['initial_balance'])
        40.0
    """
    return {
        # 基础配置
        'symbol': SYMBOL,
        'interval': INTERVAL,
        'prediction_periods': PREDICTION_PERIODS,
        'expiry_minutes': EXPIRY_MINUTES,
        'payout_ratio': PAYOUT_RATIO,
        'initial_balance': INITIAL_BALANCE,

        # 时间配置
        'start_date': BACKTEST_START_DATE,
        'end_date': BACKTEST_END_DATE,
        'data_limit': DATA_FETCH_LIMIT,

        # 交易配置
        'min_bet': MIN_BET_AMOUNT,
        'max_bet': MAX_BET_AMOUNT,
        'signal_threshold_up': SIGNAL_THRESHOLD_UP,
        'signal_threshold_down': SIGNAL_THRESHOLD_DOWN,

        # 资金管理
        'position_sizing': POSITION_SIZING_METHOD,
        'fixed_amount': FIXED_BET_AMOUNT,
        'kelly_multiplier': KELLY_MULTIPLIER,
        'percentage_risk': PERCENTAGE_RISK,

        # 风险控制
        'max_daily_loss': MAX_DAILY_LOSS,
        'max_consecutive_losses': MAX_CONSECUTIVE_LOSSES,
        'stop_loss_enabled': STOP_LOSS_ENABLED,

        # 元模型配置
        'use_meta_model': USE_META_MODEL,
        'signal_mode': SIGNAL_MODE,
        'meta_model_dir': META_MODEL_DIR,
        'meta_model_filename': META_MODEL_FILENAME,
        'meta_features_filename': META_FEATURES_FILENAME,

        # 预测间隔配置
        'prediction_intervals': PREDICTION_INTERVALS,
        'custom_interval': CUSTOM_INTERVAL,

        # 真实交易模式配置
        'realistic_mode': REALISTIC_MODE,
        'kelly_min_amount': KELLY_MIN_AMOUNT,
        'kelly_max_amount': KELLY_MAX_AMOUNT,
        'kelly_max_percentage': KELLY_MAX_PERCENTAGE,

        # 输出配置
        'output_dir': OUTPUT_DIR,
        'generate_charts': GENERATE_CHARTS,
        'generate_report': GENERATE_DETAILED_REPORT,
        'verbose': VERBOSE_OUTPUT,

        # 验证配置
        'strict_validation': STRICT_VALIDATION,
        'enable_data_leakage_check': ENABLE_DATA_LEAKAGE_CHECK,
        'enable_granger_causality_test': ENABLE_GRANGER_CAUSALITY_TEST,
        'max_granger_lags': MAX_GRANGER_LAGS,

        # 特征工程配置
        'enable_price_change': ENABLE_PRICE_CHANGE,
        'price_change_periods': PRICE_CHANGE_PERIODS,
        'enable_volume': ENABLE_VOLUME,
        'volume_avg_period': VOLUME_AVG_PERIOD,
        'enable_candle': ENABLE_CANDLE,
        'enable_pattern_recognition': ENABLE_PATTERN_RECOGNITION,
        'pattern_recognition_thresholds': PATTERN_RECOGNITION_THRESHOLDS,
        'enable_ta': ENABLE_TA,
        'hma_period': HMA_PERIOD,
        'kc_period': KC_PERIOD,
        'kc_atr_period': KC_ATR_PERIOD,
        'kc_multiplier': KC_MULTIPLIER,
        'atr_period': ATR_PERIOD,
        'rsi_period': RSI_PERIOD,
        'macd_fast': MACD_FAST,
        'macd_slow': MACD_SLOW,
        'macd_sign': MACD_SIGN,
        'stoch_k': STOCH_K,
        'stoch_d': STOCH_D,
        'stoch_smooth_k': STOCH_SMOOTH_K,
        'cci_constant': CCI_CONSTANT,
        'enable_ta_derived_features': ENABLE_TA_DERIVED_FEATURES,
        'enable_time': ENABLE_TIME,
        'enable_time_trigonometric': ENABLE_TIME_TRIGONOMETRIC,
        'enable_trend': ENABLE_TREND,
        'trend_periods': TREND_PERIODS,
        'enable_adx_trend_features': ENABLE_ADX_TREND_FEATURES,
        'enable_ema_trend_features': ENABLE_EMA_TREND_FEATURES,
        'enable_mtfa': ENABLE_MTFA,
        'mtfa_timeframes': MTFA_TIMEFRAMES,
        'mtfa_feature_lookback_periods': MTFA_FEATURE_LOOKBACK_PERIODS,
        'mtfa_specific_lookbacks': MTFA_SPECIFIC_LOOKBACKS,
        'mtfa_min_bars_to_fetch': MTFA_MIN_BARS_TO_FETCH,
        'mtfa_min_bars_for_calc': MTFA_MIN_BARS_FOR_CALC,
        'mtfa_fetch_buffer': MTFA_FETCH_BUFFER,
        'enable_fund_flow': ENABLE_FUND_FLOW,
        'fund_flow_ratio_smoothing_period': FUND_FLOW_RATIO_SMOOTHING_PERIOD,
        'enable_intelligent_nan_processing': ENABLE_INTELLIGENT_NAN_PROCESSING,
        'enable_safe_fill_nans': ENABLE_SAFE_FILL_NANS,
        'min_historical_bars_for_prediction': MIN_HISTORICAL_BARS_FOR_PREDICTION,
        'enable_advanced_feature_validation': ENABLE_ADVANCED_FEATURE_VALIDATION
    }

def validate_config():
    """
    验证配置参数的有效性

    检查所有关键配置参数是否在合理范围内，确保回测能够正常运行。
    这个函数会检查数值范围、文件路径、日期格式等各种配置的有效性。

    Returns:
        bool: 验证通过返回True

    Raises:
        ValueError: 当配置参数无效时，包含详细的错误信息列表

    Example:
        >>> validate_config()
        True
    """
    errors = []

    # 验证基本交易参数
    if INITIAL_BALANCE <= 0:
        errors.append("INITIAL_BALANCE must be positive")

    if MIN_BET_AMOUNT <= 0:
        errors.append("MIN_BET_AMOUNT must be positive")

    if MAX_BET_AMOUNT < MIN_BET_AMOUNT:
        errors.append("MAX_BET_AMOUNT must be >= MIN_BET_AMOUNT")

    if not (0 < PAYOUT_RATIO < 1):
        errors.append("PAYOUT_RATIO must be between 0 and 1")

    # 验证信号阈值参数
    if not (0 <= SIGNAL_THRESHOLD_UP <= 1):
        errors.append("SIGNAL_THRESHOLD_UP must be between 0 and 1")

    if not (0 <= SIGNAL_THRESHOLD_DOWN <= 1):
        errors.append("SIGNAL_THRESHOLD_DOWN must be between 0 and 1")

    if not (0 <= NEUTRAL_ZONE_THRESHOLD <= 0.5):
        errors.append("NEUTRAL_ZONE_THRESHOLD must be between 0 and 0.5")

    # 验证资金管理参数
    if POSITION_SIZING_METHOD not in ["fixed", "kelly", "percentage"]:
        errors.append("POSITION_SIZING_METHOD must be 'fixed', 'kelly', or 'percentage'")

    if not (0 < KELLY_MULTIPLIER <= 1):
        errors.append("KELLY_MULTIPLIER must be between 0 and 1")

    if not (0 < PERCENTAGE_RISK <= 0.5):
        errors.append("PERCENTAGE_RISK must be between 0 and 0.5")

    # 验证日期格式和逻辑
    try:
        start_date = datetime.strptime(BACKTEST_START_DATE, "%Y-%m-%d")
        end_date = datetime.strptime(BACKTEST_END_DATE, "%Y-%m-%d")
        if start_date >= end_date:
            errors.append("BACKTEST_START_DATE must be before BACKTEST_END_DATE")
    except ValueError:
        errors.append("Date format must be YYYY-MM-DD")

    # 验证技术指标参数
    if HMA_PERIOD <= 0:
        errors.append("HMA_PERIOD must be positive")

    if RSI_PERIOD <= 0:
        errors.append("RSI_PERIOD must be positive")

    if MACD_FAST >= MACD_SLOW:
        errors.append("MACD_FAST must be less than MACD_SLOW")

    # 验证目录存在性（仅在严格模式下）
    if STRICT_VALIDATION:
        if not os.path.exists(UP_MODEL_DIR):
            errors.append(f"UP_MODEL_DIR does not exist: {UP_MODEL_DIR}")

        if not os.path.exists(DOWN_MODEL_DIR):
            errors.append(f"DOWN_MODEL_DIR does not exist: {DOWN_MODEL_DIR}")

        if USE_META_MODEL and not os.path.exists(META_MODEL_DIR):
            errors.append(f"META_MODEL_DIR does not exist: {META_MODEL_DIR}")

    if errors:
        raise ValueError("Configuration validation failed:\n" + "\n".join(errors))

    return True

def get_config_summary():
    """
    获取当前配置的摘要信息

    Returns:
        dict: 包含关键配置参数的字典，用于快速查看配置状态

    Example:
        >>> summary = get_config_summary()
        >>> print(summary['symbol'])
        BTCUSDT
    """
    summary = {
        "symbol": SYMBOL,
        "interval": INTERVAL,
        "start_date": BACKTEST_START_DATE,
        "end_date": BACKTEST_END_DATE,
        "initial_balance": INITIAL_BALANCE,
        "signal_threshold_up": SIGNAL_THRESHOLD_UP,
        "signal_threshold_down": SIGNAL_THRESHOLD_DOWN,
        "position_sizing": POSITION_SIZING_METHOD,
        "realistic_mode": REALISTIC_MODE,
        "use_meta_model": USE_META_MODEL,
        "payout_ratio": PAYOUT_RATIO,
        "min_bet_amount": MIN_BET_AMOUNT,
        "max_bet_amount": MAX_BET_AMOUNT
    }
    return summary

def print_config_info():
    """
    打印配置信息的友好格式

    用于在回测开始前显示当前使用的配置参数，便于用户确认设置。
    """
    print("=" * 60)
    print("📊 回测配置信息")
    print("=" * 60)

    summary = get_config_summary()
    for key, value in summary.items():
        print(f"  {key.replace('_', ' ').title()}: {value}")

    print("=" * 60)

# =============================================================================
# 配置验证和初始化
# =============================================================================

if __name__ == "__main__":
    """
    当直接运行此配置文件时，执行配置验证和信息显示

    这有助于用户检查配置是否正确，以及了解当前的配置状态。
    可以通过运行 python backtest_config.py 来测试配置。
    """
    try:
        print("🔍 正在验证回测配置...")
        validate_config()
        print("✅ 配置验证通过")

        print("\n📁 正在创建输出目录...")
        create_output_dirs()
        print("✅ 输出目录创建完成")

        print("\n📊 当前配置摘要:")
        print_config_info()

        print("\n💡 配置文件说明:")
        print("  - 修改配置后需要重新运行回测程序")
        print("  - 建议先在小时间范围内测试配置的有效性")
        print("  - 关键参数如信号阈值、资金管理参数需要谨慎调整")
        print("  - 可以通过 python backtest_config.py 验证配置")

    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        print("\n💡 请检查并修正配置参数后重试")
        print("常见问题:")
        print("  - 检查日期格式是否为 YYYY-MM-DD")
        print("  - 检查数值参数是否在合理范围内")
        print("  - 检查模型目录路径是否存在")
        print("  - 检查信号阈值是否在 0-1 之间")
