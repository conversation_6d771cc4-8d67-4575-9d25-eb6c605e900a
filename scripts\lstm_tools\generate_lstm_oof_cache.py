#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LSTM模型OOF缓存生成工具
生成LSTM模型的真实OOF预测缓存，大幅提升元模型训练速度

使用场景:
1. LSTM模型重新训练后需要生成新的OOF缓存
2. 现有OOF缓存损坏或过期时重新生成
3. 从测试缓存升级到真实缓存

效果:
- 元模型训练时间从20-30分钟缩短到2-5分钟
- 一次生成，长期受益
- 保留LSTM模型的价值贡献
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import config
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import time

def generate_lstm_oof_cache():
    """生成LSTM模型的真实OOF预测缓存"""
    print("🚀 开始生成LSTM模型OOF缓存")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from src.training.optimized_training_module import generate_oof_predictions_from_trained_models
        from src.core import data_utils
        
        # 获取LSTM模型配置
        lstm_config = None
        for target in config.PREDICTION_TARGETS:
            if target.get('name') == 'BTC_15m_LSTM':
                lstm_config = target
                break
        
        if not lstm_config:
            print("❌ 未找到LSTM模型配置")
            return False
        
        print("✅ 找到LSTM模型配置")
        
        # 获取数据
        print("📊 获取训练数据...")
        
        # 这里需要获取与训练时相同的数据
        from binance.client import Client
        binance_client = Client()
        
        # 获取历史数据
        symbol = lstm_config.get('symbol', 'BTCUSDT')
        interval = lstm_config.get('interval', '15m')
        limit = lstm_config.get('data_fetch_limit', 5000)
        
        print(f"   交易对: {symbol}")
        print(f"   时间周期: {interval}")
        print(f"   数据量: {limit}")
        
        # 获取K线数据
        klines = binance_client.get_historical_klines(
            symbol, interval, f"{limit} hours ago UTC"
        )
        
        if not klines:
            print("❌ 获取K线数据失败")
            return False
        
        # 转换为DataFrame
        df_klines = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        # 数据预处理
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df_klines[col] = pd.to_numeric(df_klines[col])
        
        df_klines['timestamp'] = pd.to_datetime(df_klines['timestamp'], unit='ms')
        df_klines.set_index('timestamp', inplace=True)
        
        print(f"✅ 获取到 {len(df_klines)} 条K线数据")
        
        # 生成特征
        print("🔧 生成LSTM特征...")
        df_with_features = data_utils.add_lstm_features(df_klines.copy(), lstm_config)
        
        if df_with_features is None:
            print("❌ LSTM特征生成失败")
            return False
        
        print(f"✅ 生成了 {df_with_features.shape[1]} 个特征")
        
        # 生成目标变量（简化版本）
        print("🎯 生成目标变量...")
        
        # 使用简单的价格变化作为目标
        df_with_features['target'] = (
            df_with_features['close'].shift(-1) > df_with_features['close']
        ).astype(int)
        
        # 移除最后一行（没有目标值）
        df_with_features = df_with_features[:-1].dropna()
        
        print(f"✅ 生成了 {len(df_with_features)} 个有效样本")
        
        # 准备数据用于OOF生成
        feature_columns = [col for col in df_with_features.columns 
                          if col not in ['target', 'open', 'high', 'low', 'close', 'volume']]
        
        X_df = df_with_features[feature_columns]
        y_series = df_with_features['target']
        
        print(f"📊 特征数: {len(feature_columns)}")
        print(f"📊 样本数: {len(X_df)}")
        
        # 生成OOF预测
        print("🔮 生成OOF预测...")
        print("   这可能需要几分钟时间...")
        
        start_time = time.time()
        
        oof_df = generate_oof_predictions_from_trained_models(
            X_unscaled_df=X_df,
            y_series=y_series,
            target_config=lstm_config,
            model_dir=lstm_config.get('model_save_dir'),
            target_name='BTC_15m_LSTM'
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        if oof_df is None or oof_df.empty:
            print("❌ OOF预测生成失败")
            return False
        
        print(f"✅ OOF预测生成完成！")
        print(f"   耗时: {generation_time:.1f} 秒")
        print(f"   预测数: {len(oof_df)}")
        
        # 保存OOF缓存
        print("💾 保存OOF缓存...")
        
        model_dir = lstm_config.get('model_save_dir')
        
        # 保存为多种格式
        oof_pkl_path = os.path.join(model_dir, 'oof_predictions.pkl')
        oof_csv_path = os.path.join(model_dir, 'oof_predictions.csv')
        
        oof_df.to_pickle(oof_pkl_path)
        oof_df.to_csv(oof_csv_path)
        
        print(f"✅ OOF缓存已保存:")
        print(f"   PKL: {oof_pkl_path}")
        print(f"   CSV: {oof_csv_path}")
        
        # 验证缓存
        print("🔍 验证缓存文件...")
        
        try:
            loaded_oof = pd.read_pickle(oof_pkl_path)
            print(f"✅ 缓存验证成功: {len(loaded_oof)} 条记录")
            
            # 显示统计信息
            if len(loaded_oof.columns) > 0:
                first_col = loaded_oof.iloc[:, 0]
                print(f"   预测范围: [{first_col.min():.3f}, {first_col.max():.3f}]")
                print(f"   平均值: {first_col.mean():.3f}")
                
        except Exception as e:
            print(f"❌ 缓存验证失败: {e}")
            return False
        
        print("\n🎉 LSTM OOF缓存生成完成！")
        print("📈 预期效果:")
        print("   - 元模型训练时间从20-30分钟缩短到2-5分钟")
        print("   - 保留LSTM模型的价值贡献")
        print("   - 一次生成，长期受益")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成OOF缓存时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 LSTM模型OOF缓存生成器")
    print("解决LSTM模型训练缓慢问题的根本方案")
    print("=" * 60)
    
    # 确认操作
    print("⚠️  注意事项:")
    print("1. 这个过程可能需要5-10分钟")
    print("2. 会使用当前的LSTM模型生成真实的OOF预测")
    print("3. 生成后元模型训练将大幅加速")
    print("4. 如果数据或模型有更新，需要重新生成")
    
    confirm = input("\n是否继续生成LSTM OOF缓存? (y/n): ").lower().strip()
    
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    success = generate_lstm_oof_cache()
    
    if success:
        print("\n🚀 下一步:")
        print("   现在可以运行元模型训练，应该会非常快！")
        print("   命令: python train_models.py --target MetaSignal_BTC")
    else:
        print("\n💡 备选方案:")
        print("   如果OOF生成失败，可以考虑临时禁用LSTM模型")
        print("   或者检查LSTM模型的完整性")

if __name__ == "__main__":
    main()
