# 🚀 增强错误处理与日志记录指南

## 概述

本指南介绍了新实现的统一错误处理与日志记录系统，包括 `handle_training_exception` 装饰器和 `EnhancedLogger` 类，用于提供详细的上下文信息记录和优雅的异常处理。

## 核心特性

✅ **统一异常处理装饰器** - `handle_training_exception` 装饰器自动处理各种异常类型  
✅ **增强日志记录器** - `EnhancedLogger` 支持上下文信息记录（`ctx_...`）  
✅ **特定异常类型识别** - 自动识别并处理 Binance API、网络连接等特定异常  
✅ **详细堆栈跟踪** - 可配置的堆栈跟踪记录  
✅ **优雅降级** - 支持回退结果和错误恢复  
✅ **上下文信息捕获** - 自动记录函数调用上下文和异常详情  

## 快速开始

### 1. 导入模块

```python
from src.core.error_handler import handle_training_exception, get_enhanced_logger
```

### 2. 使用增强日志记录器

```python
def my_training_function(target_name, data):
    # 获取增强日志记录器
    enhanced_logger = get_enhanced_logger(__name__)
    
    # 记录带上下文信息的日志
    enhanced_logger.info(
        "开始训练模型",
        target_name=target_name,
        data_shape=data.shape,
        model_type="RandomForest"
    )
    
    # 记录警告
    enhanced_logger.warning(
        "数据质量检查发现问题",
        target_name=target_name,
        missing_values=25,
        action_taken="使用插值填充"
    )
    
    # 记录错误
    enhanced_logger.error(
        "模型训练失败",
        target_name=target_name,
        error_type="ValueError",
        feature_count=150
    )
```

### 3. 使用训练异常处理装饰器

```python
@handle_training_exception(
    function_name="train_model",
    fallback_result={
        'success': False,
        'error': 'Training failed',
        'gui_metrics': {'status': '训练失败', 'accuracy': 'N/A'}
    },
    include_traceback=True,
    max_traceback_lines=10
)
def train_model(target_name, X, y):
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        "开始模型训练",
        target_name=target_name,
        X_shape=X.shape,
        y_shape=y.shape
    )
    
    # 训练逻辑...
    # 如果发生异常，装饰器会自动处理并返回fallback_result
    
    return {'success': True, 'model': model}
```

## 装饰器参数详解

### `handle_training_exception` 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `function_name` | str | None | 函数名称（可选，默认使用被装饰函数名） |
| `target_name` | str | None | 目标名称（可选，会尝试从参数中提取） |
| `fallback_result` | Any | None | 异常时返回的默认值 |
| `log_level` | str | 'ERROR' | 日志级别 |
| `include_traceback` | bool | True | 是否包含堆栈跟踪 |
| `max_traceback_lines` | int | 10 | 最大堆栈跟踪行数 |

### 示例配置

```python
# 基础配置
@handle_training_exception()
def simple_function():
    pass

# 完整配置
@handle_training_exception(
    function_name="complex_training_function",
    target_name="BTC_15m",
    fallback_result={'error': 'Training failed'},
    log_level='WARNING',
    include_traceback=True,
    max_traceback_lines=5
)
def complex_function():
    pass
```

## 支持的异常类型

装饰器会自动识别并特殊处理以下异常类型：

### 1. Binance API 异常
- `BinanceAPIException` - API错误（记录错误代码和消息）
- `BinanceRequestException` - 请求异常

### 2. 网络异常
- `requests.exceptions.ConnectionError` - 连接错误
- `requests.exceptions.Timeout` - 超时异常

### 3. 数据异常
- `ValueError` - 数值错误
- `TypeError` - 类型错误
- `KeyError` - 键值缺失（记录缺失的键）

### 4. 系统异常
- `FileNotFoundError` - 文件未找到
- `MemoryError` - 内存不足

### 5. 通用异常
- `Exception` - 其他未分类异常

## 上下文信息记录

### EnhancedLogger 方法

```python
enhanced_logger = get_enhanced_logger(__name__)

# 信息级别
enhanced_logger.info("消息", key1=value1, key2=value2)

# 警告级别
enhanced_logger.warning("警告消息", target_name="BTC", issue="data_quality")

# 错误级别
enhanced_logger.error("错误消息", error_type="ValueError", context="training")

# 调试级别
enhanced_logger.debug("调试消息", step="preprocessing", progress=0.5)
```

### 自动上下文捕获

装饰器会自动记录以下上下文信息：

- `function` - 函数名称
- `target_name` - 目标名称（如果可用）
- `error_type` - 异常类型
- `timestamp` - 时间戳
- `args_count` - 参数数量
- `kwargs_keys` - 关键字参数键列表
- `line_number` - 异常发生行号
- `filename` - 文件名

## 在训练模块中的应用

### 已应用的模块

1. **src/training/training_module.py**
   - `get_unscaled_features_and_target()`
   - `train_target()`

2. **src/training/optimized_training_module.py**
   - `get_unscaled_features_and_target_optimized()`
   - `train_target_optimized()`

3. **src/core/data_utils.py**
   - `fetch_binance_history()`
   - `fetch_funding_rate_history()`
   - `fetch_open_interest_history()`

### 应用示例

```python
# 在训练函数中
@handle_training_exception(
    function_name="train_target",
    fallback_result={
        'success': False,
        'error': 'Training failed due to exception',
        'gui_metrics': {'status': '训练失败', 'accuracy': 'N/A'}
    }
)
def train_target(target_name, target_config, X, y, ...):
    enhanced_logger = get_enhanced_logger(__name__)
    
    enhanced_logger.info(
        f"开始训练 {target_name}",
        target_name=target_name,
        X_shape=X.shape,
        y_shape=y.shape
    )
    
    # 训练逻辑...
    
    enhanced_logger.info(
        f"{target_name} 训练完成",
        target_name=target_name,
        accuracy=accuracy,
        training_status="success"
    )
    
    return result
```

## 最佳实践

### 1. 日志记录原则
- 使用有意义的上下文键名（如 `target_name`, `data_shape`, `model_type`）
- 记录关键的业务指标和状态信息
- 在函数开始和结束时记录日志
- 使用适当的日志级别

### 2. 异常处理原则
- 为关键函数提供合理的回退结果
- 根据函数的重要性调整 `max_traceback_lines`
- 对于数据获取函数，返回空 DataFrame 作为回退
- 对于训练函数，返回失败状态字典

### 3. 性能考虑
- 装饰器开销很小，可以广泛使用
- 上下文信息记录是轻量级的
- 堆栈跟踪只在异常时记录

## 运行演示

```bash
# 运行错误处理演示
python examples/enhanced_error_handling_demo.py
```

演示脚本展示了：
- 增强日志记录器的使用
- 各种异常类型的处理
- 训练流水线中的错误处理模拟

## 故障排除

### 常见问题

1. **导入错误**
   ```python
   # 确保正确导入
   from src.core.error_handler import handle_training_exception, get_enhanced_logger
   ```

2. **装饰器不生效**
   ```python
   # 确保装饰器在函数定义之前
   @handle_training_exception()
   def my_function():
       pass
   ```

3. **日志不显示**
   ```python
   # 确保配置了日志级别
   import logging
   logging.basicConfig(level=logging.INFO)
   ```

### 调试技巧

- 使用 `include_traceback=True` 获取详细错误信息
- 检查日志输出中的 `ctx_` 前缀上下文信息
- 使用 `enhanced_logger.debug()` 记录详细的调试信息

## 总结

新的错误处理与日志记录系统提供了：

1. **统一的异常处理** - 通过装饰器实现一致的错误处理
2. **详细的上下文记录** - 自动捕获和记录关键信息
3. **特定异常识别** - 针对不同异常类型的专门处理
4. **优雅的降级** - 支持回退结果和错误恢复
5. **易于使用** - 简单的装饰器语法和日志记录API

这个系统大大提高了代码的健壮性和可维护性，使得错误诊断和问题排查变得更加容易。
