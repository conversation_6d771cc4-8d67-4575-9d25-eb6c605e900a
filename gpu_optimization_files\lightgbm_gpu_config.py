"""
LightGBM GPU优化配置
适用于RTX 3070
"""

import lightgbm as lgb
import numpy as np
import time

# RTX 3070优化的LightGBM GPU参数
LIGHTGBM_GPU_PARAMS = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'device_type': 'gpu',
    'gpu_platform_id': 0,
    'gpu_device_id': 0,
    'gpu_use_dp': False,  # 使用单精度，RTX 3070更适合
    'num_leaves': 255,    # RTX 3070可以处理更多叶子
    'learning_rate': 0.05,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'verbose': -1,
    'random_state': 42,
    'n_estimators': 500
}

# CPU备用参数
LIGHTGBM_CPU_PARAMS = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'device_type': 'cpu',
    'num_threads': 16,  # AMD 16核
    'force_row_wise': True,
    'histogram_pool_size': -1,
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'verbose': -1,
    'random_state': 42,
    'n_estimators': 500
}

def create_optimized_lgb_model(use_gpu=True):
    """创建优化的LightGBM模型"""
    try:
        if use_gpu:
            print("🚀 尝试创建GPU模型...")
            params = LIGHTGBM_GPU_PARAMS.copy()
            model = lgb.LGBMClassifier(**params)
            
            # 测试GPU是否可用
            test_X = np.random.random((100, 10)).astype(np.float32)
            test_y = np.random.randint(0, 2, 100)
            model.fit(test_X, test_y)
            
            print("✅ GPU模型创建成功")
            return model, True
            
    except Exception as e:
        print(f"⚠️  GPU模型创建失败: {e}")
        print("🔄 切换到CPU模式...")
    
    # 使用CPU模式
    params = LIGHTGBM_CPU_PARAMS.copy()
    model = lgb.LGBMClassifier(**params)
    print("✅ CPU模型创建成功")
    return model, False

def benchmark_gpu_vs_cpu():
    """GPU vs CPU性能对比"""
    print("\n🏁 GPU vs CPU性能对比")
    print("-" * 40)
    
    # 生成测试数据
    n_samples = 10000
    n_features = 50
    X = np.random.random((n_samples, n_features)).astype(np.float32)
    y = np.random.randint(0, 2, n_samples)
    
    results = {}
    
    # GPU测试
    try:
        print("测试GPU性能...")
        gpu_model = lgb.LGBMClassifier(**LIGHTGBM_GPU_PARAMS)
        
        start_time = time.time()
        gpu_model.fit(X, y)
        gpu_time = time.time() - start_time
        
        results['GPU'] = gpu_time
        print(f"  GPU训练时间: {gpu_time:.2f}秒")
        
    except Exception as e:
        print(f"  GPU测试失败: {e}")
    
    # CPU测试
    print("测试CPU性能...")
    cpu_model = lgb.LGBMClassifier(**LIGHTGBM_CPU_PARAMS)
    
    start_time = time.time()
    cpu_model.fit(X, y)
    cpu_time = time.time() - start_time
    
    results['CPU'] = cpu_time
    print(f"  CPU训练时间: {cpu_time:.2f}秒")
    
    # 计算加速比
    if 'GPU' in results and 'CPU' in results:
        speedup = results['CPU'] / results['GPU']
        print(f"\n🚀 GPU加速比: {speedup:.2f}x")
    
    return results

if __name__ == "__main__":
    # 测试GPU功能
    gpu_model, is_gpu = create_optimized_lgb_model(use_gpu=True)
    
    if is_gpu:
        print("\n🎉 LightGBM GPU配置成功！")
        benchmark_gpu_vs_cpu()
    else:
        print("\n⚠️  使用CPU模式，但已优化")
