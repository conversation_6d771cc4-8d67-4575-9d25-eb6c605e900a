#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元标签质量权重配置模块

定义质量权重系统的配置参数和默认值
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# 默认质量权重配置
DEFAULT_META_LABEL_QUALITY_CONFIG = {
    # 是否启用质量权重
    'enable_meta_label_quality_weighting': True,
    
    # 效率权重配置
    'efficiency_weight_config': {
        'max_efficiency_multiplier': 1.5,  # 最大效率权重倍数
        'min_efficiency_weight': 0.1,      # 最小效率权重
        'max_efficiency_weight': 3.0,      # 最大效率权重
        'direction_penalty': 0.5,          # 方向错误时的权重惩罚
        'final_price_weight_multiplier': 2.0,  # 基于最终价格的权重倍数
    },
    
    # 平滑度权重配置
    'smoothness_weight_config': {
        'drawdown_threshold': 0.1,         # 回撤阈值（10%）
        'smoothness_multiplier': 10,       # 平滑度权重倍数
        'min_smoothness_weight': 0.5,      # 最小平滑度权重
        'max_smoothness_weight': 2.0,      # 最大平滑度权重
    },
    
    # 最终权重限制
    'final_weight_limits': {
        'min_final_weight': 0.1,           # 最小最终权重
        'max_final_weight': 5.0,           # 最大最终权重
        'high_quality_threshold': 2.0,     # 高质量样本阈值
    },
    
    # 阈值配置（如果不使用三道屏障）
    'fallback_thresholds': {
        'default_profit_threshold': 0.02,  # 默认止盈阈值
        'default_loss_threshold': 0.015,   # 默认止损阈值
    },
    
    # 日志配置
    'logging_config': {
        'enable_detailed_logging': True,   # 是否启用详细日志
        'log_sample_statistics': True,     # 是否记录样本统计
        'log_weight_distribution': True,   # 是否记录权重分布
    }
}

def get_meta_label_quality_config(target_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取元标签质量权重配置
    
    Args:
        target_config: 目标配置字典
        
    Returns:
        质量权重配置字典
    """
    # 从目标配置中获取质量权重配置
    quality_config = target_config.get('meta_label_quality_config', {})
    
    # 与默认配置合并
    merged_config = DEFAULT_META_LABEL_QUALITY_CONFIG.copy()
    
    # 递归合并配置
    def merge_configs(default_dict, user_dict):
        for key, value in user_dict.items():
            if key in default_dict and isinstance(default_dict[key], dict) and isinstance(value, dict):
                merge_configs(default_dict[key], value)
            else:
                default_dict[key] = value
    
    merge_configs(merged_config, quality_config)
    
    # 从目标配置的顶层获取启用标志
    if 'enable_meta_label_quality_weighting' in target_config:
        merged_config['enable_meta_label_quality_weighting'] = target_config['enable_meta_label_quality_weighting']
    
    return merged_config

def validate_meta_label_quality_config(config: Dict[str, Any]) -> bool:
    """
    验证质量权重配置的有效性
    
    Args:
        config: 质量权重配置
        
    Returns:
        配置是否有效
    """
    try:
        # 检查必要的配置项
        required_sections = [
            'efficiency_weight_config',
            'smoothness_weight_config', 
            'final_weight_limits'
        ]
        
        for section in required_sections:
            if section not in config:
                logger.error(f"质量权重配置缺少必要部分: {section}")
                return False
        
        # 检查权重范围的合理性
        eff_config = config['efficiency_weight_config']
        if eff_config['min_efficiency_weight'] >= eff_config['max_efficiency_weight']:
            logger.error("效率权重配置错误: min_efficiency_weight >= max_efficiency_weight")
            return False
        
        smooth_config = config['smoothness_weight_config']
        if smooth_config['min_smoothness_weight'] >= smooth_config['max_smoothness_weight']:
            logger.error("平滑度权重配置错误: min_smoothness_weight >= max_smoothness_weight")
            return False
        
        final_config = config['final_weight_limits']
        if final_config['min_final_weight'] >= final_config['max_final_weight']:
            logger.error("最终权重配置错误: min_final_weight >= max_final_weight")
            return False
        
        logger.info("质量权重配置验证通过")
        return True
        
    except Exception as e:
        logger.error(f"质量权重配置验证失败: {e}")
        return False

def apply_quality_config_to_target(target_config: Dict[str, Any], 
                                 enable_quality_weighting: bool = True,
                                 custom_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    将质量权重配置应用到目标配置中
    
    Args:
        target_config: 目标配置字典
        enable_quality_weighting: 是否启用质量权重
        custom_config: 自定义质量权重配置
        
    Returns:
        更新后的目标配置
    """
    updated_config = target_config.copy()
    
    # 启用质量权重
    updated_config['enable_meta_label_quality_weighting'] = enable_quality_weighting
    
    # 应用自定义配置
    if custom_config:
        updated_config['meta_label_quality_config'] = custom_config
    
    logger.info(f"质量权重配置已应用到目标配置，启用状态: {enable_quality_weighting}")
    
    return updated_config

# 预定义的质量权重配置模板
QUALITY_CONFIG_TEMPLATES = {
    'conservative': {
        'efficiency_weight_config': {
            'max_efficiency_multiplier': 1.2,
            'direction_penalty': 0.7,
        },
        'smoothness_weight_config': {
            'drawdown_threshold': 0.05,
            'smoothness_multiplier': 15,
        },
        'final_weight_limits': {
            'max_final_weight': 3.0,
            'high_quality_threshold': 1.5,
        }
    },
    
    'aggressive': {
        'efficiency_weight_config': {
            'max_efficiency_multiplier': 2.0,
            'direction_penalty': 0.3,
        },
        'smoothness_weight_config': {
            'drawdown_threshold': 0.15,
            'smoothness_multiplier': 8,
        },
        'final_weight_limits': {
            'max_final_weight': 8.0,
            'high_quality_threshold': 3.0,
        }
    },
    
    'balanced': DEFAULT_META_LABEL_QUALITY_CONFIG
}

def get_quality_config_template(template_name: str) -> Dict[str, Any]:
    """
    获取预定义的质量权重配置模板
    
    Args:
        template_name: 模板名称 ('conservative', 'aggressive', 'balanced')
        
    Returns:
        质量权重配置模板
    """
    if template_name not in QUALITY_CONFIG_TEMPLATES:
        logger.warning(f"未知的质量权重配置模板: {template_name}，使用默认配置")
        return DEFAULT_META_LABEL_QUALITY_CONFIG.copy()
    
    template = QUALITY_CONFIG_TEMPLATES[template_name].copy()
    
    # 如果是简化模板，与默认配置合并
    if template_name != 'balanced':
        merged_template = DEFAULT_META_LABEL_QUALITY_CONFIG.copy()
        
        def merge_configs(default_dict, template_dict):
            for key, value in template_dict.items():
                if key in default_dict and isinstance(default_dict[key], dict) and isinstance(value, dict):
                    merge_configs(default_dict[key], value)
                else:
                    default_dict[key] = value
        
        merge_configs(merged_template, template)
        template = merged_template
    
    logger.info(f"已获取质量权重配置模板: {template_name}")
    return template
