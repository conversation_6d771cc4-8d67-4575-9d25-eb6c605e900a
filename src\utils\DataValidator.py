import pandas as pd
import numpy as np
from typing import Optional, Dict, Any

class DataValidator:
    @staticmethod
    def validate_ohlcv_data(df: pd.DataFrame) -> Dict[str, Any]:
        """验证 OHLCV 数据的完整性和合理性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'cleaned_data': df.copy()
        }
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"缺少必需列: {missing_columns}")
            return validation_result # 如果缺列，直接返回，避免后续处理错误
        
        # 先复制一份原始数据用于清理，以防转换失败或部分转换
        df_cleaned = df.copy()

        # 检查数据类型并尝试转换
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(df_cleaned[col]):
                try:
                    # 尝试转换为数值类型，无法转换的设为NaN
                    original_non_numeric_count = pd.to_numeric(df_cleaned[col], errors='coerce').isnull().sum()
                    df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
                    
                    # 检查转换后是否引入了新的NaN值（即无法转换的值）
                    if df_cleaned[col].isnull().sum() > 0:
                         validation_result['warnings'].append(f"列 {col} 中有值无法转换为数值类型，已设为NaN。")
                    else:
                         validation_result['warnings'].append(f"列 {col} 已成功转换为数值类型。")
                except Exception as e_conv: # pylint: disable=broad-except
                    validation_result['is_valid'] = False # 转换失败，数据无效
                    validation_result['errors'].append(f"列 {col} 无法转换为数值类型: {e_conv}")
                    # 如果一列关键数据转换失败，可能需要提前返回
                    validation_result['cleaned_data'] = df # 返回原始df
                    return validation_result
        
        # 在进行价格逻辑检查和异常值检查之前，处理因类型转换产生的NaN值
        # 一个简单的策略是向前填充，然后向后填充，最后用一个合理值（如0或均值）填充剩余NaN
        # 但对于OHLCV数据，更安全的做法是，如果关键价格列（open, high, low, close）在转换后出现NaN，
        # 则标记该行数据有问题，或者直接将该行移除。
        # 这里我们先标记，并在最后返回清理后的数据。
        if df_cleaned[required_columns].isnull().values.any():
            nan_rows_count = df_cleaned[required_columns].isnull().any(axis=1).sum()
            validation_result['warnings'].append(f"数据类型转换后，有 {nan_rows_count} 行在OHLCV列中包含NaN值。这些行可能影响后续分析。")
            # 选项1: 填充 (示例：用0填充，但这对于价格数据通常不好)
            # df_cleaned.fillna(0, inplace=True)
            # 选项2: 标记为无效，或在更下游处理/移除这些行
            # 这里我们暂时允许包含NaN的行继续，但下游逻辑需要能处理它们
            # 或者在这里就将包含NaN的行标记为错误，或直接移除
            # validation_result['is_valid'] = False # 如果要求OHLCV必须完整无NaN
            # validation_result['errors'].append("OHLCV数据转换后包含NaN，数据不完整")
            # validation_result['cleaned_data'] = df # 返回原始数据
            # return validation_result

        # 检查价格逻辑 (确保是在数值类型上操作)
        # 并且在操作前，需要确保这些列没有NaN值，否则比较会产生问题
        # 我们可以选择在检查前移除/填充NaN行，或者在检查时处理NaN
        
        # 仅对不存在NaN值的行进行价格逻辑检查
        valid_price_rows = df_cleaned[['open', 'high', 'low', 'close']].notna().all(axis=1)
        df_for_price_check = df_cleaned[valid_price_rows]

        if not df_for_price_check.empty:
            invalid_price_logic = (
                (df_for_price_check['high'] < df_for_price_check['low']) |
                (df_for_price_check['high'] < df_for_price_check['open']) |
                (df_for_price_check['high'] < df_for_price_check['close']) |
                (df_for_price_check['low'] > df_for_price_check['open']) |
                (df_for_price_check['low'] > df_for_price_check['close'])
            )
            if invalid_price_logic.any():
                invalid_count = invalid_price_logic.sum()
                validation_result['warnings'].append(f"发现 {invalid_count} 行价格逻辑异常 (high < low, high < open/close, low > open/close)。")
                # 可以选择修复或标记这些行，例如：
                # df_cleaned.loc[df_for_price_check[invalid_price_logic].index, 'is_price_logic_error'] = True
        else:
            validation_result['warnings'].append("没有有效的行可供进行价格逻辑检查（可能所有行都有NaN）。")

        # 检查异常值 (同样，应在无NaN的数值数据上操作)
        for col in ['open', 'high', 'low', 'close']:
            # 仅对该列中非NaN的值进行分位数计算
            numeric_col_values = df_cleaned[col].dropna()
            if len(numeric_col_values) > 1: # 需要至少两个值来计算分位数和IQR
                # 🚀 修复数据泄露：使用固定阈值进行异常值检测
                # 在数据验证阶段，我们使用历史数据的统计量
                q1, q3 = numeric_col_values.quantile([0.25, 0.75])  # 这里保留，因为是数据质量检查
                iqr = q3 - q1
                # 只有在iqr大于0时，异常值检测才有意义，避免除以0或负iqr
                if iqr > 0:
                    outlier_threshold = 3 * iqr # 可以调整这个倍数
                    # 异常值判断也应基于原始的、可能包含NaN的列，但只对非NaN值应用阈值
                    # 或者在清理了NaN的df_cleaned上做
                    outliers = (df_cleaned[col] < (q1 - outlier_threshold)) | (df_cleaned[col] > (q3 + outlier_threshold))
                    if outliers.any(): # 确保 outliers Series 不是空的并且至少有一个True
                        outlier_count = outliers.sum()
                        validation_result['warnings'].append(f"列 {col} 基于IQR方法发现 {outlier_count} 个潜在异常值。")
                        # 可以选择处理异常值，例如打标记或替换
                        # df_cleaned.loc[outliers, f'{col}_is_outlier'] = True
                elif iqr == 0 and not numeric_col_values.empty and numeric_col_values.nunique() == 1:
                    validation_result['warnings'].append(f"列 {col} 所有值相同，IQR为0，跳过异常值检测。")
                # else iqr < 0 (不太可能发生，除非数据有严重问题) 或 iqr=0 (数据点太少或都一样)
            else:
                 validation_result['warnings'].append(f"列 {col} 有效数值不足，跳过异常值检测。")
        
        validation_result['cleaned_data'] = df_cleaned
        return validation_result
