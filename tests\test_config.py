#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件
定义测试相关的配置和工具函数
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 测试数据配置
TEST_DATA_CONFIG = {
    'n_samples': 100,
    'random_seed': 42,
    'price_range': (29000, 31000),
    'volume_range': (100, 1000),
    'date_start': '2023-01-01',
    'freq': '5T'
}

# 测试目标配置
TEST_TARGET_CONFIG = {
    'name': 'TEST_TARGET',
    'symbol': 'BTCUSDT',
    'interval': '5m',
    'target_variable_type': 'binary',
    'target_direction': 'up',
    'target_periods': [1, 3, 5],
    'target_thresholds': [0.01, 0.02, 0.03],
    'drop_neutral_targets': False,
    
    # 特征配置
    'enable_price_change': True,
    'enable_volume': True,
    'enable_candle': True,
    'enable_ta': True,
    'enable_time': False,
    'enable_fund_flow': False,
    'enable_mtfa': False,
    'enable_pattern_recognition': False,
    'enable_trend_slope': False,
    
    # 技术指标参数
    'rsi_period': 14,
    'hma_period': 21,
    'atr_period': 14,
    'willr_period': 14,
    'cci_period': 14,
    'cci_constant': 0.015,
    'macd_fast': 12,
    'macd_slow': 26,
    'macd_sign': 9,
    
    # 价格变化参数
    'price_change_periods': [1, 3, 5],
    
    # 成交量参数
    'volume_change_periods': [1],
    
    # K线平滑参数
    'candle_smoothing_periods': [3],
    
    # 特征验证
    'enable_feature_validation': False,  # 测试时禁用以避免依赖问题
    'strict_feature_validation': False
}

def create_test_price_data(n_samples=None, random_seed=None, with_trend=False):
    """
    创建测试用的价格数据
    
    Args:
        n_samples: 样本数量
        random_seed: 随机种子
        with_trend: 是否包含趋势
        
    Returns:
        DataFrame with OHLCV data
    """
    if n_samples is None:
        n_samples = TEST_DATA_CONFIG['n_samples']
    
    if random_seed is not None:
        np.random.seed(random_seed)
    else:
        np.random.seed(TEST_DATA_CONFIG['random_seed'])
    
    # 生成日期索引
    dates = pd.date_range(
        TEST_DATA_CONFIG['date_start'], 
        periods=n_samples, 
        freq=TEST_DATA_CONFIG['freq']
    )
    
    # 生成价格数据
    base_price = (TEST_DATA_CONFIG['price_range'][0] + TEST_DATA_CONFIG['price_range'][1]) / 2
    
    if with_trend:
        # 添加趋势
        trend = np.linspace(0, base_price * 0.1, n_samples)
        noise = np.random.normal(0, base_price * 0.01, n_samples)
        close_prices = base_price + trend + noise
    else:
        # 随机游走
        returns = np.random.normal(0, 0.01, n_samples)
        close_prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLC数据
    data = {
        'open': close_prices + np.random.normal(0, base_price * 0.001, n_samples),
        'high': close_prices + np.abs(np.random.normal(0, base_price * 0.005, n_samples)),
        'low': close_prices - np.abs(np.random.normal(0, base_price * 0.005, n_samples)),
        'close': close_prices,
        'volume': np.random.uniform(*TEST_DATA_CONFIG['volume_range'], n_samples),
        'qav': np.random.uniform(1000000, 10000000, n_samples),
        'n': np.random.randint(100, 1000, n_samples),
        'tbbav': np.random.uniform(500000, 5000000, n_samples),
        'tbqav': np.random.uniform(500000, 5000000, n_samples)
    }
    
    return pd.DataFrame(data, index=dates)

def create_test_binary_classification_data(n_samples=None, random_seed=None, class_balance=0.3):
    """
    创建测试用的二分类数据
    
    Args:
        n_samples: 样本数量
        random_seed: 随机种子
        class_balance: 正类比例
        
    Returns:
        (y_true, y_proba) tuple
    """
    if n_samples is None:
        n_samples = TEST_DATA_CONFIG['n_samples']
    
    if random_seed is not None:
        np.random.seed(random_seed)
    else:
        np.random.seed(TEST_DATA_CONFIG['random_seed'])
    
    # 生成真实标签
    y_true = np.random.choice([0, 1], size=n_samples, p=[1-class_balance, class_balance])
    
    # 生成有区分度的概率
    y_proba = np.random.random(n_samples)
    # 让正类的概率更高
    y_proba[y_true == 1] += 0.3
    y_proba = np.clip(y_proba, 0, 1)
    
    return y_true, y_proba

def create_test_mtfa_dataframe():
    """创建测试用的MTFA特征DataFrame"""
    np.random.seed(TEST_DATA_CONFIG['random_seed'])
    n_rows = TEST_DATA_CONFIG['n_samples']
    
    data = {}
    
    # 基础数据列
    data.update({
        'open': np.random.uniform(29000, 31000, n_rows),
        'high': np.random.uniform(30000, 32000, n_rows),
        'low': np.random.uniform(28000, 30000, n_rows),
        'close': np.random.uniform(29000, 31000, n_rows),
        'volume': np.random.uniform(100, 1000, n_rows),
        'qav': np.random.uniform(1000000, 10000000, n_rows),
        'n': np.random.randint(100, 1000, n_rows),
        'tbbav': np.random.uniform(500000, 5000000, n_rows),
        'tbqav': np.random.uniform(500000, 5000000, n_rows)
    })
    
    # 目标变量列
    data.update({
        'target_up_1p': np.random.choice([0, 1], n_rows),
        'target_down_1p': np.random.choice([0, 1], n_rows),
        'future_close_1p': np.random.uniform(29000, 31000, n_rows),
        'label_binary': np.random.choice([0, 1], n_rows)
    })
    
    # 字符串列
    data.update({
        'candlestick_pattern_name': np.random.choice(['doji', 'hammer', 'star'], n_rows),
        'signal_name': np.random.choice(['buy', 'sell', 'hold'], n_rows)
    })
    
    # 临时列
    data.update({
        'temp_calculation': np.random.uniform(0, 1, n_rows),
        'debug_info': np.random.uniform(0, 1, n_rows)
    })
    
    # 元数据列
    data.update({
        'timestamp': pd.date_range('2023-01-01', periods=n_rows, freq='5T'),
        'symbol': ['BTCUSDT'] * n_rows
    })
    
    # 技术指标特征
    data.update({
        'RSI_14': np.random.uniform(0, 100, n_rows),
        'MACD': np.random.uniform(-1, 1, n_rows),
        'HMA_20': np.random.uniform(29000, 31000, n_rows),
        'ATRr_14': np.random.uniform(0, 1000, n_rows),
        'WILLR_14': np.random.uniform(-100, 0, n_rows)
    })
    
    # 价格特征
    data.update({
        'price_change_1p': np.random.uniform(-0.05, 0.05, n_rows),
        'body_size': np.random.uniform(0, 1000, n_rows),
        'close_pos_in_candle': np.random.uniform(0, 1, n_rows)
    })
    
    # 成交量特征
    data.update({
        'volume_vs_avg': np.random.uniform(0.5, 2.0, n_rows),
        'volume_change_1p': np.random.uniform(-0.5, 0.5, n_rows)
    })
    
    return pd.DataFrame(data)

def assert_dataframe_structure(df, expected_columns=None, min_rows=1):
    """
    验证DataFrame结构
    
    Args:
        df: 要验证的DataFrame
        expected_columns: 期望的列名列表
        min_rows: 最小行数
    """
    assert isinstance(df, pd.DataFrame), "输入必须是DataFrame"
    assert len(df) >= min_rows, f"DataFrame行数不足，期望至少{min_rows}行，实际{len(df)}行"
    
    if expected_columns:
        missing_columns = set(expected_columns) - set(df.columns)
        assert not missing_columns, f"缺少列: {missing_columns}"

def assert_feature_values(df, feature_name, value_range=None, allow_nan=True):
    """
    验证特征值
    
    Args:
        df: DataFrame
        feature_name: 特征名
        value_range: 值范围 (min, max)
        allow_nan: 是否允许NaN
    """
    assert feature_name in df.columns, f"特征 {feature_name} 不存在"
    
    values = df[feature_name]
    
    if not allow_nan:
        assert not values.isna().any(), f"特征 {feature_name} 包含NaN值"
    
    if value_range:
        valid_values = values.dropna()
        if len(valid_values) > 0:
            assert valid_values.min() >= value_range[0], f"特征 {feature_name} 最小值 {valid_values.min()} 小于期望 {value_range[0]}"
            assert valid_values.max() <= value_range[1], f"特征 {feature_name} 最大值 {valid_values.max()} 大于期望 {value_range[1]}"

def get_test_config(overrides=None):
    """
    获取测试配置
    
    Args:
        overrides: 覆盖的配置项
        
    Returns:
        测试配置字典
    """
    config = TEST_TARGET_CONFIG.copy()
    if overrides:
        config.update(overrides)
    return config

# 测试装饰器
def skip_if_missing_dependency(dependency_name):
    """如果缺少依赖则跳过测试的装饰器"""
    def decorator(test_func):
        def wrapper(*args, **kwargs):
            try:
                __import__(dependency_name)
                return test_func(*args, **kwargs)
            except ImportError:
                import unittest
                raise unittest.SkipTest(f"Missing dependency: {dependency_name}")
        return wrapper
    return decorator
