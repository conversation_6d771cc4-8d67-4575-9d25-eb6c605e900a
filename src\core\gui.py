# 内容来自 input_file_0.py (config.py)
import os
from dotenv import load_dotenv # 用于从 .env 文件加载环境变量 (如API密钥)
import pandas as pd           # 用于时间处理，例如 pd.Timedelta
import lightgbm as lgb        # LightGBM库，可能用于某些默认参数或类型提示

load_dotenv()
_prediction_labelframes = {} # 现在这里会存储 tk.Label 对象 (标题标签)


class ConfigModuleSimulator:
    def __init__(self):
        self.API_KEY = os.getenv("BINANCE_API_KEY", None)
        self.API_SECRET = os.getenv("BINANCE_API_SECRET", None)
        self.APScheduler_TIMEZONE = "Asia/Shanghai"
        self.SYMBOL = 'BTCUSDT'
        self.SCALER_TYPE = 'minmax'
        self.DATA_FETCH_LIMIT = 22222
        self.TRAIN_RATIO = 0.7
        self.VALIDATION_RATIO = 0.15
        self.SIMULATOR_INTEGRATION_ENABLED = True
        self.SIMULATOR_API_URL = "http://127.0.0.1:5008/signal"
        self.COMMAND_SERVER_URL = "http://127.0.0.1:8080/internal_signal"
        self.ALERT_SOUND_ENABLED = True
        self.CUSTOM_UP_SIGNAL_SOUND_PATH = "sounds/signal_up.mp3"
        self.CUSTOM_DOWN_SIGNAL_SOUND_PATH = "sounds/signal_down.mp3"
        self.CUSTOM_SIGNAL_SOUND_DURATION_MS = 20000
        self.BG_COLOR = "#2E2E2E"
        self.FG_COLOR = "#E0E0E0"
        self.LABEL_BG_COLOR = "#3C3C3C"
        self.BUTTON_COLOR = "#4A4A4A"
        self.BUTTON_FG_COLOR = "#FFFFFF"
        self.UP_COLOR = "#26A69A"
        self.DOWN_COLOR = "#EF5350"
        self.NEUTRAL_COLOR = "#FFCA28"
        self.TEXT_AREA_BG = "#1E1E1E"
        self.TEXT_AREA_FG = "#D0D0D0"
        self.ERROR_COLOR = "#FF5252"
        self.BLUE_COLOR_BUTTON = "#007BFF" # 在旧版GUI代码中有用到
        self.ENABLE_META_MODEL_TRAINING = True
        self.META_MODEL_SAVE_DIR = "meta_model_data"
        self.ENABLE_META_MODEL_PREDICTION = True
        self.META_MODEL_OOF_CV_FOLDS = 5
        self.META_MODEL_OOF_LGBM_N_ESTIMATORS = 3000
        self.META_MODEL_OOF_LGBM_EARLY_STOPPING_ROUNDS = 100
        self.META_MODEL_OOF_VALID_FOR_EARLY_STOP_RATIO = 0.15
        self.META_MODEL_OOF_MIN_SAMPLES_FOR_EARLY_STOP_VALID = 50
        self.META_MODEL_LGBM_OBJECTIVE = 'multiclass'
        self.META_MODEL_LGBM_NUM_CLASS = 3
        self.META_MODEL_LGBM_METRIC = 'multi_logloss'
        self.META_MODEL_LGBM_N_ESTIMATORS = 100
        self.META_MODEL_LGBM_LEARNING_RATE = 0.02
        self.META_MODEL_LGBM_NUM_LEAVES = 10
        self.META_MODEL_LGBM_MAX_DEPTH = 4
        self.META_MODEL_LGBM_REG_ALPHA = 2.0
        self.META_MODEL_LGBM_REG_LAMBDA = 2.0
        self.META_MODEL_LGBM_COLSAMPLE_BYTREE = 0.7
        self.META_MODEL_LGBM_SUBSAMPLE = 0.7
        self.META_MODEL_LGBM_MIN_CHILD_SAMPLES = 30
        self.META_MODEL_LGBM_RANDOM_STATE = 2024
        self.META_MODEL_LGBM_VERBOSE = 1
        self.META_MODEL_TRAIN_TEST_SPLIT_FOR_EVAL_RATIO = 0.2
        self.META_MODEL_LGBM_EARLY_STOPPING_ROUNDS_FINAL = 20
        self.META_PRE_FILTER_CONF_UP_THRESH = 0.22
        self.META_PRE_FILTER_CONF_DOWN_THRESH = 0.22
        self.BASE_MODELS_FOR_META = ["BTC_15m_UP", "BTC_15m_DOWN"]
        self.BASE_CONFIG_FOR_META_TARGET_DEFINITION = "BTC_15m_UP"
        self.META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS = "MetaModel_BTC_15m"
        self.META_MODEL_GUI_DISPLAY_NAME = "MetaSignal_BTC"
        self.META_MODEL_STATIC_KELLY_CONFIG = {
            "name": self.META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS,
            "trade_amount_strategy": "kelly_config",
            "kelly_config_params": {
                "payout_ratio_b": 0.85, "win_rate_p_estimate": 0.56,
                "virtual_total_capital_for_kelly": 50.0, "max_kelly_fraction_f": 0.08,
                "min_bet_kelly": 5.0, "max_bet_kelly": 250.0,
                "enable_initial_conservative_betting": True, "initial_conservative_trades_count": 25,
                "initial_conservative_bet_amount": 5.0, "min_bet_if_kelly_negative": 5.0, "enabled": True
            }
        }
        self.PREDICTION_TARGETS = [
            {
                "name": "BTC_15m_UP", "interval": "15m", "symbol": "BTCUSDT", "prediction_periods": [2],
                "prediction_minutes_display": 30, "model_save_dir": "trained_models_btc_15m_up",
                "target_variable_type": "UP_ONLY", "drop_neutral_targets": False, "device_type": 'gpu',
                "prediction_trigger_type": "kline_close", "enable_price_change": True,
                "PRICE_CHANGE_PERIODS": [1, 2, 3, 5, 10], "enable_volume": False, "VOLUME_AVG_PERIOD": 9,
                "enable_candle": True, "enable_ta": True, "HMA_PERIOD": 23, "KC_PERIOD": 29, "KC_ATR_PERIOD": 14,
                "KC_MULTIPLIER": 2.3352, "ATR_PERIOD": 16, "RSI_PERIOD": 22, "MACD_FAST": 17, "MACD_SLOW": 30,
                "MACD_SIGN": 11, "STOCH_K": 16,"stoch_d": 2,"STOCH_SMOOTH_K": 6, "enable_time": True, "enable_mtfa": True,
                "mtfa_timeframes": ['30m','1h', '4h'], "enable_fund_flow": True, "FUND_FLOW_RATIO_SMOOTHING_PERIOD": 15,
                "target_threshold": 0.002, "signal_threshold": 0.7, "rfe_enable": False,
                "importance_thresholding_enable": True, "importance_threshold_value": 22,
                "importance_top_n_features": 52, "importance_model_n_estimators": 200,
                "learning_rate_initial_imp": 0.05, "num_leaves_initial_imp": 15, "max_depth_initial_imp": 5,
                "reg_alpha_initial_imp": 5.0, "reg_lambda_initial_imp": 5.0,
                "colsample_bytree_initial_imp": 0.7132921738860881, "subsample_initial_imp": 0.7754745906891941,
                "min_child_samples_initial_imp": 30, "metric_initial_imp": 'binary_logloss', "optuna_enable": False,
                "optuna_n_trials": 100, "optuna_timeout": None, "optuna_metric": "average_precision",
                "optuna_direction": "maximize", "optuna_cv_folds": 3, "optuna_trial_n_estimators_max": 1500,
                "optuna_trial_early_stopping_rounds": 50, "optuna_trial_eval_metric": "binary_logloss",
                "objective": "binary", "metric": "binary_logloss", "boosting_type": "gbdt", "random_state": 42,
                "n_estimators": 5000, "early_stopping_rounds": 200, "verbose": 1, "verbosity": 1,
                "ensemble_runs": 1, "ensemble_cv_folds": 5, "learning_rate": 0.00767197942036955,
                "num_leaves": 20, "max_depth": 4, "reg_alpha": 7.0, "reg_lambda": 9.9,
                "colsample_bytree": 0.7132921738860881, "subsample": 0.7754745906891941,
                "min_child_samples": 52, "subsample_freq": 1, "enable_probability_calibration": True,
                "calibration_brier_improvement_threshold": 0.0001, "enable_shap_analysis": False,
                "enable_shap_for_live_prediction": False, "enable_trend_detection": True,
                "trend_detection_timeframe": "30m", "trend_indicator_type": 'adx', "trend_adx_period": 14,
                "trend_adx_strength_threshold": 30, "trend_adx_threshold": 20, "trend_ema_short_period": 20,
                "trend_ema_long_period": 50, "trend_filter_strategy": 'filter_only',
                "trend_chase_confidence_boost": 0.05, "enable_volatility_filter": True,
                "volatility_filter_timeframe": "30m", "volatility_atr_period": 14, "volatility_min_atr_percent": 0.1,
                "volatility_max_atr_percent": 1.2, "enable_dynamic_threshold": True, "dynamic_threshold_base": 0.7,
                "dynamic_threshold_trend_adjust": 0.05, "dynamic_threshold_volatility_adjust": 0.03,
                "meta_model_oof_cv_folds": 5, "meta_model_oof_n_estimators_large": 3000,
                "meta_model_oof_early_stopping_rounds": 100, "meta_model_oof_early_stop_eval_ratio": 0.15,
                "meta_model_oof_min_samples_for_eval": 50
            },
            { # BTC_15m_DOWN
                "name": "BTC_15m_DOWN", "interval": "15m", "symbol": "BTCUSDT", "prediction_periods": [2],
                "prediction_minutes_display": 30, "model_save_dir": "trained_models_btc_15m_down",
                "target_variable_type": "DOWN_ONLY", "drop_neutral_targets": False, "device_type": 'gpu',
                "prediction_trigger_type": "kline_close", "enable_price_change": True,
                "PRICE_CHANGE_PERIODS": [1, 2, 3, 5, 10], "enable_volume": False, "VOLUME_AVG_PERIOD": 9,
                "enable_candle": True, "enable_ta": True, "HMA_PERIOD": 23, "KC_PERIOD": 29, "KC_ATR_PERIOD": 14,
                "KC_MULTIPLIER": 2.3352, "ATR_PERIOD": 16, "RSI_PERIOD": 22, "MACD_FAST": 17, "MACD_SLOW": 30,
                "MACD_SIGN": 11, "STOCH_K": 16,"stoch_d": 2,"STOCH_SMOOTH_K": 6, "enable_time": True, "enable_mtfa": True,
                "mtfa_timeframes": ['30m','1h', '4h'], "enable_fund_flow": True, "FUND_FLOW_RATIO_SMOOTHING_PERIOD": 15,
                "target_threshold": 0.002, "signal_threshold": 0.7, "rfe_enable": False,
                "importance_thresholding_enable": True, "importance_threshold_value": 22,
                "importance_top_n_features": 52, "importance_model_n_estimators": 300,
                "learning_rate_initial_imp": 0.05, "num_leaves_initial_imp": 15, "max_depth_initial_imp": 5,
                "reg_alpha_initial_imp": 5.0, "reg_lambda_initial_imp": 5.0,
                "colsample_bytree_initial_imp": 0.7132921738860881, "subsample_initial_imp": 0.7754745906891941,
                "min_child_samples_initial_imp": 30, "metric_initial_imp": 'binary_logloss', "optuna_enable": False,
                "optuna_n_trials": 100, "optuna_timeout": None, "optuna_metric": "average_precision",
                "optuna_direction": "maximize", "optuna_cv_folds": 3, "optuna_trial_n_estimators_max": 1500,
                "optuna_trial_early_stopping_rounds": 50, "optuna_trial_eval_metric": "binary_logloss",
                "objective": "binary", "metric": "binary_logloss", "boosting_type": "gbdt", "random_state": 42,
                "n_estimators": 5000, "early_stopping_rounds": 200, "verbose": 1, "verbosity": 1,
                "ensemble_runs": 1, "ensemble_cv_folds": 5, "learning_rate": 0.00767197942036955,
                "num_leaves": 20, "max_depth": 4, "reg_alpha": 7.0, "reg_lambda": 9.9,
                "colsample_bytree": 0.7132921738860881, "subsample": 0.7754745906891941,
                "min_child_samples": 52, "subsample_freq": 1, "enable_probability_calibration": True,
                "calibration_brier_improvement_threshold": 0.0001, "enable_shap_analysis": False,
                "enable_shap_for_live_prediction": False, "enable_trend_detection": True,
                "trend_detection_timeframe": "30m", "trend_indicator_type": 'adx', "trend_adx_period": 14,
                "trend_adx_strength_threshold": 30, "trend_adx_threshold": 20,
                "trend_filter_strategy": 'filter_only', "trend_chase_confidence_boost": 0.05,
                "enable_volatility_filter": True, "volatility_filter_timeframe": "30m",
                "volatility_atr_period": 14, "volatility_min_atr_percent": 0.1, "volatility_max_atr_percent": 1.2,
                "enable_dynamic_threshold": True, "dynamic_threshold_base": 0.51,
                "dynamic_threshold_trend_adjust": 0.05, "dynamic_threshold_volatility_adjust": 0.03,
                "meta_model_oof_cv_folds": 5, "meta_model_oof_n_estimators_large": 3000,
                "meta_model_oof_early_stopping_rounds": 100, "meta_model_oof_early_stop_eval_ratio": 0.15,
                "meta_model_oof_min_samples_for_eval": 50
            },
            { 
                "name": self.META_MODEL_GUI_DISPLAY_NAME, "interval": "Meta-Analysis", "prediction_periods": [0],
                "prediction_minutes_display": "实时决策", "target_variable_type": "META_MODEL_DISPLAY",
                "prediction_trigger_type": "apscheduler_driven", "apscheduler_job_enabled": True,
                "apscheduler_trigger_type": "cron", "apscheduler_cron_config": {"minute": "*/25"}
            }
        ]
        self.ALL_TARGET_NAMES = [t['name'] for t in self.PREDICTION_TARGETS if isinstance(t, dict) and 'name' in t]
        self.COLOR_SCHEME = { # 新版GUI用的颜色方案
            'bg_primary': '#0B1426', 'bg_secondary': '#1A2332', 'bg_card': '#243447',
            'text_primary': '#E8F4FD', 'text_secondary': '#B8D4E3', 'accent_orange': '#FF6B35',
            'accent_blue': '#00D4FF', 'success_green': '#00E676', 'warning_amber': '#FFB300',
            'error_red': '#FF5252', 'border_light': '#3A4A5C', 'border_dark': '#2C3E50',
            'gradient_start': '#1E3A8A', 'gradient_end': '#3B82F6'
        }


    def get_target_config(self, target_name: str) -> dict:
        # 使用next(iter, default)形式避免StopIteration异常
        target_setting_from_list = next((t for t in self.PREDICTION_TARGETS if t.get('name') == target_name), None)
        if not target_setting_from_list:
            raise ValueError(f"在 PREDICTION_TARGETS 中未找到名为 '{target_name}' 的配置")
        final_config = {'symbol': self.SYMBOL, 'scaler_type': self.SCALER_TYPE}
        for key, value in target_setting_from_list.items():
            final_config[key.lower()] = value
        if 'device_type' in final_config:
            final_config['device_type'] = str(final_config['device_type']).lower()
        else:
            final_config['device_type'] = 'cpu'
        interval_str_for_td = final_config.get('interval')
        target_type_for_td_check = final_config.get('target_variable_type')
        if target_type_for_td_check == "META_MODEL_DISPLAY" or \
           interval_str_for_td in ["N/A", "Meta-Analysis", None]:
            final_config['interval_timedelta'] = None
        else:
            try:
                if not interval_str_for_td: raise ValueError("缺少 'interval'")
                final_config['interval_timedelta'] = pd.Timedelta(interval_str_for_td)
            except Exception as e:
                print(f"警告: 解析 '{target_name}' interval '{interval_str_for_td}' 失败: {e}。默认1h。")
                final_config['interval_timedelta'] = pd.Timedelta(hours=1)
        return final_config

config = ConfigModuleSimulator()

# 内容来自 input_file_2.py (gui.py) 并作修改
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
# import config # 已在上面模拟
import numpy as np
import traceback

# 导入新的 GUI 更新管理器
try:
    from .gui_update_manager import (
        get_gui_update_manager, initialize_gui_update_manager,
        queue_status_update, queue_prediction_update, queue_progress_update,
        queue_button_state_update, queue_price_update, queue_time_update,
        queue_meta_model_update, queue_training_metrics_update,
        UpdatePriority
    )
    GUI_UPDATE_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"警告: GUI更新管理器导入失败: {e}")
    GUI_UPDATE_MANAGER_AVAILABLE = False

# 导入 GUI 状态监听器
try:
    from .gui_state_listener import (
        get_gui_state_listener, initialize_gui_state_listener,
        notify_button_state_change, notify_price_update, notify_system_status_change
    )
    GUI_STATE_LISTENER_AVAILABLE = True
except ImportError as e:
    print(f"警告: GUI状态监听器导入失败: {e}")
    GUI_STATE_LISTENER_AVAILABLE = False

# 使用模拟的 config.COLOR_SCHEME
COLOR_SCHEME = config.COLOR_SCHEME

_gui_root = None
_prediction_text_widgets = {}
_status_label_widget = None
_time_label_widget = None
_price_label_widget = None
_train_button = None
_train_up_button = None
_train_down_button = None
_train_lstm_button = None
_train_meta_button = None
_predict_button = None
_reconnect_button = None
_prediction_labelframes = {}
_progress_bar = None
_meta_model_text_widget = None

def update_gui_safe(update_func, *args, **kwargs):
    """
    线程安全的 GUI 更新函数（兼容版本）

    优先使用新的 GUI 更新管理器，如果不可用则回退到传统方式
    """
    if _gui_root and _gui_root.winfo_exists():
        try:
            _gui_root.after(0, lambda: update_func(*args, **kwargs))
        except tk.TclError as e:
            if "application has been destroyed" not in str(e):
                print(f"GUI 更新 TclError: {e}")
        except Exception as e_other:
            print(f"GUI 更新时发生其他错误: {e_other}")

def update_status(message, level="neutral"):
    """
    更新状态信息（增强版本）

    优先使用新的 GUI 更新管理器进行批量更新优化
    """
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用新的更新管理器
        priority = UpdatePriority.HIGH if level == "error" else UpdatePriority.NORMAL
        queue_status_update(message, level, priority)
    else:
        # 回退到传统方式
        if _status_label_widget:
            color_map = {
                "error": COLOR_SCHEME['error_red'],
                "success": COLOR_SCHEME['success_green'],
                "warning": COLOR_SCHEME['warning_amber'],
                "neutral": COLOR_SCHEME['text_primary']
            }
            if level.startswith("#"): color = level
            else: color = color_map.get(level, COLOR_SCHEME['text_primary'])
            update_gui_safe(_status_label_widget.config, text=f"● {message}", fg=color)

def update_prediction_display(target_name, text, color=None):
    """
    更新预测显示（增强版本）

    优先使用新的 GUI 更新管理器，提供更好的性能和错误处理
    """
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用新的更新管理器
        queue_prediction_update(target_name, text, color, UpdatePriority.HIGH)
    else:
        # 回退到传统方式
        if color is None: color = COLOR_SCHEME['text_primary']
        widget = _prediction_text_widgets.get(target_name)
        if widget:
            def _update():
                try:
                    if widget.winfo_exists():
                        widget.config(state=tk.NORMAL); widget.delete('1.0', tk.END)
                        widget.insert(tk.END, text); widget.config(fg=color, state=tk.DISABLED)
                except tk.TclError: pass
                except Exception as e: print(f"更新预测显示 ({target_name}) 出错: {e}")
            update_gui_safe(_update)
        else: print(f"警告: 未找到目标 '{target_name}' 的预测部件。")

def update_evaluation_metrics(target_name, metrics_dict):
    widget = _prediction_text_widgets.get(target_name)
    if widget and metrics_dict:
        try:
            # 🚀 优化：支持集成模型评估指标显示
            if 'ensemble_type' in metrics_dict:
                # 新格式：集成模型评估指标
                metrics_text = f"\n\n--- {metrics_dict.get('ensemble_type', '集成模型')}评估 ---\n"

                # 基本指标
                acc_val = metrics_dict.get('accuracy', 'N/A')
                if isinstance(acc_val, (int, float)) and np.isfinite(acc_val):
                    metrics_text += f"整体准确率: {float(acc_val):.4f}\n"
                else:
                    metrics_text += f"整体准确率: {acc_val}\n"

                # F1分数、精确率、召回率
                for metric_key, metric_name in [('f1_score', 'F1分数'), ('precision', '精确率'), ('recall', '召回率')]:
                    val = metrics_dict.get(metric_key, 'N/A')
                    if isinstance(val, (int, float)) and np.isfinite(val):
                        metrics_text += f"{metric_name}: {float(val):.4f}\n"
                    else:
                        metrics_text += f"{metric_name}: {val}\n"

                # Brier分数
                brier_val = metrics_dict.get('brier_score', 'N/A')
                if isinstance(brier_val, (int, float)) and np.isfinite(brier_val):
                    metrics_text += f"Brier分数: {float(brier_val):.4f}\n"

                # 样本统计
                total_samples = metrics_dict.get('total_samples', 'N/A')
                positive_samples = metrics_dict.get('positive_samples', 'N/A')
                negative_samples = metrics_dict.get('negative_samples', 'N/A')
                metrics_text += f"\n样本统计:\n"
                metrics_text += f"  总样本数: {total_samples}\n"
                metrics_text += f"  正样本数: {positive_samples}\n"
                metrics_text += f"  负样本数: {negative_samples}\n"

                # 分类报告（如果有）
                classification_report = metrics_dict.get('classification_report')
                if isinstance(classification_report, dict):
                    metrics_text += f"\n详细分类报告:\n"
                    for class_key in ['下跌 (0)', '上涨 (1)']:
                        if class_key in classification_report:
                            class_metrics = classification_report[class_key]
                            metrics_text += f"\n{class_key}:\n"
                            for mk, mn in [('precision', '精确率'), ('recall', '召回率'), ('f1-score', 'F1分数')]:
                                val = class_metrics.get(mk, 'N/A')
                                if isinstance(val, (int, float)) and np.isfinite(val):
                                    metrics_text += f"  {mn}: {float(val):.4f}\n"
                                else:
                                    metrics_text += f"  {mn}: {val}\n"

                    # 平均指标
                    for avg_k, avg_n in [('macro avg', '宏平均'), ('weighted avg', '加权平均')]:
                        if avg_k in classification_report:
                            avg_metrics = classification_report[avg_k]
                            metrics_text += f"\n{avg_n}:\n"
                            for mk, mn in [('precision', '精确率'), ('recall', '召回率'), ('f1-score', 'F1分数')]:
                                val = avg_metrics.get(mk, 'N/A')
                                if isinstance(val, (int, float)) and np.isfinite(val):
                                    metrics_text += f"  {mn}: {float(val):.4f}\n"
                                else:
                                    metrics_text += f"  {mn}: {val}\n"

                # 错误信息（如果有）
                if 'evaluation_error' in metrics_dict:
                    metrics_text += f"\n⚠️ 评估警告: {metrics_dict['evaluation_error']}\n"

            else:
                # 原格式：传统分类报告格式
                metrics_text = "\n\n--- 测试集评估 ---\n"
                acc_val = metrics_dict.get('accuracy', 'N/A')
                if isinstance(acc_val, str) and acc_val in ['评估错误', '无测试数据', '未评估', 'N/A', '无有效评估']:
                    metrics_text += f"状态: {acc_val}\n"
                elif isinstance(acc_val, (int, float)) and np.isfinite(acc_val):
                    metrics_text += f"整体准确率: {float(acc_val):.4f}\n"
                else:
                    metrics_text += f"整体准确率: N/A\n"

                available_labels = sorted([k for k in metrics_dict if isinstance(k, str) and k.isdigit() or k in ['下跌 (0)', '上涨 (1)']])
                for label_key in available_labels:
                     label_name = f"{label_key}"
                     if label_key == '0': label_name = "下跌 (0)"
                     elif label_key == '1': label_name = "上涨 (1)"
                     metrics_text += f"\n{label_name}:\n"
                     class_metrics = metrics_dict.get(label_key)
                     if isinstance(class_metrics, dict):
                          for mk, mn in [('precision', '精确率(P)'), ('recall', '召回率(R)'), ('f1-score', 'F1分数')]:
                               val = class_metrics.get(mk, 'N/A')
                               if isinstance(val, (int, float)) and np.isfinite(val): metrics_text += f"  {mn}: {float(val):.4f}\n"
                               else: metrics_text += f"  {mn}: {val}\n"
                     else: metrics_text += f"  精确率(P): N/A\n  召回率(R): N/A\n  F1分数:   N/A\n"
                for avg_k, avg_n in [('macro avg', '宏平均'), ('weighted avg', '加权平均')]:
                     avg_metrics = metrics_dict.get(avg_k)
                     if isinstance(avg_metrics, dict):
                         metrics_text += f"\n{avg_n}:\n"
                         for mk, mn in [('precision', 'P'), ('recall', 'R'), ('f1-score', 'F1')]:
                              val = avg_metrics.get(mk, 'N/A')
                              if isinstance(val, (int, float)) and np.isfinite(val): metrics_text += f"  {mn}: {float(val):.4f}\n"
                              else: metrics_text += f"  {mn}: {val}\n"

            def _append_update():
                try:
                    if widget.winfo_exists():
                        widget.config(state=tk.NORMAL); widget.insert(tk.END, metrics_text)
                        widget.see(tk.END); widget.config(state=tk.DISABLED)
                except tk.TclError: pass
                except Exception as e: print(f"更新评估指标 ({target_name}) 出错: {e}")
            update_gui_safe(_append_update)
        except Exception as format_e: print(f"格式化评估指标 ({target_name}) 错: {format_e}"); traceback.print_exc()

def append_evaluation_metrics(target_name, metrics_dict):
    """
    追加评估指标到预测显示（与update_evaluation_metrics功能相同）

    这个函数是为了兼容GUI更新管理器的queue_training_metrics_update调用
    """
    update_evaluation_metrics(target_name, metrics_dict)

def update_time_label(time_str):
    """更新时间标签（优化版本）"""
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用优化的GUI更新管理器
        queue_time_update(time_str, UpdatePriority.LOW)
    else:
        # 回退到传统方式
        if _time_label_widget:
            update_gui_safe(_time_label_widget.config, text=f"🕒 {time_str}")

def update_price_label(price_str_full, color):
    """更新价格标签（优化版本）"""
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用优化的GUI更新管理器
        queue_price_update(price_str_full, color, UpdatePriority.NORMAL)
    else:
        # 回退到传统方式
        if _price_label_widget and _price_label_widget.winfo_exists():
            update_gui_safe(_price_label_widget.config, text=f"₿ {price_str_full}", fg=color)

def update_labelframe_text(target_name, new_text):
    """更新指定目标卡片的标题文本。"""
    title_label_widget = _prediction_labelframes.get(target_name) # 获取的是标题 tk.Label
    if title_label_widget:
        if hasattr(title_label_widget, 'config') and callable(title_label_widget.config):
            # 确保获取到的是一个可以配置text属性的组件
            update_gui_safe(title_label_widget.config, text=new_text)
        else:
            print(f"警告: 目标 '{target_name}' 的标题部件类型不正确，无法更新文本。类型为: {type(title_label_widget)}")
    else:
        print(f"警告: 未找到目标 '{target_name}' 的标题标签部件 (_prediction_labelframes)。")



def update_progress_bar(current_value, max_value):
    """传统的进度条更新函数（保持兼容性）"""
    if _progress_bar:
        try:
            current=max(0,current_value); maximum=max(1,max_value)
            percent=(current/maximum)*100 if maximum > 0 else 0
            if _progress_bar['mode']!='determinate': _progress_bar['mode']='determinate'
            _progress_bar['maximum']=100; _progress_bar['value']=percent
        except Exception as e: print(f"更新进度条错: {e}")

def update_progress(progress_percent):
    """
    更新进度条（增强版本）

    Args:
        progress_percent: 进度百分比 (0-100)
    """
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用新的更新管理器
        queue_progress_update(progress_percent, UpdatePriority.NORMAL)
    else:
        # 回退到传统方式
        if _progress_bar:
            try:
                if _progress_bar['mode']!='determinate': _progress_bar['mode']='determinate'
                _progress_bar['maximum']=100; _progress_bar['value']=max(0, min(100, progress_percent))
            except Exception as e: print(f"更新进度条错: {e}")

def set_progress_bar_indeterminate(start=True):
     if _progress_bar:
         try:
             if start: _progress_bar['mode']='indeterminate'; _progress_bar.start(10)
             else: _progress_bar.stop(); _progress_bar['mode']='determinate'; _progress_bar['value']=0
         except Exception as e: print(f"设置进度条模式错: {e}")

def reset_progress_bar():
    if _progress_bar:
        try: _progress_bar.stop(); _progress_bar['mode']='determinate'; _progress_bar['value']=0
        except tk.TclError: pass
        except Exception: pass

def set_button_state(button_name, state):
    """
    设置按钮状态，支持单个按钮和批量操作（增强版本）

    Args:
        button_name: 按钮名称或批量操作标识
        state: 按钮状态 (tk.NORMAL, tk.DISABLED)
    """
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用新的更新管理器
        queue_button_state_update(button_name, state, UpdatePriority.HIGH)
    else:
        # 回退到传统方式
        btn = None

        # 单个按钮映射
        button_mapping = {
            "train_all": _train_button,
            "train_up": _train_up_button,
            "train_down": _train_down_button,
            "train_lstm": _train_lstm_button,
            "train_meta": _train_meta_button,
            "predict": _predict_button,
            "train": _train_button  # 向后兼容
        }

        # 批量操作
        if button_name == "train_any":
            # 设置所有训练按钮状态
            training_buttons = [_train_button, _train_up_button, _train_down_button, _train_lstm_button, _train_meta_button]
            for btn in training_buttons:
                if btn:
                    try:
                        update_gui_safe(btn.config, state=state)
                    except Exception as e:
                        print(f"  警告: 设置训练按钮状态失败: {e}")
            return

        # 单个按钮操作
        btn = button_mapping.get(button_name)
        if btn:
            try:
                update_gui_safe(btn.config, state=state)
            except Exception as e:
                print(f"  警告: 设置按钮 '{button_name}' 状态失败: {e}")
        else:
            print(f"  警告: 未找到按钮 '{button_name}'")

def update_meta_model_display(text, color=None):
    """更新元模型显示（优化版本）"""
    # 减少调试输出，避免日志污染
    if GUI_UPDATE_MANAGER_AVAILABLE:
        # 使用优化的GUI更新管理器
        try:
            queue_meta_model_update(text, color, UpdatePriority.HIGH)
        except Exception as e:
            print(f"❌ [GUI] GUI更新管理器队列更新失败: {e}")
            # 回退到传统方式
            _update_meta_model_traditional(text, color)
    else:
        # 回退到传统方式
        _update_meta_model_traditional(text, color)

def _update_meta_model_traditional(text, color=None):
    """传统方式更新元模型显示"""
    if color is None:
        color = COLOR_SCHEME['text_primary']
    widget = _meta_model_text_widget

    if widget:
        def _update():
            try:
                if widget.winfo_exists():
                    widget.config(state=tk.NORMAL)
                    widget.delete('1.0', tk.END)
                    widget.insert(tk.END, text)
                    widget.config(fg=color, state=tk.DISABLED)
            except tk.TclError:
                pass  # 忽略Tkinter错误
            except Exception as e:
                print(f"❌ [GUI] 更新元模型显示出错: {e}")
        update_gui_safe(_update)

def build_gui(root, train_callback_all, train_callback_up, train_callback_down, train_lstm_callback, train_meta_callback, predict_callback, close_callback):
    global _gui_root, _prediction_text_widgets, _status_label_widget, \
           _time_label_widget, _price_label_widget, \
           _train_button, _train_up_button, _train_down_button, _train_lstm_button, _train_meta_button, _predict_button, _reconnect_button, \
           _prediction_labelframes, _progress_bar, _meta_model_text_widget
    _gui_root = root
    root.title("🚀 CryptoTrader Pro - 专业加密货币交易仪表盘")
    root.configure(bg=COLOR_SCHEME['bg_primary'])
    initial_height=870; initial_width=1200
    root.geometry(f"{initial_width}x{initial_height}")
    root.minsize(1000, 700)
    style = ttk.Style(); style.theme_use('clam')
    style.configure('Card.TFrame', background=COLOR_SCHEME['bg_card'], relief='flat', borderwidth=1)
    style.configure('Header.TLabel', background=COLOR_SCHEME['bg_primary'], foreground=COLOR_SCHEME['text_primary'], font=('Segoe UI',11,'bold'))
    style.configure('Modern.TButton', background=COLOR_SCHEME['accent_blue'], foreground='white', font=('Segoe UI',9,'bold'), borderwidth=0, focuscolor='none')
    style.map('Modern.TButton', background=[('active',COLOR_SCHEME['accent_orange']), ('pressed',COLOR_SCHEME['gradient_start'])])
    main_container = tk.Frame(root, bg=COLOR_SCHEME['bg_primary']); main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
    header_frame = tk.Frame(main_container, bg=COLOR_SCHEME['bg_secondary'], height=60); header_frame.pack(fill=tk.X, pady=(0,15)); header_frame.pack_propagate(False)
    title_label = tk.Label(header_frame, text="📊 CRYPTOCURRENCY TRADING DASHBOARD", bg=COLOR_SCHEME['bg_secondary'], fg=COLOR_SCHEME['accent_orange'], font=('Segoe UI',14,'bold')); title_label.pack(side=tk.LEFT, padx=20, pady=15)
    info_container = tk.Frame(header_frame, bg=COLOR_SCHEME['bg_secondary']); info_container.pack(side=tk.RIGHT, padx=20, pady=10)
    _time_label_widget = tk.Label(info_container, text="🕒 --:--:--", bg=COLOR_SCHEME['bg_card'], fg=COLOR_SCHEME['text_primary'], font=('Segoe UI',10,'bold'), padx=15, pady=8, relief='flat', borderwidth=1); _time_label_widget.pack(side=tk.LEFT, padx=(0,10))
    initial_symbol_for_label = getattr(config, 'SYMBOL', 'BTCUSDT').upper()
    initial_symbol_prefix = initial_symbol_for_label[:-4] if initial_symbol_for_label.endswith("USDT") else initial_symbol_for_label
    _price_label_widget = tk.Label(info_container, text=f"₿ {initial_symbol_prefix} $----.--", bg=COLOR_SCHEME['bg_card'], fg=COLOR_SCHEME['accent_blue'], font=('Segoe UI',11,'bold'), padx=15, pady=8, relief='flat', borderwidth=1); _price_label_widget.pack(side=tk.LEFT)
    content_frame = tk.Frame(main_container, bg=COLOR_SCHEME['bg_primary']); content_frame.pack(fill=tk.BOTH, expand=True)
    left_panel = tk.Frame(content_frame, bg=COLOR_SCHEME['bg_primary'], width=480); left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0,10)); left_panel.pack_propagate(False)
    if isinstance(config.PREDICTION_TARGETS, list):
        base_model_configs = [tc for tc in config.PREDICTION_TARGETS if tc.get('target_variable_type') != "META_MODEL_DISPLAY"]
        for i, target_cfg in enumerate(base_model_configs):
            target_name = target_cfg['name']; interval_display = target_cfg.get('interval','N/A')
            periods_list = target_cfg.get('prediction_periods',[1]); periods_display = periods_list[0] if periods_list and isinstance(periods_list,list) else 1
            target_type = target_cfg.get('target_variable_type','UNKNOWN')
            card_accent=COLOR_SCHEME['accent_blue']; type_icon="📊"; type_display="BOTH"
            if target_type=="UP_ONLY": card_accent=COLOR_SCHEME['success_green']; type_icon="📈"; type_display="LONG"
            elif target_type=="DOWN_ONLY": card_accent=COLOR_SCHEME['error_red']; type_icon="📉"; type_display="SHORT"
            card_frame = tk.Frame(left_panel, bg=COLOR_SCHEME['bg_card'], relief='solid', borderwidth=1, highlightbackground=card_accent, highlightthickness=2); card_frame.pack(fill=tk.X, pady=(0 if i==0 else 8,8), padx=5)
            title_f = tk.Frame(card_frame, bg=COLOR_SCHEME['bg_card']); title_f.pack(fill=tk.X, padx=15, pady=(12,8))
            title_text_card = f"{type_icon} {target_name}"; title_label_card = tk.Label(title_f, text=title_text_card, bg=COLOR_SCHEME['bg_card'], fg=COLOR_SCHEME['text_primary'], font=('Segoe UI',12,'bold')); title_label_card.pack(side=tk.LEFT)
            info_text = f"{type_display} | {interval_display} | {periods_display}P"; info_label_card = tk.Label(title_f, text=info_text, bg=COLOR_SCHEME['bg_card'], fg=card_accent, font=('Segoe UI',9,'bold')); info_label_card.pack(side=tk.RIGHT)
            text_widget = scrolledtext.ScrolledText(card_frame, wrap=tk.WORD, height=16, bg=COLOR_SCHEME['bg_primary'], fg=COLOR_SCHEME['text_secondary'], font=('Consolas',10), relief='flat', borderwidth=0, insertbackground=COLOR_SCHEME['accent_orange']); text_widget.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0,15))
            text_widget.insert(tk.END, f"⏳ 等待 {target_name} 模型预测...\n\n🔄 系统正在初始化"); text_widget.config(state=tk.DISABLED)
            _prediction_text_widgets[target_name]=text_widget; _prediction_labelframes[target_name]=title_label_card
    right_panel = tk.Frame(content_frame, bg=COLOR_SCHEME['bg_primary']); right_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10,0))
    # 使用next(iter, default)形式避免StopIteration异常
    meta_display_cfg = next((tc for tc in config.PREDICTION_TARGETS if tc.get('target_variable_type')=="META_MODEL_DISPLAY"), None)
    meta_title="🧠 AI META SIGNAL"; meta_cfg_name=None
    if meta_display_cfg:
        meta_cfg_name = meta_display_cfg.get('name'); meta_interval = meta_display_cfg.get('interval','综合')
        meta_minutes = meta_display_cfg.get('prediction_minutes_display','实时决策')
        if meta_cfg_name: meta_title = f"🧠 {meta_cfg_name} | {meta_interval} | {meta_minutes}"
    meta_card = tk.Frame(right_panel, bg=COLOR_SCHEME['bg_card'], relief='solid', borderwidth=1, highlightbackground=COLOR_SCHEME['accent_orange'], highlightthickness=3); meta_card.pack(fill=tk.BOTH, expand=True, pady=(0,8))
    meta_title_f = tk.Frame(meta_card, bg=COLOR_SCHEME['bg_card']); meta_title_f.pack(fill=tk.X, padx=20, pady=(15,10))
    meta_title_label_card = tk.Label(meta_title_f, text=meta_title, bg=COLOR_SCHEME['bg_card'], fg=COLOR_SCHEME['accent_orange'], font=('Segoe UI',14,'bold')); meta_title_label_card.pack(side=tk.LEFT)
    status_indicator = tk.Label(meta_title_f, text="🟢 ACTIVE", bg=COLOR_SCHEME['bg_card'], fg=COLOR_SCHEME['success_green'], font=('Segoe UI',10,'bold')); status_indicator.pack(side=tk.RIGHT)
    _meta_model_text_widget = scrolledtext.ScrolledText(meta_card, wrap=tk.WORD, bg=COLOR_SCHEME['bg_primary'], fg=COLOR_SCHEME['text_primary'], font=('Consolas',15), relief='flat', borderwidth=0, insertbackground=COLOR_SCHEME['accent_orange']); _meta_model_text_widget.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0,20))
    _meta_model_text_widget.insert(tk.END, "🤖 AI元模型正在分析市场数据...\n\n📊 整合多维度信号中\n⚡ 准备生成交易建议"); _meta_model_text_widget.config(state=tk.DISABLED)
    if meta_cfg_name: _prediction_text_widgets[meta_cfg_name]=_meta_model_text_widget; _prediction_labelframes[meta_cfg_name]=meta_title_label_card
    
    # --- 底部控制面板修改 ---
    control_panel = tk.Frame(main_container, bg=COLOR_SCHEME['bg_secondary']) # 移除了 height
    control_panel.pack(side=tk.BOTTOM, fill=tk.X, pady=(15, 0))
    # 移除了 control_panel.pack_propagate(False)
    
    button_container = tk.Frame(control_panel, bg=COLOR_SCHEME['bg_secondary']); button_container.pack(pady=10) # 调整了 pady
    # 添加重连回调函数参数（如果没有提供则使用默认函数）
    reconnect_callback = getattr(build_gui, '_reconnect_callback', lambda: print("重连功能未配置"))

    button_cfgs = [("🎯 训练所有模型",train_callback_all,COLOR_SCHEME['accent_blue']),("📈 训练上涨模型",train_callback_up,COLOR_SCHEME['success_green']),("📉 训练下跌模型",train_callback_down,COLOR_SCHEME['error_red']),("🤖 训练LSTM模型",train_lstm_callback,COLOR_SCHEME['gradient_end']),("🧠 训练元模型",train_meta_callback,COLOR_SCHEME['gradient_start']),("⚡ 立即预测",predict_callback,COLOR_SCHEME['accent_orange']),("🔄 重连价格数据",reconnect_callback,COLOR_SCHEME['gradient_start'])]
    buttons_list = []
    for i, (txt, cb, clr) in enumerate(button_cfgs):
        btn = tk.Button(button_container, text=txt, command=cb, bg=clr, fg='white', font=('Segoe UI',10,'bold'), relief='flat', borderwidth=0, padx=20, pady=12, cursor='hand2'); btn.pack(side=tk.LEFT, padx=8); buttons_list.append(btn)
        def on_enter(e, b=btn, oc=clr): b.config(bg=COLOR_SCHEME['accent_orange'] if oc!=COLOR_SCHEME['accent_orange'] else COLOR_SCHEME['accent_blue'])
        def on_leave(e, b=btn, oc=clr): b.config(bg=oc)
        btn.bind("<Enter>",on_enter); btn.bind("<Leave>",on_leave)
    _train_button, _train_up_button, _train_down_button, _train_lstm_button, _train_meta_button, _predict_button, _reconnect_button = buttons_list
    
    status_frame = tk.Frame(control_panel, bg=COLOR_SCHEME['bg_secondary']); status_frame.pack(fill=tk.X, padx=20, pady=(5,5)) # pady 调整为 (5,5) 或其他合适的值
    _status_label_widget = tk.Label(status_frame, text="● 系统就绪", bg=COLOR_SCHEME['bg_card'], fg=COLOR_SCHEME['text_primary'], font=('Segoe UI',10), anchor='w', padx=15, pady=8, relief='flat'); _status_label_widget.pack(fill=tk.X, pady=(0,5))
    style.configure('Modern.Horizontal.TProgressbar', background=COLOR_SCHEME['accent_orange'], troughcolor=COLOR_SCHEME['bg_card'], borderwidth=0, lightcolor=COLOR_SCHEME['accent_orange'], darkcolor=COLOR_SCHEME['accent_orange'])
    _progress_bar = ttk.Progressbar(status_frame, orient=tk.HORIZONTAL, mode='determinate', style='Modern.Horizontal.TProgressbar'); _progress_bar.pack(fill=tk.X, pady=(0,5)) # pady 调整
    root.protocol("WM_DELETE_WINDOW", close_callback)

    # 🎯 初始化增强的 GUI 更新管理器
    if GUI_UPDATE_MANAGER_AVAILABLE:
        try:
            import sys
            current_module = sys.modules[__name__]
            print(f"🔧 [GUI] 开始初始化GUI更新管理器...")
            print(f"🔧 [GUI] 当前模块: {current_module}")
            print(f"🔧 [GUI] GUI根窗口: {_gui_root}")
            print(f"🔧 [GUI] 元模型文本组件: {_meta_model_text_widget}")

            initialize_gui_update_manager(current_module, update_interval_ms=100)

            # 验证初始化结果
            from .gui_update_manager import get_gui_update_manager
            manager = get_gui_update_manager()
            print(f"🔧 [GUI] 管理器运行状态: {manager._is_running}")
            print(f"🔧 [GUI] 管理器GUI模块: {manager.gui_module is not None}")
            print(f"🔧 [GUI] 管理器GUI根窗口: {manager._gui_root is not None}")

            if manager._is_running:
                print("✅ GUI更新管理器已成功初始化并启动")
            else:
                print("⚠️ GUI更新管理器初始化但未启动")

        except Exception as e:
            print(f"⚠️ GUI更新管理器初始化失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("⚠️ GUI更新管理器不可用，使用传统更新方式")

    # 🎯 初始化 GUI 状态监听器
    if GUI_STATE_LISTENER_AVAILABLE:
        try:
            from .application_state import get_app_state
            app_state = get_app_state()
            import sys
            current_module = sys.modules[__name__]
            initialize_gui_state_listener(current_module, app_state)
            print("✅ GUI状态监听器已初始化")
        except Exception as e:
            print(f"⚠️ GUI状态监听器初始化失败: {e}")
    else:
        print("⚠️ GUI状态监听器不可用，使用传统更新方式")

# 模拟运行，以便观察效果 (在实际使用中应注释掉)
if __name__ == '__main__':
    root = tk.Tk()
    # 使用上面定义的 ConfigModuleSimulator 实例
    # config 变量已在全局定义
    build_gui(root,
              lambda: print("Train All callback"),
              lambda: print("Train Up callback"),
              lambda: print("Train Down callback"),
              lambda: print("Train Meta callback"),
              lambda: print("Predict callback"),
              lambda: root.quit())
    root.mainloop()
