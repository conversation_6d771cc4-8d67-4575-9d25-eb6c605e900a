# 🚀 统一综合日志系统使用指南

## 📋 概述

统一综合日志系统 V1.0 是一个高性能、多层次的日志记录解决方案，整合了原有的 `trading_logs_unified` 和 `analysis_logs` 系统的所有功能，并提供了更强大的分析能力。

## 🎯 核心特性

✅ **多层分类存储** - trades/contexts/analysis 三层数据管理  
✅ **异步高性能写入** - 继承原系统的异步机制，不阻塞主流程  
✅ **统一接口设计** - 一次调用完成多层记录  
✅ **智能数据路由** - 根据数据类型自动分发存储  
✅ **向后兼容** - 现有代码无需修改  
✅ **失败案例分析** - 内置深度分析功能  
✅ **数据迁移支持** - 自动迁移现有数据  

## 📁 文件结构

```
comprehensive_logs/
├── trades/           # 核心交易记录
│   └── 2025/07/trades_2025-07-15.csv
├── contexts/         # 预测上下文详细记录  
│   └── 2025/07/contexts_2025-07-15.csv
├── analysis/         # 失败案例和性能分析
│   ├── failure_patterns.csv
│   └── performance_summary.csv
└── unified_views/    # 统一查询视图（可选）
    └── complete_analysis.csv
```

## 🔧 快速开始

### 1. 基础使用

```python
from src.core.unified_comprehensive_logger import get_unified_comprehensive_logger

# 获取日志记录器
logger = get_unified_comprehensive_logger()

# 记录交易开仓
trade_id = logger.log_trade_opened(
    target_name="BTC_15m_UP",
    symbol="BTCUSDT",
    direction="LONG",
    entry_price=50000.0,
    amount=10.0,
    payout_ratio=0.85,
    context_data={
        'signal_probability': 0.85,
        'market_regime': 'uptrend',
        'atr_percent': 2.5
    }
)

# 记录交易平仓
success = logger.log_trade_closed(
    trade_id=trade_id,
    exit_price=51000.0,
    result="WIN",
    exit_reason="expired"
)
```

### 2. 预测上下文记录

```python
# 记录预测上下文
success = logger.log_prediction_context(
    target_name="BTC_15m_UP",
    symbol="BTCUSDT",
    signal_data={
        'signal_type': 'UP',
        'signal_strength': 0.85,
        'avg_up_prob': 0.75,
        'avg_down_prob': 0.25
    },
    market_data={
        'current_price': 50000.0,
        'last_kline_close': 49950.0
    },
    model_data={
        'model_type': 'LightGBM',
        'feature_names': ['rsi', 'macd', 'volume'],
        'feature_values': [65.5, 0.02, 1000000]
    },
    filter_data={
        'trend_signal': 1,
        'volatility_level': 2,
        'atr_percent': 2.5
    }
)
```

### 3. 完整交易记录

```python
# 一次性记录完整交易（开仓+平仓+上下文）
trade_data = {
    'trade_id': 'complete_trade_001',
    'entry_timestamp': datetime.now().isoformat(),
    'exit_timestamp': datetime.now().isoformat(),
    'target_name': 'BTC_15m_UP',
    'symbol': 'BTCUSDT',
    'direction': 'LONG',
    'entry_price': 50000.0,
    'exit_price': 51000.0,
    'amount': 10.0,
    'payout_ratio': 0.85,
    'result': 'WIN',
    'profit_loss': 8.5,
    'exit_reason': 'expired'
}

context_data = {
    'timestamp': datetime.now().isoformat(),
    'target_name': 'BTC_15m_UP',
    'symbol': 'BTCUSDT',
    'signal_type': 'UP',
    'signal_strength': 0.85
}

success = logger.log_complete_trade(trade_data, context_data)
```

## 🔄 兼容性支持

### 现有代码无需修改

原有的调用方式继续有效，系统会自动重定向到新的统一日志系统：

```python
# 原有的调用方式仍然有效
from src.core.unified_trade_logger import get_unified_trade_logger
from src.analysis.analysis_logger import log_prediction_context, log_trade_settlement

# 这些调用会自动重定向到新系统
logger = get_unified_trade_logger()
trade_id = logger.record_trade_entry(...)
success = logger.record_trade_exit(...)

log_prediction_context(...)
log_trade_settlement(...)
```

### 渐进式迁移

您可以选择渐进式迁移到新接口：

```python
# 新接口（推荐）
from src.core.unified_comprehensive_logger import get_unified_comprehensive_logger
logger = get_unified_comprehensive_logger()

# 或继续使用兼容接口
from src.core.logger_compatibility_layer import get_unified_trade_logger
logger = get_unified_trade_logger()
```

## 📊 数据分析功能

### 1. 失败案例分析

```python
# 分析最近7天的失败案例
analysis_result = logger.analyze_failures(days_back=7)

print(f"失败率: {analysis_result['basic_statistics']['failure_rate']:.2f}%")
print(f"总损失: {analysis_result['basic_statistics']['total_loss_amount']:.2f}")

# 查看改进建议
for i, recommendation in enumerate(analysis_result['recommendations'], 1):
    print(f"{i}. {recommendation}")
```

### 2. 性能汇总

```python
# 获取性能汇总
performance = logger.get_performance_summary(period_days=7)
print(f"分析结果: {performance}")
```

### 3. 数据加载

```python
# 加载交易数据
trades_df = logger.load_trade_logs(
    start_date="2025-07-01",
    end_date="2025-07-15"
)

# 加载预测上下文数据
contexts_df = logger.load_context_logs(
    start_date="2025-07-01", 
    end_date="2025-07-15"
)
```

## 🔄 数据迁移

### 自动迁移现有数据

```bash
# 运行迁移脚本
python migrate_logs.py

# 试运行（不实际迁移）
python migrate_logs.py --dry-run

# 不备份原数据
python migrate_logs.py --no-backup

# 仅验证现有迁移
python migrate_logs.py --verify-only
```

### 手动迁移

```python
from src.core.data_migration_manager import DataMigrationManager

# 创建迁移管理器
migration_manager = DataMigrationManager(
    old_trading_logs_dir="trading_logs_unified",
    old_analysis_logs_dir="analysis_logs",
    new_comprehensive_logs_dir="comprehensive_logs"
)

# 执行迁移
result = migration_manager.migrate_all_data(
    backup_old_data=True,
    dry_run=False
)

# 验证迁移结果
verification = migration_manager.verify_migration()
print(f"迁移状态: {verification['overall_status']}")
```

## ⚙️ 配置选项

### 基础配置

```python
logger = get_unified_comprehensive_logger(
    base_log_dir="comprehensive_logs",  # 日志基础目录
    queue_maxsize=1000,                 # 队列最大大小
    batch_size=10,                      # 批量写入大小
    flush_interval=1.0,                 # 刷新间隔（秒）
    auto_start=True                     # 是否自动启动
)
```

### 高级配置

```python
# 获取存储统计信息
storage_stats = logger.storage_manager.get_storage_statistics()
print(f"存储统计: {storage_stats}")

# 清理旧文件（保留30天）
cleaned_count = logger.storage_manager.cleanup_old_files(days_to_keep=30)
print(f"清理了 {cleaned_count} 个旧文件")

# 强制刷新所有队列
logger.flush_all_queues()

# 获取系统统计信息
stats = logger.get_statistics()
print(f"系统统计: {stats}")
```

## 🧪 测试

### 运行测试用例

```bash
# 运行所有测试
python -m pytest tests/test_unified_comprehensive_logger.py -v

# 运行特定测试
python -m pytest tests/test_unified_comprehensive_logger.py::TestUnifiedComprehensiveLogger::test_basic_trade_logging -v
```

### 手动测试

```python
# 创建测试数据
logger = get_unified_comprehensive_logger()

# 测试交易记录
trade_id = logger.log_trade_opened(
    target_name="TestStrategy",
    symbol="BTCUSDT", 
    direction="LONG",
    entry_price=50000.0,
    amount=10.0
)

success = logger.log_trade_closed(
    trade_id=trade_id,
    exit_price=51000.0,
    result="WIN"
)

print(f"测试结果: {success}")
```

## 🚨 注意事项

### 1. 性能考虑

- 系统使用异步写入，不会阻塞主流程
- 建议在程序结束前调用 `logger.stop()` 确保数据完整写入
- 大量数据写入时，可以调整 `batch_size` 和 `flush_interval` 参数

### 2. 数据完整性

- 系统会自动处理线程安全问题
- 异常情况下可能有少量数据丢失，建议监控 `failed_writes` 统计
- 重要数据建议使用 `log_complete_trade` 一次性记录

### 3. 存储管理

- 系统会自动创建目录结构
- 建议定期清理旧文件以节省存储空间
- 可以通过 `get_storage_statistics()` 监控存储使用情况

## 🔧 故障排除

### 常见问题

1. **导入错误**
   ```python
   # 确保项目路径正确
   import sys
   sys.path.append('/path/to/your/project')
   ```

2. **权限问题**
   ```bash
   # 确保日志目录有写权限
   chmod 755 comprehensive_logs/
   ```

3. **内存使用过高**
   ```python
   # 减少队列大小和批次大小
   logger = get_unified_comprehensive_logger(
       queue_maxsize=100,
       batch_size=5
   )
   ```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
logger = get_unified_comprehensive_logger()
```

## 📞 支持

如有问题或建议，请：

1. 查看测试用例了解使用方法
2. 检查日志输出获取错误信息  
3. 使用 `--dry-run` 模式测试迁移
4. 查看 `migration.log` 了解迁移详情

---

**🎉 恭喜！您已经掌握了统一综合日志系统的使用方法。现在可以享受更强大、更统一的日志记录体验了！**
