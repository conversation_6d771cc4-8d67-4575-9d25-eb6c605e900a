#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 启动时配置记录器 - 系统启动时记录完整配置状态

提供启动时配置参数的完整记录功能，包括：
- PREDICTION_TARGETS配置详情
- 模型配置参数
- 元模型参数设置
- 交易信号参数
- 系统环境信息
"""

import os
import sys
import platform
from datetime import datetime
from typing import Dict, Any, List
from ..core.error_handler import get_enhanced_logger


class StartupConfigLogger:
    """启动时配置记录器"""
    
    def __init__(self):
        self.enhanced_logger = get_enhanced_logger(__name__)
        
    def log_startup_configuration(self, config_module=None):
        """
        记录应用启动时的完整配置
        
        Args:
            config_module: 配置模块（通常是config.py）
        """
        self.enhanced_logger.info(
            "=== 应用启动配置记录开始 ===",
            timestamp=datetime.now().isoformat(),
            python_version=platform.python_version(),
            platform_info=platform.platform(),
            working_directory=os.getcwd()
        )
        
        if config_module is None:
            self.enhanced_logger.warning("配置模块未提供，跳过配置记录")
            return
            
        # 记录系统环境信息
        self._log_system_environment()
        
        # 记录PREDICTION_TARGETS配置
        self._log_prediction_targets(config_module)
        
        # 记录模型配置
        self._log_model_configuration(config_module)
        
        # 记录元模型配置
        self._log_meta_model_configuration(config_module)
        
        # 记录交易信号配置
        self._log_trading_signal_configuration(config_module)
        
        # 记录日志配置
        self._log_logging_configuration(config_module)
        
        # 记录API配置（脱敏）
        self._log_api_configuration(config_module)
        
        self.enhanced_logger.info("=== 应用启动配置记录完成 ===")
    
    def _log_system_environment(self):
        """记录系统环境信息"""
        try:
            import psutil
            memory_info = psutil.virtual_memory()
            cpu_count = psutil.cpu_count()
        except ImportError:
            memory_info = None
            cpu_count = None
        
        self.enhanced_logger.info(
            "系统环境信息",
            python_executable=sys.executable,
            python_path=sys.path[:3],  # 只记录前3个路径
            cpu_count=cpu_count,
            total_memory_gb=round(memory_info.total / (1024**3), 2) if memory_info else "未知",
            available_memory_gb=round(memory_info.available / (1024**3), 2) if memory_info else "未知"
        )
    
    def _log_prediction_targets(self, config_module):
        """记录PREDICTION_TARGETS配置"""
        if not hasattr(config_module, 'PREDICTION_TARGETS'):
            self.enhanced_logger.warning("配置中未找到PREDICTION_TARGETS")
            return
            
        targets = config_module.PREDICTION_TARGETS
        self.enhanced_logger.info(
            "预测目标配置总览",
            total_targets=len(targets),
            target_names=[target.get('name', 'unnamed') for target in targets]
        )
        
        for i, target in enumerate(targets):
            target_name = target.get('name', f'target_{i}')
            
            # 记录基本信息
            self.enhanced_logger.info(
                f"预测目标详情 - {target_name}",
                target_name=target_name,
                interval=target.get('interval', 'N/A'),
                symbol=target.get('symbol', 'N/A'),
                target_variable_type=target.get('target_variable_type', 'N/A'),
                prediction_periods=target.get('prediction_periods', 'N/A'),
                prediction_minutes_display=target.get('prediction_minutes_display', 'N/A')
            )
            
            # 记录标签方法配置
            labeling_config = {
                'labeling_method': target.get('labeling_method', 'N/A'),
                'profit_threshold': target.get('profit_threshold', 'N/A'),
                'loss_threshold': target.get('loss_threshold', 'N/A'),
                'atr_multiplier_profit': target.get('atr_multiplier_profit', 'N/A'),
                'atr_multiplier_loss': target.get('atr_multiplier_loss', 'N/A')
            }
            
            self.enhanced_logger.info(
                f"标签配置 - {target_name}",
                **labeling_config
            )
            
            # 记录模型配置
            model_config = {
                'class_weight': target.get('class_weight', 'N/A'),
                'enable_feature_selection': target.get('enable_feature_selection', 'N/A'),
                'enable_optuna_optimization': target.get('enable_optuna_optimization', 'N/A'),
                'device_type': target.get('device_type', 'N/A')
            }
            
            self.enhanced_logger.info(
                f"模型配置 - {target_name}",
                **model_config
            )
    
    def _log_model_configuration(self, config_module):
        """记录模型配置"""
        model_configs = {}
        
        # 收集模型相关配置
        model_attrs = [
            'ENABLE_FEATURE_SELECTION', 'ENABLE_OPTUNA_OPTIMIZATION',
            'OPTUNA_N_TRIALS', 'OPTUNA_TIMEOUT_SECONDS',
            'LGBM_N_ESTIMATORS', 'LGBM_LEARNING_RATE',
            'LGBM_MAX_DEPTH', 'LGBM_NUM_LEAVES'
        ]
        
        for attr in model_attrs:
            if hasattr(config_module, attr):
                model_configs[attr.lower()] = getattr(config_module, attr)
        
        if model_configs:
            self.enhanced_logger.info(
                "全局模型配置",
                **model_configs
            )
    
    def _log_meta_model_configuration(self, config_module):
        """记录元模型配置"""
        meta_configs = {}
        
        # 收集元模型相关配置
        meta_attrs = [
            'ENABLE_META_MODEL_PREDICTION', 'BASE_MODELS_FOR_META',
            'META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS',
            'META_MODEL_GUI_DISPLAY_NAME'
        ]
        
        for attr in meta_attrs:
            if hasattr(config_module, attr):
                value = getattr(config_module, attr)
                # 对于列表类型，记录长度和前几个元素
                if isinstance(value, list):
                    meta_configs[f"{attr.lower()}_count"] = len(value)
                    meta_configs[f"{attr.lower()}_items"] = value[:3] if len(value) > 3 else value
                else:
                    meta_configs[attr.lower()] = value
        
        if meta_configs:
            self.enhanced_logger.info(
                "元模型配置",
                **meta_configs
            )
    
    def _log_trading_signal_configuration(self, config_module):
        """记录交易信号配置"""
        signal_configs = {}
        
        # 收集交易信号相关配置
        signal_attrs = [
            'AUTO_PREDICTION_ENABLED', 'AUTO_PREDICTION_INTERVAL_MINUTES',
            'AUTO_PREDICTION_TRIGGER_TYPE', 'SIMULATOR_API_URL',
            'ENABLE_SIGNAL_SENDING', 'SIGNAL_CONFIDENCE_THRESHOLD'
        ]
        
        for attr in signal_attrs:
            if hasattr(config_module, attr):
                signal_configs[attr.lower()] = getattr(config_module, attr)
        
        if signal_configs:
            self.enhanced_logger.info(
                "交易信号配置",
                **signal_configs
            )
    
    def _log_logging_configuration(self, config_module):
        """记录日志配置"""
        if hasattr(config_module, 'GLOBAL_DEFAULTS') and 'logging' in config_module.GLOBAL_DEFAULTS:
            logging_config = config_module.GLOBAL_DEFAULTS['logging']
            
            self.enhanced_logger.info(
                "日志系统配置",
                log_level=logging_config.get('log_level', 'N/A'),
                log_file_name=logging_config.get('log_file_name', 'N/A'),
                enable_performance_logging=logging_config.get('enable_performance_logging', 'N/A'),
                enable_stack_trace=logging_config.get('enable_stack_trace', 'N/A'),
                max_log_file_size=logging_config.get('max_log_file_size', 'N/A'),
                log_backup_count=logging_config.get('log_backup_count', 'N/A')
            )
    
    def _log_api_configuration(self, config_module):
        """记录API配置（脱敏处理）"""
        api_configs = {}
        
        # API相关配置（脱敏）
        if hasattr(config_module, 'API_KEY'):
            api_key = getattr(config_module, 'API_KEY')
            api_configs['api_key_configured'] = api_key is not None and len(str(api_key)) > 0
            if api_key:
                api_configs['api_key_length'] = len(str(api_key))
                api_configs['api_key_prefix'] = str(api_key)[:4] + "****"
        
        if hasattr(config_module, 'API_SECRET'):
            api_secret = getattr(config_module, 'API_SECRET')
            api_configs['api_secret_configured'] = api_secret is not None and len(str(api_secret)) > 0
        
        # 其他API配置
        api_attrs = ['APScheduler_TIMEZONE', 'PROXY_URL']
        for attr in api_attrs:
            if hasattr(config_module, attr):
                value = getattr(config_module, attr)
                api_configs[attr.lower()] = "已配置" if value else "未配置"
        
        if api_configs:
            self.enhanced_logger.info(
                "API配置状态（脱敏）",
                **api_configs
            )


# 全局实例
_startup_config_logger = None

def get_startup_config_logger() -> StartupConfigLogger:
    """获取启动配置记录器实例（单例模式）"""
    global _startup_config_logger
    if _startup_config_logger is None:
        _startup_config_logger = StartupConfigLogger()
    return _startup_config_logger

def log_startup_configuration(config_module=None):
    """便捷函数：记录启动配置"""
    logger = get_startup_config_logger()
    logger.log_startup_configuration(config_module)
