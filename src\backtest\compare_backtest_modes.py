#!/usr/bin/env python3
# compare_backtest_modes.py
"""
回测模式对比分析脚本
对比真实交易情况回测 vs 理想化回测的差异
"""

import sys
import os
import logging
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志记录器
logger = logging.getLogger(__name__)
if not logger.handlers:
    logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

try:
    from realistic_backtest_main import RealisticBinaryOptionBacktester
    from backtest_main import BinaryOptionBacktester
    import backtest_config as bt_config
    from backtest_utils import calculate_performance_metrics
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有回测相关文件都在正确的位置")
    sys.exit(1)

class BacktestComparator:
    """回测模式对比器"""
    
    def __init__(self, config_dict: dict = None):
        self.config = config_dict or bt_config.get_backtest_config()
        self.results = {}
    
    def run_realistic_backtest(self) -> dict:
        """运行真实交易情况回测"""
        print("=== 运行真实交易情况回测 ===")
        
        realistic_backtester = RealisticBinaryOptionBacktester(self.config)
        
        # 加载模型
        if not realistic_backtester.load_models():
            print("错误: 真实交易回测模型加载失败")
            return {}
        
        # 加载数据
        if not realistic_backtester.load_historical_data():
            print("错误: 真实交易回测数据加载失败")
            return {}
        
        # 执行回测
        if not realistic_backtester.run_realistic_backtest():
            print("错误: 真实交易回测执行失败")
            return {}
        
        # 分析结果
        metrics, trades_df, daily_stats = realistic_backtester.analyze_results()
        
        return {
            'type': 'realistic',
            'engine': realistic_backtester.engine,
            'metrics': metrics,
            'trades_df': trades_df,
            'daily_stats': daily_stats
        }
    
    def run_idealized_backtest(self) -> dict:
        """运行理想化回测"""
        print("=== 运行理想化回测 ===")
        
        idealized_backtester = BinaryOptionBacktester(self.config)
        
        # 运行完整回测
        if not idealized_backtester.run_full_backtest():
            print("错误: 理想化回测执行失败")
            return {}
        
        # 分析结果
        metrics, trades_df, daily_stats = idealized_backtester.analyze_results()
        
        return {
            'type': 'idealized',
            'engine': idealized_backtester.engine,
            'metrics': metrics,
            'trades_df': trades_df,
            'daily_stats': daily_stats
        }
    
    def compare_results(self, realistic_result: dict, idealized_result: dict) -> dict:
        """对比两种回测结果"""
        print("=== 对比分析结果 ===")
        
        if not realistic_result or not idealized_result:
            print("错误: 缺少回测结果，无法进行对比")
            return {}
        
        realistic_metrics = realistic_result['metrics']
        idealized_metrics = idealized_result['metrics']
        
        comparison = {
            'realistic': realistic_metrics,
            'idealized': idealized_metrics,
            'differences': {}
        }
        
        # 计算关键指标差异
        key_metrics = [
            'total_trades', 'win_rate', 'total_return', 'max_drawdown',
            'sharpe_ratio', 'profit_factor'
        ]
        
        for metric in key_metrics:
            realistic_val = realistic_metrics.get(metric, 0)
            idealized_val = idealized_metrics.get(metric, 0)
            
            if idealized_val != 0:
                diff_pct = (realistic_val - idealized_val) / idealized_val * 100
            else:
                diff_pct = 0
            
            comparison['differences'][metric] = {
                'realistic': realistic_val,
                'idealized': idealized_val,
                'difference': realistic_val - idealized_val,
                'difference_pct': diff_pct
            }
        
        return comparison
    
    def generate_comparison_report(self, comparison: dict) -> str:
        """生成对比报告"""
        if not comparison:
            return "无法生成对比报告：缺少对比数据"
        
        report = "=== 回测模式对比报告 ===\n\n"
        
        # 基本信息
        realistic_metrics = comparison['realistic']
        idealized_metrics = comparison['idealized']
        
        report += "1. 基本统计对比:\n"
        report += f"{'指标':<15} {'真实交易':<12} {'理想化':<12} {'差异':<12} {'差异%':<10}\n"
        report += "-" * 70 + "\n"
        
        for metric, data in comparison['differences'].items():
            metric_name = {
                'total_trades': '总交易数',
                'win_rate': '胜率',
                'total_return': '总收益率',
                'max_drawdown': '最大回撤',
                'sharpe_ratio': '夏普比率',
                'profit_factor': '盈利因子'
            }.get(metric, metric)
            
            realistic_val = data['realistic']
            idealized_val = data['idealized']
            diff = data['difference']
            diff_pct = data['difference_pct']
            
            if metric in ['win_rate', 'total_return', 'max_drawdown']:
                realistic_str = f"{realistic_val:.1%}"
                idealized_str = f"{idealized_val:.1%}"
                diff_str = f"{diff:+.1%}"
            elif metric in ['sharpe_ratio', 'profit_factor']:
                realistic_str = f"{realistic_val:.2f}"
                idealized_str = f"{idealized_val:.2f}"
                diff_str = f"{diff:+.2f}"
            else:
                realistic_str = f"{realistic_val:.0f}"
                idealized_str = f"{idealized_val:.0f}"
                diff_str = f"{diff:+.0f}"
            
            diff_pct_str = f"{diff_pct:+.1f}%"
            
            report += f"{metric_name:<15} {realistic_str:<12} {idealized_str:<12} {diff_str:<12} {diff_pct_str:<10}\n"
        
        # 真实交易特有信息
        if 'kline_triggers' in realistic_metrics:
            kline_stats = realistic_metrics['kline_triggers']
            report += f"\n2. 真实交易特有统计:\n"
            report += f"   - 1分钟K线触发: {kline_stats.get('1m', 0)} 次\n"
            report += f"   - 15分钟K线触发: {kline_stats.get('15m', 0)} 次\n"
            report += f"   - 总信号数: {kline_stats.get('total_signals', 0)}\n"
            report += f"   - 实际执行交易: {kline_stats.get('executed_trades', 0)}\n"
            report += f"   - 凯利效率: {realistic_metrics.get('kelly_efficiency', 0):.1%}\n"
            
            if 'avg_trade_amount' in realistic_metrics:
                report += f"   - 平均交易金额: ${realistic_metrics['avg_trade_amount']:.2f}\n"
                report += f"   - 最大交易金额: ${realistic_metrics['max_trade_amount']:.2f}\n"
                report += f"   - 最小交易金额: ${realistic_metrics['min_trade_amount']:.2f}\n"
        
        # 分析结论
        report += f"\n3. 分析结论:\n"
        
        total_return_diff = comparison['differences']['total_return']['difference_pct']
        if total_return_diff > 10:
            report += "   ⚠️  真实交易回测收益率显著高于理想化回测，可能存在过拟合风险\n"
        elif total_return_diff < -20:
            report += "   ⚠️  真实交易回测收益率显著低于理想化回测，符合实际交易限制\n"
        else:
            report += "   ✅ 真实交易回测与理想化回测结果相对接近\n"
        
        trades_diff = comparison['differences']['total_trades']['difference_pct']
        if trades_diff < -30:
            report += "   📊 真实交易模式下交易次数显著减少，体现了凯利公式的风险控制\n"
        
        win_rate_diff = comparison['differences']['win_rate']['difference_pct']
        if abs(win_rate_diff) > 5:
            report += f"   📈 胜率差异较大 ({win_rate_diff:+.1f}%)，需要检查信号生成逻辑\n"
        
        return report
    
    def save_comparison_results(self, comparison: dict, report: str) -> None:
        """保存对比结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(self.config['output_dir'], "comparison")
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存对比数据
        comparison_file = os.path.join(output_dir, f"backtest_comparison_{timestamp}.json")
        import json
        with open(comparison_file, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型
            def convert_types(obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return obj
            
            # 递归转换
            def recursive_convert(data):
                if isinstance(data, dict):
                    return {k: recursive_convert(v) for k, v in data.items()}
                elif isinstance(data, list):
                    return [recursive_convert(item) for item in data]
                else:
                    return convert_types(data)
            
            converted_comparison = recursive_convert(comparison)
            json.dump(converted_comparison, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存对比报告
        report_file = os.path.join(output_dir, f"comparison_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"对比结果已保存:")
        print(f"  - 数据文件: {comparison_file}")
        print(f"  - 报告文件: {report_file}")
    
    def run_full_comparison(self) -> bool:
        """运行完整的对比分析"""
        print("=== 回测模式对比分析开始 ===")
        print(f"交易对: {self.config['symbol']}")
        print(f"时间范围: {self.config['start_date']} 到 {self.config['end_date']}")
        print(f"初始资金: ${self.config['initial_balance']}")
        print()
        
        try:
            # 运行真实交易回测
            realistic_result = self.run_realistic_backtest()
            if not realistic_result:
                return False
            
            # 运行理想化回测
            idealized_result = self.run_idealized_backtest()
            if not idealized_result:
                return False
            
            # 对比分析
            comparison = self.compare_results(realistic_result, idealized_result)
            if not comparison:
                return False
            
            # 生成报告
            report = self.generate_comparison_report(comparison)
            print(report)
            
            # 保存结果
            self.save_comparison_results(comparison, report)
            
            print("\n=== 回测模式对比分析完成 ===")
            return True
            
        except Exception as e:
            logger.error(f"对比分析过程中发生错误: {e}")
            logger.exception("对比分析错误的详细堆栈跟踪:")
            print(f"对比分析过程中发生错误: {e}")
            return False

def main():
    """主函数"""
    print("回测模式对比分析工具")
    print("=" * 50)
    
    try:
        # 创建对比器
        comparator = BacktestComparator()
        
        # 运行对比分析
        success = comparator.run_full_comparison()
        
        if success:
            print("\n🎉 回测模式对比分析成功完成!")
            return 0
        else:
            print("回测模式对比分析失败!")
            return 1
            
    except KeyboardInterrupt:
        print("\n对比分析被用户中断")
        return 1
    except Exception as e:
        logger.critical(f"对比分析过程中发生未知错误: {e}")
        logger.exception("对比分析未知错误的详细堆栈跟踪:")
        print(f"对比分析过程中发生未知错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
