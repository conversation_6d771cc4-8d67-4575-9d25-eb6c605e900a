#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 统一综合日志系统 V1.0
整合 trading_logs_unified 和 analysis_logs 为一个高性能的综合日志系统

核心特性：
- 多层分类存储：trades/contexts/analysis 分层管理
- 异步高性能写入：继承 trading_logs_unified 的异步机制
- 统一接口设计：一次调用完成多层记录
- 智能数据路由：根据数据类型自动分发存储
- 向后兼容：保持现有调用方式不变
- 内存优化：避免重复数据存储
"""

import os
import json
import time
import queue
import threading
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union
import pandas as pd
from pathlib import Path


class MultiLayerStorageManager:
    """多层存储管理器 - 管理三层数据存储结构"""
    
    def __init__(self, base_log_dir: str = "comprehensive_logs"):
        self.base_log_dir = Path(base_log_dir)
        self.logger = logging.getLogger(f"{__name__}.MultiLayerStorageManager")
        
        # 三层存储路径
        self.trades_dir = self.base_log_dir / "trades"
        self.contexts_dir = self.base_log_dir / "contexts" 
        self.analysis_dir = self.base_log_dir / "analysis"
        self.unified_views_dir = self.base_log_dir / "unified_views"
        
        # 创建目录结构
        self._create_directory_structure()
        
        # 文件锁
        self.file_locks = {
            'trades': threading.Lock(),
            'contexts': threading.Lock(),
            'analysis': threading.Lock(),
            'unified_views': threading.Lock()
        }
    
    def _create_directory_structure(self):
        """创建完整的目录结构"""
        for dir_path in [self.trades_dir, self.contexts_dir, self.analysis_dir, self.unified_views_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"多层存储目录结构已创建: {self.base_log_dir}")
    
    def get_file_path(self, layer: str, file_type: str = "daily", date: Optional[datetime] = None) -> Path:
        """
        获取指定层级的文件路径
        
        Args:
            layer: 存储层级 ('trades', 'contexts', 'analysis', 'unified_views')
            file_type: 文件类型 ('daily', 'summary', 'failure_patterns', 'performance')
            date: 日期（用于daily文件）
        
        Returns:
            文件路径
        """
        if date is None:
            date = datetime.now()
        
        base_dir = getattr(self, f"{layer}_dir")
        
        if file_type == "daily":
            # 按年/月分层的日期文件
            year_month_dir = base_dir / str(date.year) / f"{date.month:02d}"
            year_month_dir.mkdir(parents=True, exist_ok=True)
            
            if layer == "trades":
                filename = f"trades_{date.strftime('%Y-%m-%d')}.csv"
            elif layer == "contexts":
                filename = f"contexts_{date.strftime('%Y-%m-%d')}.csv"
            else:
                filename = f"{layer}_{date.strftime('%Y-%m-%d')}.csv"
            
            return year_month_dir / filename
        
        elif file_type == "summary":
            return base_dir / "performance_summary.csv"
        elif file_type == "failure_patterns":
            return base_dir / "failure_patterns.csv"
        else:
            return base_dir / f"{file_type}.csv"
    
    def ensure_csv_header(self, file_path: Path, columns: List[str], layer: str):
        """确保CSV文件头部存在"""
        with self.file_locks[layer]:
            if not file_path.exists():
                try:
                    with open(file_path, 'w', newline='', encoding='utf-8') as f:
                        import csv
                        writer = csv.writer(f)
                        writer.writerow(columns)
                    self.logger.debug(f"创建CSV头部: {file_path}")
                except Exception as e:
                    self.logger.error(f"创建CSV头部失败 {file_path}: {e}")

    def get_file_size(self, file_path: Path) -> int:
        """获取文件大小（字节）"""
        try:
            return file_path.stat().st_size if file_path.exists() else 0
        except Exception:
            return 0

    def cleanup_old_files(self, days_to_keep: int = 30):
        """清理旧的日志文件"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cleaned_count = 0

        for layer_dir in [self.trades_dir, self.contexts_dir]:
            try:
                for year_dir in layer_dir.glob("*"):
                    if not year_dir.is_dir():
                        continue

                    for month_dir in year_dir.glob("*"):
                        if not month_dir.is_dir():
                            continue

                        for csv_file in month_dir.glob("*.csv"):
                            # 从文件名提取日期
                            try:
                                date_str = csv_file.stem.split('_')[-1]  # 获取日期部分
                                file_date = datetime.strptime(date_str, '%Y-%m-%d')

                                if file_date < cutoff_date:
                                    csv_file.unlink()
                                    cleaned_count += 1
                                    self.logger.info(f"清理旧文件: {csv_file}")
                            except (ValueError, IndexError):
                                continue

                        # 清理空目录
                        if not any(month_dir.iterdir()):
                            month_dir.rmdir()

                    # 清理空年份目录
                    if not any(year_dir.iterdir()):
                        year_dir.rmdir()

            except Exception as e:
                self.logger.error(f"清理{layer_dir}目录失败: {e}")

        self.logger.info(f"清理完成，删除了 {cleaned_count} 个旧文件")
        return cleaned_count

    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {
            'base_dir': str(self.base_log_dir),
            'layers': {}
        }

        for layer_name, layer_dir in [
            ('trades', self.trades_dir),
            ('contexts', self.contexts_dir),
            ('analysis', self.analysis_dir),
            ('unified_views', self.unified_views_dir)
        ]:
            layer_stats = {
                'total_files': 0,
                'total_size_bytes': 0,
                'oldest_file': None,
                'newest_file': None
            }

            try:
                all_files = list(layer_dir.rglob("*.csv"))
                layer_stats['total_files'] = len(all_files)

                if all_files:
                    file_times = []
                    total_size = 0

                    for file_path in all_files:
                        try:
                            stat = file_path.stat()
                            total_size += stat.st_size
                            file_times.append((stat.st_mtime, str(file_path)))
                        except Exception:
                            continue

                    layer_stats['total_size_bytes'] = total_size
                    layer_stats['total_size_mb'] = round(total_size / (1024 * 1024), 2)

                    if file_times:
                        file_times.sort()
                        layer_stats['oldest_file'] = file_times[0][1]
                        layer_stats['newest_file'] = file_times[-1][1]

            except Exception as e:
                self.logger.error(f"获取{layer_name}层统计失败: {e}")

            stats['layers'][layer_name] = layer_stats

        return stats


class AsyncMultiLayerWriter:
    """多层异步写入器 - 支持多个存储层的异步写入"""
    
    def __init__(self, storage_manager: MultiLayerStorageManager, 
                 queue_maxsize: int = 1000, batch_size: int = 10, flush_interval: float = 1.0):
        self.storage_manager = storage_manager
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.logger = logging.getLogger(f"{__name__}.AsyncMultiLayerWriter")
        
        # 为每个层级创建独立的写入队列
        self.write_queues = {
            'trades': queue.Queue(maxsize=queue_maxsize),
            'contexts': queue.Queue(maxsize=queue_maxsize),
            'analysis': queue.Queue(maxsize=queue_maxsize)
        }
        
        # 写入线程
        self.writer_threads = {}
        self.stop_events = {}
        
        # 统计信息
        self.stats = {
            'trades': {'total_writes': 0, 'failed_writes': 0},
            'contexts': {'total_writes': 0, 'failed_writes': 0},
            'analysis': {'total_writes': 0, 'failed_writes': 0}
        }
        self.stats_lock = threading.Lock()
        
        # 启动写入线程
        self.start_writer_threads()
    
    def start_writer_threads(self):
        """启动所有层级的异步写入线程"""
        for layer in ['trades', 'contexts', 'analysis']:
            self.stop_events[layer] = threading.Event()
            self.writer_threads[layer] = threading.Thread(
                target=self._background_writer,
                args=(layer,),
                name=f"ComprehensiveLogger-{layer.title()}Writer",
                daemon=True
            )
            self.writer_threads[layer].start()
        
        self.logger.info("所有层级的异步写入线程已启动")
    
    def stop_writer_threads(self, timeout: float = 5.0):
        """停止所有写入线程"""
        for layer in ['trades', 'contexts', 'analysis']:
            if layer in self.stop_events:
                self.stop_events[layer].set()
                # 发送停止信号
                try:
                    self.write_queues[layer].put({"__STOP__": True}, timeout=1.0)
                except queue.Full:
                    pass
        
        # 等待线程结束
        for layer, thread in self.writer_threads.items():
            if thread.is_alive():
                thread.join(timeout=timeout)
        
        self.logger.info("所有异步写入线程已停止")
    
    def put_data(self, layer: str, data: Dict[str, Any], timeout: float = 1.0) -> bool:
        """
        将数据放入指定层级的写入队列
        
        Args:
            layer: 存储层级
            data: 数据字典
            timeout: 超时时间
        
        Returns:
            是否成功放入队列
        """
        try:
            self.write_queues[layer].put(data, timeout=timeout)
            return True
        except queue.Full:
            with self.stats_lock:
                self.stats[layer]['failed_writes'] += 1
            self.logger.error(f"{layer}层写入队列已满，数据丢失")
            return False
    
    def _background_writer(self, layer: str):
        """后台异步写入线程主函数"""
        self.logger.info(f"{layer}层后台写入线程开始运行")
        
        batch_data = []
        last_flush_time = time.time()
        
        while not self.stop_events[layer].is_set():
            try:
                # 从队列获取数据
                try:
                    data = self.write_queues[layer].get(timeout=0.1)
                except queue.Empty:
                    # 检查是否需要定时刷新
                    if batch_data and time.time() - last_flush_time >= self.flush_interval:
                        self._write_batch(layer, batch_data)
                        batch_data = []
                        last_flush_time = time.time()
                    continue
                
                # 检查停止信号
                if isinstance(data, dict) and data.get("__STOP__"):
                    break
                
                # 添加到批次
                batch_data.append(data)
                
                # 检查是否达到批量大小
                if len(batch_data) >= self.batch_size:
                    self._write_batch(layer, batch_data)
                    batch_data = []
                    last_flush_time = time.time()
                
            except Exception as e:
                self.logger.error(f"{layer}层写入线程异常: {e}")
                with self.stats_lock:
                    self.stats[layer]['failed_writes'] += 1
        
        # 线程停止前，处理剩余数据
        if batch_data:
            self._write_batch(layer, batch_data)
        
        self.logger.info(f"{layer}层后台写入线程结束")
    
    def _write_batch(self, layer: str, batch_data: List[Dict[str, Any]]):
        """批量写入数据到指定层级"""
        if not batch_data:
            return
        
        try:
            # 根据层级选择不同的写入逻辑
            if layer == "trades":
                self._write_trades_batch(batch_data)
            elif layer == "contexts":
                self._write_contexts_batch(batch_data)
            elif layer == "analysis":
                self._write_analysis_batch(batch_data)
            
            with self.stats_lock:
                self.stats[layer]['total_writes'] += len(batch_data)
            
        except Exception as e:
            self.logger.error(f"{layer}层批量写入失败: {e}")
            with self.stats_lock:
                self.stats[layer]['failed_writes'] += len(batch_data)
    
    def _write_trades_batch(self, batch_data: List[Dict[str, Any]]):
        """写入交易数据批次"""
        # 继承 trading_logs_unified 的字段结构
        from .unified_trade_logger import UnifiedTradeLogger
        columns = UnifiedTradeLogger.CSV_COLUMNS
        
        # 按日期分组写入
        date_groups = {}
        for data in batch_data:
            entry_time = data.get('entry_timestamp', datetime.now().isoformat())
            if isinstance(entry_time, str):
                date = datetime.fromisoformat(entry_time.replace('Z', '+00:00')).date()
            else:
                date = entry_time.date()
            
            if date not in date_groups:
                date_groups[date] = []
            date_groups[date].append(data)
        
        # 分别写入每个日期的文件
        for date, data_list in date_groups.items():
            file_path = self.storage_manager.get_file_path('trades', 'daily', datetime.combine(date, datetime.min.time()))
            self.storage_manager.ensure_csv_header(file_path, columns, 'trades')
            
            self._append_to_csv(file_path, data_list, columns, 'trades')
    
    def _write_contexts_batch(self, batch_data: List[Dict[str, Any]]):
        """写入预测上下文数据批次"""
        # 使用 analysis_logs 的预测上下文字段
        from ..analysis.analysis_logger import PREDICTION_CONTEXT_FIELDS
        columns = PREDICTION_CONTEXT_FIELDS
        
        # 按日期分组写入
        date_groups = {}
        for data in batch_data:
            timestamp = data.get('timestamp', datetime.now().isoformat())
            if isinstance(timestamp, str):
                date = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).date()
            else:
                date = timestamp.date()
            
            if date not in date_groups:
                date_groups[date] = []
            date_groups[date].append(data)
        
        # 分别写入每个日期的文件
        for date, data_list in date_groups.items():
            file_path = self.storage_manager.get_file_path('contexts', 'daily', datetime.combine(date, datetime.min.time()))
            self.storage_manager.ensure_csv_header(file_path, columns, 'contexts')
            
            self._append_to_csv(file_path, data_list, columns, 'contexts')
    
    def _write_analysis_batch(self, batch_data: List[Dict[str, Any]]):
        """写入分析数据批次"""
        # 根据数据类型写入不同的分析文件
        for data in batch_data:
            data_type = data.get('__data_type__', 'failure_patterns')
            
            if data_type == 'failure_patterns':
                from ..analysis.analysis_logger import FAILURE_ANALYSIS_FIELDS
                columns = FAILURE_ANALYSIS_FIELDS
                file_path = self.storage_manager.get_file_path('analysis', 'failure_patterns')
            else:
                # 默认性能摘要
                columns = ['timestamp', 'metric_name', 'metric_value', 'period', 'details']
                file_path = self.storage_manager.get_file_path('analysis', 'summary')
            
            self.storage_manager.ensure_csv_header(file_path, columns, 'analysis')
            self._append_to_csv(file_path, [data], columns, 'analysis')
    
    def _append_to_csv(self, file_path: Path, data_list: List[Dict[str, Any]], columns: List[str], layer: str):
        """追加数据到CSV文件"""
        with self.storage_manager.file_locks[layer]:
            try:
                import csv
                with open(file_path, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=columns, extrasaction='ignore')
                    for data in data_list:
                        # 处理复杂数据类型
                        processed_data = self._process_data_for_csv(data)
                        writer.writerow(processed_data)
            except Exception as e:
                self.logger.error(f"写入CSV文件失败 {file_path}: {e}")
                raise
    
    def _process_data_for_csv(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据以适应CSV格式"""
        processed = {}
        for key, value in data.items():
            if key.startswith('__'):  # 跳过内部字段
                continue
            
            if isinstance(value, (dict, list)):
                processed[key] = json.dumps(value, ensure_ascii=False)
            elif value is None:
                processed[key] = ''
            else:
                processed[key] = str(value)
        
        return processed
    
    def get_statistics(self) -> Dict[str, Dict[str, int]]:
        """获取写入统计信息"""
        with self.stats_lock:
            return {layer: stats.copy() for layer, stats in self.stats.items()}


class UnifiedComprehensiveLogger:
    """
    统一综合日志系统主类

    整合 trading_logs_unified 和 analysis_logs 的所有功能，
    提供统一的高性能日志记录接口
    """

    def __init__(self, base_log_dir: str = "comprehensive_logs",
                 queue_maxsize: int = 1000, batch_size: int = 10,
                 flush_interval: float = 1.0, auto_start: bool = True):
        """
        初始化统一综合日志系统

        Args:
            base_log_dir: 日志基础目录
            queue_maxsize: 队列最大大小
            batch_size: 批量写入大小
            flush_interval: 刷新间隔（秒）
            auto_start: 是否自动启动
        """
        self.base_log_dir = base_log_dir
        self.logger = logging.getLogger(f"{__name__}.UnifiedComprehensiveLogger")

        # 初始化存储管理器和异步写入器
        self.storage_manager = MultiLayerStorageManager(base_log_dir)
        self.async_writer = AsyncMultiLayerWriter(
            self.storage_manager, queue_maxsize, batch_size, flush_interval
        )

        # 暂存待平仓的交易数据
        self.pending_trades: Dict[str, Dict[str, Any]] = {}
        self.pending_lock = threading.Lock()

        # 统计信息
        self.total_entries = 0
        self.total_exits = 0
        self.total_contexts = 0
        self.stats_lock = threading.Lock()

        # 失败案例分析器（延迟初始化）
        self._failure_analyzer = None

        self.logger.info(f"统一综合日志系统初始化完成: {base_log_dir}")

    def log_prediction_context(self, target_name: str, symbol: str,
                             signal_data: Dict[str, Any], market_data: Dict[str, Any],
                             model_data: Dict[str, Any], filter_data: Dict[str, Any]) -> bool:
        """
        记录预测上下文信息

        Args:
            target_name: 目标策略名称
            symbol: 交易对符号
            signal_data: 信号数据
            market_data: 市场数据
            model_data: 模型数据
            filter_data: 过滤器数据

        Returns:
            是否成功记录
        """
        try:
            context_data = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'target_name': target_name,
                'symbol': symbol,
                'signal_type': signal_data.get('signal_type'),
                'signal_strength': signal_data.get('signal_strength'),
                'avg_up_prob': signal_data.get('avg_up_prob'),
                'avg_down_prob': signal_data.get('avg_down_prob'),
                'model_positive_class_prob': signal_data.get('model_positive_class_prob'),
                'signal_threshold_used': signal_data.get('signal_threshold_used'),
                'feature_names_json': json.dumps(model_data.get('feature_names', [])),
                'feature_values_json': json.dumps(model_data.get('feature_values', [])),
                'current_price': market_data.get('current_price'),
                'last_kline_close': market_data.get('last_kline_close'),
                'trend_signal': filter_data.get('trend_signal'),
                'trend_strength': filter_data.get('trend_strength'),
                'trend_status_text': filter_data.get('trend_status_text'),
                'adx_value': filter_data.get('adx_value'),
                'pdi_value': filter_data.get('pdi_value'),
                'mdi_value': filter_data.get('mdi_value'),
                'volatility_level': filter_data.get('volatility_level'),
                'volatility_status_text': filter_data.get('volatility_status_text'),
                'atr_value': filter_data.get('atr_value'),
                'atr_percent': filter_data.get('atr_percent'),
                'planned_amount': signal_data.get('planned_amount'),
                'amount_calculation_method': signal_data.get('amount_calculation_method'),
                'dynamic_threshold_adjustments': json.dumps(filter_data.get('dynamic_threshold_adjustments', {})),
                'filter_reasons': filter_data.get('filter_reasons'),
                'model_type': model_data.get('model_type'),
                'ensemble_size': model_data.get('ensemble_size')
            }

            success = self.async_writer.put_data('contexts', context_data)
            if success:
                with self.stats_lock:
                    self.total_contexts += 1

            return success

        except Exception as e:
            self.logger.error(f"记录预测上下文失败: {e}")
            return False

    def log_trade_opened(self, target_name: str, symbol: str, direction: str,
                        entry_price: float, amount: float, payout_ratio: float = 0.85,
                        trade_id: Optional[str] = None,
                        context_data: Optional[Dict[str, Any]] = None) -> str:
        """
        记录交易开仓信息（暂存到内存，等待平仓后合并写入）

        Args:
            target_name: 目标策略名称
            symbol: 交易对符号
            direction: 交易方向
            entry_price: 开仓价格
            amount: 交易金额
            payout_ratio: 盈利比例
            trade_id: 交易ID（可选）
            context_data: 上下文数据（可选）

        Returns:
            交易ID
        """
        # 参数验证
        direction_upper = direction.upper()
        valid_directions = ['LONG', 'SHORT', 'UP', 'DOWN', 'BUY', 'SELL', 'BLOCKED']
        if direction_upper not in valid_directions:
            raise ValueError(f"无效的交易方向: {direction}")

        # 对于BLOCKED类型，允许价格为0或任意值
        if direction_upper != 'BLOCKED' and entry_price <= 0:
            raise ValueError(f"开仓价格必须大于0: {entry_price}")

        if direction_upper != 'BLOCKED' and amount <= 0:
            raise ValueError(f"交易金额必须大于0: {amount}")

        if amount <= 0:
            raise ValueError(f"交易金额必须大于0: {amount}")

        # 生成交易ID
        if trade_id is None:
            trade_id = f"{target_name}_{symbol}_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"

        # 构建基础交易数据
        trade_data = {
            'trade_id': trade_id,
            'entry_timestamp': datetime.now().isoformat(),
            'target_name': target_name,
            'symbol': symbol,
            'direction': direction.upper(),
            'entry_price': entry_price,
            'amount': amount,
            'payout_ratio': payout_ratio,

            # 平仓字段（稍后填充）
            'exit_timestamp': None,
            'exit_price': None,
            'result': None,
            'profit_loss': None,
            'exit_reason': None
        }

        # 🎯 简化的上下文数据处理（与UnifiedTradeLogger保持一致）
        if context_data:
            # 使用简化的字段处理逻辑
            from .unified_trade_logger import UnifiedTradeLogger

            # 直接映射的关键字段
            direct_mapping_fields = [
                'entry_signal_probability', 'entry_neutral_probability', 'entry_opposite_probability',
                'direction_advantage', 'up_advantage', 'down_advantage', 'meta_decision_reason',
                'threshold_check_result', 'signal_filtered_reason',
                'individual_model_probabilities', 'base_model_predictions',
                'entry_market_regime', 'entry_atr_percent', 'entry_adx_value'
            ]

            for field in direct_mapping_fields:
                if field in context_data:
                    trade_data[field] = context_data[field]

            # 🎯 将复杂的元模型特征合并为决策上下文摘要
            decision_summary = self._create_decision_context_summary(context_data)
            trade_data['decision_context_summary'] = decision_summary

        # 暂存到pending_trades
        with self.pending_lock:
            self.pending_trades[trade_id] = trade_data

        with self.stats_lock:
            self.total_entries += 1

        self.logger.info(f"交易开仓记录: {trade_id} - {direction} @{entry_price}, 金额: {amount}")
        return trade_id

    def record_trade_entry(self, target_name: str, symbol: str, direction: str,
                          entry_price: float, amount: float, payout_ratio: float = 0.85,
                          context_data: Optional[Dict[str, Any]] = None) -> str:
        """🎯 兼容方法：记录交易开仓"""
        return self.log_trade_opened(
            target_name=target_name,
            symbol=symbol,
            direction=direction,
            entry_price=entry_price,
            amount=amount,
            payout_ratio=payout_ratio,
            context_data=context_data
        )

    def _create_decision_context_summary(self, context_data: Dict[str, Any]) -> str:
        """
        🎯 创建决策上下文摘要，将复杂的元模型特征合并为关键信息
        （与UnifiedTradeLogger保持一致）

        Args:
            context_data: 完整的上下文数据

        Returns:
            JSON格式的决策上下文摘要字符串
        """
        try:
            summary = {}

            # 1. AI初筛阈值信息
            ai_screening = {}
            if 'threshold_check_result' in context_data:
                threshold_result = context_data.get('threshold_check_result', {})
                if isinstance(threshold_result, str):
                    try:
                        threshold_result = json.loads(threshold_result)
                    except:
                        threshold_result = {}

                ai_screening = {
                    'up_threshold': threshold_result.get('up_threshold', 0.0),
                    'down_threshold': threshold_result.get('down_threshold', 0.0),
                    'up_gap': threshold_result.get('up_gap', 0.0),
                    'down_gap': threshold_result.get('down_gap', 0.0),
                    'up_passed': threshold_result.get('up_passed', False),
                    'down_passed': threshold_result.get('down_passed', False)
                }
            summary['ai_screening'] = ai_screening

            # 2. 人类智能复核信息
            human_review = {
                'direction_advantage_enabled': context_data.get('direction_advantage', 0.0) != 0.0,
                'up_advantage': context_data.get('up_advantage', 0.0),
                'down_advantage': context_data.get('down_advantage', 0.0),
                'advantage_threshold': 0.07  # 7%阈值
            }
            summary['human_review'] = human_review

            # 3. 全局市场状态摘要
            market_state = {
                'adx_trend_strength': context_data.get('entry_adx_value', 0.0),
                'atr_volatility': context_data.get('entry_atr_percent', 0.0),
                'market_regime': context_data.get('entry_market_regime', 'normal')
            }

            # 从复杂特征中提取关键市场指标
            for key, value in context_data.items():
                if 'global_trend_strength' in key:
                    market_state['trend_strength'] = value
                elif 'global_volatility_level' in key:
                    market_state['volatility_level'] = value
                elif 'global_ema_diff_pct' in key:
                    market_state['ema_trend_diff'] = value

            summary['market_state'] = market_state

            # 4. 基础模型贡献摘要
            model_contributions = {}
            individual_probs = context_data.get('individual_model_probabilities')
            if individual_probs:
                if isinstance(individual_probs, str):
                    try:
                        individual_probs = json.loads(individual_probs)
                    except:
                        individual_probs = {}

                # 提取关键模型概率
                for model_type in ['LSTM', 'UP', 'DOWN']:
                    up_key = f'{model_type}_model_up_prob'
                    down_key = f'{model_type}_model_down_prob'
                    if up_key in individual_probs and down_key in individual_probs:
                        up_prob = individual_probs[up_key]
                        down_prob = individual_probs[down_key]
                        signal = 'UP' if up_prob > down_prob else 'DOWN'
                        model_contributions[f'BTC_15m_{model_type}'] = {
                            'up_prob': f"{up_prob:.2%}",
                            'down_prob': f"{down_prob:.2%}",
                            'signal': signal
                        }

            summary['model_contributions'] = model_contributions

            return json.dumps(summary, ensure_ascii=False)

        except Exception as e:
            self.logger.warning(f"创建决策上下文摘要失败: {e}")
            return json.dumps({'error': str(e)}, ensure_ascii=False)

    def log_trade_closed(self, trade_id: str, exit_price: float,
                        result: str, exit_reason: str = "expired") -> bool:
        """
        记录交易平仓信息（合并数据并异步写入）

        Args:
            trade_id: 交易ID
            exit_price: 平仓价格
            result: 交易结果 (WIN 或 LOSS)
            exit_reason: 平仓原因

        Returns:
            是否成功记录
        """
        # 验证参数
        result = result.upper()
        if result not in ['WIN', 'LOSS', 'BLOCKED']:
            raise ValueError(f"无效的交易结果: {result}")

        # 对于BLOCKED类型，允许价格为0或任意值
        if result != 'BLOCKED' and exit_price <= 0:
            raise ValueError(f"平仓价格必须大于0: {exit_price}")

        # 从pending_trades中获取并移除开仓数据
        with self.pending_lock:
            if trade_id not in self.pending_trades:
                self.logger.error(f"未找到开仓记录: {trade_id}")
                return False

            trade_data = self.pending_trades.pop(trade_id)

        # 合并平仓信息
        trade_data.update({
            'exit_timestamp': datetime.now().isoformat(),
            'exit_price': exit_price,
            'result': result,
            'exit_reason': exit_reason
        })

        # 计算盈亏
        entry_price = trade_data['entry_price']
        amount = trade_data['amount']
        payout_ratio = trade_data['payout_ratio']

        if result == 'WIN':
            profit_loss = amount * payout_ratio
        elif result == 'BLOCKED':
            profit_loss = 0.0  # 被阻止的交易没有盈亏
        else:  # LOSS
            profit_loss = -amount

        trade_data['profit_loss'] = profit_loss

        # 异步写入交易数据
        success = self.async_writer.put_data('trades', trade_data)

        if success:
            with self.stats_lock:
                self.total_exits += 1

            self.logger.info(f"交易平仓: {trade_id} - {result} @{exit_price}, P&L: {profit_loss:.2f}")

            # 如果是亏损交易，触发失败案例分析
            if result == 'LOSS':
                self._trigger_failure_analysis(trade_data)

        return success

    def record_trade_exit(self, trade_id: str, exit_price: float, result: str,
                         exit_reason: str = "expired") -> bool:
        """🎯 兼容方法：记录交易平仓"""
        return self.log_trade_closed(
            trade_id=trade_id,
            exit_price=exit_price,
            result=result,
            exit_reason=exit_reason
        )

    def log_complete_trade(self, trade_data: Dict[str, Any],
                          context_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        一次性记录完整交易（开仓+平仓+上下文）

        Args:
            trade_data: 完整的交易数据
            context_data: 预测上下文数据（可选）

        Returns:
            是否成功记录
        """
        success = True

        # 记录交易数据
        trade_success = self.async_writer.put_data('trades', trade_data)
        if not trade_success:
            success = False

        # 记录上下文数据（如果提供）
        if context_data:
            context_success = self.async_writer.put_data('contexts', context_data)
            if not context_success:
                success = False

        # 更新统计
        if success:
            with self.stats_lock:
                self.total_entries += 1
                self.total_exits += 1
                if context_data:
                    self.total_contexts += 1

        # 如果是亏损交易，触发失败案例分析
        if trade_data.get('result') == 'LOSS':
            self._trigger_failure_analysis(trade_data)

        return success

    def _trigger_failure_analysis(self, trade_data: Dict[str, Any]):
        """触发失败案例分析"""
        try:
            # 延迟初始化失败案例分析器
            if self._failure_analyzer is None:
                from .comprehensive_failure_analyzer import ComprehensiveFailureAnalyzer
                self._failure_analyzer = ComprehensiveFailureAnalyzer(
                    comprehensive_logs_dir=str(self.storage_manager.base_log_dir)
                )

            # 异步记录失败案例数据
            failure_data = {
                '__data_type__': 'failure_patterns',
                'analysis_timestamp': datetime.now().isoformat(),
                'trade_id': trade_data.get('trade_id'),
                'target_name': trade_data.get('target_name'),
                'symbol': trade_data.get('symbol'),
                'direction': trade_data.get('direction'),
                'entry_price': trade_data.get('entry_price'),
                'exit_price': trade_data.get('exit_price'),
                'profit_loss': trade_data.get('profit_loss'),
                'failure_context': json.dumps({
                    'market_regime': trade_data.get('entry_market_regime'),
                    'atr_percent': trade_data.get('entry_atr_percent'),
                    'signal_probability': trade_data.get('entry_signal_probability')
                })
            }

            self.async_writer.put_data('analysis', failure_data)

        except Exception as e:
            self.logger.error(f"触发失败案例分析失败: {e}")

    def analyze_failures(self, period_days: int = 7) -> Optional[Dict[str, Any]]:
        """
        分析失败案例

        Args:
            period_days: 分析周期（天）

        Returns:
            分析结果字典
        """
        try:
            if self._failure_analyzer is None:
                from .comprehensive_failure_analyzer import ComprehensiveFailureAnalyzer
                self._failure_analyzer = ComprehensiveFailureAnalyzer(
                    comprehensive_logs_dir=str(self.storage_manager.base_log_dir)
                )

            return self._failure_analyzer.analyze_failure_patterns(period_days)

        except Exception as e:
            self.logger.error(f"失败案例分析出错: {e}")
            return None

    def get_performance_summary(self, period_days: int = 7) -> Optional[Dict[str, Any]]:
        """
        获取性能汇总

        Args:
            period_days: 统计周期（天）

        Returns:
            性能汇总字典
        """
        try:
            if self._failure_analyzer is None:
                from .comprehensive_failure_analyzer import ComprehensiveFailureAnalyzer
                self._failure_analyzer = ComprehensiveFailureAnalyzer(
                    comprehensive_logs_dir=str(self.storage_manager.base_log_dir)
                )

            return self._failure_analyzer.analyze_failure_patterns(period_days)

        except Exception as e:
            self.logger.error(f"性能汇总分析出错: {e}")
            return None

    def flush_all_queues(self, timeout: float = 5.0):
        """强制刷新所有队列中的数据"""
        for layer in ['trades', 'contexts', 'analysis']:
            try:
                self.async_writer.write_queues[layer].put({"__FLUSH__": True}, timeout=1.0)
            except queue.Full:
                self.logger.warning(f"无法刷新{layer}队列，队列已满")

    def stop(self, timeout: float = 5.0):
        """停止日志系统"""
        self.logger.info("正在停止统一综合日志系统...")

        # 刷新所有队列
        self.flush_all_queues()

        # 停止异步写入线程
        self.async_writer.stop_writer_threads(timeout)

        self.logger.info("统一综合日志系统已停止")

    def get_statistics(self) -> Dict[str, Any]:
        """获取完整的统计信息"""
        with self.stats_lock:
            basic_stats = {
                'total_entries': self.total_entries,
                'total_exits': self.total_exits,
                'total_contexts': self.total_contexts,
                'pending_trades_count': len(self.pending_trades)
            }

        # 获取异步写入器统计
        writer_stats = self.async_writer.get_statistics()

        return {
            'basic_stats': basic_stats,
            'writer_stats': writer_stats,
            'storage_path': str(self.storage_manager.base_log_dir)
        }

    def load_trade_logs(self, start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        加载交易日志数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            交易数据DataFrame
        """
        try:
            trades_dir = self.storage_manager.trades_dir
            all_files = []

            # 收集所有交易文件
            for year_dir in trades_dir.glob("*"):
                if year_dir.is_dir():
                    for month_dir in year_dir.glob("*"):
                        if month_dir.is_dir():
                            for csv_file in month_dir.glob("trades_*.csv"):
                                all_files.append(csv_file)

            if not all_files:
                self.logger.warning("未找到交易日志文件")
                return None

            # 读取并合并所有文件
            dfs = []
            for file_path in all_files:
                try:
                    df = pd.read_csv(file_path)
                    if not df.empty:
                        dfs.append(df)
                except Exception as e:
                    self.logger.error(f"读取文件失败 {file_path}: {e}")

            if not dfs:
                return None

            combined_df = pd.concat(dfs, ignore_index=True)

            # 日期过滤
            if start_date or end_date:
                combined_df['entry_timestamp'] = pd.to_datetime(combined_df['entry_timestamp'])

                if start_date:
                    combined_df = combined_df[combined_df['entry_timestamp'] >= start_date]
                if end_date:
                    combined_df = combined_df[combined_df['entry_timestamp'] <= end_date]

            return combined_df

        except Exception as e:
            self.logger.error(f"加载交易日志失败: {e}")
            return None

    def load_context_logs(self, start_date: Optional[str] = None,
                         end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        加载预测上下文日志数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            上下文数据DataFrame
        """
        try:
            contexts_dir = self.storage_manager.contexts_dir
            all_files = []

            # 收集所有上下文文件
            for year_dir in contexts_dir.glob("*"):
                if year_dir.is_dir():
                    for month_dir in year_dir.glob("*"):
                        if month_dir.is_dir():
                            for csv_file in month_dir.glob("contexts_*.csv"):
                                all_files.append(csv_file)

            if not all_files:
                self.logger.warning("未找到上下文日志文件")
                return None

            # 读取并合并所有文件
            dfs = []
            for file_path in all_files:
                try:
                    df = pd.read_csv(file_path)
                    if not df.empty:
                        dfs.append(df)
                except Exception as e:
                    self.logger.error(f"读取文件失败 {file_path}: {e}")

            if not dfs:
                return None

            combined_df = pd.concat(dfs, ignore_index=True)

            # 日期过滤
            if start_date or end_date:
                combined_df['timestamp'] = pd.to_datetime(combined_df['timestamp'])

                if start_date:
                    combined_df = combined_df[combined_df['timestamp'] >= start_date]
                if end_date:
                    combined_df = combined_df[combined_df['timestamp'] <= end_date]

            return combined_df

        except Exception as e:
            self.logger.error(f"加载上下文日志失败: {e}")
            return None


# 全局实例管理
_global_comprehensive_logger = None
_logger_lock = threading.Lock()


def get_unified_comprehensive_logger(base_log_dir: str = "comprehensive_logs",
                                   queue_maxsize: int = 1000, batch_size: int = 10,
                                   flush_interval: float = 1.0, auto_start: bool = True) -> UnifiedComprehensiveLogger:
    """
    🚫 已废弃：重定向到简化的UnifiedTradeLogger

    为了确保只使用简化的28字段日志格式，此函数现在返回UnifiedTradeLogger的兼容包装器

    Args:
        base_log_dir: 日志基础目录（将被重定向到trading_logs_unified）
        queue_maxsize: 队列最大大小（兼容参数，被忽略）
        batch_size: 批量写入大小（兼容参数，被忽略）
        flush_interval: 刷新间隔（兼容参数，被忽略）
        auto_start: 是否自动启动

    Returns:
        UnifiedTradeLogger的兼容包装器
    """
    import warnings
    warnings.warn(
        "UnifiedComprehensiveLogger已废弃，系统自动重定向到简化的UnifiedTradeLogger",
        DeprecationWarning,
        stacklevel=2
    )

    # 🎯 重定向到简化的UnifiedTradeLogger
    from .unified_trade_logger import get_unified_trade_logger
    return get_unified_trade_logger(base_log_dir="trading_logs_unified", auto_start=auto_start)


def reset_comprehensive_logger():
    """重置全局日志记录器（用于测试）"""
    global _global_comprehensive_logger

    with _logger_lock:
        if _global_comprehensive_logger:
            _global_comprehensive_logger.stop()
        _global_comprehensive_logger = None


# 向后兼容的便捷函数
def log_prediction_context(target_name: str, symbol: str, signal_data: Dict[str, Any],
                          market_data: Dict[str, Any], model_data: Dict[str, Any],
                          filter_data: Dict[str, Any]) -> bool:
    """向后兼容：记录预测上下文"""
    logger = get_unified_comprehensive_logger()
    return logger.log_prediction_context(target_name, symbol, signal_data, market_data, model_data, filter_data)


def log_trade_settlement(trade_obj, market_snapshot_entry=None,
                        market_snapshot_exit=None, related_prediction_id=None) -> bool:
    """向后兼容：记录交易结算"""
    logger = get_unified_comprehensive_logger()

    # 转换为新格式
    trade_data = {
        'trade_id': trade_obj.trade_id,
        'entry_timestamp': trade_obj.entry_time.isoformat(),
        'exit_timestamp': datetime.now().isoformat(),
        'target_name': getattr(trade_obj, 'target_name', 'Unknown'),
        'symbol': getattr(trade_obj, 'symbol', 'BTCUSDT'),
        'direction': trade_obj.direction,
        'entry_price': trade_obj.entry_price,
        'exit_price': trade_obj.exit_price,
        'amount': trade_obj.amount_staked,
        'payout_ratio': trade_obj.PAYOUT_RATIO,
        'result': 'WIN' if trade_obj.status == 'WON' else 'LOSS',
        'profit_loss': trade_obj.profit_loss,
        'exit_reason': 'expired',
        'entry_market_snapshot_json': json.dumps(market_snapshot_entry or {}),
        'exit_market_snapshot_json': json.dumps(market_snapshot_exit or {}),
        'related_prediction_id': related_prediction_id
    }

    return logger.log_complete_trade(trade_data)
