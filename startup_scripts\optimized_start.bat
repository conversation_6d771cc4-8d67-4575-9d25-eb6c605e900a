@echo off
REM AMD处理器 + RTX 3070 系统优化启动脚本

echo 🚀 启动系统优化...

REM 设置CPU线程数
set OMP_NUM_THREADS=16
set MKL_NUM_THREADS=16
set NUMBA_NUM_THREADS=16
set OPENBLAS_NUM_THREADS=16

REM TensorFlow优化
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=1
set TF_NUM_INTEROP_THREADS=4
set TF_NUM_INTRAOP_THREADS=16

REM 内存优化
set MALLOC_TRIM_THRESHOLD_=100000
set MALLOC_MMAP_THRESHOLD_=131072

REM Python优化
set PYTHONHASHSEED=0
set PYTHONUNBUFFERED=1

REM GPU优化
set CUDA_VISIBLE_DEVICES=0
set CUDA_CACHE_DISABLE=0

echo ✅ 系统优化设置完成

REM 启动Python程序
python %*
