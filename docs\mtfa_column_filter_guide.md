# MTFA列过滤器使用指南

## 概述

MTFA列过滤器是一个完善的特征列过滤系统，解决了原有MTFA功能中列过滤逻辑过于简单的问题。通过维护显式排除列表和智能过滤规则，确保只有合适的特征被合并到主DataFrame中。

## 主要改进

### ✅ 解决的问题

1. **过滤逻辑简单** → 完善的多层过滤规则
2. **硬编码排除模式** → 可配置的排除/包含列表
3. **缺乏灵活性** → 支持正则表达式和自定义模式
4. **过滤信息不透明** → 详细的过滤统计和摘要
5. **无法适应不同场景** → 严格/宽松模式可选

### 🚀 新增功能

1. **显式排除列表**: 明确定义不应合并的列模式
2. **智能包含规则**: 基于特征类型的智能识别
3. **可配置过滤**: 支持不同的过滤策略
4. **正则表达式支持**: 灵活的模式匹配
5. **详细统计**: 完整的过滤摘要信息

## 过滤规则详解

### 1. 排除规则（优先级高）

#### 基础数据列（永远排除）
```python
base_columns = {
    'open', 'high', 'low', 'close', 'volume', 
    'qav', 'n', 'tbbav', 'tbqav'
}
```

#### 目标变量列（永远排除）
```python
target_patterns = {
    'target_',           # 所有目标变量
    'future_close_',     # 未来收盘价
    'future_high_',      # 未来最高价
    'future_low_',       # 未来最低价
    'future_return_',    # 未来收益率
    'label_',            # 标签列
    'y_',                # 标签变量
}
```

#### 字符串/分类列（永远排除）
```python
string_patterns = {
    '_name',             # 名称列
    '_pattern',          # 形态列
    '_signal',           # 信号列（如果是字符串）
    '_category',         # 分类列
    '_type',             # 类型列
    'candlestick_pattern_name',  # K线形态名称
}
```

#### 临时/中间计算列（永远排除）
```python
temporary_patterns = {
    'temp_',             # 临时列
    'intermediate_',     # 中间计算列
    '_temp',             # 临时后缀
    '_intermediate',     # 中间后缀
    'calc_',             # 计算列
    'debug_',            # 调试列
}
```

#### 元数据列（永远排除）
```python
metadata_patterns = {
    'timestamp',         # 时间戳
    'datetime',          # 日期时间
    'symbol',            # 交易对
    'interval',          # 时间间隔
    'source',            # 数据源
    '_id',               # ID列
}
```

#### 统计/诊断列（可配置排除）
```python
diagnostic_patterns = {
    '_count',            # 计数列
    '_sum',              # 求和列
    '_mean',             # 均值列
    '_std',              # 标准差列
    'stat_',             # 统计前缀
    'diag_',             # 诊断前缀
}
```

#### 业务逻辑列（可配置排除）
```python
business_exclusions = {
    'commission',        # 手续费
    'slippage',          # 滑点
    'spread',            # 点差
    'funding_rate',      # 资金费率
    'liquidation_',      # 清算相关
    'margin_',           # 保证金相关
}
```

### 2. 包含规则（在排除后应用）

#### 技术指标特征（高优先级）
```python
technical_indicators = {
    'RSI_', 'rsi_',           # RSI
    'MACD', 'macd_',          # MACD
    'HMA_', 'hma_',           # Hull移动平均
    'EMA_', 'ema_',           # 指数移动平均
    'ATR', 'atr_',            # 平均真实范围
    'WILLR_', 'willr_',       # Williams %R
    'CCI_', 'cci_',           # 商品通道指数
    'STOCH', 'stoch_',        # 随机指标
    'KC_', 'kc_',             # Keltner通道
    'BB_', 'bb_',             # 布林带
    'ADX', 'adx_',            # 平均方向指数
}
```

#### 价格特征（高优先级）
```python
price_features = {
    'price_change_',      # 价格变化
    'close_pos_',         # 收盘价位置
    'body_size',          # K线实体
    'candle_range',       # K线范围
    'upper_shadow',       # 上影线
    'lower_shadow',       # 下影线
}
```

#### 成交量特征（高优先级）
```python
volume_features = {
    'volume_vs_avg',      # 成交量相对平均值
    'volume_change_',     # 成交量变化
    'taker_buy_ratio',    # 买方比率
}
```

## 使用方法

### 1. 基础使用

```python
from src.core.mtfa_column_filter import filter_mtfa_columns, get_default_mtfa_filter_config

# 过滤MTFA特征列
config = get_default_mtfa_filter_config()
filtered_columns = filter_mtfa_columns(df, '15m', config)

# 获取过滤后的DataFrame
filtered_df = df[filtered_columns]
```

### 2. 配置选项

#### 默认配置（严格模式）
```python
from src.core.mtfa_column_filter import get_default_mtfa_filter_config

config = get_default_mtfa_filter_config()
# {
#     'exclude_diagnostic_columns': True,      # 排除统计/诊断列
#     'exclude_business_columns': True,        # 排除业务逻辑列
#     'loose_inclusion_mode': False,           # 严格包含模式
#     'custom_exclusion_patterns': [],        # 自定义排除模式
#     'custom_inclusion_patterns': [],        # 自定义包含模式
#     'regex_exclusion_patterns': [],         # 正则排除模式
#     'regex_inclusion_patterns': []          # 正则包含模式
# }
```

#### 宽松配置
```python
from src.core.mtfa_column_filter import get_permissive_mtfa_filter_config

config = get_permissive_mtfa_filter_config()
# {
#     'exclude_diagnostic_columns': False,    # 不排除统计/诊断列
#     'exclude_business_columns': False,      # 不排除业务逻辑列
#     'loose_inclusion_mode': True,           # 宽松包含模式
#     ...
# }
```

### 3. 自定义配置

#### 自定义排除模式
```python
config = get_default_mtfa_filter_config()
config.update({
    'custom_exclusion_patterns': [
        'my_temp_',          # 排除自定义临时列
        'experimental_',     # 排除实验性特征
        '_deprecated'        # 排除已废弃特征
    ]
})
```

#### 自定义包含模式
```python
config = get_default_mtfa_filter_config()
config.update({
    'custom_inclusion_patterns': [
        'my_indicator_',     # 包含自定义指标
        'special_feature_'   # 包含特殊特征
    ]
})
```

#### 正则表达式模式
```python
config = get_default_mtfa_filter_config()
config.update({
    'regex_exclusion_patterns': [
        r'.*_\d+p$',        # 排除以_数字p结尾的列
        r'^temp_.*'         # 排除以temp_开头的列
    ],
    'regex_inclusion_patterns': [
        r'RSI_\d+',         # 只包含RSI指标
        r'MACD.*'           # 只包含MACD相关
    ]
})
```

### 4. 在MTFA中使用

#### 在目标配置中设置
```python
target_config = {
    'name': 'BTC_5M',
    'symbol': 'BTCUSDT',
    'interval': '5m',
    'enable_mtfa': True,
    'mtfa_timeframes': ['15m', '1h', '4h'],
    
    # MTFA列过滤配置
    'mtfa_filter_config': {
        'exclude_diagnostic_columns': True,
        'exclude_business_columns': True,
        'loose_inclusion_mode': False,
        'custom_exclusion_patterns': ['my_temp_'],
        'custom_inclusion_patterns': ['my_indicator_']
    }
}
```

#### 在MTFA优化器中使用
```python
from src.optimization.mtfa_performance_optimizer import filter_mtfa_features

# 自动使用配置的过滤器
filtered_df = filter_mtfa_features(df_features, timeframe, config)
```

## 过滤统计和监控

### 获取过滤摘要
```python
from src.core.mtfa_column_filter import MTFAColumnFilter

filter_obj = MTFAColumnFilter()
filtered_columns = filter_obj.filter_mtfa_columns(df, '15m', config)

# 获取详细摘要
summary = filter_obj.get_filter_summary(
    df.columns.tolist(), 
    filtered_columns, 
    '15m'
)

print(f"过滤摘要:")
print(f"  原始列数: {summary['original_count']}")
print(f"  过滤后列数: {summary['filtered_count']}")
print(f"  保留率: {summary['retention_rate']:.2%}")
```

### 日志监控
过滤器会自动记录详细的过滤统计：

```
MTFA列过滤完成 (15m): 原始 64 列 -> 保留 24 列 (排除 40 列)
  base_columns: 9 列
  target_patterns: 6 列
  string_patterns: 7 列
  temporary_patterns: 4 列
  metadata_patterns: 4 列
  diagnostic_patterns: 5 列
  business_exclusions: 4 列
  data_type_exclusions: 1 列
```

## 最佳实践

### 1. 开发阶段
```python
# 使用严格模式，确保只保留必要特征
config = get_default_mtfa_filter_config()
config['exclude_diagnostic_columns'] = True
config['exclude_business_columns'] = True
```

### 2. 实验阶段
```python
# 使用宽松模式，保留更多特征用于实验
config = get_permissive_mtfa_filter_config()
```

### 3. 生产环境
```python
# 使用经过验证的自定义配置
config = get_default_mtfa_filter_config()
config.update({
    'custom_exclusion_patterns': ['debug_', 'test_'],
    'custom_inclusion_patterns': ['validated_feature_']
})
```

### 4. 性能优化
```python
# 对于高频场景，使用更严格的过滤
config = get_default_mtfa_filter_config()
config.update({
    'exclude_diagnostic_columns': True,
    'exclude_business_columns': True,
    'loose_inclusion_mode': False,
    'regex_exclusion_patterns': [r'.*_debug$', r'.*_temp$']
})
```

## 故障排除

### 1. 过滤过于严格
**问题**: 保留的特征太少
**解决**: 
- 使用 `get_permissive_mtfa_filter_config()`
- 设置 `loose_inclusion_mode: True`
- 添加 `custom_inclusion_patterns`

### 2. 过滤不够严格
**问题**: 保留了不需要的特征
**解决**:
- 添加 `custom_exclusion_patterns`
- 使用 `regex_exclusion_patterns`
- 检查 `exclude_diagnostic_columns` 和 `exclude_business_columns` 设置

### 3. 特定特征被误排除
**问题**: 重要特征被错误排除
**解决**:
- 检查特征名是否匹配排除模式
- 添加到 `custom_inclusion_patterns`
- 使用 `regex_inclusion_patterns` 精确匹配

### 4. 性能问题
**问题**: 过滤过程耗时较长
**解决**:
- 减少正则表达式模式的复杂度
- 使用更简单的字符串匹配
- 考虑缓存过滤结果

## 扩展和自定义

### 添加新的排除模式
```python
# 在 MTFAColumnFilter 类中添加
class MTFAColumnFilter:
    def _initialize_exclusion_rules(self):
        # 添加新的排除模式
        self.custom_business_patterns = {
            'risk_',             # 风险相关
            'compliance_',       # 合规相关
            'audit_'             # 审计相关
        }
```

### 添加新的包含模式
```python
# 在 MTFAColumnFilter 类中添加
class MTFAColumnFilter:
    def _initialize_inclusion_rules(self):
        # 添加新的包含模式
        self.custom_indicators = {
            'my_rsi_',           # 自定义RSI
            'enhanced_macd_',    # 增强MACD
            'proprietary_'       # 专有指标
        }
```

## 总结

MTFA列过滤器提供了：

1. **完善的过滤规则** - 7类排除规则 + 5类包含规则
2. **高度可配置** - 支持严格/宽松模式和自定义模式
3. **灵活的模式匹配** - 字符串匹配 + 正则表达式支持
4. **详细的统计信息** - 完整的过滤摘要和日志
5. **向后兼容** - 无缝集成到现有MTFA系统

通过这个系统，MTFA特征合并变得更加智能和可控，确保只有高质量的特征被用于模型训练。
