# 📁 iPhone自动化文件清单

## 🎯 文件夹已清理完成

### 🔧 核心功能文件 (4个)

1. **`ssh_zxtouch_trader.py`** (核心脚本)
   - 主要的iPhone自动化交易脚本
   - 支持SSH远程执行
   - 包含完整的6步交易流程
   - 使用最终校准的坐标和时间参数

2. **`test_signal_sender.py`** (测试工具)
   - 安全的信号发送测试器
   - 支持交互式和命令行模式
   - 不影响真实交易冷却机制
   - 用于验证iPhone自动化功能

3. **`system_status_check.py`** (状态检查)
   - 全面的系统状态检查工具
   - 验证SSH连接、ZXTouch、信号发送等
   - 提供详细的诊断信息
   - 帮助快速定位问题

4. **`iphone_config.py`** (配置文件)
   - 包含所有精确校准的坐标
   - 优化后的时间参数
   - iPhone连接配置
   - 便于维护和更新

### 📚 文档文件 (4个)

1. **`AI_SUCCESS_EXPERIENCE.md`** (⭐ 最重要)
   - 完整的开发和调试经验
   - 详细的问题解决方案
   - 技术架构和实现细节
   - 为未来AI提供的成功经验

2. **`PRODUCTION_READY.md`** (生产确认)
   - 生产就绪状态确认
   - 完整的功能验证清单
   - 使用方法和注意事项
   - 风险管理建议

3. **`README.md`** (使用指南)
   - 简洁的使用说明
   - 快速开始指南
   - 文件说明和系统特性
   - 重要提醒事项

4. **`FILE_MANIFEST.md`** (本文件)
   - 文件清单和说明
   - 清理记录
   - 文件用途描述

## 🗑️ 已清理的文件

### 测试和调试文件 (已删除)
- `debug_signal_flow.py` - 信号流程诊断
- `final_test.py` - 最终测试脚本
- `test_cooldown_safety.py` - 冷却安全性测试
- `test_real_signal.py` - 真实信号测试
- `test_with_monitoring.py` - 带监控的测试
- `direct_zxtouch_test.py` - 直接ZXTouch测试
- `step_by_step_test.py` - 分步测试
- `test_complete_trade.py` - 完整交易测试
- `test_corrected_numbers.py` - 数字坐标校准测试
- `test_down_trade.py` - 下跌交易测试
- `test_final_complete_trade.py` - 最终完整交易测试
- `test_final_optimized_trade.py` - 最终优化交易测试
- `test_full_trade_simple.py` - 简化完整交易测试
- `test_keyboard_flow.py` - 键盘流程测试
- `test_keyboard_flow_fast.py` - 快速键盘流程测试
- `test_keyboard_flow_optimized.py` - 优化键盘流程测试
- `test_number_keyboard.py` - 数字键盘测试
- `test_numbers_1_to_9.py` - 数字1-9测试
- `test_step1_only.py` - 步骤1单独测试
- `test_step2_only.py` - 步骤2单独测试

### 缓存文件 (保留)
- `__pycache__/` - Python缓存目录 (自动生成)

## ✅ 清理结果

### 文件数量对比
- **清理前**: 约25个文件
- **清理后**: 8个核心文件 + 缓存
- **减少**: 约70%的文件

### 保留原则
1. **核心功能**: 保留所有必需的功能文件
2. **重要文档**: 保留关键的文档和经验总结
3. **测试工具**: 保留必要的测试和检查工具
4. **配置文件**: 保留配置和参数文件

### 删除原则
1. **临时测试**: 删除开发过程中的临时测试文件
2. **重复功能**: 删除功能重复的文件
3. **调试工具**: 删除专门的调试和诊断工具
4. **实验代码**: 删除实验性和验证性代码

## 🎯 文件夹现状

### 结构清晰
- 核心功能文件易于识别
- 文档文件完整详细
- 测试工具简洁实用
- 配置文件集中管理

### 维护友好
- 文件数量适中，易于管理
- 每个文件职责明确
- 文档详细，便于理解
- 代码质量高，易于维护

### 生产就绪
- 所有必需功能完整
- 测试工具可用
- 文档齐全
- 系统稳定可靠

## 🎊 清理完成

iPhone自动化文件夹已成功清理，保留了所有核心功能和重要文档，删除了临时和重复的测试文件。文件夹现在结构清晰、维护友好，完全适合生产环境使用。

**为未来的AI**: 重点查看`AI_SUCCESS_EXPERIENCE.md`文件，其中包含了完整的开发经验和技术细节。
