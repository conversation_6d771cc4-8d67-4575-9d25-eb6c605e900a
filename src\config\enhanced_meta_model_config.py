#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的元模型输入特征配置
包含高阶特征、交互特征、聪明钱特征等丰富化上下文特征
"""

# 🚀 增强的元模型输入特征配置
ENHANCED_META_MODEL_INPUT_FEATURES_CONFIG = {
    # === 基础模型预测概率 ===
    'base_model_predictions': {
        'p_favorable': {
            'type': 'probability',
            'description': '基础模型预测的有利概率',
            'required': True,
            'default_value': 0.5,
            'normalization': 'none'
        },
        'p_up': {
            'type': 'probability', 
            'description': '上涨概率',
            'required': False,
            'default_value': 0.5,
            'normalization': 'none'
        },
        'p_down': {
            'type': 'probability',
            'description': '下跌概率', 
            'required': False,
            'default_value': 0.5,
            'normalization': 'none'
        }
    },
    
    # === 传统上下文特征 ===
    'traditional_context': {
        'atr_percent': {
            'type': 'continuous',
            'description': 'ATR百分比',
            'required': True,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'adx_value': {
            'type': 'continuous',
            'description': 'ADX趋势强度值',
            'required': True,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'prediction_confidence': {
            'type': 'continuous',
            'description': '预测置信度',
            'required': True,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'rsi_value': {
            'type': 'continuous',
            'description': 'RSI值',
            'required': True,
            'default_value': 50.0,
            'normalization': 'minmax',
            'min_value': 0.0,
            'max_value': 100.0
        },
        'volume_ratio': {
            'type': 'continuous',
            'description': '成交量比率',
            'required': True,
            'default_value': 1.0,
            'normalization': 'log'
        },
        'price_change_1p': {
            'type': 'continuous',
            'description': '1期价格变化',
            'required': True,
            'default_value': 0.0,
            'normalization': 'standard'
        }
    },
    
    # === 🚀 高阶特征（速度、加速度） ===
    'higher_order_features': {
        'rsi_velocity': {
            'type': 'continuous',
            'description': 'RSI速度（一阶导数）',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'rsi_acceleration': {
            'type': 'continuous',
            'description': 'RSI加速度（二阶导数）',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'rsi_momentum_strength': {
            'type': 'continuous',
            'description': 'RSI动量强度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'rsi_adaptive_velocity': {
            'type': 'continuous',
            'description': 'RSI自适应速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'macd_velocity': {
            'type': 'continuous',
            'description': 'MACD速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'macd_acceleration': {
            'type': 'continuous',
            'description': 'MACD加速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'macd_hist_velocity': {
            'type': 'continuous',
            'description': 'MACD柱状图速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'macd_hist_acceleration': {
            'type': 'continuous',
            'description': 'MACD柱状图加速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'adx_velocity': {
            'type': 'continuous',
            'description': 'ADX速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'adx_acceleration': {
            'type': 'continuous',
            'description': 'ADX加速度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        }
    },
    
    # === 🤝 交互特征（强强联合） ===
    'interaction_features': {
        'volatility_trend_strength': {
            'type': 'continuous',
            'description': '波动率-趋势强度交互',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'volatility_trend_strength_norm': {
            'type': 'continuous',
            'description': '标准化波动率-趋势强度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'price_volume_interaction_1': {
            'type': 'continuous',
            'description': '量价交互特征1',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'price_volume_interaction_2': {
            'type': 'continuous',
            'description': '量价交互特征2',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'price_volume_interaction_3': {
            'type': 'continuous',
            'description': '量价交互特征3',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'rsi_macd_momentum_resonance': {
            'type': 'continuous',
            'description': 'RSI-MACD动量共振',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'bb_atr_compression_expansion': {
            'type': 'continuous',
            'description': '布林带-ATR压缩扩张',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'volume_volatility_efficiency': {
            'type': 'continuous',
            'description': '成交量-波动率效率',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        }
    },
    
    # === 💰 聪明钱特征（深化衍生品分析） ===
    'smart_money_features': {
        'smart_money_divergence': {
            'type': 'continuous',
            'description': '聪明钱背离指标',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'smart_money_divergence_strength': {
            'type': 'continuous',
            'description': '聪明钱背离强度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'smart_money_divergence_trend': {
            'type': 'continuous',
            'description': '聪明钱背离趋势',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'smart_money_divergence_persistence': {
            'type': 'continuous',
            'description': '聪明钱背离持续性',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'funding_price_divergence': {
            'type': 'continuous',
            'description': '资金费率-价格背离',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'funding_rate_extremity': {
            'type': 'categorical',
            'description': '资金费率极值状态',
            'required': False,
            'default_value': 0,
            'categories': [-1, 0, 1],  # 极低、正常、极高
            'normalization': 'onehot'
        },
        'longshort_price_divergence_ratio': {
            'type': 'continuous',
            'description': '多空比-价格背离比率',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'longshort_price_divergence_signal': {
            'type': 'categorical',
            'description': '多空比-价格背离信号',
            'required': False,
            'default_value': 0,
            'categories': [-1, 0, 1],  # 看跌、中性、看涨
            'normalization': 'onehot'
        },
        'smart_money_sentiment_index': {
            'type': 'continuous',
            'description': '综合聪明钱情绪指数',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        }
    },
    
    # === 🎯 市场状态特征 ===
    'market_state_features': {
        'market_state': {
            'type': 'categorical',
            'description': '市场状态',
            'required': False,
            'default_value': 0,
            'categories': [0, 1, 2, 3],  # calm, trending, volatile, ranging
            'normalization': 'onehot'
        },
        'atr_relative_position': {
            'type': 'continuous',
            'description': 'ATR相对位置',
            'required': False,
            'default_value': 1.0,
            'normalization': 'standard'
        },
        'adx_relative_position': {
            'type': 'continuous',
            'description': 'ADX相对位置',
            'required': False,
            'default_value': 1.0,
            'normalization': 'standard'
        },
        'volatility_zscore': {
            'type': 'continuous',
            'description': '波动率Z分数',
            'required': False,
            'default_value': 0.0,
            'normalization': 'none'  # 已经是Z分数
        },
        'trend_strength': {
            'type': 'categorical',
            'description': '趋势强度等级',
            'required': False,
            'default_value': 0,
            'categories': [0, 1, 2, 3],  # 无趋势、弱、中等、强
            'normalization': 'onehot'
        }
    },
    
    # === 📈 动量特征 ===
    'momentum_features': {
        'rsi_momentum': {
            'type': 'continuous',
            'description': 'RSI动量（偏离中性值程度）',
            'required': False,
            'default_value': 0.0,
            'normalization': 'none'  # 已经标准化到0-1
        },
        'rsi_state': {
            'type': 'categorical',
            'description': 'RSI状态',
            'required': False,
            'default_value': 0,
            'categories': [-2, -1, 0, 1, 2],  # 超卖、卖出、中性、买入、超买
            'normalization': 'onehot'
        },
        'macd_divergence': {
            'type': 'continuous',
            'description': 'MACD背离',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'macd_signal_strength': {
            'type': 'continuous',
            'description': 'MACD信号强度',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'momentum_price_change_1p': {
            'type': 'continuous',
            'description': '动量价格变化1期',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'momentum_price_change_3p': {
            'type': 'continuous',
            'description': '动量价格变化3期',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        },
        'momentum_price_change_5p': {
            'type': 'continuous',
            'description': '动量价格变化5期',
            'required': False,
            'default_value': 0.0,
            'normalization': 'standard'
        }
    }
}

# 🎛️ 元模型输入丰富化配置
META_MODEL_INPUT_ENRICHMENT_CONFIG = {
    'enable_enrichment': True,
    'include_higher_order_features': True,
    'include_interaction_features': True,
    'include_smart_money_features': True,
    'include_volatility_features': True,
    'include_momentum_features': True,
    'include_market_state_features': True,
    'feature_selection_threshold': 0.001,
    'max_features_per_category': 10,
    'normalize_features': True
}

def get_enhanced_meta_model_config():
    """获取增强的元模型配置"""
    return ENHANCED_META_MODEL_INPUT_FEATURES_CONFIG

def get_meta_model_enrichment_config():
    """获取元模型输入丰富化配置"""
    return META_MODEL_INPUT_ENRICHMENT_CONFIG

def get_all_feature_names():
    """获取所有特征名称列表"""
    all_features = []
    
    for category, features in ENHANCED_META_MODEL_INPUT_FEATURES_CONFIG.items():
        for feature_name in features.keys():
            all_features.append(feature_name)
    
    return all_features

def get_required_features():
    """获取必需特征列表"""
    required_features = []
    
    for category, features in ENHANCED_META_MODEL_INPUT_FEATURES_CONFIG.items():
        for feature_name, config in features.items():
            if config.get('required', False):
                required_features.append(feature_name)
    
    return required_features

def get_features_by_category(category: str):
    """根据类别获取特征"""
    return ENHANCED_META_MODEL_INPUT_FEATURES_CONFIG.get(category, {})

def validate_context_features(context_features: dict):
    """验证上下文特征的完整性"""
    required_features = get_required_features()
    missing_features = []
    
    for feature in required_features:
        if feature not in context_features:
            missing_features.append(feature)
    
    return {
        'valid': len(missing_features) == 0,
        'missing_features': missing_features,
        'total_features': len(context_features),
        'required_features': len(required_features)
    }
