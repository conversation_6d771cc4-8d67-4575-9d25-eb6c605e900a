#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 特征计算器 - 通过特征注册表统一管理特征计算
"""

import logging
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np

from .feature_registry import get_feature_registry, FeatureType

logger = logging.getLogger(__name__)

class FeatureCalculator:
    """特征计算器 - 通过注册表统一管理特征计算"""
    
    def __init__(self):
        self.registry = get_feature_registry()
        self._calculation_cache = {}
    
    def calculate_feature_group(self, 
                               df: pd.DataFrame, 
                               feature_group: str, 
                               config: Dict[str, Any],
                               interval_str: str = "") -> Dict[str, pd.Series]:
        """
        🚀 通过注册表计算特征组
        
        Args:
            df: 输入数据
            feature_group: 特征组名称
            config: 配置参数
            interval_str: 时间间隔字符串（用于日志）
            
        Returns:
            Dict[str, pd.Series]: 计算得到的特征字典
        """
        try:
            # 获取特征组的配置参数
            group_config = self.registry.get_feature_config_params(feature_group, config)
            
            # 根据特征组类型调用相应的计算函数
            if feature_group == 'price_change':
                return self._calculate_price_change_features(df, group_config, interval_str)
            elif feature_group == 'volume':
                return self._calculate_volume_features(df, group_config, interval_str)
            elif feature_group == 'candle':
                return self._calculate_candle_features(df, group_config, interval_str)
            elif feature_group == 'technical':
                return self._calculate_technical_features(df, group_config, interval_str)
            elif feature_group == 'fund_flow':
                return self._calculate_fund_flow_features(df, group_config, interval_str)
            elif feature_group == 'time':
                return self._calculate_time_features(df, group_config, interval_str)
            elif feature_group == 'trend':
                return self._calculate_trend_features(df, group_config, interval_str)
            else:
                logger.warning(f"未知的特征组: {feature_group}")
                return {}
                
        except Exception as e:
            logger.error(f"计算特征组 {feature_group} 时出错: {e}")
            return {}
    
    def _calculate_price_change_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算价格变化特征"""
        features = {}
        
        if not config.get('enable_price_change', False):
            return features
        
        try:
            close_prices = df['close']
            periods = config.get('price_change_periods', [1, 3, 5, 10])
            
            for period in periods:
                if isinstance(period, int) and period > 0 and len(close_prices) > period:
                    feature_name = f'price_change_{period}p'
                    features[feature_name] = close_prices.pct_change(periods=period).fillna(0) * 100
                    logger.debug(f"计算特征: {feature_name}")
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('price_change', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算价格变化特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_volume_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算成交量特征"""
        features = {}
        
        if not config.get('enable_volume', False):
            return features
        
        try:
            volume = df['volume']
            vol_avg_period = config.get('volume_avg_period', 20)
            
            # 成交量变化
            volume_change_periods = config.get('volume_change_periods', [1])
            for period in volume_change_periods:
                if isinstance(period, int) and period > 0 and len(volume) > period:
                    feature_name = f'volume_change_{period}p'
                    features[feature_name] = volume.pct_change(periods=period).fillna(0) * 100
            
            # 成交量相对于平均值的比率
            if len(volume) >= max(1, vol_avg_period // 2):
                vol_avg = volume.rolling(window=vol_avg_period, min_periods=max(1, vol_avg_period // 2)).mean()
                features['volume_vs_avg'] = (volume / vol_avg.replace(0, 1e-9)).fillna(1)
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('volume', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算成交量特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_candle_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算K线特征"""
        features = {}
        
        if not config.get('enable_candle', False):
            return features
        
        try:
            open_prices = df['open']
            high_prices = df['high']
            low_prices = df['low']
            close_prices = df['close']
            
            # 基础K线特征
            if len(close_prices) > 0 and len(open_prices) > 0:
                features['body_size'] = (close_prices - open_prices).abs()
                features['is_green_candle'] = (close_prices > open_prices).astype(int)
            
            if len(high_prices) > 0 and len(low_prices) > 0:
                features['candle_range'] = high_prices - low_prices
                features['upper_shadow'] = high_prices - close_prices.combine(open_prices, max)
                features['lower_shadow'] = open_prices.combine(close_prices, min) - low_prices
                
                candle_range_safe = features['candle_range'].replace(0, 1e-9)
                features['is_doji'] = (features['body_size'] < candle_range_safe * 0.1).astype(int)
                features['close_pos_in_candle'] = ((close_prices - low_prices) / candle_range_safe).fillna(0.5).clip(0, 1)
            
            # 标准化特征
            if 'candle_range' in features and features['candle_range'].std() > 0:
                range_std = features['candle_range'].rolling(window=20, min_periods=1).std().replace(0, 1e-9)
                features['body_size_norm'] = features['body_size'] / range_std
                features['upper_shadow_norm'] = features['upper_shadow'] / range_std
                features['lower_shadow_norm'] = features['lower_shadow'] / range_std
                features['candle_range_norm'] = features['candle_range'] / range_std
            
            # 平滑特征
            smoothing_periods = config.get('candle_smoothing_periods', [3])
            base_features = ['upper_shadow', 'close_pos_in_candle', 'body_size']
            
            for period in smoothing_periods:
                if isinstance(period, int) and period > 0:
                    for base_feat in base_features:
                        if base_feat in features:
                            smooth_name = f'{base_feat}_smooth{period}p'
                            features[smooth_name] = features[base_feat].rolling(window=period, min_periods=1).mean()
            
            # K线形态识别
            if config.get('enable_pattern_recognition', True):
                features['candlestick_pattern_name'] = pd.Series(['Unknown'] * len(df), index=df.index)
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('candle', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算K线特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_technical_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算技术指标特征"""
        features = {}
        
        if not config.get('enable_ta', False):
            return features
        
        try:
            # 这里可以集成pandas_ta或其他技术指标库
            # 为了简化，这里只实现基本的技术指标
            
            close_prices = df['close']
            high_prices = df['high']
            low_prices = df['low']
            
            # RSI
            rsi_period = config.get('rsi_period', 14)
            if isinstance(rsi_period, int) and rsi_period > 0:
                features[f'RSI_{rsi_period}'] = self._calculate_rsi(close_prices, rsi_period)
            
            # ATR
            atr_period = config.get('atr_period', 14)
            if isinstance(atr_period, int) and atr_period > 0:
                features[f'ATRr_{atr_period}'] = self._calculate_atr(high_prices, low_prices, close_prices, atr_period)
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('technical', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算技术指标特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_fund_flow_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算资金流特征"""
        features = {}
        
        if not config.get('enable_fund_flow', False):
            return features
        
        try:
            # 基础资金流特征
            features['fund_flow_indicator'] = pd.Series([0.0] * len(df), index=df.index)
            features['taker_buy_ratio'] = pd.Series([0.5] * len(df), index=df.index)
            
            # 平滑资金流特征
            smoothing_period = config.get('fund_flow_ratio_smoothing_period', 5)
            if isinstance(smoothing_period, int) and smoothing_period > 0:
                smooth_name = f'taker_buy_ratio_smooth{smoothing_period}p'
                features[smooth_name] = features['taker_buy_ratio'].rolling(window=smoothing_period, min_periods=1).mean()
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('fund_flow', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算资金流特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_time_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算时间特征"""
        features = {}
        
        try:
            # 基础时间特征
            features['hour'] = pd.Series([0] * len(df), index=df.index, dtype=int)
            features['day_of_week'] = pd.Series([0] * len(df), index=df.index, dtype=int)
            features['is_weekend'] = pd.Series([False] * len(df), index=df.index, dtype=bool)
            
            # 周期性编码
            features['hour_sin'] = pd.Series([0.0] * len(df), index=df.index)
            features['hour_cos'] = pd.Series([1.0] * len(df), index=df.index)
            features['day_sin'] = pd.Series([0.0] * len(df), index=df.index)
            features['day_cos'] = pd.Series([1.0] * len(df), index=df.index)
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('time', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算时间特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_trend_features(self, df: pd.DataFrame, config: Dict[str, Any], interval_str: str) -> Dict[str, pd.Series]:
        """计算趋势特征"""
        features = {}
        
        try:
            # 基础趋势特征
            features['trend_slope_period_1'] = pd.Series([0.0] * len(df), index=df.index)
            features['trend_slope_period_2'] = pd.Series([0.0] * len(df), index=df.index)
            features['trend_adx_signal'] = pd.Series([0.0] * len(df), index=df.index)
            features['trend_ema_signal'] = pd.Series([0.0] * len(df), index=df.index)
            features['adx_value'] = pd.Series([0.0] * len(df), index=df.index)
            features['adx_pdi'] = pd.Series([0.0] * len(df), index=df.index)
            features['adx_mdi'] = pd.Series([0.0] * len(df), index=df.index)
            features['ema_short'] = pd.Series([0.0] * len(df), index=df.index)
            features['ema_long'] = pd.Series([0.0] * len(df), index=df.index)
            
            # 注册生成的特征名
            self.registry.register_feature_names_for_group('trend', list(features.keys()), config)
            
        except Exception as e:
            logger.error(f"计算趋势特征时出错 ({interval_str}): {e}")
        
        return features
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=1).mean()
            rs = gain / loss.replace(0, 1e-9)
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50.0)
        except Exception:
            return pd.Series([50.0] * len(prices), index=prices.index)
    
    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """计算ATR指标"""
        try:
            tr1 = high - low
            tr2 = (high - close.shift(1)).abs()
            tr3 = (low - close.shift(1)).abs()
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period, min_periods=1).mean()
            return atr.fillna(0.0)
        except Exception:
            return pd.Series([0.0] * len(high), index=high.index)

# 全局特征计算器实例
feature_calculator = FeatureCalculator()

def get_feature_calculator() -> FeatureCalculator:
    """获取全局特征计算器实例"""
    return feature_calculator
