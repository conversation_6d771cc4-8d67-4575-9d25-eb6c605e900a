"""
优化的元模型训练数据准备使用示例

本示例展示如何使用优化的元模型训练数据准备方案，
解决用户提到的关键问题：
1. 基础模型各自配置的特征工程
2. 严格的索引对齐
3. 数据质量保证
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List

# 导入相关模块
from src.training.optimized_meta_data_preparation import (
    OptimizedMetaDataPreparator,
    prepare_meta_training_data_optimized
)
from src.training.elite_meta_model import EliteMetaModelTrainer
from src.core.enhanced_logger import get_enhanced_logger


def create_sample_data():
    """创建示例数据"""
    logger = get_enhanced_logger()
    logger.info("📊 创建示例数据...")
    
    # 创建时间索引
    start_time = datetime(2024, 1, 1)
    time_index = pd.date_range(start=start_time, periods=1000, freq='15min')
    
    # 创建原始K线数据
    np.random.seed(42)
    df_raw_data = pd.DataFrame({
        'open': 50000 + np.cumsum(np.random.randn(1000) * 10),
        'high': 50000 + np.cumsum(np.random.randn(1000) * 10) + 50,
        'low': 50000 + np.cumsum(np.random.randn(1000) * 10) - 50,
        'close': 50000 + np.cumsum(np.random.randn(1000) * 10),
        'volume': np.random.randint(100, 1000, 1000)
    }, index=time_index)
    
    # 创建目标变量
    target_data = pd.Series(
        np.random.choice([0, 1], size=1000, p=[0.6, 0.4]),
        index=time_index,
        name='target'
    )
    
    # 创建基础模型配置
    base_models_config = {
        'BTC_15m_UP': {
            'model_type': 'up',
            'prediction_horizon': 1,
            'feature_config': {
                'rsi_period': 14,
                'macd_fast': 12,
                'macd_slow': 26
            }
        },
        'BTC_15m_DOWN': {
            'model_type': 'down',
            'prediction_horizon': 1,
            'feature_config': {
                'rsi_period': 21,
                'macd_fast': 8,
                'macd_slow': 21
            }
        }
    }
    
    # 创建已训练模型信息
    trained_models_info = {
        'BTC_15m_UP': {
            'config': base_models_config['BTC_15m_UP'],
            'model_dir': 'models/BTC_15m_UP',
            'elite_folds': [
                {'fold_index': 0, 'model_config': base_models_config['BTC_15m_UP']},
                {'fold_index': 2, 'model_config': base_models_config['BTC_15m_UP']}
            ]
        },
        'BTC_15m_DOWN': {
            'config': base_models_config['BTC_15m_DOWN'],
            'model_dir': 'models/BTC_15m_DOWN',
            'elite_folds': [
                {'fold_index': 1, 'model_config': base_models_config['BTC_15m_DOWN']},
                {'fold_index': 3, 'model_config': base_models_config['BTC_15m_DOWN']}
            ]
        }
    }
    
    return df_raw_data, target_data, base_models_config, trained_models_info


def example_basic_usage():
    """基本使用示例"""
    logger = get_enhanced_logger()
    logger.info("🚀 开始基本使用示例...")
    
    # 创建示例数据
    df_raw_data, target_data, base_models_config, trained_models_info = create_sample_data()
    
    try:
        # 使用优化的数据准备函数
        X_meta, y_meta = prepare_meta_training_data_optimized(
            df_raw_data=df_raw_data,
            target_data=target_data,
            trained_models_info=trained_models_info,
            base_models_config=base_models_config
        )
        
        logger.info(f"✅ 元模型训练数据准备完成:")
        logger.info(f"  - X_meta形状: {X_meta.shape}")
        logger.info(f"  - y_meta长度: {len(y_meta)}")
        logger.info(f"  - 特征列表: {list(X_meta.columns)}")
        logger.info(f"  - 索引对齐检查: {X_meta.index.equals(y_meta.index)}")
        
        return X_meta, y_meta
        
    except Exception as e:
        logger.error(f"基本使用示例失败: {e}")
        return None, None


def example_advanced_usage():
    """高级使用示例"""
    logger = get_enhanced_logger()
    logger.info("🚀 开始高级使用示例...")
    
    # 创建示例数据
    df_raw_data, target_data, base_models_config, trained_models_info = create_sample_data()
    
    try:
        # 创建数据准备器
        preparator = OptimizedMetaDataPreparator(base_models_config)
        
        # 生成OOF预测
        oof_df = preparator.generate_oof_predictions_with_model_specific_features(
            df_raw_data=df_raw_data,
            target_data=target_data,
            trained_models_info=trained_models_info
        )
        
        logger.info(f"✅ OOF预测生成完成:")
        logger.info(f"  - OOF形状: {oof_df.shape}")
        logger.info(f"  - OOF列名: {list(oof_df.columns)}")
        
        # 手动对齐目标变量
        common_index = oof_df.index.intersection(target_data.index)
        X_meta = oof_df.loc[common_index]
        y_meta = target_data.loc[common_index]
        
        logger.info(f"✅ 手动对齐完成:")
        logger.info(f"  - 最终X_meta形状: {X_meta.shape}")
        logger.info(f"  - 最终y_meta长度: {len(y_meta)}")
        
        return X_meta, y_meta
        
    except Exception as e:
        logger.error(f"高级使用示例失败: {e}")
        return None, None


def example_elite_meta_integration():
    """精英元模型集成示例"""
    logger = get_enhanced_logger()
    logger.info("🚀 开始精英元模型集成示例...")
    
    # 创建示例数据
    df_raw_data, target_data, base_models_config, trained_models_info = create_sample_data()
    
    try:
        # 创建精英元模型训练器
        elite_trainer = EliteMetaModelTrainer(base_models_config)
        
        # 启用优化的数据准备
        elite_trainer.enable_optimized_data_preparation(True)
        
        # 模拟精英模型信息
        elite_models = {}
        for model_name, model_info in trained_models_info.items():
            elite_models[model_name] = model_info['elite_folds']
        
        elite_trainer.elite_models = elite_models
        
        # 创建模拟的OOF预测数据（用于传统方法对比）
        mock_oof_predictions = pd.DataFrame({
            'oof_BTC_15m_UP_elite_fold0': np.random.rand(len(target_data)),
            'oof_BTC_15m_UP_elite_fold2': np.random.rand(len(target_data)),
            'oof_BTC_15m_DOWN_elite_fold1': np.random.rand(len(target_data)),
            'oof_BTC_15m_DOWN_elite_fold3': np.random.rand(len(target_data))
        }, index=target_data.index)
        
        # 训练超级元模型（会自动使用优化的数据准备）
        result = elite_trainer.train_super_meta_model(
            oof_predictions=mock_oof_predictions,
            target_data=target_data,
            output_dir="models/test_elite_meta",
            df_full_hist_data_for_oof_input=df_raw_data
        )
        
        logger.info(f"✅ 精英元模型训练完成: {result}")
        
        return result
        
    except Exception as e:
        logger.error(f"精英元模型集成示例失败: {e}")
        return None


def example_data_quality_validation():
    """数据质量验证示例"""
    logger = get_enhanced_logger()
    logger.info("🚀 开始数据质量验证示例...")
    
    # 创建示例数据
    df_raw_data, target_data, base_models_config, trained_models_info = create_sample_data()
    
    # 故意引入一些数据质量问题
    df_raw_data.iloc[100:110] = np.nan  # 添加NaN值
    target_data.iloc[200:205] = np.nan  # 目标变量也添加NaN
    
    try:
        # 创建数据准备器
        preparator = OptimizedMetaDataPreparator(base_models_config)
        
        # 生成OOF预测（会自动处理数据质量问题）
        oof_df = preparator.generate_oof_predictions_with_model_specific_features(
            df_raw_data=df_raw_data,
            target_data=target_data,
            trained_models_info=trained_models_info
        )
        
        logger.info("✅ 数据质量验证完成，系统自动处理了数据质量问题")
        
        return oof_df
        
    except Exception as e:
        logger.error(f"数据质量验证示例失败: {e}")
        return None


def main():
    """主函数"""
    logger = get_enhanced_logger()
    logger.info("🌟 开始优化的元模型训练数据准备示例...")
    
    # 运行各种示例
    logger.info("\n" + "="*50)
    logger.info("1. 基本使用示例")
    logger.info("="*50)
    X_meta_basic, y_meta_basic = example_basic_usage()
    
    logger.info("\n" + "="*50)
    logger.info("2. 高级使用示例")
    logger.info("="*50)
    X_meta_advanced, y_meta_advanced = example_advanced_usage()
    
    logger.info("\n" + "="*50)
    logger.info("3. 精英元模型集成示例")
    logger.info("="*50)
    elite_result = example_elite_meta_integration()
    
    logger.info("\n" + "="*50)
    logger.info("4. 数据质量验证示例")
    logger.info("="*50)
    quality_result = example_data_quality_validation()
    
    logger.info("\n🎉 所有示例运行完成！")


if __name__ == "__main__":
    main()
