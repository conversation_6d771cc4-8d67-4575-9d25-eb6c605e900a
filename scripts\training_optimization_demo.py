#!/usr/bin/env python3
"""
训练优化演示脚本
展示如何使用集成的优化功能进行训练
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入优化模块
from src.optimization.training_pipeline_coordinator import get_training_pipeline_coordinator
from src.optimization.training_data_cache import get_training_data_cache
from src.optimization.performance_monitor import get_performance_monitor
from src.optimization.progress_tracker import get_progress_tracker

# 导入训练模块
from src.training.optimized_training_module import get_unscaled_features_and_target_optimized, train_target_optimized

# 导入配置
import config

def demo_training_optimization():
    """演示训练优化功能"""
    print("🚀 训练优化功能演示")
    print("=" * 60)
    
    # 1. 初始化协调器
    print("\n📋 1. 初始化训练管道协调器...")
    coordinator = get_training_pipeline_coordinator()
    
    # 2. 演示数据缓存功能
    print("\n💾 2. 演示数据缓存功能...")
    demo_data_cache()
    
    # 3. 演示性能监控功能
    print("\n📊 3. 演示性能监控功能...")
    demo_performance_monitoring()
    
    # 4. 演示进度跟踪功能
    print("\n📈 4. 演示进度跟踪功能...")
    demo_progress_tracking()
    
    # 5. 演示完整训练流程优化
    print("\n🎯 5. 演示完整训练流程优化...")
    demo_optimized_training_pipeline()
    
    # 6. 生成优化报告
    print("\n📋 6. 生成优化报告...")
    coordinator.print_optimization_report()

def demo_data_cache():
    """演示数据缓存功能"""
    cache = get_training_data_cache()
    
    # 模拟配置
    test_config = {
        'symbol': 'BTCUSDT',
        'interval': '15m',
        'enable_mtfa': True,
        'mtfa_timeframes': ['1h', '4h']
    }
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'feature1': np.random.randn(1000),
        'feature2': np.random.randn(1000),
        'target': np.random.randint(0, 2, 1000)
    })
    
    print("  存储测试数据到缓存...")
    cache.store_data('BTCUSDT', '15m', test_config, test_data, 'test_features')
    
    print("  从缓存读取数据...")
    cached_data = cache.get_cached_data('BTCUSDT', '15m', test_config, 'test_features')
    
    if cached_data is not None:
        print(f"  ✅ 缓存命中！数据形状: {cached_data.shape}")
    else:
        print("  ❌ 缓存未命中")
    
    # 打印缓存统计
    cache.print_cache_report()

def demo_performance_monitoring():
    """演示性能监控功能"""
    monitor = get_performance_monitor()
    
    # 模拟一些训练阶段
    with monitor.monitor_stage("data_loading", "BTC_15m_UP") as stage:
        print("  模拟数据加载...")
        time.sleep(0.5)  # 模拟耗时操作
    
    with monitor.monitor_stage("feature_engineering", "BTC_15m_UP") as stage:
        print("  模拟特征工程...")
        time.sleep(1.0)  # 模拟耗时操作
    
    with monitor.monitor_stage("model_training", "BTC_15m_UP") as stage:
        print("  模拟模型训练...")
        time.sleep(2.0)  # 模拟耗时操作
    
    # 记录自定义指标
    monitor.record_custom_metric("accuracy", 0.85, "BTC_15m_UP")
    monitor.record_custom_metric("feature_count", 150, "BTC_15m_UP")
    
    # 打印性能报告
    monitor.print_performance_report()

def demo_progress_tracking():
    """演示进度跟踪功能"""
    tracker = get_progress_tracker()
    
    # 开始主要训练阶段
    main_stage = tracker.start_stage("complete_training", 3, estimated_duration=10.0)
    
    # 子阶段1: 数据准备
    data_stage = tracker.start_stage("data_preparation", 5)
    for i in range(5):
        tracker.update_progress(message=f"准备数据步骤 {i+1}/5")
        time.sleep(0.2)
    tracker.finish_stage(data_stage)
    tracker.update_progress(stage_id=main_stage)
    
    # 子阶段2: 模型训练
    training_stage = tracker.start_stage("model_training", 3)
    for i in range(3):
        tracker.update_progress(message=f"训练折次 {i+1}/3")
        time.sleep(0.5)
    tracker.finish_stage(training_stage)
    tracker.update_progress(stage_id=main_stage)
    
    # 子阶段3: 模型保存
    save_stage = tracker.start_stage("model_saving", 2)
    for i in range(2):
        tracker.update_progress(message=f"保存步骤 {i+1}/2")
        time.sleep(0.3)
    tracker.finish_stage(save_stage)
    tracker.update_progress(stage_id=main_stage)
    
    # 完成主阶段
    tracker.finish_stage(main_stage)
    
    # 打印进度报告
    tracker.print_progress_report()

def demo_optimized_training_pipeline():
    """演示完整的优化训练流程"""
    coordinator = get_training_pipeline_coordinator()
    
    # 模拟目标配置
    target_config = {
        'name': 'BTC_15m_UP_DEMO',
        'symbol': 'BTCUSDT',
        'interval': '15m',
        'enable_mtfa': False,  # 简化演示
        'cv_folds': 2,
        'target_column': 'BTC_15m_UP_DEMO_target'
    }
    
    # 创建模拟历史数据
    print("  创建模拟历史数据...")
    np.random.seed(42)
    n_samples = 1000
    
    df_hist_data = pd.DataFrame({
        'open': 50000 + np.random.randn(n_samples) * 1000,
        'high': 50500 + np.random.randn(n_samples) * 1000,
        'low': 49500 + np.random.randn(n_samples) * 1000,
        'close': 50000 + np.random.randn(n_samples) * 1000,
        'volume': 100 + np.random.randn(n_samples) * 20,
        'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='15min')
    })
    df_hist_data.set_index('timestamp', inplace=True)
    
    with coordinator.training_stage("demo_training_pipeline", "BTC_15m_UP_DEMO", total_steps=3) as stage:
        
        # 步骤1: 获取特征和目标（使用优化版本）
        stage.update_progress(message="获取特征和目标...")
        print("  获取特征和目标变量...")
        
        # 注意：这里我们需要模拟 get_unscaled_features_and_target_optimized 的行为
        # 因为它依赖于实际的数据处理函数
        try:
            # 简化的特征创建（模拟）
            features_df = df_hist_data.copy()
            features_df['rsi'] = np.random.randn(n_samples) * 10 + 50
            features_df['macd'] = np.random.randn(n_samples) * 0.1
            features_df['bb_upper'] = features_df['close'] * 1.02
            features_df['bb_lower'] = features_df['close'] * 0.98
            
            # 创建目标变量（模拟）
            features_df[target_config['target_column']] = np.random.randint(0, 2, n_samples)
            
            # 分离特征和目标
            feature_columns = [col for col in features_df.columns if col != target_config['target_column']]
            X_unscaled_df = features_df[feature_columns]
            y_series = features_df[target_config['target_column']]
            
            # 内存优化
            X_unscaled_df = stage.optimize_memory(X_unscaled_df)
            
            print(f"    特征数据形状: {X_unscaled_df.shape}")
            print(f"    目标数据形状: {y_series.shape}")
            
        except Exception as e:
            print(f"    ⚠️ 特征获取模拟失败: {e}")
            X_unscaled_df = df_hist_data[['open', 'high', 'low', 'close', 'volume']]
            y_series = pd.Series(np.random.randint(0, 2, n_samples), index=df_hist_data.index)
            feature_columns = X_unscaled_df.columns.tolist()
        
        # 步骤2: 记录性能指标
        stage.update_progress(message="记录性能指标...")
        stage.record_metric("feature_count", len(feature_columns))
        stage.record_metric("sample_count", len(X_unscaled_df))
        stage.record_metric("positive_ratio", y_series.mean())
        
        # 步骤3: 模拟训练过程
        stage.update_progress(message="模拟训练过程...")
        print("  模拟训练过程...")
        
        # 简单的模拟训练
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import cross_val_score
        from sklearn.preprocessing import StandardScaler
        
        # 数据预处理
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_unscaled_df.fillna(0))
        
        # 简单模型训练
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        scores = cross_val_score(model, X_scaled, y_series, cv=2)
        avg_score = scores.mean()
        
        print(f"    模拟训练完成，平均准确率: {avg_score:.4f}")
        
        # 记录训练结果
        stage.record_metric("avg_cv_accuracy", avg_score)
        stage.record_metric("cv_std", scores.std())
        
        print("  ✅ 优化训练流程演示完成")

def main():
    """主函数"""
    try:
        demo_training_optimization()
        print(f"\n🎉 训练优化演示完成！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
