#!/usr/bin/env python3
"""
数据泄露修复后自动化重新训练脚本

此脚本自动化执行数据泄露修复后的重新训练流程，包括：
1. 验证修复效果
2. 备份现有模型
3. 清理缓存
4. 重新训练所有模型
5. 性能对比分析
6. 生成报告
"""

import os
import sys
import json
import shutil
import pandas as pd
from datetime import datetime
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/retraining_after_leakage_fix.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RetrainingManager:
    """重新训练管理器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.backup_dir = f"models_backup_before_leakage_fix_{self.start_time.strftime('%Y%m%d_%H%M%S')}"
        self.results = {
            'start_time': self.start_time.isoformat(),
            'validation_passed': False,
            'backup_completed': False,
            'cache_cleared': False,
            'models_retrained': {},
            'meta_model_retrained': False,
            'performance_comparison': {},
            'errors': []
        }
    
    def run_full_retraining(self):
        """执行完整的重新训练流程"""
        logger.info("🚀 开始数据泄露修复后的自动化重新训练流程")
        
        try:
            # 1. 验证修复效果
            if not self.validate_leakage_fix():
                logger.error("❌ 数据泄露修复验证失败，停止重新训练")
                return False
            
            # 2. 备份现有模型
            if not self.backup_existing_models():
                logger.error("❌ 模型备份失败，停止重新训练")
                return False
            
            # 3. 清理缓存
            if not self.clear_caches():
                logger.error("❌ 缓存清理失败，停止重新训练")
                return False
            
            # 4. 重新训练基础模型
            if not self.retrain_base_models():
                logger.error("❌ 基础模型重新训练失败")
                return False
            
            # 5. 重新训练元模型
            if not self.retrain_meta_model():
                logger.error("❌ 元模型重新训练失败")
                return False
            
            # 6. 性能对比分析
            self.analyze_performance_changes()
            
            # 7. 生成报告
            self.generate_report()
            
            logger.info("✅ 自动化重新训练流程完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 重新训练流程出错: {e}")
            self.results['errors'].append(str(e))
            return False
        
        finally:
            self.results['end_time'] = datetime.now().isoformat()
            self.save_results()
    
    def validate_leakage_fix(self):
        """验证数据泄露修复效果"""
        logger.info("🔍 验证数据泄露修复效果...")
        
        try:
            # 运行验证脚本
            result = os.system('python scripts/validate_data_leakage_fix.py')
            
            if result == 0:
                logger.info("✅ 数据泄露修复验证通过")
                self.results['validation_passed'] = True
                return True
            else:
                logger.error("❌ 数据泄露修复验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证过程出错: {e}")
            self.results['errors'].append(f"验证失败: {e}")
            return False
    
    def backup_existing_models(self):
        """备份现有模型"""
        logger.info("💾 备份现有模型...")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 备份模型目录
            if os.path.exists('models'):
                shutil.copytree('models', f'{self.backup_dir}/models')
                logger.info(f"✅ 模型文件已备份到 {self.backup_dir}/models")
            
            # 备份日志目录
            if os.path.exists('logs'):
                shutil.copytree('logs', f'{self.backup_dir}/logs')
                logger.info(f"✅ 日志文件已备份到 {self.backup_dir}/logs")
            
            # 备份性能记录
            performance_files = [
                'performance_summary.json',
                'training_history.json',
                'model_metrics.json'
            ]
            
            for file in performance_files:
                if os.path.exists(file):
                    shutil.copy2(file, f'{self.backup_dir}/')
                    logger.info(f"✅ {file} 已备份")
            
            self.results['backup_completed'] = True
            self.results['backup_location'] = self.backup_dir
            return True
            
        except Exception as e:
            logger.error(f"❌ 备份失败: {e}")
            self.results['errors'].append(f"备份失败: {e}")
            return False
    
    def clear_caches(self):
        """清理缓存"""
        logger.info("🧹 清理缓存...")
        
        try:
            cache_dirs = [
                'cache/features',
                'cache/models',
                'cache/predictions',
                'cache/data'
            ]
            
            for cache_dir in cache_dirs:
                if os.path.exists(cache_dir):
                    shutil.rmtree(cache_dir)
                    os.makedirs(cache_dir, exist_ok=True)
                    logger.info(f"✅ 已清理 {cache_dir}")
            
            self.results['cache_cleared'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 缓存清理失败: {e}")
            self.results['errors'].append(f"缓存清理失败: {e}")
            return False
    
    def retrain_base_models(self):
        """重新训练基础模型"""
        logger.info("🔄 重新训练基础模型...")
        
        try:
            from config import PREDICTION_TARGETS, get_target_config_wrapper
            from src.training.training_pipeline import run_training_pipeline
            
            success_count = 0
            total_count = len(PREDICTION_TARGETS)
            
            for target_name in PREDICTION_TARGETS.keys():
                logger.info(f"🔄 重新训练 {target_name}...")
                
                try:
                    target_config = get_target_config_wrapper(target_name)
                    
                    # 重新训练
                    results = run_training_pipeline(
                        target_config=target_config,
                        force_retrain=True,
                        clear_cache=True,
                        validate_no_leakage=True
                    )
                    
                    self.results['models_retrained'][target_name] = {
                        'success': True,
                        'metrics': results.get('test_metrics', {}),
                        'training_time': results.get('training_time', 0)
                    }
                    
                    success_count += 1
                    logger.info(f"✅ {target_name} 重新训练完成")
                    
                except Exception as e:
                    logger.error(f"❌ {target_name} 重新训练失败: {e}")
                    self.results['models_retrained'][target_name] = {
                        'success': False,
                        'error': str(e)
                    }
                    self.results['errors'].append(f"{target_name} 训练失败: {e}")
            
            logger.info(f"📊 基础模型重新训练完成: {success_count}/{total_count} 成功")
            return success_count > 0  # 至少有一个模型训练成功
            
        except Exception as e:
            logger.error(f"❌ 基础模型重新训练过程出错: {e}")
            self.results['errors'].append(f"基础模型训练失败: {e}")
            return False
    
    def retrain_meta_model(self):
        """重新训练元模型"""
        logger.info("🔄 重新训练元模型...")
        
        try:
            from src.training.elite_meta_model import train_elite_meta_model
            
            # 检查是否有足够的基础模型
            successful_base_models = [
                name for name, result in self.results['models_retrained'].items()
                if result.get('success', False)
            ]
            
            if len(successful_base_models) < 2:
                logger.warning("⚠️  成功的基础模型数量不足，跳过元模型训练")
                return True
            
            # 重新训练元模型
            meta_results = train_elite_meta_model(
                force_retrain=True,
                validate_base_models=True,
                validate_no_leakage=True
            )
            
            self.results['meta_model_retrained'] = True
            self.results['meta_model_results'] = meta_results
            
            logger.info("✅ 元模型重新训练完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 元模型重新训练失败: {e}")
            self.results['errors'].append(f"元模型训练失败: {e}")
            return False
    
    def analyze_performance_changes(self):
        """分析性能变化"""
        logger.info("📊 分析性能变化...")
        
        try:
            # 加载备份的性能数据
            old_performance_file = f'{self.backup_dir}/performance_summary.json'
            if os.path.exists(old_performance_file):
                with open(old_performance_file, 'r') as f:
                    old_performance = json.load(f)
                
                # 对比新旧性能
                comparison = {}
                for target_name, old_metrics in old_performance.items():
                    if target_name in self.results['models_retrained']:
                        new_result = self.results['models_retrained'][target_name]
                        if new_result.get('success', False):
                            new_metrics = new_result.get('metrics', {})
                            
                            target_comparison = {}
                            for metric in ['accuracy', 'precision', 'recall', 'f1']:
                                old_val = old_metrics.get(metric, 0)
                                new_val = new_metrics.get(metric, 0)
                                change = new_val - old_val
                                change_pct = (change / old_val * 100) if old_val > 0 else 0
                                
                                target_comparison[metric] = {
                                    'old': old_val,
                                    'new': new_val,
                                    'change': change,
                                    'change_pct': change_pct
                                }
                            
                            comparison[target_name] = target_comparison
                
                self.results['performance_comparison'] = comparison
                logger.info("✅ 性能对比分析完成")
            
        except Exception as e:
            logger.error(f"❌ 性能分析失败: {e}")
            self.results['errors'].append(f"性能分析失败: {e}")
    
    def generate_report(self):
        """生成重新训练报告"""
        logger.info("📝 生成重新训练报告...")
        
        try:
            report_file = f'reports/retraining_report_{self.start_time.strftime("%Y%m%d_%H%M%S")}.md'
            os.makedirs('reports', exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 数据泄露修复后重新训练报告\n\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 总体状态
                f.write("## 📋 总体状态\n\n")
                f.write(f"- **验证通过**: {'✅' if self.results['validation_passed'] else '❌'}\n")
                f.write(f"- **备份完成**: {'✅' if self.results['backup_completed'] else '❌'}\n")
                f.write(f"- **缓存清理**: {'✅' if self.results['cache_cleared'] else '❌'}\n")
                f.write(f"- **元模型训练**: {'✅' if self.results['meta_model_retrained'] else '❌'}\n\n")
                
                # 基础模型状态
                f.write("## 🔄 基础模型重新训练状态\n\n")
                for target_name, result in self.results['models_retrained'].items():
                    status = '✅' if result.get('success', False) else '❌'
                    f.write(f"- **{target_name}**: {status}\n")
                f.write("\n")
                
                # 性能对比
                if self.results['performance_comparison']:
                    f.write("## 📊 性能对比分析\n\n")
                    f.write("| 模型 | 指标 | 修复前 | 修复后 | 变化 | 变化% |\n")
                    f.write("|------|------|--------|--------|------|-------|\n")
                    
                    for target_name, comparison in self.results['performance_comparison'].items():
                        for metric, values in comparison.items():
                            f.write(f"| {target_name} | {metric} | {values['old']:.3f} | {values['new']:.3f} | {values['change']:+.3f} | {values['change_pct']:+.1f}% |\n")
                    f.write("\n")
                
                # 错误信息
                if self.results['errors']:
                    f.write("## ⚠️ 错误和警告\n\n")
                    for error in self.results['errors']:
                        f.write(f"- {error}\n")
                    f.write("\n")
                
                # 建议
                f.write("## 💡 后续建议\n\n")
                f.write("1. 监控新模型的实际交易性能\n")
                f.write("2. 考虑重新优化超参数\n")
                f.write("3. 评估是否需要调整特征工程策略\n")
                f.write("4. 定期运行数据泄露检查\n")
            
            logger.info(f"✅ 报告已生成: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 报告生成失败: {e}")
    
    def save_results(self):
        """保存结果"""
        try:
            results_file = f'results/retraining_results_{self.start_time.strftime("%Y%m%d_%H%M%S")}.json'
            os.makedirs('results', exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 结果已保存: {results_file}")
            
        except Exception as e:
            logger.error(f"❌ 结果保存失败: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 数据泄露修复后自动化重新训练")
    print("=" * 80)
    
    manager = RetrainingManager()
    success = manager.run_full_retraining()
    
    if success:
        print("\n🎉 重新训练流程成功完成！")
        return 0
    else:
        print("\n❌ 重新训练流程失败，请检查日志。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
