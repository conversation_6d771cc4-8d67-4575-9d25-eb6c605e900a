#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 统一综合日志系统演示脚本
展示新的统一综合日志系统的各项功能
"""

import sys
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.unified_comprehensive_logger import get_unified_comprehensive_logger
from src.core.logger_compatibility_layer import (
    get_unified_trade_logger,
    log_prediction_context,
    initialize_loggers
)


def demo_basic_usage():
    """演示基础使用方法"""
    print("=" * 60)
    print("🚀 演示1: 基础交易日志记录")
    print("=" * 60)
    
    # 获取日志记录器
    logger = get_unified_comprehensive_logger()
    
    # 记录交易开仓
    print("📈 记录交易开仓...")
    trade_id = logger.log_trade_opened(
        target_name="BTC_15m_UP_Demo",
        symbol="BTCUSDT",
        direction="LONG",
        entry_price=50000.0,
        amount=10.0,
        payout_ratio=0.85,
        context_data={
            'signal_probability': 0.85,
            'market_regime': 'uptrend',
            'atr_percent': 2.5,
            'adx_value': 28.5,
            'meta_model_inputs': json.dumps({
                'avg_up_prob': 0.75,
                'avg_down_prob': 0.25,
                'trend_signal': 1
            })
        }
    )
    
    print(f"✅ 交易开仓记录成功: {trade_id}")
    
    # 模拟等待
    print("⏱️ 模拟交易进行中...")
    time.sleep(1)
    
    # 记录交易平仓
    print("📉 记录交易平仓...")
    success = logger.log_trade_closed(
        trade_id=trade_id,
        exit_price=51000.0,
        result="WIN",
        exit_reason="expired"
    )
    
    print(f"✅ 交易平仓记录成功: {success}")
    
    return logger


def demo_prediction_context():
    """演示预测上下文记录"""
    print("\n" + "=" * 60)
    print("🔍 演示2: 预测上下文记录")
    print("=" * 60)
    
    logger = get_unified_comprehensive_logger()
    
    # 构建预测上下文数据
    signal_data = {
        'signal_type': 'UP',
        'signal_strength': 0.85,
        'avg_up_prob': 0.75,
        'avg_down_prob': 0.25,
        'model_positive_class_prob': 0.82,
        'signal_threshold_used': 0.6,
        'planned_amount': 10.0,
        'amount_calculation_method': 'fixed'
    }
    
    market_data = {
        'current_price': 50000.0,
        'last_kline_close': 49950.0
    }
    
    model_data = {
        'model_type': 'LightGBM',
        'ensemble_size': 5,
        'feature_names': ['rsi_14', 'macd_signal', 'volume_sma', 'price_change_pct'],
        'feature_values': [65.5, 0.02, 1000000, 0.001]
    }
    
    filter_data = {
        'trend_signal': 1,
        'trend_strength': 0.8,
        'trend_status_text': 'Strong Uptrend',
        'adx_value': 28.5,
        'pdi_value': 25.2,
        'mdi_value': 15.8,
        'volatility_level': 2,
        'volatility_status_text': 'Medium Volatility',
        'atr_value': 1250.0,
        'atr_percent': 2.5,
        'dynamic_threshold_adjustments': {'volatility_adj': 0.05},
        'filter_reasons': 'trend_confirmed,volatility_acceptable'
    }
    
    print("📊 记录预测上下文...")
    success = logger.log_prediction_context(
        target_name="BTC_15m_UP_Demo",
        symbol="BTCUSDT",
        signal_data=signal_data,
        market_data=market_data,
        model_data=model_data,
        filter_data=filter_data
    )
    
    print(f"✅ 预测上下文记录成功: {success}")
    
    return logger


def demo_complete_trade():
    """演示完整交易记录"""
    print("\n" + "=" * 60)
    print("📦 演示3: 完整交易记录")
    print("=" * 60)
    
    logger = get_unified_comprehensive_logger()
    
    # 构建完整交易数据
    trade_data = {
        'trade_id': f'demo_complete_trade_{int(time.time())}',
        'entry_timestamp': (datetime.now() - timedelta(minutes=30)).isoformat(),
        'exit_timestamp': datetime.now().isoformat(),
        'target_name': 'BTC_15m_DOWN_Demo',
        'symbol': 'BTCUSDT',
        'direction': 'SHORT',
        'entry_price': 50000.0,
        'exit_price': 49500.0,
        'amount': 15.0,
        'payout_ratio': 0.85,
        'result': 'WIN',
        'profit_loss': 12.75,
        'exit_reason': 'expired',
        'entry_signal_probability': 0.78,
        'entry_neutral_probability': 0.15,
        'entry_opposite_probability': 0.07,
        'direction_advantage': 0.71,
        'meta_decision_reason': 'strong_directional_consensus',
        'entry_market_regime': 'downtrend',
        'entry_atr_percent': 2.8,
        'entry_adx_value': 32.1
    }
    
    context_data = {
        'timestamp': (datetime.now() - timedelta(minutes=31)).isoformat(),
        'target_name': 'BTC_15m_DOWN_Demo',
        'symbol': 'BTCUSDT',
        'signal_type': 'DOWN',
        'signal_strength': 0.78,
        'avg_up_prob': 0.22,
        'avg_down_prob': 0.78,
        'model_type': 'Ensemble',
        'ensemble_size': 5
    }
    
    print("📦 记录完整交易（开仓+平仓+上下文）...")
    success = logger.log_complete_trade(trade_data, context_data)
    
    print(f"✅ 完整交易记录成功: {success}")
    
    return logger


def demo_failure_analysis():
    """演示失败案例分析"""
    print("\n" + "=" * 60)
    print("🔍 演示4: 失败案例分析")
    print("=" * 60)
    
    logger = get_unified_comprehensive_logger()
    
    # 记录一些失败交易用于分析
    print("📉 记录一些失败交易用于分析...")
    
    for i in range(3):
        trade_data = {
            'trade_id': f'demo_failed_trade_{i}_{int(time.time())}',
            'entry_timestamp': (datetime.now() - timedelta(hours=i+1)).isoformat(),
            'exit_timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
            'target_name': f'FailDemo_Strategy_{i}',
            'symbol': 'BTCUSDT',
            'direction': 'LONG' if i % 2 == 0 else 'SHORT',
            'entry_price': 50000.0 + i * 100,
            'exit_price': 49000.0 + i * 50,
            'amount': 10.0,
            'payout_ratio': 0.85,
            'result': 'LOSS',
            'profit_loss': -10.0,
            'exit_reason': 'expired',
            'entry_signal_probability': 0.65 - i * 0.05,
            'entry_market_regime': 'sideways',
            'entry_atr_percent': 3.5 + i * 0.5
        }
        
        logger.log_complete_trade(trade_data)
        print(f"  📉 失败交易 {i+1} 已记录")
    
    # 等待异步写入完成
    print("⏱️ 等待数据写入完成...")
    time.sleep(2)
    
    # 执行失败案例分析
    print("🔍 执行失败案例分析...")
    analysis_result = logger.analyze_failures(days_back=1)
    
    if analysis_result and 'basic_statistics' in analysis_result:
        stats = analysis_result['basic_statistics']
        print(f"📊 分析结果:")
        print(f"  - 总交易数: {stats.get('total_trades', 0)}")
        print(f"  - 失败交易数: {stats.get('failed_trades', 0)}")
        print(f"  - 失败率: {stats.get('failure_rate', 0):.2f}%")
        print(f"  - 总损失金额: {stats.get('total_loss_amount', 0):.2f}")
        print(f"  - 平均单笔损失: {stats.get('avg_loss_per_trade', 0):.2f}")
        
        # 显示改进建议
        recommendations = analysis_result.get('recommendations', [])
        if recommendations:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"  {i}. {rec}")
    else:
        print("⚠️ 分析结果为空或格式异常")
    
    return logger


def demo_compatibility_layer():
    """演示兼容层功能"""
    print("\n" + "=" * 60)
    print("🔄 演示5: 兼容层功能")
    print("=" * 60)
    
    print("🔧 初始化兼容层...")
    initialize_loggers()
    
    # 使用兼容的统一交易日志记录器
    print("📈 使用兼容接口记录交易...")
    compat_logger = get_unified_trade_logger()
    
    trade_id = compat_logger.record_trade_entry(
        target_name="CompatDemo_Strategy",
        symbol="BTCUSDT",
        direction="LONG",
        entry_price=50000.0,
        amount=10.0,
        context_data={
            'signal_probability': 0.8,
            'market_regime': 'uptrend'
        }
    )
    
    print(f"✅ 兼容接口开仓记录成功: {trade_id}")
    
    success = compat_logger.record_trade_exit(
        trade_id=trade_id,
        exit_price=51000.0,
        result="WIN"
    )
    
    print(f"✅ 兼容接口平仓记录成功: {success}")
    
    # 使用兼容的预测上下文记录
    print("📊 使用兼容接口记录预测上下文...")
    signal_data = {'signal_type': 'UP', 'signal_strength': 0.8}
    market_data = {'current_price': 50000.0}
    model_data = {'model_type': 'LightGBM'}
    filter_data = {'trend_signal': 1}
    
    success = log_prediction_context(
        target_name="CompatDemo_Strategy",
        symbol="BTCUSDT",
        signal_data=signal_data,
        market_data=market_data,
        model_data=model_data,
        filter_data=filter_data
    )
    
    print(f"✅ 兼容接口上下文记录成功: {success}")
    
    return compat_logger


def demo_statistics_and_data_loading():
    """演示统计信息和数据加载"""
    print("\n" + "=" * 60)
    print("📊 演示6: 统计信息和数据加载")
    print("=" * 60)
    
    logger = get_unified_comprehensive_logger()
    
    # 等待所有异步写入完成
    print("⏱️ 等待所有数据写入完成...")
    time.sleep(3)
    
    # 获取系统统计信息
    print("📊 获取系统统计信息...")
    stats = logger.get_statistics()
    
    basic_stats = stats.get('basic_stats', {})
    print(f"📈 基础统计:")
    print(f"  - 总开仓数: {basic_stats.get('total_entries', 0)}")
    print(f"  - 总平仓数: {basic_stats.get('total_exits', 0)}")
    print(f"  - 上下文记录数: {basic_stats.get('total_contexts', 0)}")
    print(f"  - 待平仓交易数: {basic_stats.get('pending_trades_count', 0)}")
    
    # 获取存储统计信息
    print("\n💾 获取存储统计信息...")
    storage_stats = logger.storage_manager.get_storage_statistics()
    
    for layer_name, layer_stats in storage_stats.get('layers', {}).items():
        print(f"📁 {layer_name} 层:")
        print(f"  - 文件数: {layer_stats.get('total_files', 0)}")
        print(f"  - 总大小: {layer_stats.get('total_size_mb', 0):.2f} MB")
    
    # 尝试加载数据
    print("\n📥 尝试加载交易数据...")
    trades_df = logger.load_trade_logs()
    
    if trades_df is not None and not trades_df.empty:
        print(f"✅ 成功加载 {len(trades_df)} 条交易记录")
        print(f"📋 数据列: {list(trades_df.columns)[:5]}...")  # 显示前5列
    else:
        print("⚠️ 暂无交易数据或数据加载失败")
    
    # 尝试加载上下文数据
    print("\n📥 尝试加载上下文数据...")
    contexts_df = logger.load_context_logs()
    
    if contexts_df is not None and not contexts_df.empty:
        print(f"✅ 成功加载 {len(contexts_df)} 条上下文记录")
    else:
        print("⚠️ 暂无上下文数据或数据加载失败")


def main():
    """主演示函数"""
    print("🚀 统一综合日志系统演示")
    print("=" * 80)
    print("本演示将展示新的统一综合日志系统的各项功能")
    print("包括：交易记录、预测上下文、失败分析、兼容层等")
    print("=" * 80)
    
    try:
        # 演示1: 基础使用
        demo_basic_usage()
        
        # 演示2: 预测上下文记录
        demo_prediction_context()
        
        # 演示3: 完整交易记录
        demo_complete_trade()
        
        # 演示4: 失败案例分析
        demo_failure_analysis()
        
        # 演示5: 兼容层功能
        demo_compatibility_layer()
        
        # 演示6: 统计信息和数据加载
        demo_statistics_and_data_loading()
        
        print("\n" + "=" * 80)
        print("🎉 演示完成！")
        print("=" * 80)
        print("✅ 所有功能演示成功")
        print("📁 日志文件已保存到 comprehensive_logs/ 目录")
        print("📖 更多使用方法请参考 docs/UNIFIED_COMPREHENSIVE_LOGGER_GUIDE.md")
        print("🧪 运行测试: python -m pytest tests/test_unified_comprehensive_logger.py")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 确保日志系统正常停止
        try:
            logger = get_unified_comprehensive_logger()
            logger.stop()
            print("\n🔧 日志系统已安全停止")
        except:
            pass


if __name__ == "__main__":
    main()
