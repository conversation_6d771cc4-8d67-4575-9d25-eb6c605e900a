# -*- coding: utf-8 -*-
"""
集中式预测过滤器模块
整合所有预测信号过滤逻辑，包括趋势过滤、波动率过滤、动态阈值调整等
"""

import logging
import numpy as np
import pandas as pd
import sys
import inspect
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# 导入错误处理基础设施
# 避免循环导入，直接定义异常类
class DataValidationError(Exception):
    """数据验证错误"""
    def __init__(self, message, context=None, original_exception=None):
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.original_exception = original_exception

class ConfigurationError(Exception):
    """配置错误"""
    def __init__(self, message, context=None, original_exception=None):
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.original_exception = original_exception

class PredictionExecutionError(Exception):
    """预测执行错误"""
    def __init__(self, message, context=None, original_exception=None):
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.original_exception = original_exception

# 本地错误日志记录函数（避免循环导入）
def log_prediction_error(logger, error, function_name, target_name=None, additional_context=None):
    """
    记录预测错误的详细信息

    Args:
        logger: 日志记录器
        error: 异常对象
        function_name: 函数名
        target_name: 目标名称（可选）
        additional_context: 额外上下文信息（可选）
    """
    try:
        # 获取完整异常信息
        exc_type, exc_value, exc_traceback = sys.exc_info()

        # 获取调用栈信息
        frame = inspect.currentframe()
        if frame and frame.f_back:
            caller_frame = frame.f_back
            line_number = caller_frame.f_lineno
            filename = caller_frame.f_code.co_filename
        else:
            line_number = None
            filename = None

        # 构建错误上下文
        error_context = {
            'function': function_name,
            'line_number': line_number,
            'filename': filename,
            'target_name': target_name,
            'timestamp': datetime.now().isoformat()
        }

        if additional_context:
            error_context.update(additional_context)

        # 记录详细错误信息
        logger.error(f"预测错误 - 函数: {function_name}")
        logger.error(f"错误类型: {type(error).__name__}")
        logger.error(f"错误信息: {str(error)}")
        logger.error(f"错误上下文: {error_context}")

        # 如果有原始异常，也记录
        if hasattr(error, 'original_exception') and error.original_exception:
            logger.error(f"原始异常: {type(error.original_exception).__name__}: {error.original_exception}")

    except Exception as log_error:
        # 如果日志记录本身失败，至少打印基本信息
        logger.error(f"记录预测错误失败: {log_error}")
        logger.error(f"原始错误: {type(error).__name__}: {str(error)}")

class PredictionExecutionError(Exception):
    """预测执行错误"""
    def __init__(self, message, context=None, original_exception=None):
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.original_exception = original_exception


class FilterAction(Enum):
    """过滤器动作枚举"""
    ALLOW = "allow"           # 允许信号通过
    BLOCK = "block"           # 阻止信号
    MODIFY = "modify"         # 修改信号
    ADJUST_THRESHOLD = "adjust_threshold"  # 调整阈值


class FilterReason(Enum):
    """过滤原因枚举"""
    VOLATILITY_HIGH = "volatility_high"
    VOLATILITY_LOW = "volatility_low"
    TREND_OPPOSITE = "trend_opposite"
    TREND_WEAK = "trend_weak"
    DYNAMIC_THRESHOLD = "dynamic_threshold"
    TREND_CHASING = "trend_chasing"
    MARKET_STATE = "market_state"


@dataclass
class FilterInput:
    """过滤器输入数据结构"""
    # 基础信号信息
    raw_signal: str                    # 原始信号 ("UP", "DOWN", "Neutral")
    up_probability: float              # 上涨概率
    down_probability: float            # 下跌概率
    target_variable_type: str          # 目标变量类型 ("UP_ONLY", "DOWN_ONLY", "BOTH")
    
    # 市场状态信息
    trend_signal: int                  # 趋势信号 (-1, 0, 1)
    trend_strength: int                # 趋势强度 (0, 1)
    adx_value: float                   # ADX值
    pdi_value: float                   # PDI值
    mdi_value: float                   # MDI值
    ema_short: float                   # 短期EMA
    ema_long: float                    # 长期EMA
    volatility_level: int              # 波动率水平 (-1, 0, 1)
    atr_value: float                   # ATR值
    atr_percent: float                 # ATR百分比
    
    # 配置信息
    signal_threshold: float            # 信号阈值
    target_config: Dict[str, Any]      # 目标配置
    target_name: str                   # 目标名称


@dataclass
class FilterResult:
    """过滤器结果数据结构"""
    # 过滤结果
    final_signal: str                  # 最终信号
    action: FilterAction               # 过滤动作
    reasons: List[str]                 # 过滤原因列表
    
    # 阈值调整
    adjusted_threshold: float          # 调整后的阈值
    threshold_adjustments: List[str]   # 阈值调整原因
    
    # 显示信息
    prediction_label: str              # 预测标签
    details: str                       # 详细信息
    filter_description: str            # 过滤描述
    
    # 元数据
    success: bool                      # 是否成功
    error_message: Optional[str] = None  # 错误消息


class PredictionFilter:
    """
    集中式预测过滤器类
    整合所有预测信号过滤逻辑
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化过滤器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
    def apply_filters(self, filter_input: FilterInput) -> FilterResult:
        """
        应用所有过滤器
        
        Args:
            filter_input: 过滤器输入数据
            
        Returns:
            FilterResult: 过滤结果
        """
        try:
            self.logger.info(f"开始应用预测过滤器，目标: {filter_input.target_name}")
            
            # 验证输入
            self._validate_input(filter_input)
            
            # 初始化结果
            result = FilterResult(
                final_signal=filter_input.raw_signal,
                action=FilterAction.ALLOW,
                reasons=[],
                adjusted_threshold=filter_input.signal_threshold,
                threshold_adjustments=[],
                prediction_label="",
                details="",
                filter_description="",
                success=True
            )
            
            # 1. 动态阈值调整
            self._apply_dynamic_threshold_adjustment(filter_input, result)
            
            # 2. 概率过低过滤（从元模型决策层迁移过来）
            self._apply_probability_filter(filter_input, result)

            # 3. 重新评估信号（基于调整后的阈值）
            self._reevaluate_signal_with_threshold(filter_input, result)

            # 4. 波动率过滤
            self._apply_volatility_filter(filter_input, result)

            # 5. 趋势过滤
            self._apply_trend_filter(filter_input, result)

            # 6. 生成最终标签和描述
            self._generate_final_labels(filter_input, result)
            
            self.logger.info(f"过滤器应用完成: {filter_input.raw_signal} -> {result.final_signal}")
            return result
            
        except (DataValidationError, ConfigurationError) as e:
            self.logger.error(f"过滤器配置或数据错误: {e}")
            return self._create_error_result(str(e), filter_input.raw_signal)
            
        except Exception as e:
            error_context = {
                'target_name': filter_input.target_name,
                'raw_signal': filter_input.raw_signal,
                'step': 'filter_application'
            }
            wrapped_error = PredictionExecutionError(
                f"预测过滤器执行时发生未预期错误: {str(e)}",
                context=error_context,
                original_exception=e
            )
            log_prediction_error(self.logger, wrapped_error, 'apply_filters', 
                               filter_input.target_name, error_context)
            return self._create_error_result(str(wrapped_error), filter_input.raw_signal)
    
    def _validate_input(self, filter_input: FilterInput) -> None:
        """验证输入数据"""
        if not filter_input.target_name:
            raise DataValidationError("目标名称不能为空")
        
        if filter_input.raw_signal not in ["UP", "DOWN", "Neutral"]:
            raise DataValidationError(f"无效的原始信号: {filter_input.raw_signal}")
        
        if not (0 <= filter_input.up_probability <= 1):
            raise DataValidationError(f"上涨概率超出范围: {filter_input.up_probability}")
        
        if not (0 <= filter_input.down_probability <= 1):
            raise DataValidationError(f"下跌概率超出范围: {filter_input.down_probability}")
        
        if filter_input.target_variable_type not in ["UP_ONLY", "DOWN_ONLY", "BOTH"]:
            raise DataValidationError(f"无效的目标变量类型: {filter_input.target_variable_type}")

    def _apply_probability_filter(self, filter_input: FilterInput, result: FilterResult) -> None:
        """
        应用概率过低过滤
        🎯 V19.0 修复：使用与元模型决策层一致的非对称阈值策略
        """
        # 🎯 V19.0 修复：使用非对称阈值，与元模型决策层保持一致
        up_threshold = filter_input.target_config.get('META_SIGNAL_UP_THRESHOLD', 0.52)
        down_threshold = filter_input.target_config.get('META_SIGNAL_DOWN_THRESHOLD', 0.57)  # 🔧 修复：默认值与配置文件保持一致(57%)

        # 检查是否满足非对称阈值要求
        up_prob = filter_input.up_probability
        down_prob = filter_input.down_probability

        # 🔧 修复：分别检查上涨和下跌概率是否满足各自的阈值
        up_signal_valid = up_prob >= up_threshold
        down_signal_valid = down_prob >= down_threshold

        # 如果两个方向都不满足阈值要求，则过滤
        if not up_signal_valid and not down_signal_valid:
            result.final_signal = "Neutral"
            result.action = FilterAction.BLOCK

            # 🔧 修复：提供准确的过滤原因
            reason = f"概率不足(UP:{up_prob:.3f}<{up_threshold:.2f}, DOWN:{down_prob:.3f}<{down_threshold:.2f})"
            result.reasons.append(reason)

            self.logger.info(f"概率过滤: UP {up_prob:.3f} < {up_threshold:.2f}, DOWN {down_prob:.3f} < {down_threshold:.2f}")
            print(f"      ❌ 概率不足 (UP: {up_prob:.3f} < {up_threshold:.2f}, DOWN: {down_prob:.3f} < {down_threshold:.2f})")

    def _apply_dynamic_threshold_adjustment(self, filter_input: FilterInput, result: FilterResult) -> None:
        """应用动态阈值调整"""
        config = filter_input.target_config
        enable_dynamic_threshold = config.get('enable_dynamic_threshold', False)
        
        if not enable_dynamic_threshold:
            return
        
        # 使用正确的配置键名，避免与目标变量创建的dynamic_threshold_base冲突
        base_threshold = config.get('signal_dynamic_threshold_base', filter_input.signal_threshold)
        trend_adjust = config.get('dynamic_threshold_trend_adjust', 0.03)
        vol_adjust = config.get('dynamic_threshold_volatility_adjust', 0.02)
        
        adjusted_threshold = base_threshold
        adjustments = []
        
        # 趋势调整
        if filter_input.trend_strength == 1:  # 强趋势
            current_direction = self._get_signal_direction(result.final_signal)
            
            if (filter_input.trend_signal == 1 and current_direction == "UP") or \
               (filter_input.trend_signal == -1 and current_direction == "DOWN"):
                # 趋势确认，降低阈值
                adjusted_threshold -= trend_adjust
                trend_name = "强升" if filter_input.trend_signal == 1 else "强降"
                adjustments.append(f"{trend_name}确认-{trend_adjust:.3f}")
            elif (filter_input.trend_signal == 1 and current_direction == "DOWN") or \
                 (filter_input.trend_signal == -1 and current_direction == "UP"):
                # 趋势相反，提高阈值
                adjusted_threshold += trend_adjust
                trend_name = "强升" if filter_input.trend_signal == 1 else "强降"
                adjustments.append(f"{trend_name}过滤+{trend_adjust:.3f}")
        
        # 波动率调整
        if filter_input.volatility_level != 0:  # 异常波动率
            adjusted_threshold += vol_adjust
            vol_name = "低" if filter_input.volatility_level == 1 else "高"
            adjustments.append(f"{vol_name}波+{vol_adjust:.3f}")
        
        # 限制阈值范围
        max_threshold = config.get('dynamic_threshold_max_clip', 0.95)
        result.adjusted_threshold = np.clip(adjusted_threshold, 0.51, max_threshold)
        result.threshold_adjustments = adjustments
        
        if adjustments:
            self.logger.info(f"动态阈值调整: {base_threshold:.3f} -> {result.adjusted_threshold:.3f} "
                           f"(调整: {', '.join(adjustments)})")
    
    def _reevaluate_signal_with_threshold(self, filter_input: FilterInput, result: FilterResult) -> None:
        """基于调整后的阈值重新评估信号"""
        if result.adjusted_threshold == filter_input.signal_threshold:
            return  # 阈值未变化，无需重新评估
        
        target_type = filter_input.target_variable_type
        threshold = result.adjusted_threshold
        
        if target_type == "UP_ONLY":
            if filter_input.up_probability > threshold:
                result.final_signal = "UP"
            else:
                result.final_signal = "Neutral"
        elif target_type == "DOWN_ONLY":
            if filter_input.down_probability > threshold:
                result.final_signal = "DOWN"
            else:
                result.final_signal = "Neutral"
        elif target_type == "BOTH":
            if filter_input.up_probability > threshold:
                result.final_signal = "UP"
            elif filter_input.down_probability > threshold:
                result.final_signal = "DOWN"
            else:
                result.final_signal = "Neutral"
        
        if result.final_signal != filter_input.raw_signal:
            result.action = FilterAction.ADJUST_THRESHOLD
            result.reasons.append("动态阈值调整")
    
    def _apply_volatility_filter(self, filter_input: FilterInput, result: FilterResult) -> None:
        """应用波动率过滤"""
        config = filter_input.target_config
        enable_volatility_filter = config.get('enable_volatility_filter', False)
        
        if not enable_volatility_filter:
            return
        
        if (filter_input.volatility_level != 0 and 
            result.final_signal not in ["Neutral", "Error_Initialization"]):
            
            vol_type = "低" if filter_input.volatility_level == 1 else "高"
            result.final_signal = "Neutral_Filtered_Volatility"
            result.action = FilterAction.BLOCK
            result.reasons.append(f"过滤({vol_type}波)")
            
            self.logger.info(f"波动率过滤: {vol_type}波动率，信号被过滤")
    
    def _apply_trend_filter(self, filter_input: FilterInput, result: FilterResult) -> None:
        """应用趋势过滤"""
        config = filter_input.target_config
        enable_trend_detection = config.get('enable_trend_detection', False)
        trend_filter_strategy = config.get('trend_filter_strategy', 'none')
        
        if not enable_trend_detection or trend_filter_strategy == 'none':
            return
        
        # 跳过已被其他过滤器处理的信号
        if ("Filtered" in result.final_signal or "Error" in result.final_signal or 
            result.final_signal == "Neutral"):
            return
        
        is_strong_trend = (filter_input.trend_strength == 1)
        signal_direction = self._get_signal_direction(result.final_signal)
        
        # 趋势过滤逻辑
        if filter_input.trend_signal != 0 and signal_direction != "Neutral":
            if is_strong_trend and trend_filter_strategy in ['filter_only', 'chase_trend']:
                # 检查信号与强趋势是否相反
                if ((signal_direction == "UP" and filter_input.trend_signal == -1) or
                    (signal_direction == "DOWN" and filter_input.trend_signal == 1)):
                    
                    result.final_signal = "Neutral_Filtered_Trend"
                    result.action = FilterAction.BLOCK
                    trend_name = "降" if filter_input.trend_signal == -1 else "升"
                    result.reasons.append(f"趋势:强{trend_name}过滤{signal_direction}")
                    
                    self.logger.info(f"趋势过滤: 强{trend_name}趋势与{signal_direction}信号相反")
        
        # 趋势追逐逻辑
        elif (signal_direction == "Neutral" and trend_filter_strategy == 'chase_trend' and is_strong_trend):
            chase_boost = config.get('trend_chase_confidence_boost', 0.05)
            target_type = filter_input.target_variable_type
            
            if target_type in ["UP_ONLY", "BOTH"]:
                if (filter_input.trend_signal == 1 and 
                    filter_input.up_probability > (0.5 + chase_boost)):
                    result.final_signal = "UP_Chasing"
                    result.action = FilterAction.MODIFY
                    result.reasons.append("趋势:强升追多")
            
            if target_type in ["DOWN_ONLY", "BOTH"]:
                if (filter_input.trend_signal == -1 and 
                    filter_input.down_probability > (0.5 + chase_boost)):
                    result.final_signal = "DOWN_Chasing"
                    result.action = FilterAction.MODIFY
                    result.reasons.append("趋势:强降追空")
    
    def _get_signal_direction(self, signal: str) -> str:
        """获取信号方向"""
        if "UP" in signal:
            return "UP"
        elif "DOWN" in signal:
            return "DOWN"
        else:
            return "Neutral"
    
    def _generate_final_labels(self, filter_input: FilterInput, result: FilterResult) -> None:
        """生成最终标签和描述"""
        # 生成预测标签
        if "UP" in result.final_signal and not result.final_signal.startswith("Error_"):
            chasing_suffix = " (追涨)" if "_Chasing" in result.final_signal else ""
            result.prediction_label = f"上涨 看多{chasing_suffix}"
        elif "DOWN" in result.final_signal and not result.final_signal.startswith("Error_"):
            chasing_suffix = " (追跌)" if "_Chasing" in result.final_signal else ""
            result.prediction_label = f"下跌 看空{chasing_suffix}"
        elif "Filtered" in result.final_signal:
            filter_desc = ", ".join(result.reasons) if result.reasons else "已过滤"
            result.prediction_label = f"中性 ({filter_desc})"
        else:
            if result.final_signal == "Neutral":
                result.prediction_label = "中性 观望"
            else:
                result.prediction_label = f"错误 ({result.final_signal})"
        
        # 生成详细信息
        details_parts = []
        
        # 阈值信息
        if result.threshold_adjustments:
            # 使用正确的配置键名，避免与目标变量创建的dynamic_threshold_base冲突
            base_threshold = filter_input.target_config.get('signal_dynamic_threshold_base', filter_input.signal_threshold)
            details_parts.append(f"动态阈P: {result.adjusted_threshold:.3f} "
                               f"(基:{base_threshold:.3f},调:{','.join(result.threshold_adjustments)})")
        else:
            details_parts.append(f"固定阈P: {result.adjusted_threshold:.3f}")
        
        # 过滤原因
        if result.reasons:
            details_parts.extend(result.reasons)
        
        result.details = "\n".join(details_parts)
        result.filter_description = ", ".join(result.reasons) if result.reasons else "无过滤"
    
    def _create_error_result(self, error_message: str, original_signal: str) -> FilterResult:
        """创建错误结果"""
        return FilterResult(
            final_signal=f"Error_Filter_{original_signal}",
            action=FilterAction.BLOCK,
            reasons=[f"过滤器错误: {error_message}"],
            adjusted_threshold=0.5,
            threshold_adjustments=[],
            prediction_label=f"错误 (过滤器故障)",
            details=f"过滤器执行失败: {error_message}",
            filter_description=f"过滤器错误: {error_message}",
            success=False,
            error_message=error_message
        )


def create_filter_input_from_prediction_data(
    raw_signal: str,
    up_probability: float,
    down_probability: float,
    target_variable_type: str,
    trend_signal: int,
    trend_strength: int,
    adx_value: float,
    pdi_value: float,
    mdi_value: float,
    ema_short: float,
    ema_long: float,
    volatility_level: int,
    atr_value: float,
    atr_percent: float,
    signal_threshold: float,
    target_config: Dict[str, Any],
    target_name: str
) -> FilterInput:
    """
    从预测数据创建过滤器输入对象的辅助函数

    Args:
        raw_signal: 原始信号
        up_probability: 上涨概率
        down_probability: 下跌概率
        target_variable_type: 目标变量类型
        trend_signal: 趋势信号
        trend_strength: 趋势强度
        adx_value: ADX值
        pdi_value: PDI值
        mdi_value: MDI值
        ema_short: 短期EMA
        ema_long: 长期EMA
        volatility_level: 波动率水平
        atr_value: ATR值
        atr_percent: ATR百分比
        signal_threshold: 信号阈值
        target_config: 目标配置
        target_name: 目标名称

    Returns:
        FilterInput: 过滤器输入对象
    """
    return FilterInput(
        raw_signal=raw_signal,
        up_probability=up_probability,
        down_probability=down_probability,
        target_variable_type=target_variable_type,
        trend_signal=trend_signal,
        trend_strength=trend_strength,
        adx_value=adx_value,
        pdi_value=pdi_value,
        mdi_value=mdi_value,
        ema_short=ema_short,
        ema_long=ema_long,
        volatility_level=volatility_level,
        atr_value=atr_value,
        atr_percent=atr_percent,
        signal_threshold=signal_threshold,
        target_config=target_config,
        target_name=target_name
    )
